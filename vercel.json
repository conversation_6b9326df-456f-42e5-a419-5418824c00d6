{"version": 2, "name": "<PERSON><PERSON><PERSON>", "builds": [{"src": "backend/dist/index.js", "use": "@vercel/node", "config": {"includeFiles": ["backend/dist/**", "backend/package.json", "backend/node_modules/**"]}}, {"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/backend/dist/index.js"}, {"src": "/health", "dest": "/backend/dist/index.js"}, {"src": "/(.*)", "dest": "/frontend/dist/index.html"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"backend/dist/index.js": {"maxDuration": 30}}, "regions": ["iad1"], "github": {"enabled": true, "autoAlias": true}, "rewrites": [{"source": "/api/(.*)", "destination": "/backend/dist/index.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';"}]}]}