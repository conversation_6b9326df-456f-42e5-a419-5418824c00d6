#!/bin/bash

echo "🚀 Starting RentaHub servers..."

# Kill any existing processes
pkill -f "tsx src/index.ts" || true
pkill -f "vite" || true

echo "📍 Starting backend server..."
cd /Users/<USER>/Desktop/RENTAHUB/backend
npm run dev > ../logs/backend.log 2>&1 &
BACKEND_PID=$!

echo "📍 Starting frontend server..."
cd /Users/<USER>/Desktop/RENTAHUB/frontend
npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

echo "⏳ Waiting for servers to start..."
sleep 5

echo "🔍 Testing backend health..."
curl -f http://localhost:3001/api/health && echo "✅ Backend is healthy" || echo "❌ Backend is not responding"

echo "🔍 Testing frontend..."
curl -f http://localhost:5173 > /dev/null && echo "✅ Frontend is running" || echo "❌ Frontend is not responding"

echo "📊 Server PIDs: Backend=$BACKEND_PID, Frontend=$FRONTEND_PID"
echo "📋 Check logs:"
echo "  Backend: tail -f /Users/<USER>/Desktop/RENTAHUB/logs/backend.log"
echo "  Frontend: tail -f /Users/<USER>/Desktop/RENTAHUB/logs/frontend.log"
