# RENTAHUB Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/rentahub
DATABASE_PROVIDER=postgresql

# Authentication Secrets
JWT_SECRET=your_very_long_and_secure_random_secret_key
JWT_EXPIRATION=24h

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_signing_secret

# Email Service Configuration
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_SERVICE_API_KEY=your_email_service_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
FRONTEND_PORT=3000

# Backend Configuration
BACKEND_URL=http://localhost:3001
BACKEND_PORT=3001

# Deployment Platform
DEPLOY_PLATFORM=local  # Options: local, railway

# Logging and Monitoring
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# Feature Flags
ENABLE_GOOGLE_AUTH=true
ENABLE_EMAIL_AUTH=true
ENABLE_STRIPE_PAYMENTS=true

# Security Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://rentahub.com
RATE_LIMIT_WINDOW_MS=15 * 60 * 1000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # max requests per window

# Optional: Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password

# Optional: Third-Party Integrations
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
