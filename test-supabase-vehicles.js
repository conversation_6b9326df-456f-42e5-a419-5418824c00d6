const { createClient } = require('@supabase/supabase-js');

// Supabase credentials from frontend/.env
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testVehicles() {
  console.log('🔍 Testing Supabase vehicle data...');
  
  try {
    // Test vehicles table
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('id, make, model, year, engine_size, category, images, daily_rate, description')
      .eq('active', true)
      .limit(5);

    if (vehiclesError) {
      console.error('❌ Error fetching vehicles:', vehiclesError);
    } else {
      console.log(`✅ Found ${vehicles?.length || 0} vehicles in vehicles table:`, vehicles);
    }

    // Test vehicle_catalog table (if it exists)
    const { data: catalog, error: catalogError } = await supabase
      .from('vehicle_catalog')
      .select('*')
      .limit(5);

    if (catalogError) {
      console.log('ℹ️ vehicle_catalog table does not exist or has no data');
    } else {
      console.log(`✅ Found ${catalog?.length || 0} vehicles in vehicle_catalog table:`, catalog);
    }

    // Test scraped_vehicles table (if it exists)
    const { data: scraped, error: scrapedError } = await supabase
      .from('scraped_vehicles')
      .select('*')
      .limit(5);

    if (scrapedError) {
      console.log('ℹ️ scraped_vehicles table does not exist or has no data');
    } else {
      console.log(`✅ Found ${scraped?.length || 0} vehicles in scraped_vehicles table:`, scraped);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testVehicles(); 