#!/usr/bin/env node

/**
 * Test Provider API endpoints to verify backend connectivity
 */

const API_BASE_URL = 'http://localhost:3001';
const TEST_USER_ID = 'dcd4a493-ba65-4406-a65f-df364b141c27';

console.log('🧪 Testing Provider API Endpoints\n');

async function testEndpoint(endpoint, description) {
  try {
    console.log(`🔍 Testing: ${description}`);
    console.log(`   URL: ${API_BASE_URL}${endpoint}`);
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': TEST_USER_ID
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log(`   ✅ Status: ${response.status} - ${response.statusText}`);
      console.log(`   📊 Response: ${JSON.stringify(data).substring(0, 100)}...`);
    } else {
      console.log(`   ⚠️  Status: ${response.status} - ${response.statusText}`);
      console.log(`   📊 Response: ${JSON.stringify(data)}`);
    }
    
    return { success: response.ok, status: response.status, data };
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting API connectivity tests...\n');
  
  // Test basic health check
  await testEndpoint('/health', 'Health Check');
  console.log('');
  
  // Test provider endpoints
  const providerEndpoints = [
    { endpoint: '/api/providers/me', description: 'Get Current Provider Profile' },
    { endpoint: '/api/providers/dashboard/stats', description: 'Get Dashboard Statistics' },
    { endpoint: '/api/providers/vehicles?page=1&limit=20', description: 'Get Provider Vehicles' },
    { endpoint: '/api/providers/profile', description: 'Get Provider Profile' },
    { endpoint: '/api/providers/payment-states', description: 'Get Payment States' }
  ];
  
  let successCount = 0;
  let totalTests = providerEndpoints.length;
  
  for (const { endpoint, description } of providerEndpoints) {
    const result = await testEndpoint(endpoint, description);
    if (result.success || result.status < 500) {
      successCount++;
    }
    console.log('');
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log('================');
  console.log(`✅ Successful/Valid responses: ${successCount}/${totalTests}`);
  console.log(`❌ Failed responses: ${totalTests - successCount}/${totalTests}`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 All API endpoints are responding correctly!');
    console.log('✅ Backend server is running and accessible');
    console.log('✅ Provider dashboard should now work properly');
  } else if (successCount > 0) {
    console.log('\n⚠️  Some endpoints are working, but there may be authentication or data issues');
    console.log('💡 This is normal if the user doesn\'t exist in the database yet');
  } else {
    console.log('\n❌ No endpoints are working - check backend server status');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('- Check the browser console for any remaining connection errors');
  console.log('- Verify that the frontend is using http://localhost:3001 as API URL');
  console.log('- Test the provider dashboard in the browser');
}

// Run the tests
runTests().catch(console.error);
