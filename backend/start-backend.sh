#!/bin/bash

# Simple backend starter script for RentaHub
echo "🚀 Starting RentaHub Backend Server..."

# Change to backend directory
cd "$(dirname "$0")"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Make sure you're in the backend directory."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

echo "✅ Starting development server on port 3001..."
echo "📊 Health check will be available at: http://localhost:3001/health"
echo "📖 API documentation at: http://localhost:3001/api-docs"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the server
npm run dev
