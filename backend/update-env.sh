#!/bin/bash

echo "Updating RENTAHUB environment variables with real credentials..."

# Create .env file with real values
cat > .env << 'EOF'
# RENTAHUB Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/rentahub
DATABASE_PROVIDER=postgresql

# Server Configuration
PORT=3000
NODE_ENV=development

# Authentication Secrets
JWT_SECRET=rentahub_jwt_secret_2025_secure_random_string_32_chars_minimum
JWT_EXPIRATION=24h

# Google OAuth Configuration
GOOGLE_CLIENT_ID=177790503193-5g624cighvbma0n0nrrg4oqljagdegi5.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_51RhDTEFSJ2b2gFS10sW4gFLJ
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_13bb090c39c0e6d3a3d4d609de2e1ce3092100fae2f40393f0a5bc6b72ee438a

# Email Configuration (Elastic Email)
SMTP_HOST=smtp.elasticemail.com
SMTP_PORT=2525
SMTP_USER=<EMAIL>
SMTP_PASS=38187DC40E1EBD017958C403379C0065E5DE

# Email Identity
SUPPORT_EMAIL=<EMAIL>
EMERGENCY_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Application Configuration
APP_NAME=RENTAHUB
APP_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3001

# Security Configuration
CORS_ORIGIN=http://localhost:3001
SESSION_SECRET=rentahub_session_secret_2025_secure_random_string

# Logging Configuration
LOG_LEVEL=info
ENABLE_DEBUG_LOGGING=true

# Feature Flags
ENABLE_STRIPE_PAYMENTS=true
ENABLE_GOOGLE_OAUTH=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_WEBHOOK_LOGGING=true
EOF

echo "✅ Environment file updated successfully!"
echo "📝 Please review the .env file and update any values that need to be changed."
echo "🔧 You may need to update:"
echo "   - DATABASE_URL (if your Postgres password is different)"
echo "   - STRIPE_PUBLISHABLE_KEY (if you have it)"
echo "   - Any other values specific to your setup" 