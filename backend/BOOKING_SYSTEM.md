# RentaHub Booking System

## Overview
The RentaHub Booking System is a comprehensive solution for managing two-wheeler rentals, providing a flexible and secure booking workflow.

## Key Features
- Dual Payment Method Support (Card and Cash)
- Conflict Prevention Mechanism
- Flexible Booking Status Management
- Real-time Availability Tracking

## Booking Workflow

### Booking Status Lifecycle
1. **PENDING**: Initial state for cash bookings
2. **CONFIRMED**: Approved bookings (auto for card, manual for cash)
3. **IN_PROGRESS**: Booking is currently active
4. **COMPLETED**: Booking has finished
5. **CANCELLED**: Booking was cancelled by user or provider

### Booking Creation Rules
- Card Bookings: Automatically confirmed with immediate payment
- Cash Bookings: Require provider manual confirmation
- Prevents overlapping bookings for the same vehicle
- Validates booking dates against vehicle availability

## Payment Methods
### Card Payment
- Instant confirmation
- Full payment processed at booking
- Automatic status update to CONFIRMED

### Cash Payment
- Initial status set to PENDING
- Requires provider review
- Manual confirmation/rejection process

## Conflict Prevention
- Real-time availability checking
- Transaction-level locking
- Prevents double booking scenarios

## Technical Components
- `BookingService`: Core booking logic
- `BookingController`: API request handling
- `NotificationService`: Status update notifications
- `PaymentService`: Payment processing

## Security Considerations
- Role-based access control
- Input validation
- Secure payment processing
- Comprehensive error handling

## Testing Strategy
- Unit tests for individual components
- Integration tests for workflow scenarios
- Edge case coverage
- Mock payment and notification services

## Performance Optimizations
- Efficient database queries
- Minimal locking overhead
- Caching of availability information

## Future Enhancements
- Machine learning-based pricing
- Advanced analytics
- Multi-vehicle booking support

## Troubleshooting
- Check server logs for detailed error information
- Verify payment gateway configurations
- Ensure proper role assignments

## Dependencies
- Node.js
- Prisma ORM
- Socket.io
- Stripe/PayPal Payment Gateways

## Configuration
Ensure the following environment variables are set:
- `DATABASE_URL`: Prisma database connection
- `PAYMENT_GATEWAY_KEY`: Payment gateway credentials
- `SOCKET_URL`: Websocket server URL

## Installation
```bash
npm install
npx prisma generate
npm run migrate
npm run test
npm run start
```

## Contributing
Please read CONTRIBUTING.md for details on our code of conduct and the process for submitting pull requests. 