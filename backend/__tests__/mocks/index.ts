export const mockBooking = {
  id: 'booking_123',
  customerId: 'user_456',
  ownerId: 'owner_789',
  vehicleName: 'Tesla Model 3',
  dailyRate: 100,
  rentalDays: 3,
  status: 'confirmed'
};

export const mockUser = {
  id: 'user_456',
  email: '<EMAIL>',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  verified: true,
  is_provider: false
};

export const mockPaymentResult = {
  stripe: {
    success: true,
    paymentIntentId: 'stripe_intent_123',
    clientSecret: 'stripe_client_secret_123',
    status: 'pending'
  },
  paypal: {
    success: true,
    paymentIntentId: 'paypal_intent_456',
    clientSecret: 'https://paypal.com/approval',
    status: 'pending'
  }
};
