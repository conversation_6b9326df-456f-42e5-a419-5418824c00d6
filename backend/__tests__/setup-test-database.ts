import 'dotenv/config';
import DatabaseService from '../src/services/DatabaseService';

/**
 * Test Database Setup Script
 * 
 * This script ensures the test database is properly configured
 * and contains the necessary data for testing.
 */

async function setupTestDatabase() {
  console.log('🗄️ Setting up test database...');

  try {
    // Test database connection
    const isConnected = await DatabaseService.testConnection();
    if (!isConnected) {
      throw new Error('Cannot connect to test database');
    }
    console.log('✅ Database connection verified');

    // Verify essential tables exist
    const tables = [
      'users',
      'vehicles', 
      'bookings',
      'payments',
      'reviews',
      'vehicle_catalog'
    ];

    for (const table of tables) {
      try {
        const result = await DatabaseService.query(`SELECT COUNT(*) FROM ${table} LIMIT 1`);
        console.log(`✅ Table '${table}' exists and accessible`);
      } catch (error) {
        console.error(`❌ Table '${table}' not accessible:`, error);
        throw new Error(`Required table '${table}' is not available`);
      }
    }

    // Ensure vehicle catalog has data for testing
    const catalogResult = await DatabaseService.query(
      'SELECT COUNT(*) as count FROM vehicle_catalog'
    );
    
    const catalogCount = catalogResult.data?.[0]?.count || 0;
    if (catalogCount === 0) {
      console.log('📋 Vehicle catalog is empty, seeding basic data...');
      
      // Add basic vehicle catalog entries for testing
      const basicCatalogData = [
        {
          make: 'Honda',
          model: 'Scoopy',
          category: 'small_scooter',
          engine_size: '110cc',
          fuel_type: 'gasoline',
          transmission: 'automatic',
          features: ['Helmet included', 'Under-seat storage']
        },
        {
          make: 'Yamaha',
          model: 'NMAX',
          category: 'medium_scooter',
          engine_size: '155cc',
          fuel_type: 'gasoline',
          transmission: 'automatic',
          features: ['ABS', 'Smart key', 'USB charging']
        },
        {
          make: 'Honda',
          model: 'PCX',
          category: 'medium_scooter',
          engine_size: '160cc',
          fuel_type: 'gasoline',
          transmission: 'automatic',
          features: ['Smart key', 'LED lights', 'Large storage']
        }
      ];

      for (const item of basicCatalogData) {
        await DatabaseService.query(
          `INSERT INTO vehicle_catalog (make, model, category, engine_size, fuel_type, transmission, features)
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [item.make, item.model, item.category, item.engine_size, item.fuel_type, item.transmission, JSON.stringify(item.features)]
        );
      }
      
      console.log('✅ Basic vehicle catalog data seeded');
    } else {
      console.log(`✅ Vehicle catalog has ${catalogCount} entries`);
    }

    // Clean up any existing test data
    console.log('🧹 Cleaning up existing test data...');
    
    // Delete test users and their related data
    const testEmailPatterns = ['%@test.com', '%@test.rentahub.com', '%test%@%'];
    
    for (const pattern of testEmailPatterns) {
      // Get test users
      const testUsers = await DatabaseService.query(
        'SELECT id FROM users WHERE email LIKE $1',
        [pattern]
      );

      if (testUsers.data && testUsers.data.length > 0) {
        const userIds = testUsers.data.map(user => user.id);
        
        // Delete related data first (due to foreign key constraints)
        await DatabaseService.query(
          'DELETE FROM bookings WHERE customer_id = ANY($1) OR provider_id = ANY($1)',
          [userIds]
        );
        
        await DatabaseService.query(
          'DELETE FROM vehicles WHERE provider_id = ANY($1)',
          [userIds]
        );
        
        await DatabaseService.query(
          'DELETE FROM reviews WHERE user_id = ANY($1)',
          [userIds]
        );
        
        // Finally delete users
        await DatabaseService.query(
          'DELETE FROM users WHERE id = ANY($1)',
          [userIds]
        );
        
        console.log(`✅ Cleaned up ${userIds.length} test users and related data`);
      }
    }

    // Verify test environment variables
    const requiredEnvVars = [
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'JWT_SECRET'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.warn(`⚠️ Environment variable ${envVar} is not set`);
      } else {
        console.log(`✅ Environment variable ${envVar} is configured`);
      }
    }

    console.log('🎉 Test database setup complete!');
    
    return {
      success: true,
      message: 'Test database is ready for testing'
    };

  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    throw error;
  }
}

// Run setup if called directly
if (require.main === module) {
  setupTestDatabase()
    .then(() => {
      console.log('✅ Test database setup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test database setup failed:', error);
      process.exit(1);
    });
}

export default setupTestDatabase;
