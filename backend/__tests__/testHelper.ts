import request from 'supertest';
import { nanoid } from 'nanoid';
import { createClient } from '@supabase/supabase-js';
import app from '../src/server';
import ProductionAuthService from '../src/services/ProductionAuthService';
import DatabaseService from '../src/services/DatabaseService';

// Test configuration - using the existing superuser for all tests
export const TEST_CONFIG = {
  ADMIN_EMAIL: '<EMAIL>',
  PROVIDER_EMAIL: '<EMAIL>',
  CUSTOMER_EMAIL: '<EMAIL>',
  PASSWORD: 'Password1234',
  LANGUAGE: 'en',
  PAGE: 1,
  SIZE: 30,
  TIMEOUT: 30000
};

// Test user IDs (will be set during initialization)
let ADMIN_USER_ID: string;
let PROVIDER_USER_ID: string;
let CUSTOMER_USER_ID: string;

// Test tokens
let ADMIN_TOKEN: string;
let PROVIDER_TOKEN: string;
let CUSTOMER_TOKEN: string;

// Utility functions
export const getName = (prefix: string): string => {
  expect(prefix.length).toBeGreaterThan(1);
  return `${prefix}.${nanoid()}`.toLowerCase();
};

export const getVehicleName = (): string => getName('vehicle');
export const getBookingName = (): string => getName('booking');

export const delay = (milliseconds: number): Promise<void> => 
  new Promise((resolve) => setTimeout(resolve, milliseconds));

// Database helpers
export const cleanupTestData = async (): Promise<void> => {
  try {
    // Clean up test users and related data
    const testEmails = [
      TEST_CONFIG.ADMIN_EMAIL,
      TEST_CONFIG.PROVIDER_EMAIL,
      TEST_CONFIG.CUSTOMER_EMAIL
    ];

    for (const email of testEmails) {
      const user = await DatabaseService.getUserByEmail(email);
      if (user) {
        // Clean up user's vehicles
        const vehicles = await DatabaseService.getVehicles({ providerId: user.id, page: 1, limit: 100 });
        if (vehicles.success && vehicles.data) {
          for (const vehicle of vehicles.data) {
            await DatabaseService.deleteVehicle(vehicle.id);
          }
        }

        // Clean up user's bookings
        const bookings = await DatabaseService.getBookings({ userId: user.id, page: 1, limit: 100 });
        if (bookings.success && bookings.data) {
          for (const booking of bookings.data) {
            await DatabaseService.deleteBooking(booking.id);
          }
        }

        // Delete user
        await DatabaseService.deleteUser(user.id);
      }
    }
  } catch (error) {
    console.warn('Cleanup warning:', error);
  }
};

// Test user initialization
export const initializeTestUsers = async (): Promise<void> => {
  try {
    // Create admin user
    const adminResult = await ProductionAuthService.register({
      email: TEST_CONFIG.ADMIN_EMAIL,
      password: TEST_CONFIG.PASSWORD,
      first_name: 'Test',
      last_name: 'Admin',
      role: 'ADMIN'
    });

    if (adminResult.success && adminResult.user && adminResult.token) {
      ADMIN_USER_ID = adminResult.user.id;
      ADMIN_TOKEN = adminResult.token;
    }

    // Create provider user
    const providerResult = await ProductionAuthService.register({
      email: TEST_CONFIG.PROVIDER_EMAIL,
      password: TEST_CONFIG.PASSWORD,
      first_name: 'Test',
      last_name: 'Provider',
      role: 'PROVIDER'
    });

    if (providerResult.success && providerResult.user && providerResult.token) {
      PROVIDER_USER_ID = providerResult.user.id;
      PROVIDER_TOKEN = providerResult.token;
    }

    // Create customer user
    const customerResult = await ProductionAuthService.register({
      email: TEST_CONFIG.CUSTOMER_EMAIL,
      password: TEST_CONFIG.PASSWORD,
      first_name: 'Test',
      last_name: 'Customer',
      role: 'CUSTOMER'
    });

    if (customerResult.success && customerResult.user && customerResult.token) {
      CUSTOMER_USER_ID = customerResult.user.id;
      CUSTOMER_TOKEN = customerResult.token;
    }

    console.log('✅ Test users initialized');
  } catch (error) {
    console.error('❌ Failed to initialize test users:', error);
    throw error;
  }
};

// Authentication helpers
export const getAdminToken = (): string => ADMIN_TOKEN;
export const getProviderToken = (): string => PROVIDER_TOKEN;
export const getCustomerToken = (): string => CUSTOMER_TOKEN;

export const getAdminUserId = (): string => ADMIN_USER_ID;
export const getProviderUserId = (): string => PROVIDER_USER_ID;
export const getCustomerUserId = (): string => CUSTOMER_USER_ID;

// Request helpers
export const authenticatedRequest = (token: string) => {
  return request(app).set('Authorization', `Bearer ${token}`);
};

export const adminRequest = () => authenticatedRequest(getAdminToken());
export const providerRequest = () => authenticatedRequest(getProviderToken());
export const customerRequest = () => authenticatedRequest(getCustomerToken());

// Test data factories
export const createTestVehicle = (providerId?: string) => ({
  provider_id: providerId || getProviderUserId(),
  make: 'Honda',
  model: 'Scoopy',
  year: 2023,
  category: 'small_scooter',
  daily_rate: 75000,
  location_city: 'Jakarta',
  location_address: 'Test Address',
  description: 'Test vehicle for unit testing',
  features: ['Helmet included', 'Test feature'],
  images: ['https://test-image.jpg'],
  license_plate: `TEST${nanoid(4)}`,
  engine_size: '110cc',
  fuel_type: 'gasoline',
  transmission: 'automatic',
  helmet_included: true,
  insurance_included: false,
  deposit_required: 200000
});

export const createTestBooking = (customerId?: string, providerId?: string, vehicleId?: string) => ({
  customer_id: customerId || getCustomerUserId(),
  provider_id: providerId || getProviderUserId(),
  vehicle_id: vehicleId || 'test-vehicle-id',
  start_date: '2024-02-01',
  end_date: '2024-02-03',
  start_time: '09:00',
  end_time: '18:00',
  pickup_location: 'Test Pickup Location',
  dropoff_location: 'Test Dropoff Location',
  total_amount: 225000,
  base_amount: 200000,
  tax_amount: 20000,
  service_fee: 5000,
  payment_status: 'pending',
  special_requests: 'Test booking request'
});

// Validation helpers
export const expectValidUser = (user: any) => {
  expect(user).toBeDefined();
  expect(user.id).toBeDefined();
  expect(user.email).toBeDefined();
  expect(user.role).toBeDefined();
  expect(user.created_at).toBeDefined();
};

export const expectValidVehicle = (vehicle: any) => {
  expect(vehicle).toBeDefined();
  expect(vehicle.id).toBeDefined();
  expect(vehicle.make).toBeDefined();
  expect(vehicle.model).toBeDefined();
  expect(vehicle.category).toBeDefined();
  expect(vehicle.daily_rate).toBeDefined();
};

export const expectValidBooking = (booking: any) => {
  expect(booking).toBeDefined();
  expect(booking.id).toBeDefined();
  expect(booking.customer_id).toBeDefined();
  expect(booking.provider_id).toBeDefined();
  expect(booking.vehicle_id).toBeDefined();
  expect(booking.total_amount).toBeDefined();
};

// Error testing helpers
export const expectApiError = (response: any, statusCode: number, errorMessage?: string) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(false);
  if (errorMessage) {
    expect(response.body.error).toContain(errorMessage);
  }
};

export const expectApiSuccess = (response: any, statusCode: number = 200) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(true);
};

// Export app for testing
export { app };
