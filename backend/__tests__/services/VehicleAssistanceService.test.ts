import { VehicleAssistanceService } from '../../src/services/VehicleAssistanceService';
import { PrismaClient } from '@prisma/client';
import { EmailService } from '../../src/services/EmailService';
import { NotificationService } from '../../src/services/NotificationService';

// Mock dependencies
jest.mock('@prisma/client');
jest.mock('../../src/services/EmailService');
jest.mock('../../src/services/NotificationService');

const mockPrisma = {
  vehicleAssistanceRequest: {
    create: jest.fn(),
    findMany: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    groupBy: jest.fn(),
    findUnique: jest.fn()
  },
  user: {
    findUnique: jest.fn()
  }
};

const mockEmailService = {
  sendEmail: jest.fn()
};

const mockNotificationService = {
  sendNotification: jest.fn()
};

describe('VehicleAssistanceService', () => {
  let service: VehicleAssistanceService;

  beforeEach(() => {
    jest.clearAllMocks();
    (PrismaClient as jest.Mock).mockImplementation(() => mockPrisma);
    (EmailService as jest.Mock).mockImplementation(() => mockEmailService);
    (NotificationService as jest.Mock).mockImplementation(() => mockNotificationService);
    
    service = new VehicleAssistanceService();
  });

  describe('createAssistanceRequest', () => {
    const validRequestData = {
      userId: 'user123',
      reason: 'Engine stopped working',
      category: 'BREAKDOWN' as const,
      priority: 'HIGH' as const,
      location: 'Jl. Sudirman No. 123, Jakarta'
    };

    it('should create a new assistance request successfully', async () => {
      const mockRequest = {
        id: 'req123',
        ...validRequestData,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.vehicleAssistanceRequest.findFirst.mockResolvedValue(null);
      mockPrisma.vehicleAssistanceRequest.create.mockResolvedValue(mockRequest);

      const result = await service.createAssistanceRequest(validRequestData);

      expect(result).toEqual(mockRequest);
      expect(mockPrisma.vehicleAssistanceRequest.create).toHaveBeenCalledWith({
        data: {
          userId: validRequestData.userId,
          reason: validRequestData.reason,
          category: validRequestData.category,
          priority: validRequestData.priority,
          location: validRequestData.location,
          status: 'PENDING'
        }
      });
    });

    it('should throw error if required fields are missing', async () => {
      const invalidData = {
        userId: 'user123'
        // Missing required fields
      };

      await expect(service.createAssistanceRequest(invalidData as any))
        .rejects.toThrow('Missing required fields: userId, reason, and category are required');
    });

    it('should throw error if active request exists for same booking', async () => {
      const dataWithBooking = {
        ...validRequestData,
        bookingId: 'booking123'
      };

      mockPrisma.vehicleAssistanceRequest.findFirst.mockResolvedValue({
        id: 'existing123',
        status: 'PENDING'
      });

      await expect(service.createAssistanceRequest(dataWithBooking))
        .rejects.toThrow('An active assistance request already exists for this booking');
    });

    it('should auto-determine priority based on category', async () => {
      const accidentData = {
        ...validRequestData,
        category: 'ACCIDENT' as const,
        priority: undefined
      };

      const mockRequest = {
        id: 'req123',
        ...accidentData,
        priority: 'URGENT',
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.vehicleAssistanceRequest.findFirst.mockResolvedValue(null);
      mockPrisma.vehicleAssistanceRequest.create.mockResolvedValue(mockRequest);

      await service.createAssistanceRequest(accidentData);

      expect(mockPrisma.vehicleAssistanceRequest.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          priority: 'URGENT'
        })
      });
    });

    it('should send admin notification for new request', async () => {
      const mockRequest = {
        id: 'req123',
        ...validRequestData,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.vehicleAssistanceRequest.findFirst.mockResolvedValue(null);
      mockPrisma.vehicleAssistanceRequest.create.mockResolvedValue(mockRequest);

      await service.createAssistanceRequest(validRequestData);

      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: expect.stringContaining('Vehicle Assistance Request'),
          template: 'vehicle_assistance_alert'
        })
      );
    });
  });

  describe('getAssistanceRequestsByUser', () => {
    it('should return user assistance requests', async () => {
      const userId = 'user123';
      const mockRequests = [
        {
          id: 'req1',
          userId,
          category: 'BREAKDOWN',
          status: 'PENDING',
          createdAt: new Date()
        }
      ];

      mockPrisma.vehicleAssistanceRequest.findMany.mockResolvedValue(mockRequests);

      const result = await service.getAssistanceRequestsByUser(userId);

      expect(result).toEqual(mockRequests);
      expect(mockPrisma.vehicleAssistanceRequest.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        include: expect.any(Object)
      });
    });
  });

  describe('updateAssistanceRequestStatus', () => {
    it('should update request status successfully', async () => {
      const requestId = 'req123';
      const newStatus = 'RESOLVED';
      const adminNotes = 'Issue resolved by technician';

      const mockUpdatedRequest = {
        id: requestId,
        status: newStatus,
        adminNotes,
        resolvedAt: new Date(),
        user: {
          id: 'user123',
          name: 'John Doe',
          email: '<EMAIL>'
        }
      };

      mockPrisma.vehicleAssistanceRequest.update.mockResolvedValue(mockUpdatedRequest);

      const result = await service.updateAssistanceRequestStatus(requestId, newStatus, adminNotes);

      expect(result).toEqual(mockUpdatedRequest);
      expect(mockPrisma.vehicleAssistanceRequest.update).toHaveBeenCalledWith({
        where: { id: requestId },
        data: {
          status: newStatus,
          adminNotes,
          resolvedAt: expect.any(Date),
          updatedAt: expect.any(Date)
        },
        include: expect.any(Object)
      });
    });

    it('should send status update notification', async () => {
      const requestId = 'req123';
      const newStatus = 'IN_PROGRESS';

      const mockUpdatedRequest = {
        id: requestId,
        status: newStatus,
        user: {
          id: 'user123',
          name: 'John Doe',
          email: '<EMAIL>'
        }
      };

      mockPrisma.vehicleAssistanceRequest.update.mockResolvedValue(mockUpdatedRequest);

      await service.updateAssistanceRequestStatus(requestId, newStatus);

      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: expect.stringContaining('Assistance Request Update'),
          template: 'assistance_status_update'
        })
      );
    });
  });

  describe('getAllAssistanceRequests', () => {
    it('should return paginated assistance requests for admin', async () => {
      const mockRequests = [
        { id: 'req1', status: 'PENDING' },
        { id: 'req2', status: 'RESOLVED' }
      ];
      const totalCount = 25;

      mockPrisma.vehicleAssistanceRequest.findMany.mockResolvedValue(mockRequests);
      mockPrisma.vehicleAssistanceRequest.count.mockResolvedValue(totalCount);

      const result = await service.getAllAssistanceRequests(undefined, undefined, 10, 0);

      expect(result).toEqual({
        requests: mockRequests,
        total: totalCount
      });
      expect(mockPrisma.vehicleAssistanceRequest.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        take: 10,
        skip: 0,
        include: expect.any(Object)
      });
    });

    it('should filter by status and priority', async () => {
      const status = 'PENDING';
      const priority = 'HIGH';

      mockPrisma.vehicleAssistanceRequest.findMany.mockResolvedValue([]);
      mockPrisma.vehicleAssistanceRequest.count.mockResolvedValue(0);

      await service.getAllAssistanceRequests(status, priority, 10, 0);

      expect(mockPrisma.vehicleAssistanceRequest.findMany).toHaveBeenCalledWith({
        where: { status, priority },
        orderBy: expect.any(Array),
        take: 10,
        skip: 0,
        include: expect.any(Object)
      });
    });
  });

  describe('determinePriority', () => {
    it('should return URGENT for ACCIDENT category', () => {
      const priority = (service as any).determinePriority('ACCIDENT');
      expect(priority).toBe('URGENT');
    });

    it('should return HIGH for BREAKDOWN category', () => {
      const priority = (service as any).determinePriority('BREAKDOWN');
      expect(priority).toBe('HIGH');
    });

    it('should return MEDIUM for FLAT_TIRE category', () => {
      const priority = (service as any).determinePriority('FLAT_TIRE');
      expect(priority).toBe('MEDIUM');
    });

    it('should return LOW for KEY_LOCKED category', () => {
      const priority = (service as any).determinePriority('KEY_LOCKED');
      expect(priority).toBe('LOW');
    });

    it('should override with requested priority', () => {
      const priority = (service as any).determinePriority('BREAKDOWN', 'LOW');
      expect(priority).toBe('LOW');
    });
  });

  describe('getEstimatedResponseTime', () => {
    it('should return correct response times for each priority', () => {
      expect((service as any).getEstimatedResponseTime('URGENT')).toBe('15-30 minutes');
      expect((service as any).getEstimatedResponseTime('HIGH')).toBe('30-60 minutes');
      expect((service as any).getEstimatedResponseTime('MEDIUM')).toBe('1-2 hours');
      expect((service as any).getEstimatedResponseTime('LOW')).toBe('2-4 hours');
    });
  });
});

describe('VehicleAssistanceService Integration Tests', () => {
  // These would be integration tests that actually connect to a test database
  // and test the full flow including email sending and notifications
  
  it.skip('should create assistance request and send all notifications', async () => {
    // Integration test implementation
  });

  it.skip('should handle concurrent requests for same booking correctly', async () => {
    // Integration test implementation
  });

  it.skip('should properly clean up resources on service shutdown', async () => {
    // Integration test implementation
  });
});
