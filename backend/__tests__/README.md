# RentaHub Testing Framework

## Overview

This directory contains a comprehensive testing framework for the RentaHub backend, including unit tests, integration tests, and end-to-end tests using both <PERSON><PERSON> and Playwright.

## Test Structure

```
__tests__/
├── unit/                    # Unit tests for individual components
│   ├── auth.test.ts        # Authentication service tests
│   ├── vehicle.test.ts     # Vehicle management tests
│   ├── booking.test.ts     # Booking service tests
│   ├── payment.test.ts     # Payment processing tests
│   ├── profile.test.ts     # User profile tests
│   └── error-scenarios.test.ts # Edge case testing
├── integration/             # Integration tests for API endpoints
│   └── error-scenarios.test.ts # Complex error scenario testing
├── e2e/                    # End-to-end tests with Playwright
│   ├── auth-flow.test.ts   # Authentication flow testing
│   ├── booking-flow.test.ts # Complete booking workflow
│   ├── payment-flow.test.ts # Payment processing workflow
│   ├── helpers.ts          # E2E test utilities and page objects
│   ├── global-setup.ts     # E2E environment setup
│   └── global-teardown.ts  # E2E environment cleanup
├── testHelper.ts           # Shared test utilities
├── globalSetup.ts          # Jest global setup
├── globalTeardown.ts       # Jest global teardown
├── setupTests.ts           # Test environment configuration
├── setup-test-database.ts  # Database setup for testing
└── README.md              # This file
```

## Test Types

### 1. Unit Tests
- **Purpose**: Test individual functions and components in isolation
- **Framework**: Jest with Supertest
- **Coverage**: Authentication, Vehicle management, Booking logic, Payment processing, Profile management
- **Location**: `__tests__/unit/`

### 2. Integration Tests
- **Purpose**: Test API endpoints and service interactions
- **Framework**: Jest with Supertest
- **Coverage**: Error scenarios, edge cases, system resilience
- **Location**: `__tests__/integration/`

### 3. End-to-End Tests
- **Purpose**: Test complete user workflows in a browser environment
- **Framework**: Playwright
- **Coverage**: Authentication flows, booking workflows, payment processing
- **Location**: `__tests__/e2e/`

## Running Tests

### Prerequisites
1. Ensure all dependencies are installed: `npm install`
2. Set up environment variables in `.env` file
3. Ensure database is accessible and properly configured

### Quick Start
```bash
# Run all Jest tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test types
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e          # Jest E2E tests only

# Run Playwright E2E tests
npm run test:playwright

# Run tests in watch mode
npm run test:watch

# Use the comprehensive test runner
./run-tests.sh --help      # See all options
./run-tests.sh --unit      # Run unit tests
./run-tests.sh --playwright # Run Playwright tests
./run-tests.sh --coverage  # Run with coverage
```

### Test Runner Script
The `run-tests.sh` script provides a comprehensive testing interface:

```bash
# Basic usage
./run-tests.sh                    # Run all Jest tests
./run-tests.sh --unit            # Run only unit tests
./run-tests.sh --integration     # Run only integration tests
./run-tests.sh --playwright      # Run Playwright E2E tests
./run-tests.sh --coverage        # Generate coverage report
./run-tests.sh --watch           # Run in watch mode
./run-tests.sh --verbose         # Verbose output
```

## Test Configuration

### Jest Configuration (`jest.config.json`)
- TypeScript support with ts-jest
- Coverage reporting (text, lcov, html)
- Global setup and teardown
- Test timeout: 5 minutes
- Single worker for database consistency

### Playwright Configuration (`playwright.config.ts`)
- Multi-browser testing (Chrome, Firefox, Safari)
- Mobile device testing
- Automatic server startup
- Screenshot and video on failure
- Trace collection for debugging

## Test Utilities

### testHelper.ts
Provides shared utilities for all tests:
- Test user management
- Database cleanup functions
- Request helpers with authentication
- Test data factories
- Validation helpers

### E2E Helpers (`e2e/helpers.ts`)
Page object models and utilities for E2E tests:
- `AuthPage` - Authentication workflows
- `VehiclePage` - Vehicle search and selection
- `BookingPage` - Booking creation and management
- `PaymentPage` - Payment processing
- `DashboardPage` - User dashboard interactions

## Test Data Management

### Test Users
The framework automatically creates and manages test users:
- **Customer**: For booking and payment testing
- **Provider**: For vehicle management testing
- **Admin**: For administrative function testing

### Database Cleanup
- Automatic cleanup before and after test runs
- Isolated test data to prevent conflicts
- Proper foreign key constraint handling

## Error Scenario Testing

The framework includes comprehensive error scenario testing:

### Duplicate Booking Prevention
- Exact duplicate booking attempts
- Overlapping date ranges
- Concurrent booking attempts
- Race condition handling

### Payment Flow Errors
- Payment intent creation failures
- Invalid payment confirmations
- Webhook processing errors
- Payment timeout scenarios

### Availability Conflicts
- Vehicle status changes during booking
- Date validation edge cases
- Booking limits and restrictions

### System Resilience
- Rapid successive requests
- Malformed request bodies
- Database connection issues
- Rate limiting behavior

## Coverage Reports

Coverage reports are generated in multiple formats:
- **HTML**: `coverage/lcov-report/index.html` (opens automatically)
- **LCOV**: `coverage/lcov.info` (for CI/CD integration)
- **Text**: Console output during test run

Target coverage thresholds:
- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

## Debugging Tests

### Jest Tests
```bash
# Run specific test file
npm test auth.test.ts

# Run specific test case
npm test -- --testNamePattern="should login successfully"

# Debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Playwright Tests
```bash
# Run with browser visible
npm run test:playwright:headed

# Debug mode with browser dev tools
npm run test:playwright:debug

# Interactive UI mode
npm run test:playwright:ui
```

## Continuous Integration

The testing framework is designed for CI/CD integration:

### Environment Variables
```bash
NODE_ENV=test
SUPABASE_URL=your_test_database_url
SUPABASE_SERVICE_ROLE_KEY=your_test_service_key
JWT_SECRET=test_jwt_secret
```

### CI Configuration Example
```yaml
- name: Run Tests
  run: |
    npm install
    npm run build
    npm run test:coverage
    npm run test:playwright

- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/lcov.info
```

## Best Practices

### Writing Tests
1. **Descriptive Names**: Use clear, descriptive test names
2. **Arrange-Act-Assert**: Follow the AAA pattern
3. **Isolation**: Each test should be independent
4. **Cleanup**: Always clean up test data
5. **Assertions**: Use specific, meaningful assertions

### Test Data
1. **Unique Data**: Use unique identifiers for test data
2. **Realistic Data**: Use realistic test data that matches production
3. **Edge Cases**: Test boundary conditions and edge cases
4. **Error Scenarios**: Test both success and failure paths

### Performance
1. **Parallel Execution**: Use parallel test execution where possible
2. **Database Optimization**: Minimize database operations
3. **Test Isolation**: Avoid test interdependencies
4. **Resource Cleanup**: Clean up resources after tests

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify environment variables
   - Check database accessibility
   - Ensure proper permissions

2. **Test Timeouts**
   - Increase timeout values if needed
   - Check for hanging promises
   - Verify async/await usage

3. **Flaky Tests**
   - Add proper wait conditions
   - Use deterministic test data
   - Avoid time-dependent assertions

4. **Memory Issues**
   - Clean up test data properly
   - Close database connections
   - Avoid memory leaks in tests

### Getting Help
- Check test logs for detailed error messages
- Use debug mode for step-by-step execution
- Review test helper functions for proper usage
- Ensure all dependencies are properly installed
