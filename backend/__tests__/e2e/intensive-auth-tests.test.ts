import { test, expect } from '@playwright/test';

test.describe('Intensive Authentication Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Start fresh for each test
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Complete user registration flow - Customer', async ({ page }) => {
    console.log('🔐 Testing customer registration flow...');
    
    // Look for sign up elements
    const signUpSelectors = [
      'button:has-text("Sign Up")',
      'a:has-text("Sign Up")',
      'button:has-text("Register")',
      'a:has-text("Register")',
      '[href*="signup"]',
      '[href*="register"]',
      '.signup-btn',
      '.register-btn'
    ];
    
    let signUpFound = false;
    for (const selector of signUpSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found sign up element: ${selector}`);
        await page.locator(selector).first().click();
        signUpFound = true;
        break;
      }
    }
    
    if (!signUpFound) {
      console.log('⚠️ No sign up button found, checking URL patterns...');
      // Try direct navigation to common signup URLs
      const signupUrls = ['/signup', '/register', '/auth/signup', '/auth/register'];
      for (const url of signupUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error')) {
            console.log(`✅ Found signup page at: ${url}`);
            signUpFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible`);
        }
      }
    }
    
    if (signUpFound) {
      await page.waitForLoadState('networkidle');
      
      // Look for form fields
      const emailSelectors = [
        'input[type="email"]',
        'input[name*="email"]',
        '[data-testid*="email"]',
        '#email',
        '.email-input'
      ];
      
      const passwordSelectors = [
        'input[type="password"]',
        'input[name*="password"]',
        '[data-testid*="password"]',
        '#password',
        '.password-input'
      ];
      
      // Try to fill registration form
      const uniqueEmail = `test-customer-${Date.now()}@example.com`;
      
      for (const selector of emailSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().fill(uniqueEmail);
          console.log('✅ Filled email field');
          break;
        }
      }
      
      for (const selector of passwordSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().fill('TestPassword123!');
          console.log('✅ Filled password field');
          break;
        }
      }
      
      // Look for additional fields (name, etc.)
      const nameSelectors = [
        'input[name*="name"]',
        'input[name*="first"]',
        '[data-testid*="name"]',
        '#firstName',
        '#name'
      ];
      
      for (const selector of nameSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().fill('Test User');
          console.log('✅ Filled name field');
          break;
        }
      }
      
      // Take screenshot of filled form
      await page.screenshot({ path: 'test-results/registration-form-filled.png' });
      
      // Try to submit
      const submitSelectors = [
        'button[type="submit"]',
        'input[type="submit"]',
        'button:has-text("Sign Up")',
        'button:has-text("Register")',
        'button:has-text("Create")',
        '.submit-btn',
        '.register-btn'
      ];
      
      for (const selector of submitSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().click();
          console.log('✅ Clicked submit button');
          break;
        }
      }
      
      // Wait for response
      await page.waitForTimeout(3000);
      
      // Check for success or error messages
      const currentUrl = page.url();
      console.log(`📍 After registration URL: ${currentUrl}`);
      
      // Take screenshot of result
      await page.screenshot({ path: 'test-results/registration-result.png' });
      
    } else {
      console.log('⚠️ Could not find registration form - this may indicate the auth system needs setup');
    }
  });

  test('Complete user login flow', async ({ page }) => {
    console.log('🔐 Testing login flow...');
    
    // Look for sign in elements
    const signInSelectors = [
      'button:has-text("Sign In")',
      'a:has-text("Sign In")',
      'button:has-text("Login")',
      'a:has-text("Login")',
      '[href*="signin"]',
      '[href*="login"]',
      '.signin-btn',
      '.login-btn'
    ];
    
    let signInFound = false;
    for (const selector of signInSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found sign in element: ${selector}`);
        await page.locator(selector).first().click();
        signInFound = true;
        break;
      }
    }
    
    if (!signInFound) {
      // Try direct navigation
      const loginUrls = ['/signin', '/login', '/auth/signin', '/auth/login'];
      for (const url of loginUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error')) {
            console.log(`✅ Found login page at: ${url}`);
            signInFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible`);
        }
      }
    }
    
    if (signInFound) {
      await page.waitForLoadState('networkidle');
      
      // Try to fill login form with test credentials
      const emailSelectors = [
        'input[type="email"]',
        'input[name*="email"]',
        '[data-testid*="email"]',
        '#email'
      ];
      
      const passwordSelectors = [
        'input[type="password"]',
        'input[name*="password"]',
        '[data-testid*="password"]',
        '#password'
      ];
      
      // Use test credentials
      for (const selector of emailSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().fill('<EMAIL>');
          console.log('✅ Filled email field');
          break;
        }
      }
      
      for (const selector of passwordSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().fill('testpassword');
          console.log('✅ Filled password field');
          break;
        }
      }
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/login-form-filled.png' });
      
      // Try to submit
      const submitSelectors = [
        'button[type="submit"]',
        'input[type="submit"]',
        'button:has-text("Sign In")',
        'button:has-text("Login")',
        '.submit-btn',
        '.login-btn'
      ];
      
      for (const selector of submitSelectors) {
        if (await page.locator(selector).count() > 0) {
          await page.locator(selector).first().click();
          console.log('✅ Clicked login button');
          break;
        }
      }
      
      // Wait for response
      await page.waitForTimeout(3000);
      
      const currentUrl = page.url();
      console.log(`📍 After login URL: ${currentUrl}`);
      
      // Take screenshot of result
      await page.screenshot({ path: 'test-results/login-result.png' });
    }
  });

  test('Form validation testing', async ({ page }) => {
    console.log('🔍 Testing form validation...');
    
    // Try to find any forms on the page
    const forms = await page.locator('form').count();
    console.log(`📝 Found ${forms} forms on the page`);
    
    if (forms > 0) {
      // Test empty form submission
      const submitButtons = await page.locator('button[type="submit"], input[type="submit"]').count();
      
      if (submitButtons > 0) {
        console.log('🧪 Testing empty form submission...');
        await page.locator('button[type="submit"], input[type="submit"]').first().click();
        await page.waitForTimeout(2000);
        
        // Look for validation messages
        const validationSelectors = [
          '.error',
          '.invalid',
          '.validation-error',
          '[class*="error"]',
          '[role="alert"]',
          '.field-error'
        ];
        
        let validationFound = false;
        for (const selector of validationSelectors) {
          const count = await page.locator(selector).count();
          if (count > 0) {
            console.log(`✅ Found ${count} validation messages with selector: ${selector}`);
            validationFound = true;
          }
        }
        
        if (!validationFound) {
          console.log('⚠️ No validation messages found - form may not have client-side validation');
        }
        
        await page.screenshot({ path: 'test-results/form-validation.png' });
      }
    }
  });

  test('Navigation and routing testing', async ({ page }) => {
    console.log('🧭 Testing navigation and routing...');
    
    // Get all links on the page
    const links = await page.locator('a[href]').all();
    console.log(`🔗 Found ${links.length} links to test`);
    
    const testedUrls = new Set();
    const results = [];
    
    // Test first 10 links to avoid overwhelming
    for (let i = 0; i < Math.min(10, links.length); i++) {
      const link = links[i];
      const href = await link.getAttribute('href');
      const text = await link.textContent();
      
      if (href && !testedUrls.has(href) && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
        testedUrls.add(href);
        
        try {
          console.log(`🔗 Testing link: "${text}" -> ${href}`);
          
          if (href.startsWith('http')) {
            // External link - just check if it's valid
            results.push({ href, text, status: 'external', accessible: true });
          } else {
            // Internal link - navigate and test
            await page.goto(href);
            await page.waitForLoadState('networkidle');
            
            const currentUrl = page.url();
            const title = await page.title();
            
            results.push({
              href,
              text,
              status: 'internal',
              accessible: true,
              finalUrl: currentUrl,
              title
            });
            
            console.log(`  ✅ Accessible - Title: "${title}"`);
          }
        } catch (error) {
          console.log(`  ❌ Error accessing ${href}: ${error}`);
          results.push({ href, text, status: 'error', accessible: false, error: error.toString() });
        }
      }
    }
    
    // Return to homepage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    console.log('📊 Navigation test results:', results);
    
    // At least some links should be accessible
    const accessibleLinks = results.filter(r => r.accessible);
    expect(accessibleLinks.length).toBeGreaterThan(0);
  });
});
