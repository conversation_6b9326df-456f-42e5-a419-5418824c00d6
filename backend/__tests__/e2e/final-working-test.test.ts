import { test, expect } from '@playwright/test';

test.describe('Final Working Tests', () => {
  test('Homepage loads and is interactive', async ({ page }) => {
    console.log('🚀 Starting homepage test...');
    
    // Navigate with more lenient wait condition
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    
    // Wait for React to mount
    await page.waitForSelector('body', { timeout: 30000 });
    
    // Wait a bit for dynamic content
    await page.waitForTimeout(3000);
    
    // Verify basic page structure
    const title = await page.title();
    console.log('📄 Page title:', title);
    expect(title).toContain('RentaHub');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/final-homepage.png', fullPage: true });
    
    // Check for content
    const bodyText = await page.locator('body').textContent();
    expect(bodyText).toContain('RentaHub');
    
    console.log('✅ Homepage test passed!');
  });

  test('Can interact with page elements', async ({ page }) => {
    console.log('🖱️ Testing page interactions...');
    
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(5000); // Wait for content to load
    
    // Count interactive elements
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    const inputs = await page.locator('input').count();
    
    console.log(`Found: ${buttons} buttons, ${links} links, ${inputs} inputs`);
    
    // Should have interactive elements
    expect(buttons + links).toBeGreaterThan(5);
    
    // Try to interact with first input if available
    if (inputs > 0) {
      const firstInput = page.locator('input').first();
      const inputType = await firstInput.getAttribute('type');
      
      if (inputType === 'text' || inputType === 'email' || !inputType) {
        await firstInput.fill('test');
        console.log('✅ Successfully interacted with input field');
        await firstInput.clear();
      }
    }
    
    console.log('✅ Interaction test passed!');
  });

  test('Page has proper structure', async ({ page }) => {
    console.log('🏗️ Testing page structure...');
    
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);
    
    // Check for React root
    const reactRoot = await page.locator('#root, [data-reactroot]').count();
    expect(reactRoot).toBeGreaterThan(0);
    
    // Check for basic HTML structure
    const hasHeader = await page.locator('header, nav, .header, .navbar').count();
    const hasMain = await page.locator('main, .main, .content').count();
    const hasFooter = await page.locator('footer, .footer').count();
    
    console.log(`Structure: header=${hasHeader}, main=${hasMain}, footer=${hasFooter}`);
    
    // Should have some structural elements
    expect(hasHeader + hasMain + hasFooter).toBeGreaterThan(0);
    
    console.log('✅ Structure test passed!');
  });

  test('Mobile responsiveness works', async ({ page }) => {
    console.log('📱 Testing mobile responsiveness...');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);
    
    // Take mobile screenshot
    await page.screenshot({ path: 'test-results/mobile-final.png', fullPage: true });
    
    // Check that content is still visible
    const visibleElements = await page.locator('div:visible').count();
    console.log(`Mobile view: ${visibleElements} visible elements`);
    
    expect(visibleElements).toBeGreaterThan(20);
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.reload({ waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);
    
    // Take desktop screenshot
    await page.screenshot({ path: 'test-results/desktop-final.png', fullPage: true });
    
    const desktopElements = await page.locator('div:visible').count();
    console.log(`Desktop view: ${desktopElements} visible elements`);
    
    expect(desktopElements).toBeGreaterThan(30);
    
    console.log('✅ Responsiveness test passed!');
  });

  test('Authentication elements are present', async ({ page }) => {
    console.log('🔐 Looking for authentication elements...');
    
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(5000);
    
    // Look for authentication-related text/buttons
    const pageContent = await page.locator('body').textContent();
    
    const hasSignIn = pageContent?.toLowerCase().includes('sign in') || 
                     pageContent?.toLowerCase().includes('login');
    const hasSignUp = pageContent?.toLowerCase().includes('sign up') || 
                     pageContent?.toLowerCase().includes('register');
    
    console.log(`Authentication elements: SignIn=${hasSignIn}, SignUp=${hasSignUp}`);
    
    // Look for clickable auth elements
    const authButtons = await page.locator('button:has-text("Sign"), a:has-text("Sign"), button:has-text("Login"), a:has-text("Login")').count();
    console.log(`Found ${authButtons} authentication buttons/links`);
    
    // Should have some authentication elements
    expect(hasSignIn || hasSignUp || authButtons > 0).toBeTruthy();
    
    console.log('✅ Authentication elements test passed!');
  });

  test('Page loads without critical errors', async ({ page }) => {
    console.log('🐛 Testing for critical errors...');
    
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Capture console messages
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      } else if (msg.type() === 'warning') {
        warnings.push(msg.text());
      }
    });
    
    // Capture page errors
    page.on('pageerror', error => {
      errors.push(`Page Error: ${error.message}`);
    });
    
    await page.goto('/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(5000);
    
    console.log(`Found ${errors.length} errors and ${warnings.length} warnings`);
    
    if (errors.length > 0) {
      console.log('Errors:', errors.slice(0, 3)); // Show first 3 errors
    }
    
    // Should have minimal critical errors
    expect(errors.length).toBeLessThan(10);
    
    console.log('✅ Error checking test passed!');
  });
});
