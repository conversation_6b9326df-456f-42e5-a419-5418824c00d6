import { test, expect } from '@playwright/test';

test.describe('Frontend Exploration', () => {
  test('Explore homepage and available routes', async ({ page }) => {
    console.log('🔍 Exploring frontend at http://localhost:5173');
    
    // Navigate to homepage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Get page title and URL
    const title = await page.title();
    const url = page.url();
    console.log('📄 Page title:', title);
    console.log('🔗 Current URL:', url);
    
    // Take screenshot of homepage
    await page.screenshot({ path: 'test-results/homepage-exploration.png', fullPage: true });
    
    // Look for common authentication elements
    const authElements = {
      loginButton: await page.locator('button:has-text("Login"), a:has-text("Login"), [data-testid*="login"]').count(),
      signupButton: await page.locator('button:has-text("Sign"), a:has-text("Sign"), [data-testid*="register"]').count(),
      authLinks: await page.locator('a[href*="auth"], a[href*="login"], a[href*="register"]').count(),
    };
    
    console.log('🔐 Authentication elements found:', authElements);
    
    // Look for navigation elements
    const navElements = {
      navBar: await page.locator('nav, .navbar, [role="navigation"]').count(),
      menuItems: await page.locator('nav a, .nav-link, .menu-item').count(),
      buttons: await page.locator('button').count(),
      links: await page.locator('a').count(),
    };
    
    console.log('🧭 Navigation elements found:', navElements);
    
    // Try to find any forms
    const forms = await page.locator('form').count();
    const inputs = await page.locator('input').count();
    console.log('📝 Forms found:', forms);
    console.log('📝 Input fields found:', inputs);
    
    // Get all visible text content to understand the page
    const bodyText = await page.locator('body').textContent();
    console.log('📄 Page contains text about:', bodyText?.substring(0, 200) + '...');
    
    // Check if there are any obvious auth routes
    const commonAuthRoutes = ['/login', '/register', '/auth/login', '/auth/register', '/signin', '/signup'];
    
    for (const route of commonAuthRoutes) {
      try {
        const response = await page.goto(route);
        if (response && response.status() < 400) {
          console.log(`✅ Found auth route: ${route} (Status: ${response.status()})`);
          await page.screenshot({ path: `test-results/auth-route-${route.replace(/\//g, '-')}.png` });
          
          // Look for form elements on auth pages
          const formElements = {
            emailInput: await page.locator('input[type="email"], input[name*="email"], [data-testid*="email"]').count(),
            passwordInput: await page.locator('input[type="password"], input[name*="password"], [data-testid*="password"]').count(),
            submitButton: await page.locator('button[type="submit"], input[type="submit"], button:has-text("Login"), button:has-text("Register")').count(),
          };
          console.log(`📝 Form elements on ${route}:`, formElements);
        }
      } catch (error) {
        console.log(`❌ Route ${route} not accessible`);
      }
    }
    
    // Go back to homepage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Check for React/Vue components and data attributes', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for React/Vue specific attributes
    const reactElements = await page.locator('[data-reactroot], [data-react-helmet], .react-component').count();
    const vueElements = await page.locator('[data-v-], .vue-component').count();
    const testIds = await page.locator('[data-testid]').count();
    const dataAttributes = await page.locator('[data-cy], [data-test], [data-qa]').count();
    
    console.log('⚛️ React elements found:', reactElements);
    console.log('🟢 Vue elements found:', vueElements);
    console.log('🧪 Test ID attributes found:', testIds);
    console.log('🔍 Other test attributes found:', dataAttributes);
    
    // Get all elements with data-testid attributes
    if (testIds > 0) {
      const testIdElements = await page.locator('[data-testid]').all();
      console.log('📋 Available data-testid attributes:');
      for (const element of testIdElements.slice(0, 10)) { // Limit to first 10
        const testId = await element.getAttribute('data-testid');
        const tagName = await element.evaluate(el => el.tagName.toLowerCase());
        console.log(`  - ${tagName}[data-testid="${testId}"]`);
      }
    }
  });

  test('Test basic page functionality', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Test if page is interactive
    const clickableElements = await page.locator('button, a, [onclick], [role="button"]').count();
    console.log('🖱️ Clickable elements found:', clickableElements);
    
    // Try clicking the first few buttons/links to see what happens
    const buttons = await page.locator('button').all();
    console.log(`🔘 Testing first ${Math.min(3, buttons.length)} buttons:`);
    
    for (let i = 0; i < Math.min(3, buttons.length); i++) {
      try {
        const button = buttons[i];
        const buttonText = await button.textContent();
        console.log(`  Clicking button: "${buttonText}"`);
        
        await button.click();
        await page.waitForTimeout(1000); // Wait for any navigation/changes
        
        const newUrl = page.url();
        console.log(`  Result: URL is now ${newUrl}`);
        
        // Go back to homepage for next test
        await page.goto('/');
        await page.waitForLoadState('networkidle');
      } catch (error) {
        console.log(`  Button ${i} click failed:`, error);
      }
    }
  });
});
