import { test, expect } from '@playwright/test';
import { 
  AuthPage, 
  VehiclePage, 
  BookingPage, 
  PaymentPage, 
  DashboardPage,
  TEST_USERS,
  waitForApiResponse,
  expectToastMessage,
  takeScreenshot
} from './helpers';

test.describe('Complete Booking Flow', () => {
  let authPage: AuthPage;
  let vehiclePage: VehiclePage;
  let bookingPage: BookingPage;
  let paymentPage: PaymentPage;
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page);
    vehiclePage = new VehiclePage(page);
    bookingPage = new BookingPage(page);
    paymentPage = new PaymentPage(page);
    dashboardPage = new DashboardPage(page);
  });

  test('Customer can complete full booking flow', async ({ page }) => {
    // Step 1: Login as customer
    await authPage.goto();
    await authPage.login(TEST_USERS.CUSTOMER.email, TEST_USERS.CUSTOMER.password);
    
    // Verify login success
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('[data-testid="user-name"]')).toContainText('therentahub');

    // Step 2: Search for vehicles
    await vehiclePage.searchVehicles({
      category: 'small_scooter',
      city: 'Jakarta'
    });

    // Wait for search results to load
    await page.waitForTimeout(3000);
    
    // Verify vehicles are displayed
    const vehicleCount = await vehiclePage.getVehicleCount();
    expect(vehicleCount).toBeGreaterThan(0);

    // Take screenshot of search results
    await takeScreenshot(page, 'vehicle-search-results');

    // Step 3: Select a vehicle
    await vehiclePage.selectVehicle(0);
    
    // Verify vehicle details page
    await expect(page.locator('[data-testid="vehicle-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="vehicle-price"]')).toBeVisible();
    await expect(page.locator('[data-testid="book-now-button"]')).toBeVisible();

    // Step 4: Create booking
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfter = new Date();
    dayAfter.setDate(dayAfter.getDate() + 3);

    await bookingPage.createBooking({
      startDate: tomorrow.toISOString().split('T')[0],
      endDate: dayAfter.toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '18:00',
      pickupLocation: 'Jakarta Central Station',
      specialRequests: 'Please have the vehicle ready by 9 AM'
    });

    // Wait for booking creation
    await waitForApiResponse(page, '/api/bookings');

    // Verify booking details page
    await expect(page.locator('[data-testid="booking-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-amount"]')).toBeVisible();

    // Take screenshot of booking summary
    await takeScreenshot(page, 'booking-summary');

    // Step 5: Proceed to payment (if Stripe is configured)
    if (await page.locator('[data-testid="payment-section"]').isVisible()) {
      await paymentPage.handleTestPayment();
      
      // Wait for payment processing
      await waitForApiResponse(page, '/api/payments');
      
      // Verify payment success
      await expect(page.locator('[data-testid="payment-success"]')).toBeVisible();
      await expectToastMessage(page, 'Payment successful');
    } else {
      // If payment is not configured, just confirm booking
      await bookingPage.confirmBooking();
      await expect(page.locator('[data-testid="booking-success"]')).toBeVisible();
    }

    // Take screenshot of success page
    await takeScreenshot(page, 'booking-success');

    // Step 6: Verify booking appears in dashboard
    await dashboardPage.goto();
    await dashboardPage.navigateToBookings();

    // Verify booking is listed
    await expect(page.locator('[data-testid="booking-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="booking-item"]').first()).toBeVisible();

    // Verify booking details
    await expect(page.locator('[data-testid="booking-item"]').first())
      .toContainText('Jakarta Central Station');
  });

  test('Customer can cancel booking', async ({ page }) => {
    // First create a booking (simplified version)
    await authPage.goto();
    await authPage.login(TEST_USERS.CUSTOMER.email, TEST_USERS.CUSTOMER.password);
    
    // Go to bookings page
    await dashboardPage.goto();
    await dashboardPage.navigateToBookings();

    // Check if there are any bookings to cancel
    const bookingExists = await page.locator('[data-testid="booking-item"]').first().isVisible();
    
    if (bookingExists) {
      // Click on first booking
      await page.locator('[data-testid="booking-item"]').first().click();
      
      // Cancel the booking
      await bookingPage.cancelBooking('Change of plans');
      
      // Verify cancellation success
      await expect(page.locator('[data-testid="cancellation-success"]')).toBeVisible();
      await expectToastMessage(page, 'Booking cancelled successfully');
    } else {
      // Skip test if no bookings available
      test.skip('No bookings available to cancel');
    }
  });

  test('Provider receives booking notification', async ({ page }) => {
    // Login as provider
    await authPage.goto();
    await authPage.login(TEST_USERS.PROVIDER.email, TEST_USERS.PROVIDER.password);
    
    // Go to provider dashboard
    await dashboardPage.goto();
    
    // Check for new booking notifications
    await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible();
    
    // Navigate to bookings
    await dashboardPage.navigateToBookings();
    
    // Verify provider can see incoming bookings
    await expect(page.locator('[data-testid="booking-list"]')).toBeVisible();
    
    // If there are bookings, verify provider can manage them
    const bookingExists = await page.locator('[data-testid="booking-item"]').first().isVisible();
    
    if (bookingExists) {
      await page.locator('[data-testid="booking-item"]').first().click();
      
      // Verify provider can see booking details
      await expect(page.locator('[data-testid="booking-details"]')).toBeVisible();
      await expect(page.locator('[data-testid="customer-info"]')).toBeVisible();
      
      // Verify provider can update booking status
      await expect(page.locator('[data-testid="status-update-button"]')).toBeVisible();
    }
  });

  test('Booking flow handles errors gracefully', async ({ page }) => {
    // Login as customer
    await authPage.goto();
    await authPage.login(TEST_USERS.CUSTOMER.email, TEST_USERS.CUSTOMER.password);
    
    // Try to book with invalid dates
    await vehiclePage.searchVehicles();
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      // Try booking with past dates
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      await bookingPage.createBooking({
        startDate: yesterday.toISOString().split('T')[0],
        endDate: yesterday.toISOString().split('T')[0],
        pickupLocation: 'Error Test Location'
      });
      
      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]'))
        .toContainText(/cannot.*past|invalid.*date/i);
    }
  });

  test('Mobile booking flow works correctly', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Login as customer
    await authPage.goto();
    await authPage.login(TEST_USERS.CUSTOMER.email, TEST_USERS.CUSTOMER.password);
    
    // Verify mobile navigation works
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // Test mobile vehicle search
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    // Verify mobile layout
    await expect(page.locator('[data-testid="vehicle-list"]')).toBeVisible();
    
    // Take screenshot of mobile view
    await takeScreenshot(page, 'mobile-vehicle-search');
    
    // Test mobile booking flow
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      // Verify mobile booking form
      await expect(page.locator('[data-testid="mobile-booking-form"]')).toBeVisible();
    }
  });
});
