import { test, expect } from '@playwright/test';

test.describe('Working Authentication Flow', () => {
  test('Can navigate to homepage and see content', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify page loaded
    await expect(page).toHaveTitle(/RentaHub/);
    
    // Look for actual content we saw in the debug
    const bodyText = await page.locator('body').textContent();
    expect(bodyText).toContain('RentaHub');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/homepage-working.png', fullPage: true });
    
    console.log('✅ Homepage loaded successfully');
  });

  test('Can find and click Sign In button', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for Sign In button/link (we saw "Sign In" in the body text)
    const signInButton = page.locator('button:has-text("Sign In"), a:has-text("Sign In"), [href*="signin"], [href*="login"]').first();
    
    if (await signInButton.count() > 0) {
      console.log('✅ Found Sign In button');
      await signInButton.click();
      
      // Wait for navigation
      await page.waitForLoadState('networkidle');
      
      // Take screenshot of sign in page
      await page.screenshot({ path: 'test-results/signin-page.png', fullPage: true });
      
      // Check if we're on a sign in page
      const currentUrl = page.url();
      const pageContent = await page.locator('body').textContent();
      
      console.log('Current URL after clicking Sign In:', currentUrl);
      console.log('Page contains sign in content:', pageContent?.includes('sign') || pageContent?.includes('login'));
      
    } else {
      console.log('⚠️ Sign In button not found, checking available buttons...');
      
      // List all available buttons
      const buttons = await page.locator('button').all();
      console.log(`Found ${buttons.length} buttons:`);
      
      for (let i = 0; i < Math.min(5, buttons.length); i++) {
        const buttonText = await buttons[i].textContent();
        console.log(`  Button ${i + 1}: "${buttonText}"`);
      }
      
      // List all available links
      const links = await page.locator('a').all();
      console.log(`Found ${links.length} links:`);
      
      for (let i = 0; i < Math.min(5, links.length); i++) {
        const linkText = await links[i].textContent();
        const href = await links[i].getAttribute('href');
        console.log(`  Link ${i + 1}: "${linkText}" -> ${href}`);
      }
    }
  });

  test('Can find and click Sign Up button', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for Sign Up button/link
    const signUpButton = page.locator('button:has-text("Sign Up"), a:has-text("Sign Up"), [href*="signup"], [href*="register"]').first();
    
    if (await signUpButton.count() > 0) {
      console.log('✅ Found Sign Up button');
      await signUpButton.click();
      
      // Wait for navigation
      await page.waitForLoadState('networkidle');
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/signup-page.png', fullPage: true });
      
      const currentUrl = page.url();
      console.log('Current URL after clicking Sign Up:', currentUrl);
      
    } else {
      console.log('⚠️ Sign Up button not found');
    }
  });

  test('Can explore vehicle listings', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for vehicle-related content
    const vehicleButtons = page.locator('button:has-text("Find"), button:has-text("Vehicle"), a:has-text("Find"), a:has-text("Vehicle")');
    
    if (await vehicleButtons.count() > 0) {
      console.log('✅ Found vehicle-related buttons');
      
      const firstButton = vehicleButtons.first();
      const buttonText = await firstButton.textContent();
      console.log(`Clicking: "${buttonText}"`);
      
      await firstButton.click();
      await page.waitForLoadState('networkidle');
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/vehicle-page.png', fullPage: true });
      
      const currentUrl = page.url();
      console.log('Current URL after clicking vehicle button:', currentUrl);
    }
  });

  test('Can test form inputs', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // We saw 4 input fields in the debug, let's find them
    const inputs = await page.locator('input').all();
    console.log(`Found ${inputs.length} input fields`);
    
    for (let i = 0; i < Math.min(3, inputs.length); i++) {
      const input = inputs[i];
      const type = await input.getAttribute('type');
      const placeholder = await input.getAttribute('placeholder');
      const name = await input.getAttribute('name');
      
      console.log(`Input ${i + 1}: type="${type}", placeholder="${placeholder}", name="${name}"`);
      
      // Try to interact with text inputs
      if (type === 'text' || type === 'email' || !type) {
        try {
          await input.fill('test input');
          console.log(`  ✅ Successfully filled input ${i + 1}`);
          await input.clear();
        } catch (error) {
          console.log(`  ⚠️ Could not fill input ${i + 1}: ${error}`);
        }
      }
    }
  });

  test('Can test responsive design', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/desktop-responsive.png', fullPage: true });
    
    const desktopElements = await page.locator('div:visible').count();
    console.log(`Desktop view: ${desktopElements} visible elements`);
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/mobile-responsive.png', fullPage: true });
    
    const mobileElements = await page.locator('div:visible').count();
    console.log(`Mobile view: ${mobileElements} visible elements`);
    
    // Both views should have content
    expect(desktopElements).toBeGreaterThan(50);
    expect(mobileElements).toBeGreaterThan(30);
  });

  test('Can test page performance', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`Page load time: ${loadTime}ms`);
    
    // Page should load within reasonable time
    expect(loadTime).toBeLessThan(10000); // 10 seconds max
    
    // Check for any JavaScript errors
    const errors: string[] = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Wait a bit to catch any delayed errors
    await page.waitForTimeout(2000);
    
    console.log(`JavaScript errors found: ${errors.length}`);
    if (errors.length > 0) {
      console.log('Errors:', errors);
    }
    
    // Should have minimal errors
    expect(errors.length).toBeLessThan(5);
  });
});
