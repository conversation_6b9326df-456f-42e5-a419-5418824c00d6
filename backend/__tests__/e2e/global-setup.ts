import { chromium, FullConfig } from '@playwright/test';
import { cleanupTestData, initializeTestUsers } from '../testHelper';

async function globalSetup(config: FullConfig) {
  console.log('🎭 Setting up E2E test environment...');

  try {
    // Clean up any existing test data
    await cleanupTestData();
    console.log('✅ Test data cleaned up');

    // Initialize test users for E2E tests
    await initializeTestUsers();
    console.log('✅ Test users initialized');

    // Create a browser instance to verify frontend is accessible
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    try {
      // Wait for frontend to be available
      await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
      console.log('✅ Frontend is accessible');
    } catch (error) {
      console.warn('⚠️ Frontend not accessible, tests may fail:', error);
    }

    // Verify backend API is accessible
    try {
      const response = await page.request.get('http://localhost:3001/health');
      if (response.ok()) {
        console.log('✅ Backend API is accessible');
      } else {
        console.warn('⚠️ Backend API not responding correctly');
      }
    } catch (error) {
      console.warn('⚠️ Backend API not accessible:', error);
    }

    await browser.close();

    console.log('🎉 E2E test environment setup complete');
  } catch (error) {
    console.error('❌ E2E global setup failed:', error);
    throw error;
  }
}

export default globalSetup;
