import { test, expect } from '@playwright/test';

test.describe('Frontend E2E Tests', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page has loaded by looking for common elements
    const title = await page.title();
    console.log('Page title:', title);
    
    // Basic assertion - page should have loaded
    expect(title).toBeTruthy();
  });

  test('should be able to navigate to different pages', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Try to find navigation elements
    const navElements = await page.locator('nav, [role="navigation"], .nav, .navbar').count();
    console.log('Navigation elements found:', navElements);
    
    // Check if there are any links on the page
    const links = await page.locator('a').count();
    console.log('Links found:', links);
    
    expect(links).toBeGreaterThan(0);
  });

  test('should have responsive design', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for desktop
    await page.screenshot({ path: 'test-results/desktop-view.png' });
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for mobile
    await page.screenshot({ path: 'test-results/mobile-view.png' });
    
    console.log('✅ Responsive design test completed');
  });

  test('should handle 404 pages gracefully', async ({ page }) => {
    // Navigate to a non-existent page
    const response = await page.goto('/non-existent-page');
    
    // Check if we get a proper response (could be 404 or redirect)
    console.log('Response status:', response?.status());
    
    // The page should still load something (404 page or redirect)
    await page.waitForLoadState('networkidle');
    
    const title = await page.title();
    console.log('404 page title:', title);
    
    expect(title).toBeTruthy();
  });

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Listen for page errors
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit more to catch any delayed errors
    await page.waitForTimeout(2000);
    
    console.log('JavaScript errors found:', errors.length);
    if (errors.length > 0) {
      console.log('Errors:', errors);
    }
    
    // For now, just log errors but don't fail the test
    // In a real scenario, you might want to fail if there are critical errors
    expect(errors.length).toBeLessThan(10); // Allow some minor errors
  });
});
