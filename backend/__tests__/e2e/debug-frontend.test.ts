import { test, expect } from '@playwright/test';

test.describe('Frontend Debug', () => {
  test('Debug frontend loading issues', async ({ page }) => {
    console.log('🔍 Starting frontend debug...');
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`🖥️ Console ${msg.type()}: ${msg.text()}`);
    });
    
    // Enable error logging
    page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    // Enable request/response logging
    page.on('request', request => {
      console.log(`📤 Request: ${request.method()} ${request.url()}`);
    });
    
    page.on('response', response => {
      console.log(`📥 Response: ${response.status()} ${response.url()}`);
    });
    
    try {
      console.log('🌐 Navigating to http://localhost:5173...');
      
      // Try to navigate with a longer timeout and different wait conditions
      await page.goto('http://localhost:5173', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
      
      console.log('✅ Page loaded successfully');
      
      // Wait a bit for any async content
      await page.waitForTimeout(2000);
      
      // Get basic page info
      const title = await page.title();
      const url = page.url();
      console.log(`📄 Title: "${title}"`);
      console.log(`🔗 URL: ${url}`);
      
      // Take a screenshot to see what's actually rendered
      await page.screenshot({ 
        path: 'test-results/debug-frontend-screenshot.png', 
        fullPage: true 
      });
      
      // Get the HTML content
      const htmlContent = await page.content();
      console.log(`📝 HTML length: ${htmlContent.length} characters`);
      console.log(`📝 HTML preview: ${htmlContent.substring(0, 500)}...`);
      
      // Check for common elements
      const bodyText = await page.locator('body').textContent();
      console.log(`📄 Body text length: ${bodyText?.length || 0} characters`);
      console.log(`📄 Body text preview: ${bodyText?.substring(0, 200) || 'No text found'}...`);
      
      // Look for any visible elements
      const visibleElements = {
        divs: await page.locator('div:visible').count(),
        buttons: await page.locator('button:visible').count(),
        links: await page.locator('a:visible').count(),
        inputs: await page.locator('input:visible').count(),
        forms: await page.locator('form:visible').count(),
      };
      
      console.log('👁️ Visible elements:', visibleElements);
      
      // Check for React/Vue app mounting
      const reactRoot = await page.locator('#root, #app, [data-reactroot]').count();
      console.log(`⚛️ React/Vue root elements: ${reactRoot}`);
      
      // Check for any error messages on the page
      const errorElements = await page.locator('.error, .alert-danger, [class*="error"]').count();
      console.log(`❌ Error elements on page: ${errorElements}`);
      
      // Try to find any loading indicators
      const loadingElements = await page.locator('.loading, .spinner, [class*="loading"]').count();
      console.log(`⏳ Loading elements: ${loadingElements}`);
      
    } catch (error) {
      console.log(`❌ Navigation failed: ${error}`);
      
      // Still try to take a screenshot to see what happened
      try {
        await page.screenshot({ 
          path: 'test-results/debug-frontend-error-screenshot.png' 
        });
      } catch (screenshotError) {
        console.log(`❌ Screenshot also failed: ${screenshotError}`);
      }
      
      throw error;
    }
  });

  test('Test direct HTML content', async ({ page }) => {
    console.log('🔍 Testing direct HTML response...');
    
    try {
      // Make a direct request to see what the server returns
      const response = await page.request.get('http://localhost:5173');
      const status = response.status();
      const headers = response.headers();
      const body = await response.text();
      
      console.log(`📊 Response status: ${status}`);
      console.log(`📋 Response headers:`, headers);
      console.log(`📄 Response body length: ${body.length} characters`);
      console.log(`📄 Response body preview: ${body.substring(0, 500)}...`);
      
      // Check if it's a proper HTML document
      const isHTML = body.includes('<html') || body.includes('<!DOCTYPE');
      console.log(`📄 Is HTML document: ${isHTML}`);
      
      // Look for common patterns
      const patterns = {
        hasReact: body.includes('react') || body.includes('React'),
        hasVue: body.includes('vue') || body.includes('Vue'),
        hasVite: body.includes('vite') || body.includes('Vite'),
        hasScript: body.includes('<script'),
        hasCSS: body.includes('<link') && body.includes('stylesheet'),
        hasDiv: body.includes('<div'),
      };
      
      console.log('🔍 Content patterns:', patterns);
      
    } catch (error) {
      console.log(`❌ Direct request failed: ${error}`);
    }
  });

  test('Test different ports and URLs', async ({ page }) => {
    console.log('🔍 Testing different ports...');
    
    const urlsToTest = [
      'http://localhost:5173',
      'http://localhost:3000',
      'http://localhost:8080',
      'http://127.0.0.1:5173',
    ];
    
    for (const url of urlsToTest) {
      try {
        console.log(`🌐 Testing ${url}...`);
        const response = await page.request.get(url);
        console.log(`✅ ${url} responded with status: ${response.status()}`);
      } catch (error) {
        console.log(`❌ ${url} failed: ${error}`);
      }
    }
  });
});
