import { test, expect } from '@playwright/test';

test.describe('Diagnostic Testing', () => {
  
  test('Deep diagnostic of what is actually rendering', async ({ page }) => {
    console.log('🔍 Running deep diagnostic...');
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`🖥️ Console ${msg.type()}: ${msg.text()}`);
    });
    
    // Enable error logging
    page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });
    
    // Test each route with detailed analysis
    const routes = ['/', '/signup', '/signin', '/search'];
    
    for (const route of routes) {
      console.log(`\n🔍 === ANALYZING ${route} ===`);
      
      await page.goto(route);
      await page.waitForLoadState('networkidle');
      
      // Get the actual HTML content
      const htmlContent = await page.content();
      console.log(`📄 HTML length: ${htmlContent.length} characters`);
      
      // Get the body content
      const bodyContent = await page.locator('body').innerHTML();
      console.log(`📄 Body HTML preview: ${bodyContent.substring(0, 500)}...`);
      
      // Get visible text
      const bodyText = await page.locator('body').textContent();
      console.log(`📄 Visible text: "${bodyText?.substring(0, 200) || 'No text'}..."`);
      
      // Check for React/Vue app mounting
      const reactRoot = await page.locator('#root, #app, [data-reactroot]').innerHTML();
      console.log(`⚛️ React root content: ${reactRoot.substring(0, 300)}...`);
      
      // Check for loading states
      const loadingElements = await page.locator('.loading, .spinner, [class*="loading"]').count();
      console.log(`⏳ Loading elements: ${loadingElements}`);
      
      // Check for error messages
      const errorElements = await page.locator('.error, [class*="error"]').count();
      console.log(`❌ Error elements: ${errorElements}`);
      
      // Check current URL (might be redirected)
      const currentUrl = page.url();
      console.log(`🔗 Current URL: ${currentUrl}`);
      
      // Check if there are any network requests happening
      await page.waitForTimeout(2000);
      
      // Take screenshot
      await page.screenshot({ 
        path: `test-results/diagnostic-${route.replace('/', 'home')}.png`, 
        fullPage: true 
      });
      
      console.log(`📸 Screenshot saved for ${route}`);
    }
  });

  test('Check for authentication redirects', async ({ page }) => {
    console.log('🔐 Checking for authentication redirects...');
    
    const protectedRoutes = ['/dashboard', '/book', '/payment'];
    
    for (const route of protectedRoutes) {
      console.log(`🔒 Testing ${route} for auth redirect...`);
      
      const response = await page.goto(route);
      await page.waitForLoadState('networkidle');
      
      const finalUrl = page.url();
      const statusCode = response?.status();
      
      console.log(`  📍 ${route} -> ${finalUrl} (Status: ${statusCode})`);
      
      // Check if redirected to login
      if (finalUrl.includes('login') || finalUrl.includes('signin') || finalUrl.includes('auth')) {
        console.log(`  🔒 ${route} requires authentication`);
      } else if (finalUrl === `http://localhost:5173${route}`) {
        console.log(`  ✅ ${route} accessible without auth`);
      } else {
        console.log(`  🔄 ${route} redirected to ${finalUrl}`);
      }
    }
  });

  test('Check JavaScript errors and network issues', async ({ page }) => {
    console.log('🐛 Checking for JavaScript errors and network issues...');
    
    const errors: string[] = [];
    const networkErrors: string[] = [];
    const requests: string[] = [];
    
    // Capture JavaScript errors
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Capture network requests
    page.on('request', request => {
      requests.push(`${request.method()} ${request.url()}`);
    });
    
    // Capture failed requests
    page.on('requestfailed', request => {
      networkErrors.push(`Failed: ${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
    });
    
    await page.goto('/signup');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    console.log(`🐛 JavaScript errors found: ${errors.length}`);
    errors.forEach((error, i) => console.log(`  ${i + 1}. ${error}`));
    
    console.log(`🌐 Network errors found: ${networkErrors.length}`);
    networkErrors.forEach((error, i) => console.log(`  ${i + 1}. ${error}`));
    
    console.log(`📡 Network requests made: ${requests.length}`);
    requests.slice(0, 10).forEach((req, i) => console.log(`  ${i + 1}. ${req}`));
    
    // Check if API endpoints are being called
    const apiRequests = requests.filter(req => req.includes('/api/'));
    console.log(`🔌 API requests: ${apiRequests.length}`);
    apiRequests.forEach(req => console.log(`  API: ${req}`));
  });

  test('Test with different wait strategies', async ({ page }) => {
    console.log('⏱️ Testing different wait strategies...');
    
    await page.goto('/signup');
    
    // Strategy 1: Wait for load states
    console.log('📡 Waiting for networkidle...');
    await page.waitForLoadState('networkidle');
    let elements = await page.locator('*:visible').count();
    console.log(`  Elements after networkidle: ${elements}`);
    
    // Strategy 2: Wait for specific selectors
    console.log('🎯 Waiting for common selectors...');
    const selectors = ['form', 'input', 'button', '.signup', '.login', '.auth'];
    for (const selector of selectors) {
      try {
        await page.waitForSelector(selector, { timeout: 2000 });
        const count = await page.locator(selector).count();
        console.log(`  ✅ Found ${count} ${selector} elements`);
      } catch {
        console.log(`  ❌ No ${selector} elements found`);
      }
    }
    
    // Strategy 3: Wait for text content
    console.log('📝 Waiting for text content...');
    const textSelectors = [
      'text=Sign Up',
      'text=Register', 
      'text=Email',
      'text=Password',
      'text=Login',
      'text=Sign In'
    ];
    
    for (const textSelector of textSelectors) {
      try {
        await page.waitForSelector(textSelector, { timeout: 2000 });
        console.log(`  ✅ Found text: ${textSelector}`);
      } catch {
        console.log(`  ❌ No text found: ${textSelector}`);
      }
    }
    
    // Strategy 4: Wait for URL changes
    console.log('🔗 Checking URL stability...');
    const initialUrl = page.url();
    await page.waitForTimeout(3000);
    const finalUrl = page.url();
    
    if (initialUrl !== finalUrl) {
      console.log(`  🔄 URL changed: ${initialUrl} -> ${finalUrl}`);
    } else {
      console.log(`  ✅ URL stable: ${finalUrl}`);
    }
    
    // Final element count
    elements = await page.locator('*:visible').count();
    console.log(`📊 Final visible elements: ${elements}`);
  });
});
