import { test, expect } from '@playwright/test';
import { 
  AuthPage, 
  VehiclePage, 
  BookingPage, 
  PaymentPage,
  TEST_USERS,
  waitForApiResponse,
  expectToastMessage,
  expectErrorMessage,
  takeScreenshot,
  mockApiResponse
} from './helpers';

test.describe('Payment Flow', () => {
  let authPage: AuthPage;
  let vehiclePage: VehiclePage;
  let bookingPage: BookingPage;
  let paymentPage: PaymentPage;

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page);
    vehiclePage = new VehiclePage(page);
    bookingPage = new BookingPage(page);
    paymentPage = new PaymentPage(page);

    // Login as customer for payment tests
    await authPage.goto();
    await authPage.login(TEST_USERS.CUSTOMER.email, TEST_USERS.CUSTOMER.password);
  });

  test('Successful payment flow with test card', async ({ page }) => {
    // Create a booking first
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Payment Test Location'
      });

      // Check if payment section is available
      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        // Test successful payment
        await paymentPage.handleTestPayment();
        
        // Verify payment success
        await expect(page.locator('[data-testid="payment-success"]')).toBeVisible();
        await expectToastMessage(page, 'Payment successful');
        
        // Take screenshot of payment success
        await takeScreenshot(page, 'payment-success');
        
        // Verify booking status updated
        await expect(page.locator('[data-testid="booking-status"]')).toContainText('Confirmed');
      } else {
        console.log('Payment section not available - Stripe may not be configured');
        test.skip('Payment testing requires Stripe configuration');
      }
    } else {
      test.skip('No vehicles available for payment testing');
    }
  });

  test('Payment fails with invalid card', async ({ page }) => {
    // Create a booking first
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Payment Failure Test'
      });

      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        // Use invalid test card
        await paymentPage.fillPaymentDetails({
          cardNumber: '****************', // Stripe test card that always fails
          expiryDate: '12/25',
          cvc: '123',
          cardholderName: 'Test User'
        });
        
        await paymentPage.submitPayment();
        
        // Should show payment error
        await expectErrorMessage(page, 'payment failed');
        
        // Booking should remain in pending status
        await expect(page.locator('[data-testid="booking-status"]')).toContainText('Pending');
        
        // Take screenshot of payment failure
        await takeScreenshot(page, 'payment-failure');
      }
    }
  });

  test('Payment form validation works', async ({ page }) => {
    // Create a booking first
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Validation Test'
      });

      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        // Try to submit without filling payment details
        await page.click('[data-testid="submit-payment-button"]');
        
        // Should show validation errors
        await expect(page.locator('[data-testid="card-error"]')).toBeVisible();
        
        // Fill invalid card details
        await paymentPage.fillPaymentDetails({
          cardNumber: '1234', // Invalid card number
          expiryDate: '13/20', // Invalid expiry
          cvc: '12', // Invalid CVC
          cardholderName: ''  // Empty name
        });
        
        await page.click('[data-testid="submit-payment-button"]');
        
        // Should show specific validation errors
        await expect(page.locator('[data-testid="card-number-error"]')).toBeVisible();
        await expect(page.locator('[data-testid="expiry-error"]')).toBeVisible();
        await expect(page.locator('[data-testid="cvc-error"]')).toBeVisible();
        await expect(page.locator('[data-testid="name-error"]')).toBeVisible();
      }
    }
  });

  test('Payment timeout handling', async ({ page }) => {
    // Mock slow payment response
    await mockApiResponse(page, '**/api/payments/create-intent', {
      success: false,
      error: 'Payment processing timeout'
    });

    // Create a booking
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Timeout Test'
      });

      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        await paymentPage.handleTestPayment();
        
        // Should show timeout error
        await expectErrorMessage(page, 'timeout');
        
        // Should provide retry option
        await expect(page.locator('[data-testid="retry-payment-button"]')).toBeVisible();
      }
    }
  });

  test('Multiple payment attempts handling', async ({ page }) => {
    // Create a booking
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Multiple Attempts Test'
      });

      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        // First attempt with failing card
        await paymentPage.fillPaymentDetails({
          cardNumber: '****************', // Always fails
          expiryDate: '12/25',
          cvc: '123',
          cardholderName: 'Test User'
        });
        
        await paymentPage.submitPayment();
        await expectErrorMessage(page, 'payment failed');
        
        // Second attempt with valid card
        await paymentPage.fillPaymentDetails({
          cardNumber: '****************', // Always succeeds
          expiryDate: '12/25',
          cvc: '123',
          cardholderName: 'Test User'
        });
        
        await paymentPage.submitPayment();
        
        // Should succeed on second attempt
        await expect(page.locator('[data-testid="payment-success"]')).toBeVisible();
      }
    }
  });

  test('Payment amount verification', async ({ page }) => {
    // Create a booking
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      // Get vehicle price
      const priceText = await page.locator('[data-testid="vehicle-price"]').textContent();
      const vehiclePrice = parseFloat(priceText?.replace(/[^\d]/g, '') || '0');
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 3); // 2 days

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Amount Verification Test'
      });

      // Verify booking summary shows correct amounts
      await expect(page.locator('[data-testid="base-amount"]')).toBeVisible();
      await expect(page.locator('[data-testid="tax-amount"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-amount"]')).toBeVisible();
      
      // Verify total amount calculation
      const baseAmountText = await page.locator('[data-testid="base-amount"]').textContent();
      const taxAmountText = await page.locator('[data-testid="tax-amount"]').textContent();
      const totalAmountText = await page.locator('[data-testid="total-amount"]').textContent();
      
      const baseAmount = parseFloat(baseAmountText?.replace(/[^\d]/g, '') || '0');
      const taxAmount = parseFloat(taxAmountText?.replace(/[^\d]/g, '') || '0');
      const totalAmount = parseFloat(totalAmountText?.replace(/[^\d]/g, '') || '0');
      
      // Verify calculation is correct
      expect(totalAmount).toBe(baseAmount + taxAmount);
      
      // Verify base amount is reasonable (2 days * daily rate)
      expect(baseAmount).toBeGreaterThan(vehiclePrice);
      expect(baseAmount).toBeLessThanOrEqual(vehiclePrice * 3); // Allow for some variation
    }
  });

  test('Payment security measures', async ({ page }) => {
    // Test that sensitive payment data is not exposed in DOM
    await vehiclePage.searchVehicles({ category: 'small_scooter' });
    
    if (await vehiclePage.getVehicleCount() > 0) {
      await vehiclePage.selectVehicle(0);
      
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfter = new Date();
      dayAfter.setDate(dayAfter.getDate() + 2);

      await bookingPage.createBooking({
        startDate: tomorrow.toISOString().split('T')[0],
        endDate: dayAfter.toISOString().split('T')[0],
        pickupLocation: 'Security Test'
      });

      if (await page.locator('[data-testid="payment-section"]').isVisible()) {
        // Fill payment details
        await paymentPage.fillPaymentDetails({
          cardNumber: '****************',
          expiryDate: '12/25',
          cvc: '123',
          cardholderName: 'Test User'
        });
        
        // Check that card details are not visible in page source
        const pageContent = await page.content();
        expect(pageContent).not.toContain('****************');
        expect(pageContent).not.toContain('123'); // CVC should not be in DOM
        
        // Verify Stripe elements are properly isolated
        await expect(page.locator('[data-testid="stripe-card-element"]')).toBeVisible();
        
        // Verify HTTPS is used for payment processing
        const currentUrl = page.url();
        if (currentUrl.startsWith('http://localhost')) {
          console.log('Local development - HTTPS check skipped');
        } else {
          expect(currentUrl).toMatch(/^https:/);
        }
      }
    }
  });
});
