import { test, expect } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';

test.describe('Database Connection Test', () => {
  test('should be able to connect to Supabase', async () => {
    const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
    const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.log('❌ Database connection error:', error);
      console.log('Error code:', error.code);
      console.log('Error message:', error.message);
      
      // Check if it's a table not found error
      if (error.code === '42P01') {
        console.log('⚠️ Users table does not exist - this is expected for a fresh setup');
        // This is not a failure - just means tables need to be created
        expect(error.code).toBe('42P01');
      } else if (error.code === '42501') {
        console.log('⚠️ Permission denied - RLS might be enabled');
        // This indicates RLS is blocking access
        expect(error.code).toBe('42501');
      } else {
        // Unexpected error
        throw new Error(`Unexpected database error: ${error.message}`);
      }
    } else {
      console.log('✅ Database connection successful');
      console.log('Data:', data);
      expect(data).toBeDefined();
    }
  });

  test('should be able to check database schema', async () => {
    const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
    const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Check what tables exist
    const { data: tables, error } = await supabase
      .rpc('get_schema_tables')
      .select();

    if (error) {
      console.log('❌ Schema check error:', error);
      // This is expected if the RPC function doesn't exist
      expect(error).toBeDefined();
    } else {
      console.log('✅ Schema accessible');
      console.log('Available tables:', tables);
    }
  });
});
