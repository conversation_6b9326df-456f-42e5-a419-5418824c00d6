import { test, expect } from '@playwright/test';

test.describe('Simple Playwright Test', () => {
  test('should be able to navigate to Google', async ({ page }) => {
    await page.goto('https://www.google.com');
    await expect(page).toHaveTitle(/Google/);
    console.log('✅ Playwright is working correctly!');
  });

  test('should be able to navigate to local frontend', async ({ page }) => {
    try {
      // Navigate to local development server
      await page.goto('http://localhost:5173/');
      console.log('✅ Local frontend is accessible');
      // Just check if page loads without specific assertions
      await page.waitForLoadState('networkidle');

      // Basic assertion to ensure page loaded
      await expect(page).toHaveURL(/localhost:5173/);
    } catch (error) {
      console.log('⚠️ Local frontend not accessible:', error);
      // This is expected if frontend is not running
    }
  });
});
