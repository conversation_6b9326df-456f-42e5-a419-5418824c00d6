import { cleanupTestData } from '../testHelper';

async function globalTeardown() {
  console.log('🧹 Cleaning up E2E test environment...');

  try {
    // Clean up all test data
    await cleanupTestData();
    console.log('✅ E2E test data cleaned up');

    console.log('🎉 E2E test environment teardown complete');
  } catch (error) {
    console.error('❌ E2E global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown;
