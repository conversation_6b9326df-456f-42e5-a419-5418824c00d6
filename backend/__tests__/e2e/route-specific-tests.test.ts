import { test, expect } from '@playwright/test';

test.describe('Route-Specific Intensive Testing', () => {
  
  test('Signup page comprehensive testing', async ({ page }) => {
    console.log('🔐 Testing signup page in detail...');
    
    await page.goto('/signup');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/signup-page-detailed.png', fullPage: true });
    
    // Count all elements
    const elements = {
      inputs: await page.locator('input').count(),
      buttons: await page.locator('button').count(),
      forms: await page.locator('form').count(),
      links: await page.locator('a').count(),
      headings: await page.locator('h1, h2, h3, h4, h5, h6').count()
    };
    
    console.log('📊 Signup page elements:', elements);
    
    // Test form interactions
    const inputs = await page.locator('input').all();
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const type = await input.getAttribute('type') || 'text';
      const placeholder = await input.getAttribute('placeholder') || '';
      
      console.log(`📝 Input ${i + 1}: type="${type}", placeholder="${placeholder}"`);
      
      try {
        if (type === 'email') {
          await input.fill('<EMAIL>');
        } else if (type === 'password') {
          await input.fill('TestPassword123!');
        } else if (type === 'text') {
          await input.fill('Test User');
        }
        console.log(`  ✅ Successfully filled input ${i + 1}`);
      } catch (error) {
        console.log(`  ⚠️ Could not fill input ${i + 1}: ${error}`);
      }
    }
    
    expect(elements.inputs + elements.buttons + elements.forms).toBeGreaterThan(0);
  });

  test('Signin page comprehensive testing', async ({ page }) => {
    console.log('🔐 Testing signin page in detail...');
    
    await page.goto('/signin');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/signin-page-detailed.png', fullPage: true });
    
    const elements = {
      inputs: await page.locator('input').count(),
      buttons: await page.locator('button').count(),
      forms: await page.locator('form').count(),
      links: await page.locator('a').count(),
      headings: await page.locator('h1, h2, h3, h4, h5, h6').count()
    };
    
    console.log('📊 Signin page elements:', elements);
    
    // Test login form
    const emailInputs = await page.locator('input[type="email"], input[name*="email"]').count();
    const passwordInputs = await page.locator('input[type="password"]').count();
    
    console.log(`📝 Found ${emailInputs} email inputs and ${passwordInputs} password inputs`);
    
    if (emailInputs > 0) {
      await page.locator('input[type="email"], input[name*="email"]').first().fill('<EMAIL>');
      console.log('✅ Filled email field');
    }
    
    if (passwordInputs > 0) {
      await page.locator('input[type="password"]').first().fill('testpassword');
      console.log('✅ Filled password field');
    }
    
    expect(elements.inputs + elements.buttons + elements.forms).toBeGreaterThan(0);
  });

  test('Search page comprehensive testing', async ({ page }) => {
    console.log('🚗 Testing search page in detail...');
    
    await page.goto('/search');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/search-page-detailed.png', fullPage: true });
    
    const elements = {
      inputs: await page.locator('input').count(),
      buttons: await page.locator('button').count(),
      selects: await page.locator('select').count(),
      vehicles: await page.locator('.vehicle, .car, .listing, [data-testid*="vehicle"]').count(),
      filters: await page.locator('.filter, [data-testid*="filter"]').count()
    };
    
    console.log('📊 Search page elements:', elements);
    
    // Test search functionality
    const searchInputs = await page.locator('input[type="search"], input[placeholder*="search"]').count();
    if (searchInputs > 0) {
      await page.locator('input[type="search"], input[placeholder*="search"]').first().fill('bike');
      console.log('✅ Filled search input');
      
      // Try to trigger search
      await page.keyboard.press('Enter');
      await page.waitForTimeout(2000);
    }
    
    expect(elements.inputs + elements.buttons + elements.selects).toBeGreaterThan(0);
  });

  test('Booking page comprehensive testing', async ({ page }) => {
    console.log('📅 Testing booking page in detail...');
    
    await page.goto('/book');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/booking-page-detailed.png', fullPage: true });
    
    const elements = {
      inputs: await page.locator('input').count(),
      buttons: await page.locator('button').count(),
      selects: await page.locator('select').count(),
      dateInputs: await page.locator('input[type="date"], input[type="datetime-local"]').count(),
      timeInputs: await page.locator('input[type="time"]').count()
    };
    
    console.log('📊 Booking page elements:', elements);
    
    // Test date inputs
    if (elements.dateInputs > 0) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dateString = tomorrow.toISOString().split('T')[0];
      
      await page.locator('input[type="date"]').first().fill(dateString);
      console.log(`✅ Filled date input with: ${dateString}`);
    }
    
    // Test time inputs
    if (elements.timeInputs > 0) {
      await page.locator('input[type="time"]').first().fill('10:00');
      console.log('✅ Filled time input');
    }
    
    expect(elements.inputs + elements.buttons + elements.selects).toBeGreaterThan(0);
  });

  test('Payment page comprehensive testing', async ({ page }) => {
    console.log('💳 Testing payment page in detail...');
    
    await page.goto('/payment');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/payment-page-detailed.png', fullPage: true });
    
    const elements = {
      inputs: await page.locator('input').count(),
      buttons: await page.locator('button').count(),
      forms: await page.locator('form').count(),
      cardInputs: await page.locator('input[name*="card"], input[placeholder*="card"]').count(),
      expiryInputs: await page.locator('input[name*="expiry"], input[placeholder*="MM"]').count(),
      cvvInputs: await page.locator('input[name*="cvv"], input[name*="cvc"]').count()
    };
    
    console.log('📊 Payment page elements:', elements);
    
    // Test payment form
    if (elements.cardInputs > 0) {
      await page.locator('input[name*="card"], input[placeholder*="card"]').first().fill('****************');
      console.log('✅ Filled card number');
    }
    
    if (elements.expiryInputs > 0) {
      await page.locator('input[name*="expiry"], input[placeholder*="MM"]').first().fill('12/25');
      console.log('✅ Filled expiry date');
    }
    
    if (elements.cvvInputs > 0) {
      await page.locator('input[name*="cvv"], input[name*="cvc"]').first().fill('123');
      console.log('✅ Filled CVV');
    }
    
    expect(elements.inputs + elements.buttons + elements.forms).toBeGreaterThan(0);
  });

  test('Dashboard page comprehensive testing', async ({ page }) => {
    console.log('👤 Testing dashboard page in detail...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.screenshot({ path: 'test-results/dashboard-page-detailed.png', fullPage: true });
    
    const elements = {
      buttons: await page.locator('button').count(),
      links: await page.locator('a').count(),
      cards: await page.locator('.card, .dashboard-card, [data-testid*="card"]').count(),
      stats: await page.locator('.stat, .metric, [data-testid*="stat"]').count(),
      navigation: await page.locator('nav, .nav, .navigation').count()
    };
    
    console.log('📊 Dashboard page elements:', elements);
    
    // Check for user-specific content
    const userElements = await page.locator('.user, .profile, [data-testid*="user"]').count();
    const bookingElements = await page.locator('.booking, [data-testid*="booking"]').count();
    
    console.log(`👤 User elements: ${userElements}, Booking elements: ${bookingElements}`);
    
    expect(elements.buttons + elements.links + elements.cards).toBeGreaterThan(0);
  });

  test('Cross-page navigation testing', async ({ page }) => {
    console.log('🧭 Testing navigation between all pages...');
    
    const pages = [
      { url: '/', name: 'Homepage' },
      { url: '/signup', name: 'Signup' },
      { url: '/signin', name: 'Signin' },
      { url: '/search', name: 'Search' },
      { url: '/book', name: 'Booking' },
      { url: '/payment', name: 'Payment' },
      { url: '/dashboard', name: 'Dashboard' }
    ];
    
    const results = [];
    
    for (const pageInfo of pages) {
      console.log(`🔗 Testing ${pageInfo.name} at ${pageInfo.url}`);
      
      try {
        const startTime = Date.now();
        await page.goto(pageInfo.url);
        await page.waitForLoadState('domcontentloaded');
        const loadTime = Date.now() - startTime;
        
        const title = await page.title();
        const elements = await page.locator('*:visible').count();
        
        results.push({
          name: pageInfo.name,
          url: pageInfo.url,
          accessible: true,
          loadTime,
          title,
          elements
        });
        
        console.log(`  ✅ ${pageInfo.name}: ${loadTime}ms, ${elements} elements, title: "${title}"`);
        
      } catch (error) {
        results.push({
          name: pageInfo.name,
          url: pageInfo.url,
          accessible: false,
          error: error.toString()
        });
        
        console.log(`  ❌ ${pageInfo.name}: ${error}`);
      }
    }
    
    console.log('📊 Navigation test results:', results);
    
    // All pages should be accessible
    const accessiblePages = results.filter(r => r.accessible);
    expect(accessiblePages.length).toBe(pages.length);
  });

  test('Performance testing across all routes', async ({ page }) => {
    console.log('⚡ Testing performance across all routes...');
    
    const routes = ['/signup', '/signin', '/search', '/book', '/payment', '/dashboard'];
    const performanceResults = [];
    
    for (const route of routes) {
      console.log(`⚡ Testing performance of ${route}`);
      
      const startTime = Date.now();
      await page.goto(route);
      const domTime = Date.now() - startTime;
      
      await page.waitForLoadState('networkidle');
      const fullTime = Date.now() - startTime;
      
      performanceResults.push({
        route,
        domTime,
        fullTime
      });
      
      console.log(`  ⏱️ ${route}: DOM ${domTime}ms, Full ${fullTime}ms`);
    }
    
    const avgDomTime = performanceResults.reduce((sum, r) => sum + r.domTime, 0) / performanceResults.length;
    const avgFullTime = performanceResults.reduce((sum, r) => sum + r.fullTime, 0) / performanceResults.length;
    
    console.log(`📊 Average performance: DOM ${avgDomTime.toFixed(0)}ms, Full ${avgFullTime.toFixed(0)}ms`);
    
    // Performance should be reasonable
    expect(avgDomTime).toBeLessThan(5000); // 5 seconds max
    expect(avgFullTime).toBeLessThan(15000); // 15 seconds max
  });
});
