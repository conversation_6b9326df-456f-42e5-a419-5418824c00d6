import { test, expect } from '@playwright/test';

test.describe('Intensive Business Logic Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Vehicle search and filtering functionality', async ({ page }) => {
    console.log('🚗 Testing vehicle search functionality...');
    
    // Look for search-related elements
    const searchSelectors = [
      'input[type="search"]',
      'input[placeholder*="search"]',
      'input[placeholder*="find"]',
      'input[name*="search"]',
      '[data-testid*="search"]',
      '.search-input',
      '#search'
    ];
    
    let searchFound = false;
    for (const selector of searchSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found search input: ${selector}`);
        
        // Test search functionality
        await page.locator(selector).first().fill('bike');
        await page.keyboard.press('Enter');
        await page.waitForTimeout(2000);
        
        // Check for results
        const resultsSelectors = [
          '.search-results',
          '.vehicle-list',
          '.results',
          '[data-testid*="results"]',
          '.vehicle-card',
          '.listing'
        ];
        
        for (const resultSelector of resultsSelectors) {
          const resultCount = await page.locator(resultSelector).count();
          if (resultCount > 0) {
            console.log(`✅ Found ${resultCount} search results with selector: ${resultSelector}`);
          }
        }
        
        searchFound = true;
        break;
      }
    }
    
    if (!searchFound) {
      console.log('⚠️ No search functionality found on homepage');
      
      // Try navigating to potential search/browse pages
      const searchUrls = ['/search', '/vehicles', '/browse', '/find', '/listings'];
      for (const url of searchUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error')) {
            console.log(`✅ Found search page at: ${url}`);
            
            // Look for vehicle listings
            const vehicleSelectors = [
              '.vehicle',
              '.car',
              '.bike',
              '.listing',
              '.item',
              '[data-testid*="vehicle"]'
            ];
            
            for (const selector of vehicleSelectors) {
              const count = await page.locator(selector).count();
              if (count > 0) {
                console.log(`✅ Found ${count} vehicle listings with selector: ${selector}`);
              }
            }
            
            searchFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/vehicle-search.png', fullPage: true });
  });

  test('Booking flow simulation', async ({ page }) => {
    console.log('📅 Testing booking flow...');
    
    // Look for booking-related elements
    const bookingSelectors = [
      'button:has-text("Book")',
      'button:has-text("Rent")',
      'button:has-text("Reserve")',
      'a:has-text("Book")',
      'a:has-text("Rent")',
      '[data-testid*="book"]',
      '.book-btn',
      '.rent-btn'
    ];
    
    let bookingFound = false;
    for (const selector of bookingSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found booking element: ${selector}`);
        
        try {
          await page.locator(selector).first().click();
          await page.waitForLoadState('networkidle');
          
          // Look for booking form elements
          const bookingFormSelectors = [
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[name*="date"]',
            'input[placeholder*="date"]',
            '.date-picker',
            '[data-testid*="date"]'
          ];
          
          let dateInputFound = false;
          for (const dateSelector of bookingFormSelectors) {
            if (await page.locator(dateSelector).count() > 0) {
              console.log(`✅ Found date input: ${dateSelector}`);
              
              // Try to fill date
              const tomorrow = new Date();
              tomorrow.setDate(tomorrow.getDate() + 1);
              const dateString = tomorrow.toISOString().split('T')[0];
              
              await page.locator(dateSelector).first().fill(dateString);
              console.log(`✅ Filled date: ${dateString}`);
              dateInputFound = true;
              break;
            }
          }
          
          // Look for time selection
          const timeSelectors = [
            'input[type="time"]',
            'select[name*="time"]',
            'select[name*="hour"]',
            '.time-picker'
          ];
          
          for (const timeSelector of timeSelectors) {
            if (await page.locator(timeSelector).count() > 0) {
              console.log(`✅ Found time input: ${timeSelector}`);
              
              if (timeSelector.includes('select')) {
                const options = await page.locator(`${timeSelector} option`).count();
                if (options > 1) {
                  await page.locator(timeSelector).selectOption({ index: 1 });
                }
              } else {
                await page.locator(timeSelector).fill('10:00');
              }
              break;
            }
          }
          
          bookingFound = true;
          break;
        } catch (error) {
          console.log(`⚠️ Error clicking booking element: ${error}`);
        }
      }
    }
    
    if (!bookingFound) {
      // Try direct navigation to booking pages
      const bookingUrls = ['/book', '/booking', '/rent', '/reserve'];
      for (const url of bookingUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error')) {
            console.log(`✅ Found booking page at: ${url}`);
            bookingFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/booking-flow.png', fullPage: true });
  });

  test('Payment flow simulation', async ({ page }) => {
    console.log('💳 Testing payment flow...');
    
    // Look for payment-related elements
    const paymentSelectors = [
      'button:has-text("Pay")',
      'button:has-text("Checkout")',
      'button:has-text("Purchase")',
      'a:has-text("Pay")',
      '[data-testid*="pay"]',
      '.pay-btn',
      '.checkout-btn'
    ];
    
    let paymentFound = false;
    for (const selector of paymentSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found payment element: ${selector}`);
        
        try {
          await page.locator(selector).first().click();
          await page.waitForLoadState('networkidle');
          
          // Look for payment form elements
          const paymentFormSelectors = [
            'input[name*="card"]',
            'input[placeholder*="card"]',
            'input[type="tel"][maxlength="19"]', // Credit card format
            '[data-testid*="card"]',
            '.card-input'
          ];
          
          for (const cardSelector of paymentFormSelectors) {
            if (await page.locator(cardSelector).count() > 0) {
              console.log(`✅ Found card input: ${cardSelector}`);
              
              // Use test card number
              await page.locator(cardSelector).first().fill('****************');
              console.log('✅ Filled test card number');
              break;
            }
          }
          
          // Look for expiry date
          const expirySelectors = [
            'input[name*="expiry"]',
            'input[placeholder*="MM/YY"]',
            'input[placeholder*="expiry"]',
            '[data-testid*="expiry"]'
          ];
          
          for (const expirySelector of expirySelectors) {
            if (await page.locator(expirySelector).count() > 0) {
              await page.locator(expirySelector).first().fill('12/25');
              console.log('✅ Filled expiry date');
              break;
            }
          }
          
          // Look for CVV
          const cvvSelectors = [
            'input[name*="cvv"]',
            'input[name*="cvc"]',
            'input[placeholder*="CVV"]',
            'input[placeholder*="CVC"]',
            '[data-testid*="cvv"]'
          ];
          
          for (const cvvSelector of cvvSelectors) {
            if (await page.locator(cvvSelector).count() > 0) {
              await page.locator(cvvSelector).first().fill('123');
              console.log('✅ Filled CVV');
              break;
            }
          }
          
          paymentFound = true;
          break;
        } catch (error) {
          console.log(`⚠️ Error in payment flow: ${error}`);
        }
      }
    }
    
    if (!paymentFound) {
      // Try direct navigation to payment pages
      const paymentUrls = ['/payment', '/checkout', '/pay'];
      for (const url of paymentUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error')) {
            console.log(`✅ Found payment page at: ${url}`);
            paymentFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/payment-flow.png', fullPage: true });
  });

  test('User dashboard and profile testing', async ({ page }) => {
    console.log('👤 Testing user dashboard functionality...');
    
    // Look for dashboard/profile links
    const dashboardSelectors = [
      'a:has-text("Dashboard")',
      'a:has-text("Profile")',
      'a:has-text("Account")',
      'a:has-text("My Account")',
      '[href*="dashboard"]',
      '[href*="profile"]',
      '[data-testid*="dashboard"]',
      '.dashboard-link'
    ];
    
    let dashboardFound = false;
    for (const selector of dashboardSelectors) {
      if (await page.locator(selector).count() > 0) {
        console.log(`✅ Found dashboard element: ${selector}`);
        
        try {
          await page.locator(selector).first().click();
          await page.waitForLoadState('networkidle');
          
          // Look for dashboard elements
          const dashboardElements = [
            '.dashboard',
            '.profile',
            '.user-info',
            '[data-testid*="dashboard"]',
            '.stats',
            '.summary'
          ];
          
          for (const element of dashboardElements) {
            const count = await page.locator(element).count();
            if (count > 0) {
              console.log(`✅ Found ${count} dashboard elements: ${element}`);
            }
          }
          
          dashboardFound = true;
          break;
        } catch (error) {
          console.log(`⚠️ Error accessing dashboard: ${error}`);
        }
      }
    }
    
    if (!dashboardFound) {
      // Try direct navigation
      const dashboardUrls = ['/dashboard', '/profile', '/account', '/user'];
      for (const url of dashboardUrls) {
        try {
          await page.goto(url);
          const currentUrl = page.url();
          if (!currentUrl.includes('404') && !currentUrl.includes('error') && !currentUrl.includes('login')) {
            console.log(`✅ Found dashboard page at: ${url}`);
            dashboardFound = true;
            break;
          }
        } catch (error) {
          console.log(`❌ ${url} not accessible or requires authentication`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/dashboard-testing.png', fullPage: true });
  });

  test('Data validation and error handling', async ({ page }) => {
    console.log('🔍 Testing data validation and error handling...');
    
    // Find forms and test with invalid data
    const forms = await page.locator('form').all();
    console.log(`📝 Found ${forms.length} forms to test`);
    
    for (let i = 0; i < Math.min(3, forms.length); i++) {
      const form = forms[i];
      console.log(`📝 Testing form ${i + 1}...`);
      
      // Find inputs in this form
      const inputs = await form.locator('input').all();
      
      for (const input of inputs) {
        const type = await input.getAttribute('type') || 'text';
        
        try {
          // Test with invalid data based on input type
          switch (type) {
            case 'email':
              await input.fill('invalid-email');
              console.log('  🧪 Testing invalid email format');
              break;
            case 'tel':
              await input.fill('not-a-phone-number');
              console.log('  🧪 Testing invalid phone format');
              break;
            case 'number':
              await input.fill('not-a-number');
              console.log('  🧪 Testing invalid number format');
              break;
            case 'url':
              await input.fill('not-a-url');
              console.log('  🧪 Testing invalid URL format');
              break;
            case 'date':
              await input.fill('invalid-date');
              console.log('  🧪 Testing invalid date format');
              break;
            default:
              // Test with very long string
              await input.fill('x'.repeat(1000));
              console.log('  🧪 Testing with very long input');
          }
        } catch (error) {
          console.log(`  ⚠️ Input validation test failed: ${error}`);
        }
      }
      
      // Try to submit form with invalid data
      const submitButton = await form.locator('button[type="submit"], input[type="submit"]').first();
      if (await submitButton.count() > 0) {
        try {
          await submitButton.click();
          await page.waitForTimeout(2000);
          
          // Look for error messages
          const errorSelectors = [
            '.error',
            '.invalid',
            '.validation-error',
            '[role="alert"]',
            '.alert-danger',
            '.field-error'
          ];
          
          let errorsFound = 0;
          for (const errorSelector of errorSelectors) {
            const errorCount = await page.locator(errorSelector).count();
            errorsFound += errorCount;
          }
          
          console.log(`  📊 Found ${errorsFound} validation error messages`);
          
        } catch (error) {
          console.log(`  ⚠️ Form submission test failed: ${error}`);
        }
      }
    }
    
    await page.screenshot({ path: 'test-results/validation-testing.png', fullPage: true });
  });
});
