import { test, expect } from '@playwright/test';

test.describe('Intensive UI and Interaction Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Comprehensive form interaction testing', async ({ page }) => {
    console.log('📝 Testing all form interactions...');
    
    // Find all input elements
    const inputs = await page.locator('input').all();
    console.log(`📝 Found ${inputs.length} input fields`);
    
    const inputResults = [];
    
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const type = await input.getAttribute('type') || 'text';
      const name = await input.getAttribute('name') || `input-${i}`;
      const placeholder = await input.getAttribute('placeholder') || '';
      const required = await input.getAttribute('required') !== null;
      
      console.log(`📝 Testing input ${i + 1}: type="${type}", name="${name}", placeholder="${placeholder}", required=${required}`);
      
      try {
        // Test different input types
        switch (type) {
          case 'text':
          case 'email':
            await input.fill('<EMAIL>');
            await input.clear();
            await input.fill('Test Input Value');
            break;
          case 'password':
            await input.fill('TestPassword123!');
            break;
          case 'number':
            await input.fill('12345');
            break;
          case 'tel':
            await input.fill('+1234567890');
            break;
          case 'url':
            await input.fill('https://example.com');
            break;
          case 'search':
            await input.fill('search query');
            break;
          case 'date':
            await input.fill('2024-12-31');
            break;
          default:
            if (type !== 'hidden' && type !== 'submit' && type !== 'button') {
              await input.fill('test value');
            }
        }
        
        // Test focus and blur
        await input.focus();
        await input.blur();
        
        inputResults.push({
          index: i,
          type,
          name,
          placeholder,
          required,
          status: 'success',
          interactive: true
        });
        
        console.log(`  ✅ Input ${i + 1} is fully interactive`);
        
      } catch (error) {
        console.log(`  ⚠️ Input ${i + 1} interaction failed: ${error}`);
        inputResults.push({
          index: i,
          type,
          name,
          placeholder,
          required,
          status: 'error',
          interactive: false,
          error: error.toString()
        });
      }
    }
    
    // Test select elements
    const selects = await page.locator('select').all();
    console.log(`📝 Found ${selects.length} select elements`);
    
    for (let i = 0; i < selects.length; i++) {
      const select = selects[i];
      try {
        const options = await select.locator('option').all();
        if (options.length > 1) {
          // Select the second option (first is usually default)
          await select.selectOption({ index: 1 });
          console.log(`  ✅ Select ${i + 1} is interactive`);
        }
      } catch (error) {
        console.log(`  ⚠️ Select ${i + 1} interaction failed: ${error}`);
      }
    }
    
    // Test textareas
    const textareas = await page.locator('textarea').all();
    console.log(`📝 Found ${textareas.length} textarea elements`);
    
    for (let i = 0; i < textareas.length; i++) {
      const textarea = textareas[i];
      try {
        await textarea.fill('This is a test message for the textarea element. It contains multiple lines and various characters to test the input handling.');
        console.log(`  ✅ Textarea ${i + 1} is interactive`);
      } catch (error) {
        console.log(`  ⚠️ Textarea ${i + 1} interaction failed: ${error}`);
      }
    }
    
    await page.screenshot({ path: 'test-results/form-interactions-complete.png', fullPage: true });
    
    // Verify at least some inputs are interactive
    const interactiveInputs = inputResults.filter(r => r.interactive);
    expect(interactiveInputs.length).toBeGreaterThan(0);
  });

  test('Button interaction and functionality testing', async ({ page }) => {
    console.log('🔘 Testing all button interactions...');
    
    const buttons = await page.locator('button').all();
    console.log(`🔘 Found ${buttons.length} buttons`);
    
    const buttonResults = [];
    
    for (let i = 0; i < Math.min(15, buttons.length); i++) { // Test first 15 buttons
      const button = buttons[i];
      const text = await button.textContent() || '';
      const type = await button.getAttribute('type') || 'button';
      const disabled = await button.isDisabled();
      const visible = await button.isVisible();
      
      console.log(`🔘 Testing button ${i + 1}: "${text.trim()}", type="${type}", disabled=${disabled}, visible=${visible}`);
      
      if (visible && !disabled) {
        try {
          // Record initial state
          const initialUrl = page.url();
          
          // Click the button
          await button.click();
          
          // Wait for any changes
          await page.waitForTimeout(1000);
          
          // Check what happened
          const newUrl = page.url();
          const urlChanged = initialUrl !== newUrl;
          
          // Check for modals or overlays
          const modals = await page.locator('.modal, .overlay, .popup, [role="dialog"]').count();
          
          // Check for loading states
          const loading = await page.locator('.loading, .spinner, [aria-busy="true"]').count();
          
          buttonResults.push({
            index: i,
            text: text.trim(),
            type,
            status: 'clicked',
            urlChanged,
            newUrl: urlChanged ? newUrl : null,
            modalsOpened: modals,
            loadingStates: loading
          });
          
          console.log(`  ✅ Button clicked - URL changed: ${urlChanged}, Modals: ${modals}, Loading: ${loading}`);
          
          // If URL changed, go back to test other buttons
          if (urlChanged && newUrl !== initialUrl) {
            await page.goBack();
            await page.waitForLoadState('networkidle');
          }
          
        } catch (error) {
          console.log(`  ⚠️ Button ${i + 1} click failed: ${error}`);
          buttonResults.push({
            index: i,
            text: text.trim(),
            type,
            status: 'error',
            error: error.toString()
          });
        }
      } else {
        buttonResults.push({
          index: i,
          text: text.trim(),
          type,
          status: 'skipped',
          reason: disabled ? 'disabled' : 'not visible'
        });
      }
    }
    
    console.log('🔘 Button test results:', buttonResults);
    
    // Take screenshot after button testing
    await page.screenshot({ path: 'test-results/button-interactions-complete.png', fullPage: true });
    
    // Verify at least some buttons are functional
    const functionalButtons = buttonResults.filter(r => r.status === 'clicked');
    expect(functionalButtons.length).toBeGreaterThan(0);
  });

  test('Responsive design stress testing', async ({ page }) => {
    console.log('📱 Testing responsive design across multiple viewports...');
    
    const viewports = [
      { name: 'Mobile Portrait', width: 375, height: 667 },
      { name: 'Mobile Landscape', width: 667, height: 375 },
      { name: 'Tablet Portrait', width: 768, height: 1024 },
      { name: 'Tablet Landscape', width: 1024, height: 768 },
      { name: 'Desktop Small', width: 1280, height: 720 },
      { name: 'Desktop Large', width: 1920, height: 1080 },
      { name: 'Ultra Wide', width: 2560, height: 1440 }
    ];
    
    const responsiveResults = [];
    
    for (const viewport of viewports) {
      console.log(`📱 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Wait for any responsive adjustments
      await page.waitForTimeout(1000);
      
      // Count visible elements
      const visibleElements = await page.locator('*:visible').count();
      const buttons = await page.locator('button:visible').count();
      const links = await page.locator('a:visible').count();
      const inputs = await page.locator('input:visible').count();
      
      // Check for horizontal scrollbars (indicates responsive issues)
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      const viewportWidth = viewport.width;
      const hasHorizontalScroll = bodyWidth > viewportWidth;
      
      // Check for overlapping elements (basic check)
      const overlappingElements = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        let overlaps = 0;
        // Simple overlap detection - this is a basic implementation
        for (let i = 0; i < Math.min(50, elements.length); i++) {
          const rect = elements[i].getBoundingClientRect();
          if (rect.width > window.innerWidth || rect.height > window.innerHeight) {
            overlaps++;
          }
        }
        return overlaps;
      });
      
      responsiveResults.push({
        viewport: viewport.name,
        dimensions: `${viewport.width}x${viewport.height}`,
        visibleElements,
        buttons,
        links,
        inputs,
        hasHorizontalScroll,
        overlappingElements,
        responsive: !hasHorizontalScroll && overlappingElements < 5
      });
      
      // Take screenshot
      await page.screenshot({ 
        path: `test-results/responsive-${viewport.name.toLowerCase().replace(' ', '-')}.png`,
        fullPage: true 
      });
      
      console.log(`  📊 Elements: ${visibleElements}, Buttons: ${buttons}, Links: ${links}, Inputs: ${inputs}`);
      console.log(`  📏 Horizontal scroll: ${hasHorizontalScroll}, Overlapping: ${overlappingElements}`);
    }
    
    console.log('📱 Responsive test results:', responsiveResults);
    
    // Verify responsive design works on most viewports
    const responsiveViewports = responsiveResults.filter(r => r.responsive);
    expect(responsiveViewports.length).toBeGreaterThan(viewports.length * 0.7); // At least 70% should be responsive
  });

  test('Performance and loading testing', async ({ page }) => {
    console.log('⚡ Testing performance and loading...');
    
    const performanceResults = [];
    
    // Test multiple page loads
    for (let i = 0; i < 3; i++) {
      console.log(`⚡ Performance test ${i + 1}/3`);
      
      const startTime = Date.now();
      
      await page.goto('/', { waitUntil: 'domcontentloaded' });
      const domLoadTime = Date.now() - startTime;
      
      await page.waitForLoadState('networkidle');
      const fullLoadTime = Date.now() - startTime;
      
      // Count resources
      const images = await page.locator('img').count();
      const scripts = await page.locator('script').count();
      const stylesheets = await page.locator('link[rel="stylesheet"]').count();
      
      // Check for loading indicators
      const loadingIndicators = await page.locator('.loading, .spinner, [aria-busy="true"]').count();
      
      performanceResults.push({
        attempt: i + 1,
        domLoadTime,
        fullLoadTime,
        images,
        scripts,
        stylesheets,
        loadingIndicators
      });
      
      console.log(`  ⏱️ DOM: ${domLoadTime}ms, Full: ${fullLoadTime}ms, Images: ${images}, Scripts: ${scripts}`);
    }
    
    // Calculate averages
    const avgDomLoad = performanceResults.reduce((sum, r) => sum + r.domLoadTime, 0) / performanceResults.length;
    const avgFullLoad = performanceResults.reduce((sum, r) => sum + r.fullLoadTime, 0) / performanceResults.length;
    
    console.log(`📊 Average DOM load: ${avgDomLoad.toFixed(0)}ms, Average full load: ${avgFullLoad.toFixed(0)}ms`);
    
    // Performance should be reasonable
    expect(avgDomLoad).toBeLessThan(10000); // 10 seconds max for DOM
    expect(avgFullLoad).toBeLessThan(30000); // 30 seconds max for full load
  });

  test('Accessibility testing', async ({ page }) => {
    console.log('♿ Testing accessibility features...');
    
    // Check for basic accessibility attributes
    const accessibilityResults = {
      altTexts: await page.locator('img[alt]').count(),
      totalImages: await page.locator('img').count(),
      ariaLabels: await page.locator('[aria-label]').count(),
      ariaDescribedBy: await page.locator('[aria-describedby]').count(),
      headings: await page.locator('h1, h2, h3, h4, h5, h6').count(),
      landmarks: await page.locator('main, nav, header, footer, aside, section[aria-label]').count(),
      focusableElements: await page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').count()
    };
    
    console.log('♿ Accessibility audit results:', accessibilityResults);
    
    // Test keyboard navigation
    console.log('⌨️ Testing keyboard navigation...');
    
    const focusableElements = await page.locator('a, button, input, select, textarea').all();
    let keyboardNavigable = 0;
    
    for (let i = 0; i < Math.min(10, focusableElements.length); i++) {
      try {
        await focusableElements[i].focus();
        const isFocused = await focusableElements[i].evaluate(el => document.activeElement === el);
        if (isFocused) {
          keyboardNavigable++;
        }
      } catch (error) {
        // Element might not be focusable
      }
    }
    
    console.log(`⌨️ Keyboard navigable elements: ${keyboardNavigable}/${Math.min(10, focusableElements.length)}`);
    
    // Basic accessibility checks
    expect(accessibilityResults.headings).toBeGreaterThan(0); // Should have headings
    expect(accessibilityResults.focusableElements).toBeGreaterThan(0); // Should have focusable elements
    
    // If there are images, some should have alt text
    if (accessibilityResults.totalImages > 0) {
      expect(accessibilityResults.altTexts).toBeGreaterThan(0);
    }
  });
});
