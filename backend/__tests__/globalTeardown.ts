import { cleanupTestData } from './testHelper';

export default async function globalTeardown() {
  try {
    console.log('🧹 Cleaning up test environment...');

    // Clean up all test data
    await cleanupTestData();
    console.log('✅ Test data cleaned up');

    console.log('🎉 Test environment teardown complete');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}
