import 'dotenv/config';
import DatabaseService from '../src/services/DatabaseService';
import { initializeTestUsers, cleanupTestData } from './testHelper';

export default async function globalSetup() {
  try {
    console.log('🧪 Setting up test environment...');

    // Test database connection
    const isConnected = await DatabaseService.testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection verified');

    // Clean up any existing test data
    await cleanupTestData();
    console.log('✅ Test data cleaned up');

    // Initialize test users
    await initializeTestUsers();
    console.log('✅ Test users initialized');

    console.log('🎉 Test environment setup complete');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}
