import request from 'supertest';
import { 
  app, 
  TEST_CONFIG,
  customerRequest,
  providerRequest,
  createTestVehicle,
  createTestBooking,
  expectApiSuccess,
  expectApiError,
  delay
} from '../testHelper';

describe('Integration Error Scenarios', () => {
  let testVehicleId: string;

  beforeAll(async () => {
    // Create a test vehicle for error scenario tests
    const vehicleData = createTestVehicle();
    delete vehicleData.provider_id;

    const vehicleResponse = await providerRequest()
      .post('/api/vehicles')
      .send(vehicleData);

    if (vehicleResponse.status === 201) {
      testVehicleId = vehicleResponse.body.data.id;
    }
  });

  describe('Duplicate Booking Prevention', () => {
    it('should prevent exact duplicate bookings', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-08-01',
        end_date: '2024-08-03',
        pickup_location: 'Duplicate Prevention Test'
      };

      // First booking
      const response1 = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiSuccess(response1, 201);
      const firstBookingId = response1.body.data.id;

      // Immediate duplicate attempt
      const response2 = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Should either prevent duplicate or handle at business logic level
      if (response2.status === 201) {
        // If system allows duplicate creation, verify they can't both be confirmed
        console.warn('System allows duplicate booking creation - testing confirmation logic');
        
        // Try to confirm both bookings
        const confirm1 = await customerRequest()
          .put(`/api/bookings/${firstBookingId}/status`)
          .send({ status: 'confirmed' });

        const confirm2 = await customerRequest()
          .put(`/api/bookings/${response2.body.data.id}/status`)
          .send({ status: 'confirmed' });

        // At least one should fail
        const successCount = [confirm1, confirm2].filter(r => r.status === 200).length;
        expect(successCount).toBeLessThanOrEqual(1);
      } else {
        expectApiError(response2, 400);
      }
    });

    it('should detect overlapping booking attempts', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      const baseBooking = {
        vehicle_id: testVehicleId,
        start_date: '2024-08-10',
        end_date: '2024-08-12',
        pickup_location: 'Overlap Base Test'
      };

      // Create first booking
      const response1 = await customerRequest()
        .post('/api/bookings')
        .send(baseBooking);

      expectApiSuccess(response1, 201);

      // Test various overlap scenarios
      const overlapScenarios = [
        {
          name: 'Start during existing booking',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-08-11',
            end_date: '2024-08-13',
            pickup_location: 'Overlap Start Test'
          }
        },
        {
          name: 'End during existing booking',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-08-09',
            end_date: '2024-08-11',
            pickup_location: 'Overlap End Test'
          }
        },
        {
          name: 'Completely contains existing booking',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-08-09',
            end_date: '2024-08-13',
            pickup_location: 'Overlap Contains Test'
          }
        },
        {
          name: 'Completely contained by existing booking',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-08-10',
            end_date: '2024-08-11',
            pickup_location: 'Overlap Contained Test'
          }
        }
      ];

      for (const scenario of overlapScenarios) {
        const response = await customerRequest()
          .post('/api/bookings')
          .send(scenario.data);

        // Should either prevent overlap or handle at confirmation stage
        if (response.status === 201) {
          console.warn(`${scenario.name} was allowed - should be handled at confirmation`);
        } else {
          expectApiError(response, 400);
        }
      }
    });

    it('should handle concurrent booking attempts', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      const bookingData1 = {
        vehicle_id: testVehicleId,
        start_date: '2024-08-20',
        end_date: '2024-08-22',
        pickup_location: 'Concurrent Test 1'
      };

      const bookingData2 = {
        vehicle_id: testVehicleId,
        start_date: '2024-08-20',
        end_date: '2024-08-22',
        pickup_location: 'Concurrent Test 2'
      };

      // Simulate concurrent requests
      const promises = [
        customerRequest().post('/api/bookings').send(bookingData1),
        customerRequest().post('/api/bookings').send(bookingData2)
      ];

      const [response1, response2] = await Promise.all(promises);

      // Verify only one succeeds or both are handled properly
      const successCount = [response1, response2].filter(r => r.status === 201).length;
      
      if (successCount === 2) {
        // If both succeed, verify they can't both be confirmed
        const bookingIds = [response1.body.data.id, response2.body.data.id];
        
        const confirmPromises = bookingIds.map(id =>
          customerRequest()
            .put(`/api/bookings/${id}/status`)
            .send({ status: 'confirmed' })
        );

        const confirmResponses = await Promise.all(confirmPromises);
        const confirmSuccessCount = confirmResponses.filter(r => r.status === 200).length;
        
        expect(confirmSuccessCount).toBeLessThanOrEqual(1);
      } else {
        expect(successCount).toBeGreaterThanOrEqual(1);
      }
    });
  });

  describe('Failed Payment Flow Handling', () => {
    let testBookingId: string;

    beforeAll(async () => {
      if (!testVehicleId) return;

      // Create a booking for payment tests
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-09-01',
        end_date: '2024-09-03',
        pickup_location: 'Payment Error Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      if (response.status === 201) {
        testBookingId = response.body.data.id;
      }
    });

    it('should handle payment intent creation failures', async () => {
      if (!testBookingId) {
        test.skip('No test booking available');
        return;
      }

      const paymentData = {
        booking_id: testBookingId
      };

      const response = await customerRequest()
        .post('/api/payments/create-intent')
        .send(paymentData);

      // Since Stripe is disabled in tests, should fail gracefully
      if (response.status === 500) {
        expectApiError(response, 500);
        expect(response.body.error).toContain('Stripe');
      } else {
        expectApiSuccess(response, 200);
      }
    });

    it('should handle invalid payment confirmations', async () => {
      const confirmData = {
        payment_intent_id: 'pi_invalid_test_id'
      };

      const response = await customerRequest()
        .post('/api/payments/confirm')
        .send(confirmData);

      expectApiError(response, 500);
    });

    it('should handle malformed webhook data', async () => {
      const malformedWebhooks = [
        {
          name: 'Missing signature',
          headers: {},
          body: { id: 'evt_test', type: 'payment_intent.succeeded' }
        },
        {
          name: 'Invalid signature',
          headers: { 'stripe-signature': 'invalid_signature' },
          body: { id: 'evt_test', type: 'payment_intent.succeeded' }
        },
        {
          name: 'Malformed body',
          headers: { 'stripe-signature': 'test_signature' },
          body: { invalid: 'data' }
        }
      ];

      for (const webhook of malformedWebhooks) {
        const response = await request(app)
          .post('/api/payments/webhook')
          .set(webhook.headers)
          .send(webhook.body);

        expectApiError(response, 400);
      }
    });

    it('should handle payment processing timeouts', async () => {
      // This would require mocking timeout scenarios
      // For now, just verify the endpoint responds within reasonable time
      
      if (!testBookingId) {
        test.skip('No test booking available');
        return;
      }

      const startTime = Date.now();
      
      const response = await customerRequest()
        .post('/api/payments/create-intent')
        .send({ booking_id: testBookingId });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Should respond within 30 seconds
      expect(responseTime).toBeLessThan(30000);
    });
  });

  describe('Availability Conflict Handling', () => {
    it('should handle vehicle status changes during booking', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      // Start booking process
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-09-10',
        end_date: '2024-09-12',
        pickup_location: 'Status Change Test'
      };

      // Simulate vehicle becoming unavailable during booking
      const bookingPromise = customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Simultaneously try to disable the vehicle
      const disablePromise = providerRequest()
        .put(`/api/vehicles/${testVehicleId}`)
        .send({ is_active: false });

      const [bookingResponse, disableResponse] = await Promise.all([
        bookingPromise,
        disablePromise
      ]);

      // Either booking should fail or vehicle disable should fail
      if (bookingResponse.status === 201 && disableResponse.status === 200) {
        console.warn('Both booking and vehicle disable succeeded - potential race condition');
      }

      // Re-enable vehicle for other tests
      await providerRequest()
        .put(`/api/vehicles/${testVehicleId}`)
        .send({ is_active: true });
    });

    it('should validate date ranges properly', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      const invalidDateScenarios = [
        {
          name: 'Start date after end date',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-10-10',
            end_date: '2024-10-05',
            pickup_location: 'Invalid Date Range Test'
          },
          expectedError: /start.*before.*end|invalid.*date/i
        },
        {
          name: 'Past start date',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2020-01-01',
            end_date: '2020-01-02',
            pickup_location: 'Past Date Test'
          },
          expectedError: /past.*date|cannot.*book.*past/i
        },
        {
          name: 'Invalid date format',
          data: {
            vehicle_id: testVehicleId,
            start_date: 'invalid-date',
            end_date: '2024-10-10',
            pickup_location: 'Invalid Format Test'
          },
          expectedError: /invalid.*date.*format/i
        }
      ];

      for (const scenario of invalidDateScenarios) {
        const response = await customerRequest()
          .post('/api/bookings')
          .send(scenario.data);

        if (response.status === 201) {
          console.warn(`${scenario.name} was accepted - should be validated`);
        } else {
          expectApiError(response, 400);
          if (scenario.expectedError) {
            expect(response.body.error).toMatch(scenario.expectedError);
          }
        }
      }
    });

    it('should handle booking extremely far in the future', async () => {
      if (!testVehicleId) {
        test.skip('No test vehicle available');
        return;
      }

      const farFutureDate = new Date();
      farFutureDate.setFullYear(farFutureDate.getFullYear() + 10);

      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: farFutureDate.toISOString().split('T')[0],
        end_date: farFutureDate.toISOString().split('T')[0],
        pickup_location: 'Far Future Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Should either accept or reject with appropriate message
      if (response.status !== 201) {
        expectApiError(response, 400);
        expect(response.body.error).toMatch(/too.*far.*future|booking.*limit/i);
      }
    });
  });

  describe('System Load and Resilience', () => {
    it('should handle rapid successive requests', async () => {
      const requests = [];
      
      // Create 10 rapid requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          customerRequest()
            .get('/api/vehicles')
            .query({ page: 1, limit: 5 })
        );
      }

      const responses = await Promise.all(requests);

      // All requests should succeed or be rate limited
      responses.forEach((response, index) => {
        expect([200, 429].includes(response.status)).toBe(true);
        if (response.status === 429) {
          console.log(`Request ${index + 1} was rate limited`);
        }
      });
    });

    it('should handle malformed request bodies gracefully', async () => {
      const malformedRequests = [
        {
          name: 'Extremely large payload',
          data: { data: 'x'.repeat(100000) },
          expectedStatus: [400, 413] // Bad Request or Payload Too Large
        },
        {
          name: 'Circular reference object',
          data: (() => {
            const obj: any = { name: 'test' };
            obj.self = obj;
            return obj;
          })(),
          expectedStatus: [400]
        },
        {
          name: 'Invalid JSON structure',
          data: { 'invalid"key': 'value' },
          expectedStatus: [400]
        }
      ];

      for (const scenario of malformedRequests) {
        try {
          const response = await customerRequest()
            .post('/api/bookings')
            .send(scenario.data);

          expect(scenario.expectedStatus.includes(response.status)).toBe(true);
        } catch (error) {
          // Some malformed data might cause request to fail before reaching server
          console.log(`${scenario.name} caused request error:`, error);
        }
      }
    });

    it('should handle database connection issues gracefully', async () => {
      // This test would require mocking database failures
      // For now, just verify endpoints respond within reasonable time
      
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/vehicles')
        .query({ page: 1, limit: 10 });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Should respond within 10 seconds
      expect(responseTime).toBeLessThan(10000);
      
      // Should either succeed or fail gracefully
      expect([200, 500, 503].includes(response.status)).toBe(true);
    });
  });
});
