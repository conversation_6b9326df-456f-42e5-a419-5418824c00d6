import request from 'supertest';
import { 
  app, 
  TEST_CONFIG,
  getName,
  customerRequest,
  providerRequest,
  expectApiSuccess,
  expectApiError,
  expectValidUser
} from '../testHelper';

describe('Profile Management API', () => {
  describe('GET /api/users/profile', () => {
    it('should return user profile for authenticated customer', async () => {
      const response = await customerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.user).toBeDefined();
      expectValidUser(response.body.data.user);
      expect(response.body.data.user.role).toBe('CUSTOMER');
    });

    it('should return user profile for authenticated provider', async () => {
      const response = await providerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      expect(response.body.data.user.role).toBe('PROVIDER');
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/users/profile');

      expectApiError(response, 401);
    });

    it('should not include sensitive information', async () => {
      const response = await customerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      expect(response.body.data.user.password_hash).toBeUndefined();
      expect(response.body.data.user.password).toBeUndefined();
    });
  });

  describe('PUT /api/auth/profile', () => {
    it('should update basic profile information successfully', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name',
        phone: '+***********'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiSuccess(response, 200);
      expect(response.body.data.user.first_name).toBe(updateData.first_name);
      expect(response.body.data.user.last_name).toBe(updateData.last_name);
      expect(response.body.data.user.phone).toBe(updateData.phone);
    });

    it('should update provider business information', async () => {
      const updateData = {
        business_name: 'Updated Business Name',
        address: 'Updated Business Address',
        city: 'Updated City'
      };

      const response = await providerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiSuccess(response, 200);
      expect(response.body.data.user.business_name).toBe(updateData.business_name);
      expect(response.body.data.user.address).toBe(updateData.address);
      expect(response.body.data.user.city).toBe(updateData.city);
    });

    it('should fail without authentication', async () => {
      const updateData = {
        first_name: 'Unauthorized',
        last_name: 'Update'
      };

      const response = await request(app)
        .put('/api/auth/profile')
        .send(updateData);

      expectApiError(response, 401);
    });

    it('should validate email format when updating email', async () => {
      const updateData = {
        email: 'invalid-email-format'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiError(response, 400, 'Invalid email format');
    });

    it('should prevent updating to existing email', async () => {
      // First, get another user's email (this is simplified)
      const existingEmail = TEST_CONFIG.PROVIDER_EMAIL;

      const updateData = {
        email: existingEmail
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiError(response, 400, 'Email already in use');
    });

    it('should validate phone number format', async () => {
      const updateData = {
        phone: 'invalid-phone'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      // Depending on validation, this might succeed or fail
      // expectApiError(response, 400, 'Invalid phone format');
    });

    it('should not allow role changes', async () => {
      const updateData = {
        role: 'ADMIN'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      // Role should not be changeable through profile update
      if (response.status === 200) {
        expect(response.body.data.user.role).toBe('CUSTOMER');
      } else {
        expectApiError(response, 400);
      }
    });

    it('should handle partial updates', async () => {
      const updateData = {
        first_name: 'PartialUpdate'
        // Only updating first name
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiSuccess(response, 200);
      expect(response.body.data.user.first_name).toBe(updateData.first_name);
      // Other fields should remain unchanged
      expect(response.body.data.user.last_name).toBeDefined();
    });

    it('should update timestamp on profile changes', async () => {
      // Get current profile
      const profileResponse = await customerRequest()
        .get('/api/users/profile');
      
      const originalUpdatedAt = profileResponse.body.data.user.updated_at;

      // Wait a moment to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update profile
      const updateData = {
        first_name: 'TimestampTest'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      expectApiSuccess(response, 200);
      expect(response.body.data.user.updated_at).not.toBe(originalUpdatedAt);
      expect(new Date(response.body.data.user.updated_at).getTime())
        .toBeGreaterThan(new Date(originalUpdatedAt).getTime());
    });
  });

  describe('Profile Data Validation', () => {
    it('should handle empty update requests', async () => {
      const response = await customerRequest()
        .put('/api/auth/profile')
        .send({});

      // Empty updates should either succeed (no changes) or fail gracefully
      expect([200, 400].includes(response.status)).toBe(true);
    });

    it('should sanitize input data', async () => {
      const updateData = {
        first_name: '<script>alert("xss")</script>',
        last_name: 'Normal Name'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      if (response.status === 200) {
        // Should sanitize the script tag
        expect(response.body.data.user.first_name).not.toContain('<script>');
      }
    });

    it('should handle very long input strings', async () => {
      const longString = 'a'.repeat(1000);
      const updateData = {
        first_name: longString
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      // Should either truncate or reject very long strings
      if (response.status === 200) {
        expect(response.body.data.user.first_name.length).toBeLessThanOrEqual(255);
      } else {
        expectApiError(response, 400);
      }
    });

    it('should validate date formats', async () => {
      const updateData = {
        date_of_birth: 'invalid-date'
      };

      const response = await customerRequest()
        .put('/api/auth/profile')
        .send(updateData);

      // Should validate date format
      if (response.status !== 200) {
        expectApiError(response, 400);
      }
    });
  });

  describe('Provider-Specific Profile Features', () => {
    it('should handle provider verification status', async () => {
      const response = await providerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      expect(response.body.data.user.verification_status).toBeDefined();
      expect(['PENDING', 'VERIFIED', 'REJECTED'].includes(response.body.data.user.verification_status)).toBe(true);
    });

    it('should include business-related fields for providers', async () => {
      const response = await providerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      const user = response.body.data.user;
      
      // These fields should be available for providers
      expect(user.business_name).toBeDefined();
      expect(user.total_vehicles).toBeDefined();
      expect(user.total_earnings).toBeDefined();
      expect(user.rating).toBeDefined();
    });

    it('should not include business fields for customers', async () => {
      const response = await customerRequest()
        .get('/api/users/profile');

      expectApiSuccess(response, 200);
      const user = response.body.data.user;
      
      // Business fields should be null or undefined for customers
      expect(user.business_name).toBeNull();
      expect(user.total_vehicles).toBe(0);
      expect(user.total_earnings).toBe(0);
    });
  });
});
