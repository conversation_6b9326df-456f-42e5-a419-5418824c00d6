import request from 'supertest';
import { 
  app, 
  TEST_CONFIG,
  getName,
  providerRequest,
  customerRequest,
  createTestVehicle,
  expectApiSuccess,
  expectApiError,
  expectValidVehicle
} from '../testHelper';

describe('Vehicle Management API', () => {
  describe('GET /api/vehicles', () => {
    it('should return vehicle list for public access', async () => {
      const response = await request(app)
        .get('/api/vehicles');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.pagination).toBeDefined();
    });

    it('should support pagination parameters', async () => {
      const response = await request(app)
        .get('/api/vehicles')
        .query({ page: 1, limit: 5 });

      expectApiSuccess(response, 200);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(5);
    });

    it('should support category filtering', async () => {
      const response = await request(app)
        .get('/api/vehicles')
        .query({ category: 'small_scooter' });

      expectApiSuccess(response, 200);
      // If there are results, they should match the category
      if (response.body.data.length > 0) {
        response.body.data.forEach((vehicle: any) => {
          expect(vehicle.category).toBe('small_scooter');
        });
      }
    });

    it('should support city filtering', async () => {
      const response = await request(app)
        .get('/api/vehicles')
        .query({ city: 'Jakarta' });

      expectApiSuccess(response, 200);
      // If there are results, they should match the city
      if (response.body.data.length > 0) {
        response.body.data.forEach((vehicle: any) => {
          expect(vehicle.location_city).toBe('Jakarta');
        });
      }
    });

    it('should support price range filtering', async () => {
      const response = await request(app)
        .get('/api/vehicles')
        .query({ minPrice: 50000, maxPrice: 100000 });

      expectApiSuccess(response, 200);
      // If there are results, they should be within price range
      if (response.body.data.length > 0) {
        response.body.data.forEach((vehicle: any) => {
          expect(vehicle.daily_rate).toBeGreaterThanOrEqual(50000);
          expect(vehicle.daily_rate).toBeLessThanOrEqual(100000);
        });
      }
    });
  });

  describe('GET /api/vehicles/categories', () => {
    it('should return available vehicle categories', async () => {
      const response = await request(app)
        .get('/api/vehicles/categories');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);

      // Check category structure
      response.body.data.forEach((category: any) => {
        expect(category.value).toBeDefined();
        expect(category.label).toBeDefined();
        expect(category.description).toBeDefined();
      });
    });
  });

  describe('GET /api/vehicles/price-ranges', () => {
    it('should return price ranges for different categories', async () => {
      const response = await request(app)
        .get('/api/vehicles/price-ranges');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(response.body.metadata).toBeDefined();
      expect(response.body.metadata.currency).toBe('IDR');

      // Check price range structure
      const priceRanges = response.body.data;
      expect(priceRanges.small_scooter).toBeDefined();
      expect(priceRanges.small_scooter.min).toBeDefined();
      expect(priceRanges.small_scooter.max).toBeDefined();
      expect(priceRanges.small_scooter.average).toBeDefined();
    });
  });

  describe('GET /api/vehicles/:id', () => {
    it('should return vehicle details for valid ID', async () => {
      // First get a vehicle from the list
      const listResponse = await request(app)
        .get('/api/vehicles')
        .query({ limit: 1 });

      if (listResponse.body.data.length > 0) {
        const vehicleId = listResponse.body.data[0].id;
        
        const response = await request(app)
          .get(`/api/vehicles/${vehicleId}`);

        expectApiSuccess(response, 200);
        expectValidVehicle(response.body.data);
        expect(response.body.data.id).toBe(vehicleId);
      }
    });

    it('should return 404 for non-existent vehicle', async () => {
      const response = await request(app)
        .get('/api/vehicles/non-existent-id');

      expectApiError(response, 404, 'Vehicle not found');
    });
  });

  describe('POST /api/vehicles', () => {
    it('should create vehicle successfully for provider', async () => {
      const vehicleData = createTestVehicle();
      delete vehicleData.provider_id; // Will be set from token

      const response = await providerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      expectApiSuccess(response, 201);
      expectValidVehicle(response.body.data);
      expect(response.body.data.make).toBe(vehicleData.make);
      expect(response.body.data.model).toBe(vehicleData.model);
    });

    it('should fail for customer role', async () => {
      const vehicleData = createTestVehicle();

      const response = await customerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      expectApiError(response, 403, 'Insufficient permissions');
    });

    it('should fail without authentication', async () => {
      const vehicleData = createTestVehicle();

      const response = await request(app)
        .post('/api/vehicles')
        .send(vehicleData);

      expectApiError(response, 401, 'Access token required');
    });

    it('should fail with missing required fields', async () => {
      const incompleteData = {
        make: 'Honda'
        // Missing other required fields
      };

      const response = await providerRequest()
        .post('/api/vehicles')
        .send(incompleteData);

      expectApiError(response, 400);
    });

    it('should fail with invalid category', async () => {
      const vehicleData = {
        ...createTestVehicle(),
        category: 'invalid_category'
      };

      const response = await providerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      expectApiError(response, 400);
    });

    it('should fail with negative daily rate', async () => {
      const vehicleData = {
        ...createTestVehicle(),
        daily_rate: -1000
      };

      const response = await providerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      expectApiError(response, 400);
    });
  });

  describe('PUT /api/vehicles/:id', () => {
    let testVehicleId: string;

    beforeAll(async () => {
      // Create a test vehicle
      const vehicleData = createTestVehicle();
      delete vehicleData.provider_id;

      const response = await providerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      testVehicleId = response.body.data.id;
    });

    it('should update vehicle successfully by owner', async () => {
      const updateData = {
        description: 'Updated test description',
        daily_rate: 85000
      };

      const response = await providerRequest()
        .put(`/api/vehicles/${testVehicleId}`)
        .send(updateData);

      expectApiSuccess(response, 200);
      expect(response.body.data.description).toBe(updateData.description);
      expect(response.body.data.daily_rate).toBe(updateData.daily_rate);
    });

    it('should fail for non-owner provider', async () => {
      // This would require creating another provider, simplified for now
      const updateData = { description: 'Unauthorized update' };

      const response = await customerRequest()
        .put(`/api/vehicles/${testVehicleId}`)
        .send(updateData);

      expectApiError(response, 403);
    });

    it('should fail for non-existent vehicle', async () => {
      const updateData = { description: 'Update non-existent' };

      const response = await providerRequest()
        .put('/api/vehicles/non-existent-id')
        .send(updateData);

      expectApiError(response, 404, 'Vehicle not found');
    });
  });
});
