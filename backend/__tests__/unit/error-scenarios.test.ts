import request from 'supertest';
import { 
  app, 
  TEST_CONFIG,
  customerRequest,
  providerRequest,
  createTestVehicle,
  expectApiSuccess,
  expectApiError,
  delay
} from '../testHelper';

describe('Error Scenario Testing', () => {
  let testVehicleId: string;

  beforeAll(async () => {
    // Create a test vehicle for error scenario tests
    const vehicleData = createTestVehicle();
    delete vehicleData.provider_id;

    const vehicleResponse = await providerRequest()
      .post('/api/vehicles')
      .send(vehicleData);

    testVehicleId = vehicleResponse.body.data.id;
  });

  describe('Duplicate Booking Attempts', () => {
    it('should prevent duplicate bookings for same vehicle and dates', async () => {
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-07-01',
        end_date: '2024-07-03',
        pickup_location: 'Duplicate Test Location'
      };

      // First booking
      const response1 = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiSuccess(response1, 201);

      // Second booking with same dates (should fail)
      const response2 = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // This should either fail immediately or be handled by business logic
      if (response2.status !== 201) {
        expectApiError(response2, 400, 'Vehicle not available');
      } else {
        // If both succeed, the system should handle conflicts at payment/confirmation stage
        console.warn('System allows duplicate bookings - should be handled at confirmation stage');
      }
    });

    it('should handle overlapping booking attempts', async () => {
      const baseBookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-07-10',
        end_date: '2024-07-12',
        pickup_location: 'Overlap Test Base'
      };

      // First booking
      const response1 = await customerRequest()
        .post('/api/bookings')
        .send(baseBookingData);

      expectApiSuccess(response1, 201);

      // Overlapping booking (starts during existing booking)
      const overlappingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-07-11',
        end_date: '2024-07-13',
        pickup_location: 'Overlap Test Overlapping'
      };

      const response2 = await customerRequest()
        .post('/api/bookings')
        .send(overlappingData);

      // Should prevent overlapping bookings
      if (response2.status !== 201) {
        expectApiError(response2, 400);
      }
    });

    it('should handle concurrent booking attempts', async () => {
      const bookingData1 = {
        vehicle_id: testVehicleId,
        start_date: '2024-07-20',
        end_date: '2024-07-22',
        pickup_location: 'Concurrent Test 1'
      };

      const bookingData2 = {
        vehicle_id: testVehicleId,
        start_date: '2024-07-20',
        end_date: '2024-07-22',
        pickup_location: 'Concurrent Test 2'
      };

      // Simulate concurrent requests
      const [response1, response2] = await Promise.all([
        customerRequest().post('/api/bookings').send(bookingData1),
        customerRequest().post('/api/bookings').send(bookingData2)
      ]);

      // At least one should succeed, at most one should succeed
      const successCount = [response1, response2].filter(r => r.status === 201).length;
      expect(successCount).toBeGreaterThanOrEqual(1);
      expect(successCount).toBeLessThanOrEqual(1);
    });
  });

  describe('Failed Payment Flows', () => {
    let testBookingId: string;

    beforeAll(async () => {
      // Create a booking for payment tests
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-08-01',
        end_date: '2024-08-03',
        pickup_location: 'Payment Error Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      testBookingId = response.body.data.id;
    });

    it('should handle payment intent creation failure', async () => {
      const paymentData = {
        booking_id: testBookingId
      };

      const response = await customerRequest()
        .post('/api/payments/create-intent')
        .send(paymentData);

      // Since Stripe is disabled in tests, this should fail gracefully
      if (response.status === 500) {
        expectApiError(response, 500, 'Stripe not configured');
      } else {
        expectApiSuccess(response, 200);
      }
    });

    it('should handle invalid payment confirmation', async () => {
      const confirmData = {
        payment_intent_id: 'pi_invalid_payment_intent'
      };

      const response = await customerRequest()
        .post('/api/payments/confirm')
        .send(confirmData);

      // Should handle invalid payment intent gracefully
      expectApiError(response, 500);
    });

    it('should handle webhook processing errors', async () => {
      const invalidWebhookData = {
        id: 'evt_invalid',
        type: 'invalid_event_type',
        data: {
          object: {
            id: 'invalid_object'
          }
        }
      };

      const response = await request(app)
        .post('/api/payments/webhook')
        .set('stripe-signature', 'invalid-signature')
        .send(invalidWebhookData);

      expectApiError(response, 400);
    });

    it('should handle payment timeout scenarios', async () => {
      // Simulate a payment that takes too long
      const paymentData = {
        booking_id: testBookingId
      };

      // This test would need actual timeout simulation
      // For now, just test the endpoint responds within reasonable time
      const startTime = Date.now();
      
      const response = await customerRequest()
        .post('/api/payments/create-intent')
        .send(paymentData);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Should respond within 30 seconds
      expect(responseTime).toBeLessThan(30000);
    });
  });

  describe('Availability Conflicts', () => {
    it('should handle vehicle status changes during booking', async () => {
      // This would test the scenario where a vehicle becomes unavailable
      // while a customer is in the booking process
      
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-09-01',
        end_date: '2024-09-03',
        pickup_location: 'Status Change Test'
      };

      // First, try to book the vehicle
      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Should either succeed or fail gracefully
      expect([201, 400, 409].includes(response.status)).toBe(true);
    });

    it('should handle provider disabling vehicle during booking process', async () => {
      // Create a new vehicle for this test
      const vehicleData = createTestVehicle();
      delete vehicleData.provider_id;

      const vehicleResponse = await providerRequest()
        .post('/api/vehicles')
        .send(vehicleData);

      const newVehicleId = vehicleResponse.body.data.id;

      // Disable the vehicle
      await providerRequest()
        .put(`/api/vehicles/${newVehicleId}`)
        .send({ is_active: false });

      // Try to book the disabled vehicle
      const bookingData = {
        vehicle_id: newVehicleId,
        start_date: '2024-09-10',
        end_date: '2024-09-12',
        pickup_location: 'Disabled Vehicle Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Should prevent booking of disabled vehicles
      if (response.status !== 201) {
        expectApiError(response, 400, 'Vehicle not available');
      }
    });

    it('should handle date validation edge cases', async () => {
      const invalidDateScenarios = [
        {
          name: 'Start date after end date',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2024-10-10',
            end_date: '2024-10-05',
            pickup_location: 'Invalid Date Test'
          }
        },
        {
          name: 'Past dates',
          data: {
            vehicle_id: testVehicleId,
            start_date: '2020-01-01',
            end_date: '2020-01-02',
            pickup_location: 'Past Date Test'
          }
        },
        {
          name: 'Invalid date format',
          data: {
            vehicle_id: testVehicleId,
            start_date: 'invalid-date',
            end_date: '2024-10-10',
            pickup_location: 'Invalid Format Test'
          }
        }
      ];

      for (const scenario of invalidDateScenarios) {
        const response = await customerRequest()
          .post('/api/bookings')
          .send(scenario.data);

        // Should reject invalid dates
        if (response.status === 201) {
          console.warn(`${scenario.name} was accepted - should be validated`);
        } else {
          expectApiError(response, 400);
        }
      }
    });
  });

  describe('System Load and Stress Scenarios', () => {
    it('should handle multiple rapid requests from same user', async () => {
      const requests = [];
      
      // Create 5 rapid requests
      for (let i = 0; i < 5; i++) {
        requests.push(
          customerRequest()
            .get('/api/vehicles')
            .query({ page: 1, limit: 10 })
        );
      }

      const responses = await Promise.all(requests);

      // All requests should succeed or be rate limited
      responses.forEach(response => {
        expect([200, 429].includes(response.status)).toBe(true);
      });
    });

    it('should handle malformed request bodies', async () => {
      const malformedScenarios = [
        {
          name: 'Invalid JSON',
          body: '{"invalid": json}',
          contentType: 'application/json'
        },
        {
          name: 'Extremely large payload',
          body: JSON.stringify({ data: 'x'.repeat(10000) }),
          contentType: 'application/json'
        },
        {
          name: 'Wrong content type',
          body: 'plain text data',
          contentType: 'text/plain'
        }
      ];

      for (const scenario of malformedScenarios) {
        const response = await request(app)
          .post('/api/bookings')
          .set('Content-Type', scenario.contentType)
          .send(scenario.body);

        // Should handle malformed requests gracefully
        expect([400, 413, 415].includes(response.status)).toBe(true);
      }
    });

    it('should handle database connection issues gracefully', async () => {
      // This test would require mocking database failures
      // For now, just test that endpoints respond
      
      const response = await request(app)
        .get('/api/vehicles');

      // Should either succeed or fail gracefully
      expect([200, 500, 503].includes(response.status)).toBe(true);
    });
  });

  describe('Security Edge Cases', () => {
    it('should prevent SQL injection attempts', async () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'/*",
        "1; DELETE FROM bookings; --"
      ];

      for (const injection of sqlInjectionAttempts) {
        const response = await request(app)
          .get('/api/vehicles')
          .query({ search: injection });

        // Should not cause server errors
        expect([200, 400].includes(response.status)).toBe(true);
      }
    });

    it('should handle XSS attempts in input fields', async () => {
      const xssAttempts = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("xss")</script>'
      ];

      for (const xss of xssAttempts) {
        const bookingData = {
          vehicle_id: testVehicleId,
          start_date: '2024-11-01',
          end_date: '2024-11-02',
          pickup_location: xss,
          special_requests: xss
        };

        const response = await customerRequest()
          .post('/api/bookings')
          .send(bookingData);

        // Should either sanitize input or reject malicious content
        if (response.status === 201) {
          expect(response.body.data.pickup_location).not.toContain('<script>');
          expect(response.body.data.special_requests).not.toContain('<script>');
        }
      }
    });

    it('should handle authorization bypass attempts', async () => {
      // Try to access admin endpoints without proper authorization
      const adminEndpoints = [
        '/api/admin/dashboard',
        '/api/admin/users',
        '/api/admin/vehicles'
      ];

      for (const endpoint of adminEndpoints) {
        const response = await customerRequest()
          .get(endpoint);

        // Should deny access
        expect([401, 403, 404].includes(response.status)).toBe(true);
      }
    });
  });
});
