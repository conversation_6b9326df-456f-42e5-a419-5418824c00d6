import request from 'supertest';
import { 
  app, 
  TEST_CONFIG,
  getName,
  customerRequest,
  providerRequest,
  createTestVehicle,
  createTestBooking,
  expectApiSuccess,
  expectApiError,
  expectValidBooking
} from '../testHelper';

describe('Booking Management API', () => {
  let testVehicleId: string;

  beforeAll(async () => {
    // Create a test vehicle for booking tests
    const vehicleData = createTestVehicle();
    delete vehicleData.provider_id;

    const vehicleResponse = await providerRequest()
      .post('/api/vehicles')
      .send(vehicleData);

    testVehicleId = vehicleResponse.body.data.id;
  });

  describe('GET /api/bookings', () => {
    it('should return user bookings for authenticated customer', async () => {
      const response = await customerRequest()
        .get('/api/bookings');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.pagination).toBeDefined();
    });

    it('should return provider bookings for authenticated provider', async () => {
      const response = await providerRequest()
        .get('/api/bookings');

      expectApiSuccess(response, 200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/bookings');

      expectApiError(response, 401, 'User not found');
    });

    it('should support status filtering', async () => {
      const response = await customerRequest()
        .get('/api/bookings')
        .query({ status: 'pending' });

      expectApiSuccess(response, 200);
      // If there are results, they should match the status
      if (response.body.data.length > 0) {
        response.body.data.forEach((booking: any) => {
          expect(booking.status).toBe('pending');
        });
      }
    });

    it('should support pagination', async () => {
      const response = await customerRequest()
        .get('/api/bookings')
        .query({ page: 1, limit: 5 });

      expectApiSuccess(response, 200);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(5);
    });
  });

  describe('POST /api/bookings', () => {
    it('should create booking successfully for customer', async () => {
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-03-01',
        end_date: '2024-03-03',
        start_time: '09:00',
        end_time: '18:00',
        pickup_location: 'Test Pickup Location',
        dropoff_location: 'Test Dropoff Location',
        special_requests: 'Test booking request'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiSuccess(response, 201);
      expectValidBooking(response.body.data);
      expect(response.body.data.vehicle_id).toBe(testVehicleId);
      expect(response.body.data.start_date).toBe(bookingData.start_date);
      expect(response.body.data.status).toBe('pending');
      expect(response.body.data.payment_status).toBe('pending');
    });

    it('should fail without authentication', async () => {
      const bookingData = createTestBooking();

      const response = await request(app)
        .post('/api/bookings')
        .send(bookingData);

      expectApiError(response, 401, 'User not found');
    });

    it('should fail with missing required fields', async () => {
      const incompleteData = {
        vehicle_id: testVehicleId
        // Missing dates
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(incompleteData);

      expectApiError(response, 400, 'start date, and end date are required');
    });

    it('should fail with non-existent vehicle', async () => {
      const bookingData = {
        vehicle_id: 'non-existent-vehicle-id',
        start_date: '2024-03-01',
        end_date: '2024-03-03'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiError(response, 404, 'Vehicle not found');
    });

    it('should fail with invalid date range', async () => {
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-03-03',
        end_date: '2024-03-01' // End date before start date
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiError(response, 400);
    });

    it('should calculate total amount correctly', async () => {
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-03-10',
        end_date: '2024-03-12', // 2 days
        start_time: '09:00',
        end_time: '18:00',
        pickup_location: 'Test Location'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      expectApiSuccess(response, 201);
      expect(response.body.data.total_amount).toBeGreaterThan(0);
      expect(response.body.data.base_amount).toBeGreaterThan(0);
      expect(response.body.data.tax_amount).toBeGreaterThanOrEqual(0);
      expect(response.body.data.service_fee).toBeGreaterThanOrEqual(0);
    });
  });

  describe('PUT /api/bookings/:id/status', () => {
    let testBookingId: string;

    beforeAll(async () => {
      // Create a test booking
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: '2024-04-01',
        end_date: '2024-04-03',
        pickup_location: 'Status Test Location'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      testBookingId = response.body.data.id;
    });

    it('should update booking status successfully', async () => {
      const statusUpdate = {
        status: 'confirmed'
      };

      const response = await customerRequest()
        .put(`/api/bookings/${testBookingId}/status`)
        .send(statusUpdate);

      expectApiSuccess(response, 200);
      expect(response.body.data.status).toBe('confirmed');
    });

    it('should handle cancellation with reason', async () => {
      const statusUpdate = {
        status: 'cancelled',
        cancellation_reason: 'Test cancellation reason'
      };

      const response = await customerRequest()
        .put(`/api/bookings/${testBookingId}/status`)
        .send(statusUpdate);

      expectApiSuccess(response, 200);
      expect(response.body.data.status).toBe('cancelled');
      expect(response.body.data.cancellation_reason).toBe(statusUpdate.cancellation_reason);
    });

    it('should fail with invalid status', async () => {
      const statusUpdate = {
        status: 'invalid_status'
      };

      const response = await customerRequest()
        .put(`/api/bookings/${testBookingId}/status`)
        .send(statusUpdate);

      expectApiError(response, 400, 'Invalid booking status');
    });

    it('should fail for non-existent booking', async () => {
      const statusUpdate = {
        status: 'confirmed'
      };

      const response = await customerRequest()
        .put('/api/bookings/non-existent-id/status')
        .send(statusUpdate);

      expectApiError(response, 500, 'Failed to update booking');
    });

    it('should fail without authentication', async () => {
      const statusUpdate = {
        status: 'confirmed'
      };

      const response = await request(app)
        .put(`/api/bookings/${testBookingId}/status`)
        .send(statusUpdate);

      expectApiError(response, 401, 'User not found');
    });
  });

  describe('Booking Business Logic', () => {
    it('should prevent booking in the past', async () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: pastDate.toISOString().split('T')[0],
        end_date: pastDate.toISOString().split('T')[0],
        pickup_location: 'Past Date Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // This should ideally fail, but depends on backend validation
      // expectApiError(response, 400, 'Cannot book in the past');
    });

    it('should handle same-day bookings', async () => {
      const today = new Date().toISOString().split('T')[0];
      
      const bookingData = {
        vehicle_id: testVehicleId,
        start_date: today,
        end_date: today,
        start_time: '14:00',
        end_time: '18:00',
        pickup_location: 'Same Day Test'
      };

      const response = await customerRequest()
        .post('/api/bookings')
        .send(bookingData);

      // Should succeed for same-day bookings
      expectApiSuccess(response, 201);
    });
  });
});
