import request from 'supertest';
import { 
  app, 
  TEST_CONFIG, 
  getName, 
  expectApiSuccess, 
  expectApiError,
  expectValidUser 
} from '../testHelper';

describe('Authentication API', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new customer successfully', async () => {
      const userData = {
        email: `customer.${getName('test')}@test.com`,
        password: TEST_CONFIG.PASSWORD,
        first_name: 'Test',
        last_name: 'Customer',
        role: 'CUSTOMER'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiSuccess(response, 201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.user).toBeDefined();
      expect(response.body.data.token).toBeDefined();
      
      expectValidUser(response.body.data.user);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.role).toBe('CUSTOMER');
    });

    it('should register a new provider successfully', async () => {
      const userData = {
        email: `provider.${getName('test')}@test.com`,
        password: TEST_CONFIG.PASSWORD,
        first_name: 'Test',
        last_name: 'Provider',
        role: 'PROVIDER'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiSuccess(response, 201);
      expect(response.body.data.user.role).toBe('PROVIDER');
    });

    it('should fail with invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: TEST_CONFIG.PASSWORD,
        first_name: 'Test',
        last_name: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiError(response, 400, 'Invalid email format');
    });

    it('should fail with weak password', async () => {
      const userData = {
        email: `test.${getName('weak')}@test.com`,
        password: '123',
        first_name: 'Test',
        last_name: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiError(response, 400, 'Password must be at least 6 characters');
    });

    it('should fail with missing required fields', async () => {
      const userData = {
        email: `test.${getName('missing')}@test.com`
        // Missing password
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiError(response, 400, 'Email and password are required');
    });

    it('should fail with duplicate email', async () => {
      const email = `duplicate.${getName('test')}@test.com`;
      const userData = {
        email,
        password: TEST_CONFIG.PASSWORD,
        first_name: 'Test',
        last_name: 'User'
      };

      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Second registration with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectApiError(response, 400, 'User with this email already exists');
    });
  });

  describe('POST /api/auth/login', () => {
    const testEmail = `login.${getName('test')}@test.com`;
    const testPassword = TEST_CONFIG.PASSWORD;

    beforeAll(async () => {
      // Create a test user for login tests
      await request(app)
        .post('/api/auth/register')
        .send({
          email: testEmail,
          password: testPassword,
          first_name: 'Login',
          last_name: 'Test'
        });
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testEmail,
          password: testPassword
        });

      expectApiSuccess(response, 200);
      expect(response.body.data.user).toBeDefined();
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.email).toBe(testEmail);
    });

    it('should fail with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: testPassword
        });

      expectApiError(response, 401, 'Invalid email or password');
    });

    it('should fail with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testEmail,
          password: 'wrongpassword'
        });

      expectApiError(response, 401, 'Invalid email or password');
    });

    it('should fail with missing credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testEmail
          // Missing password
        });

      expectApiError(response, 400, 'Email and password are required');
    });
  });

  describe('GET /api/auth/me', () => {
    let userToken: string;
    let userId: string;

    beforeAll(async () => {
      // Create and login a test user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: `me.${getName('test')}@test.com`,
          password: TEST_CONFIG.PASSWORD,
          first_name: 'Me',
          last_name: 'Test'
        });

      userToken = registerResponse.body.data.token;
      userId = registerResponse.body.data.user.id;
    });

    it('should return current user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${userToken}`);

      expectApiSuccess(response, 200);
      expect(response.body.data.user).toBeDefined();
      expect(response.body.data.user.id).toBe(userId);
      expect(response.body.data.user.password_hash).toBeUndefined(); // Should not include password
    });

    it('should fail without authentication token', async () => {
      const response = await request(app)
        .get('/api/auth/me');

      expectApiError(response, 401, 'Access token required');
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');

      expectApiError(response, 403, 'Invalid or expired token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout');

      expectApiSuccess(response, 200);
      expect(response.body.message).toBe('Logout successful');
    });
  });
});
