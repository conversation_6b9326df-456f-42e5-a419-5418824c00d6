#!/bin/bash

# RENTAHUB Environment Setup Script

echo "Setting up RENTAHUB environment variables..."

# Create .env file with required variables
cat > .env << 'EOF'
# RENTAHUB Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/rentahub
DATABASE_PROVIDER=postgresql

# Server Configuration
PORT=3001
NODE_ENV=development

# Authentication Secrets
JWT_SECRET=your_very_long_and_secure_random_secret_key_123456789
JWT_EXPIRATION=24h

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_51RhDTEFSJ2b2gFS10sW4gFLJ
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_13bb090c39c0e6d3a3d4d609de2e1ce3092100fae2f40393f0a5bc6b72ee438a

# Email Service Configuration
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_SERVICE_API_KEY=your_email_service_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
FRONTEND_PORT=3000

# Backend Configuration
BACKEND_URL=http://localhost:3001
BACKEND_PORT=3001

# Deployment Platform
DEPLOY_PLATFORM=local

# Logging and Monitoring
LOG_LEVEL=info

# Feature Flags
ENABLE_GOOGLE_AUTH=true
ENABLE_EMAIL_AUTH=true
ENABLE_STRIPE_PAYMENTS=true
ENABLE_SOS_ALERTS=false
ENABLE_SUPPORT_TICKETS=false

# Security Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://rentahub.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Support Email Configuration
SUPPORT_EMAIL=<EMAIL>
EMERGENCY_EMAIL=<EMAIL>

# SMTP Configuration (for email sending)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EOF

echo "✅ Environment file created successfully!"
echo ""
echo "⚠️  IMPORTANT: You need to update the following values in .env:"
echo "   - DATABASE_URL: Your actual PostgreSQL connection string"
echo "   - JWT_SECRET: A secure random string (at least 32 characters)"
echo "   - STRIPE_SECRET_KEY: Your actual Stripe secret key"
echo "   - GOOGLE_CLIENT_ID: Your Google OAuth client ID"
echo "   - GOOGLE_CLIENT_SECRET: Your Google OAuth client secret"
echo "   - SMTP_USER: Your email address"
echo "   - SMTP_PASS: Your email app password"
echo ""
echo "For now, you can use the placeholder values to get the server running." 