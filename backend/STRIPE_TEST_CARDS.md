# Stripe Test Cards for RentaHub Integration Tests

## Test Card Numbers

### Successful Payments
- **Visa (Successful Payment)**: 
  - Card Number: `4242 4242 4242 4242`
  - Expiry: Any future date
  - CVC: Any 3-digit number
  - Postal Code: Any valid postal code

### Specific Scenario Test Cards

1. **Insufficient Funds**:
   - Card Number: `4000 0000 0000 9995`
   - Simulates a card with insufficient funds

2. **Card Declined**:
   - Card Number: `4000 0000 0000 0002`
   - Always triggers a decline

3. **Authentication Required**:
   - Card Number: `4000 0025 0000 3155`
   - Simulates a card requiring additional authentication

## Best Practices
- Always use test cards in integration tests
- Never use real card numbers
- Rotate test cards periodically
- Ensure tests cover various payment scenarios

## Stripe Test Mode Considerations
- These cards only work in <PERSON><PERSON>'s test mode
- No real charges are processed
- Results are simulated by Stripe

## Integration Test Scenarios to Cover
- Successful payment
- Payment with insufficient funds
- Declined payments
- Authentication required
- Refund processing
- Partial refunds 