# =============================================================================
# RENTAHUB PRODUCTION ENVIRONMENT VARIABLES
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRATION=24h

# =============================================================================
# SUPABASE DATABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://rocxjzukyqelvuyltfrq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.Ej4KcZKbKxUzJVXKZKQUKJns1cU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU

# =============================================================================
# STRIPE PAYMENT CONFIGURATION
# =============================================================================
# Get these from your Stripe Dashboard (https://dashboard.stripe.com)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# =============================================================================
# EMAIL SERVICE CONFIGURATION
# =============================================================================
# SMTP Configuration for sending emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=RentaHub

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# For vehicle images and user avatars
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://your-frontend-domain.com,https://your-admin-domain.com

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session security
SESSION_SECRET=your-session-secret-key-minimum-32-characters

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
# Google Maps API for location services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# SMS service for phone verification
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Sentry for error tracking
SENTRY_DSN=your_sentry_dsn_here

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# BUSINESS CONFIGURATION
# =============================================================================
# Platform settings
PLATFORM_COMMISSION_RATE=0.10
PLATFORM_SERVICE_FEE=5000
DEFAULT_CURRENCY=IDR
DEFAULT_COUNTRY=Indonesia
DEFAULT_TIMEZONE=Asia/Jakarta

# Booking settings
MIN_BOOKING_HOURS=4
MAX_BOOKING_DAYS=30
CANCELLATION_WINDOW_HOURS=24

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Only used in development
DEBUG=false
MOCK_PAYMENTS=false
MOCK_SMS=false
MOCK_EMAIL=false

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================
# Railway deployment settings
RAILWAY_STATIC_URL=your-railway-static-url
RAILWAY_PUBLIC_DOMAIN=your-railway-domain.railway.app

# Health check settings
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================
# Database backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Maintenance mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=RentaHub is currently under maintenance. Please try again later.
