# Server Configuration
PORT=3000
NODE_ENV=production
HOSTNAME=0.0.0.0

# Database Configuration
DATABASE_URL=${PGHOST}://${PGUSER}:${PGPASSWORD}@${PGHOST}:${PGPORT}/${PGDATABASE}
DATABASE_PROVIDER=postgresql

# Authentication
JWT_SECRET=${RAILWAY_JWT_SECRET}
JWT_EXPIRATION=24h

# Stripe Configuration
STRIPE_SECRET_KEY=${RAILWAY_STRIPE_SECRET_KEY}
STRIPE_PUBLISHABLE_KEY=${RAILWAY_STRIPE_PUBLISHABLE_KEY}
STRIPE_WEBHOOK_SECRET=${RAILWAY_STRIPE_WEBHOOK_SECRET}

# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=${RAILWAY_SENDGRID_API_KEY}

# Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Security
CORS_ORIGIN=${RAILWAY_FRONTEND_URL}
