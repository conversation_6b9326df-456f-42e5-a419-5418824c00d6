# RentaHub Backend

🚗 **Modern Vehicle Rental Platform Backend** - Built with Node.js, Express, TypeScript, and Prisma

## 🌟 Features

- **🔐 Authentication & Authorization** - JWT-based auth with role-based access control
- **🚗 Vehicle Management** - Comprehensive vehicle catalog and listing system
- **📅 Booking System** - Advanced booking workflow with availability checking
- **💳 Payment Processing** - Stripe integration with webhook handling
- **📊 Analytics & Reporting** - Real-time dashboard and analytics
- **🔍 Search & Filtering** - Advanced vehicle search with geospatial support
- **📱 API-First Design** - RESTful API with standardized responses
- **🚀 Performance Optimized** - Caching, rate limiting, and query optimization
- **📚 Comprehensive Documentation** - API docs and architecture guides

## 🏗️ Architecture

```
backend/src/
├── config/          # Configuration & environment
├── controllers/     # Request handlers (organized by domain)
├── middleware/      # Express middleware
├── routes/          # API route definitions
├── services/        # Business logic layer
├── lib/            # External service integrations
├── utils/          # Utility functions
├── types/          # TypeScript type definitions
├── docs/           # Documentation
└── tests/          # Test files
```

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- PostgreSQL database
- Supabase account (optional)
- Stripe account (for payments)

### Installation

1. **Clone and install dependencies:**
```bash
cd backend
npm install
```

2. **Set up environment variables:**
```bash
cp ../.env.example .env
# Edit .env with your actual values
```

3. **Set up database:**
```bash
npx prisma generate
npx prisma db push
```

4. **Start development server:**
```bash
npm run dev
```

The server will start at `http://localhost:3001`

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/rentahub

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication
JWT_SECRET=your-super-secure-jwt-secret

# Stripe
STRIPE_SECRET_KEY=sk_test_your-stripe-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
```

### Database Setup

The backend uses Prisma ORM with PostgreSQL:

```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# View database in Prisma Studio
npx prisma studio
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Vehicles
- `GET /api/vehicles` - List vehicles (paginated)
- `GET /api/vehicles/:id` - Get vehicle details
- `GET /api/vehicles/search` - Search vehicles
- `GET /api/vehicles/suggestions` - Autocomplete suggestions

### Bookings
- `POST /api/bookings` - Create booking
- `GET /api/bookings` - List user bookings
- `GET /api/bookings/:id` - Get booking details
- `PUT /api/bookings/:id/status` - Update booking status

### Payments
- `POST /api/payments/create-payment-intent` - Create payment
- `POST /api/payments/confirm-payment` - Confirm payment
- `GET /api/payments/history` - Payment history

See [API Documentation](src/docs/API.md) for complete endpoint reference.

## 🧪 Testing

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run tests with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Railway Deployment
The app is configured for Railway deployment:

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push to main branch

## 📊 Performance

### Caching
- Response caching for GET requests
- Redis support for distributed caching
- Automatic cache invalidation

### Rate Limiting
- 100 requests per 15 minutes (general)
- 5 requests per 15 minutes (auth endpoints)
- Configurable per endpoint

### Database Optimization
- Connection pooling
- Query optimization utilities
- Batch operations support

## 🔐 Security

### Authentication
- JWT tokens with configurable expiration
- Password hashing with bcrypt
- OAuth integration (Google, Facebook)

### Authorization
- Role-based access control (RBAC)
- Permission-based route protection
- API key authentication for integrations

## 📚 Documentation

- [API Documentation](src/docs/API.md) - Complete API reference
- [Architecture Guide](src/docs/ARCHITECTURE.md) - System architecture

## 🛠️ Development

### Code Organization
- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic
- **Middleware**: Handle cross-cutting concerns
- **Utils**: Utility functions and helpers

### Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run test         # Run tests
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

## 📄 License

This project is licensed under the MIT License.
