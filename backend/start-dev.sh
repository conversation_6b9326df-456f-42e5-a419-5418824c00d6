#!/bin/bash

echo "🚀 Starting RentaHub Backend Development Server"
echo "📦 Node Version: $(node --version)"
echo "🌍 Environment: development"
echo "🔗 Port: 3001"

# Kill any existing processes on port 3001
echo "🧹 Cleaning up existing processes..."
pkill -f "test-server.ts" || true
pkill -f "src/index.ts" || true

# Wait a moment for processes to clean up
sleep 2

# Start the development server
echo "🚀 Starting server..."
npx tsx src/index.ts 