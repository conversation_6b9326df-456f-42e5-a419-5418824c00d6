name: Code Coverage

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      # Setup Node.js
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
      
      # Install dependencies
      - name: Install dependencies
        run: npm ci
      
      # Run tests with coverage
      - name: Run tests with coverage
        run: npm run test:coverage
      
      # Upload coverage to Codecov
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          fail_ci_if_error: true
          verbose: true
