name: Scheduled Project Maintenance

on:
  schedule:
    # Run every Sunday at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:  # Allow manual triggering

jobs:
  maintenance:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install Dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run Maintenance Script
      working-directory: ./backend
      run: bash scripts/project-maintenance.sh
    
    - name: Notify Slack on Failure
      uses: 8398a7/action-slack@v3
      if: failure()
      with:
        status: ${{ job.status }}
        text: Scheduled maintenance failed
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
    
    - name: Upload Maintenance Logs
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: maintenance-logs
        path: backend/maintenance-logs/
        retention-days: 7 