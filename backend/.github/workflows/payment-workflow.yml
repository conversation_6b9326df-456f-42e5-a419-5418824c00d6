name: Payment Services CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/src/services/PaymentService.ts'
      - 'backend/src/controllers/PaymentController.ts'
      - 'backend/src/__tests__/PaymentService.test.ts'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/src/services/PaymentService.ts'
      - 'backend/src/controllers/PaymentController.ts'
      - 'backend/src/__tests__/PaymentService.test.ts'

# Add explicit permissions
permissions:
  contents: read
  actions: read

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_DB: rentahub_test
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install Dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run Prisma Migrations
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/rentahub_test
      run: npx prisma migrate dev
    
    - name: Run Tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/rentahub_test
        STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
        STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_TEST_WEBHOOK_SECRET }}
      run: npm test -- --coverage
    
    - name: Upload Coverage
      uses: codecov/codecov-action@v3
      with:
        files: ./backend/coverage/lcov.info
        flags: payment-services

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        command: test
        args: ./backend --severity-threshold=high

  performance-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.x'
    
    - name: Install Dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run Performance Benchmarks
      working-directory: ./backend
      run: npm run benchmark:payment-services

  deploy:
    needs: [test, security-scan, performance-check]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to Production
      env:
        DEPLOY_KEY: ${{ secrets.SERVER_SSH_KEY }}
      run: |
        echo "${{ secrets.SERVER_SSH_KEY }}" > deploy_key
        chmod 600 deploy_key
        ssh -i deploy_key <EMAIL> "cd /path/to/app && git pull && npm run deploy:payment-services"
