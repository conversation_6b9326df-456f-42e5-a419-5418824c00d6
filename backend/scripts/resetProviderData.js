const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetProviderData() {
  const providerEmail = '<EMAIL>';

  try {
    console.log('🔄 Starting provider data reset for:', providerEmail);

    // Check if provider exists
    const provider = await prisma.user.findUnique({
      where: { email: providerEmail },
      select: { id: true, name: true, email: true, role: true }
    });
    
    if (!provider) {
      console.log('❌ Provider not found with email:', providerEmail);
      return;
    }
    
    if (provider.role !== 'PROVIDER') {
      console.log('❌ User is not a provider:', provider.role);
      return;
    }
    
    console.log('✅ Found provider:', provider.name, '(' + provider.email + ')');
    
    const providerId = provider.id;

    // Get all vehicles for this provider
    const vehicles = await prisma.vehicle.findMany({
      where: { providerId },
      select: { id: true, brand: true, model: true }
    });

    console.log(`📊 Found ${vehicles.length} vehicles to clean up`);

    // Get all bookings for this provider
    const bookings = await prisma.booking.findMany({
      where: { providerId },
      select: { id: true, status: true, totalPrice: true }
    });
    
    console.log(`📊 Found ${bookings.length} bookings to clean up`);
    
    // Start deletion process
    console.log('\n🗑️  Starting deletion process...');
    
    // 1. Delete booking-related data
    if (bookings.length > 0) {
      const bookingIds = bookings.map(b => b.id);
      
      // Delete ride checklists
      const rideChecklistsDeleted = await prisma.rideChecklist.deleteMany({
        where: { bookingId: { in: bookingIds } }
      });
      console.log(`   ✅ Deleted ${rideChecklistsDeleted.count} ride checklists`);
      
      // Delete damage reports
      const damageReportsDeleted = await prisma.damageReport.deleteMany({
        where: { bookingId: { in: bookingIds } }
      });
      console.log(`   ✅ Deleted ${damageReportsDeleted.count} damage reports`);
      
      // Delete SOS alerts
      const sosAlertsDeleted = await prisma.sOSAlert.deleteMany({
        where: { bookingId: { in: bookingIds } }
      });
      console.log(`   ✅ Deleted ${sosAlertsDeleted.count} SOS alerts`);
      
      // Delete payments
      const paymentsDeleted = await prisma.payment.deleteMany({
        where: { bookingId: { in: bookingIds } }
      });
      console.log(`   ✅ Deleted ${paymentsDeleted.count} payments`);
      
      // Delete bookings
      const bookingsDeleted = await prisma.booking.deleteMany({
        where: { providerId }
      });
      console.log(`   ✅ Deleted ${bookingsDeleted.count} bookings`);
    }
    
    // 2. Delete vehicle-related data
    if (vehicles.length > 0) {
      const vehicleIds = vehicles.map(v => v.id);
      
      // Delete vehicle images
      const vehicleImagesDeleted = await prisma.vehicleImage.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${vehicleImagesDeleted.count} vehicle images`);
      
      // Delete vehicle availability
      const availabilityDeleted = await prisma.vehicleAvailability.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${availabilityDeleted.count} availability records`);
      
      // Delete vehicle performance metrics
      const metricsDeleted = await prisma.vehiclePerformanceMetric.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${metricsDeleted.count} performance metrics`);
      
      // Delete saved vehicles
      const savedVehiclesDeleted = await prisma.savedVehicle.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${savedVehiclesDeleted.count} saved vehicle records`);
      
      // Delete reviews
      const reviewsDeleted = await prisma.review.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${reviewsDeleted.count} reviews`);
      
      // Delete insurance policies
      const insuranceDeleted = await prisma.insurancePolicy.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${insuranceDeleted.count} insurance policies`);
      
      // Delete historical risk data
      const riskDataDeleted = await prisma.historicalRiskDataset.deleteMany({
        where: { vehicleId: { in: vehicleIds } }
      });
      console.log(`   ✅ Deleted ${riskDataDeleted.count} historical risk data records`);
      
      // Delete vehicles
      const vehiclesDeleted = await prisma.vehicle.deleteMany({
        where: { providerId }
      });
      console.log(`   ✅ Deleted ${vehiclesDeleted.count} vehicles`);
    }
    
    // 3. Delete provider-specific data (but keep the user account)
    // Delete notifications for this provider
    const notificationsDeleted = await prisma.notification.deleteMany({
      where: { userId: providerId }
    });
    console.log(`   ✅ Deleted ${notificationsDeleted.count} notifications`);
    
    // Delete agreements
    const agreementsDeleted = await prisma.agreement.deleteMany({
      where: { providerId }
    });
    console.log(`   ✅ Deleted ${agreementsDeleted.count} agreements`);
    
    console.log('\n✅ Provider data reset completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Provider account: KEPT (${provider.name})`);
    console.log(`   - Vehicles deleted: ${vehicles.length}`);
    console.log(`   - Bookings deleted: ${bookings.length}`);
    console.log(`   - All related data cleaned up`);
    
  } catch (error) {
    console.error('❌ Error resetting provider data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
resetProviderData()
  .then(() => {
    console.log('🎉 Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
