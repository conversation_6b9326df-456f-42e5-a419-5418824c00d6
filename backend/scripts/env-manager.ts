import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

class EnvironmentManager {
  private static ENV_TEMPLATE = {
    development: {
      NODE_ENV: 'development',
      PORT: 4000,
      LOG_LEVEL: 'debug'
    },
    production: {
      NODE_ENV: 'production',
      PORT: 3000,
      LOG_LEVEL: 'error'
    },
    test: {
      NODE_ENV: 'test',
      PORT: 5000,
      LOG_LEVEL: 'warn'
    }
  };

  // Generate secure secret
  static generateSecret(length = 64): string {
    return crypto.randomBytes(length).toString('hex');
  }

  // Create environment file
  static createEnvFile(env: 'development' | 'production' | 'test') {
    const envPath = path.resolve(process.cwd(), `.env.${env}`);
    
    const baseConfig = this.ENV_TEMPLATE[env];
    const additionalConfig = {
      JWT_SECRET: this.generateSecret(),
      SENTRY_DSN: env === 'production' ? 'your_sentry_dsn' : '',
      SUPABASE_URL: env === 'production' ? 'your_supabase_url' : 'http://localhost:54321',
      STRIPE_SECRET_KEY: env === 'production' ? 'your_stripe_secret_key' : ''
    };

    const envContent = Object.entries({
      ...baseConfig,
      ...additionalConfig
    }).map(([key, value]) => `${key}=${value}`).join('\n');

    fs.writeFileSync(envPath, envContent);
    console.log(`Created ${envPath}`);
  }

  // Validate environment configuration
  static validateEnv(env: Record<string, string>) {
    const requiredKeys = [
      'NODE_ENV', 
      'PORT', 
      'JWT_SECRET', 
      'SUPABASE_URL'
    ];

    const missingKeys = requiredKeys.filter(key => !env[key]);
    
    if (missingKeys.length > 0) {
      throw new Error(`Missing required environment variables: ${missingKeys.join(', ')}`);
    }
  }

  // Load and validate environment
  static loadEnv(env: 'development' | 'production' | 'test') {
    const envPath = path.resolve(process.cwd(), `.env.${env}`);
    
    if (!fs.existsSync(envPath)) {
      this.createEnvFile(env);
    }

    const envContent = fs.readFileSync(envPath, 'utf-8');
    const parsedEnv = Object.fromEntries(
      envContent.split('\n')
        .filter(line => line.trim() && !line.startsWith('#'))
        .map(line => line.split('='))
    );

    this.validateEnv(parsedEnv);
    return parsedEnv;
  }

  // Main execution
  static run() {
    const command = process.argv[2];
    const env = process.argv[3] as 'development' | 'production' | 'test';

    switch (command) {
      case 'create':
        this.createEnvFile(env);
        break;
      case 'validate':
        this.loadEnv(env);
        console.log(`Environment ${env} is valid`);
        break;
      default:
        console.error('Usage: ts-node env-manager.ts [create|validate] [development|production|test]');
        process.exit(1);
    }
  }
}

// Run the script if called directly
if (require.main === module) {
  EnvironmentManager.run();
} 