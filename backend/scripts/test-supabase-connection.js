#!/usr/bin/env node

/**
 * Test Supabase Connection After Permission Fix
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testConnection() {
  console.log('🔍 Testing Supabase Connection After Permission Fix...');
  console.log('====================================================');

  try {
    // Test 1: Basic table access
    console.log('📋 Test 1: Basic table access...');
    const { data: users, error: usersError } = await supabase
      .from('User')
      .select('count')
      .limit(1);

    if (usersError) {
      console.log('❌ Users table access failed:', usersError.message);
    } else {
      console.log('✅ Users table accessible');
    }

    // Test 2: Try to access other common tables
    const tables = ['Vehicle', 'Booking', 'Payment', 'Review'];
    
    for (const table of tables) {
      console.log(`📋 Test: ${table} table access...`);
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1);

      if (error) {
        console.log(`⚠️  ${table} table: ${error.message}`);
      } else {
        console.log(`✅ ${table} table accessible`);
      }
    }

    // Test 3: Try to execute a function
    console.log('📋 Test 3: Function execution...');
    const { data: funcResult, error: funcError } = await supabase
      .rpc('exec_sql', { sql: 'SELECT 1 as test' });

    if (funcError) {
      console.log('⚠️  Function execution failed:', funcError.message);
    } else {
      console.log('✅ Function execution successful');
    }

    // Test 4: Try to insert test data
    console.log('📋 Test 4: Insert test data...');
    const testUser = {
      id: 'test-' + Date.now(),
      email: '<EMAIL>',
      role: 'CUSTOMER',
      created_at: new Date().toISOString()
    };

    const { data: insertResult, error: insertError } = await supabase
      .from('User')
      .insert([testUser])
      .select();

    if (insertError) {
      console.log('⚠️  Insert failed:', insertError.message);
    } else {
      console.log('✅ Insert successful');

      // Clean up test data
      await supabase
        .from('User')
        .delete()
        .eq('id', testUser.id);
      console.log('🧹 Test data cleaned up');
    }

    console.log('');
    console.log('🎉 Connection test completed!');
    console.log('✅ If you see mostly green checkmarks, the permissions are fixed!');

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
  }
}

if (require.main === module) {
  testConnection()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testConnection };
