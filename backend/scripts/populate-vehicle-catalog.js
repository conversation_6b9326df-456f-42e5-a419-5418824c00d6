const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
require('dotenv').config();

const prisma = new PrismaClient();

async function populateVehicleCatalog() {
  try {
    console.log('🚗 Populating Vehicle Catalog from Scraped Data');
    console.log('===============================================');
    
    // Read the scraped data
    const data = JSON.parse(fs.readFileSync('../indonesia_scooters_essential.json', 'utf8'));
    console.log(`📊 Found ${data.data.length} vehicles to import`);
    
    // Clear existing data
    console.log('\n🧹 Clearing existing vehicle_catalog data...');
    await prisma.$executeRaw`DELETE FROM vehicle_catalog`;
    
    // Transform and insert data
    console.log('\n📥 Inserting vehicle data...');
    let inserted = 0;
    let skipped = 0;
    
    for (const vehicle of data.data) {
      try {
        // Calculate daily rate from price
        let dailyRate = null;
        if (vehicle.specs?.price) {
          const priceStr = vehicle.specs.price.replace(/[₹,]/g, '');
          const monthlyPrice = parseFloat(priceStr);
          if (!isNaN(monthlyPrice)) {
            dailyRate = Math.round(monthlyPrice / 30); // Convert to daily estimate
          }
        }
        
        // If no price, generate reasonable daily rate based on make
        if (!dailyRate) {
          const premiumBrands = ['Triumph', 'Ducati', 'BMW', 'KTM'];
          const midRangeBrands = ['Royal Enfield', 'Yamaha', 'Honda'];
          
          if (premiumBrands.some(brand => vehicle.make.includes(brand))) {
            dailyRate = Math.floor(Math.random() * 3000) + 2000; // 2000-5000
          } else if (midRangeBrands.some(brand => vehicle.make.includes(brand))) {
            dailyRate = Math.floor(Math.random() * 1500) + 800; // 800-2300
          } else {
            dailyRate = Math.floor(Math.random() * 1000) + 500; // 500-1500
          }
        }
        
        // Use Prisma create instead of raw SQL to avoid syntax issues
        await prisma.$executeRaw`
          INSERT INTO vehicle_catalog (
            id, make, model, year, engine, type, category,
            image_url, source, daily_rate, rating, reviews,
            features, available_units, has_insurance,
            insurance_price, location_city, is_reference_only,
            created_at, updated_at
          ) VALUES (
            gen_random_uuid(),
            ${vehicle.make || 'Unknown'},
            ${vehicle.model || 'Unknown'},
            ${vehicle.year?.toString() || '2025'},
            ${vehicle.specs?.engine || vehicle.engineSize || 'Not specified'},
            'motorcycle',
            'scooter_motorcycle',
            ${vehicle.images?.[0] || null},
            'scraped_indonesia_data',
            ${dailyRate}::numeric,
            ${Math.round((Math.random() * 2 + 3) * 10) / 10}::numeric,
            ${Math.floor(Math.random() * 100)}::integer,
            ARRAY[${vehicle.specs?.transmission || 'Manual'}]::text[],
            ${Math.floor(Math.random() * 5) + 1}::integer,
            true::boolean,
            ${Math.floor(Math.random() * 500) + 200}::numeric,
            'Indonesia',
            true::boolean,
            NOW(),
            NOW()
          )
        `;
        
        inserted++;
        
        if (inserted % 10 === 0) {
          console.log(`   ✅ Progress: ${inserted}/${data.data.length} vehicles inserted`);
        }
        
      } catch (error) {
        skipped++;
        console.log(`   ⚠️  Skipped: ${vehicle.make} ${vehicle.model} - ${error.message}`);
      }
    }
    
    console.log(`\n🎉 Import Complete!`);
    console.log(`   ✅ Successfully inserted: ${inserted} vehicles`);
    console.log(`   ⚠️  Skipped: ${skipped} vehicles`);
    
    // Verify the final count
    const count = await prisma.$queryRaw`SELECT COUNT(*) as count FROM vehicle_catalog`;
    console.log(`📊 Total vehicles in catalog: ${count[0].count}`);
    
    // Show sample data for verification
    console.log('\n📋 Sample inserted vehicles:');
    const sample = await prisma.$queryRaw`
      SELECT make, model, year, daily_rate, image_url 
      FROM vehicle_catalog 
      ORDER BY make, model
      LIMIT 10
    `;
    
    sample.forEach((vehicle, index) => {
      const hasImage = vehicle.image_url ? '🖼️' : '❌';
      console.log(`   ${index + 1}. ${vehicle.make} ${vehicle.model} (${vehicle.year}) - ₹${vehicle.daily_rate}/day ${hasImage}`);
    });
    
    // Show unique makes for autocomplete testing
    console.log('\n🏷️  Unique makes for autocomplete:');
    const makes = await prisma.$queryRaw`
      SELECT DISTINCT make, COUNT(*) as model_count 
      FROM vehicle_catalog 
      GROUP BY make 
      ORDER BY make
    `;
    
    makes.slice(0, 10).forEach(make => {
      console.log(`   • ${make.make} (${make.model_count} models)`);
    });
    
    console.log(`\n🎯 Vehicle catalog is ready for autocomplete functionality!`);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    console.log('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the population
populateVehicleCatalog();
