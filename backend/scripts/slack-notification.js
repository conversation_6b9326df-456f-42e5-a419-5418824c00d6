const axios = require('axios');

async function sendSlackNotification(message, webhookUrl) {
  try {
    await axios.post(webhookUrl, {
      text: message,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: message
          }
        }
      ]
    });
    console.log('Slack notification sent successfully');
  } catch (error) {
    console.error('Failed to send Slack notification:', error);
  }
}

module.exports = {
  sendSlackNotification,
  
  // Example usage in CI/CD pipeline
  notifyCodeCoverage: async (coverageReport) => {
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!webhookUrl) {
      console.warn('Slack webhook URL not configured');
      return;
    }

    const message = `🧪 Code Coverage Report:
    - Total Coverage: ${coverageReport.total}%
    - Changed Files: ${coverageReport.changedFiles}
    - Uncovered Lines: ${coverageReport.uncoveredLines}`;

    await sendSlackNotification(message, webhookUrl);
  }
};
