#!/usr/bin/env node

/**
 * Execute Supabase Permissions Fix using Node.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Create Supabase client with service role
const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function executePermissionsFix() {
  console.log('🔧 Executing Supabase Permissions Fix...');
  console.log('==========================================');

  try {
    // Execute individual SQL commands
    const commands = [
      'GRANT USAGE ON SCHEMA public TO service_role;',
      'GRANT CREATE ON SCHEMA public TO service_role;',
      'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;',
      'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;',
      'GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO service_role;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;',
      'ALTER ROLE service_role SET row_security = off;',
      'GRANT ALL ON SCHEMA public TO service_role;',

      // Add anon role permissions for frontend access
      'GRANT USAGE ON SCHEMA public TO anon;',
      'GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;',
      'GRANT INSERT ON ALL TABLES IN SCHEMA public TO anon;',
      'GRANT UPDATE ON ALL TABLES IN SCHEMA public TO anon;',
      'GRANT DELETE ON ALL TABLES IN SCHEMA public TO anon;',

      // Add authenticated role permissions
      'GRANT USAGE ON SCHEMA public TO authenticated;',
      'GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;',
      'GRANT INSERT ON ALL TABLES IN SCHEMA public TO authenticated;',
      'GRANT UPDATE ON ALL TABLES IN SCHEMA public TO authenticated;',
      'GRANT DELETE ON ALL TABLES IN SCHEMA public TO authenticated;',

      // Set default privileges for anon role
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO anon;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO anon;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO anon;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO anon;',

      // Set default privileges for authenticated role
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO authenticated;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO authenticated;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO authenticated;',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO authenticated;'
    ];

    console.log('🔧 Executing basic permission commands...');
    
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      console.log(`📝 [${i + 1}/${commands.length}] ${command}`);
      
      try {
        // Try to execute using rpc if available
        const { error } = await supabase.rpc('exec_sql', { sql: command });
        
        if (error) {
          console.log(`⚠️  Command failed: ${error.message}`);
        } else {
          console.log('✅ Command executed successfully');
        }
      } catch (err) {
        console.log(`⚠️  Command error: ${err.message}`);
      }
    }

    // Create the exec_sql function
    console.log('🔧 Creating exec_sql function...');
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
          EXECUTE sql;
      END;
      $$;
    `;

    try {
      const { error: funcError } = await supabase.rpc('exec_sql', { sql: createFunctionSql });
      if (funcError) {
        console.log('⚠️  Function creation failed:', funcError.message);
      } else {
        console.log('✅ exec_sql function created');
        
        // Grant execute permission
        const { error: grantError } = await supabase.rpc('exec_sql', { 
          sql: 'GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO service_role;' 
        });
        
        if (grantError) {
          console.log('⚠️  Grant execute failed:', grantError.message);
        } else {
          console.log('✅ Execute permission granted');
        }
      }
    } catch (err) {
      console.log('⚠️  Function creation error:', err.message);
    }

    // Test the connection
    console.log('🔍 Testing connection after fixes...');
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (testError) {
      console.log('❌ Connection test failed:', testError.message);
      console.log('');
      console.log('🔧 MANUAL EXECUTION REQUIRED:');
      console.log('=============================');
      console.log('Please execute the following SQL manually in Supabase SQL Editor:');
      console.log('https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq/sql/new');
      console.log('');
      commands.forEach((cmd, i) => {
        console.log(`-- Command ${i + 1}`);
        console.log(cmd);
        console.log('');
      });
    } else {
      console.log('✅ Connection test successful!');
      console.log('🎉 Permissions have been fixed!');
    }

  } catch (error) {
    console.error('❌ Permission fix failed:', error.message);
    console.log('');
    console.log('🔧 Please execute the SQL manually in Supabase dashboard');
  }
}

if (require.main === module) {
  executePermissionsFix()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Execution failed:', error);
      process.exit(1);
    });
}

module.exports = { executePermissionsFix };
