-- Create application_logs table for monitoring
CREATE TABLE public.application_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    level VARCHAR(10) NOT NULL CHECK (level IN ('info', 'warn', 'error')),
    message TEXT NOT NULL,
    context JSONB,
    error JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX idx_application_logs_timestamp ON public.application_logs(timestamp);
CREATE INDEX idx_application_logs_level ON public.application_logs(level);

-- Optional: Row-level security for logs
ALTER TABLE public.application_logs ENABLE ROW LEVEL SECURITY;

-- Policy to restrict log access (adjust as needed)
CREATE POLICY "Logs are viewable by service role only" 
    ON public.application_logs 
    FOR SELECT 
    USING (role() = 'service_role'); 