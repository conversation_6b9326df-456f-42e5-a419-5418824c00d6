#!/usr/bin/env node

/**
 * Fix Supabase Database Permissions
 * 
 * This script fixes the "permission denied for schema public" error
 * by granting proper privileges to the service_role.
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Create Supabase client with service role
const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixPermissions() {
  console.log('🔧 Fixing Supabase Database Permissions...');
  console.log('=============================================');

  try {
    // Step 1: Test current connection
    console.log('🔍 Step 1: Testing current connection...');
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (testError) {
      console.log('❌ Current connection failed:', testError.message);
      console.log('🔧 Proceeding with permission fixes...');
    } else {
      console.log('✅ Connection working - permissions may already be fixed');
      return;
    }

    // Step 2: Execute permission fixes using SQL
    console.log('🔧 Step 2: Applying permission fixes...');
    
    const permissionQueries = [
      // Grant usage on schema public to service_role
      'GRANT USAGE ON SCHEMA public TO service_role;',
      
      // Grant all privileges on all tables in public schema
      'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;',
      
      // Grant all privileges on all sequences in public schema
      'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;',
      
      // Grant all privileges on all functions in public schema
      'GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;',
      
      // Set default privileges for future tables
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO service_role;',
      
      // Set default privileges for future sequences
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;',
      
      // Set default privileges for future functions
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;',
      
      // Grant create privilege on schema public
      'GRANT CREATE ON SCHEMA public TO service_role;',
      
      // Make service_role a superuser-like role for this schema
      'GRANT postgres TO service_role;'
    ];

    for (const query of permissionQueries) {
      try {
        console.log(`🔧 Executing: ${query}`);
        const { error } = await supabase.rpc('exec_sql', { sql: query });
        
        if (error) {
          console.log(`⚠️  Query failed: ${error.message}`);
        } else {
          console.log('✅ Query executed successfully');
        }
      } catch (err) {
        console.log(`⚠️  Query error: ${err.message}`);
      }
    }

    // Step 3: Test connection after fixes
    console.log('🔍 Step 3: Testing connection after fixes...');
    const { data: finalTest, error: finalError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (finalError) {
      console.log('❌ Connection still failing:', finalError.message);
      console.log('🔧 Trying alternative approach...');
      
      // Alternative: Try to create the exec_sql function if it doesn't exist
      await createExecSqlFunction();
      
    } else {
      console.log('✅ Connection successful after permission fixes!');
    }

  } catch (error) {
    console.error('❌ Permission fix failed:', error.message);
    console.log('🔧 Trying manual SQL execution...');
    await executeManualSql();
  }
}

async function createExecSqlFunction() {
  console.log('🔧 Creating exec_sql function...');
  
  try {
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION exec_sql(sql text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$;
    `;
    
    // This might fail, but we'll try
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSql });
    
    if (error) {
      console.log('⚠️  Could not create exec_sql function:', error.message);
    } else {
      console.log('✅ exec_sql function created');
    }
  } catch (err) {
    console.log('⚠️  Function creation failed:', err.message);
  }
}

async function executeManualSql() {
  console.log('🔧 Executing manual SQL commands...');
  
  // Try to execute SQL directly through Supabase's SQL editor endpoint
  // This is a fallback approach
  
  const sqlCommands = `
    -- Grant permissions to service_role
    GRANT USAGE ON SCHEMA public TO service_role;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
    GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO service_role;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;
    GRANT CREATE ON SCHEMA public TO service_role;
  `;
  
  console.log('📋 Manual SQL commands to execute in Supabase SQL Editor:');
  console.log('========================================================');
  console.log(sqlCommands);
  console.log('========================================================');
  console.log('');
  console.log('🔗 Go to: https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq/sql/new');
  console.log('📝 Copy and paste the SQL commands above');
  console.log('▶️  Click "Run" to execute them');
}

// Run the permission fix
if (require.main === module) {
  fixPermissions()
    .then(() => {
      console.log('🎉 Permission fix process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Permission fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixPermissions };
