const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function moveVehiclesToCatalog() {
  try {
    console.log('🔄 Moving test vehicles from Vehicle table to vehicle_catalog...');
    
    // Get all vehicles from the Vehicle table
    const vehicles = await prisma.vehicle.findMany({
      select: {
        id: true,
        brand: true,
        model: true,
        year: true,
        engine: true,
        type: true,
        category: true,
        vehicleType: true
      }
    });
    
    console.log(`📊 Found ${vehicles.length} vehicles to move:`);
    vehicles.forEach((v, i) => {
      console.log(`  ${i+1}. ${v.brand || 'Unknown'} ${v.model || 'Unknown'} (${v.year || 'N/A'})`);
    });
    
    if (vehicles.length === 0) {
      console.log('✅ No vehicles to move');
      return;
    }
    
    // Check current vehicle_catalog structure
    const catalogSample = await prisma.vehicleCatalog.findFirst();
    console.log('\n📋 Current vehicle_catalog structure:', catalogSample);
    
    // Transform and insert vehicles into vehicle_catalog
    const catalogEntries = vehicles.map(v => ({
      make: v.brand || 'Unknown',
      model: v.model || 'Unknown',
      year: v.year || '2023',
      engine: v.engine || 'Unknown',
      type: v.type || v.vehicleType || 'motorcycle',
      category: v.category || 'scooter',
      image_url: `https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop`,
      source: 'migrated_test_data', // Required field
      daily_rate: 50000, // Default rate in IDR
      rating: 4.5,
      reviews: 0,
      features: ['Manual'],
      available_units: 1,
      has_insurance: true,
      insurance_price: 200,
      location_city: 'Indonesia',
      is_reference_only: false,
      description: `${v.brand || 'Unknown'} ${v.model || 'Unknown'} - Great for city rides`,
      active: true
    }));
    
    console.log('\n📝 Prepared catalog entries:');
    catalogEntries.forEach((entry, i) => {
      console.log(`  ${i+1}. ${entry.make} ${entry.model} (${entry.year})`);
    });
    
    // Insert into vehicle_catalog (using upsert to avoid duplicates)
    let insertedCount = 0;
    for (const entry of catalogEntries) {
      try {
        const result = await prisma.vehicleCatalog.upsert({
          where: {
            make_model_year: {
              make: entry.make,
              model: entry.model,
              year: entry.year
            }
          },
          update: entry,
          create: entry
        });
        console.log(`✅ Upserted: ${entry.make} ${entry.model}`);
        insertedCount++;
      } catch (error) {
        console.error(`❌ Failed to upsert ${entry.make} ${entry.model}:`, error.message);
      }
    }
    
    console.log(`\n✅ Successfully processed ${insertedCount} vehicles into catalog`);
    
    // Now delete the vehicles from the Vehicle table
    console.log('\n🗑️  Deleting test vehicles from Vehicle table...');
    
    // First delete related records
    const vehicleIds = vehicles.map(v => v.id);
    
    // Delete related bookings first
    const deletedBookings = await prisma.booking.deleteMany({
      where: { vehicleId: { in: vehicleIds } }
    });
    console.log(`   ✅ Deleted ${deletedBookings.count} related bookings`);
    
    // Delete vehicle images
    const deletedImages = await prisma.vehicleImage.deleteMany({
      where: { vehicleId: { in: vehicleIds } }
    });
    console.log(`   ✅ Deleted ${deletedImages.count} vehicle images`);
    
    // Delete other related records
    const deletedReviews = await prisma.review.deleteMany({
      where: { vehicleId: { in: vehicleIds } }
    });
    console.log(`   ✅ Deleted ${deletedReviews.count} reviews`);
    
    const deletedSaved = await prisma.savedVehicle.deleteMany({
      where: { vehicleId: { in: vehicleIds } }
    });
    console.log(`   ✅ Deleted ${deletedSaved.count} saved vehicle records`);
    
    // Finally delete the vehicles
    const deletedVehicles = await prisma.vehicle.deleteMany({
      where: { id: { in: vehicleIds } }
    });
    console.log(`   ✅ Deleted ${deletedVehicles.count} vehicles from Vehicle table`);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Moved ${insertedCount} vehicles to catalog`);
    console.log(`   - Deleted ${deletedVehicles.count} vehicles from Vehicle table`);
    console.log(`   - Cleaned up ${deletedBookings.count} bookings, ${deletedImages.count} images, ${deletedReviews.count} reviews`);
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
moveVehiclesToCatalog()
  .then(() => {
    console.log('✅ Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
