#!/usr/bin/env node

/**
 * Debug Frontend Routing Issue
 */

const { chromium } = require('playwright');

async function debugRouting() {
  console.log('🔍 Debugging Frontend Routing...');
  console.log('================================');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Test 1: Navigate to homepage
    console.log('📋 Test 1: Navigate to homepage...');
    await page.goto('http://localhost:5173/');
    await page.waitForTimeout(2000);
    
    const homeTitle = await page.title();
    console.log(`✅ Homepage title: ${homeTitle}`);

    // Test 2: Navigate to register page
    console.log('📋 Test 2: Navigate to register page...');
    await page.goto('http://localhost:5173/register');
    await page.waitForTimeout(2000);
    
    const registerTitle = await page.title();
    console.log(`✅ Register page title: ${registerTitle}`);

    // Test 3: Check what's actually rendered
    console.log('📋 Test 3: Check page content...');
    const bodyText = await page.textContent('body');
    console.log(`📄 Page content preview: ${bodyText.substring(0, 200)}...`);

    // Test 4: Look for the auth modal
    console.log('📋 Test 4: Look for auth elements...');

    const emailInput = await page.locator('[data-testid="email-input"]').count();
    console.log(`📧 Email input count: ${emailInput}`);

    const passwordInput = await page.locator('[data-testid="password-input"]').count();
    console.log(`🔒 Password input count: ${passwordInput}`);

    const authModal = await page.locator('[role="dialog"]').count();
    console.log(`📱 Modal count: ${authModal}`);

    // Check if inputs are visible
    const emailVisible = await page.locator('[data-testid="email-input"]').isVisible();
    console.log(`📧 Email input visible: ${emailVisible}`);

    const passwordVisible = await page.locator('[data-testid="password-input"]').isVisible();
    console.log(`🔒 Password input visible: ${passwordVisible}`);

    // Check for MUI modal backdrop
    const backdrop = await page.locator('.MuiBackdrop-root').count();
    console.log(`🎭 MUI Backdrop count: ${backdrop}`);

    // Test 5: Check for any error messages
    console.log('📋 Test 5: Check for errors...');
    const errorElements = await page.locator('text=/error|Error|ERROR/').count();
    console.log(`❌ Error elements count: ${errorElements}`);

    // Test 6: Take a screenshot
    console.log('📋 Test 6: Taking screenshot...');
    await page.screenshot({ path: 'debug-register-page.png', fullPage: true });
    console.log('📸 Screenshot saved as debug-register-page.png');

    console.log('');
    console.log('🎉 Debug completed! Check the screenshot for visual inspection.');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    await browser.close();
  }
}

if (require.main === module) {
  debugRouting()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { debugRouting };
