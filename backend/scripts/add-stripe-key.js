const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addStripeKey() {
  try {
    // Get the Stripe key from environment variable
    const stripeKey = process.env.STRIPE_SECRET_KEY;
    
    if (!stripeKey) {
      console.error('❌ STRIPE_SECRET_KEY environment variable not found');
      process.exit(1);
    }

    // Add the Stripe key to the database
    await prisma.apiKey.upsert({
      where: { keyValue: stripeKey },
      update: {
        keyName: 'STRIPE_SECRET_KEY',
        isActive: true,
        updatedAt: new Date()
      },
      create: {
        keyName: 'STRIPE_SECRET_KEY',
        keyValue: stripeKey,
        permissions: ['payment', 'webhook'],
        isActive: true
      }
    });

    console.log('✅ Stripe secret key added to database');

    // Also add webhook secret if available
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (webhookSecret) {
      await prisma.apiKey.upsert({
        where: { keyValue: webhookSecret },
        update: {
          keyName: 'STRIPE_WEBHOOK_SECRET',
          isActive: true,
          updatedAt: new Date()
        },
        create: {
          keyName: 'STRIPE_WEBHOOK_SECRET',
          keyValue: webhookSecret,
          permissions: ['webhook'],
          isActive: true
        }
      });
      console.log('✅ Stripe webhook secret added to database');
    }

  } catch (error) {
    console.error('❌ Error adding Stripe key:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addStripeKey(); 