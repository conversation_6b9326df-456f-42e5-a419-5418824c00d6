#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default URL (replace with your actual Railway backend URL)
HEALTH_CHECK_URL="${1:-https://rentahub-backend-production.up.railway.app/health}"

# Perform health check
echo "🩺 Performing health check on: ${HEALTH_CHECK_URL}"

# Use curl with detailed output
RESPONSE=$(curl -s -w "\n%{http_code}" "${HEALTH_CHECK_URL}")

# Split response and status code
BODY=$(echo "${RESPONSE}" | head -n -1)
STATUS_CODE=$(echo "${RESPONSE}" | tail -n1)

# Check response
if [ "${STATUS_CODE}" -eq 200 ]; then
    echo -e "${GREEN}✅ Health Check Successful${NC}"
    echo -e "${GREEN}Status Code: ${STATUS_CODE}${NC}"
    
    # Pretty print JSON response
    echo "${BODY}" | jq .
else
    echo -e "${RED}❌ Health Check Failed${NC}"
    echo -e "${RED}Status Code: ${STATUS_CODE}${NC}"
    echo "Response Body:"
    echo "${BODY}"
    exit 1
fi 