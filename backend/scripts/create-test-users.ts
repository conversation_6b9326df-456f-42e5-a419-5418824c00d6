import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

async function createTestUsers() {
  console.log('🔐 Creating test users with passwords...')

  try {
    // Create admin user with password
    const adminPassword = await hash('admin123', 10)
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: adminPassword,
        name: 'RentaHub Admin',
        role: 'ADMIN',
        status: 'ACTIVE',
        emailVerified: true
      },
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        name: 'RentaHub Admin',
        role: 'ADMIN',
        status: 'ACTIVE',
        phoneNumber: '+**********',
        emailVerified: true
      }
    })

    // Create provider user with password
    const providerPassword = await hash('provider123', 10)
    const provider = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: providerPassword,
        name: 'Vehicle Provider',
        role: 'PROVIDER',
        status: 'ACTIVE',
        emailVerified: true
      },
      create: {
        email: '<EMAIL>',
        password: providerPassword,
        name: 'Vehicle Provider',
        role: 'PROVIDER',
        status: 'ACTIVE',
        companyName: 'RentaHub Vehicles',
        phoneNumber: '+**********',
        emailVerified: true
      }
    })

    // Create customer user with password
    const customerPassword = await hash('customer123', 10)
    const customer = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: customerPassword,
        name: 'Test Customer',
        role: 'CUSTOMER',
        status: 'ACTIVE',
        emailVerified: true
      },
      create: {
        email: '<EMAIL>',
        password: customerPassword,
        name: 'Test Customer',
        role: 'CUSTOMER',
        status: 'ACTIVE',
        phoneNumber: '+**********',
        emailVerified: true
      }
    })

    console.log('✅ Test users created successfully!')
    console.log('')
    console.log('🔑 Login Credentials:')
    console.log('')
    console.log('👨‍💼 Admin:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    console.log('')
    console.log('🏢 Provider:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: provider123')
    console.log('')
    console.log('👤 Customer:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: customer123')
    console.log('')

  } catch (error) {
    console.error('❌ Error creating test users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUsers() 