const axios = require('axios');
const fs = require('fs');
const path = require('path');

class MonitoringDashboardSetup {
  constructor(config) {
    this.config = config;
  }

  // Sentry Dashboard Setup
  async setupSentryDashboard() {
    if (!this.config.sentry) {
      console.log('Sentry configuration not found');
      return;
    }

    const sentryDashboard = {
      title: 'RentaHub Backend Performance',
      widgets: [
        {
          title: 'Error Rate',
          type: 'line_chart',
          queries: [
            {
              name: 'Errors per Minute',
              fields: ['count(error.unhandled)']
            }
          ]
        },
        {
          title: 'Response Time',
          type: 'line_chart',
          queries: [
            {
              name: 'Avg Response Time',
              fields: ['avg(transaction.duration)']
            }
          ]
        }
      ]
    };

    try {
      const response = await axios.post(
        `https://sentry.io/api/0/organizations/${this.config.sentry.org}/dashboards/`,
        sentryDashboard,
        {
          headers: {
            'Authorization': `Bearer ${this.config.sentry.authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Sentry Dashboard Created:', response.data.id);
    } catch (error) {
      console.error('Failed to create Sentry dashboard:', error.message);
    }
  }

  // Datadog Dashboard Setup
  async setupDatadogDashboard() {
    if (!this.config.datadog) {
      console.log('Datadog configuration not found');
      return;
    }

    const datadogDashboard = {
      title: 'RentaHub Backend Monitoring',
      widgets: [
        {
          definition: {
            title: 'Backend Errors',
            type: 'timeseries',
            requests: [
              {
                q: 'sum:rentahub.backend.errors{*}'
              }
            ]
          }
        },
        {
          definition: {
            title: 'Request Latency',
            type: 'timeseries',
            requests: [
              {
                q: 'avg:rentahub.backend.request.latency{*}'
              }
            ]
          }
        }
      ]
    };

    try {
      const response = await axios.post(
        'https://api.datadoghq.com/api/v1/dashboard',
        datadogDashboard,
        {
          headers: {
            'DD-API-KEY': this.config.datadog.apiKey,
            'DD-APPLICATION-KEY': this.config.datadog.appKey,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Datadog Dashboard Created:', response.data.id);
    } catch (error) {
      console.error('Failed to create Datadog dashboard:', error.message);
    }
  }

  // New Relic Dashboard Setup
  async setupNewRelicDashboard() {
    if (!this.config.newRelic) {
      console.log('New Relic configuration not found');
      return;
    }

    const newRelicDashboard = {
      dashboard: {
        title: 'RentaHub Backend Performance',
        widgets: [
          {
            title: 'Error Rate',
            visualization: 'line_chart',
            nrql: 'FROM Transaction SELECT count(*) WHERE error = true TIMESERIES'
          },
          {
            title: 'Response Time',
            visualization: 'line_chart',
            nrql: 'FROM Transaction SELECT average(duration) TIMESERIES'
          }
        ]
      }
    };

    try {
      const response = await axios.post(
        'https://api.newrelic.com/v2/dashboards.json',
        newRelicDashboard,
        {
          headers: {
            'X-Api-Key': this.config.newRelic.apiKey,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('New Relic Dashboard Created:', response.data.dashboard.id);
    } catch (error) {
      console.error('Failed to create New Relic dashboard:', error.message);
    }
  }

  // Main execution method
  async setup() {
    await this.setupSentryDashboard();
    await this.setupDatadogDashboard();
    await this.setupNewRelicDashboard();
  }
}

// Configuration loading
const loadConfig = () => {
  const configPath = path.resolve(__dirname, '../.env.production');
  const config = {};

  try {
    const envContents = fs.readFileSync(configPath, 'utf-8');
    envContents.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        config[key.trim()] = value.trim();
      }
    });

    return {
      sentry: {
        org: config.SENTRY_ORG,
        authToken: config.SENTRY_AUTH_TOKEN
      },
      datadog: {
        apiKey: config.DATADOG_API_KEY,
        appKey: config.DATADOG_APP_KEY
      },
      newRelic: {
        apiKey: config.NEW_RELIC_API_KEY
      }
    };
  } catch (error) {
    console.error('Failed to load configuration:', error);
    return {};
  }
};

// Run the setup
const config = loadConfig();
const dashboardSetup = new MonitoringDashboardSetup(config);
dashboardSetup.setup(); 