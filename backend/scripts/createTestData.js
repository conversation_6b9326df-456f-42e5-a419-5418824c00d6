const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('🔄 Creating test bookings and vehicles...');
    
    const providerId = 'cmd7hwwhm0000xcxshowpwrwd';
    
    // Create a test customer
    let customer;
    try {
      customer = await prisma.user.findFirst({
        where: { role: 'CUSTOMER' }
      });
      
      if (!customer) {
        customer = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: '<PERSON>',
            phoneNumber: '+91-**********',
            role: 'CUSTOMER',
            emailVerified: true,
            status: 'ACTIVE'
          }
        });
        console.log('✅ Test customer created:', customer.id);
      } else {
        console.log('✅ Using existing customer:', customer.id);
      }
    } catch (error) {
      console.log('⚠️ Customer creation error:', error.message);
      return;
    }
    
    // Create test vehicles in the Vehicle table (for bookings)
    let vehicles = await prisma.vehicle.findMany({
      where: { providerId }
    });

    if (vehicles.length === 0) {
      try {
        const vehicle1 = await prisma.vehicle.create({
          data: {
            providerId: providerId,
            vehicleType: 'Scooter',
            dailyRate: 300,
            weeklyRate: 1800,
            monthlyRate: 6000,
            brand: 'Honda',
            model: 'Activa 6G',
            year: '2023',
            category: 'Scooter',
            location_city: 'Mumbai',
            depositAmount: 1000,
            availableUnits: 1
          }
        });

        const vehicle2 = await prisma.vehicle.create({
          data: {
            providerId: providerId,
            vehicleType: 'Motorcycle',
            dailyRate: 500,
            weeklyRate: 3000,
            monthlyRate: 10000,
            brand: 'Yamaha',
            model: 'FZ-S',
            year: '2022',
            category: 'Motorcycle',
            location_city: 'Mumbai',
            depositAmount: 2000,
            availableUnits: 1
          }
        });

        vehicles = [vehicle1, vehicle2];
        console.log('✅ Test vehicles created:', vehicles.length);
      } catch (error) {
        console.log('⚠️ Vehicle creation error:', error.message);
      }
    } else {
      console.log('✅ Found existing vehicles:', vehicles.length);
    }
    
    // Create test bookings if we have customer and vehicles
    if (customer && vehicles.length > 0) {
      try {
        const booking1 = await prisma.booking.create({
          data: {
            userId: customer.id,
            providerId: providerId,
            vehicleId: vehicles[0].id,
            startDate: new Date('2024-01-15'),
            endDate: new Date('2024-01-17'),
            totalPrice: 900,
            status: 'COMPLETED'
          }
        });
        
        const booking2 = await prisma.booking.create({
          data: {
            userId: customer.id,
            providerId: providerId,
            vehicleId: vehicles[0].id,
            startDate: new Date('2024-01-20'),
            endDate: new Date('2024-01-22'),
            totalPrice: 1500,
            status: 'PENDING'
          }
        });
        
        console.log('✅ Test bookings created');
      } catch (error) {
        console.log('⚠️ Booking creation error:', error.message);
      }
    }
    
    console.log('\n🎉 Test data setup complete!');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
