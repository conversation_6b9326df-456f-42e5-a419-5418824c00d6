#!/bin/bash

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Test stages
run_linter() {
    echo -e "${YELLOW}Running ESLint...${NC}"
    npx eslint 'src/**/*.{ts,tsx}'
}

run_type_check() {
    echo -e "${YELLOW}Running TypeScript Type Check...${NC}"
    npx tsc --noEmit
}

run_unit_tests() {
    echo -e "${YELLOW}Running Unit Tests...${NC}"
    NODE_ENV=test npx jest __tests__ --coverage
}

run_integration_tests() {
    echo -e "${YELLOW}Running Integration Tests...${NC}"
    NODE_ENV=test npx jest __integration__ --coverage
}

run_e2e_tests() {
    echo -e "${YELLOW}Running End-to-End Tests...${NC}"
    # Placeholder for E2E test command
    echo "E2E tests not configured yet"
}

generate_coverage_report() {
    echo -e "${YELLOW}Generating Coverage Report...${NC}"
    npx istanbul report
}

main() {
    # Ensure we're in the right directory
    cd "$(dirname "$0")/.."

    # Run tests in sequence
    run_linter
    LINTER_EXIT=$?

    run_type_check
    TYPE_CHECK_EXIT=$?

    run_unit_tests
    UNIT_TEST_EXIT=$?

    run_integration_tests
    INTEGRATION_TEST_EXIT=$?

    run_e2e_tests
    E2E_TEST_EXIT=$?

    generate_coverage_report

    # Check for any failures
    if [ $LINTER_EXIT -ne 0 ] || 
       [ $TYPE_CHECK_EXIT -ne 0 ] || 
       [ $UNIT_TEST_EXIT -ne 0 ] || 
       [ $INTEGRATION_TEST_EXIT -ne 0 ] || 
       [ $E2E_TEST_EXIT -ne 0 ]; then
        echo -e "${RED}❌ Some tests failed or there were linting/type errors${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ All tests passed successfully!${NC}"
}

# Run the main function
main
