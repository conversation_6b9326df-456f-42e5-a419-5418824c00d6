#!/usr/bin/env node

/**
 * Apply Supabase permissions directly using SQL commands
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Create Supabase client with service role
const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyPermissions() {
  console.log('🔧 Applying Supabase Permissions Directly...');
  console.log('==========================================');

  try {
    // First create the exec_sql function
    console.log('🔧 Creating exec_sql function...');
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
          EXECUTE sql;
      END;
      $$;
    `;

    const { error: funcError } = await supabase.rpc('exec', { sql: createFunctionSql });
    if (funcError) {
      console.log('⚠️  Function creation failed, trying alternative approach...');
      
      // Try using direct SQL execution via REST API
      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
          'apikey': SERVICE_ROLE_KEY
        },
        body: JSON.stringify({ sql: createFunctionSql })
      });

      if (!response.ok) {
        console.log('⚠️  REST API approach also failed, using manual SQL...');
        
        // Apply permissions manually using individual SQL statements
        const permissionsSql = `
-- Grant basic schema access to service_role
GRANT USAGE ON SCHEMA public TO service_role;
GRANT CREATE ON SCHEMA public TO service_role;

-- Grant all privileges on existing tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO service_role;

-- Ensure service_role can bypass RLS
ALTER ROLE service_role SET row_security = off;
GRANT ALL ON SCHEMA public TO service_role;

-- Grant permissions to anon role for frontend access
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;
GRANT INSERT ON ALL TABLES IN SCHEMA public TO anon;
GRANT UPDATE ON ALL TABLES IN SCHEMA public TO anon;
GRANT DELETE ON ALL TABLES IN SCHEMA public TO anon;

-- Grant permissions to authenticated role
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT INSERT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT UPDATE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT DELETE ON ALL TABLES IN SCHEMA public TO authenticated;

-- Set default privileges for anon role
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO anon;

-- Set default privileges for authenticated role
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO authenticated;
        `;

        console.log('📝 Applying permissions via manual SQL...');
        console.log('🔗 Please execute this SQL in Supabase SQL Editor:');
        console.log('https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq/sql/new');
        console.log('\n' + permissionsSql);
        
        return;
      }
    }

    console.log('✅ Permissions applied successfully!');
    
    // Test the connection
    console.log('🔍 Testing connection...');
    const { data: testData, error: testError } = await supabase
      .from('User')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.log('❌ Connection test failed:', testError.message);
    } else {
      console.log('✅ Connection test successful!');
    }
    
  } catch (error) {
    console.error('❌ Failed to apply permissions:', error);
  }
}

// Run the script
applyPermissions();
