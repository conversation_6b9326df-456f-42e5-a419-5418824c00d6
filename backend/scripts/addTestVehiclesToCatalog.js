const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addTestVehiclesToCatalog() {
  try {
    console.log('🔄 Adding test vehicles to vehicle_catalog...');
    
    // The 3 vehicles that were deleted from Vehicle table
    const testVehicles = [
      {
        make: 'Honda',
        model: 'Activa 6G',
        year: '2023',
        engine: '109.51cc',
        type: 'scooter',
        category: 'scooter_motorcycle',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop',
        source: 'migrated_test_data',
        daily_rate: 45000,
        rating: 4.5,
        reviews: 12,
        features: ['Automatic', 'LED Headlight', 'Digital Display'],
        available_units: 2,
        has_insurance: true,
        insurance_price: 150,
        location_city: 'Jakarta',
        is_reference_only: false
      },
      {
        make: 'Yamaha',
        model: 'FZ-S',
        year: '2022',
        engine: '149cc',
        type: 'motorcycle',
        category: 'street_motorcycle',
        image_url: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
        source: 'migrated_test_data',
        daily_rate: 65000,
        rating: 4.7,
        reviews: 18,
        features: ['Manual', 'ABS', 'LED Headlight', 'Digital Display'],
        available_units: 1,
        has_insurance: true,
        insurance_price: 250,
        location_city: 'Bandung',
        is_reference_only: false
      },
      {
        make: 'Hero',
        model: 'Sprint',
        year: '2023',
        engine: 'Electric',
        type: 'bicycle',
        category: 'electric_bicycle',
        image_url: 'https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=800&h=600&fit=crop',
        source: 'migrated_test_data',
        daily_rate: 25000,
        rating: 4.2,
        reviews: 8,
        features: ['Electric', 'Lightweight', 'Foldable'],
        available_units: 3,
        has_insurance: false,
        insurance_price: 0,
        location_city: 'Surabaya',
        is_reference_only: false
      }
    ];
    
    console.log(`📝 Adding ${testVehicles.length} test vehicles to catalog:`);
    testVehicles.forEach((v, i) => {
      console.log(`  ${i+1}. ${v.make} ${v.model} (${v.year}) - ${v.type}`);
    });
    
    let addedCount = 0;
    for (const vehicle of testVehicles) {
      try {
        const result = await prisma.vehicleCatalog.upsert({
          where: {
            make_model_year: {
              make: vehicle.make,
              model: vehicle.model,
              year: vehicle.year
            }
          },
          update: vehicle,
          create: vehicle
        });
        console.log(`✅ Added: ${vehicle.make} ${vehicle.model} (${vehicle.year})`);
        addedCount++;
      } catch (error) {
        console.error(`❌ Failed to add ${vehicle.make} ${vehicle.model}:`, error.message);
      }
    }
    
    console.log(`\n🎉 Successfully added ${addedCount} vehicles to catalog!`);
    
    // Verify the total count
    const totalCount = await prisma.vehicleCatalog.count();
    console.log(`📊 Total vehicles in catalog: ${totalCount}`);
    
  } catch (error) {
    console.error('❌ Error adding vehicles to catalog:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addTestVehiclesToCatalog()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
