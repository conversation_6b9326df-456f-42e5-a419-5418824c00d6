#!/bin/bash

# Sentry CLI Installation
npm install -g @sentry/cli

# Interactive Sentry Project Setup
echo "🚀 RentaHub Backend Sentry Setup"
echo "Please ensure you have a Sentry account (sentry.io)"

# Prompt for Sentry details
read -p "Enter your Sentry organization slug: " ORG_SLUG
read -p "Enter your Sentry project slug: " PROJECT_SLUG
read -p "Enter your Sentry auth token: " AUTH_TOKEN

# Create Sentry configuration
sentry-cli --url https://sentry.io/ login --auth-token "$AUTH_TOKEN"

# Create .sentryclirc configuration file
cat > .sentryclirc << EOL
[defaults]
org = $ORG_SLUG
project = $PROJECT_SLUG
EOL

# Get the DSN
DSN=$(sentry-cli releases propose-version)

# Update environment configuration
echo "SENTRY_DSN=$DSN" >> .env.production

echo "✅ Sentry setup complete!"
echo "DSN has been added to .env.production" 