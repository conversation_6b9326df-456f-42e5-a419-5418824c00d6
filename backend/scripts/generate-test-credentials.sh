#!/bin/bash

# Stripe Test Credentials Generator and Manager

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to generate a secure random string
generate_secret() {
    openssl rand -base64 32 | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1
}

# Function to validate Stripe API key format
validate_stripe_key() {
    local key=$1
    if [[ $key =~ ^sk_test_[a-zA-Z0-9]+$ ]]; then
        return 0
    else
        return 1
    fi
}

# Main script
main() {
    echo -e "${YELLOW}Stripe Test Credentials Generator${NC}"
    
    # Generate Stripe Secret Key
    while true; do
        read -p "Enter Stripe Test Secret Key (or press Enter to generate): " STRIPE_SECRET_KEY
        
        if [ -z "$STRIPE_SECRET_KEY" ]; then
            STRIPE_SECRET_KEY=$(generate_secret)
            echo -e "${GREEN}Generated Stripe Secret Key: $STRIPE_SECRET_KEY${NC}"
        fi
        
        if validate_stripe_key "$STRIPE_SECRET_KEY"; then
            break
        else
            echo -e "${RED}Invalid Stripe Secret Key format. Must start with sk_test_${NC}"
        fi
    done

    # Generate Stripe Publishable Key
    while true; do
        read -p "Enter Stripe Test Publishable Key (or press Enter to generate): " STRIPE_PUBLISHABLE_KEY
        
        if [ -z "$STRIPE_PUBLISHABLE_KEY" ]; then
            STRIPE_PUBLISHABLE_KEY=$(generate_secret)
            echo -e "${GREEN}Generated Stripe Publishable Key: $STRIPE_PUBLISHABLE_KEY${NC}"
        fi
        
        break
    done

    # Create .env.test file
    cat > .env.test << EOL
# Stripe Test Credentials
STRIPE_TEST_SECRET_KEY=$STRIPE_SECRET_KEY
STRIPE_TEST_PUBLISHABLE_KEY=$STRIPE_PUBLISHABLE_KEY

# Test Database Configuration
DATABASE_TEST_URL="postgresql://postgres:your_test_password@localhost:5432/rentahub_test?schema=public"

# Test Environment Settings
NODE_ENV=test
LOG_LEVEL=debug

# Additional Test Configurations
TEST_USER_ID=test_user_123
TEST_PROVIDER_ID=test_provider_123
TEST_VEHICLE_ID=test_vehicle_123

# Monitoring and Logging
MONITORING_DISABLED=true
SENTRY_DSN_TEST=""
EOL

    echo -e "${GREEN}✓ .env.test file generated successfully${NC}"
    echo -e "${YELLOW}IMPORTANT: Keep these credentials secure and never commit them to version control!${NC}"

    # Provide GitHub CLI command suggestion
    echo -e "\n${YELLOW}To set GitHub Secrets, use the GitHub CLI:${NC}"
    echo "gh secret set STRIPE_TEST_SECRET_KEY -b\"$STRIPE_SECRET_KEY\""
    echo "gh secret set STRIPE_TEST_PUBLISHABLE_KEY -b\"$STRIPE_PUBLISHABLE_KEY\""
}

# Run the main function
main 