import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';

interface ServiceDocumentation {
  serviceName: string;
  description: string;
  methods: MethodDocumentation[];
}

interface MethodDocumentation {
  name: string;
  description: string;
  parameters: ParameterDocumentation[];
  returnType: string;
}

interface ParameterDocumentation {
  name: string;
  type: string;
  description: string;
  optional: boolean;
}

class DocumentationGenerator {
  private sourceFiles: ts.SourceFile[] = [];

  constructor(private servicesDir: string) {}

  /**
   * Load TypeScript source files
   */
  loadSourceFiles(): void {
    const files = this.getTypescriptFiles(this.servicesDir);
    this.sourceFiles = files.map(file => 
      ts.createSourceFile(
        file, 
        fs.readFileSync(file).toString(), 
        ts.ScriptTarget.Latest, 
        true
      )
    );
  }

  /**
   * Get all TypeScript files in a directory
   */
  private getTypescriptFiles(dir: string): string[] {
    let files: string[] = [];
    const items = fs.readdirSync(dir);

    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        files = files.concat(this.getTypescriptFiles(fullPath));
      } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
        files.push(fullPath);
      }
    });

    return files;
  }

  /**
   * Extract JSDoc comments
   */
  private extractJSDocComment(node: ts.Node): string | null {
    const comments = ts.getLeadingCommentRanges(
      node.getSourceFile().getFullText(), 
      node.pos
    );

    if (comments) {
      const comment = comments[0];
      const commentText = node.getSourceFile().getFullText().slice(
        comment.pos, 
        comment.end
      );
      
      // Remove JSDoc markers and clean up
      return commentText
        .replace(/\/\*\*|\*\/|\*/g, '')
        .trim();
    }

    return null;
  }

  /**
   * Generate documentation for a service
   */
  generateServiceDocumentation(sourceFile: ts.SourceFile): ServiceDocumentation[] {
    const services: ServiceDocumentation[] = [];

    function visit(node: ts.Node) {
      // Look for class declarations
      if (ts.isClassDeclaration(node) && node.name) {
        const serviceName = node.name.getText();
        const description = this.extractJSDocComment(node) || 'No description available';
        const methods: MethodDocumentation[] = [];

        // Iterate through class methods
        node.members.forEach(member => {
          if (ts.isMethodDeclaration(member) && member.name) {
            const methodName = member.name.getText();
            const methodDescription = this.extractJSDocComment(member) || 'No method description';
            
            // Extract parameters
            const parameters: ParameterDocumentation[] = member.parameters.map(param => ({
              name: param.name.getText(),
              type: param.type ? param.type.getText() : 'any',
              description: this.extractParameterDescription(member, param.name.getText()) || '',
              optional: !!param.questionToken
            }));

            // Get return type
            const returnType = member.type 
              ? member.type.getText() 
              : 'void';

            methods.push({
              name: methodName,
              description: methodDescription,
              parameters,
              returnType
            });
          }
        });

        services.push({
          serviceName,
          description,
          methods
        });
      }

      ts.forEachChild(node, visit.bind(this));
    }

    visit.call(this, sourceFile);
    return services;
  }

  /**
   * Extract parameter description from JSDoc
   */
  private extractParameterDescription(
    method: ts.MethodDeclaration, 
    paramName: string
  ): string | null {
    const comment = this.extractJSDocComment(method);
    if (comment) {
      const paramMatch = comment.match(
        new RegExp(`@param\\s+{[^}]*}\\s*${paramName}\\s*(.*)`, 'i')
      );
      return paramMatch ? paramMatch[1].trim() : null;
    }
    return null;
  }

  /**
   * Generate Markdown documentation
   */
  generateMarkdownDocumentation(services: ServiceDocumentation[]): string {
    let markdown = '# RentaHub Service Documentation\n\n';

    services.forEach(service => {
      markdown += `## ${service.serviceName}\n\n`;
      markdown += `${service.description}\n\n`;

      service.methods.forEach(method => {
        markdown += `### ${method.name}\n\n`;
        markdown += `${method.description}\n\n`;
        
        markdown += '#### Parameters:\n\n';
        method.parameters.forEach(param => {
          markdown += `- \`${param.name}\` (${param.type})${param.optional ? ' (optional)' : ''}: ${param.description}\n`;
        });

        markdown += `\n#### Returns: \`${method.returnType}\`\n\n`;
      });
    });

    return markdown;
  }

  /**
   * Generate documentation for all services
   */
  generateDocs(): void {
    this.loadSourceFiles();

    const allServiceDocs: ServiceDocumentation[] = [];

    this.sourceFiles.forEach(sourceFile => {
      const serviceDocs = this.generateServiceDocumentation(sourceFile);
      allServiceDocs.push(...serviceDocs);
    });

    // Generate Markdown
    const markdownDocs = this.generateMarkdownDocumentation(allServiceDocs);

    // Write to file
    const outputDir = path.join(this.servicesDir, '..', 'docs');
    fs.mkdirSync(outputDir, { recursive: true });
    fs.writeFileSync(
      path.join(outputDir, 'services.md'), 
      markdownDocs
    );

    console.log('Documentation generated successfully!');
  }
}

// Run documentation generation
const docGenerator = new DocumentationGenerator(
  path.join(__dirname, '..', 'src', 'services')
);
docGenerator.generateDocs(); 