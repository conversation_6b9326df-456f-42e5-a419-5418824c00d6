#!/usr/bin/env tsx

/**
 * Environment Validation Script
 * 
 * This script validates that all required environment variables are properly
 * configured for the current environment.
 */

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function success(message: string) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function error(message: string) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function warning(message: string) {
  console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function info(message: string) {
  console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

interface ValidationResult {
  category: string;
  passed: boolean;
  errors: string[];
  warnings: string[];
}

class EnvironmentValidator {
  private results: ValidationResult[] = [];
  private environment: string;

  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
  }

  async validate(): Promise<boolean> {
    console.log(`🔍 Validating ${this.environment.toUpperCase()} environment configuration...\n`);

    // Run all validation checks
    this.validateCoreSettings();
    this.validateDatabaseConfig();
    this.validateAuthConfig();
    this.validatePaymentConfig();
    this.validateEmailConfig();
    this.validateExternalServices();
    this.validateSecuritySettings();
    await this.validateConnections();

    // Display results
    this.displayResults();

    // Return overall success
    return this.results.every(result => result.passed);
  }

  private validateCoreSettings() {
    const result: ValidationResult = {
      category: 'Core Application Settings',
      passed: true,
      errors: [],
      warnings: []
    };

    const requiredVars = ['NODE_ENV', 'PORT'];
    const optionalVars = ['APP_NAME', 'APP_VERSION'];

    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        result.errors.push(`${varName} is required`);
        result.passed = false;
      }
    });

    optionalVars.forEach(varName => {
      if (!process.env[varName]) {
        result.warnings.push(`${varName} is not set (optional)`);
      }
    });

    // Validate PORT is a number
    const port = process.env.PORT;
    if (port && (isNaN(Number(port)) || Number(port) < 1 || Number(port) > 65535)) {
      result.errors.push('PORT must be a valid port number (1-65535)');
      result.passed = false;
    }

    this.results.push(result);
  }

  private validateDatabaseConfig() {
    const result: ValidationResult = {
      category: 'Database Configuration',
      passed: true,
      errors: [],
      warnings: []
    };

    const requiredVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
    const optionalVars = ['SUPABASE_ANON_KEY'];

    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        result.errors.push(`${varName} is required`);
        result.passed = false;
      }
    });

    optionalVars.forEach(varName => {
      if (!process.env[varName]) {
        result.warnings.push(`${varName} is not set (may be needed for frontend)`);
      }
    });

    // Validate Supabase URL format
    const supabaseUrl = process.env.SUPABASE_URL;
    if (supabaseUrl && !supabaseUrl.match(/^https:\/\/[a-z0-9]+\.supabase\.co$/)) {
      result.warnings.push('SUPABASE_URL format may be incorrect');
    }

    this.results.push(result);
  }

  private validateAuthConfig() {
    const result: ValidationResult = {
      category: 'Authentication Configuration',
      passed: true,
      errors: [],
      warnings: []
    };

    const requiredVars = ['JWT_SECRET'];
    const optionalVars = ['JWT_EXPIRATION', 'JWT_REFRESH_EXPIRATION', 'BCRYPT_ROUNDS'];

    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        result.errors.push(`${varName} is required`);
        result.passed = false;
      }
    });

    // Validate JWT_SECRET strength
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret) {
      if (jwtSecret.length < 32) {
        result.errors.push('JWT_SECRET must be at least 32 characters long');
        result.passed = false;
      }
      if (jwtSecret === 'your_super_secret_jwt_key_here_minimum_32_characters') {
        result.errors.push('JWT_SECRET must be changed from default value');
        result.passed = false;
      }
    }

    // Validate BCRYPT_ROUNDS
    const bcryptRounds = process.env.BCRYPT_ROUNDS;
    if (bcryptRounds) {
      const rounds = Number(bcryptRounds);
      if (isNaN(rounds) || rounds < 8 || rounds > 15) {
        result.warnings.push('BCRYPT_ROUNDS should be between 8 and 15');
      }
    }

    this.results.push(result);
  }

  private validatePaymentConfig() {
    const result: ValidationResult = {
      category: 'Payment Configuration',
      passed: true,
      errors: [],
      warnings: []
    };

    const stripeVars = ['STRIPE_SECRET_KEY', 'STRIPE_PUBLISHABLE_KEY', 'STRIPE_WEBHOOK_SECRET'];
    
    // Check if any Stripe variables are set
    const hasStripeConfig = stripeVars.some(varName => process.env[varName]);

    if (hasStripeConfig) {
      stripeVars.forEach(varName => {
        if (!process.env[varName]) {
          result.errors.push(`${varName} is required when Stripe is configured`);
          result.passed = false;
        }
      });

      // Validate Stripe key formats
      const secretKey = process.env.STRIPE_SECRET_KEY;
      const publishableKey = process.env.STRIPE_PUBLISHABLE_KEY;

      if (secretKey && !secretKey.startsWith('sk_')) {
        result.errors.push('STRIPE_SECRET_KEY must start with "sk_"');
        result.passed = false;
      }

      if (publishableKey && !publishableKey.startsWith('pk_')) {
        result.errors.push('STRIPE_PUBLISHABLE_KEY must start with "pk_"');
        result.passed = false;
      }

      // Check for test vs live keys in production
      if (this.environment === 'production') {
        if (secretKey && secretKey.includes('_test_')) {
          result.warnings.push('Using Stripe test keys in production environment');
        }
      } else {
        if (secretKey && !secretKey.includes('_test_')) {
          result.warnings.push('Using Stripe live keys in non-production environment');
        }
      }
    } else {
      result.warnings.push('Stripe payment processing is not configured');
    }

    this.results.push(result);
  }

  private validateEmailConfig() {
    const result: ValidationResult = {
      category: 'Email Configuration',
      passed: true,
      errors: [],
      warnings: []
    };

    const emailVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS'];
    const hasEmailConfig = emailVars.some(varName => process.env[varName]);

    if (hasEmailConfig) {
      emailVars.forEach(varName => {
        if (!process.env[varName]) {
          result.warnings.push(`${varName} is not set (may be required for email functionality)`);
        }
      });

      // Validate SMTP_PORT
      const smtpPort = process.env.SMTP_PORT;
      if (smtpPort && (isNaN(Number(smtpPort)) || Number(smtpPort) < 1 || Number(smtpPort) > 65535)) {
        result.errors.push('SMTP_PORT must be a valid port number');
        result.passed = false;
      }
    } else {
      result.warnings.push('Email functionality is not configured');
    }

    this.results.push(result);
  }

  private validateExternalServices() {
    const result: ValidationResult = {
      category: 'External Services',
      passed: true,
      errors: [],
      warnings: []
    };

    const optionalServices = [
      'GOOGLE_MAPS_API_KEY',
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN',
      'SENTRY_DSN'
    ];

    optionalServices.forEach(varName => {
      if (!process.env[varName]) {
        result.warnings.push(`${varName} is not configured (optional service)`);
      }
    });

    this.results.push(result);
  }

  private validateSecuritySettings() {
    const result: ValidationResult = {
      category: 'Security Settings',
      passed: true,
      errors: [],
      warnings: []
    };

    // Check CORS configuration
    if (!process.env.CORS_ORIGIN) {
      result.warnings.push('CORS_ORIGIN is not set (may cause frontend connection issues)');
    }

    // Check rate limiting
    const rateLimitVars = ['RATE_LIMIT_WINDOW_MS', 'RATE_LIMIT_MAX_REQUESTS'];
    rateLimitVars.forEach(varName => {
      if (!process.env[varName]) {
        result.warnings.push(`${varName} is not set (using default rate limiting)`);
      }
    });

    // Production-specific security checks
    if (this.environment === 'production') {
      if (process.env.DEBUG) {
        result.warnings.push('DEBUG is enabled in production (may expose sensitive information)');
      }

      if (process.env.VERBOSE_LOGGING === 'true') {
        result.warnings.push('Verbose logging is enabled in production');
      }

      if (!process.env.SENTRY_DSN) {
        result.warnings.push('Error tracking (Sentry) is not configured for production');
      }
    }

    this.results.push(result);
  }

  private async validateConnections() {
    const result: ValidationResult = {
      category: 'Connection Tests',
      passed: true,
      errors: [],
      warnings: []
    };

    // Test Supabase connection
    try {
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (supabaseUrl && supabaseKey) {
        const supabase = createClient(supabaseUrl, supabaseKey);
        const { error } = await supabase.from('users').select('count').limit(1);
        
        if (error) {
          result.errors.push(`Supabase connection failed: ${error.message}`);
          result.passed = false;
        } else {
          success('Supabase database connection successful');
        }
      }
    } catch (error) {
      result.errors.push(`Supabase connection test failed: ${error}`);
      result.passed = false;
    }

    this.results.push(result);
  }

  private displayResults() {
    console.log('\n📊 Environment Validation Results');
    console.log('='.repeat(50));

    let totalErrors = 0;
    let totalWarnings = 0;

    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`\n${status} ${result.category}`);

      if (result.errors.length > 0) {
        result.errors.forEach(err => error(`  ${err}`));
        totalErrors += result.errors.length;
      }

      if (result.warnings.length > 0) {
        result.warnings.forEach(warn => warning(`  ${warn}`));
        totalWarnings += result.warnings.length;
      }

      if (result.passed && result.errors.length === 0 && result.warnings.length === 0) {
        success('  All checks passed');
      }
    });

    console.log('\n📈 Summary');
    console.log('='.repeat(20));
    console.log(`Environment: ${this.environment.toUpperCase()}`);
    console.log(`Total Errors: ${totalErrors}`);
    console.log(`Total Warnings: ${totalWarnings}`);

    if (totalErrors === 0) {
      success('Environment validation passed!');
    } else {
      error(`Environment validation failed with ${totalErrors} error(s)`);
    }

    if (totalWarnings > 0) {
      warning(`${totalWarnings} warning(s) found - review recommended`);
    }
  }
}

// Run validation if called directly
async function main() {
  const validator = new EnvironmentValidator();
  const success = await validator.validate();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

export default EnvironmentValidator;
