#!/bin/bash

# SSL Certificate Setup for RentaHub Backend

# Ensure script is run as root
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root" 
   exit 1
fi

# Domain configuration
DOMAIN="api.rentahub.com"
EMAIL="<EMAIL>"

# Install Certbot
if [[ $(uname) == "Darwin" ]]; then
    # macOS (using Homebrew)
    brew install certbot
elif [[ $(uname) == "Linux" ]]; then
    # Linux (Ubuntu/Debian)
    sudo apt-get update
    sudo apt-get install -y certbot python3-certbot-nginx
else
    echo "Unsupported operating system"
    exit 1
fi

# Obtain SSL Certificate
certbot certonly \
    --standalone \
    --preferred-challenges http \
    --agree-tos \
    --no-eff-email \
    -d "$DOMAIN" \
    -m "$EMAIL"

# Create renewal hook to reload Nginx
mkdir -p /etc/letsencrypt/renewal-hooks/post
cat > /etc/letsencrypt/renewal-hooks/post/nginx-reload.sh << EOL
#!/bin/bash
nginx -s reload
EOL
chmod +x /etc/letsencrypt/renewal-hooks/post/nginx-reload.sh

# Set up automatic renewal
(crontab -l 2>/dev/null; echo "0 0,12 * * * certbot renew --quiet") | crontab -

echo "✅ SSL Certificate Setup Complete for $DOMAIN"
echo "Certificate located at: /etc/letsencrypt/live/$DOMAIN"
echo "Automatic renewal configured" 