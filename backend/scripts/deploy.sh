#!/bin/bash
set -e

# Deployment Script for RentaHub Backend

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="rentahub-backend"
DEPLOY_DIR="/var/www/${PROJECT_NAME}"
REPO_URL="**************:your-org/rentahub-backend.git"
BRANCH="${1:-main}"

# Logging function
log() {
    echo -e "${GREEN}[DEPLOY] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# Validate prerequisites
validate_prerequisites() {
    log "Validating deployment prerequisites..."
    
    # Check required tools
    for tool in git npm pm2 node; do
        if ! command -v "$tool" &> /dev/null; then
            error "$tool is not installed"
        fi
    done

    # Check Node.js version
    NODE_VERSION=$(node --version)
    if [[ ! "$NODE_VERSION" =~ ^v(18|20)\. ]]; then
        error "Node.js version must be 18.x or 20.x. Current version: $NODE_VERSION"
    fi
}

# Prepare deployment directory
prepare_deployment_directory() {
    log "Preparing deployment directory..."
    
    if [ ! -d "$DEPLOY_DIR" ]; then
        mkdir -p "$DEPLOY_DIR"
    fi

    cd "$DEPLOY_DIR"
}

# Clone or update repository
update_repository() {
    log "Updating repository..."
    
    if [ ! -d ".git" ]; then
        git clone "$REPO_URL" .
    fi

    git fetch origin
    git checkout "$BRANCH"
    git pull origin "$BRANCH"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    npm ci --prefer-offline --no-audit
    npm run build
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    npm run migrate:up
}

# Configure environment
configure_environment() {
    log "Configuring environment..."
    
    # Copy environment file if not exists
    if [ ! -f ".env.production" ]; then
        cp .env.example .env.production
        error "Please update .env.production with your configuration"
    fi
}

# Start application with PM2
start_application() {
    log "Starting application with PM2..."
    
    pm2 delete "$PROJECT_NAME" || true
    pm2 start ecosystem.config.js --env production
    pm2 save
}

# Health check
run_health_check() {
    log "Running health check..."
    
    # Wait for application to start
    sleep 10
    
    # Perform health check
    HEALTH_CHECK=$(curl -s http://localhost:3000/health)
    if [[ "$HEALTH_CHECK" != *"healthy"* ]]; then
        error "Health check failed"
    fi
}

# Main deployment workflow
main() {
    validate_prerequisites
    prepare_deployment_directory
    update_repository
    configure_environment
    install_dependencies
    run_migrations
    start_application
    run_health_check

    log "Deployment completed successfully!"
}

# Execute main function
main

# Optional: Send deployment notification
# curl -X POST https://your-monitoring-service.com/deploy \
#      -H "Content-Type: application/json" \
#      -d '{"app":"'"$PROJECT_NAME"'","status":"success"}' 