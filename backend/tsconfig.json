{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "outDir": "./dist", "baseUrl": ".", "allowSyntheticDefaultImports": true, "noImplicitAny": false, "strictNullChecks": false, "allowJs": true, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "express", "jest"]}, "include": ["src/**/*", "src/types/**/*.d.ts"], "exclude": ["node_modules", "src/generated/**/*", "__tests__/**/*", "**/__tests__/**/*", "**/__integration__/**/*", "**/*.test.ts", "**/*.spec.ts", "src/test-*.ts"]}