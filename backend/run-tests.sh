#!/bin/bash

# =============================================================================
# RENTAHUB TEST RUNNER SCRIPT
# =============================================================================
# Comprehensive test runner for RentaHub backend

set -e

echo "🧪 RentaHub Test Runner"
echo "======================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Checking environment..."

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_success ".env file created from .env.example"
    else
        print_error ".env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Build the project
print_status "Building project..."
if npm run build; then
    print_success "Project built successfully"
else
    print_error "Build failed"
    exit 1
fi

# Check if test database is accessible
print_status "Checking database connection..."
if node -e "
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
dotenv.config();

const supabase = createClient(
    process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltfrq.supabase.co',
    process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU'
);

supabase.from('users').select('count').limit(1).then(({ error }) => {
    if (error) {
        console.error('Database connection failed:', error.message);
        process.exit(1);
    } else {
        console.log('Database connection successful');
        process.exit(0);
    }
});
"; then
    print_success "Database connection verified"
else
    print_error "Database connection failed"
    exit 1
fi

# Parse command line arguments
TEST_TYPE="all"
COVERAGE=false
WATCH=false
VERBOSE=false
PLAYWRIGHT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --unit)
            TEST_TYPE="unit"
            shift
            ;;
        --integration)
            TEST_TYPE="integration"
            shift
            ;;
        --e2e)
            TEST_TYPE="e2e"
            shift
            ;;
        --playwright)
            PLAYWRIGHT=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --unit        Run only unit tests (Jest)"
            echo "  --integration Run only integration tests (Jest)"
            echo "  --e2e         Run only end-to-end tests (Jest)"
            echo "  --playwright  Run Playwright E2E tests"
            echo "  --coverage    Generate coverage report"
            echo "  --watch       Run tests in watch mode"
            echo "  --verbose     Verbose output"
            echo "  --help        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run all Jest tests"
            echo "  $0 --unit            # Run only unit tests"
            echo "  $0 --playwright      # Run Playwright E2E tests"
            echo "  $0 --coverage        # Run tests with coverage"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run Playwright tests if requested
if [ "$PLAYWRIGHT" = true ]; then
    print_status "Running Playwright E2E tests..."

    # Check if Playwright is installed
    if ! command -v playwright &> /dev/null; then
        print_error "Playwright is not installed. Installing..."
        npx playwright install
    fi

    # Set test environment
    export NODE_ENV=test
    export FRONTEND_URL="http://localhost:3000"

    # Build Playwright command
    PLAYWRIGHT_CMD="playwright test"

    if [ "$VERBOSE" = true ]; then
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --reporter=list"
    fi

    print_status "Executing: $PLAYWRIGHT_CMD"
    if eval $PLAYWRIGHT_CMD; then
        print_success "Playwright tests passed!"

        # Open test report if available
        if [ -f "playwright-report/index.html" ]; then
            print_status "Opening Playwright test report..."
            if command -v open &> /dev/null; then
                open playwright-report/index.html
            elif command -v xdg-open &> /dev/null; then
                xdg-open playwright-report/index.html
            fi
        fi

        exit 0
    else
        print_error "Playwright tests failed!"
        exit 1
    fi
fi

# Build Jest command
JEST_CMD="jest"

if [ "$COVERAGE" = true ]; then
    JEST_CMD="$JEST_CMD --coverage"
fi

if [ "$WATCH" = true ]; then
    JEST_CMD="$JEST_CMD --watch"
fi

if [ "$VERBOSE" = true ]; then
    JEST_CMD="$JEST_CMD --verbose"
fi

case $TEST_TYPE in
    unit)
        JEST_CMD="$JEST_CMD --testPathPattern=unit"
        print_status "Running unit tests..."
        ;;
    integration)
        JEST_CMD="$JEST_CMD --testPathPattern=integration"
        print_status "Running integration tests..."
        ;;
    e2e)
        JEST_CMD="$JEST_CMD --testPathPattern=e2e"
        print_status "Running end-to-end tests..."
        ;;
    all)
        print_status "Running all Jest tests..."
        ;;
esac

# Set test environment
export NODE_ENV=test

# Run tests
print_status "Executing: $JEST_CMD"
if eval $JEST_CMD; then
    print_success "All tests passed!"

    if [ "$COVERAGE" = true ]; then
        print_status "Coverage report generated in coverage/ directory"

        # Open coverage report if available
        if command -v open &> /dev/null; then
            print_status "Opening coverage report..."
            open coverage/lcov-report/index.html
        elif command -v xdg-open &> /dev/null; then
            print_status "Opening coverage report..."
            xdg-open coverage/lcov-report/index.html
        fi
    fi

    exit 0
else
    print_error "Some tests failed!"
    exit 1
fi
