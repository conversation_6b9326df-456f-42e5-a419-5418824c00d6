import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';

export const fetchAnalytics = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/analytics/overview`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch analytics', error);
    throw error;
  }
};

export const fetchPerformanceMetrics = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/performance/metrics`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch performance metrics', error);
    throw error;
  }
};

export const fetchMonitoringData = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/monitoring/status`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch monitoring data', error);
    throw error;
  }
};

export const triggerCacheRefresh = async () => {
  try {
    const response = await axios.post(`${API_BASE_URL}/cache/refresh`);
    return response.data;
  } catch (error) {
    console.error('Failed to refresh cache', error);
    throw error;
  }
};

export const generatePerformanceReport = async (
  startDate: Date, 
  endDate: Date
) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/performance/report`, {
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to generate performance report', error);
    throw error;
  }
};
