<testsuites id="" name="" tests="5" failures="1" skipped="4" errors="0" time="422.74109899999996">
<testsuite name="auth-flow.test.ts" timestamp="2025-07-21T06:53:05.845Z" hostname="chromium" tests="1" failures="0" skipped="1" time="354.937" errors="0">
<testcase name="Authentication Flow › Protected routes redirect to login" classname="auth-flow.test.ts" time="354.937">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth-flow.test.ts" timestamp="2025-07-21T06:53:05.845Z" hostname="firefox" tests="1" failures="1" skipped="0" time="0.006" errors="0">
<testcase name="Authentication Flow › Protected routes redirect to login" classname="auth-flow.test.ts" time="0.006">
<failure message="auth-flow.test.ts:164:7 Protected routes redirect to login" type="FAILURE">
<![CDATA[  [firefox] › auth-flow.test.ts:164:7 › Authentication Flow › Protected routes redirect to login ───

    TimeoutError: browserType.launch: Timeout 180000ms exceeded.
    Call log:
      - <launching> /Users/<USER>/Library/Caches/ms-playwright/firefox-1489/firefox/Nightly.app/Contents/MacOS/firefox -no-remote -headless -profile /var/folders/2s/df_1m4c13638zcn8vz13v8qw0000gn/T/playwright_firefoxdev_profile-XSrdbW -juggler-pipe -silent
      - <launched> pid=61207
      - [pid=61207][err] *** You are running in headless mode.
      - [pid=61207][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement

]]>
</failure>
</testcase>
</testsuite>
<testsuite name="auth-flow.test.ts" timestamp="2025-07-21T06:53:05.845Z" hostname="webkit" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="Authentication Flow › Protected routes redirect to login" classname="auth-flow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth-flow.test.ts" timestamp="2025-07-21T06:53:05.845Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="Authentication Flow › Protected routes redirect to login" classname="auth-flow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth-flow.test.ts" timestamp="2025-07-21T06:53:05.845Z" hostname="Mobile Safari" tests="1" failures="0" skipped="1" time="0" errors="0">
<testcase name="Authentication Flow › Protected routes redirect to login" classname="auth-flow.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>