{"config": {"configFile": "/Users/<USER>/Desktop/RENTAHUB/backend/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 1, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/RENTAHUB/backend/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 2, "webServer": null}, "suites": [{"title": "auth-flow.test.ts", "file": "auth-flow.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth-flow.test.ts", "line": 4, "column": 6, "specs": [{"title": "Protected routes redirect to login", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "interrupted", "duration": 354937, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T06:53:11.020Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results/auth-flow-Authentication-F-a5562-ed-routes-redirect-to-login-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/RENTAHUB/backend/test-results/auth-flow-Authentication-F-a5562-ed-routes-redirect-to-login-chromium/error-context.md"}]}], "status": "skipped"}], "id": "0f3b2e986fc758129f61-3d43d18f44d00d86cc6f", "file": "auth-flow.test.ts", "line": 164, "column": 7}, {"title": "Protected routes redirect to login", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 6, "error": {"message": "TimeoutError: browserType.launch: Timeout 180000ms exceeded.\nCall log:\n\u001b[2m  - <launching> /Users/<USER>/Library/Caches/ms-playwright/firefox-1489/firefox/Nightly.app/Contents/MacOS/firefox -no-remote -headless -profile /var/folders/2s/df_1m4c13638zcn8vz13v8qw0000gn/T/playwright_firefoxdev_profile-XSrdbW -juggler-pipe -silent\u001b[22m\n\u001b[2m  - <launched> pid=61207\u001b[22m\n\u001b[2m  - [pid=61207][err] *** You are running in headless mode.\u001b[22m\n\u001b[2m  - [pid=61207][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement\u001b[22m\n", "stack": "TimeoutError: browserType.launch: Timeout 180000ms exceeded.\nCall log:\n\u001b[2m  - <launching> /Users/<USER>/Library/Caches/ms-playwright/firefox-1489/firefox/Nightly.app/Contents/MacOS/firefox -no-remote -headless -profile /var/folders/2s/df_1m4c13638zcn8vz13v8qw0000gn/T/playwright_firefoxdev_profile-XSrdbW -juggler-pipe -silent\u001b[22m\n\u001b[2m  - <launched> pid=61207\u001b[22m\n\u001b[2m  - [pid=61207][err] *** You are running in headless mode.\u001b[22m\n\u001b[2m  - [pid=61207][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement\u001b[22m\n"}, "errors": [{"message": "TimeoutError: browserType.launch: Timeout 180000ms exceeded.\nCall log:\n\u001b[2m  - <launching> /Users/<USER>/Library/Caches/ms-playwright/firefox-1489/firefox/Nightly.app/Contents/MacOS/firefox -no-remote -headless -profile /var/folders/2s/df_1m4c13638zcn8vz13v8qw0000gn/T/playwright_firefoxdev_profile-XSrdbW -juggler-pipe -silent\u001b[22m\n\u001b[2m  - <launched> pid=61207\u001b[22m\n\u001b[2m  - [pid=61207][err] *** You are running in headless mode.\u001b[22m\n\u001b[2m  - [pid=61207][err] JavaScript warning: resource://services-settings/Utils.sys.mjs, line 116: unreachable code after return statement\u001b[22m\n"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T06:53:11.036Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "0f3b2e986fc758129f61-6eff2ff01ec03e56462d", "file": "auth-flow.test.ts", "line": 164, "column": 7}, {"title": "Protected routes redirect to login", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "0f3b2e986fc758129f61-beef820bc59cbaf3eb7a", "file": "auth-flow.test.ts", "line": 164, "column": 7}, {"title": "Protected routes redirect to login", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "0f3b2e986fc758129f61-231b9d678a0b19c3db5f", "file": "auth-flow.test.ts", "line": 164, "column": 7}, {"title": "Protected routes redirect to login", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "0f3b2e986fc758129f61-46c07a9ea35fb7d17b60", "file": "auth-flow.test.ts", "line": 164, "column": 7}]}]}], "errors": [{"message": "\u001b[31mTesting stopped early after 1 maximum allowed failures.\u001b[39m"}], "stats": {"startTime": "2025-07-21T06:52:09.604Z", "duration": 422741.099, "expected": 0, "skipped": 4, "unexpected": 1, "flaky": 0}}