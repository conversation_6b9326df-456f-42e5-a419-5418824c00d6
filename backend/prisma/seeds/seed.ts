import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

const seAsiaVehicles = [
  {
    id: 'honda-click-125',
    providerId: 'system-provider',
    brand: 'Honda',
    model: 'Click 125i',
    year: '2024',
    engine: '125cc',
    type: 'scooter',
    category: 'small',
    dailyRate: 15.00,
    weeklyRate: 80.00,
    monthlyRate: 300.00,
    availableUnits: 5,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 2,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 100.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 5.00,
    addOns: ['helmet', 'raincoat', 'delivery'],
    smartTags: ['fuel-efficient', 'automatic', 'city-friendly'],
    deliveryAvailable: true,
    deliveryRadius: 10.0,
    location_address: 'Bangkok, Thailand',
    location_city: 'Bangkok',
    location_latitude: 13.7563,
    location_longitude: 100.5018
  },
  {
    id: 'yamaha-nmax-155',
    providerId: 'system-provider',
    brand: 'Yamaha',
    model: 'NMAX 155',
    year: '2024',
    engine: '155cc',
    type: 'scooter',
    category: 'medium',
    dailyRate: 20.00,
    weeklyRate: 110.00,
    monthlyRate: 400.00,
    availableUnits: 3,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 2,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 150.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 7.00,
    addOns: ['helmet', 'raincoat', 'delivery', 'phone-mount'],
    smartTags: ['premium', 'comfortable', 'touring'],
    deliveryAvailable: true,
    deliveryRadius: 15.0,
    location_address: 'Ho Chi Minh City, Vietnam',
    location_city: 'Ho Chi Minh City',
    location_latitude: 10.8231,
    location_longitude: 106.6297
  },
  {
    id: 'honda-beat-110',
    providerId: 'system-provider',
    brand: 'Honda',
    model: 'Beat 110',
    year: '2024',
    engine: '110cc',
    type: 'scooter',
    category: 'small',
    dailyRate: 12.00,
    weeklyRate: 65.00,
    monthlyRate: 250.00,
    availableUnits: 8,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 1,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 80.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 3.00,
    addOns: ['helmet', 'raincoat'],
    smartTags: ['economical', 'lightweight', 'beginner-friendly'],
    deliveryAvailable: true,
    deliveryRadius: 8.0,
    location_address: 'Jakarta, Indonesia',
    location_city: 'Jakarta',
    location_latitude: -6.2088,
    location_longitude: 106.8456
  }
];

async function main() {
  console.log('🌏 Starting RentaHub database seeding...');

  // Create admin user
  const adminPassword = await hash('AdminPassword123!', 10)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'RentaHub Admin',
      role: 'ADMIN',
      status: 'ACTIVE',
      phoneNumber: '+**********',
    }
  })

  // Create sample provider
  const providerPassword = await hash('ProviderPass456!', 10)
  const provider = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Vehicle Provider',
      role: 'PROVIDER',
      status: 'ACTIVE',
      companyName: 'RentaHub Vehicles',
      phoneNumber: '+**********',
    }
  })

  // Create system provider for SE Asian vehicles
  const systemProvider = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'RentaHub System',
      role: 'PROVIDER',
      status: 'ACTIVE',
      emailVerified: true
    }
  })

  console.log('👥 Users created:', { admin: admin.email, provider: provider.email, systemProvider: systemProvider.email });

  // Clear existing vehicles
  await prisma.vehicle.deleteMany({});
  console.log('🗑️  Cleared existing vehicles');

  // Insert SE Asian vehicles
  for (const vehicle of seAsiaVehicles) {
    await prisma.vehicle.create({
      data: vehicle
    });
  }

  console.log(`✅ Successfully seeded ${seAsiaVehicles.length} SE Asian vehicles`);
  console.log('📍 Available markets: Thailand, Vietnam, Indonesia, Malaysia, Philippines');
  console.log('🏍️  Vehicle types: Honda Click 125i, Yamaha NMAX 155, Honda Beat 110');
  console.log('💰 Price range: $12-20/day, $65-110/week, $250-400/month');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

export {}
