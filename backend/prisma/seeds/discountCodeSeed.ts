import { PrismaClient, DiscountType } from '@prisma/client';

const prisma = new PrismaClient();

const discountCodes = [
  {
    code: 'RENT10',
    description: '10% off first booking',
    discountType: DiscountType.PERCENTAGE,
    value: 10,
    minBookingAmount: 50,
    startDate: new Date(),
    endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    usageLimit: 100,
    isActive: true
  },
  {
    code: 'SUMMER25',
    description: '$25 off summer bookings',
    discountType: DiscountType.FIXED_AMOUNT,
    value: 25,
    minBookingAmount: 100,
    startDate: new Date('2024-06-01'),
    endDate: new Date('2024-08-31'),
    usageLimit: 50,
    isActive: true
  },
  {
    code: 'LOYALTY20',
    description: '20% off for loyal customers',
    discountType: DiscountType.PERCENTAGE,
    value: 20,
    minBookingAmount: 200,
    startDate: new Date(),
    endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    usageLimit: 20,
    isActive: true
  }
];

async function seedDiscountCodes() {
  try {
    for (const code of discountCodes) {
      await prisma.discountCode.upsert({
        where: { code: code.code },
        update: code,
        create: code
      });
    }
    console.log('Discount codes seeded successfully');
  } catch (error) {
    console.error('Error seeding discount codes:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

seedDiscountCodes();
