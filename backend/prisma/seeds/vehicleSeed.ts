import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const vehicleData = [
  {
    vehicleType: 'Scooter',
    dailyRate: 25.00,
    hasInsurance: true,
    addOns: ['helmet', 'phone_holder']
  },
  {
    vehicleType: 'Dirt Bike',
    dailyRate: 50.00,
    hasInsurance: false,
    addOns: ['helmet', 'rain_cover']
  },
  {
    vehicleType: 'Motorbike',
    dailyRate: 40.00,
    hasInsurance: true,
    addOns: ['helmet', 'full_tank']
  },
  {
    vehicleType: 'Luxury Bike',
    dailyRate: 100.00,
    hasInsurance: true,
    addOns: ['helmet', 'phone_holder', 'full_tank']
  },
  {
    vehicleType: 'Scooter',
    dailyRate: 30.00,
    hasInsurance: false,
    addOns: ['rain_cover']
  },
  {
    vehicleType: 'Dirt Bike',
    dailyRate: 60.00,
    hasInsurance: true,
    addOns: ['helmet', 'phone_holder', 'rain_cover']
  }
];

async function seedVehicles() {
  try {
    for (const vehicle of vehicleData) {
      await prisma.vehicle.create({
        data: vehicle
      });
    }
    console.log('Vehicle seed data created successfully');
  } catch (error) {
    console.error('Error seeding vehicle data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

seedVehicles();
