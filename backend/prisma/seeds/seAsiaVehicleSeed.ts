import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const seAsiaVehicles = [
  {
    id: 'honda-click-125',
    providerId: 'system-provider',
    brand: 'Honda',
    model: 'Click 125i',
    year: '2024',
    engine: '125cc',
    type: 'scooter',
    category: 'small',
    marketCoverage: ['thailand', 'vietnam', 'indonesia', 'malaysia'],
    imageSet: {
      primary: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop',
      gallery: [
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop'
      ]
    },
    specMetadata: {
      engine: '125cc PGM-FI',
      fuelSystem: 'PGM-FI',
      transmission: 'Automatic',
      mileage: '45 km/L',
      maxPower: '11.1 PS @ 8,500 rpm',
      maxTorque: '10.8 Nm @ 6,500 rpm',
      fuelCapacity: '5.5L',
      seatHeight: '780mm',
      weight: '108kg',
      dimensions: '1,890 x 690 x 1,140 mm'
    },
    availableCount: 5,
    rentalOptions: {
      insurance: { offered: true, dailyCost: 5.00 },
      delivery: { pickup: true, dropoff: true, fee: 2.50 },
      accessories: {
        helmets: 2,
        raincoats: true,
        fullTank: true
      },
      deposit: 100.00
    },
    pricing: {
      daily: 15.00,
      weekly: 80.00,
      monthly: 300.00
    },
    dailyRate: 15.00,
    weeklyRate: 80.00,
    monthlyRate: 300.00,
    availableUnits: 5,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 2,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 100.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 5.00,
    addOns: ['helmet', 'raincoat', 'delivery'],
    smartTags: ['fuel-efficient', 'automatic', 'city-friendly'],
    deliveryAvailable: true,
    deliveryRadius: 10.0,
    location_address: 'Bangkok, Thailand',
    location_city: 'Bangkok',
    location_latitude: 13.7563,
    location_longitude: 100.5018
  },
  {
    id: 'yamaha-nmax-155',
    providerId: 'system-provider',
    brand: 'Yamaha',
    model: 'NMAX 155',
    year: '2024',
    engine: '155cc',
    type: 'scooter',
    category: 'medium',
    marketCoverage: ['thailand', 'vietnam', 'indonesia', 'malaysia', 'philippines'],
    imageSet: {
      primary: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
      gallery: [
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop'
      ]
    },
    specMetadata: {
      engine: '155cc Blue Core',
      fuelSystem: 'EFI',
      transmission: 'Automatic',
      mileage: '40 km/L',
      maxPower: '15.4 PS @ 8,000 rpm',
      maxTorque: '13.8 Nm @ 6,500 rpm',
      fuelCapacity: '7.1L',
      seatHeight: '765mm',
      weight: '127kg',
      dimensions: '2,015 x 740 x 1,115 mm'
    },
    availableCount: 3,
    rentalOptions: {
      insurance: { offered: true, dailyCost: 7.00 },
      delivery: { pickup: true, dropoff: true, fee: 3.00 },
      accessories: {
        helmets: 2,
        raincoats: true,
        fullTank: true
      },
      deposit: 150.00
    },
    pricing: {
      daily: 20.00,
      weekly: 110.00,
      monthly: 400.00
    },
    dailyRate: 20.00,
    weeklyRate: 110.00,
    monthlyRate: 400.00,
    availableUnits: 3,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 2,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 150.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 7.00,
    addOns: ['helmet', 'raincoat', 'delivery', 'phone-mount'],
    smartTags: ['premium', 'comfortable', 'touring'],
    deliveryAvailable: true,
    deliveryRadius: 15.0,
    location_address: 'Ho Chi Minh City, Vietnam',
    location_city: 'Ho Chi Minh City',
    location_latitude: 10.8231,
    location_longitude: 106.6297
  },
  {
    id: 'honda-beat-110',
    providerId: 'system-provider',
    brand: 'Honda',
    model: 'Beat 110',
    year: '2024',
    engine: '110cc',
    type: 'scooter',
    category: 'small',
    marketCoverage: ['indonesia', 'thailand', 'vietnam'],
    imageSet: {
      primary: 'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop',
      gallery: [
        'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop'
      ]
    },
    specMetadata: {
      engine: '110cc PGM-FI',
      fuelSystem: 'PGM-FI',
      transmission: 'Automatic',
      mileage: '50 km/L',
      maxPower: '9.3 PS @ 8,000 rpm',
      maxTorque: '9.0 Nm @ 6,000 rpm',
      fuelCapacity: '4.0L',
      seatHeight: '750mm',
      weight: '95kg',
      dimensions: '1,800 x 670 x 1,030 mm'
    },
    availableCount: 8,
    rentalOptions: {
      insurance: { offered: true, dailyCost: 3.00 },
      delivery: { pickup: true, dropoff: true, fee: 2.00 },
      accessories: {
        helmets: 1,
        raincoats: true,
        fullTank: true
      },
      deposit: 80.00
    },
    pricing: {
      daily: 12.00,
      weekly: 65.00,
      monthly: 250.00
    },
    dailyRate: 12.00,
    weeklyRate: 65.00,
    monthlyRate: 250.00,
    availableUnits: 8,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 1,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 80.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 3.00,
    addOns: ['helmet', 'raincoat'],
    smartTags: ['economical', 'lightweight', 'beginner-friendly'],
    deliveryAvailable: true,
    deliveryRadius: 8.0,
    location_address: 'Jakarta, Indonesia',
    location_city: 'Jakarta',
    location_latitude: -6.2088,
    location_longitude: 106.8456
  },
  {
    id: 'suzuki-address-110',
    providerId: 'system-provider',
    brand: 'Suzuki',
    model: 'Address 110',
    year: '2024',
    engine: '110cc',
    type: 'scooter',
    category: 'small',
    marketCoverage: ['thailand', 'vietnam', 'indonesia'],
    imageSet: {
      primary: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop',
      gallery: [
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop'
      ]
    },
    specMetadata: {
      engine: '110cc SOHC',
      fuelSystem: 'EFI',
      transmission: 'Automatic',
      mileage: '48 km/L',
      maxPower: '9.4 PS @ 7,500 rpm',
      maxTorque: '9.0 Nm @ 6,000 rpm',
      fuelCapacity: '4.2L',
      seatHeight: '740mm',
      weight: '98kg',
      dimensions: '1,785 x 665 x 1,035 mm'
    },
    availableCount: 6,
    rentalOptions: {
      insurance: { offered: true, dailyCost: 3.50 },
      delivery: { pickup: true, dropoff: true, fee: 2.00 },
      accessories: {
        helmets: 1,
        raincoats: true,
        fullTank: true
      },
      deposit: 85.00
    },
    pricing: {
      daily: 13.00,
      weekly: 70.00,
      monthly: 270.00
    },
    dailyRate: 13.00,
    weeklyRate: 70.00,
    monthlyRate: 270.00,
    availableUnits: 6,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 1,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 85.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 3.50,
    addOns: ['helmet', 'raincoat'],
    smartTags: ['reliable', 'fuel-efficient', 'urban'],
    deliveryAvailable: true,
    deliveryRadius: 10.0,
    location_address: 'Hanoi, Vietnam',
    location_city: 'Hanoi',
    location_latitude: 21.0285,
    location_longitude: 105.8542
  },
  {
    id: 'yamaha-mio-125',
    providerId: 'system-provider',
    brand: 'Yamaha',
    model: 'Mio 125',
    year: '2024',
    engine: '125cc',
    type: 'scooter',
    category: 'small',
    marketCoverage: ['indonesia', 'malaysia', 'thailand'],
    imageSet: {
      primary: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
      gallery: [
        'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1593941707882-a5bba6c5c4e8?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800&h=600&fit=crop'
      ]
    },
    specMetadata: {
      engine: '125cc Blue Core',
      fuelSystem: 'EFI',
      transmission: 'Automatic',
      mileage: '42 km/L',
      maxPower: '11.1 PS @ 8,000 rpm',
      maxTorque: '10.8 Nm @ 6,500 rpm',
      fuelCapacity: '4.2L',
      seatHeight: '750mm',
      weight: '99kg',
      dimensions: '1,850 x 670 x 1,040 mm'
    },
    availableCount: 7,
    rentalOptions: {
      insurance: { offered: true, dailyCost: 4.00 },
      delivery: { pickup: true, dropoff: true, fee: 2.50 },
      accessories: {
        helmets: 1,
        raincoats: true,
        fullTank: true
      },
      deposit: 90.00
    },
    pricing: {
      daily: 14.00,
      weekly: 75.00,
      monthly: 280.00
    },
    dailyRate: 14.00,
    weeklyRate: 75.00,
    monthlyRate: 280.00,
    availableUnits: 7,
    insuranceOffered: true,
    dropoffAvailable: true,
    pickupAvailable: true,
    helmetsIncluded: 1,
    raincoatsIncluded: true,
    fullTank: true,
    depositAmount: 90.00,
    vehicleType: 'scooter',
    hasInsurance: true,
    insurancePrice: 4.00,
    addOns: ['helmet', 'raincoat'],
    smartTags: ['popular', 'reliable', 'city-friendly'],
    deliveryAvailable: true,
    deliveryRadius: 12.0,
    location_address: 'Kuala Lumpur, Malaysia',
    location_city: 'Kuala Lumpur',
    location_latitude: 3.1390,
    location_longitude: 101.6869
  }
];

async function main() {
  console.log('🌏 Seeding SE Asian vehicles...');

  // Create a system provider user if it doesn't exist
  const systemProvider = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'RentaHub System',
      role: 'PROVIDER',
      status: 'ACTIVE',
      emailVerified: true
    }
  });

  // Clear existing vehicles
  await prisma.vehicle.deleteMany({});

  // Insert SE Asian vehicles
  for (const vehicle of seAsiaVehicles) {
    await prisma.vehicle.create({
      data: vehicle
    });
  }

  console.log(`✅ Successfully seeded ${seAsiaVehicles.length} SE Asian vehicles`);
  console.log('📍 Available markets: Thailand, Vietnam, Indonesia, Malaysia, Philippines');
  console.log('🏍️  Vehicle types: Honda Click 125i, Yamaha NMAX 155, Honda Beat 110, Suzuki Address 110, Yamaha Mio 125');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding vehicles:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 