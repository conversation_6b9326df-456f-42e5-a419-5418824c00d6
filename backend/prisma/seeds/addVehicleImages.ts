import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const vehicleImages = [
  {
    vehicleId: 'honda-click-125',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
        caption: 'Honda Click 125i - Front View',
        isPrimary: true
      },
      {
        url: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400',
        caption: 'Honda Click 125i - Side View',
        isPrimary: false
      }
    ]
  },
  {
    vehicleId: 'yamaha-nmax-155',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1591637333184-19aa84b3e01f?w=400',
        caption: 'Yamaha NMAX 155 - Front View',
        isPrimary: true
      },
      {
        url: 'https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=400',
        caption: 'Yamaha NMAX 155 - Side View',
        isPrimary: false
      }
    ]
  },
  {
    vehicleId: 'honda-beat-110',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1619771914272-e3c1ba17ba4d?w=400',
        caption: 'Honda Beat 110 - Front View',
        isPrimary: true
      },
      {
        url: 'https://images.unsplash.com/photo-1622185135505-2d795003994a?w=400',
        caption: 'Honda Beat 110 - Side View',
        isPrimary: false
      }
    ]
  }
];

async function main() {
  console.log('🖼️  Adding vehicle images...');

  for (const vehicleImage of vehicleImages) {
    // Clear existing images for this vehicle
    await prisma.vehicleImage.deleteMany({
      where: { vehicleId: vehicleImage.vehicleId }
    });

    // Add new images
    for (const image of vehicleImage.images) {
      await prisma.vehicleImage.create({
        data: {
          vehicleId: vehicleImage.vehicleId,
          url: image.url,
          caption: image.caption,
          isPrimary: image.isPrimary
        }
      });
    }

    console.log(`✅ Added ${vehicleImage.images.length} images to ${vehicleImage.vehicleId}`);
  }

  console.log('🎉 All vehicle images added successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error adding vehicle images:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

export {} 