-- Add vehicle listing fields
ALTER TABLE "Vehicle" ADD COLUMN "brand" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "model" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "year" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "engine" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "type" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "category" TEXT;
ALTER TABLE "Vehicle" ADD COLUMN "availableUnits" INTEGER NOT NULL DEFAULT 1;
ALTER TABLE "Vehicle" ADD COLUMN "yearlyRate" DECIMAL(10,2);
ALTER TABLE "Vehicle" ADD COLUMN "insuranceOffered" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Vehicle" ADD COLUMN "dropoffAvailable" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Vehicle" ADD COLUMN "pickupAvailable" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Vehicle" ADD COLUMN "helmetsIncluded" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "Vehicle" ADD COLUMN "raincoatsIncluded" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Vehicle" ADD COLUMN "fullTank" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "Vehicle" ADD COLUMN "depositAmount" DECIMAL(10,2) NOT NULL DEFAULT 0; 