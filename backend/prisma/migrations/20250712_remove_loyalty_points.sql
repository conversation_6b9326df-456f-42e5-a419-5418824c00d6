-- Remove loyalty points system from database
-- Migration: 20250712_remove_loyalty_points

-- 1. Drop the loyalty_points table
DROP TABLE IF EXISTS "loyalty_points" CASCADE;

-- 2. Remove the loyaltyPoints relation from the User model
-- (This will be handled by the schema update)

-- 3. Remove the pointsRedeemed field from the Booking model
-- (This will be handled by the schema update)

-- 4. Clean up any existing loyalty points data
-- (The table is already dropped above)

-- 5. Update any existing bookings to remove pointsRedeemed values
UPDATE "Booking" SET "pointsRedeemed" = 0 WHERE "pointsRedeemed" IS NOT NULL;

-- 6. Add a check constraint to ensure pointsRedeemed is always 0
ALTER TABLE "Booking" ADD CONSTRAINT "booking_points_redeemed_zero" CHECK ("pointsRedeemed" = 0);

-- 7. Set default value for pointsRedeemed to 0
ALTER TABLE "Booking" ALTER COLUMN "pointsRedeemed" SET DEFAULT 0; 