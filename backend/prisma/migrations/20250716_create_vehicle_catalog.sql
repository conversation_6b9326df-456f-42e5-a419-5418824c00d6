-- Create comprehensive vehicle_catalog table with all vehicle details
CREATE TABLE IF NOT EXISTS vehicle_catalog (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  make TEXT NOT NULL,
  model TEXT NOT NULL,
  year TEXT,
  engine TEXT,
  type TEXT,
  category TEXT,
  image_url TEXT,
  source TEXT NOT NULL, -- "Southeast Asia Scrape" or "Mock"
  daily_rate DECIMAL(10,2),
  rating DECIMAL(3,2),
  reviews INTEGER,
  features TEXT[],
  available_units INTEGER DEFAULT 1,
  has_insurance BOOLEAN DEFAULT true,
  insurance_price DECIMAL(10,2),
  location_city TEXT DEFAULT 'Indonesia',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (make, model, year)
);

-- Create index for faster searches
CREATE INDEX IF NOT EXISTS idx_vehicle_catalog_make_model ON vehicle_catalog (make, model);
CREATE INDEX IF NOT EXISTS idx_vehicle_catalog_category ON vehicle_catalog (category);
CREATE INDEX IF NOT EXISTS idx_vehicle_catalog_source ON vehicle_catalog (source);

-- Enable RLS (Row Level Security)
ALTER TABLE vehicle_catalog ENABLE ROW LEVEL SECURITY;

-- Create policy to allow read access for all users
CREATE POLICY "Allow read access to vehicle_catalog" ON vehicle_catalog
  FOR SELECT USING (true);

-- Create policy to allow insert/update for authenticated users
CREATE POLICY "Allow insert/update vehicle_catalog" ON vehicle_catalog
  FOR ALL USING (auth.role() = 'authenticated'); 