-- Create listed_vehicles table for live listings from providers
CREATE TABLE listed_vehicles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID REFERENCES "User"(id),
  make TEXT NOT NULL,
  model TEXT NOT NULL,
  year INTEGER,
  category TEXT,
  fuel_type TEXT,
  location_city TEXT,
  price_per_day INTEGER,
  description TEXT,
  images TEXT[], -- image URLs
  is_available BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add is_reference_only column to vehicle_catalog
ALTER TABLE vehicle_catalog ADD COLUMN is_reference_only BOOLEAN DEFAULT TRUE;

-- Create indexes for better performance
CREATE INDEX idx_listed_vehicles_provider_id ON listed_vehicles(provider_id);
CREATE INDEX idx_listed_vehicles_available ON listed_vehicles(is_available);
CREATE INDEX idx_listed_vehicles_location ON listed_vehicles(location_city);
CREATE INDEX idx_vehicle_catalog_reference ON vehicle_catalog(is_reference_only);
CREATE INDEX idx_vehicle_catalog_make_model ON vehicle_catalog(make, model); 