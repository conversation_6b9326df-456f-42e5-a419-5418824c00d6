-- Payment Tables Migration

-- Payments table
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID NOT NULL REFERENCES bookings(id),
  customer_id UUID NOT NULL REFERENCES users(id),
  payment_method VARCHAR(50) NOT NULL, -- 'stripe', 'paypal', 'bank_transfer'
  payment_gateway_id VARCHAR(255), -- External payment ID
  
  -- Amount breakdown
  base_rental_cost DECIMAL(10,2) NOT NULL,
  booking_fee_rate DECIMAL(5,4) DEFAULT 0.15,
  booking_fee_amount DECIMAL(10,2) NOT NULL,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  
  -- Payment status
  status VARCHAR(50) DEFAULT 'pending' 
    CHECK (status IN ('pending', 'completed', 'failed', 'refunded', 'partial_refund')),
  gateway_status VARCHAR(100),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  -- Metadata
  gateway_response JSONB,
  failure_reason TEXT,
  
  CONSTRAINT fk_booking FOREIGN KEY (booking_id) REFERENCES bookings(id),
  CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES users(id)
);

-- Payment splits tracking
CREATE TABLE payment_splits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id),
  owner_id UUID NOT NULL REFERENCES users(id),
  
  -- Split amounts
  owner_amount DECIMAL(10,2) NOT NULL,
  rentahub_amount DECIMAL(10,2) NOT NULL,
  processing_fees DECIMAL(10,2) DEFAULT 0,
  net_owner_payout DECIMAL(10,2) NOT NULL,
  
  -- Split status
  split_status VARCHAR(50) DEFAULT 'pending' 
    CHECK (split_status IN ('pending', 'completed', 'failed')),
  split_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_payment FOREIGN KEY (payment_id) REFERENCES payments(id),
  CONSTRAINT fk_owner FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Owner payouts tracking
CREATE TABLE owner_payouts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID NOT NULL REFERENCES users(id),
  
  -- Payout details
  total_earnings DECIMAL(10,2) NOT NULL,
  processing_fees DECIMAL(10,2) NOT NULL,
  net_payout DECIMAL(10,2) NOT NULL,
  
  -- Payout method
  payout_method VARCHAR(50) NOT NULL 
    CHECK (payout_method IN ('bank_transfer', 'paypal', 'stripe_transfer')),
  payout_account_id VARCHAR(255),
  
  -- Status tracking
  status VARCHAR(50) DEFAULT 'pending' 
    CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  scheduled_date DATE,
  completed_date TIMESTAMP WITH TIME ZONE,
  
  -- Reference data
  payment_splits JSONB, -- Array of payment_split IDs included
  gateway_payout_id VARCHAR(255),
  failure_reason TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_owner FOREIGN KEY (owner_id) REFERENCES users(id)
);

-- Refunds tracking
CREATE TABLE refunds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES payments(id),
  booking_id UUID NOT NULL REFERENCES bookings(id),
  
  -- Refund amounts
  original_amount DECIMAL(10,2) NOT NULL,
  refund_amount DECIMAL(10,2) NOT NULL,
  fee_refund_amount DECIMAL(10,2) NOT NULL,
  owner_refund_amount DECIMAL(10,2) NOT NULL,
  
  -- Refund details
  refund_reason TEXT,
  refund_type VARCHAR(50) NOT NULL 
    CHECK (refund_type IN ('full', 'partial', 'cancellation_fee')),
  gateway_refund_id VARCHAR(255),
  
  -- Status
  status VARCHAR(50) DEFAULT 'pending' 
    CHECK (status IN ('pending', 'completed', 'failed')),
  processed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT fk_payment FOREIGN KEY (payment_id) REFERENCES payments(id),
  CONSTRAINT fk_booking FOREIGN KEY (booking_id) REFERENCES bookings(id)
);

-- Payment audit logs
CREATE TABLE payment_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID REFERENCES payments(id),
  action VARCHAR(100) NOT NULL,
  details JSONB,
  user_id UUID REFERENCES users(id),
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
