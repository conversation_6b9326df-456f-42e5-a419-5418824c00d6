-- Loyalty Points Table
CREATE TABLE IF NOT EXISTS loyalty_points (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  points_earned INTEGER DEFAULT 0,
  source VARCHAR(20) CHECK (source IN ('booking', 'promotion', 'referral', 'review_bonus')),
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Tiers Table
CREATE TABLE IF NOT EXISTS user_tiers (
  user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  current_tier VARCHAR(10) DEFAULT 'Bronze' CHECK (current_tier IN ('Bronze', 'Silver', 'Gold')),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update Users Table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS loyalty_points INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS current_tier VARCHAR(10) DEFAULT 'Bronze';

-- Update Vehicles Table
ALTER TABLE vehicles 
ADD COLUMN IF NOT EXISTS rating NUMERIC(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0;

-- Update Providers Table
ALTER TABLE providers 
ADD COLUMN IF NOT EXISTS rating NUMERIC(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0;

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_loyalty_points_user_id ON loyalty_points(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_vehicle_id ON reviews(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_reviews_provider_id ON reviews(provider_id);
