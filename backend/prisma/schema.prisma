generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                       String                        @id @default(cuid())
  email                    String                        @unique
  password                 String?
  name                     String?
  role                     UserRole                      @default(CUSTOMER)
  status                   UserStatus                    @default(ACTIVE)
  googleId                 String?                       @unique
  emailVerified            Boolean                       @default(false)
  phoneVerified            Boolean                       @default(false)
  phoneNumber              String?
  profileImage             String?
  companyName              String?
  profilePicture           String?
  lastLogin                DateTime?
  createdAt                DateTime                      @default(now())
  updatedAt                DateTime                      @updatedAt
  preferredLanguage        String?                       @default("en")
  payoutMethod             String?                       @map("payout_method")
  stripeAccountId          String?                       @map("stripe_account_id")
  bankAccountName          String?                       @map("bank_account_name")
  bankAccountNumber        String?                       @map("bank_account_number")
  bankName                 String?                       @map("bank_name")
  bankBranch               String?                       @map("bank_branch")
  bankCountry              String?                       @map("bank_country")
  providerBookings         Booking[]                     @relation("ProviderBookings")
  userBookings             Booking[]                     @relation("UserBookings")
  documents                Document[]
  historicalRiskData       HistoricalRiskDataset[]
  insurancePolicies        InsurancePolicy[]
  notifications            Notification[]
  oAuthStates              OAuthState[]
  reports                  Report[]
  reviews                  Review[]
  savedVehicles            SavedVehicle[]
  communicationPreferences UserCommunicationPreferences?
  payoutRequests           PayoutRequest[]               @relation("ProviderPayoutRequests")
  userAssistanceRequests   VehicleAssistanceRequest[] @relation("UserAssistanceRequests")
  providerAssistanceRequests VehicleAssistanceRequest[] @relation("ProviderAssistanceRequests")
  supportTickets           SupportTicket[]
  listedVehicles           ListedVehicle[]               @relation("ProviderListedVehicles")
}

model UserProfile {
  id        String   @id @db.Uuid
  email     String
  firstName String?  @map("first_name") @db.VarChar(100)
  lastName  String?  @map("last_name") @db.VarChar(100)
  phone     String?  @db.VarChar(20)
  role      String   @default("user")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @map("updated_at")

  @@map("user_profiles")
}

model Vehicle {
  id                 String                     @id @default(cuid())
  vehicleType        String
  dailyRate          Decimal?
  weeklyRate         Decimal?
  monthlyRate        Decimal?
  hasInsurance       Boolean                    @default(false)
  insurancePrice     Decimal                    @default(0)
  addOns             String[]
  addOnPrices        Json?
  smartTags          String[]                   @default([])
  deliveryAvailable  Boolean                    @default(false)
  deliveryRadius     Decimal?
  deliveryOptions    Json?
  availableUnits     Int                        @default(1)
  brand              String?
  category           String?
  depositAmount      Decimal                    @default(0)
  dropoffAvailable   Boolean                    @default(false)
  engine             String?
  fullTank           Boolean                    @default(false)
  helmetsIncluded    Int                        @default(0)
  insuranceOffered   Boolean                    @default(false)
  location_address   String?
  location_city      String?
  location_latitude  Decimal?
  location_longitude Decimal?
  model              String?
  pickupAvailable    Boolean                    @default(false)
  providerId         String
  raincoatsIncluded  Boolean                    @default(false)
  type               String?
  year               String?
  yearlyRate         Decimal?
  bookings           Booking[]
  damageReports      DamageReport[]
  documents          Document[]
  historicalRiskData HistoricalRiskDataset[]
  insurancePolicies  InsurancePolicy[]
  reviews            Review[]
  savedByUsers       SavedVehicle[]
  performanceMetrics VehiclePerformanceMetric[]
  availability       VehicleAvailability[]
  images             VehicleImage[]
  assistanceRequests VehicleAssistanceRequest[]

  @@index([providerId])
}

model VehicleAvailability {
  id        String             @id @default(cuid())
  vehicleId String
  date      DateTime
  status    AvailabilityStatus @default(AVAILABLE)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  vehicle   Vehicle            @relation(fields: [vehicleId], references: [id])

  @@unique([vehicleId, date])
  @@map("vehicle_availability")
}

model VehicleImage {
  id        String   @id @default(cuid())
  vehicleId String
  url       String
  caption   String?
  isPrimary Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id])

  @@map("vehicle_images")
}

model Booking {
  id                    String               @id @default(cuid())
  userId                String
  providerId            String
  vehicleId             String
  totalPrice            Decimal              @default(0)
  status                BookingStatus        @default(PENDING)
  startDate             DateTime
  endDate               DateTime
  paymentMethod         PaymentMethod        @default(CARD)
  cancellationReason    String?
  stripePaymentIntentId String?
  discountApplied       Decimal              @default(0)
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  depositAmount         Decimal              @default(0)
  paymentStatus         PaymentStatus        @default(PENDING)
  quote                 Json?
  provider              User                 @relation("ProviderBookings", fields: [providerId], references: [id])
  user                  User                 @relation("UserBookings", fields: [userId], references: [id])
  vehicle               Vehicle              @relation(fields: [vehicleId], references: [id])
  damageAssessment      DamageAssessment?
  damageReport          DamageReport?
  depositTransactions   DepositTransaction[]
  documents             Document[]
  insuranceClaims       InsuranceClaim[]
  payment               Payment?
  refunds               Refund[]
  refundTransactions    RefundTransaction[]
  reviews               Review[]
  rideChecklist         RideChecklist?
  payoutRequests        PayoutRequest[]
  assistanceRequests    VehicleAssistanceRequest[]
}

model Payment {
  id               String   @id @default(cuid())
  bookingId        String   @unique
  amount           Decimal
  status           String
  paymentMethod    String
  transactionDate  DateTime @default(now())
  providerId       String
  providerEarnings Decimal  @default(0)
  payoutStatus     String   @default("PENDING")
  booking          Booking  @relation(fields: [bookingId], references: [id])
}

model SavedVehicle {
  id        String   @id @default(cuid())
  userId    String
  vehicleId String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id])

  @@unique([userId, vehicleId])
}

model Review {
  id         String   @id @default(cuid())
  userId     String
  vehicleId  String
  rating     Int
  comment    String?
  createdAt  DateTime @default(now())
  bookingId  String?
  booking_id String?
  response   String?
  updatedAt  DateTime @updatedAt
  booking    Booking? @relation(fields: [bookingId], references: [id])
  user       User     @relation(fields: [userId], references: [id])
  vehicle    Vehicle  @relation(fields: [vehicleId], references: [id])
}

model RideChecklist {
  id            String             @id @default(cuid())
  bookingId     String             @unique
  stage         RideChecklistStage
  preRideNotes  String?
  postRideNotes String?
  booking       Booking            @relation(fields: [bookingId], references: [id])
}

model DamageReport {
  id        String               @id @default(cuid())
  bookingId String               @unique
  severity  DamageReportSeverity
  notes     String?
  images    String[]
  vehicleId String
  booking   Booking              @relation(fields: [bookingId], references: [id])
  vehicle   Vehicle              @relation(fields: [vehicleId], references: [id])
}

model UserCommunicationPreferences {
  id                 String   @id @default(cuid())
  userId             String   @unique
  emailNotifications Boolean  @default(true)
  smsNotifications   Boolean  @default(false)
  pushNotifications  Boolean  @default(false)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  user               User     @relation(fields: [userId], references: [id])
}

model SystemAlert {
  id        String   @id @default(cuid())
  type      String
  message   String
  severity  String
  createdAt DateTime @default(now())
  details   String?
}

model IntegrationLog {
  id            String   @id @default(cuid())
  service       String
  request_body  String?
  response_body String?
  error_message String?
  status_code   Int
  created_at    DateTime @default(now())
}

model WebhookLog {
  id         String   @id @default(cuid())
  service    String
  event_type String
  payload    String
  processed  Boolean  @default(false)
  created_at DateTime @default(now())
}

model OAuthState {
  id        String   @id @default(cuid())
  state     String   @unique
  userId    String?
  createdAt DateTime @default(now())
  expiresAt DateTime
  user      User?    @relation(fields: [userId], references: [id])
}

model SupportTicket {
  id          String   @id @default(cuid())
  userId      String
  providerId  String?
  bookingId   String?
  subject     String
  description String
  message     String?
  role        String   @default("USER")
  status      String   @default("OPEN")
  priority    String   @default("MEDIUM")
  category    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id])

  @@map("support_tickets")
}

model VehicleAssistanceRequest {
  id          String                    @id @default(cuid())
  userId      String
  bookingId   String?
  providerId  String?
  vehicleId   String?
  location    String?
  message     String?
  reason      String
  category    VehicleAssistanceCategory @default(BREAKDOWN)
  priority    AssistancePriority        @default(MEDIUM)
  status      AssistanceStatus          @default(PENDING)
  adminNotes  String?
  resolvedAt  DateTime?
  createdAt   DateTime                  @default(now())
  updatedAt   DateTime                  @updatedAt
  booking     Booking?                  @relation(fields: [bookingId], references: [id])
  provider    User?                     @relation("ProviderAssistanceRequests", fields: [providerId], references: [id])
  user        User                      @relation("UserAssistanceRequests", fields: [userId], references: [id])
  vehicle     Vehicle?                  @relation(fields: [vehicleId], references: [id])

  @@map("vehicle_assistance_requests")
}

model ApiKey {
  id          String   @id @default(cuid())
  keyName     String
  keyValue    String   @unique
  isActive    Boolean  @default(true)
  permissions String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("api_keys")
}

model Refund {
  id        String   @id @default(cuid())
  bookingId String
  amount    Decimal
  reason    String?
  status    String   @default("PENDING")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  booking   Booking  @relation(fields: [bookingId], references: [id])
}

model RefundRule {
  id                 String   @id @default(cuid())
  vehicleType        String
  cancellationWindow Int      @unique
  refundPercentage   Decimal  @db.Decimal(5, 2)
  bookingFeeRefund   Boolean  @default(false)
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

model RefundTransaction {
  id              String       @id @default(cuid())
  bookingId       String
  amount          Decimal      @db.Decimal(10, 2)
  refundAmount    Decimal?     @db.Decimal(10, 2)
  refundFee       Decimal?     @db.Decimal(10, 2)
  originalAmount  Decimal?     @db.Decimal(10, 2)
  paymentGateway  String?
  status          RefundStatus @default(PENDING)
  reason          String?
  gatewayRefundId String?
  adminNotes      String?
  processedAt     DateTime?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  booking         Booking      @relation(fields: [bookingId], references: [id])
}

model DamageAssessment {
  id                  String   @id @default(cuid())
  bookingId           String   @unique
  damageDescription   String
  estimatedCost       Decimal  @db.Decimal(10, 2)
  estimatedRepairCost Decimal  @db.Decimal(10, 2)
  damagePhotos        String[] @default([])
  assessmentDate      DateTime @default(now())
  assessedBy          String
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  booking             Booking  @relation(fields: [bookingId], references: [id])
}

model DepositTransaction {
  id                 String                 @id @default(cuid())
  bookingId          String
  amount             Decimal                @db.Decimal(10, 2)
  transactionType    DepositTransactionType
  type               DepositTransactionType
  status             PaymentStatus          @default(PENDING)
  holdStartDate      DateTime?
  holdEndDate        DateTime?
  damageAssessmentId String?
  deductionReason    String?
  deductionEvidence  String?
  processedAt        DateTime?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  booking            Booking                @relation(fields: [bookingId], references: [id])
}

model DiscountCode {
  id               String       @id @default(cuid())
  code             String       @unique
  discountType     DiscountType
  value            Decimal      @db.Decimal(10, 2)
  minBookingAmount Decimal      @default(0) @db.Decimal(10, 2)
  usageLimit       Int?
  timesUsed        Int          @default(0)
  startDate        DateTime
  endDate          DateTime
  isActive         Boolean      @default(true)
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
}

model Notification {
  id        String             @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  status    NotificationStatus @default(PENDING)
  readAt    DateTime?
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  user      User               @relation(fields: [userId], references: [id])
}

model Document {
  id         String         @id @default(cuid())
  userId     String
  vehicleId  String?
  bookingId  String?
  type       DocumentType
  status     DocumentStatus @default(PENDING)
  fileName   String
  fileUrl    String
  uploadedAt DateTime       @default(now())
  approvedAt DateTime?
  expiresAt  DateTime?
  notes      String?
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  booking    Booking?       @relation(fields: [bookingId], references: [id])
  user       User           @relation(fields: [userId], references: [id])
  vehicle    Vehicle?       @relation(fields: [vehicleId], references: [id])
}

model InsurancePolicy {
  id             String           @id @default(cuid())
  userId         String
  vehicleId      String?
  policyNumber   String           @unique
  provider       String
  type           InsuranceType
  coverageAmount Decimal          @db.Decimal(12, 2)
  premium        Decimal          @db.Decimal(8, 2)
  startDate      DateTime
  endDate        DateTime
  isActive       Boolean          @default(true)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  claims         InsuranceClaim[]
  user           User             @relation(fields: [userId], references: [id])
  vehicle        Vehicle?         @relation(fields: [vehicleId], references: [id])
}

model InsuranceClaim {
  id             String          @id @default(cuid())
  policyId       String
  bookingId      String?
  claimNumber    String          @unique
  status         ClaimStatus     @default(PENDING)
  claimAmount    Decimal         @db.Decimal(10, 2)
  approvedAmount Decimal?        @db.Decimal(10, 2)
  description    String
  incidentDate   DateTime
  filedDate      DateTime        @default(now())
  processedDate  DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  booking        Booking?        @relation(fields: [bookingId], references: [id])
  policy         InsurancePolicy @relation(fields: [policyId], references: [id])
}

model Report {
  id          String   @id @default(cuid())
  title       String
  description String?
  type        String
  generatedBy String
  data        Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [generatedBy], references: [id])
}

model PricingConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       Json
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model VehiclePerformanceMetric {
  id         String   @id @default(cuid())
  vehicleId  String
  metric     String
  value      Decimal  @db.Decimal(10, 2)
  recordedAt DateTime @default(now())
  vehicle    Vehicle  @relation(fields: [vehicleId], references: [id])
}

model HistoricalRiskDataset {
  id         String   @id @default(cuid())
  vehicleId  String?
  userId     String?
  riskScore  Decimal  @db.Decimal(5, 2)
  factors    Json
  recordedAt DateTime @default(now())
  user       User?    @relation(fields: [userId], references: [id])
  vehicle    Vehicle? @relation(fields: [vehicleId], references: [id])
}

model GeospatialSearchLog {
  id          String   @id @default(cuid())
  searchQuery String
  coordinates Json
  results     Json
  searchedAt  DateTime @default(now())
}

model Language {
  id           String        @id @default(cuid())
  code         String        @unique
  name         String
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  translations Translation[]
}

model Translation {
  id           String   @id @default(cuid())
  languageCode String
  key          String
  value        String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  language     Language @relation(fields: [languageCode], references: [code])

  @@unique([languageCode, key])
}

model InsuranceConfiguration {
  id             String   @id @default(cuid())
  vehicleType    String
  basePremium    Decimal  @db.Decimal(8, 2)
  riskMultiplier Decimal  @db.Decimal(5, 2)
  coverageTypes  Json
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model ScheduledReport {
  id           String    @id @default(cuid())
  name         String
  type         String
  schedule     String
  recipients   String[]  @default([])
  reportConfig Json?
  isActive     Boolean   @default(true)
  lastRunAt    DateTime?
  nextRunAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model VehicleCatalog {
  id              String   @id @default(uuid()) @db.Uuid
  make            String
  model           String
  year            String?
  engine          String?
  type            String?
  category        String?
  image_url       String?
  source          String
  daily_rate      Decimal? @db.Decimal(10, 2)
  rating          Decimal? @db.Decimal(3, 2)
  reviews         Int?
  features        String[]
  available_units Int?
  has_insurance   Boolean?
  insurance_price Decimal? @db.Decimal(10, 2)
  location_city   String?  @default("Indonesia")
  is_reference_only Boolean @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@unique([make, model, year])
  @@map("vehicle_catalog")
}

model ListedVehicle {
  id              String   @id @default(uuid()) @db.Uuid
  provider_id     String
  make            String
  model           String
  year            Int?
  category        String?
  fuel_type       String?
  location_city   String?
  price_per_day   Int?
  description     String?
  images          String[]
  is_available    Boolean  @default(true)
  created_at      DateTime @default(now())
  provider        User     @relation("ProviderListedVehicles", fields: [provider_id], references: [id])

  @@map("listed_vehicles")
}

model PayoutRequest {
  id         String   @id @default(uuid()) @db.Uuid
  bookingId  String   @map("booking_id")
  providerId String   @map("provider_id")
  method     String
  status     String   @default("pending")
  amount     Int
  createdAt  DateTime @default(now()) @map("created_at")
  booking    Booking  @relation(fields: [bookingId], references: [id])
  provider   User     @relation("ProviderPayoutRequests", fields: [providerId], references: [id])

  @@map("payout_requests")
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REFUNDED
}

enum UserRole {
  CUSTOMER
  PROVIDER
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum RideChecklistStage {
  START
  PICKED_UP
  END
  RETURNED
}

enum DamageReportSeverity {
  MINOR
  MODERATE
  SEVERE
}

enum PaymentMethod {
  CARD
  CASH
}

enum RefundStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSED
  FAILED
  CANCELLED
  COMPLETED
}

enum DepositTransactionType {
  DEPOSIT_HOLD
  DEPOSIT_RELEASE
  DEPOSIT_REFUND
  DEPOSIT_DEDUCTION
  RELEASE
  PARTIAL_DEDUCTION
}

enum DiscountType {
  PERCENTAGE
  FIXED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum VehicleStatus {
  AVAILABLE
  UNAVAILABLE
  MAINTENANCE
  RESERVED
}

enum NotificationType {
  BOOKING_CONFIRMED
  BOOKING_CANCELLED
  PAYMENT_RECEIVED
  REMINDER
  SYSTEM_ALERT
  PROMOTIONAL
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  READ
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum SupportTicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum DocumentType {
  DRIVERS_LICENSE
  INSURANCE_CARD
  VEHICLE_REGISTRATION
  INSPECTION_REPORT
  DAMAGE_REPORT
  CONTRACT
  RECEIPT
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum InsuranceType {
  BASIC
  COMPREHENSIVE
  PREMIUM
}

enum ClaimStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSING
  PAID
}

enum AvailabilityStatus {
  AVAILABLE
  UNAVAILABLE
  MAINTENANCE
  BOOKED
}

enum VehicleAssistanceCategory {
  BREAKDOWN
  ACCIDENT
  FLAT_TIRE
  FUEL_EMPTY
  BATTERY_DEAD
  KEY_LOCKED
  MECHANICAL_ISSUE
  ELECTRICAL_ISSUE
  OTHER
}

enum AssistancePriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum AssistanceStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  RESOLVED
  CANCELLED
}
