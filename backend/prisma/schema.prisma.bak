generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model SavedVehicle {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  vehicleId String
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id])
  createdAt DateTime @default(now())

  @@unique([userId, vehicleId])
}

model LoyaltyPoints {
  id        String   @id @default(cuid())
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id])
  balance   Int      @default(0)
  totalEarned Int    @default(0)
  lastEarnedAt DateTime?
}

model BookingHistory {
  id          String   @id @default(cuid())
  bookingId   String   @unique
  booking     Booking  @relation(fields: [bookingId], references: [id])
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  providerId  String
  provider    User     @relation("ProviderBookings", fields: [providerId], references: [id])
  vehicleId   String
  vehicle     Vehicle  @relation(fields: [vehicleId], references: [id])
  pointsEarned Int     @default(0)
  pointsRedeemed Int   @default(0)
  discountApplied Decimal @default(0)
  status      BookingHistoryStatus @default(COMPLETED)
  createdAt   DateTime @default(now())
}

enum BookingHistoryStatus {
  PENDING
  COMPLETED
  CANCELLED
}

model User {
  id            String         @id @default(cuid())
  savedVehicles SavedVehicle[]
  loyaltyPoints LoyaltyPoints?
  bookingHistories BookingHistory[]
  
  # New filtering fields
  vehicleType   String?
  dailyRate    Decimal?
  hasInsurance Boolean @default(false)
  addOns       String[]  providerBookingHistories BookingHistory[] @relation("ProviderBookings")
  // Other existing user fields
}

model Booking {
  id            String         @id @default(cuid())
  pointsRedeemed Int @default(0)
  discountApplied Decimal @default(0)
  bookingHistory BookingHistory?
  // Other existing booking fields
}

model Vehicle {
  id            String         @id @default(cuid())
  savedByUsers SavedVehicle[]
  bookingHistories BookingHistory[]

  vehicleType   String?
  dailyRate    Decimal?
  hasInsurance Boolean @default(false)
  addOns       String[]
}
