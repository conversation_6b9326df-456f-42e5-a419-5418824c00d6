const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000/api';

// Axios instance with error handling
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 5000,
  validateStatus: function (status) {
    return status >= 200 && status < 500; // Reject only if the status code is greater than or equal to 500
  }
});

// Test utilities
async function runTest(name, testFunction) {
  try {
    console.log(`🧪 Running test: ${name}`);
    await testFunction();
    console.log(`✅ ${name} passed`);
  } catch (error) {
    console.error(`❌ ${name} failed:`, 
      error.response ? error.response.data : error.message
    );
    throw error;
  }
}

// Test data
const testUser = {
  email: `test_user_${Date.now()}@example.com`,
  password: 'TestPassword123!',
  first_name: 'Test',
  last_name: 'User'
};

const testVehicle = {
  provider_id: 'test_provider_123',
  vehicle_type: 'sedan',
  make: 'Toyota',
  model: 'Camry',
  year: 2022,
  price_per_day: 50,
  location: 'Test City'
};

// Test Suite
async function runApiTests() {
  // Health Check
  await runTest('Server Health Check', async () => {
    const response = await api.get('/health');
    if (response.data.status !== 'healthy') {
      throw new Error('Server not healthy');
    }
  });

  // User Registration Test
  await runTest('User Registration', async () => {
    const response = await api.post('/users/register', testUser);
    if (!response.data.user.id) throw new Error('Registration failed');
    testUser.id = response.data.user.id;
  });

  // User Login Test
  await runTest('User Login', async () => {
    const response = await api.post('/users/login', {
      email: testUser.email,
      password: testUser.password
    });
    if (!response.data.user) throw new Error('Login failed');
  });

  // Add Vehicle Test
  await runTest('Add Vehicle', async () => {
    const response = await api.post('/vehicles', testVehicle);
    if (!response.data.vehicle.id) throw new Error('Vehicle creation failed');
    testVehicle.id = response.data.vehicle.id;
  });

  // Search Vehicles Test
  await runTest('Search Vehicles', async () => {
    const response = await api.get('/vehicles/search', {
      params: { vehicle_type: 'sedan' }
    });
    if (response.data.vehicles.length === 0) throw new Error('No vehicles found');
  });

  // Create Booking Test
  await runTest('Create Booking', async () => {
    const bookingData = {
      user_id: testUser.id,
      vehicle_id: testVehicle.id,
      start_date: '2024-02-01',
      end_date: '2024-02-05',
      total_price: 200
    };
    const response = await api.post('/bookings', bookingData);
    if (!response.data.booking.id) throw new Error('Booking creation failed');
  });
}

// Run tests
runApiTests().then(() => {
  console.log('🏁 All API tests completed successfully');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
