// CONSOLIDATED MAIN SERVER ENTRY POINT
// This file combines the best of server.ts and the original index.ts
import express, { Request, Response, NextFunction } from "express";
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import nocache from 'nocache';
import cookieParser from 'cookie-parser';
import <PERSON><PERSON> from 'stripe';
import { handleStripeWebhook } from './controllers/stripeWebhookController';
import {
  generalRateLimiter,
  authRateLimiter,
  readRateLimiter,
  webhookRateLimiter,
  rateLimitLogger
} from './middleware/rateLimitMiddleware';
import { getCorsMiddleware } from './middleware/corsMiddleware';
import { connectionMonitoringMiddleware } from './middleware/connectionMonitoringMiddleware';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { comprehensiveSanitization } from './middleware/inputSanitizationMiddleware';
import { comprehensiveSecurityHeaders } from './middleware/securityHeadersMiddleware';
import { comprehensiveSecurityMonitoring } from './middleware/securityMonitoringMiddleware';
import { comprehensiveAdvancedApiSecurity } from './middleware/advancedApiSecurityMiddleware';
import { config } from './config/env';
import ApiKeyService from './services/ApiKeyService';

// Import TypeScript controllers
import VehicleCatalogController from './controllers/VehicleCatalogController';
import ListedVehicleController from './controllers/ListedVehicleController';
import ProviderController from './controllers/ProviderController';
import VehicleManagementController from './controllers/VehicleManagementController';
import BookingManagementController from './controllers/BookingManagementController';

// Import routes
import savedVehiclesRoutes from './routes/savedVehiclesRoutes';
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import vehicleRoutes from './routes/vehicleRoutes';
import bookingRoutes from './routes/bookingRoutes';
import paymentRoutes from './routes/paymentRoutes';
import refundRoutes from './routes/refundRoutes';
import reviewRoutes from './routes/reviewRoutes';
import healthRoutes from './routes/healthRoutes';
import analyticsRoutes from './routes/analyticsRoutes';
import adminRoutes from './routes/adminRoutes';
import apiKeyRoutes from './routes/apiKeyRoutes';
import webhookRoutes from './routes/webhookRoutes';
import supportRoutes from './routes/supportRoutes';
import vehicleAssistanceRoutes from './routes/vehicleAssistanceRoutes';
import vehicleListingRoutes from './routes/vehicleListingRoutes';
// import vehicleDataRoutes from './routes/vehicleDataRoutes';
// import agreementRoutes from './routes/agreements';
import oauthRoutes from './routes/oauthRoutes';

// Load and validate environment variables
import env, { PORT, getEnvironmentSummary } from './config/environment';

// Initialize Express app
const app = express();

// Log environment summary
console.log('🔧 Environment Configuration:');
console.table(getEnvironmentSummary());

// Initialize Database Service
import DatabaseService from './services/DatabaseService';

// Initialize Stripe with database key
let stripe: Stripe | null = null;

const initializeStripe = async () => {
  try {
    console.log('🔧 Initializing Stripe...');

    // Skip database lookup for now and use environment variable directly
    console.log('⚠️ Using environment variable for Stripe key');
    if (process.env.STRIPE_SECRET_KEY) {
      stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: '2025-06-30.basil' });
      console.log('✅ Stripe initialized with environment variable');
    } else {
      console.log('⚠️ No Stripe secret key found in environment variables');
    }
  } catch (error) {
    console.error('❌ Failed to initialize Stripe:', error);
    console.log('⚠️ Continuing without Stripe initialization');
  }
};

// Test database connection
const testDatabaseConnection = async () => {
  try {
    const isConnected = await DatabaseService.testConnection();
    if (isConnected) {
      console.log('✅ Database connected successfully');
    } else {
      console.log('❌ Database connection failed');
    }
  } catch (error) {
    console.log('❌ Database connection failed:', error);
  }
};

// Initialize Stripe on startup
initializeStripe();

// Enhanced Security Headers - Following bookcars security pattern
app.use(comprehensiveSecurityHeaders());

app.use(compression());
app.use(nocache());
app.use(cookieParser());
app.use(requestLogger);
app.use(connectionMonitoringMiddleware);

// Security monitoring middleware
comprehensiveSecurityMonitoring().forEach(middleware => {
  app.use(middleware);
});

// CORS Configuration
app.use(getCorsMiddleware());

// Rate Limiting
app.use(rateLimitLogger);
app.use('/api/auth', authRateLimiter);
app.use('/api/webhooks', webhookRateLimiter);
app.use('/api', readRateLimiter);
app.use(generalRateLimiter);

// Body parsing middleware
app.use('/api/webhooks/stripe', express.raw({ type: 'application/json' }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization middleware (after body parsing, before routes)
app.use(comprehensiveSanitization({
  skipFields: ['password'], // Don't sanitize password field (handled separately)
  maxLength: 10000,
  strictMode: true
}));

// Advanced API security middleware
comprehensiveAdvancedApiSecurity({
  supportedVersions: ['v1'],
  enableRequestSigning: false, // Enable in production with proper secret
  enableResponseEncryption: false, // Enable for sensitive endpoints
  signingSecret: config.JWT_SECRET,
  encryptionKey: config.JWT_SECRET
}).forEach(middleware => {
  app.use(middleware);
});

// Stripe webhook endpoint (must be before express.json middleware)
app.post('/api/webhooks/stripe', express.raw({ type: 'application/json' }), handleStripeWebhook);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'RentaHub API is running',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: process.env.NODE_ENV || 'development'
  });
});

// API version endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'RentaHub API',
    version: '1.0.0',
    status: 'online',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint to verify server is working
app.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Server is working!',
    timestamp: new Date().toISOString()
  });
});

// API Routes
console.log('🔧 Loading API routes...');
app.use('/api/saved-vehicles', savedVehiclesRoutes);
app.use('/api/auth', authRoutes);
// app.use('/api/users', userRoutes);
// app.use('/api/vehicles', vehicleRoutes);
// app.use('/api/bookings', bookingRoutes);
// app.use('/api/payments', paymentRoutes);
// app.use('/api/refunds', refundRoutes);
// app.use('/api/reviews', reviewRoutes);
// app.use('/api/analytics', analyticsRoutes);
// app.use('/api/admin', adminRoutes);
// app.use('/api/api-keys', apiKeyRoutes);
// app.use('/api/webhooks', webhookRoutes);
// app.use('/api/support', supportRoutes);
app.use('/api/vehicle-assistance', vehicleAssistanceRoutes);
// app.use('/api/vehicle-listings', vehicleListingRoutes);
// app.use('/api/vehicle-data', vehicleDataRoutes);
// app.use('/api/providers/agreements', agreementRoutes);
app.use('/api', healthRoutes);
// app.use('/oauth', oauthRoutes);

// Vehicle Catalog API routes (for autocomplete/reference data)
app.get('/api/vehicle-catalog', VehicleCatalogController.getAllVehicles);
app.get('/api/vehicle-catalog/search', VehicleCatalogController.searchVehicles);
app.get('/api/vehicle-catalog/suggestions', VehicleCatalogController.getVehicleSuggestions);
app.get('/api/vehicle-catalog/makes', VehicleCatalogController.getUniqueMakes);
app.get('/api/vehicle-catalog/make/:make', VehicleCatalogController.getVehiclesByMake);
app.get('/api/vehicle-catalog/health', VehicleCatalogController.healthCheck);
app.post('/api/vehicle-catalog/filter', VehicleCatalogController.filterVehicles);
app.get('/api/vehicle-catalog/:id', VehicleCatalogController.getVehicleById);

// Listed Vehicles API routes (for actual user listings)
app.get('/api/listed-vehicles', ListedVehicleController.getActiveVehicles);
app.get('/api/listed-vehicles/featured', ListedVehicleController.getFeaturedVehicles);
app.get('/api/listed-vehicles/search', ListedVehicleController.searchVehicles);
app.get('/api/listed-vehicles/stats', ListedVehicleController.getListingStats);
app.get('/api/listed-vehicles/check', ListedVehicleController.checkActiveListings);
app.get('/api/listed-vehicles/health', ListedVehicleController.healthCheck);
app.get('/api/listed-vehicles/:id', ListedVehicleController.getVehicleById);

// Provider API routes (for service providers)
app.get('/api/providers/health', ProviderController.healthCheck);
app.get('/api/providers/me', ProviderController.getCurrentProvider);
app.put('/api/providers/me', ProviderController.updateProvider);
app.get('/api/providers/dashboard/stats', ProviderController.getDashboardStats);

// Vehicle Management API routes (for providers) - Must come before generic :id route
app.get('/api/providers/vehicles', VehicleManagementController.getProviderVehicles);
app.post('/api/providers/vehicles', VehicleManagementController.createVehicle);
app.get('/api/providers/vehicles/:id', VehicleManagementController.getVehicleById);
app.put('/api/providers/vehicles/:id', VehicleManagementController.updateVehicle);
app.delete('/api/providers/vehicles/:id', VehicleManagementController.deleteVehicle);
app.get('/api/providers/vehicles/:id/availability', VehicleManagementController.getVehicleAvailability);

// Vehicle suggestions for providers
app.get('/api/providers/vehicle-suggestions', VehicleManagementController.getVehicleSuggestions);

// Booking Management API routes (for providers)
app.get('/api/providers/bookings/stats', BookingManagementController.getBookingStats);
app.get('/api/providers/bookings', BookingManagementController.getProviderBookings);
app.get('/api/providers/bookings/:id', BookingManagementController.getBookingById);
app.put('/api/providers/bookings/:id/status', BookingManagementController.updateBookingStatus);
app.post('/api/providers/bookings/:id/accept', BookingManagementController.acceptBooking);
app.post('/api/providers/bookings/:id/reject', BookingManagementController.rejectBooking);
app.post('/api/providers/bookings/:id/complete', BookingManagementController.completeBooking);

// Generic provider routes (must come after specific routes)
app.get('/api/providers', ProviderController.getAllProviders);
app.get('/api/providers/:id', ProviderController.getProvider);
console.log('✅ All API routes loaded successfully');

// Error handling middleware
// app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    await testDatabaseConnection();

    const server = app.listen(PORT, () => {
      console.log(`🚀 RentaHub API server running on port ${PORT}`);
      console.log(`📊 Health check available at http://localhost:${PORT}/health`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log('✅ All systems operational');
    });

    // Graceful shutdown
    const shutdown = (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Gracefully shutting down...`);
      server.close(async () => {
        console.log('🔌 HTTP server closed');
        // Database connection will be closed automatically
        console.log('🗄️ Database connection closed');
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

export default app;
