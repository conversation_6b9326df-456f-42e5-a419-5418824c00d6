// This file was previously using TypeORM, but the project now uses Prisma
// The User model is defined in prisma/schema.prisma
// This file is kept for reference but commented out to avoid import errors

/*
import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  BaseEntity
} from 'typeorm';

@Entity('users')
export class User extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  googleId: string;

  @Column({ nullable: true })
  profileImage: string;

  @Column({ 
    type: 'enum', 
    enum: ['customer', 'provider', 'admin'],
    default: 'customer'
  })
  role: 'customer' | 'provider' | 'admin';

  @Column({ 
    type: 'enum', 
    enum: ['active', 'pending', 'suspended'],
    default: 'active'
  })
  status: 'active' | 'pending' | 'suspended';

  @Column({ nullable: true })
  companyName: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
*/

// Export empty object to avoid import errors
export {} 