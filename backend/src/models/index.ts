// =============================================================================
// DATABASE MODELS & TYPES
// =============================================================================
// TypeScript interfaces for all database entities

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN' | 'SUPER_ADMIN';
  email_verified: boolean;
  phone_verified: boolean;
  avatar_url?: string;
  date_of_birth?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface Provider extends User {
  business_name?: string;
  business_license?: string;
  tax_id?: string;
  bank_account_number?: string;
  bank_name?: string;
  verification_status: 'PENDING' | 'VERIFIED' | 'REJECTED';
  verification_documents?: string[];
  rating?: number;
  total_reviews?: number;
  total_vehicles?: number;
  total_bookings?: number;
  total_earnings?: number;
  commission_rate?: number;
}

export interface Vehicle {
  id: string;
  provider_id: string;
  make: string;
  model: string;
  year: number;
  category: 'small_scooter' | 'large_scooter' | 'luxury_bike' | 'car' | 'motorcycle';
  daily_rate: number;
  hourly_rate?: number;
  weekly_rate?: number;
  monthly_rate?: number;
  status: 'available' | 'booked' | 'maintenance' | 'inactive';
  location_city: string;
  location_address?: string;
  location_lat?: number;
  location_lng?: number;
  description?: string;
  features?: string[];
  images?: string[];
  license_plate?: string;
  engine_size?: string;
  fuel_type?: 'gasoline' | 'electric' | 'hybrid';
  transmission?: 'manual' | 'automatic';
  seats?: number;
  helmet_included?: boolean;
  insurance_included?: boolean;
  deposit_required?: number;
  minimum_age?: number;
  rating?: number;
  total_reviews?: number;
  total_bookings?: number;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface Booking {
  id: string;
  customer_id: string;
  provider_id: string;
  vehicle_id: string;
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  pickup_location?: string;
  dropoff_location?: string;
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'no_show';
  total_amount: number;
  base_amount: number;
  tax_amount?: number;
  service_fee?: number;
  deposit_amount?: number;
  discount_amount?: number;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded' | 'partial_refund';
  payment_method?: 'stripe' | 'cash' | 'bank_transfer';
  stripe_payment_intent_id?: string;
  special_requests?: string;
  cancellation_reason?: string;
  cancelled_by?: string;
  cancelled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string;
  booking_id: string;
  customer_id: string;
  provider_id: string;
  amount: number;
  currency: string;
  payment_method: 'stripe' | 'cash' | 'bank_transfer';
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  stripe_payment_intent_id?: string;
  stripe_charge_id?: string;
  provider_payout_amount?: number;
  platform_fee?: number;
  processing_fee?: number;
  payout_status?: 'pending' | 'paid' | 'failed';
  payout_date?: string;
  refund_amount?: number;
  refund_reason?: string;
  refunded_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Review {
  id: string;
  booking_id: string;
  customer_id: string;
  provider_id: string;
  vehicle_id: string;
  rating: number;
  comment?: string;
  cleanliness_rating?: number;
  communication_rating?: number;
  accuracy_rating?: number;
  value_rating?: number;
  images?: string[];
  response?: string;
  responded_at?: string;
  is_featured?: boolean;
  created_at: string;
  updated_at: string;
}

export interface Analytics {
  id: string;
  date: string;
  provider_id?: string;
  total_bookings: number;
  total_revenue: number;
  total_vehicles: number;
  active_vehicles: number;
  occupancy_rate: number;
  average_booking_value: number;
  customer_acquisition_cost?: number;
  customer_lifetime_value?: number;
  conversion_rate?: number;
  cancellation_rate?: number;
  created_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  type: 'booking' | 'payment' | 'review' | 'system' | 'promotion';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

export interface ApiKey {
  id: string;
  user_id: string;
  name: string;
  key_hash: string;
  permissions: string[];
  last_used?: string;
  expires_at?: string;
  is_active: boolean;
  created_at: string;
}

// =============================================================================
// REQUEST/RESPONSE TYPES
// =============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  metadata?: any;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface VehicleSearchParams extends PaginationParams {
  category?: string;
  city?: string;
  minPrice?: number;
  maxPrice?: number;
  startDate?: string;
  endDate?: string;
  features?: string[];
}

export interface BookingCreateRequest {
  vehicle_id: string;
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  pickup_location?: string;
  dropoff_location?: string;
  special_requests?: string;
}

export interface PaymentCreateRequest {
  booking_id: string;
  payment_method: 'stripe';
  return_url?: string;
}

// =============================================================================
// ENUMS
// =============================================================================

export enum UserRole {
  CUSTOMER = 'CUSTOMER',
  PROVIDER = 'PROVIDER',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}

export enum VehicleCategory {
  SMALL_SCOOTER = 'small_scooter',
  LARGE_SCOOTER = 'large_scooter',
  LUXURY_BIKE = 'luxury_bike',
  CAR = 'car',
  MOTORCYCLE = 'motorcycle'
}

export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled'
}
