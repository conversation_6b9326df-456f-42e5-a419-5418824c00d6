// Performance Optimization Utilities
// This file contains utilities for improving API performance

import { Request, Response, NextFunction } from 'express';
import { createHash } from 'crypto';

// Response caching utility
export class ResponseCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  // Generate cache key from request
  private generateKey(req: Request): string {
    const { method, originalUrl, query, body } = req;
    const keyData = { method, url: originalUrl, query, body };
    return createHash('md5').update(JSON.stringify(keyData)).digest('hex');
  }

  // Set cache entry
  set(req: Request, data: any, ttlSeconds: number = 300): void {
    const key = this.generateKey(req);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    });
  }

  // Get cache entry
  get(req: Request): any | null {
    const key = this.generateKey(req);
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  // Clear cache
  clear(): void {
    this.cache.clear();
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
export const responseCache = new ResponseCache();

// Cache middleware
export const cacheMiddleware = (ttlSeconds: number = 300) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Check cache
    const cachedData = responseCache.get(req);
    if (cachedData) {
      console.log(`📦 Cache hit for ${req.originalUrl}`);
      return res.json(cachedData);
    }

    // Override res.json to cache response
    const originalJson = res.json;
    res.json = function(data: any) {
      // Cache successful responses only
      if (res.statusCode >= 200 && res.statusCode < 300) {
        responseCache.set(req, data, ttlSeconds);
        console.log(`💾 Cached response for ${req.originalUrl}`);
      }
      return originalJson.call(this, data);
    };

    next();
  };
};

// Request timing middleware
export const timingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`⏱️ ${req.method} ${req.originalUrl} - ${duration}ms`);
    
    // Add timing header
    res.set('X-Response-Time', `${duration}ms`);
    
    // Log slow requests
    if (duration > 1000) {
      console.warn(`🐌 Slow request detected: ${req.method} ${req.originalUrl} - ${duration}ms`);
    }
  });
  
  next();
};

// Memory usage monitoring
export const memoryMonitor = {
  getUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  },

  logUsage(): void {
    const usage = this.getUsage();
    console.log('📊 Memory Usage:', {
      rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(usage.external / 1024 / 1024)}MB`
    });
  },

  middleware: (req: Request, res: Response, next: NextFunction) => {
    const usage = memoryMonitor.getUsage();
    res.set('X-Memory-Usage', `${Math.round(usage.heapUsed / 1024 / 1024)}MB`);
    next();
  }
};

// Database query optimization helpers
export const queryOptimizer = {
  // Batch database operations
  batchQueries: async <T>(
    queries: (() => Promise<T>)[],
    batchSize: number = 10
  ): Promise<T[]> => {
    const results: T[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(query => query()));
      results.push(...batchResults);
    }
    
    return results;
  },

  // Add query timing
  timeQuery: async <T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> => {
    const startTime = Date.now();
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      console.log(`🗄️ Query ${queryName} completed in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Query ${queryName} failed after ${duration}ms:`, error);
      throw error;
    }
  }
};

// Response compression helpers
export const compressionHelpers = {
  // Check if response should be compressed
  shouldCompress: (req: Request, res: Response): boolean => {
    // Don't compress if client doesn't support it
    if (!req.headers['accept-encoding']?.includes('gzip')) {
      return false;
    }

    // Don't compress small responses
    const contentLength = res.get('content-length');
    if (contentLength && parseInt(contentLength) < 1024) {
      return false;
    }

    // Don't compress already compressed content
    const contentType = res.get('content-type');
    if (contentType?.includes('image/') || contentType?.includes('video/')) {
      return false;
    }

    return true;
  }
};

// Request deduplication
export class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();

  // Generate request key
  private generateKey(req: Request): string {
    const { method, originalUrl, query, body } = req;
    const keyData = { method, url: originalUrl, query, body };
    return createHash('md5').update(JSON.stringify(keyData)).digest('hex');
  }

  // Deduplicate identical requests
  async deduplicate<T>(req: Request, handler: () => Promise<T>): Promise<T> {
    const key = this.generateKey(req);
    
    // If identical request is already pending, wait for it
    if (this.pendingRequests.has(key)) {
      console.log(`🔄 Deduplicating request: ${req.originalUrl}`);
      return this.pendingRequests.get(key) as Promise<T>;
    }

    // Execute the request
    const promise = handler().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, promise);
    return promise;
  }
}

// Global deduplicator instance
export const requestDeduplicator = new RequestDeduplicator();

// Performance monitoring middleware
export const performanceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
    
    // Add performance headers
    res.set('X-Response-Time', `${duration.toFixed(2)}ms`);
    res.set('X-Memory-Delta', `${Math.round(memoryDelta / 1024)}KB`);
    
    // Log performance metrics
    if (duration > 500 || Math.abs(memoryDelta) > 1024 * 1024) { // Log if > 500ms or > 1MB memory change
      console.log(`📈 Performance: ${req.method} ${req.originalUrl}`, {
        duration: `${duration.toFixed(2)}ms`,
        memoryDelta: `${Math.round(memoryDelta / 1024)}KB`,
        statusCode: res.statusCode
      });
    }
  });

  next();
};

// Cleanup function for periodic maintenance
export const performanceCleanup = () => {
  console.log('🧹 Running performance cleanup...');
  
  // Clean expired cache entries
  responseCache.cleanup();
  
  // Log memory usage
  memoryMonitor.logUsage();
  
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
    console.log('🗑️ Garbage collection triggered');
  }
};

// Schedule cleanup every 5 minutes
setInterval(performanceCleanup, 5 * 60 * 1000);
