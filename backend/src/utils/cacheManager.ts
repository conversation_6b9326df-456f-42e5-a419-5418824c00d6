import Redis from 'ioredis';
import { createHash } from 'crypto';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string;
}

export class CacheManager {
  private redis: Redis;
  private defaultTTL: number;

  constructor(redisUrl?: string) {
    this.redis = new Redis(redisUrl || process.env.REDIS_URL || 'redis://localhost:6379');
    this.defaultTTL = 3600; // 1 hour default cache time
  }

  /**
   * Generate a consistent hash key
   * @param key Original key
   * @param prefix Optional prefix
   */
  private generateKey(key: string, prefix?: string): string {
    const hash = createHash('sha256').update(key).digest('hex');
    return prefix ? `${prefix}:${hash}` : hash;
  }

  /**
   * Set a value in cache
   * @param key Cache key
   * @param value Value to cache
   * @param options Caching options
   */
  async set(
    key: string, 
    value: any, 
    options: CacheOptions = {}
  ): Promise<void> {
    const { 
      ttl = this.defaultTTL, 
      prefix = 'rentahub' 
    } = options;

    const cacheKey = this.generateKey(key, prefix);
    const serializedValue = JSON.stringify(value);

    await this.redis.set(cacheKey, serializedValue, 'EX', ttl);
  }

  /**
   * Get a value from cache
   * @param key Cache key
   * @param options Caching options
   */
  async get<T = any>(
    key: string, 
    options: CacheOptions = {}
  ): Promise<T | null> {
    const { 
      prefix = 'rentahub' 
    } = options;

    const cacheKey = this.generateKey(key, prefix);
    const cachedValue = await this.redis.get(cacheKey);

    return cachedValue ? JSON.parse(cachedValue) : null;
  }

  /**
   * Delete a value from cache
   * @param key Cache key
   * @param options Caching options
   */
  async delete(
    key: string, 
    options: CacheOptions = {}
  ): Promise<void> {
    const { 
      prefix = 'rentahub' 
    } = options;

    const cacheKey = this.generateKey(key, prefix);
    await this.redis.del(cacheKey);
  }

  /**
   * Memoize a function with caching
   * @param fn Function to memoize
   * @param options Caching options
   */
  memoize<T extends (...args: any[]) => Promise<any>>(
    fn: T, 
    options: CacheOptions = {}
  ): T {
    return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      const { 
        ttl = this.defaultTTL, 
        prefix = 'rentahub:memoize' 
      } = options;

      // Create a unique key based on function name and arguments
      const key = `${fn.name}:${JSON.stringify(args)}`;
      const cacheKey = this.generateKey(key, prefix);

      // Check cache first
      const cachedResult = await this.get(cacheKey);
      if (cachedResult !== null) {
        return cachedResult;
      }

      // Execute function and cache result
      const result = await fn(...args);
      await this.set(cacheKey, result, { ttl, prefix });

      return result;
    }) as T;
  }

  /**
   * Clear all cached entries with a specific prefix
   * @param prefix Prefix to clear
   */
  async clearPrefix(prefix: string): Promise<void> {
    const keys = await this.redis.keys(`${prefix}:*`);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }

  /**
   * Close Redis connection
   */
  async close(): Promise<void> {
    await this.redis.quit();
  }
}

export default new CacheManager(); 