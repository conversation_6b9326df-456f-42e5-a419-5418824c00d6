import jwt from 'jsonwebtoken';
// import { User } from '../models/User'; // Using Prisma User type instead
import { User } from '@prisma/client';

import { config } from '../config/env';

const JWT_SECRET = config.JWT_SECRET;
const TOKEN_EXPIRATION = '30d';

export function generateToken(user: any) {
  return jwt.sign(
    { 
      id: user.id, 
      email: user.email, 
      role: user.role 
    }, 
    JWT_SECRET, 
    { expiresIn: TOKEN_EXPIRATION }
  );
}

export function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

export function decodeToken(token: string) {
  return jwt.decode(token);
} 