import { createClient } from '@supabase/supabase-js';
import { logger } from './logger';

// Environment variables for Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;

// Validate required environment variables
if (!SUPABASE_URL) {
  logger.error('SUPABASE_URL environment variable is required');
  process.exit(1);
}

if (!SUPABASE_SERVICE_ROLE_KEY) {
  logger.error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

if (!SUPABASE_ANON_KEY) {
  logger.error('SUPABASE_ANON_KEY environment variable is required');
  process.exit(1);
}

// Initialize Supabase client with service role key for admin operations
export const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Initialize Supabase client with anon key for regular operations
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

export interface MonitoringMetrics {
  activeConnections: number;
  databaseSize: number;
  queryPerformance: {
    avgResponseTime: number;
    slowQueries: number;
  };
  errorRate: number;
  uptime: number;
}

export class SupabaseMonitor {
  private static instance: SupabaseMonitor;
  private metrics: MonitoringMetrics;
  private lastCheck: Date;

  private constructor() {
    this.metrics = {
      activeConnections: 0,
      databaseSize: 0,
      queryPerformance: {
        avgResponseTime: 0,
        slowQueries: 0
      },
      errorRate: 0,
      uptime: 0
    };
    this.lastCheck = new Date();
  }

  public static getInstance(): SupabaseMonitor {
    if (!SupabaseMonitor.instance) {
      SupabaseMonitor.instance = new SupabaseMonitor();
    }
    return SupabaseMonitor.instance;
  }

  /**
   * Check Supabase connection health
   */
  async checkConnectionHealth(): Promise<boolean> {
    try {
      const startTime = Date.now();
      
      // Test basic connection
      const { data, error } = await supabase
        .from('users')
        .select('count')
        .limit(1);

      const responseTime = Date.now() - startTime;

      if (error) {
        logger.error('Supabase connection health check failed:', error);
        return false;
      }

      logger.info('Supabase connection health check passed', {
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Supabase connection health check error:', error);
      return false;
    }
  }

  /**
   * Get database metrics
   */
  async getDatabaseMetrics(): Promise<MonitoringMetrics> {
    try {
      const startTime = Date.now();

      // Get basic metrics
      const { data: userCount } = await supabase
        .from('users')
        .select('id', { count: 'exact' });

      const { data: vehicleCount } = await supabase
        .from('vehicles')
        .select('id', { count: 'exact' });

      const { data: bookingCount } = await supabase
        .from('bookings')
        .select('id', { count: 'exact' });

      const responseTime = Date.now() - startTime;

      // Update metrics
      this.metrics = {
        activeConnections: (userCount?.length || 0) + (vehicleCount?.length || 0) + (bookingCount?.length || 0),
        databaseSize: this.estimateDatabaseSize(userCount?.length || 0, vehicleCount?.length || 0, bookingCount?.length || 0),
        queryPerformance: {
          avgResponseTime: responseTime,
          slowQueries: responseTime > 1000 ? 1 : 0
        },
        errorRate: 0, // Would need to track errors over time
        uptime: Date.now() - this.lastCheck.getTime()
      };

      this.lastCheck = new Date();

      logger.info('Database metrics collected', {
        metrics: this.metrics,
        timestamp: new Date().toISOString()
      });

      return this.metrics;
    } catch (error) {
      logger.error('Failed to get database metrics:', error);
      return this.metrics;
    }
  }

  /**
   * Estimate database size based on record counts
   */
  private estimateDatabaseSize(userCount: number, vehicleCount: number, bookingCount: number): number {
    // Rough estimation: 1KB per user, 2KB per vehicle, 3KB per booking
    const estimatedSize = (userCount * 1024) + (vehicleCount * 2048) + (bookingCount * 3072);
    return estimatedSize;
  }

  /**
   * Log database performance issues
   */
  async logPerformanceIssue(issue: string, details: any): Promise<void> {
    try {
      const { error } = await supabaseAdmin
        .from('performance_logs')
        .insert({
          issue,
          details: JSON.stringify(details),
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development'
        });

      if (error) {
        logger.error('Failed to log performance issue:', error);
      } else {
        logger.warn('Performance issue logged:', { issue, details });
      }
    } catch (error) {
      logger.error('Error logging performance issue:', error);
    }
  }

  /**
   * Monitor query performance
   */
  async monitorQueryPerformance<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const responseTime = Date.now() - startTime;

      // Log slow queries
      if (responseTime > 1000) {
        await this.logPerformanceIssue('Slow Query', {
          queryName,
          responseTime,
          timestamp: new Date().toISOString()
        });
      }

      logger.debug('Query performance monitored', {
        queryName,
        responseTime: `${responseTime}ms`,
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      await this.logPerformanceIssue('Query Error', {
        queryName,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): MonitoringMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      activeConnections: 0,
      databaseSize: 0,
      queryPerformance: {
        avgResponseTime: 0,
        slowQueries: 0
      },
      errorRate: 0,
      uptime: 0
    };
    this.lastCheck = new Date();
  }
}

// Export singleton instance
export const supabaseMonitor = SupabaseMonitor.getInstance(); 