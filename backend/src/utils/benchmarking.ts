import { performance } from 'perf_hooks';
import v8 from 'v8';
import fs from 'fs';
import path from 'path';

interface BenchmarkResult {
  name: string;
  duration: number;
  memoryUsed: number;
  gcStats?: {
    totalHeapSize: number;
    usedHeapSize: number;
    heapSizeLimit: number;
  };
}

export class PerformanceBenchmark {
  private results: BenchmarkResult[] = [];
  private outputDir: string;

  constructor(outputDir?: string) {
    this.outputDir = outputDir || path.join(process.cwd(), 'performance-logs');
    
    // Ensure output directory exists
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Benchmark a synchronous function
   * @param name Benchmark name
   * @param fn Function to benchmark
   * @param iterations Number of iterations
   */
  benchmarkSync(name: string, fn: () => void, iterations = 1000): BenchmarkResult {
    const startMemory = process.memoryUsage().heapUsed;
    const startTime = performance.now();

    for (let i = 0; i < iterations; i++) {
      fn();
    }

    const endTime = performance.now();
    const endMemory = process.memoryUsage().heapUsed;

    const result: BenchmarkResult = {
      name,
      duration: endTime - startTime,
      memoryUsed: endMemory - startMemory,
      gcStats: this.getGarbageCollectionStats()
    };

    this.results.push(result);
    return result;
  }

  /**
   * Benchmark an asynchronous function
   * @param name Benchmark name
   * @param fn Async function to benchmark
   * @param iterations Number of iterations
   */
  async benchmarkAsync(
    name: string, 
    fn: () => Promise<void>, 
    iterations = 1000
  ): Promise<BenchmarkResult> {
    const startMemory = process.memoryUsage().heapUsed;
    const startTime = performance.now();

    for (let i = 0; i < iterations; i++) {
      await fn();
    }

    const endTime = performance.now();
    const endMemory = process.memoryUsage().heapUsed;

    const result: BenchmarkResult = {
      name,
      duration: endTime - startTime,
      memoryUsed: endMemory - startMemory,
      gcStats: this.getGarbageCollectionStats()
    };

    this.results.push(result);
    return result;
  }

  /**
   * Get garbage collection statistics
   */
  private getGarbageCollectionStats() {
    const heapStats = v8.getHeapStatistics();
    return {
      totalHeapSize: heapStats.total_heap_size,
      usedHeapSize: heapStats.used_heap_size,
      heapSizeLimit: heapStats.heap_size_limit
    };
  }

  /**
   * Compare benchmark results
   * @param results Benchmark results to compare
   */
  compareResults(results?: BenchmarkResult[]): { 
    fastest: BenchmarkResult; 
    slowest: BenchmarkResult;
    averageDuration: number;
  } {
    const benchmarkResults = results || this.results;
    
    if (benchmarkResults.length === 0) {
      throw new Error('No benchmark results to compare');
    }

    const sortedByDuration = [...benchmarkResults].sort((a, b) => a.duration - b.duration);
    const averageDuration = benchmarkResults.reduce((sum, result) => sum + result.duration, 0) / benchmarkResults.length;

    return {
      fastest: sortedByDuration[0],
      slowest: sortedByDuration[sortedByDuration.length - 1],
      averageDuration
    };
  }

  /**
   * Export benchmark results to a JSON file
   * @param filename Optional filename (defaults to timestamp)
   */
  exportResults(filename?: string): string {
    const outputFilename = filename || `benchmark-${Date.now()}.json`;
    const outputPath = path.join(this.outputDir, outputFilename);

    fs.writeFileSync(
      outputPath, 
      JSON.stringify(this.results, null, 2)
    );

    return outputPath;
  }

  /**
   * Profile memory usage of a function
   * @param name Profile name
   * @param fn Function to profile
   */
  profileMemory(name: string, fn: () => void): { 
    memoryBefore: number; 
    memoryAfter: number; 
    memoryDiff: number 
  } {
    const memoryBefore = process.memoryUsage().heapUsed;
    fn();
    const memoryAfter = process.memoryUsage().heapUsed;

    return {
      memoryBefore,
      memoryAfter,
      memoryDiff: memoryAfter - memoryBefore
    };
  }

  /**
   * Clear all previous benchmark results
   */
  clearResults(): void {
    this.results = [];
  }
}

export default new PerformanceBenchmark(); 