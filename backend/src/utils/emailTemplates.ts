import { NotificationType } from '../types/NotificationTypes';

export const EmailTemplates = {
  [NotificationType.BOOKING_CONFIRMATION]: {
    subject: (data: any) => `Booking Confirmation - ${data.bookingReference}`,
    body: (data: any) => `
      <h1>Booking Confirmed</h1>
      <p>Dear ${data.customerName || data.providerName},</p>
      <p>Your booking for ${data.vehicleModel} has been confirmed.</p>
      <ul>
        <li>Booking Reference: ${data.bookingReference}</li>
        <li>Start Date: ${data.startDate}</li>
        <li>Total Price: $${data.totalPrice}</li>
      </ul>
    `
  },
  [NotificationType.BOOKING_CANCELLATION]: {
    subject: (data: any) => `Booking Cancelled - ${data.bookingReference}`,
    body: (data: any) => `
      <h1>Booking Cancelled</h1>
      <p>Dear ${data.customerName || data.providerName},</p>
      <p>The booking for ${data.vehicleModel} has been cancelled.</p>
      <ul>
        <li>Booking Reference: ${data.bookingReference}</li>
        <li>Cancellation Reason: ${data.cancellationReason}</li>
      </ul>
    `
  },
  [NotificationType.BOOKING_AMENDMENT]: {
    subject: (data: any) => `Booking Amendment - ${data.bookingReference}`,
    body: (data: any) => `
      <h1>Booking Amended</h1>
      <p>Dear ${data.customerName || data.providerName},</p>
      <p>Your booking for ${data.vehicleModel} has been amended.</p>
      <ul>
        <li>Booking Reference: ${data.bookingReference}</li>
        <li>Amendments: ${data.amendments}</li>
      </ul>
    `
  },
  [NotificationType.BOOKING_REMINDER]: {
    subject: (data: any) => `Booking Reminder - ${data.bookingReference}`,
    body: (data: any) => `
      <h1>Booking Reminder</h1>
      <p>Dear ${data.customerName || data.providerName},</p>
      <p>This is a reminder about your upcoming booking for ${data.vehicleModel}.</p>
      <ul>
        <li>Booking Reference: ${data.bookingReference}</li>
        <li>Start Date: ${data.startDate}</li>
        <li>Pickup Location: ${data.pickupLocation}</li>
      </ul>
    `
  },
  [NotificationType.BOOKING_COMPLETION]: {
    subject: (data: any) => `Booking Completed - ${data.bookingReference}`,
    body: (data: any) => `
      <h1>Booking Completed</h1>
      <p>Dear ${data.customerName || data.providerName},</p>
      <p>Your booking for ${data.vehicleModel} has been completed.</p>
      <ul>
        <li>Booking Reference: ${data.bookingReference}</li>
        <li>Completion Date: ${data.completedDate}</li>
        <li>Total Paid: $${data.totalPaid}</li>
      </ul>
    `
  },

  // Add getTemplate method
  getTemplate: (type: NotificationType, data: any) => {
    const template = EmailTemplates[type];
    if (!template) {
      throw new Error(`Email template not found for type: ${type}`);
    }

    return {
      subject: template.subject(data),
      html: template.body(data),
      text: template.body(data).replace(/<[^>]*>/g, '') // Strip HTML tags for text version
    };
  }
};
