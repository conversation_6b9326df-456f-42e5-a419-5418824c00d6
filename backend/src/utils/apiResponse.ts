import { Response } from 'express';

// Standardized API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
  timestamp: string;
}

// Success response helper
export const successResponse = <T>(
  res: Response,
  data?: T,
  message?: string,
  statusCode: number = 200,
  meta?: ApiResponse['meta']
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    meta,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
};

// Error response helper
export const errorResponse = (
  res: Response,
  error: string,
  statusCode: number = 400,
  errors?: string[]
): Response => {
  const response: ApiResponse = {
    success: false,
    error,
    errors,
    timestamp: new Date().toISOString()
  };

  return res.status(statusCode).json(response);
};

// Validation error response
export const validationErrorResponse = (
  res: Response,
  errors: string[],
  message: string = 'Validation failed'
): Response => {
  return errorResponse(res, message, 422, errors);
};

// Not found response
export const notFoundResponse = (
  res: Response,
  resource: string = 'Resource'
): Response => {
  return errorResponse(res, `${resource} not found`, 404);
};

// Unauthorized response
export const unauthorizedResponse = (
  res: Response,
  message: string = 'Unauthorized access'
): Response => {
  return errorResponse(res, message, 401);
};

// Forbidden response
export const forbiddenResponse = (
  res: Response,
  message: string = 'Access forbidden'
): Response => {
  return errorResponse(res, message, 403);
};

// Server error response
export const serverErrorResponse = (
  res: Response,
  message: string = 'Internal server error'
): Response => {
  return errorResponse(res, message, 500);
};

// Paginated response helper
export const paginatedResponse = <T>(
  res: Response,
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): Response => {
  const totalPages = Math.ceil(total / limit);
  
  return successResponse(res, data, message, 200, {
    page,
    limit,
    total,
    totalPages
  });
};

// Created response helper
export const createdResponse = <T>(
  res: Response,
  data: T,
  message: string = 'Resource created successfully'
): Response => {
  return successResponse(res, data, message, 201);
};

// Updated response helper
export const updatedResponse = <T>(
  res: Response,
  data: T,
  message: string = 'Resource updated successfully'
): Response => {
  return successResponse(res, data, message, 200);
};

// Deleted response helper
export const deletedResponse = (
  res: Response,
  message: string = 'Resource deleted successfully'
): Response => {
  return successResponse(res, null, message, 200);
};

// No content response helper
export const noContentResponse = (res: Response): Response => {
  return res.status(204).send();
};
