/**
 * RentaHub API Standards and Conventions
 * 
 * This file defines the standardized patterns for API endpoints,
 * response formats, and routing conventions across the RentaHub platform.
 */

// Standard API URL patterns
export const API_PATTERNS = {
  // Resource-based patterns (RESTful)
  COLLECTION: '/api/{resource}',           // GET /api/vehicles
  ITEM: '/api/{resource}/{id}',            // GET /api/vehicles/123
  CREATE: '/api/{resource}',               // POST /api/vehicles
  UPDATE: '/api/{resource}/{id}',          // PUT /api/vehicles/123
  PATCH: '/api/{resource}/{id}',           // PATCH /api/vehicles/123
  DELETE: '/api/{resource}/{id}',          // DELETE /api/vehicles/123
  
  // Action-based patterns (for non-CRUD operations)
  ACTION: '/api/{resource}/{id}/{action}', // POST /api/vehicles/123/activate
  SEARCH: '/api/{resource}/search',        // GET /api/vehicles/search
  FILTER: '/api/{resource}/filter',        // POST /api/vehicles/filter
  
  // Nested resource patterns
  NESTED: '/api/{parent}/{id}/{child}',    // GET /api/providers/123/vehicles
  NESTED_ITEM: '/api/{parent}/{id}/{child}/{childId}', // GET /api/providers/123/vehicles/456
};

// Standard HTTP status codes usage
export const STATUS_CODES = {
  // Success
  OK: 200,                    // Successful GET, PUT, PATCH
  CREATED: 201,               // Successful POST
  ACCEPTED: 202,              // Accepted for processing
  NO_CONTENT: 204,            // Successful DELETE
  
  // Client errors
  BAD_REQUEST: 400,           // Invalid request data
  UNAUTHORIZED: 401,          // Authentication required
  FORBIDDEN: 403,             // Access denied
  NOT_FOUND: 404,             // Resource not found
  METHOD_NOT_ALLOWED: 405,    // HTTP method not allowed
  CONFLICT: 409,              // Resource conflict
  UNPROCESSABLE_ENTITY: 422,  // Validation errors
  TOO_MANY_REQUESTS: 429,     // Rate limit exceeded
  
  // Server errors
  INTERNAL_SERVER_ERROR: 500, // Server error
  NOT_IMPLEMENTED: 501,       // Feature not implemented
  BAD_GATEWAY: 502,           // Gateway error
  SERVICE_UNAVAILABLE: 503,   // Service unavailable
};

// Standard query parameters
export const QUERY_PARAMS = {
  // Pagination
  PAGE: 'page',               // ?page=1
  LIMIT: 'limit',             // ?limit=20
  OFFSET: 'offset',           // ?offset=0
  
  // Sorting
  SORT: 'sort',               // ?sort=name
  ORDER: 'order',             // ?order=asc|desc
  
  // Filtering
  FILTER: 'filter',           // ?filter[status]=active
  SEARCH: 'q',                // ?q=search+term
  
  // Field selection
  FIELDS: 'fields',           // ?fields=id,name,email
  INCLUDE: 'include',         // ?include=profile,vehicles
  EXCLUDE: 'exclude',         // ?exclude=password,tokens
};

// Standard response field names
export const RESPONSE_FIELDS = {
  SUCCESS: 'success',
  DATA: 'data',
  MESSAGE: 'message',
  ERROR: 'error',
  ERRORS: 'errors',
  META: 'meta',
  TIMESTAMP: 'timestamp',
};

// Standard error types
export const ERROR_TYPES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
};

// Standard route naming conventions
export const ROUTE_CONVENTIONS = {
  // Use plural nouns for collections
  COLLECTIONS: ['vehicles', 'users', 'bookings', 'payments'],
  
  // Use kebab-case for multi-word resources
  MULTI_WORD: ['vehicle-listings', 'payment-methods', 'user-profiles'],
  
  // Use consistent action names
  ACTIONS: {
    ACTIVATE: 'activate',
    DEACTIVATE: 'deactivate',
    APPROVE: 'approve',
    REJECT: 'reject',
    CANCEL: 'cancel',
    COMPLETE: 'complete',
    VERIFY: 'verify',
    RESET: 'reset',
  },
};

// Standard middleware order
export const MIDDLEWARE_ORDER = [
  'helmet',           // Security headers
  'compression',      // Response compression
  'cors',            // CORS handling
  'rateLimiting',    // Rate limiting
  'bodyParser',      // Request parsing
  'authentication',  // Auth verification
  'authorization',   // Permission checks
  'validation',      // Input validation
  'controller',      // Business logic
  'errorHandler',    // Error handling
];

// Standard validation patterns
export const VALIDATION_PATTERNS = {
  ID: /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, // UUID
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
};

export default {
  API_PATTERNS,
  STATUS_CODES,
  QUERY_PARAMS,
  RESPONSE_FIELDS,
  ERROR_TYPES,
  ROUTE_CONVENTIONS,
  MIDDLEWARE_ORDER,
  VALIDATION_PATTERNS,
};
