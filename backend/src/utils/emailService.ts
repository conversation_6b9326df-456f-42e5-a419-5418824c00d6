import nodemailer from 'nodemailer';
import { config } from '../config/env';

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    // Configure email transporter
    this.transporter = nodemailer.createTransport({
      host: config.EMAIL_HOST,
      port: config.EMAIL_PORT,
      secure: config.EMAIL_SECURE,
      auth: {
        user: config.EMAIL_USER,
        pass: config.EMAIL_PASS
      }
    });
  }

  // Send password reset email
  async sendPasswordResetEmail(email: string, resetLink: string): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: `"RentaHub" <${config.EMAIL_FROM}>`,
        to: email,
        subject: 'Password Reset Request',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Password Reset Request</h2>
            <p>You have requested to reset your password for your RentaHub account.</p>
            <p>Click the link below to reset your password. This link will expire in 1 hour:</p>
            <p><a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
            <small>This is an automated message. Please do not reply.</small>
          </div>
        `
      });
    } catch (error) {
      console.error('Password reset email error:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  // Send email verification email
  async sendVerificationEmail(email: string, verificationLink: string): Promise<void> {
    try {
      await this.transporter.sendMail({
        from: `"RentaHub" <${config.EMAIL_FROM}>`,
        to: email,
        subject: 'Verify Your Email',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Verify Your Email Address</h2>
            <p>Thank you for signing up with RentaHub! To complete your registration, please verify your email address.</p>
            <p>Click the link below to verify your email. This link will expire in 24 hours:</p>
            <p><a href="${verificationLink}" style="background-color: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a></p>
            <p>If you did not create an account, please ignore this email.</p>
            <small>This is an automated message. Please do not reply.</small>
          </div>
        `
      });
    } catch (error) {
      console.error('Email verification email error:', error);
      throw new Error('Failed to send verification email');
    }
  }

  // Send account status notification
  async sendAccountStatusNotification(email: string, status: 'activated' | 'suspended' | 'banned'): Promise<void> {
    try {
      const statusMessages = {
        'activated': 'Your account has been successfully activated.',
        'suspended': 'Your account has been temporarily suspended.',
        'banned': 'Your account has been permanently banned.'
      };

      await this.transporter.sendMail({
        from: `"RentaHub" <${config.EMAIL_FROM}>`,
        to: email,
        subject: 'Account Status Update',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Account Status Update</h2>
            <p>${statusMessages[status]}</p>
            <p>If you have any questions, please contact our support team.</p>
            <small>This is an automated message. Please do not reply.</small>
          </div>
        `
      });
    } catch (error) {
      console.error('Account status notification error:', error);
      throw new Error('Failed to send account status notification');
    }
  }
}

export default new EmailService();