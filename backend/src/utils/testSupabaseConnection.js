const { supabase } = require('./supabaseClient');

async function runConnectionTest() {
  try {
    console.log('🔍 Detailed Supabase Connection Test');
    
    // Attempt a more detailed query
    console.log('🔍 Attempting detailed query...');
    const { data, error } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);

    if (error) {
      console.error('❌ Query failed:', error);
      process.exit(1);
    }

    console.log('✅ Query successful');
    console.log('📊 Sample data:', data);
    
    process.exit(0);
  } catch (err) {
    console.error('❌ Unexpected error:', err);
    process.exit(1);
  }
}

runConnectionTest();
