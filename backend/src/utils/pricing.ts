import { Vehicle, Booking } from '@prisma/client';

// Extended booking interface for pricing calculations
interface BookingExtended {
  startDate?: Date;
  endDate?: Date;
  selectedAddOns?: any[];
  deliveryRequested?: boolean;
  deliveryAddress?: string;
  deliveryDistance?: number;
  totalDays?: number;
  vehicleRate?: number;
  addOnsTotal?: number;
  deliveryFee?: number;
  commissionRate?: number;
  commissionAmount?: number;
}

/**
 * Calculate booking total with advanced pricing logic
 * @param vehicle Vehicle details
 * @param bookingData Partial booking information
 * @returns Calculated booking totals
 */
export function calculateBookingTotal(
  vehicle: Vehicle, 
  bookingData: any
): any {
  // Validate required dates
  if (!bookingData.startDate || !bookingData.endDate) {
    throw new Error('Start and end dates are required');
  }

  // Calculate total days
  const startDate = new Date(bookingData.startDate);
  const endDate = new Date(bookingData.endDate);
  const totalDays = Math.max(1, Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24)
  ));

  // Dynamic rate selection based on booking duration
  const rate = selectOptimalRate(vehicle, totalDays);

  // Base vehicle rate
  const vehicleRate = rate * totalDays;

  // Add-ons calculation
  const addOnsTotal = calculateAddOnsTotal(
    vehicle.addOns || [], 
    bookingData.selectedAddOns || []
  );

  // Delivery fee
  const deliveryFee = calculateDeliveryFee(vehicle, bookingData);

  // Subtotal calculation
  const subtotal = vehicleRate + addOnsTotal + deliveryFee;

  // Commission calculation (default 15%)
  const commissionRate = 0.15;
  const commissionAmount = subtotal * commissionRate;

  // Total calculation
  const total = subtotal + commissionAmount;

  return {
    totalDays: totalDays,
    vehicleRate: vehicleRate,
    addOnsTotal: addOnsTotal,
    deliveryFee: deliveryFee,
    subtotal,
    commissionRate: commissionRate,
    commissionAmount: commissionAmount,
    total
  };
}

/**
 * Select optimal rate based on booking duration
 * @param vehicle Vehicle details
 * @param totalDays Number of rental days
 * @returns Applicable daily rate
 */
function selectOptimalRate(vehicle: Vehicle, totalDays: number): number {
  if (totalDays >= 30) return Number(vehicle.monthlyRate) / 30;
  if (totalDays >= 7) return Number(vehicle.weeklyRate) / 7;
  return Number(vehicle.dailyRate);
}

/**
 * Calculate add-ons total
 * @param availableAddOns Vehicle's available add-ons
 * @param selectedAddOns User-selected add-ons
 * @returns Total add-ons cost
 */
function calculateAddOnsTotal(
  availableAddOns: any[], 
  selectedAddOns: any[]
): number {
  return selectedAddOns.reduce((total, selectedAddOn) => {
    const matchingAddOn = availableAddOns.find(
      addOn => addOn.id === selectedAddOn.id
    );
    return total + (matchingAddOn?.price || 0);
  }, 0);
}

/**
 * Calculate delivery fee with provider-defined options
 * @param vehicle Vehicle details
 * @param bookingData Booking information
 * @returns Calculated delivery fee
 */
function calculateDeliveryFee(
  vehicle: Vehicle, 
  bookingData: any
): number {
  // If delivery not requested or not available
  if (!bookingData.deliveryRequested || !vehicle.deliveryAvailable) {
    return 0;
  }

  // Use delivery options from vehicle
  const deliveryOptions = (vehicle.deliveryOptions as any) || {
    isFreeDelivery: false,
    baseDeliveryFee: 0,
    maxFreeDeliveryRadius: 0,
    additionalKmRate: 0,
    deliveryLocations: []
  };

  // Free delivery case
  if (deliveryOptions.isFreeDelivery) {
    return 0;
  }

  // Check if delivery address is in allowed locations
  if (deliveryOptions.deliveryLocations.length > 0) {
    const isValidLocation = deliveryOptions.deliveryLocations.some(
      location => bookingData.deliveryAddress?.includes(location)
    );
    
    if (!isValidLocation) {
      throw new Error('Delivery to this location is not supported');
    }
  }

  // Calculate delivery fee
  let deliveryFee = deliveryOptions.baseDeliveryFee;

  // Add distance-based fee if beyond free radius
  if (bookingData.deliveryDistance && 
      bookingData.deliveryDistance > deliveryOptions.maxFreeDeliveryRadius) {
    const extraDistance = bookingData.deliveryDistance - deliveryOptions.maxFreeDeliveryRadius;
    deliveryFee += extraDistance * deliveryOptions.additionalKmRate;
  }

  return Math.max(0, deliveryFee);
}

/**
 * Calculate additional fee based on delivery distance
 * @param vehicle Vehicle details
 * @param bookingData Booking information
 * @returns Distance-based delivery fee
 */
function calculateDistanceFee(
  vehicle: Vehicle, 
  bookingData: any
): number {
  if (!bookingData.deliveryAddress) return 0;

  // Placeholder for distance calculation
  // In a real implementation, use a geocoding service to calculate actual distance
  const estimatedDistance = 10; // km
  const distanceRate = 2; // $ per km

  return Math.max(
    0, 
    (estimatedDistance - (Number(vehicle.deliveryRadius) || 0)) * distanceRate
  );
}

/**
 * Apply dynamic discounts
 * @param total Total booking amount
 * @param user User details
 * @param vehicle Vehicle details
 * @returns Discounted total
 */
export function applyDynamicDiscounts(
  total: number, 
  user: any, 
  vehicle: Vehicle
): number {
  let discountedTotal = total;

  // Loyalty discount
  if (user.total_bookings > 10) {
    discountedTotal *= 0.9; // 10% off
  }

  // Seasonal/off-peak discount
  const currentDate = new Date();
  const isOffPeakSeason = 
    (currentDate.getMonth() >= 9 && currentDate.getMonth() <= 11) || // Off-season months
    (currentDate.getMonth() >= 1 && currentDate.getMonth() <= 3);

  if (isOffPeakSeason) {
    discountedTotal *= 0.85; // 15% off
  }

  // Vehicle-specific long-term rental discount
  if (total >= Number(vehicle.monthlyRate) * 2) {
    discountedTotal *= 0.8; // 20% off for extended rentals
  }

  return discountedTotal;
}

export default {
  calculateBookingTotal,
  applyDynamicDiscounts
}; 