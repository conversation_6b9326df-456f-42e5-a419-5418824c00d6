export const supportTicketTemplate = (data: {
  ticketId: string;
  category: string;
  message: string;
  userName?: string;
  userEmail?: string;
}) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>New Support Ticket - RentaHub</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #f4f4f4; padding: 10px; text-align: center; }
    .content { background-color: #ffffff; padding: 20px; border-radius: 5px; }
    .footer { text-align: center; font-size: 12px; color: #777; margin-top: 20px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>RentaHub Support Ticket</h1>
    </div>
    <div class="content">
      <h2>New Support Ticket Submitted</h2>
      <p><strong>Ticket ID:</strong> ${data.ticketId}</p>
      <p><strong>Category:</strong> ${data.category}</p>
      
      ${data.userName ? `<p><strong>User:</strong> ${data.userName} (${data.userEmail})</p>` : ''}
      
      <h3>Message:</h3>
      <blockquote>
        ${data.message}
      </blockquote>
      
      <p>Please log into the admin dashboard to review and respond to this ticket.</p>
    </div>
    <div class="footer">
      <p>© ${new Date().getFullYear()} RentaHub. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
`;

export const sosAlertTemplate = (data: {
  alertId: string;
  bookingId: string;
  reason: string;
  userName?: string;
  userEmail?: string;
}) => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>🚨 URGENT: SOS Alert - RentaHub</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #ff4d4d; color: white; padding: 10px; text-align: center; }
    .content { background-color: #ffffff; padding: 20px; border-radius: 5px; }
    .footer { text-align: center; font-size: 12px; color: #777; margin-top: 20px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚨 URGENT SOS ALERT</h1>
    </div>
    <div class="content">
      <h2>Emergency Situation Reported</h2>
      <p><strong>Alert ID:</strong> ${data.alertId}</p>
      <p><strong>Booking ID:</strong> ${data.bookingId}</p>
      
      ${data.userName ? `<p><strong>User:</strong> ${data.userName} (${data.userEmail})</p>` : ''}
      
      <h3>Reported Reason:</h3>
      <blockquote style="background-color: #ffeeee; padding: 10px; border-left: 4px solid #ff4d4d;">
        ${data.reason}
      </blockquote>
      
      <p><strong>IMMEDIATE ACTION REQUIRED</strong></p>
      <p>Please investigate this SOS alert immediately and contact the user/provider.</p>
    </div>
    <div class="footer">
      <p>© ${new Date().getFullYear()} RentaHub. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
`;
