import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  // Log request details
  logger.info(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    authorization: req.get('Authorization') ? 'Bearer ***' : 'None',
    query: Object.keys(req.query).length > 0 ? req.query : undefined,
    body: req.method !== 'GET' && req.body ? '***' : undefined
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - start;
    const statusCode = res.statusCode;
    
    logger.info(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - ${statusCode} (${duration}ms)`, {
      statusCode,
      duration,
      contentLength: res.get('Content-Length'),
      contentType: res.get('Content-Type')
    });
    
    return originalEnd.call(this, chunk, encoding);
  };

  next();
}; 