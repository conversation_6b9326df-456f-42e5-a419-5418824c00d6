import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { config } from '../config/env';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface AuthUser {
  id: string;
  email: string;
  role: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  status: string;
}

export const generateToken = (payload: any, stayConnected: boolean = false): string => {
  const options: jwt.SignOptions = {
    expiresIn: (stayConnected ? '7d' : (config.JWT_EXPIRATION || '24h')) as any
  };
  
  return jwt.sign(payload, config.JWT_SECRET, options);
};

export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      res.status(401).json({ error: 'Access token required' });
      return;
    }

    const decoded = jwt.verify(token, config.JWT_SECRET) as any;
    
    // Fetch user from database to ensure they still exist and get latest data
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true
      }
    });

    if (!user) {
      res.status(401).json({ error: 'User not found' });
      return;
    }

    // Check if user is suspended
    if (user.status === 'SUSPENDED') {
      res.status(403).json({ error: 'Account has been suspended' });
      return;
    }

    // Add user to request object
    (req as any).user = {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      status: user.status
    };

    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

export const requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user;
  if (!user || user.role !== 'ADMIN') {
    res.status(403).json({ error: 'Admin access required' });
    return;
  }
  next();
};

export const requireProvider = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user;
  if (!user || (user.role !== 'PROVIDER' && user.role !== 'ADMIN')) {
    res.status(403).json({ error: 'Provider access required' });
    return;
  }
  next();
};

export const requireCustomer = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user;
  if (!user || (user.role !== 'CUSTOMER' && user.role !== 'ADMIN')) {
    res.status(403).json({ error: 'Customer access required' });
    return;
  }
  next();
};

export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      next();
      return;
    }

    const decoded = jwt.verify(token, config.JWT_SECRET) as any;
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,
        emailVerified: true,
        phoneVerified: true
      }
    });

    if (user && user.status !== 'SUSPENDED') {
      (req as any).user = {
        id: user.id,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        phoneVerified: user.phoneVerified,
        status: user.status
      };
    }

    next();
  } catch (error) {
    // For optional auth, we don't fail on token errors
    next();
  }
};
