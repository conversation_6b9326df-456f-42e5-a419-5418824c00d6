import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';

/**
 * Enhanced security headers middleware following bookcars security pattern
 */

/**
 * Comprehensive security headers configuration
 */
export const securityHeaders = () => {
  return [
    // Content Security Policy
    helmet.contentSecurityPolicy({
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'", 
          "'unsafe-inline'", 
          "https://fonts.googleapis.com",
          "https://cdn.jsdelivr.net"
        ],
        scriptSrc: [
          "'self'", 
          "'unsafe-eval'", // Required for some frameworks
          "'unsafe-inline'", // Required for inline scripts (minimize usage)
          "https://js.stripe.com",
          "https://maps.googleapis.com"
        ],
        imgSrc: [
          "'self'", 
          "data:", 
          "https:",
          "blob:"
        ],
        connectSrc: [
          "'self'", 
          "https://api.stripe.com",
          "https://*.supabase.co",
          "https://maps.googleapis.com",
          "https://api.rentahub.info"
        ],
        fontSrc: [
          "'self'", 
          "https://fonts.gstatic.com",
          "https://cdn.jsdelivr.net"
        ],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: [
          "https://js.stripe.com",
          "https://maps.google.com"
        ],
        baseUri: ["'self'"],
        formAction: ["'self'"],
        frameAncestors: ["'none'"]
      },
      reportOnly: false
    }),

    // DNS Prefetch Control
    helmet.dnsPrefetchControl({
      allow: false
    }),

    // Cross-Origin Embedder Policy
    helmet.crossOriginEmbedderPolicy({
      policy: false // Disabled for compatibility with third-party services
    }),

    // Frame Guard (X-Frame-Options)
    helmet.frameguard({
      action: 'deny'
    }),

    // Hide Powered By
    helmet.hidePoweredBy(),

    // HTTP Strict Transport Security
    helmet.hsts({
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    }),

    // IE No Open
    helmet.ieNoOpen(),

    // X-Content-Type-Options
    helmet.noSniff(),

    // Permitted Cross Domain Policies
    helmet.permittedCrossDomainPolicies({
      permittedPolicies: 'none'
    }),

    // Referrer Policy
    helmet.referrerPolicy({
      policy: 'strict-origin-when-cross-origin'
    }),

    // XSS Filter
    helmet.xssFilter(),

    // Origin Agent Cluster
    helmet.originAgentCluster(),

    // Cross-Origin Resource Policy
    helmet.crossOriginResourcePolicy({
      policy: 'cross-origin'
    }),

    // Cross-Origin Opener Policy
    helmet.crossOriginOpenerPolicy({
      policy: 'same-origin-allow-popups'
    })
  ];
};

/**
 * Additional custom security headers
 */
export const customSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // XSS Protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer Policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Feature Policy / Permissions Policy
  res.setHeader('Permissions-Policy', [
    'camera=()',
    'microphone=()',
    'geolocation=(self)',
    'payment=(self)',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()'
  ].join(', '));

  // Cross-Origin Policies
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin-allow-popups');
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  
  // Cache Control for sensitive endpoints
  if (req.path.includes('/api/auth') || req.path.includes('/api/admin')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
  }

  next();
};

/**
 * Security headers for API responses
 */
export const apiSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // API-specific headers
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-RateLimit-Policy', 'standard');
  
  // Prevent caching of API responses with sensitive data
  if (req.method !== 'GET' || req.path.includes('/user') || req.path.includes('/admin')) {
    res.setHeader('Cache-Control', 'no-store');
  }

  next();
};

/**
 * Development-only security headers
 */
export const developmentSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'development') {
    // Add development-specific headers
    res.setHeader('X-Development-Mode', 'true');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
  }

  next();
};

/**
 * Production-only security headers
 */
export const productionSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'production') {
    // Strict Transport Security (HTTPS only)
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    
    // Expect-CT (Certificate Transparency)
    res.setHeader('Expect-CT', 'max-age=86400, enforce');
    
    // Remove server information
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');
  }

  next();
};

/**
 * Comprehensive security middleware combining all security measures
 */
export const comprehensiveSecurityHeaders = () => {
  const middlewares = [
    ...securityHeaders(),
    customSecurityHeaders,
    apiSecurityHeaders,
    developmentSecurityHeaders,
    productionSecurityHeaders
  ];

  return (req: Request, res: Response, next: NextFunction) => {
    let index = 0;

    const runNext = () => {
      if (index >= middlewares.length) {
        return next();
      }

      const middleware = middlewares[index++];
      middleware(req, res, runNext);
    };

    runNext();
  };
};

/**
 * Security headers for file uploads
 */
export const fileUploadSecurityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent execution of uploaded files
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Content-Disposition', 'attachment');
  
  // Additional file security
  if (req.path.includes('/upload') || req.path.includes('/file')) {
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  }

  next();
};

export default {
  securityHeaders,
  customSecurityHeaders,
  apiSecurityHeaders,
  developmentSecurityHeaders,
  productionSecurityHeaders,
  comprehensiveSecurityHeaders,
  fileUploadSecurityHeaders
};
