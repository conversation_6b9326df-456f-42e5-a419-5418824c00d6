import { Request, Response, NextFunction } from "express";
import { supabase } from '../utils/supabaseClient';

export const roleMiddleware = (allowedRoles: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Get the authenticated user from the request
    const user = (req as any).user;

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    try {
      // Fetch user details to check role
      const { data: userData, error } = await supabase
        .from('users')
        .select('is_provider')
        .eq('id', user.id)
        .single();

      if (error || !userData) {
        return res.status(403).json({
          success: false,
          error: 'User details not found'
        });
      }

      // Determine user role
      const userRole = userData.is_provider ? 'provider' : 'user';

      // Check if user's role is in the allowed roles
      if (!allowedRoles.includes(userRole) && !allowedRoles.includes('admin')) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions'
        });
      }

      // If we're here, the user has the required role
      next();
    } catch (error) {
      console.error('Role authorization error:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during role authorization'
      });
    }
  };
};

// Specific middleware for admin role
export const adminRoleMiddleware = roleMiddleware(['admin']); 