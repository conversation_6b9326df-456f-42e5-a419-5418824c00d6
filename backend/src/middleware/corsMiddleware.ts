import cors from 'cors';
import { Request, Response, NextFunction } from "express";

// Get allowed origins from environment variables
const getAllowedOrigins = (): string[] => {
  const origins = process.env.CORS_ALLOWED_ORIGINS;
  const frontendUrl = process.env.FRONTEND_URL;
  const adminUrl = process.env.ADMIN_URL;
  
  // Start with default development URLs
  const allowedOrigins = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'];
  
  // Add any configured origins from env var
  if (origins) {
    allowedOrigins.push(...origins.split(',').map(origin => origin.trim()));
  }
  
  // Add frontend URL if set
  if (frontendUrl) {
    allowedOrigins.push(frontendUrl);
  }
  
  // Add admin URL if set
  if (adminUrl) {
    allowedOrigins.push(adminUrl);
  }
  
  // Add Railway default domains if in production
  if (process.env.NODE_ENV === 'production') {
    allowedOrigins.push(
      'https://rentahub-frontend-production.up.railway.app',
      'https://rentahub-admin-production.up.railway.app'
    );
  }
  
  return allowedOrigins;
};

// CORS configuration for production
const corsOptions: cors.CorsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = getAllowedOrigins();
    
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {
      return callback(null, true);
    }
    
    // Check if the origin is in the allowed list
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // Log unauthorized origin attempts
    console.warn(`CORS blocked request from unauthorized origin: ${origin}`);
    return callback(new Error(`Not allowed by CORS: ${origin}`));
  },
  credentials: true, // Allow cookies and authorization headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'x-user-id', // Required for user identification
    'Stripe-Signature' // Required for Stripe webhooks
  ],
  exposedHeaders: ['X-Total-Count', 'X-RateLimit-Limit', 'X-RateLimit-Remaining'],
  maxAge: 86400, // Cache preflight requests for 24 hours
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

// Production CORS middleware
export const corsMiddleware = cors(corsOptions);

// Development CORS middleware (more permissive)
export const corsDevMiddleware = cors({
  origin: 'http://localhost:5173', // Only allow frontend dev server
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'x-user-id', // Required for user identification
    'Stripe-Signature'
  ],
  exposedHeaders: ['X-Total-Count', 'X-RateLimit-Limit', 'X-RateLimit-Remaining']
});

// CORS error handler middleware
export const corsErrorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  if (err.message.includes('Not allowed by CORS')) {
    return res.status(403).json({
      error: 'CORS Error',
      message: 'Origin not allowed',
      timestamp: new Date().toISOString()
    });
  }
  next(err);
};

// Get the appropriate CORS middleware based on environment
export const getCorsMiddleware = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  return isProduction ? corsMiddleware : corsDevMiddleware;
}; 