import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Input sanitization middleware to prevent XSS, SQL injection, and other attacks
 */

interface SanitizationOptions {
  skipFields?: string[];
  allowHtml?: boolean;
  maxLength?: number;
  strictMode?: boolean;
}

export class InputSanitizer {
  /**
   * Sanitize string input to prevent XSS attacks
   */
  static sanitizeString(input: string, options: SanitizationOptions = {}): string {
    if (!input || typeof input !== 'string') return input;

    const { allowHtml = false, maxLength = 10000, strictMode = false } = options;

    // Trim whitespace
    let sanitized = input.trim();

    // Enforce length limits
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    if (!allowHtml) {
      // Basic HTML entity escaping
      sanitized = sanitized
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
    }

    if (strictMode) {
      // Remove potentially dangerous patterns
      sanitized = sanitized
        .replace(/javascript:/gi, '')
        .replace(/vbscript:/gi, '')
        .replace(/data:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }

    return sanitized;
  }

  /**
   * Sanitize SQL input to prevent injection attacks
   */
  static sanitizeSqlInput(input: string): string {
    if (!input || typeof input !== 'string') return input;

    // Escape SQL special characters
    return input
      .replace(/'/g, "''")
      .replace(/;/g, '\\;')
      .replace(/--/g, '\\--')
      .replace(/\/\*/g, '\\/\\*')
      .replace(/\*\//g, '\\*\\/')
      .replace(/xp_/gi, 'xp\\_')
      .replace(/sp_/gi, 'sp\\_');
  }

  /**
   * Sanitize search queries
   */
  static sanitizeSearchQuery(query: string): string {
    if (!query || typeof query !== 'string') return '';

    // Basic regex escaping
    return query.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Validate and sanitize email
   */
  static sanitizeEmail(email: string): string | null {
    if (!email || typeof email !== 'string') return null;

    const sanitized = email.trim().toLowerCase();
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(sanitized) ? sanitized : null;
  }

  /**
   * Validate and sanitize phone number
   */
  static sanitizePhone(phone: string): string | null {
    if (!phone || typeof phone !== 'string') return null;

    // Remove all non-digit characters except + at the beginning
    const sanitized = phone.replace(/[^\d+]/g, '').replace(/(?!^)\+/g, '');
    
    // Basic phone validation (adjust regex as needed)
    const phoneRegex = /^\+?[\d\s\-\(\)]{7,15}$/;
    return phoneRegex.test(sanitized) ? sanitized : null;
  }

  /**
   * Sanitize URL input
   */
  static sanitizeUrl(url: string): string | null {
    if (!url || typeof url !== 'string') return null;

    const sanitized = url.trim();

    // Basic URL validation - only allow http and https
    const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

    return urlRegex.test(sanitized) ? sanitized : null;
  }

  /**
   * Recursively sanitize object properties
   */
  static sanitizeObject(obj: any, options: SanitizationOptions = {}): any {
    if (!obj || typeof obj !== 'object') return obj;

    const { skipFields = [] } = options;

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item, options));
    }

    const sanitized: any = {};

    for (const [key, value] of Object.entries(obj)) {
      if (skipFields.includes(key)) {
        sanitized[key] = value;
        continue;
      }

      if (typeof value === 'string') {
        // Apply specific sanitization based on field name
        if (key.toLowerCase().includes('email')) {
          sanitized[key] = this.sanitizeEmail(value);
        } else if (key.toLowerCase().includes('phone')) {
          sanitized[key] = this.sanitizePhone(value);
        } else if (key.toLowerCase().includes('url') || key.toLowerCase().includes('link')) {
          sanitized[key] = this.sanitizeUrl(value);
        } else if (key.toLowerCase().includes('search') || key.toLowerCase().includes('query')) {
          sanitized[key] = this.sanitizeSearchQuery(value);
        } else {
          sanitized[key] = this.sanitizeString(value, options);
        }
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeObject(value, options);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }
}

/**
 * Middleware to sanitize request body
 */
export const sanitizeBody = (options: SanitizationOptions = {}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (req.body && typeof req.body === 'object') {
        req.body = InputSanitizer.sanitizeObject(req.body, options);
      }
      next();
    } catch (error) {
      logger.error('Input sanitization error:', error);
      res.status(400).json({ 
        error: 'Invalid input data',
        message: 'Request contains invalid or potentially dangerous content'
      });
    }
  };
};

/**
 * Middleware to sanitize query parameters
 */
export const sanitizeQuery = (options: SanitizationOptions = {}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (req.query && typeof req.query === 'object') {
        req.query = InputSanitizer.sanitizeObject(req.query, options);
      }
      next();
    } catch (error) {
      logger.error('Query sanitization error:', error);
      res.status(400).json({ 
        error: 'Invalid query parameters',
        message: 'Query contains invalid or potentially dangerous content'
      });
    }
  };
};

/**
 * Comprehensive sanitization middleware
 */
export const comprehensiveSanitization = (options: SanitizationOptions = {}) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Sanitize body
      if (req.body && typeof req.body === 'object') {
        req.body = InputSanitizer.sanitizeObject(req.body, options);
      }

      // Sanitize query parameters
      if (req.query && typeof req.query === 'object') {
        req.query = InputSanitizer.sanitizeObject(req.query, options);
      }

      // Sanitize URL parameters
      if (req.params && typeof req.params === 'object') {
        req.params = InputSanitizer.sanitizeObject(req.params, options);
      }

      // Log suspicious patterns in development
      if (process.env.NODE_ENV === 'development') {
        const suspiciousPatterns = [
          /<script/i, /javascript:/i, /vbscript:/i, /on\w+=/i,
          /union\s+select/i, /drop\s+table/i, /delete\s+from/i,
          /insert\s+into/i, /update\s+set/i
        ];

        const checkForSuspiciousContent = (obj: any, path = '') => {
          if (typeof obj === 'string') {
            suspiciousPatterns.forEach(pattern => {
              if (pattern.test(obj)) {
                logger.warn(`Suspicious pattern detected in ${path}: ${pattern}`);
              }
            });
          } else if (typeof obj === 'object' && obj !== null) {
            Object.entries(obj).forEach(([key, value]) => {
              checkForSuspiciousContent(value, `${path}.${key}`);
            });
          }
        };

        checkForSuspiciousContent(req.body, 'body');
        checkForSuspiciousContent(req.query, 'query');
        checkForSuspiciousContent(req.params, 'params');
      }

      next();
    } catch (error) {
      logger.error('Comprehensive sanitization error:', error);
      res.status(400).json({ 
        error: 'Invalid request data',
        message: 'Request contains invalid or potentially dangerous content'
      });
    }
  };
};

// InputSanitizer is already exported above
