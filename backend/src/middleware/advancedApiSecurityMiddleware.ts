import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import { config } from '../config/env';

/**
 * Advanced API security middleware
 */

interface ApiKeyData {
  id: string;
  name: string;
  permissions: string[];
  rateLimit: number;
  expiresAt?: Date;
}

interface RequestSignature {
  timestamp: string;
  nonce: string;
  signature: string;
}

export class AdvancedApiSecurity {
  private static apiKeys: Map<string, ApiKeyData> = new Map();
  private static usedNonces: Set<string> = new Set();
  private static readonly NONCE_EXPIRY = 5 * 60 * 1000; // 5 minutes

  /**
   * API versioning middleware
   */
  static apiVersioning(supportedVersions: string[] = ['v1']) {
    return (req: Request, res: Response, next: NextFunction) => {
      // Extract version from URL path or header
      let version = req.params.version || req.headers['api-version'] as string;
      
      // Default to latest version if not specified
      if (!version) {
        version = supportedVersions[supportedVersions.length - 1];
      }

      // Validate version
      if (!supportedVersions.includes(version)) {
        return res.status(400).json({
          error: 'Unsupported API version',
          supportedVersions,
          requestedVersion: version
        });
      }

      // Add version to request object
      (req as any).apiVersion = version;
      
      // Set response header
      res.setHeader('API-Version', version);
      
      next();
    };
  }

  /**
   * Request signing verification
   */
  static requestSigning(secretKey: string) {
    return (req: Request, res: Response, next: NextFunction) => {
      const signature = req.headers['x-signature'] as string;
      const timestamp = req.headers['x-timestamp'] as string;
      const nonce = req.headers['x-nonce'] as string;

      if (!signature || !timestamp || !nonce) {
        return res.status(401).json({
          error: 'Missing required signature headers',
          required: ['x-signature', 'x-timestamp', 'x-nonce']
        });
      }

      // Check timestamp (prevent replay attacks)
      const requestTime = parseInt(timestamp);
      const currentTime = Date.now();
      const timeDiff = Math.abs(currentTime - requestTime);
      
      if (timeDiff > 5 * 60 * 1000) { // 5 minutes
        return res.status(401).json({
          error: 'Request timestamp too old',
          maxAge: '5 minutes'
        });
      }

      // Check nonce (prevent replay attacks)
      if (this.usedNonces.has(nonce)) {
        return res.status(401).json({
          error: 'Nonce already used'
        });
      }

      // Generate expected signature
      const payload = `${req.method}${req.path}${timestamp}${nonce}${JSON.stringify(req.body || {})}`;
      const expectedSignature = crypto
        .createHmac('sha256', secretKey)
        .update(payload)
        .digest('hex');

      // Verify signature
      if (signature !== expectedSignature) {
        return res.status(401).json({
          error: 'Invalid request signature'
        });
      }

      // Store nonce to prevent reuse
      this.usedNonces.add(nonce);
      
      // Clean up old nonces periodically
      setTimeout(() => {
        this.usedNonces.delete(nonce);
      }, this.NONCE_EXPIRY);

      next();
    };
  }

  /**
   * Webhook signature verification (for Stripe, etc.)
   */
  static webhookSignatureVerification(webhookSecret: string, headerName = 'stripe-signature') {
    return (req: Request, res: Response, next: NextFunction) => {
      const signature = req.headers[headerName] as string;
      
      if (!signature) {
        return res.status(401).json({
          error: 'Missing webhook signature'
        });
      }

      try {
        // For Stripe webhooks
        if (headerName === 'stripe-signature') {
          const elements = signature.split(',');
          const signatureElements: any = {};
          
          elements.forEach(element => {
            const [key, value] = element.split('=');
            signatureElements[key] = value;
          });

          const timestamp = signatureElements.t;
          const signatures = [signatureElements.v1];

          // Check timestamp
          const timestampDiff = Math.abs(Date.now() / 1000 - parseInt(timestamp));
          if (timestampDiff > 300) { // 5 minutes
            return res.status(401).json({
              error: 'Webhook timestamp too old'
            });
          }

          // Verify signature
          const payload = `${timestamp}.${req.body}`;
          const expectedSignature = crypto
            .createHmac('sha256', webhookSecret)
            .update(payload, 'utf8')
            .digest('hex');

          if (!signatures.includes(expectedSignature)) {
            return res.status(401).json({
              error: 'Invalid webhook signature'
            });
          }
        }

        next();
      } catch (error) {
        logger.error('Webhook signature verification error:', error);
        return res.status(401).json({
          error: 'Webhook signature verification failed'
        });
      }
    };
  }

  /**
   * Enhanced session management
   */
  static enhancedSessionManagement() {
    return (req: Request, res: Response, next: NextFunction) => {
      const token = req.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next();
      }

      try {
        const decoded = jwt.verify(token, config.JWT_SECRET) as any;
        
        // Check for session hijacking indicators
        const userAgent = req.headers['user-agent'];
        const clientIP = req.ip;
        
        if (decoded.userAgent && decoded.userAgent !== userAgent) {
          logger.warn('Potential session hijacking detected - User Agent mismatch', {
            userId: decoded.id,
            expectedUA: decoded.userAgent,
            actualUA: userAgent
          });
          
          return res.status(401).json({
            error: 'Session security violation detected'
          });
        }

        if (decoded.clientIP && decoded.clientIP !== clientIP) {
          logger.warn('Potential session hijacking detected - IP mismatch', {
            userId: decoded.id,
            expectedIP: decoded.clientIP,
            actualIP: clientIP
          });
          
          // In production, you might want to allow IP changes but log them
          // For now, we'll just log and continue
        }

        // Add session metadata to request
        (req as any).sessionMetadata = {
          userAgent,
          clientIP,
          tokenIssuedAt: decoded.iat,
          tokenExpiresAt: decoded.exp
        };

        next();
      } catch (error) {
        next();
      }
    };
  }

  /**
   * API key authentication
   */
  static apiKeyAuthentication() {
    return (req: Request, res: Response, next: NextFunction) => {
      const apiKey = req.headers['x-api-key'] as string;
      
      if (!apiKey) {
        return res.status(401).json({
          error: 'API key required'
        });
      }

      const keyData = this.apiKeys.get(apiKey);
      
      if (!keyData) {
        return res.status(401).json({
          error: 'Invalid API key'
        });
      }

      // Check expiration
      if (keyData.expiresAt && keyData.expiresAt < new Date()) {
        return res.status(401).json({
          error: 'API key expired'
        });
      }

      // Add API key data to request
      (req as any).apiKeyData = keyData;
      
      next();
    };
  }

  /**
   * Request correlation ID
   */
  static requestCorrelation() {
    return (req: Request, res: Response, next: NextFunction) => {
      // Generate or use existing correlation ID
      const correlationId = req.headers['x-correlation-id'] as string || 
                           crypto.randomUUID();
      
      // Add to request and response
      (req as any).correlationId = correlationId;
      res.setHeader('X-Correlation-ID', correlationId);
      
      // Log request with correlation ID
      logger.info('Request received', {
        correlationId,
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
      
      next();
    };
  }

  /**
   * API response encryption (for sensitive data)
   */
  static responseEncryption(encryptionKey: string) {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalSend = res.send;
      
      res.send = function(data: any) {
        // Only encrypt sensitive endpoints
        const sensitiveEndpoints = ['/api/user', '/api/admin', '/api/payment'];
        const shouldEncrypt = sensitiveEndpoints.some(endpoint => 
          req.path.startsWith(endpoint)
        );
        
        if (shouldEncrypt && typeof data === 'string') {
          try {
            const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
            let encrypted = cipher.update(data, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            res.setHeader('Content-Encoding', 'encrypted');
            return originalSend.call(this, encrypted);
          } catch (error) {
            logger.error('Response encryption error:', error);
          }
        }
        
        return originalSend.call(this, data);
      };
      
      next();
    };
  }

  /**
   * Register API key
   */
  static registerApiKey(keyData: ApiKeyData): string {
    const apiKey = crypto.randomBytes(32).toString('hex');
    this.apiKeys.set(apiKey, keyData);
    return apiKey;
  }

  /**
   * Revoke API key
   */
  static revokeApiKey(apiKey: string): boolean {
    return this.apiKeys.delete(apiKey);
  }
}

/**
 * Comprehensive advanced API security middleware
 */
export const comprehensiveAdvancedApiSecurity = (options: {
  supportedVersions?: string[];
  enableRequestSigning?: boolean;
  enableWebhookVerification?: boolean;
  enableResponseEncryption?: boolean;
  signingSecret?: string;
  webhookSecret?: string;
  encryptionKey?: string;
} = {}) => {
  const middlewares: any[] = [];

  // API versioning
  if (options.supportedVersions) {
    middlewares.push(AdvancedApiSecurity.apiVersioning(options.supportedVersions));
  }

  // Request correlation
  middlewares.push(AdvancedApiSecurity.requestCorrelation());

  // Enhanced session management
  middlewares.push(AdvancedApiSecurity.enhancedSessionManagement());

  // Request signing
  if (options.enableRequestSigning && options.signingSecret) {
    middlewares.push(AdvancedApiSecurity.requestSigning(options.signingSecret));
  }

  // Response encryption
  if (options.enableResponseEncryption && options.encryptionKey) {
    middlewares.push(AdvancedApiSecurity.responseEncryption(options.encryptionKey));
  }

  return middlewares;
};

export { AdvancedApiSecurity };
