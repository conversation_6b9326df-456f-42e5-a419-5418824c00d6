import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    status?: string;
    getDisplayName?(): string;
  };
}

/**
 * Role-based access control middleware
 * Checks if the authenticated user has one of the required roles
 */
export const accessControlMiddleware = (allowedRoles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // Check if user is authenticated (should be set by authMiddleware)
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // Check if user's role is in the allowed roles
      if (!allowedRoles.includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
        });
      }

      // Check if user account is active
      if (req.user.status !== 'ACTIVE') {
        return res.status(403).json({
          success: false,
          error: 'Account not active',
          message: 'Your account must be active to access this resource'
        });
      }

      // User has required role and active status, proceed
      next();
    } catch (error) {
      console.error('Role-based access control error:', error);
      return res.status(500).json({
        success: false,
        error: 'Access control check failed'
      });
    }
  };
};

/**
 * Admin-only access middleware
 * Shorthand for admin and super admin access
 */
export const adminOnlyMiddleware = accessControlMiddleware([UserRole.ADMIN]);

/**
 * Provider access middleware
 * Allows providers, admins, and super admins
 */
export const providerAccessMiddleware = accessControlMiddleware([
  UserRole.PROVIDER, 
  UserRole.ADMIN
]);

/**
 * Customer access middleware
 * Allows customers, providers, admins, and super admins
 */
export const customerAccessMiddleware = accessControlMiddleware([
  UserRole.CUSTOMER,
  UserRole.PROVIDER,
  UserRole.ADMIN
]);

/**
 * Super admin only middleware
 * Highest level access
 */
export const superAdminOnlyMiddleware = accessControlMiddleware([UserRole.ADMIN]);

export default accessControlMiddleware;
