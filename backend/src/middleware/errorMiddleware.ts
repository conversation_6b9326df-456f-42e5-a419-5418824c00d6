import { Request, Response, NextFunction } from "express";

// Custom error class
export class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Custom error classes for more specific error handling
export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class ResourceNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ResourceNotFoundError';
  }
}

export class DatabaseError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DatabaseError';
  }
}

// Error response formatter
const sendErrorDev = (err: AppError, res: Response) => {
  res.status(err.statusCode).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack
  });
};

const sendErrorProd = (err: AppError, res: Response) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      status: err.status,
      message: err.message
    });
  } 
  // Programming or unknown error: don't leak error details
  else {
    console.error('UNHANDLED ERROR 💥', err);
    res.status(500).json({
      status: 'error',
      message: 'Something went very wrong!'
    });
  }
};

// Global error handling middleware
export function errorHandler(
  err: Error, 
  req: Request, 
  res: Response, 
  next: NextFunction
) {
  // Log the error for server-side tracking
  console.error(`[Error] ${err.name}: ${err.message}`);
  console.error(err.stack);

  // Determine the appropriate HTTP status code and response
  switch (err.name) {
    case 'ValidationError':
      res.status(400).json({
        status: 'error',
        type: 'VALIDATION_ERROR',
        message: err.message
      });
      break;

    case 'AuthorizationError':
      res.status(403).json({
        status: 'error',
        type: 'AUTHORIZATION_ERROR',
        message: err.message
      });
      break;

    case 'ResourceNotFoundError':
      res.status(404).json({
        status: 'error',
        type: 'RESOURCE_NOT_FOUND',
        message: err.message
      });
      break;

    case 'DatabaseError':
      res.status(500).json({
        status: 'error',
        type: 'DATABASE_ERROR',
        message: 'An internal database error occurred'
      });
      break;

    default:
      // Catch-all for unhandled errors
      res.status(500).json({
        status: 'error',
        type: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred'
      });
  }
}

// Async error wrapper to simplify error handling in route handlers
export function asyncHandler(fn: Function) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await fn(req, res, next);
    } catch (error) {
      next(error);
    }
  };
}

// Catch unhandled promise rejections
process.on('unhandledRejection', (reason: Error, promise: Promise<any>) => {
  console.error('UNHANDLED REJECTION! 💥 Shutting down...');
  console.error(reason.name, reason.message);
  
  // Graceful shutdown
  process.exit(1);
});

// Catch uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.error(err.name, err.message);
  
  // Graceful shutdown
  process.exit(1);
}); 