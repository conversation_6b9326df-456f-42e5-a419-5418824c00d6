import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Security monitoring and logging middleware
 */

interface SecurityEvent {
  type: 'AUTH_ATTEMPT' | 'AUTH_SUCCESS' | 'AUTH_FAILURE' | 'SUSPICIOUS_ACTIVITY' | 'RATE_LIMIT_HIT' | 'INVALID_INPUT' | 'UNAUTHORIZED_ACCESS';
  ip: string;
  userAgent: string;
  path: string;
  method: string;
  userId?: string;
  details?: any;
  timestamp: Date;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

class SecurityMonitor {
  private static instance: SecurityMonitor;
  private suspiciousIPs: Map<string, { count: number; lastSeen: Date }> = new Map();
  private authAttempts: Map<string, { count: number; lastAttempt: Date }> = new Map();
  private readonly MAX_AUTH_ATTEMPTS = 5;
  private readonly SUSPICIOUS_THRESHOLD = 10;
  private readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  private constructor() {
    // Clean up old entries periodically
    setInterval(() => {
      this.cleanupOldEntries();
    }, this.CLEANUP_INTERVAL);
  }

  public static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor();
    }
    return SecurityMonitor.instance;
  }

  private cleanupOldEntries(): void {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - this.CLEANUP_INTERVAL);

    // Clean up suspicious IPs
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.lastSeen < oneHourAgo) {
        this.suspiciousIPs.delete(ip);
      }
    }

    // Clean up auth attempts
    for (const [ip, data] of this.authAttempts.entries()) {
      if (data.lastAttempt < oneHourAgo) {
        this.authAttempts.delete(ip);
      }
    }
  }

  public logSecurityEvent(event: SecurityEvent): void {
    // Log to console/file
    logger.warn('Security Event', {
      type: event.type,
      severity: event.severity,
      ip: event.ip,
      path: event.path,
      method: event.method,
      userId: event.userId,
      userAgent: event.userAgent,
      details: event.details,
      timestamp: event.timestamp
    });

    // Track suspicious activity
    this.trackSuspiciousActivity(event);

    // In production, you might want to send alerts for critical events
    if (event.severity === 'CRITICAL' && process.env.NODE_ENV === 'production') {
      this.sendSecurityAlert(event);
    }
  }

  private trackSuspiciousActivity(event: SecurityEvent): void {
    const ip = event.ip;

    // Track auth attempts
    if (event.type === 'AUTH_FAILURE') {
      const attempts = this.authAttempts.get(ip) || { count: 0, lastAttempt: new Date() };
      attempts.count++;
      attempts.lastAttempt = new Date();
      this.authAttempts.set(ip, attempts);

      if (attempts.count >= this.MAX_AUTH_ATTEMPTS) {
        this.logSecurityEvent({
          ...event,
          type: 'SUSPICIOUS_ACTIVITY',
          severity: 'HIGH',
          details: { reason: 'Multiple failed auth attempts', count: attempts.count }
        });
      }
    }

    // Track general suspicious activity
    if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
      const suspicious = this.suspiciousIPs.get(ip) || { count: 0, lastSeen: new Date() };
      suspicious.count++;
      suspicious.lastSeen = new Date();
      this.suspiciousIPs.set(ip, suspicious);
    }
  }

  private sendSecurityAlert(event: SecurityEvent): void {
    // In a real application, you would send this to your monitoring service
    // For now, just log it as a critical event
    logger.error('CRITICAL SECURITY EVENT', event);
    
    // You could integrate with services like:
    // - Slack notifications
    // - Email alerts
    // - PagerDuty
    // - Sentry
    // - Custom webhook
  }

  public isIPSuspicious(ip: string): boolean {
    const suspicious = this.suspiciousIPs.get(ip);
    return suspicious ? suspicious.count >= this.SUSPICIOUS_THRESHOLD : false;
  }

  public getAuthAttempts(ip: string): number {
    const attempts = this.authAttempts.get(ip);
    return attempts ? attempts.count : 0;
  }
}

const securityMonitor = SecurityMonitor.getInstance();

/**
 * Middleware to log authentication attempts
 */
export const authenticationLogger = (req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send;
  
  res.send = function(data: any) {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    // Check if this is an auth endpoint
    if (req.path.includes('/auth/') || req.path.includes('/login') || req.path.includes('/register')) {
      const statusCode = res.statusCode;
      
      if (statusCode === 200 || statusCode === 201) {
        securityMonitor.logSecurityEvent({
          type: 'AUTH_SUCCESS',
          ip,
          userAgent,
          path: req.path,
          method: req.method,
          timestamp: new Date(),
          severity: 'LOW'
        });
      } else if (statusCode === 401 || statusCode === 403) {
        securityMonitor.logSecurityEvent({
          type: 'AUTH_FAILURE',
          ip,
          userAgent,
          path: req.path,
          method: req.method,
          timestamp: new Date(),
          severity: 'MEDIUM',
          details: { statusCode, body: req.body }
        });
      }
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware to detect suspicious patterns
 */
export const suspiciousActivityDetector = (req: Request, res: Response, next: NextFunction) => {
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    // SQL injection patterns
    /(\b(union|select|insert|delete|update|drop|create|alter|exec|execute)\b)/i,
    // XSS patterns
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    // Path traversal
    /\.\.\//g,
    // Command injection
    /[;&|`$(){}[\]]/g
  ];

  const checkSuspiciousContent = (obj: any, path = ''): boolean => {
    if (typeof obj === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(obj));
    } else if (typeof obj === 'object' && obj !== null) {
      return Object.entries(obj).some(([key, value]) => 
        checkSuspiciousContent(value, `${path}.${key}`)
      );
    }
    return false;
  };

  // Check request body, query, and params
  const hasSuspiciousContent = 
    checkSuspiciousContent(req.body, 'body') ||
    checkSuspiciousContent(req.query, 'query') ||
    checkSuspiciousContent(req.params, 'params');

  if (hasSuspiciousContent) {
    securityMonitor.logSecurityEvent({
      type: 'SUSPICIOUS_ACTIVITY',
      ip,
      userAgent,
      path: req.path,
      method: req.method,
      timestamp: new Date(),
      severity: 'HIGH',
      details: {
        reason: 'Suspicious patterns detected in request',
        body: req.body,
        query: req.query,
        params: req.params
      }
    });
  }

  // Check for suspicious user agents
  const suspiciousUserAgents = [
    /sqlmap/i,
    /nikto/i,
    /nmap/i,
    /masscan/i,
    /nessus/i,
    /openvas/i,
    /burp/i,
    /zap/i
  ];

  if (suspiciousUserAgents.some(pattern => pattern.test(userAgent))) {
    securityMonitor.logSecurityEvent({
      type: 'SUSPICIOUS_ACTIVITY',
      ip,
      userAgent,
      path: req.path,
      method: req.method,
      timestamp: new Date(),
      severity: 'HIGH',
      details: { reason: 'Suspicious user agent detected' }
    });
  }

  next();
};

/**
 * Middleware to log unauthorized access attempts
 */
export const unauthorizedAccessLogger = (req: Request, res: Response, next: NextFunction) => {
  const originalStatus = res.status;
  
  res.status = function(code: number) {
    if (code === 401 || code === 403) {
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      
      securityMonitor.logSecurityEvent({
        type: 'UNAUTHORIZED_ACCESS',
        ip,
        userAgent,
        path: req.path,
        method: req.method,
        timestamp: new Date(),
        severity: code === 403 ? 'HIGH' : 'MEDIUM',
        details: { statusCode: code }
      });
    }
    
    return originalStatus.call(this, code);
  };
  
  next();
};

/**
 * Middleware to block suspicious IPs
 */
export const suspiciousIPBlocker = (req: Request, res: Response, next: NextFunction) => {
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  
  if (securityMonitor.isIPSuspicious(ip)) {
    securityMonitor.logSecurityEvent({
      type: 'SUSPICIOUS_ACTIVITY',
      ip,
      userAgent: req.get('User-Agent') || 'unknown',
      path: req.path,
      method: req.method,
      timestamp: new Date(),
      severity: 'CRITICAL',
      details: { reason: 'Request from suspicious IP blocked' }
    });
    
    return res.status(429).json({
      error: 'Too many suspicious requests',
      message: 'Your IP has been temporarily blocked due to suspicious activity'
    });
  }
  
  next();
};

/**
 * Comprehensive security monitoring middleware
 */
export const comprehensiveSecurityMonitoring = () => {
  return [
    authenticationLogger,
    suspiciousActivityDetector,
    unauthorizedAccessLogger,
    suspiciousIPBlocker
  ];
};

export { securityMonitor, SecurityMonitor };
