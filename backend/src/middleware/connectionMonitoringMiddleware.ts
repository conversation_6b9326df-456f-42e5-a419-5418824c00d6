import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';

interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  lastActivity: Date;
  dbConnectionPool: {
    active: number;
    idle: number;
    total: number;
  };
}

class ConnectionMonitor {
  private static instance: ConnectionMonitor;
  private connections: Map<string, { timestamp: Date; lastActivity: Date }> = new Map();
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient();
  }

  static getInstance(): ConnectionMonitor {
    if (!ConnectionMonitor.instance) {
      ConnectionMonitor.instance = new ConnectionMonitor();
    }
    return ConnectionMonitor.instance;
  }

  trackConnection(connectionId: string): void {
    const now = new Date();
    this.connections.set(connectionId, {
      timestamp: now,
      lastActivity: now
    });
  }

  updateActivity(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  removeConnection(connectionId: string): void {
    this.connections.delete(connectionId);
  }

  getStats(): ConnectionStats {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    
    let activeConnections = 0;
    let lastActivity = new Date(0);

    for (const [_, connection] of this.connections) {
      if (connection.lastActivity > fiveMinutesAgo) {
        activeConnections++;
      }
      if (connection.lastActivity > lastActivity) {
        lastActivity = connection.lastActivity;
      }
    }

    return {
      totalConnections: this.connections.size,
      activeConnections,
      lastActivity,
      dbConnectionPool: {
        active: 0, // Prisma doesn't expose pool stats directly
        idle: 0,
        total: 0
      }
    };
  }

  cleanup(): void {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    for (const [connectionId, connection] of this.connections) {
      if (connection.lastActivity < oneHourAgo) {
        this.connections.delete(connectionId);
      }
    }
  }
}

export const connectionMonitor = ConnectionMonitor.getInstance();

// Middleware to track connections
export const connectionMonitoringMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const connectionId = req.ip + ':' + (req.headers['user-agent'] || 'unknown');
  
  // Track new connection
  connectionMonitor.trackConnection(connectionId);
  
  // Update activity on each request
  connectionMonitor.updateActivity(connectionId);
  
  // Clean up old connections periodically
  if (Math.random() < 0.01) { // 1% chance to trigger cleanup
    connectionMonitor.cleanup();
  }
  
  // Track connection close
  res.on('close', () => {
    connectionMonitor.removeConnection(connectionId);
  });
  
  next();
};

// Route to get connection stats
export const getConnectionStats = (req: Request, res: Response) => {
  const stats = connectionMonitor.getStats();
  res.json(stats);
};