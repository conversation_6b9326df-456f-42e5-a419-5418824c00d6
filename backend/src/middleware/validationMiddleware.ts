import { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from 'joi';

// Custom error response interface
interface ValidationErrorResponse {
  message: string;
  errors?: Array<{
    field: string;
    error: string;
  }>;
}

export class ValidationMiddleware {
  // Validate request body against a Joi schema
  static validate(schema: Joi.ObjectSchema) {
    return (req: Request, res: Response, next: NextFunction) => {
      const { error } = schema.validate(req.body, { 
        abortEarly: false,  // Collect all errors, not just the first
        allowUnknown: false // Disallow unknown fields
      });

      if (error) {
        const errors: ValidationErrorResponse = {
          message: 'Validation failed',
          errors: error.details.map(err => ({
            field: err.path.join('.'),
            error: err.message
          }))
        };

        return res.status(400).json(errors);
      }

      next();
    };
  }

  // Schemas for different types of validations
  static schemas = {
    // User creation/update validation
    user: Joi.object({
      email: Joi.string().email().required(),
      first_name: Joi.string().min(2).max(50).required(),
      last_name: Joi.string().min(2).max(50).required(),
      phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
      role: Joi.string().valid('user', 'admin', 'provider').default('user')
    }),

    // Vehicle creation/update validation
    vehicle: Joi.object({
      provider_id: Joi.string().uuid().required(),
      category: Joi.string().valid('small_scooter', 'large_scooter', 'luxury_bike').required(),
      make: Joi.string().min(2).max(100).required(),
      model: Joi.string().min(2).max(100).required(),
      year: Joi.number().min(1900).max(new Date().getFullYear() + 1).required(),
      engine_size: Joi.number().positive().required(),
      transmission: Joi.string().valid('automatic', 'manual').default('automatic'),
      daily_rate: Joi.number().positive().required(),
      quantity: Joi.number().integer().min(1).default(1)
    }),

    // Booking creation validation
    booking: Joi.object({
      user_id: Joi.string().uuid().required(),
      vehicle_id: Joi.string().uuid().required(),
      start_date: Joi.date().iso().required(),
      end_date: Joi.date().iso().min(Joi.ref('start_date')).required(),
      payment_method: Joi.string().valid('card', 'cash').required()
    }),

    // Provider creation/update validation
    provider: Joi.object({
      user_id: Joi.string().uuid().required(),
      business_name: Joi.string().min(2).max(200).required(),
      business_type: Joi.string().valid('individual', 'shop').default('individual'),
      address: Joi.string().min(5).max(500).required(),
      city: Joi.string().min(2).max(100).required(),
      phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
      verified: Joi.boolean().default(false)
    }),

    // Query parameter validation for list endpoints
    listQuery: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sort_by: Joi.string().optional(),
      order: Joi.string().valid('asc', 'desc').default('desc')
    })
  };

  // Validate query parameters for list endpoints
  static validateListQuery() {
    return this.validate(this.schemas.listQuery);
  }
}

// Custom error handler for validation errors
export const validationErrorHandler = (
  err: Error, 
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  if (err instanceof Joi.ValidationError) {
    const errors: ValidationErrorResponse = {
      message: 'Validation failed',
      errors: err.details.map(detail => ({
        field: detail.path.join('.'),
        error: detail.message
      }))
    };

    return res.status(400).json(errors);
  }

  // Pass to default error handler if not a validation error
  next(err);
}; 