import { Request, Response, NextFunction } from "express";
import rateLimit from 'express-rate-limit';
import Redis from 'ioredis';

// Redis configuration - only connect if Redis is available
let redisClient: Redis | null = null;

try {
  redisClient = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    // Optional: Add password if required
    // password: process.env.REDIS_PASSWORD,
    lazyConnect: true, // Don't connect immediately
    maxRetriesPerRequest: 3,
  });
  
  // Handle Redis connection errors
  redisClient.on('error', (error) => {
    console.warn('Redis connection error, falling back to memory-based rate limiting:', error.message);
    redisClient = null;
  });
  
  // Test connection
  redisClient.ping().catch(() => {
    console.warn('Redis connection failed, falling back to memory-based rate limiting');
    redisClient = null;
  });
} catch (error) {
  console.warn('Redis not available, using memory-based rate limiting');
  redisClient = null;
}

// General rate limiter for all routes
export const generalRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests, please try again later.',
  statusCode: 429, // 429 status = Too Many Requests
});

// Stricter limiter for authentication routes
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 login attempts per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many login attempts, please try again later.',
  statusCode: 429,
});

// More permissive limiter for read-only routes
export const readRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Limit each IP to 200 read requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many read requests, please try again later.',
  statusCode: 429,
});

// Webhook rate limiter with reasonable limits for production use
export const webhookRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes (shorter window)
  max: 200, // Allow 200 webhook requests per 15 minutes (more reasonable for production)
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Webhook request limit exceeded.',
  statusCode: 429,
  skip: (req) => {
    // Skip rate limiting in development mode for easier testing
    return process.env.NODE_ENV === 'development';
  }
});

// Logging middleware to track rate limit events
export const rateLimitLogger = (req: Request, res: Response, next: NextFunction) => {
  res.on('finish', () => {
    if (res.statusCode === 429) {
      console.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      // You could integrate this with your logging service or Sentry
    }
  });
  next();
};

// Graceful shutdown for Redis
export const closeRedisConnection = () => {
  if (redisClient) {
    return redisClient.quit();
  }
  return Promise.resolve();
}; 