import { Request, Response, NextFunction } from "express";
import winston from 'winston';
import { supabase } from '../utils/supabaseClient';

// Configure Winston logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    // Write all logs to console
    new winston.transports.Console({
      format: winston.format.simple()
    }),
    // Write error logs to a file
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    // Write all logs to a combined log file
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

// Admin activity logging interface
interface AdminActivityLog {
  user_id: string;
  action: string;
  resource: string;
  details?: any;
  timestamp: Date;
  ip_address: string;
}

export const loggingMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  // Capture original response methods
  const oldWrite = res.write;
  const oldEnd = res.end;
  const chunks: any[] = [];

  // Override response write method to capture response body
  res.write = function(chunk: any) {
    chunks.push(chunk);
    return oldWrite.apply(res, arguments as any);
  };

  // Override response end method to capture response body
  res.end = function(chunk: any) {
    if (chunk) {
      chunks.push(chunk);
    }
    return oldEnd.apply(res, arguments as any);
  };

  // Log request details
  logger.info({
    method: req.method,
    path: req.path,
    body: req.body,
    query: req.query,
    headers: req.headers
  });

  // Attach logging to response
  res.on('finish', async () => {
    const duration = Date.now() - startTime;
    const responseBody = Buffer.concat(chunks).toString('utf8');

    // Log response details
    logger.info({
      responseStatus: res.statusCode,
      responseTime: duration,
      responseBody: safeJSONParse(responseBody)
    });

    // Log admin activities
    if (req.user && req.path.startsWith('/api/admin')) {
      try {
        await logAdminActivity(req, res);
      } catch (error) {
        logger.error('Failed to log admin activity', { error });
      }
    }
  });

  next();
};

// Safely parse JSON without throwing errors
function safeJSONParse(str: string) {
  try {
    return JSON.parse(str);
  } catch {
    return str;
  }
}

// Log admin activities to Supabase
async function logAdminActivity(req: Request, res: Response) {
  const adminActivityLog: AdminActivityLog = {
    user_id: req.user?.id || 'unknown',
    action: req.method,
    resource: req.path,
    details: {
      query: req.query,
      body: req.body,
      status: res.statusCode
    },
    timestamp: new Date(),
    ip_address: req.ip || 'unknown'
  };

  const { error } = await supabase
    .from('admin_activity_logs')
    .insert(adminActivityLog);

  if (error) {
    logger.error('Failed to log admin activity', { error, adminActivityLog });
  }
}

export default logger; 