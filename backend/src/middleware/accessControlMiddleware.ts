import { Request, Response, NextFunction } from "express";
import { UserRole } from '@prisma/client';

export enum AccessLevel {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin'
}

export enum ResourceType {
  BOOKING = 'booking',
  USER = 'user',
  VEHICLE = 'vehicle',
  REFUND = 'refund',
  NOTIFICATION = 'notification'
}

// Simple access control middleware that accepts permission strings
export const accessControlMiddleware = (permissions: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // For now, allow all authenticated users (this can be enhanced later)
    // In a real implementation, you would check user permissions against the required permissions
    const permissionsArray = Array.isArray(permissions) ? permissions : [permissions];
    
    // Basic role-based access control
    if (permissionsArray.includes('admin') && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Admin access required' });
    }
    
    if (permissionsArray.includes('provider') && 
        req.user.role !== UserRole.PROVIDER && 
        req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ error: 'Provider access required' });
    }

    next();
  };
};

// Legacy interface check for access control
interface AccessControlData {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
}

// More complex access control with database checks
export const checkAccessControl = async (data: AccessControlData): Promise<boolean> => {
  // For production use, implement proper RBAC logic here
  // For now, return true to allow access
  return true;
};

export default accessControlMiddleware;