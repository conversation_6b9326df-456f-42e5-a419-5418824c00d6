import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { createLogger, transports, format } from 'winston';

// Create a logger for upload events
const uploadLogger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  transports: [
    new transports.File({ filename: 'logs/uploads.log' }),
    new transports.Console()
  ]
});

// Define allowed file types
const ALLOWED_FILE_TYPES = [
  'image/jpeg', 
  'image/png', 
  'image/jpg', 
  'application/pdf'
];

// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10 megabytes

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '..', '..', 'uploads', 'verification');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(
      null, 
      `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`
    );
  }
});

// File filter for upload validation
const fileFilter = (req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  if (!ALLOWED_FILE_TYPES.includes(file.mimetype)) {
    uploadLogger.warn('Unsupported file type uploaded', { 
      mimetype: file.mimetype 
    });
    return cb(new Error('Unsupported file type. Only JPEG, PNG, and PDF are allowed.'));
  }

  cb(null, true);
};

// Configure multer upload
const uploadMiddleware = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 1 // Limit to single file upload
  }
});

export { uploadMiddleware };