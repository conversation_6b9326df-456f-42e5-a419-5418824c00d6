import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { logger } from '../utils/logger';

/**
 * Enhanced file upload security middleware
 */

interface FileUploadOptions {
  maxFileSize?: number;
  allowedMimeTypes?: string[];
  allowedExtensions?: string[];
  maxFiles?: number;
  uploadPath?: string;
  enableVirusScanning?: boolean;
  quarantinePath?: string;
}

export class FileUploadSecurity {
  private static readonly DEFAULT_MAX_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly DEFAULT_UPLOAD_PATH = 'uploads/';
  private static readonly DEFAULT_QUARANTINE_PATH = 'quarantine/';

  /**
   * Allowed MIME types for different file categories
   */
  private static readonly ALLOWED_MIME_TYPES = {
    images: [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml'
    ],
    documents: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv'
    ],
    videos: [
      'video/mp4',
      'video/mpeg',
      'video/quicktime',
      'video/x-msvideo'
    ]
  };

  /**
   * Dangerous file extensions that should never be allowed
   */
  private static readonly DANGEROUS_EXTENSIONS = [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh', '.ps1'
  ];

  /**
   * Validate file type based on MIME type and extension
   */
  static validateFileType(file: Express.Multer.File, allowedTypes: string[]): boolean {
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    // Check for dangerous extensions
    if (this.DANGEROUS_EXTENSIONS.includes(fileExtension)) {
      logger.warn(`Dangerous file extension detected: ${fileExtension}`);
      return false;
    }

    // Check MIME type
    if (!allowedTypes.includes(file.mimetype)) {
      logger.warn(`Invalid MIME type: ${file.mimetype}`);
      return false;
    }

    return true;
  }

  /**
   * Validate file size
   */
  static validateFileSize(file: Express.Multer.File, maxSize: number): boolean {
    if (file.size > maxSize) {
      logger.warn(`File too large: ${file.size} bytes (max: ${maxSize})`);
      return false;
    }
    return true;
  }

  /**
   * Generate secure filename
   */
  static generateSecureFilename(originalName: string): string {
    const extension = path.extname(originalName);
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    return `${timestamp}_${randomBytes}${extension}`;
  }

  /**
   * Simulate virus scanning (in production, integrate with real antivirus)
   */
  static async simulateVirusScanning(filePath: string): Promise<boolean> {
    try {
      // Read file content for basic pattern matching
      const fileContent = fs.readFileSync(filePath, 'utf8');
      
      // Check for suspicious patterns (basic simulation)
      const suspiciousPatterns = [
        /eval\s*\(/gi,
        /exec\s*\(/gi,
        /system\s*\(/gi,
        /shell_exec\s*\(/gi,
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /vbscript:/gi
      ];

      const hasSuspiciousContent = suspiciousPatterns.some(pattern => 
        pattern.test(fileContent)
      );

      if (hasSuspiciousContent) {
        logger.warn(`Suspicious content detected in file: ${filePath}`);
        return false;
      }

      return true;
    } catch (error) {
      // If we can't read the file as text, assume it's binary and safe
      return true;
    }
  }

  /**
   * Quarantine suspicious file
   */
  static async quarantineFile(filePath: string, quarantinePath: string): Promise<void> {
    try {
      const filename = path.basename(filePath);
      const quarantineFilePath = path.join(quarantinePath, `quarantine_${Date.now()}_${filename}`);
      
      // Ensure quarantine directory exists
      if (!fs.existsSync(quarantinePath)) {
        fs.mkdirSync(quarantinePath, { recursive: true });
      }

      // Move file to quarantine
      fs.renameSync(filePath, quarantineFilePath);
      
      logger.warn(`File quarantined: ${quarantineFilePath}`);
    } catch (error) {
      logger.error('Failed to quarantine file:', error);
    }
  }

  /**
   * Create secure multer configuration
   */
  static createSecureMulterConfig(options: FileUploadOptions = {}): multer.Multer {
    const {
      maxFileSize = this.DEFAULT_MAX_SIZE,
      allowedMimeTypes = [...this.ALLOWED_MIME_TYPES.images, ...this.ALLOWED_MIME_TYPES.documents],
      maxFiles = 5,
      uploadPath = this.DEFAULT_UPLOAD_PATH
    } = options;

    // Ensure upload directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        cb(null, uploadPath);
      },
      filename: (req, file, cb) => {
        const secureFilename = this.generateSecureFilename(file.originalname);
        cb(null, secureFilename);
      }
    });

    const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      // Validate file type
      if (!this.validateFileType(file, allowedMimeTypes)) {
        return cb(new Error('Invalid file type'));
      }

      cb(null, true);
    };

    return multer({
      storage,
      fileFilter,
      limits: {
        fileSize: maxFileSize,
        files: maxFiles,
        fieldSize: 1024 * 1024, // 1MB for form fields
        fieldNameSize: 100,
        fields: 10
      }
    });
  }
}

/**
 * Middleware for secure file upload handling
 */
export const secureFileUpload = (options: FileUploadOptions = {}) => {
  const upload = FileUploadSecurity.createSecureMulterConfig(options);
  
  return (req: Request, res: Response, next: NextFunction) => {
    const uploadMiddleware = upload.array('files', options.maxFiles || 5);
    
    uploadMiddleware(req, res, async (err) => {
      if (err) {
        logger.error('File upload error:', err);
        
        if (err instanceof multer.MulterError) {
          switch (err.code) {
            case 'LIMIT_FILE_SIZE':
              return res.status(400).json({ error: 'File too large' });
            case 'LIMIT_FILE_COUNT':
              return res.status(400).json({ error: 'Too many files' });
            case 'LIMIT_UNEXPECTED_FILE':
              return res.status(400).json({ error: 'Unexpected file field' });
            default:
              return res.status(400).json({ error: 'File upload error' });
          }
        }
        
        return res.status(400).json({ error: err.message });
      }

      // Additional security checks for uploaded files
      if (req.files && Array.isArray(req.files)) {
        const quarantinePath = options.quarantinePath || FileUploadSecurity['DEFAULT_QUARANTINE_PATH'];
        
        for (const file of req.files) {
          // Validate file size (double-check)
          if (!FileUploadSecurity.validateFileSize(file, options.maxFileSize || FileUploadSecurity['DEFAULT_MAX_SIZE'])) {
            // Remove the uploaded file
            fs.unlinkSync(file.path);
            return res.status(400).json({ error: 'File too large' });
          }

          // Virus scanning simulation
          if (options.enableVirusScanning) {
            const isClean = await FileUploadSecurity.simulateVirusScanning(file.path);
            if (!isClean) {
              await FileUploadSecurity.quarantineFile(file.path, quarantinePath);
              return res.status(400).json({ error: 'File failed security scan' });
            }
          }

          // Set secure file permissions (read-only)
          try {
            fs.chmodSync(file.path, 0o444);
          } catch (error) {
            logger.warn('Failed to set file permissions:', error);
          }
        }
      }

      next();
    });
  };
};

/**
 * Middleware for image upload with additional validation
 */
export const secureImageUpload = (options: Partial<FileUploadOptions> = {}) => {
  return secureFileUpload({
    ...options,
    allowedMimeTypes: FileUploadSecurity['ALLOWED_MIME_TYPES'].images,
    maxFileSize: options.maxFileSize || 5 * 1024 * 1024, // 5MB for images
    enableVirusScanning: true
  });
};

/**
 * Middleware for document upload with additional validation
 */
export const secureDocumentUpload = (options: Partial<FileUploadOptions> = {}) => {
  return secureFileUpload({
    ...options,
    allowedMimeTypes: FileUploadSecurity['ALLOWED_MIME_TYPES'].documents,
    maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB for documents
    enableVirusScanning: true
  });
};

/**
 * File download security middleware
 */
export const secureFileDownload = (req: Request, res: Response, next: NextFunction) => {
  // Prevent path traversal attacks
  const filename = path.basename(req.params.filename || '');
  
  if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return res.status(400).json({ error: 'Invalid filename' });
  }

  // Set security headers for file downloads
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('X-Frame-Options', 'DENY');

  next();
};

export { FileUploadSecurity };
