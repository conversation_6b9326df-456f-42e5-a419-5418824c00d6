# RentaHub API Documentation

## 🚀 Overview

The RentaHub API is a RESTful service that provides endpoints for vehicle rental management, user authentication, booking processing, and payment handling.

**Base URL:** `https://api.rentahub.com` (Production) | `http://localhost:3001` (Development)

**API Version:** v1

## 🔐 Authentication

### Bearer Token Authentication
```http
Authorization: Bearer <your-jwt-token>
```

### API Key Authentication (for integrations)
```http
X-API-Key: <your-api-key>
```

## 📋 Standard Response Format

All API responses follow this standardized format:

```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": "Validation failed",
  "errors": [
    "Email is required",
    "Password must be at least 8 characters"
  ],
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🔗 API Endpoints

### Authentication Endpoints

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "John Doe",
  "role": "CUSTOMER",
  "phoneNumber": "+**********"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "CUSTOMER",
      "status": "PENDING",
      "emailVerified": false
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400
  },
  "message": "User registered successfully"
}
```

#### POST /api/auth/login
Authenticate user and receive access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "rememberMe": true
}
```

#### GET /api/auth/me
Get current authenticated user information.

**Headers:** `Authorization: Bearer <token>`

### Vehicle Endpoints

#### GET /api/vehicles
Get list of available vehicles with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `sort` (optional): Sort field (default: createdAt)
- `order` (optional): Sort order - asc|desc (default: desc)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "vehicle_123",
      "make": "Honda",
      "model": "Click 125i",
      "year": 2023,
      "category": "SCOOTER",
      "dailyRate": 150000,
      "location": {
        "city": "Jakarta",
        "address": "Jl. Sudirman No. 123"
      },
      "images": ["https://..."],
      "isAvailable": true
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

#### GET /api/vehicles/:id
Get specific vehicle details.

#### GET /api/vehicles/search
Search vehicles with filters.

**Query Parameters:**
- `q`: Search query
- `make`: Vehicle make
- `model`: Vehicle model
- `category`: Vehicle category
- `city`: Location city
- `minPrice`: Minimum daily rate
- `maxPrice`: Maximum daily rate

#### GET /api/vehicles/suggestions
Get vehicle suggestions for autocomplete.

**Query Parameters:**
- `query`: Search term
- `limit`: Number of suggestions (default: 10)

### Booking Endpoints

#### POST /api/bookings
Create a new booking.

**Request Body:**
```json
{
  "vehicleId": "vehicle_123",
  "startDate": "2024-02-01T10:00:00.000Z",
  "endDate": "2024-02-05T10:00:00.000Z",
  "pickupLocation": "Jl. Sudirman No. 123",
  "dropoffLocation": "Jl. Thamrin No. 456",
  "additionalRequests": "Need helmet"
}
```

#### GET /api/bookings
Get user's bookings.

#### GET /api/bookings/:id
Get specific booking details.

#### PUT /api/bookings/:id/status
Update booking status (Provider/Admin only).

### Payment Endpoints

#### POST /api/payments/create-payment-intent
Create Stripe payment intent.

#### POST /api/payments/confirm-payment
Confirm payment completion.

#### GET /api/payments/history
Get payment history.

### User Management Endpoints

#### GET /api/users/profile
Get user profile.

#### PUT /api/users/profile
Update user profile.

#### POST /api/users/upload-avatar
Upload profile picture.

### Admin Endpoints

#### GET /api/admin/dashboard/stats
Get dashboard statistics (Admin only).

#### GET /api/admin/users
Get all users (Admin only).

#### GET /api/admin/bookings
Get all bookings (Admin only).

## 📊 HTTP Status Codes

- `200` - OK: Successful GET, PUT, PATCH
- `201` - Created: Successful POST
- `204` - No Content: Successful DELETE
- `400` - Bad Request: Invalid request data
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Access denied
- `404` - Not Found: Resource not found
- `422` - Unprocessable Entity: Validation errors
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## 🔄 Rate Limiting

- **General API**: 100 requests per 15 minutes
- **Authentication**: 5 requests per 15 minutes
- **File Upload**: 10 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## 🔍 Filtering & Sorting

### Filtering
Use query parameters for filtering:
```
GET /api/vehicles?category=SCOOTER&city=Jakarta&minPrice=100000
```

### Sorting
```
GET /api/vehicles?sort=dailyRate&order=asc
```

### Field Selection
```
GET /api/vehicles?fields=id,make,model,dailyRate
```

## 📝 Pagination

All list endpoints support pagination:
```
GET /api/vehicles?page=2&limit=10
```

Response includes pagination metadata:
```json
{
  "meta": {
    "page": 2,
    "limit": 10,
    "total": 150,
    "totalPages": 15
  }
}
```

## 🚨 Error Handling

### Validation Errors (422)
```json
{
  "success": false,
  "error": "Validation failed",
  "errors": [
    "Email is required",
    "Password must be at least 8 characters"
  ]
}
```

### Authentication Errors (401)
```json
{
  "success": false,
  "error": "Invalid or expired token"
}
```

### Not Found Errors (404)
```json
{
  "success": false,
  "error": "Vehicle not found"
}
```

## 🔧 Development

### Testing the API
```bash
# Health check
curl http://localhost:3001/health

# Get vehicles
curl -H "Authorization: Bearer <token>" \
     http://localhost:3001/api/vehicles
```

### Postman Collection
Import our Postman collection: [RentaHub API.postman_collection.json](./postman/RentaHub_API.postman_collection.json)
