# RentaHub Backend Architecture

## 📁 Directory Structure

```
backend/src/
├── config/                 # Configuration files
│   ├── environment.ts      # Environment validation & config
│   ├── database.ts         # Database configuration
│   └── cors.ts            # CORS configuration
├── controllers/            # Request handlers
│   ├── auth/              # Authentication controllers
│   ├── vehicles/          # Vehicle-related controllers
│   ├── bookings/          # Booking controllers
│   ├── payments/          # Payment controllers
│   └── admin/             # Admin controllers
├── middleware/            # Express middleware
│   ├── auth.ts           # Authentication middleware
│   ├── validation.ts     # Input validation
│   ├── rateLimit.ts      # Rate limiting
│   └── errorHandler.ts   # Error handling
├── routes/               # API route definitions
│   ├── v1/              # API version 1
│   │   ├── auth.ts      # Authentication routes
│   │   ├── vehicles.ts  # Vehicle routes
│   │   ├── bookings.ts  # Booking routes
│   │   └── index.ts     # Route aggregation
│   └── index.ts         # Main router
├── services/            # Business logic layer
│   ├── auth/           # Authentication services
│   ├── vehicles/       # Vehicle services
│   ├── bookings/       # Booking services
│   ├── payments/       # Payment services
│   └── notifications/  # Notification services
├── models/             # Data models (if using non-Prisma models)
├── lib/                # External service integrations
│   ├── database.ts     # Prisma client
│   ├── supabase.ts     # Supabase client
│   ├── stripe.ts       # Stripe client
│   └── redis.ts        # Redis client
├── utils/              # Utility functions
│   ├── apiResponse.ts  # Standardized responses
│   ├── apiStandards.ts # API conventions
│   ├── validation.ts   # Validation helpers
│   └── logger.ts       # Logging utilities
├── types/              # TypeScript type definitions
│   ├── api.ts         # API types
│   ├── auth.ts        # Authentication types
│   └── database.ts    # Database types
├── scripts/            # Utility scripts
│   ├── seed.ts        # Database seeding
│   └── migrate.ts     # Migration scripts
└── tests/              # Test files
    ├── unit/          # Unit tests
    ├── integration/   # Integration tests
    └── fixtures/      # Test data
```

## 🏗️ Architecture Patterns

### 1. **Layered Architecture**
```
Routes → Controllers → Services → Database
```

### 2. **Dependency Injection**
- Services are injected into controllers
- Database clients are injected into services
- Configuration is centralized

### 3. **Error Handling**
- Centralized error handling middleware
- Standardized error responses
- Proper error logging

### 4. **Validation**
- Input validation at route level
- Business logic validation in services
- Database constraints as final safety net

## 🔄 Request Flow

1. **Request** → Rate Limiting Middleware
2. **Authentication** → Auth Middleware (if required)
3. **Validation** → Input Validation Middleware
4. **Routing** → Route Handler
5. **Controller** → Business Logic Orchestration
6. **Service** → Core Business Logic
7. **Database** → Data Persistence
8. **Response** → Standardized API Response

## 📋 Naming Conventions

### Files & Directories
- **kebab-case** for directories: `vehicle-listings/`
- **camelCase** for files: `vehicleController.ts`
- **PascalCase** for classes: `VehicleService.ts`

### Code
- **camelCase** for variables and functions: `getUserById()`
- **PascalCase** for classes and interfaces: `UserService`, `ApiResponse`
- **UPPER_SNAKE_CASE** for constants: `MAX_FILE_SIZE`

### API Endpoints
- **kebab-case** for URLs: `/api/vehicle-listings`
- **Plural nouns** for collections: `/api/vehicles`
- **RESTful patterns**: GET `/api/vehicles/:id`

## 🔐 Security Layers

1. **Network Level**: Rate limiting, CORS
2. **Authentication**: JWT tokens, OAuth
3. **Authorization**: Role-based access control
4. **Input Validation**: Schema validation
5. **Database**: Parameterized queries, constraints

## 📊 Monitoring & Logging

- **Request Logging**: All API requests
- **Error Logging**: Centralized error tracking
- **Performance Monitoring**: Response times
- **Health Checks**: System status endpoints

## 🚀 Deployment

- **Environment**: Configuration validation
- **Database**: Connection pooling, migrations
- **Caching**: Redis for session/data caching
- **Monitoring**: Health checks, metrics
