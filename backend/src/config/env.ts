import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables from .env file
dotenv.config();

// Define environment variable schema
const envSchema = z.object({
  // Existing configurations
  DATABASE_URL: z.string().optional(),
  PORT: z.coerce.number().default(3001), // Fix: coerce string to number
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // JWT Configuration
  JWT_SECRET: z.string(),
  JWT_EXPIRATION: z.string().default('1h'),
  
  // Stripe Configuration
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // OAuth Providers
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  GOOGLE_CALLBACK_URL: z.string().optional(),
  
  // Email Configuration
  EMAIL_HOST: z.string().default('smtp.gmail.com'),
  EMAIL_PORT: z.coerce.number().default(465),
  EMAIL_SECURE: z.coerce.boolean().default(true),
  EMAIL_USER: z.string().optional(), // Make optional for development
  EMAIL_PASS: z.string().optional(), // Make optional for development
  EMAIL_FROM: z.string().default('<EMAIL>'),
  
  // Frontend URL for email links
  FRONTEND_URL: z.string().default('http://localhost:3000')
});

// Validate and parse environment variables
const parsedEnv = envSchema.parse(process.env);

// Export configuration
export const config = {
  ...parsedEnv,
  
  // Additional derived configurations
  isDevelopment: parsedEnv.NODE_ENV === 'development',
  isProduction: parsedEnv.NODE_ENV === 'production',
  
  // Optional feature flags or environment-specific settings
  features: {
    enableSignup: true,
    enableOAuth: !!parsedEnv.GOOGLE_CLIENT_ID,
    enableEmailVerification: true
  }
};
