import { config } from 'dotenv';
import { z } from 'zod';
import { URL } from 'url';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  // Deployment
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  
  // Database
  DATABASE_URL: z.string().min(1, 'Database URL is required'),
  
  // Supabase
  SUPABASE_URL: z.string().url('Invalid Supabase URL'),
  SUPABASE_ANON_KEY: z.string().min(1, 'Supabase anon key is required'),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1, 'Supabase service role key is required'),
  
  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRATION: z.string().default('24h'),
  
  // Stripe (optional)
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // Google OAuth (optional)
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  GOOGLE_CALLBACK_URL: z.string().url().optional(),
  
  // Email (optional)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  SMTP_FROM: z.string().optional(),
  
  // CORS & URLs
  FRONTEND_URL: z.string().url().default('http://localhost:3000'),
  ADMIN_URL: z.string().url().default('http://localhost:3002'),
  BACKEND_URL: z.string().url().default('http://localhost:3001'),
  CORS_ORIGIN: z.string().optional(),
  
  // Performance
  REDIS_URL: z.string().optional(),
  CACHE_TTL: z.string().transform(Number).default('3600'),
  
  // Rate limiting
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  
  // File uploads
  MAX_FILE_SIZE: z.string().transform(Number).default('********'), // 10MB
  UPLOAD_PATH: z.string().default('uploads/'),
  
  // Debugging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  DEBUG: z.string().transform(val => val === 'true').default('false'),
  
  // Optional services
  SENTRY_DSN: z.string().optional(),
  GOOGLE_MAPS_API_KEY: z.string().optional(),
  TWILIO_ACCOUNT_SID: z.string().optional(),
  TWILIO_AUTH_TOKEN: z.string().optional(),
});

// Validate environment variables
let env: z.infer<typeof envSchema>;

try {
  env = envSchema.parse(process.env);
  console.log('✅ Environment variables validated successfully');
} catch (error) {
  console.error('❌ Environment validation failed:');
  console.error('Error details:', error);
  if (error instanceof z.ZodError) {
    console.error('ZodError detected, processing errors...');
    if (error.errors && Array.isArray(error.errors)) {
      error.errors.forEach(err => {
        console.error(`  - ${err.path?.join('.') || 'unknown'}: ${err.message}`);
      });
    } else {
      console.error('No errors array found in ZodError');
    }
  } else {
    console.error('Not a ZodError:', typeof error, error);
  }
  process.exit(1);
}

// Export validated environment
export default env;

// Export individual configs for convenience
export const {
  NODE_ENV,
  PORT,
  DATABASE_URL,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY,
  JWT_SECRET,
  JWT_EXPIRATION,
  STRIPE_SECRET_KEY,
  STRIPE_PUBLISHABLE_KEY,
  STRIPE_WEBHOOK_SECRET,
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  GOOGLE_CALLBACK_URL,
  FRONTEND_URL,
  ADMIN_URL,
  BACKEND_URL,
  CORS_ORIGIN,
  REDIS_URL,
  CACHE_TTL,
  RATE_LIMIT_WINDOW,
  RATE_LIMIT_MAX,
  MAX_FILE_SIZE,
  UPLOAD_PATH,
  LOG_LEVEL,
  DEBUG,
} = env;

// Helper functions
export const isDevelopment = () => NODE_ENV === 'development';
export const isProduction = () => NODE_ENV === 'production';
export const isTest = () => NODE_ENV === 'test';

// CORS origins array
export const getCorsOrigins = (): string[] => {
  if (CORS_ORIGIN) {
    return CORS_ORIGIN.split(',').map(origin => origin.trim());
  }
  return [FRONTEND_URL, ADMIN_URL];
};

// Database connection string validation
export const validateDatabaseConnection = (): boolean => {
  try {
    const url = new URL(DATABASE_URL);
    return url.protocol === 'postgresql:' || url.protocol === 'postgres:';
  } catch {
    return false;
  }
};

// Stripe configuration check
export const hasStripeConfig = (): boolean => {
  return !!(STRIPE_SECRET_KEY && STRIPE_PUBLISHABLE_KEY);
};

// Email configuration check
export const hasEmailConfig = (): boolean => {
  return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASS);
};

// Google OAuth configuration check
export const hasGoogleOAuthConfig = (): boolean => {
  return !!(GOOGLE_CLIENT_ID && GOOGLE_CLIENT_SECRET);
};

// Redis configuration check
export const hasRedisConfig = (): boolean => {
  return !!REDIS_URL;
};

// Environment summary for logging
export const getEnvironmentSummary = () => ({
  environment: NODE_ENV,
  port: PORT,
  database: validateDatabaseConnection() ? '✅ Valid' : '❌ Invalid',
  supabase: SUPABASE_URL ? '✅ Configured' : '❌ Missing',
  stripe: hasStripeConfig() ? '✅ Configured' : '⚠️ Optional',
  email: hasEmailConfig() ? '✅ Configured' : '⚠️ Optional',
  oauth: hasGoogleOAuthConfig() ? '✅ Configured' : '⚠️ Optional',
  redis: hasRedisConfig() ? '✅ Configured' : '⚠️ Optional',
});
