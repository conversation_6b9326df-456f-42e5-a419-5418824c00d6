import express, { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const port = 3002;

// This is your Stripe CLI webhook secret for testing your endpoint locally.
const endpointSecret = 'whsec_13bb090c39c0e6d3a3d4d609de2e1ce3092100fae2f40393f0a5bc6b72ee438a';

// Use raw body parser for Stripe webhooks
app.post('/webhook', express.raw({type: 'application/json'}), (request, response) => {
  const sig = request.headers['stripe-signature'];

  console.log('Received webhook!');
  console.log('Signature:', sig);
  
  if (!sig) {
    console.error('Missing signature');
    return response.status(400).send('Missing signature');
  }

  let event;

  try {
    // Verify the webhook signature
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '');
    event = stripe.webhooks.constructEvent(request.body, sig, endpointSecret);
    
    console.log('Webhook verified! Event type:', event.type);
    
    // Return a 200 response to acknowledge receipt of the event
    response.json({received: true});
  } catch (err) {
    console.error('Webhook Error:', err);
    response.status(400).send(`Webhook Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
  }
});

app.listen(port, () => {
  console.log(`Test webhook server running at http://localhost:${port}`);
  console.log(`Webhook endpoint: http://localhost:${port}/webhook`);
});
