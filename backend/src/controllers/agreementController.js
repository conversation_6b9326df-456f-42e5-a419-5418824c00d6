const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');
const AgreementNotificationService = require('../services/AgreementNotificationService');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const notificationService = new AgreementNotificationService();

class AgreementController {
  /**
   * Get agreement templates for a provider
   */
  static async getTemplates(req, res) {
    try {
      const providerId = req.user?.id || req.headers['x-user-id'];
      
      const query = `
        SELECT 
          t.id,
          t.name,
          t.type,
          t.description,
          t.is_active,
          t.is_default,
          t.created_at,
          t.updated_at,
          COALESCE(
            json_agg(
              json_build_object(
                'id', s.id,
                'title', s.title,
                'content', s.content,
                'is_required', s.is_required,
                'order', s.section_order,
                'variables', s.variables
              ) ORDER BY s.section_order
            ) FILTER (WHERE s.id IS NOT NULL),
            '[]'::json
          ) as sections
        FROM agreement_templates t
        LEFT JOIN agreement_template_sections s ON t.id = s.template_id
        WHERE t.is_active = true 
          AND (t.created_by IS NULL OR t.created_by = $1)
        GROUP BY t.id, t.name, t.type, t.description, t.is_active, t.is_default, t.created_at, t.updated_at
        ORDER BY t.is_default DESC, t.created_at DESC
      `;
      
      const result = await pool.query(query, [providerId]);
      
      res.json({
        success: true,
        data: result.rows
      });
    } catch (error) {
      console.error('Error fetching agreement templates:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch agreement templates'
      });
    }
  }

  /**
   * Generate agreement content from template and booking data
   */
  static async generateAgreement(req, res) {
    try {
      const { templateId, bookingId, customClauses = [] } = req.body;
      const providerId = req.user?.id || req.headers['x-user-id'];

      // Get template with sections
      const templateQuery = `
        SELECT 
          t.*,
          json_agg(
            json_build_object(
              'id', s.id,
              'title', s.title,
              'content', s.content,
              'is_required', s.is_required,
              'order', s.section_order,
              'variables', s.variables
            ) ORDER BY s.section_order
          ) as sections
        FROM agreement_templates t
        LEFT JOIN agreement_template_sections s ON t.id = s.template_id
        WHERE t.id = $1 AND t.is_active = true
        GROUP BY t.id
      `;

      // Get booking data with related information
      const bookingQuery = `
        SELECT 
          b.*,
          u.name as customer_name,
          u.email as customer_email,
          u.phone as customer_phone,
          v.brand || ' ' || v.model as vehicle_name,
          v.year as vehicle_year,
          v.specifications->>'licensePlate' as vehicle_license,
          v.daily_rate,
          v.deposit_amount,
          p.name as provider_name,
          p.company_name as provider_company,
          p.phone as provider_phone
        FROM bookings b
        JOIN users u ON b.user_id = u.id
        JOIN vehicles v ON b.vehicle_id = v.id
        JOIN providers p ON b.provider_id = p.id
        WHERE b.id = $1 AND b.provider_id = $2
      `;

      const [templateResult, bookingResult] = await Promise.all([
        pool.query(templateQuery, [templateId]),
        pool.query(bookingQuery, [bookingId, providerId])
      ]);

      if (templateResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Template not found'
        });
      }

      if (bookingResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Booking not found'
        });
      }

      const template = templateResult.rows[0];
      const booking = bookingResult.rows[0];

      // Generate agreement content
      const generatedContent = this.generateAgreementContent(template, booking, customClauses);

      // Save generated agreement
      const agreementId = uuidv4();
      const insertQuery = `
        INSERT INTO agreements (
          id, booking_id, template_id, provider_id, customer_id,
          title, content, generated_content, status, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      `;

      const metadata = {
        customerName: booking.customer_name,
        vehicleName: booking.vehicle_name,
        rentalPeriod: `${new Date(booking.start_date).toLocaleDateString()} - ${new Date(booking.end_date).toLocaleDateString()}`,
        totalAmount: parseFloat(booking.total)
      };

      const agreementResult = await pool.query(insertQuery, [
        agreementId,
        bookingId,
        templateId,
        providerId,
        booking.user_id,
        `${template.name} - ${booking.customer_name}`,
        JSON.stringify(template.sections),
        generatedContent,
        'draft',
        JSON.stringify(metadata)
      ]);

      // Log agreement creation
      await this.logAgreementAction(agreementId, 'created', providerId, 'provider');

      res.json({
        success: true,
        data: {
          agreement: agreementResult.rows[0],
          content: generatedContent
        }
      });
    } catch (error) {
      console.error('Error generating agreement:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate agreement'
      });
    }
  }

  /**
   * Send agreement to customer
   */
  static async sendAgreement(req, res) {
    try {
      const { bookingId, templateId, content, settings = {}, metadata = {} } = req.body;
      const providerId = req.user?.id || req.headers['x-user-id'];

      // Create and send agreement in one step
      const agreementId = uuidv4();

      // Get customer info from booking
      const bookingQuery = `
        SELECT b.user_id, u.email as customer_email, u.name as customer_name
        FROM bookings b
        JOIN users u ON b.user_id = u.id
        WHERE b.id = $1 AND b.provider_id = $2
      `;

      const bookingResult = await pool.query(bookingQuery, [bookingId, providerId]);

      if (bookingResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Booking not found'
        });
      }

      const booking = bookingResult.rows[0];

      // Insert agreement with sent status
      const insertQuery = `
        INSERT INTO agreements (
          id, booking_id, template_id, provider_id, customer_id,
          title, generated_content, status, sent_at, settings, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9, $10)
        RETURNING *
      `;

      const title = `Rental Agreement - ${metadata.customerName || booking.customer_name}`;

      const result = await pool.query(insertQuery, [
        agreementId,
        bookingId,
        templateId,
        providerId,
        booking.user_id,
        title,
        content,
        'sent',
        JSON.stringify(settings),
        JSON.stringify(metadata)
      ]);

      const agreement = result.rows[0];

      // Log agreement actions
      await this.logAgreementAction(agreementId, 'created', providerId, 'provider');
      await this.logAgreementAction(agreementId, 'sent', providerId, 'provider');

      // Send email/SMS notification to customer
      try {
        await notificationService.sendAgreementNotification(
          agreement,
          booking.customer_email,
          booking.customer_phone
        );
      } catch (notificationError) {
        console.error('Failed to send agreement notification:', notificationError);
        // Don't fail the entire request if notification fails
      }

      res.json({
        success: true,
        data: agreement
      });
    } catch (error) {
      console.error('Error sending agreement:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to send agreement'
      });
    }
  }

  /**
   * Get agreements for a provider
   */
  static async getAgreements(req, res) {
    try {
      const providerId = req.user?.id || req.headers['x-user-id'];
      const { status, limit = 50, offset = 0 } = req.query;

      let query = `
        SELECT 
          a.*,
          u.name as customer_name,
          u.email as customer_email,
          v.brand || ' ' || v.model as vehicle_name,
          COUNT(s.id) as signature_count
        FROM agreements a
        JOIN users u ON a.customer_id = u.id
        JOIN bookings b ON a.booking_id = b.id
        JOIN vehicles v ON b.vehicle_id = v.id
        LEFT JOIN agreement_signatures s ON a.id = s.agreement_id
        WHERE a.provider_id = $1
      `;

      const params = [providerId];

      if (status) {
        query += ` AND a.status = $${params.length + 1}`;
        params.push(status);
      }

      query += `
        GROUP BY a.id, u.name, u.email, v.brand, v.model
        ORDER BY a.created_at DESC
        LIMIT $${params.length + 1} OFFSET $${params.length + 2}
      `;

      params.push(limit, offset);

      const result = await pool.query(query, params);

      res.json({
        success: true,
        data: result.rows
      });
    } catch (error) {
      console.error('Error fetching agreements:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch agreements'
      });
    }
  }

  /**
   * Generate agreement content by replacing variables
   */
  static generateAgreementContent(template, booking, customClauses = []) {
    let content = '';

    // Process each section
    template.sections.forEach(section => {
      content += `\n\n## ${section.title}\n\n`;
      
      let sectionContent = section.content;
      
      // Replace variables with actual data
      const variables = {
        '{{CUSTOMER_NAME}}': booking.customer_name || 'Customer',
        '{{CUSTOMER_EMAIL}}': booking.customer_email || '',
        '{{CUSTOMER_PHONE}}': booking.customer_phone || '',
        '{{PROVIDER_NAME}}': booking.provider_name || 'Provider',
        '{{PROVIDER_COMPANY}}': booking.provider_company || '',
        '{{PROVIDER_PHONE}}': booking.provider_phone || '',
        '{{VEHICLE_NAME}}': booking.vehicle_name || 'Vehicle',
        '{{VEHICLE_YEAR}}': booking.vehicle_year || '',
        '{{VEHICLE_LICENSE}}': booking.vehicle_license || '',
        '{{RENTAL_START}}': new Date(booking.start_date).toLocaleDateString(),
        '{{RENTAL_END}}': new Date(booking.end_date).toLocaleDateString(),
        '{{RENTAL_DAYS}}': Math.ceil((new Date(booking.end_date) - new Date(booking.start_date)) / (1000 * 60 * 60 * 24)),
        '{{TOTAL_AMOUNT}}': `$${parseFloat(booking.total || 0).toFixed(2)}`,
        '{{DAILY_RATE}}': `$${parseFloat(booking.daily_rate || 0).toFixed(2)}`,
        '{{DEPOSIT_AMOUNT}}': `$${parseFloat(booking.deposit_amount || 0).toFixed(2)}`,
        '{{PICKUP_LOCATION}}': booking.pickup_address || '',
        '{{INSURANCE_TYPE}}': booking.insurance_option || 'Basic',
        '{{BOOKING_ID}}': booking.id,
        '{{AGREEMENT_DATE}}': new Date().toLocaleDateString(),
        '{{PAYMENT_METHOD}}': booking.payment_method || 'Card',
      };

      // Replace all variables
      Object.entries(variables).forEach(([variable, value]) => {
        sectionContent = sectionContent.replace(new RegExp(variable, 'g'), value);
      });

      content += sectionContent;
    });

    // Add custom clauses
    if (customClauses.length > 0) {
      content += '\n\n## Additional Terms\n\n';
      customClauses.forEach((clause, index) => {
        content += `${index + 1}. ${clause}\n\n`;
      });
    }

    // Add signature section
    content += '\n\n## Signatures\n\n';
    content += 'By signing below, both parties agree to the terms and conditions outlined in this agreement.\n\n';
    content += `**Customer Signature:** ___________________________ Date: ___________\n`;
    content += `**Provider Signature:** ___________________________ Date: ___________\n\n`;
    content += `Agreement ID: ${booking.id}-${Date.now()}\n`;
    content += `Generated on: ${new Date().toLocaleString()}`;

    return content;
  }

  /**
   * Log agreement action for audit trail
   */
  static async logAgreementAction(agreementId, action, actorId, actorType, notes = null) {
    try {
      const query = `
        INSERT INTO agreement_history (
          agreement_id, action, actor_id, actor_type, notes
        ) VALUES ($1, $2, $3, $4, $5)
      `;
      
      await pool.query(query, [agreementId, action, actorId, actorType, notes]);
    } catch (error) {
      console.error('Error logging agreement action:', error);
    }
  }
}

module.exports = AgreementController;
