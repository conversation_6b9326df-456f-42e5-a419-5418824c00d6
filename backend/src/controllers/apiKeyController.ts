import { Request, Response, NextFunction } from "express";
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

export class APIKeyController {
  /**
   * Create a new API key
   * @param req Express request
   * @param res Express response
   */
  static async createAPIKey(req: Request, res: Response) {
    try {
      const { keyName, permissions } = req.body;

      // Generate a secure random API key
      const apiKey = crypto.randomBytes(32).toString('hex');

      // Store the API key
      const newAPIKey = await prisma.apiKey.create({
        data: {
          keyName,
          keyValue: apiKey,
          permissions: permissions || []
        }
      });

      res.status(201).json({
        message: 'API key created successfully',
        key: {
          id: newAPIKey.id,
          keyName: newAPIKey.keyName,
          keyValue: newAPIKey.keyValue,
          permissions: newAPIKey.permissions
        }
      });
    } catch (error) {
      console.error('Error creating API key:', error);
      res.status(500).json({ error: 'Failed to create API key' });
    }
  }

  /**
   * List all API keys
   * @param req Express request
   * @param res Express response
   */
  static async listAPIKeys(req: Request, res: Response) {
    try {
      const { active, page = 1, limit = 20 } = req.query;

      const whereClause: any = {};
      if (active !== undefined) whereClause.isActive = active === 'true';

      const apiKeys = await prisma.apiKey.findMany({
        where: whereClause,
        select: {
          id: true,
          keyName: true,
          isActive: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit)
      });

      const total = await prisma.apiKey.count({ where: whereClause });

      res.json({
        keys: apiKeys,
        page: Number(page),
        limit: Number(limit),
        total
      });
    } catch (error) {
      console.error('Error listing API keys:', error);
      res.status(500).json({ error: 'Failed to retrieve API keys' });
    }
  }

  /**
   * Delete an API key
   * @param req Express request
   * @param res Express response
   */
  static async deleteAPIKey(req: Request, res: Response) {
    try {
      const { id } = req.params;

      // Delete the API key
      await prisma.apiKey.delete({
        where: { id }
      });

      res.json({ message: 'API key deleted successfully' });
    } catch (error) {
      console.error('Error deleting API key:', error);
      res.status(500).json({ error: 'Failed to delete API key' });
    }
  }

  /**
   * Check the status of external services
   * @param req Express request
   * @param res Express response
   */
  static async checkServiceStatus(req: Request, res: Response) {
    try {
      const services = [
        { name: 'Stripe', checkFunction: () => this.checkStripeStatus() },
        { name: 'SendGrid', checkFunction: () => this.checkSendGridStatus() },
        { name: 'Google Maps', checkFunction: () => this.checkGoogleMapsStatus() }
      ];

      const serviceStatuses = await Promise.all(
        services.map(async (service) => {
          try {
            const status = await service.checkFunction();
            return { 
              name: service.name, 
              status, 
              lastChecked: new Date() 
            };
          } catch (error) {
            return { 
              name: service.name, 
              status: 'error', 
              error: error instanceof Error ? error.message : 'Unknown error',
              lastChecked: new Date() 
            };
          }
        })
      );

      res.json({ services: serviceStatuses });
    } catch (error) {
      console.error('Error checking service status:', error);
      res.status(500).json({ error: 'Failed to check service status' });
    }
  }

  /**
   * Check Stripe service status
   * @returns Service status
   */
  private static async checkStripeStatus(): Promise<string> {
    try {
      // Implement Stripe API health check
      // This could be a simple API call or account retrieval
      const stripeKey = await prisma.apiKey.findFirst({ 
        where: { keyName: { contains: 'stripe' } } 
      });

      if (!stripeKey) {
        return 'no_key';
      }

      // Placeholder for actual Stripe API check
      // In a real implementation, you'd use the Stripe SDK to verify connectivity
      return 'operational';
    } catch (error) {
      return 'error';
    }
  }

  /**
   * Check SendGrid service status
   * @returns Service status
   */
  private static async checkSendGridStatus(): Promise<string> {
    try {
      // Implement SendGrid API health check
      const sendgridKey = await prisma.apiKey.findFirst({ 
        where: { keyName: { contains: 'sendgrid' } } 
      });

      if (!sendgridKey) {
        return 'no_key';
      }

      // Placeholder for actual SendGrid API check
      return 'operational';
    } catch (error) {
      return 'error';
    }
  }

  /**
   * Check Google Maps service status
   * @returns Service status
   */
  private static async checkGoogleMapsStatus(): Promise<string> {
    try {
      // Implement Google Maps API health check
      const googleMapsKey = await prisma.apiKey.findFirst({ 
        where: { keyName: { contains: 'google_maps' } } 
      });

      if (!googleMapsKey) {
        return 'no_key';
      }

      // Placeholder for actual Google Maps API check
      return 'operational';
    } catch (error) {
      return 'error';
    }
  }
}

export default APIKeyController;
