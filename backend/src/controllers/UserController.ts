import { Request, Response, NextFunction } from "express";
import { supabase } from '../utils/supabaseClient';
import { ServiceError } from '../services/BaseService';

export class UserController {
  // Get all users (with optional filtering)
  static async getAllUsers(req: Request, res: Response, next: NextFunction) {
    try {
      const { page = 1, limit = 10, filter = {} } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      const query = supabase
        .from('users')
        .select('*', { count: 'exact' })
        .range(offset, offset + Number(limit) - 1);

      // Apply optional filters
      Object.entries(filter).forEach(([key, value]) => {
        query.eq(key, value);
      });

      const { data, count, error } = await query;

      if (error) throw new ServiceError(error.message, 500);

      res.json({
        users: data,
        totalUsers: count,
        page: Number(page),
        limit: Number(limit)
      });
    } catch (error) {
      next(error);
    }
  }

  // Get user by ID
  static async getUserById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw new ServiceError(error.message, 404);
      if (!data) throw new ServiceError('User not found', 404);

      res.json(data);
    } catch (error) {
      next(error);
    }
  }

  // Create a new user
  static async createUser(req: Request, res: Response, next: NextFunction) {
    try {
      const userData = req.body;
      const { data, error } = await supabase
        .from('users')
        .insert(userData)
        .select()
        .single();

      if (error) throw new ServiceError(error.message, 400);

      res.status(201).json(data);
    } catch (error) {
      next(error);
    }
  }

  // Update user
  static async updateUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userData = req.body;

      const { data, error } = await supabase
        .from('users')
        .update(userData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new ServiceError(error.message, 400);
      if (!data) throw new ServiceError('User not found', 404);

      res.json(data);
    } catch (error) {
      next(error);
    }
  }

  // Delete user
  static async deleteUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (error) throw new ServiceError(error.message, 400);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }

  // Get user profile
  static async getUserProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { data: user, error } = await supabase
        .from('users')
        .select('id, name, email, role, phoneNumber, emailVerified, phoneVerified')
        .eq('id', userId)
        .single();

      if (error) throw new ServiceError(error.message, 404);

      res.json({ user });
    } catch (error) {
      next(error);
    }
  }

  // Update user profile
  static async updateUserProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { name, phoneNumber } = req.body;
      
      const { data: user, error } = await supabase
        .from('users')
        .update({ name, phoneNumber })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw new ServiceError(error.message, 400);

      res.json({ user });
    } catch (error) {
      next(error);
    }
  }

  // Register as provider
  static async registerAsProvider(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const { businessName, businessAddress, licenseNumber } = req.body;
      
      // Update user role to provider and add provider details
      const { data: user, error: userError } = await supabase
        .from('users')
        .update({ role: 'PROVIDER' })
        .eq('id', userId)
        .select()
        .single();

      if (userError) throw new ServiceError(userError.message, 400);

      // Create provider profile
      const { data: provider, error: providerError } = await supabase
        .from('providers')
        .insert({
          userId,
          businessName,
          businessAddress,
          licenseNumber,
          status: 'PENDING'
        })
        .select()
        .single();

      if (providerError) throw new ServiceError(providerError.message, 400);

      res.json({ user, provider });
    } catch (error) {
      next(error);
    }
  }
}

export default UserController;
