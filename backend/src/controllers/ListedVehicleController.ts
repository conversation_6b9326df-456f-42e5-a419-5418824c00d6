import { Request, Response } from 'express';
import ListedVehicleService from '../services/ListedVehicleService';
import { logger } from '../utils/logger';

export class ListedVehicleController {
  /**
   * Get active listed vehicles for home page
   * GET /api/listed-vehicles
   */
  static async getActiveVehicles(req: Request, res: Response) {
    try {
      const { limit = 20 } = req.query;
      const limitNum = parseInt(limit as string, 10);

      const vehicles = await ListedVehicleService.getActiveListedVehicles(limitNum);
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        hasListings: vehicles.length > 0
      });
    } catch (error) {
      logger.error('Error in getActiveVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch active vehicles',
        message: error instanceof Error ? error.message : 'Unknown error',
        hasListings: false
      });
    }
  }

  /**
   * Get featured vehicles for home page
   * GET /api/listed-vehicles/featured
   */
  static async getFeaturedVehicles(req: Request, res: Response) {
    try {
      const { limit = 6 } = req.query;
      const limitNum = parseInt(limit as string, 10);

      const vehicles = await ListedVehicleService.getFeaturedVehicles(limitNum);
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        hasListings: vehicles.length > 0
      });
    } catch (error) {
      logger.error('Error in getFeaturedVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch featured vehicles',
        message: error instanceof Error ? error.message : 'Unknown error',
        hasListings: false
      });
    }
  }

  /**
   * Search listed vehicles
   * GET /api/listed-vehicles/search?q=query&category=&minPrice=&maxPrice=&city=
   */
  static async searchVehicles(req: Request, res: Response) {
    try {
      const { 
        q: query, 
        category, 
        minPrice, 
        maxPrice, 
        city, 
        make, 
        transmission, 
        fuelType,
        limit = 20 
      } = req.query;

      const filters = {
        category: category as string,
        minPrice: minPrice ? parseInt(minPrice as string, 10) : undefined,
        maxPrice: maxPrice ? parseInt(maxPrice as string, 10) : undefined,
        city: city as string,
        make: make as string,
        transmission: transmission as string,
        fuelType: fuelType as string
      };

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof typeof filters] === undefined) {
          delete filters[key as keyof typeof filters];
        }
      });

      const limitNum = parseInt(limit as string, 10);
      const vehicles = await ListedVehicleService.searchListedVehicles(
        query as string, 
        filters, 
        limitNum
      );
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        query: query || '',
        filters,
        hasListings: vehicles.length > 0
      });
    } catch (error) {
      logger.error('Error in searchVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search vehicles',
        message: error instanceof Error ? error.message : 'Unknown error',
        hasListings: false
      });
    }
  }

  /**
   * Get vehicle by ID
   * GET /api/listed-vehicles/:id
   */
  static async getVehicleById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      const vehicle = await ListedVehicleService.getListedVehicleById(id);
      
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          error: 'Vehicle not found'
        });
      }

      res.json({
        success: true,
        data: vehicle
      });
    } catch (error) {
      logger.error('Error in getVehicleById:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get listing statistics
   * GET /api/listed-vehicles/stats
   */
  static async getListingStats(req: Request, res: Response) {
    try {
      const stats = await ListedVehicleService.getListingStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error in getListingStats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch listing statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Check if there are active listings (for CTA logic)
   * GET /api/listed-vehicles/check
   */
  static async checkActiveListings(req: Request, res: Response) {
    try {
      const hasListings = await ListedVehicleService.hasActiveListings();
      
      res.json({
        success: true,
        hasListings,
        message: hasListings 
          ? 'Active listings found' 
          : 'No active listings - show CTA'
      });
    } catch (error) {
      logger.error('Error in checkActiveListings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check active listings',
        message: error instanceof Error ? error.message : 'Unknown error',
        hasListings: false
      });
    }
  }

  /**
   * Health check for listed vehicles service
   * GET /api/listed-vehicles/health
   */
  static async healthCheck(req: Request, res: Response) {
    try {
      const stats = await ListedVehicleService.getListingStats();
      
      res.json({
        success: true,
        message: 'Listed vehicles service is healthy',
        stats: {
          activeListings: stats.activeListings,
          totalProviders: stats.totalProviders,
          averageDailyRate: stats.averageDailyRate,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error in listed vehicles health check:', error);
      res.status(500).json({
        success: false,
        error: 'Listed vehicles service is unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default ListedVehicleController;
