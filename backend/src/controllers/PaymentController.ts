// @ts-nocheck
import { Request, Response, NextFunction } from "express";
import { PaymentWorkflowService } from '../services/PaymentWorkflowService';
import BookingService from '../services/BookingService';
import UserService from '../services/UserService';
import { logger } from '../utils/logger';
import { PayPalService } from '../services/PayPalService';
import PaymentService from '../services/PaymentService';
import monitoringService from '../utils/monitoring';

export class PaymentController {
  private logger = logger;
  private paymentWorkflowService: PaymentWorkflowService;
  private bookingService: BookingService;
  private userService: UserService;
  private paypalService: PayPalService;

  constructor() {
    // Logger is already initialized as a default import
    this.paymentWorkflowService = new PaymentWorkflowService();
    this.bookingService = new BookingService();
    this.userService = new UserService();
    this.paypalService = new PayPalService();
  }

  /**
   * Initialize payment for a booking
   */
  async initializePayment(req: Request, res: Response): Promise<Response> {
    try {
      const { 
        bookingId, 
        paymentMethod 
      } = req.body;

      // Validate request body
      if (!bookingId || !paymentMethod) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID and payment method are required'
        });
      }

      // Fetch booking details
      const booking = await this.bookingService.getBookingById(bookingId);
      if (!booking) {
        return res.status(404).json({
          success: false,
          error: 'Booking not found'
        });
      }

      // Fetch customer details
      const customer = await this.userService.getUserById(booking.userId);
      if (!customer) {
        return res.status(404).json({
          success: false,
          error: 'Customer not found'
        });
      }

      // Initialize payment
      const paymentResult = await this.paymentWorkflowService.initializePayment({
        booking,
        customer,
        paymentMethod
      });

      if (!paymentResult.success) {
        return res.status(400).json(paymentResult);
      }

      return res.status(201).json(paymentResult);

    } catch (error) {
      this.logger.error('Payment initialization failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Confirm payment after successful transaction
   */
  async confirmPayment(req: Request, res: Response): Promise<Response> {
    try {
      const { 
        paymentId, 
        transactionId,
        paymentMethod
      } = req.body;

      // Validate request body
      if (!paymentId || !transactionId || !paymentMethod) {
        return res.status(400).json({
          success: false,
          error: 'Payment ID, transaction ID, and payment method are required'
        });
      }

      // Confirm payment
      const paymentResult = await this.paymentWorkflowService.confirmPayment(
        paymentId, 
        transactionId,
        paymentMethod
      );

      if (!paymentResult.success) {
        return res.status(400).json(paymentResult);
      }

      return res.status(200).json(paymentResult);

    } catch (error) {
      this.logger.error('Payment confirmation failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Process refund for a payment
   */
  async processRefund(req: Request, res: Response): Promise<Response> {
    try {
      const { 
        paymentId, 
        refundReason 
      } = req.body;

      // Validate request body
      if (!paymentId || !refundReason) {
        return res.status(400).json({
          success: false,
          error: 'Payment ID and refund reason are required'
        });
      }

      // Process refund
      const refundResult = await this.paymentWorkflowService.processRefund(
        paymentId, 
        refundReason
      );

      if (!refundResult.success) {
        return res.status(400).json(refundResult);
      }

      return res.status(200).json(refundResult);

    } catch (error) {
      this.logger.error('Refund processing failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Retrieve payment details
   */
  async getPaymentDetails(req: Request, res: Response): Promise<Response> {
    try {
      const { paymentId } = req.params;

      // Validate request parameter
      if (!paymentId) {
        return res.status(400).json({
          success: false,
          error: 'Payment ID is required'
        });
      }

      // Fetch payment details from database
      const { data: payment, error } = await this.bookingService.supabase
        .from('payments')
        .select('*')
        .eq('id', paymentId)
        .single();

      if (error) {
        return res.status(404).json({
          success: false,
          error: 'Payment not found'
        });
      }

      return res.status(200).json({
        success: true,
        payment
      });

    } catch (error) {
      this.logger.error('Retrieving payment details failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * List payments for a user
   */
  async listUserPayments(req: Request, res: Response): Promise<Response> {
    try {
      const { userId } = req.params;
      const { 
        page = 1, 
        limit = 10, 
        status 
      } = req.query;

      // Validate request parameter
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Calculate pagination
      const offset = (Number(page) - 1) * Number(limit);

      // Build query
      let query = this.bookingService.supabase
        .from('payments')
        .select('*', { count: 'exact' })
        .eq('customer_id', userId);

      // Optional status filter
      if (status) {
        query = query.eq('status', status);
      }

      // Apply pagination
      query = query.range(offset, offset + Number(limit) - 1);

      // Execute query
      const { data: payments, count, error } = await query;

      if (error) {
        return res.status(500).json({
          success: false,
          error: 'Failed to retrieve payments'
        });
      }

      return res.status(200).json({
        success: true,
        payments,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0
        }
      });

    } catch (error) {
      this.logger.error('Listing user payments failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Handle PayPal webhook events
   */
  async handlePayPalWebhook(req: Request, res: Response): Promise<Response> {
    try {
      const payload = req.body;
      const headers = req.headers;

      // Verify webhook signature
      const isValidWebhook = await this.paypalService.verifyWebhookSignature(
        payload, 
        headers
      );

      if (!isValidWebhook) {
        return res.status(400).json({
          success: false,
          error: 'Invalid webhook signature'
        });
      }

      // Process different PayPal webhook events
      switch (payload.event_type) {
        case 'PAYMENT.CAPTURE.COMPLETED':
          await this.processPayPalPaymentCapture(payload);
          break;
        case 'PAYMENT.CAPTURE.REFUNDED':
          await this.processPayPalRefund(payload);
          break;
        default:
          this.logger.warn('Unhandled PayPal webhook event', payload.event_type);
      }

      return res.status(200).json({ success: true });

    } catch (error) {
      this.logger.error('PayPal webhook processing failed', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Process PayPal payment capture
   */
  private async processPayPalPaymentCapture(payload: any): Promise<void> {
    try {
      const paymentId = payload.resource.id;
      const amount = payload.resource.amount.total;

      // Find corresponding booking/payment record
      const { data: payment, error } = await this.bookingService.supabase
        .from('payments')
        .select('*')
        .eq('payment_gateway_id', paymentId)
        .single();

      if (error || !payment) {
        this.logger.warn('PayPal payment capture: No matching payment found', paymentId);
        return;
      }

      // Confirm payment
      await this.paymentWorkflowService.confirmPayment(
        payment.id, 
        paymentId, 
        'paypal'
      );
    } catch (error) {
      this.logger.error('PayPal payment capture processing failed', error);
    }
  }

  /**
   * Process PayPal refund
   */
  private async processPayPalRefund(payload: any): Promise<void> {
    try {
      const refundId = payload.resource.id;
      const originalPaymentId = payload.resource.capture_id;

      // Find corresponding payment record
      const { data: payment, error } = await this.bookingService.supabase
        .from('payments')
        .select('*')
        .eq('payment_gateway_id', originalPaymentId)
        .single();

      if (error || !payment) {
        this.logger.warn('PayPal refund: No matching payment found', originalPaymentId);
        return;
      }

      // Process refund
      await this.paymentWorkflowService.processRefund(
        payment.id, 
        'customer_request'
      );
    } catch (error) {
      this.logger.error('PayPal refund processing failed', error);
    }
  }

  /**
   * Create Stripe PaymentIntent for booking
   */
  async createStripePaymentIntent(req: Request, res: Response) {
    try {
      const { bookingId, amount, currency = 'usd' } = req.body;

      // Validate input
      if (!bookingId || !amount) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID and amount are required'
        });
      }

      if (amount <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Amount must be greater than 0'
        });
      }

      const paymentService = new PaymentService();
      const result = await paymentService.createStripePaymentIntent(
        bookingId,
        amount,
        currency
      );

      if (!result.success) {
        return res.status(400).json(result);
      }

      res.json(result);
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({
        success: false,
        error: 'Failed to create payment intent',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Confirm Stripe payment
   */
  async confirmStripePayment(req: Request, res: Response) {
    try {
      const { bookingId, paymentIntentId } = req.body;

      // Validate input
      if (!bookingId || !paymentIntentId) {
        return res.status(400).json({ error: 'Booking ID and Payment Intent ID are required' });
      }

      const payment = await PaymentService.confirmStripePayment(
        bookingId, 
        paymentIntentId
      );

      res.json(payment);
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({ 
        error: 'Failed to confirm payment', 
        details: error.message 
      });
    }
  }

  /**
   * Confirm cash payment by provider
   */
  async confirmCashPayment(req: Request, res: Response) {
    try {
      const { bookingId } = req.params;
      const providerId = req.user?.id; // Assuming authenticated provider

      // Validate input
      if (!bookingId) {
        return res.status(400).json({ error: 'Booking ID is required' });
      }

      const payment = await PaymentService.confirmCashPayment(
        bookingId, 
        providerId
      );

      res.json(payment);
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({ 
        error: 'Failed to confirm cash payment', 
        details: error.message 
      });
    }
  }

  /**
   * Get provider earnings
   */
  async getProviderEarnings(req: Request, res: Response) {
    try {
      const providerId = req.user?.id; // Assuming authenticated provider

      const earnings = await PaymentService.getProviderEarnings(providerId);

      res.json(earnings);
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({ 
        error: 'Failed to retrieve provider earnings', 
        details: error.message 
      });
    }
  }

  /**
   * Update provider bank details
   */
  async updateProviderBankDetails(req: Request, res: Response) {
    try {
      const providerId = req.user?.id; // Assuming authenticated provider
      const { 
        bankName, 
        accountNumber, 
        accountHolderName, 
        branchCode 
      } = req.body;

      // Validate input
      if (!bankName || !accountNumber || !accountHolderName) {
        return res.status(400).json({ 
          error: 'Bank name, account number, and account holder name are required' 
        });
      }

      const bankDetails = await PaymentService.updateProviderBankDetails(
        providerId, 
        { 
          bankName, 
          accountNumber, 
          accountHolderName, 
          branchCode 
        }
      );

      res.json(bankDetails);
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({ 
        error: 'Failed to update bank details', 
        details: error.message 
      });
    }
  }

  /**
   * Manually trigger provider payouts (admin-only)
   */
  async processProviderPayouts(req: Request, res: Response) {
    try {
      await PaymentService.processProviderPayouts();

      res.json({ 
        message: 'Provider payouts processed successfully' 
      });
    } catch (error) {
      monitoringService.captureException(error);
      res.status(500).json({ 
        error: 'Failed to process provider payouts', 
        details: error.message 
      });
    }
  }





  // Static stub methods for routes compatibility
  static async createPaymentIntent(req: Request, res: Response, next: NextFunction) {
    try {
      const controller = new PaymentController();
      await controller.createStripePaymentIntent(req, res);
    } catch (error) {
      next(error);
    }
  }

  static async createCheckoutSession(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Create checkout session endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async confirmPayment(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Confirm payment endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async getCheckoutSession(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Get checkout session endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async getCurrencySymbol(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Get currency symbol endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async convertPrice(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Convert price endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async createConnectAccount(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Create connect account endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async createAccountLink(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Create account link endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  // Static methods for route compatibility
  static async createStripePaymentIntent(req: Request, res: Response, next: NextFunction) {
    const instance = new PaymentController();
    return instance.createPaymentIntent(req, res);
  }

  static async confirmStripePayment(req: Request, res: Response, next: NextFunction) {
    const instance = new PaymentController();
    return instance.confirmPayment(req, res);
  }

  static async confirmCashPayment(req: Request, res: Response, next: NextFunction) {
    const instance = new PaymentController();
    return instance.confirmPayment(req, res);
  }

  static async getProviderEarnings(req: Request, res: Response, next: NextFunction) {
    try {
      const providerId = req.user?.id;
      // Stub implementation - return mock data
      res.json({ 
        success: true, 
        earnings: { totalEarnings: 0, pendingPayouts: 0 } 
      });
    } catch (error) {
      next(error);
    }
  }

  static async updateProviderBankDetails(req: Request, res: Response, next: NextFunction) {
    try {
      // Stub implementation
      res.json({ 
        success: true, 
        message: 'Bank details updated successfully' 
      });
    } catch (error) {
      next(error);
    }
  }

  static async processProviderPayouts(req: Request, res: Response, next: NextFunction) {
    try {
      // Stub implementation
      res.json({ 
        success: true, 
        message: 'Provider payouts processed successfully' 
      });
    } catch (error) {
      next(error);
    }
  }
}

export default PaymentController;
