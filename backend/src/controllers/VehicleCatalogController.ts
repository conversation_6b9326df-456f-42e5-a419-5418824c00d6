import { Request, Response } from 'express';
import VehicleCatalogService from '../services/VehicleCatalogService';
import { logger } from '../utils/logger';

export class VehicleCatalogController {
  /**
   * Get all vehicles from catalog
   * GET /api/vehicle-catalog
   */
  static async getAllVehicles(req: Request, res: Response) {
    try {
      const vehicles = await VehicleCatalogService.getAllVehicles();
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length
      });
    } catch (error) {
      logger.error('Error in getAllVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle catalog',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Search vehicles for autocomplete
   * GET /api/vehicle-catalog/search?q=query
   */
  static async searchVehicles(req: Request, res: Response) {
    try {
      const { q: query } = req.query;

      if (!query || typeof query !== 'string') {
        return res.status(400).json({
          success: false,
          error: 'Query parameter is required'
        });
      }

      const vehicles = await VehicleCatalogService.searchVehicles(query);
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        query
      });
    } catch (error) {
      logger.error('Error in searchVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search vehicles',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicle suggestions for autocomplete
   * GET /api/vehicle-catalog/suggestions?q=query
   */
  static async getVehicleSuggestions(req: Request, res: Response) {
    try {
      const { q: query } = req.query;

      if (!query || typeof query !== 'string') {
        return res.status(400).json({
          success: false,
          error: 'Query parameter is required'
        });
      }

      const suggestions = await VehicleCatalogService.getVehicleSuggestions(query);
      
      res.json({
        success: true,
        data: suggestions,
        count: suggestions.length,
        query
      });
    } catch (error) {
      logger.error('Error in getVehicleSuggestions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get vehicle suggestions',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get unique makes for dropdown
   * GET /api/vehicle-catalog/makes
   */
  static async getUniqueMakes(req: Request, res: Response) {
    try {
      const makes = await VehicleCatalogService.getUniqueMakes();
      
      res.json({
        success: true,
        data: makes,
        count: makes.length
      });
    } catch (error) {
      logger.error('Error in getUniqueMakes:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle makes',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicles by make
   * GET /api/vehicle-catalog/make/:make
   */
  static async getVehiclesByMake(req: Request, res: Response) {
    try {
      const { make } = req.params;

      if (!make) {
        return res.status(400).json({
          success: false,
          error: 'Make parameter is required'
        });
      }

      const vehicles = await VehicleCatalogService.getVehiclesByMake(make);
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        make
      });
    } catch (error) {
      logger.error('Error in getVehiclesByMake:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicles by make',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicle by ID
   * GET /api/vehicle-catalog/:id
   */
  static async getVehicleById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      const vehicle = await VehicleCatalogService.getVehicleById(id);
      
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          error: 'Vehicle not found'
        });
      }

      res.json({
        success: true,
        data: vehicle
      });
    } catch (error) {
      logger.error('Error in getVehicleById:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Filter vehicles with advanced search
   * POST /api/vehicle-catalog/filter
   */
  static async filterVehicles(req: Request, res: Response) {
    try {
      const filters = req.body;
      
      const vehicles = await VehicleCatalogService.filterVehicles(filters);
      
      res.json({
        success: true,
        data: vehicles,
        count: vehicles.length,
        filters
      });
    } catch (error) {
      logger.error('Error in filterVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to filter vehicles',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Health check for vehicle catalog
   * GET /api/vehicle-catalog/health
   */
  static async healthCheck(req: Request, res: Response) {
    try {
      const makes = await VehicleCatalogService.getUniqueMakes();
      
      res.json({
        success: true,
        message: 'Vehicle catalog service is healthy',
        stats: {
          uniqueMakes: makes.length,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error in vehicle catalog health check:', error);
      res.status(500).json({
        success: false,
        error: 'Vehicle catalog service is unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default VehicleCatalogController;
