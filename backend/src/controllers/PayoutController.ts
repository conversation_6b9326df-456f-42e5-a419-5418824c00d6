import { Request, Response } from 'express';
import { PayoutService, CreatePayoutRequestData } from '../services/PayoutService';
import { asyncHandler } from '../utils/asyncHandler';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';

const payoutService = new PayoutService();

export class PayoutController {
  /**
   * Create a new payout request
   * POST /api/payouts
   */
  createPayoutRequest = asyncHandler(async (req: Request, res: Response) => {
    const { bookingId, amount, method, description } = req.body;
    const providerId = req.user?.id;

    if (!providerId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!bookingId || !amount || !method) {
      return res.status(400).json({ 
        error: 'Missing required fields: bookingId, amount, method' 
      });
    }

    if (!['stripe', 'bank', 'manual'].includes(method)) {
      return res.status(400).json({ 
        error: 'Invalid payout method. Must be: stripe, bank, or manual' 
      });
    }

    try {
      const payoutRequest = await payoutService.createPayoutRequest({
        providerId,
        bookingId,
        amount: parseFloat(amount),
        method: method as 'stripe' | 'bank' | 'manual',
        description
      });

      res.status(201).json({
        success: true,
        data: payoutRequest,
        message: 'Payout request created successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payout request'
      });
    }
  });

  /**
   * Process a payout request (admin only)
   * POST /api/payouts/:id/process
   */
  processPayoutRequest = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
      const processedPayout = await payoutService.processPayoutRequest(id);

      res.json({
        success: true,
        data: processedPayout,
        message: 'Payout processed successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process payout'
      });
    }
  });

  /**
   * Get payout requests for the authenticated provider
   * GET /api/payouts/provider
   */
  getProviderPayouts = asyncHandler(async (req: Request, res: Response) => {
    const providerId = req.user?.id;

    if (!providerId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const payouts = await payoutService.getProviderPayouts(providerId);

    res.json({
      success: true,
      data: payouts,
      count: payouts.length
    });
  });

  /**
   * Get all payout requests (admin only)
   * GET /api/payouts
   */
  getAllPayouts = asyncHandler(async (req: Request, res: Response) => {
    const { status, method, page = '1', limit = '20' } = req.query;
    
    let payouts = await payoutService.getAllPayouts();

    // Filter by status
    if (status && typeof status === 'string') {
      payouts = payouts.filter(payout => payout.status === status);
    }

    // Filter by method
    if (method && typeof method === 'string') {
      payouts = payouts.filter(payout => payout.method === method);
    }

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedPayouts = payouts.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedPayouts,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: payouts.length,
        totalPages: Math.ceil(payouts.length / limitNum)
      }
    });
  });

  /**
   * Get payout request by ID
   * GET /api/payouts/:id
   */
  getPayoutById = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    const payout = await payoutService.getPayoutById(id);

    if (!payout) {
      return res.status(404).json({
        success: false,
        error: 'Payout request not found'
      });
    }

    // Check if user is authorized to view this payout
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (userRole !== 'ADMIN' && payout.providerId !== userId) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to view this payout'
      });
    }

    res.json({
      success: true,
      data: payout
    });
  });

  /**
   * Update payout status (admin only)
   * PATCH /api/payouts/:id/status
   */
  updatePayoutStatus = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { status, notes } = req.body;

    if (!status || !['PENDING', 'COMPLETED', 'FAILED', 'PENDING_MANUAL'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be: PENDING, COMPLETED, FAILED, or PENDING_MANUAL'
      });
    }

    try {
      const updatedPayout = await payoutService.updatePayoutStatus(id, status, notes);

      res.json({
        success: true,
        data: updatedPayout,
        message: 'Payout status updated successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update payout status'
      });
    }
  });

  /**
   * Get payout statistics (admin only)
   * GET /api/payouts/stats
   */
  getPayoutStats = asyncHandler(async (req: Request, res: Response) => {
    const allPayouts = await payoutService.getAllPayouts();

    const stats = {
      total: allPayouts.length,
      totalAmount: allPayouts.reduce((sum, payout) => sum + payout.amount, 0),
      byStatus: {
        PENDING: allPayouts.filter(p => p.status === 'PENDING').length,
        COMPLETED: allPayouts.filter(p => p.status === 'COMPLETED').length,
        FAILED: allPayouts.filter(p => p.status === 'FAILED').length,
        PENDING_MANUAL: allPayouts.filter(p => p.status === 'PENDING_MANUAL').length,
      },
      byMethod: {
        stripe: allPayouts.filter(p => p.method === 'stripe').length,
        bank: allPayouts.filter(p => p.method === 'bank').length,
        manual: allPayouts.filter(p => p.method === 'manual').length,
      }
    };

    res.json({
      success: true,
      data: stats
    });
  });
}

export default PayoutController;