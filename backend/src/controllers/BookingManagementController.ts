import { Request, Response } from 'express';
import BookingManagementService from '../services/BookingManagementService';
import { logger } from '../utils/logger';

export class BookingManagementController {
  /**
   * Get all bookings for current provider
   * GET /api/providers/bookings?page=1&limit=20&status=PENDING
   */
  static async getProviderBookings(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;

      const result = await BookingManagementService.getProviderBookings(userId, page, limit, status);
      
      res.json({
        success: true,
        data: result.bookings,
        pagination: {
          page,
          limit,
          total: result.total,
          pages: result.pages
        }
      });
    } catch (error) {
      logger.error('Error in getProviderBookings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch bookings',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get booking by ID for current provider
   * GET /api/providers/bookings/:id
   */
  static async getBookingById(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID is required'
        });
      }

      const booking = await BookingManagementService.getBookingById(id, userId);
      
      if (!booking) {
        return res.status(404).json({
          success: false,
          error: 'Booking not found'
        });
      }

      res.json({
        success: true,
        data: booking
      });
    } catch (error) {
      logger.error('Error in getBookingById:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch booking',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update booking status
   * PUT /api/providers/bookings/:id/status
   */
  static async updateBookingStatus(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      const { status } = req.body;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID is required'
        });
      }

      if (!status) {
        return res.status(400).json({
          success: false,
          error: 'Status is required'
        });
      }

      // Validate status
      const validStatuses = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid status. Valid statuses are: ' + validStatuses.join(', ')
        });
      }

      const updatedBooking = await BookingManagementService.updateBookingStatus(id, userId, status);

      res.json({
        success: true,
        data: updatedBooking,
        message: `Booking status updated to ${status}`
      });
    } catch (error) {
      logger.error('Error in updateBookingStatus:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update booking status',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Accept booking (shortcut for updating status to CONFIRMED)
   * POST /api/providers/bookings/:id/accept
   */
  static async acceptBooking(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID is required'
        });
      }

      const updatedBooking = await BookingManagementService.updateBookingStatus(id, userId, 'CONFIRMED');

      res.json({
        success: true,
        data: updatedBooking,
        message: 'Booking accepted successfully'
      });
    } catch (error) {
      logger.error('Error in acceptBooking:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to accept booking',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Reject booking (shortcut for updating status to CANCELLED)
   * POST /api/providers/bookings/:id/reject
   */
  static async rejectBooking(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID is required'
        });
      }

      const updatedBooking = await BookingManagementService.updateBookingStatus(id, userId, 'CANCELLED');

      res.json({
        success: true,
        data: updatedBooking,
        message: 'Booking rejected successfully'
      });
    } catch (error) {
      logger.error('Error in rejectBooking:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to reject booking',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Complete booking (shortcut for updating status to COMPLETED)
   * POST /api/providers/bookings/:id/complete
   */
  static async completeBooking(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Booking ID is required'
        });
      }

      const updatedBooking = await BookingManagementService.updateBookingStatus(id, userId, 'COMPLETED');

      res.json({
        success: true,
        data: updatedBooking,
        message: 'Booking completed successfully'
      });
    } catch (error) {
      logger.error('Error in completeBooking:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to complete booking',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get booking statistics for current provider
   * GET /api/providers/bookings/stats
   */
  static async getBookingStats(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const stats = await BookingManagementService.getBookingStats(userId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error in getBookingStats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch booking statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default BookingManagementController;
