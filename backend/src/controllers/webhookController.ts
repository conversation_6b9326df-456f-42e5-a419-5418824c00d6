import { Request, Response, NextFunction } from "express";
import { StripeService } from '../services/integrations/stripeService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class WebhookController {
  /**
   * Handle Stripe webhook events
   * @param req Express request
   * @param res Express response
   */
  static async handleStripeWebhook(req: Request, res: Response) {
    try {
      // Get the Stripe signature from headers
      const signature = req.headers['stripe-signature'] as string;

      // Handle the webhook
      const event = await StripeService.handleWebhook(
        req.body, 
        signature
      );

      // Respond with success
      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Webhook error:', error);
      
      // Log webhook error
      await prisma.webhookLog.create({
        data: {
          service: 'stripe',
          event_type: 'error',
          payload: JSON.stringify(error),
          processed: false
        }
      });

      res.status(400).send(`Webhook Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle generic webhook for other services
   * @param req Express request
   * @param res Express response
   */
  static async handleGenericWebhook(req: Request, res: Response) {
    const { provider } = req.params;
    
    try {
      // Log the incoming webhook
      await prisma.webhookLog.create({
        data: {
          service: provider,
          event_type: req.body.type || 'unknown',
          payload: JSON.stringify(req.body),
          processed: false
        }
      });

      // Add service-specific webhook handling logic here
      switch (provider) {
        case 'sendgrid':
          // Handle SendGrid webhook
          break;
        case 'google-maps':
          // Handle Google Maps webhook
          break;
        default:
          console.warn(`Unhandled webhook provider: ${provider}`);
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error(`${provider} webhook error:`, error);
      
      // Log webhook error
      await prisma.webhookLog.create({
        data: {
          service: provider,
          event_type: 'error',
          payload: JSON.stringify(error),
          processed: false
        }
      });

      res.status(400).send(`Webhook Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve webhook logs for admin dashboard
   * @param req Express request
   * @param res Express response
   */
  static async getWebhookLogs(req: Request, res: Response) {
    try {
      const { service, processed, page = 1, limit = 20 } = req.query;

      const whereClause: any = {};
      if (service) whereClause.service = service as string;
      if (processed !== undefined) whereClause.processed = processed === 'true';

      const webhookLogs = await prisma.webhookLog.findMany({
        where: whereClause,
        orderBy: { created_at: 'desc' },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit)
      });

      const total = await prisma.webhookLog.count({ where: whereClause });

      res.json({
        logs: webhookLogs,
        page: Number(page),
        limit: Number(limit),
        total
      });
    } catch (error) {
      console.error('Error retrieving webhook logs:', error);
      res.status(500).json({ error: 'Failed to retrieve webhook logs' });
    }
  }
}

export default WebhookController;
