import { Request, Response, NextFunction } from "express";
import { supabase } from '../utils/supabaseClient';
import { ServiceError } from '../services/BaseService';

export class BookingController {
  // Get all bookings with optional filtering and pagination
  static async getAllBookings(req: Request, res: Response, next: NextFunction) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        filter = {}, 
        sort = 'created_at', 
        order = 'desc' 
      } = req.query;

      const offset = (Number(page) - 1) * Number(limit);

      const query = supabase
        .from('Booking')
        .select('*, vehicle:Vehicle(*), user:User(*)', { count: 'exact' })
        .range(offset, offset + Number(limit) - 1)
        .order(String(sort), { ascending: order === 'asc' });

      // Apply optional filters
      Object.entries(filter).forEach(([key, value]) => {
        query.eq(key, value);
      });

      const { data, count, error } = await query;

      if (error) throw new ServiceError(error.message, 500);

      res.json({
        bookings: data,
        totalBookings: count,
        page: Number(page),
        limit: Number(limit)
      });
    } catch (error) {
      next(error);
    }
  }

  // Get booking by ID
  static async getBookingById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const { data, error } = await supabase
        .from('bookings')
        .select('*, vehicle:vehicles(*), user:users(*)')
        .eq('id', id)
        .single();

      if (error) throw new ServiceError(error.message, 404);
      if (!data) throw new ServiceError('Booking not found', 404);

      res.json(data);
    } catch (error) {
      next(error);
    }
  }

  // Create a new booking
  static async createBooking(req: Request, res: Response, next: NextFunction) {
    try {
      const bookingData = req.body;

      // Validate vehicle availability
      const availabilityCheck = await BookingController.checkVehicleAvailability(
        bookingData.vehicle_id, 
        bookingData.start_date, 
        bookingData.end_date
      );

      if (!availabilityCheck.isAvailable) {
        throw new ServiceError('Vehicle not available for selected dates', 400);
      }

      const { data, error } = await supabase
        .from('bookings')
        .insert(bookingData)
        .select()
        .single();

      if (error) throw new ServiceError(error.message, 400);

      res.status(201).json(data);
    } catch (error) {
      next(error);
    }
  }

  // Update booking
  static async updateBooking(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const bookingData = req.body;

      // If dates are being changed, check availability
      if (bookingData.start_date || bookingData.end_date) {
        const existingBooking = await supabase
          .from('bookings')
          .select('vehicle_id')
          .eq('id', id)
          .single();

        const availabilityCheck = await BookingController.checkVehicleAvailability(
          existingBooking.data?.vehicle_id, 
          bookingData.start_date, 
          bookingData.end_date
        );

        if (!availabilityCheck.isAvailable) {
          throw new ServiceError('Vehicle not available for selected dates', 400);
        }
      }

      const { data, error } = await supabase
        .from('bookings')
        .update(bookingData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new ServiceError(error.message, 400);
      if (!data) throw new ServiceError('Booking not found', 404);

      res.json(data);
    } catch (error) {
      next(error);
    }
  }

  // Cancel/Delete booking
  static async cancelBooking(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', id);

      if (error) throw new ServiceError(error.message, 400);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }

  // Check vehicle availability
  static async checkVehicleAvailability(
    vehicleId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{ isAvailable: boolean, conflictingBookings?: any }> {
    const { data: conflictingBookings, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('vehicle_id', vehicleId)
      .or(
        `start_date.lte.${startDate},end_date.gte.${startDate}` +
        `start_date.lte.${endDate},end_date.gte.${endDate}` +
        `start_date.gte.${startDate},end_date.lte.${endDate}`
      );

    if (error) throw new ServiceError(error.message, 500);

    return {
      isAvailable: conflictingBookings.length === 0,
      conflictingBookings
    };
  }

  // Get user's booking history
  static async getUserBookings(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const { 
        page = 1, 
        limit = 10, 
        status = 'all' 
      } = req.query;

      const offset = (Number(page) - 1) * Number(limit);

      const query = supabase
        .from('Booking')
        .select('*, vehicle:Vehicle(*)', { count: 'exact' })
        .eq('userId', userId)
        .range(offset, offset + Number(limit) - 1)
        .order('createdAt', { ascending: false });

      if (status !== 'all') {
        query.eq('status', status);
      }

      const { data, count, error } = await query;

      if (error) throw new ServiceError(error.message, 500);

      res.json({
        bookings: data,
        totalBookings: count,
        page: Number(page),
        limit: Number(limit)
      });
    } catch (error) {
      next(error);
    }
  }

  // Stub methods to fix route compilation errors
  static async getUnavailableDates(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Unavailable dates endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async confirmCashBooking(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Confirm cash booking endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async rejectCashBooking(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Reject cash booking endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async listBookings(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'List bookings endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async updateBookingStatus(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Update booking status endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async confirmPayment(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Confirm payment endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async checkAvailability(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Check availability endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async getBookingStats(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Get booking stats endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }

  static async getProviderBookings(req: Request, res: Response, next: NextFunction) {
    try {
      res.json({ message: 'Get provider bookings endpoint - not implemented yet' });
    } catch (error) {
      next(error);
    }
  }
}
