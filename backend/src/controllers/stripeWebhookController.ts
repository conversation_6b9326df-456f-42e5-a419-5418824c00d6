import { Request, Response, NextFunction } from "express";
import { StripeWebhookService } from '../services/integrations/stripeWebhookService';
import { logger } from '../utils/logger';
import { createError } from '../middleware/errorHandler';
import ApiKeyService from '../services/ApiKeyService';

// Initialize the service with the database key
let stripeWebhookService: StripeWebhookService | null = null;

const initializeWebhookService = async () => {
  try {
    const stripeKey = await ApiKeyService.getStripeSecretKey();
    if (stripeKey) {
      stripeWebhookService = new StripeWebhookService(stripeKey);
      logger.info('✅ Stripe webhook service initialized with database key');
    } else {
      logger.warn('⚠️  Stripe secret key not found in database, using environment variable');
      const envStripeKey = process.env.STRIPE_SECRET_KEY;
      if (envStripeKey) {
        stripeWebhookService = new StripeWebhookService(envStripeKey);
        logger.info('✅ Stripe webhook service initialized with environment variable');
      } else {
        logger.error('❌ No Stripe secret key found, webhook service will be disabled');
      }
    }
  } catch (error) {
    logger.error('❌ Error initializing Stripe webhook service:', error);
  }
};

// Initialize webhook service on startup
initializeWebhookService();

export const handleStripeWebhook = async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();
  
  // Check if webhook service is initialized
  if (!stripeWebhookService) {
    logger.error('Webhook service not initialized');
    res.status(503).json({ 
      error: 'Webhook service not available',
      timestamp: new Date().toISOString()
    });
    return;
  }
  
  try {
    // Validate request headers
    const signature = req.headers['stripe-signature'] as string;
    const contentType = req.headers['content-type'];
    
    if (!signature) {
      logger.error('Missing stripe-signature header', {
        headers: Object.keys(req.headers),
        contentType,
        userAgent: req.headers['user-agent']
      });
      res.status(400).json({ 
        error: 'Missing stripe-signature header',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Validate content type
    if (contentType !== 'application/json') {
      logger.error('Invalid content type for webhook', {
        contentType,
        expectedType: 'application/json'
      });
      res.status(400).json({ 
        error: 'Invalid content type. Expected application/json',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Get raw body with proper validation
    let rawBody: Buffer = (req as any).rawBody;
    
    // Fallback to req.body if rawBody is not available
    if (!rawBody && req.body) {
      if (Buffer.isBuffer(req.body)) {
        rawBody = req.body;
      } else if (typeof req.body === 'string') {
        rawBody = Buffer.from(req.body);
      } else if (typeof req.body === 'object') {
        rawBody = Buffer.from(JSON.stringify(req.body));
      }
    }
    
    if (!rawBody || rawBody.length === 0) {
      logger.error('Raw body not found or empty', { 
        middleware: 'express.raw()', 
        bodyPresent: !!req.body,
        bodyType: typeof req.body,
        bodyLength: req.body ? (Buffer.isBuffer(req.body) ? req.body.length : JSON.stringify(req.body).length) : 0,
        rawBodyPresent: !!(req as any).rawBody,
        contentType,
        signatureHeader: signature?.substring(0, 20) + '...',
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
      res.status(400).json({ 
        error: 'Raw body not available or empty',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Validate payload size
    const maxPayloadSize = 1024 * 1024; // 1MB
    if (rawBody.length > maxPayloadSize) {
      logger.error('Webhook payload too large', {
        payloadSize: rawBody.length,
        maxSize: maxPayloadSize,
        signature: signature?.substring(0, 20) + '...'
      });
      res.status(413).json({ 
        error: 'Payload too large',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Additional validation - check if body looks like valid JSON
    if (rawBody.length < 10) {
      logger.error('Suspiciously small webhook payload', {
        rawBodyLength: rawBody.length,
        rawBodyContent: rawBody.toString(),
        signature: signature?.substring(0, 20) + '...'
      });
      res.status(400).json({ 
        error: 'Webhook payload too small',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Log webhook attempt
    logger.info('Webhook verification started', {
      rawBodyLength: rawBody.length,
      signaturePrefix: signature.substring(0, 10) + '...',
      contentType,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    });

    // Process webhook
    const result = await stripeWebhookService.handleWebhookEvent(rawBody, signature, res);

    const processingTime = Date.now() - startTime;

    if (result.success) {
      logger.info('Webhook processed successfully', {
        statusCode: result.statusCode,
        message: result.message,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString()
      });
      res.status(result.statusCode).json({ 
        received: true,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString()
      });
    } else {
      logger.error('Webhook processing failed', {
        statusCode: result.statusCode,
        message: result.message,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString()
      });
      res.status(result.statusCode).json({
        error: result.message,
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const err = error as Error;
    
    logger.error('Webhook processing error:', {
      message: err.message,
      stack: err.stack,
      rawBodyLength: (req as any).rawBody?.length || 0,
      bodyType: typeof req.body,
      signature: req.headers['stripe-signature']?.toString().substring(0, 20) + '...',
      processingTime: `${processingTime}ms`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      timestamp: new Date().toISOString()
    });

    // Don't expose internal errors in production
    const errorMessage = process.env.NODE_ENV === 'production' 
      ? 'Webhook processing failed' 
      : err.message;

    res.status(500).json({
      error: errorMessage,
      processingTime: `${processingTime}ms`,
      timestamp: new Date().toISOString()
    });
  }
};

// Health check for webhook endpoint
export const webhookHealthCheck = async (req: Request, res: Response): Promise<void> => {
  try {
    res.status(200).json({
      status: 'healthy',
      service: 'stripe-webhook',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    logger.error('Webhook health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      service: 'stripe-webhook',
      error: 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
}; 