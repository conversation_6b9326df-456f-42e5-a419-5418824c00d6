import { Request, Response, NextFunction } from "express";
import { SessionRequest } from '../types/express.d';
import oAuthService from '../services/OAuthService'
import { logger } from '../utils/logger'
import { User } from '@prisma/client'

export class OAuthController {
  // Initiate Google OAuth authorization with enhanced security
  async initiateGoogleOAuth(req: Request, res: Response) {
    try {
      // Generate state token for CSRF protection
      const state = oAuthService.generateStateToken()

      // Store state in session with additional security
      if (!req.session) {
        req.session = {}
      }
      req.session.oauthState = {
        token: state,
        createdAt: Date.now()
      }

      // Generate authorization URL
      const url = oAuthService.getAuthorizationUrl(state)

      // Log OAuth initiation attempt
      logger.info('Google OAuth authorization initiated', { 
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      // Redirect to Google's authorization page
      res.redirect(url)
    } catch (error) {
      // Comprehensive error handling
      logger.error('Google OAuth initiation failed', { 
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      // Redirect to error page with secure error handling
      const errorRedirectUrl = new URL(
        process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error'
      )
      errorRedirectUrl.searchParams.append(
        'message', 
        'OAuth initialization failed. Please try again.'
      )
      
      res.redirect(errorRedirectUrl.toString())
    }
  }

  // Handle Google OAuth callback with robust error handling
  async handleGoogleOAuthCallback(req: Request, res: Response) {
    try {
      // Handle both GET (query params) and POST (body) requests
      const { code, state } = req.method === 'POST' ? req.body : req.query

      // Validate inputs
      if (!code || !state) {
        return res.status(400).json({ message: 'Missing OAuth code or state' })
      }

      // Verify state matches what was stored in session
      const storedState = req.session?.oauthState
      if (!storedState || storedState.token !== state) {
        // Log potential CSRF attempt
        logger.warn('Potential CSRF attack detected', { 
          ip: req.ip,
          userAgent: req.get('User-Agent')
        })
        
        return res.status(403).json({ message: 'Invalid OAuth state' })
      }

      // Check state token age (optional additional security)
      const STATE_TOKEN_EXPIRY = 15 * 60 * 1000 // 15 minutes
      if (Date.now() - storedState.createdAt > STATE_TOKEN_EXPIRY) {
        return res.status(403).json({ message: 'OAuth state token expired' })
      }

      // Handle OAuth callback
      const { user, token }: { user: any, token: string } = await oAuthService.handleOAuthCallback(code as string)

      // Clear stored state
      delete req.session?.oauthState

      // Log successful authentication  
      logger.info('Successful Google OAuth authentication', { 
        userId: (user as any).id, 
        email: (user as any).email,
        ip: req.ip
      })

      // Redirect to frontend with token
      const frontendRedirectUrl = new URL(
        process.env.FRONTEND_OAUTH_REDIRECT_URL || 'https://yourdomain.com/oauth-callback'
      )
      frontendRedirectUrl.searchParams.append('token', token)
      frontendRedirectUrl.searchParams.append('userId', (user as any).id)

      res.redirect(frontendRedirectUrl.toString())
    } catch (error) {
      // Comprehensive error logging
      logger.error('Google OAuth callback processing failed', { 
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })
      
      // Redirect to error page
      const errorRedirectUrl = new URL(
        process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error'
      )
      errorRedirectUrl.searchParams.append(
        'message', 
        'Authentication failed. Please try again.'
      )
      
      res.redirect(errorRedirectUrl.toString())
    }
  }

  // Initiate Facebook OAuth authorization with enhanced security
  async initiateFacebookOAuth(req: SessionRequest, res: Response) {
    try {
      const state = oAuthService.generateStateToken()
      if (!req.session) req.session = {}
      req.session.oauthState = { token: state, createdAt: Date.now() }
      const url = oAuthService.getAuthorizationUrl(state)
      logger.info('Facebook OAuth authorization initiated', { ip: req.ip, userAgent: req.get('User-Agent') })
      res.redirect(url)
    } catch (error) {
      logger.error('Facebook OAuth initiation failed', { error: error instanceof Error ? error.message : 'Unknown error', ip: req.ip, userAgent: req.get('User-Agent') })
      const errorRedirectUrl = new URL(process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error')
      errorRedirectUrl.searchParams.append('message', 'OAuth initialization failed. Please try again.')
      res.redirect(errorRedirectUrl.toString())
    }
  }

  // Handle Facebook OAuth callback with robust error handling
  async handleFacebookOAuthCallback(req: SessionRequest, res: Response) {
    try {
      const { code, state } = req.query
      if (!code || !state) return res.status(400).json({ message: 'Missing OAuth code or state' })
      const storedState = req.session?.oauthState
      if (!storedState || storedState.token !== state) {
        logger.warn('Potential CSRF attack detected', { ip: req.ip, userAgent: req.get('User-Agent') })
        return res.status(403).json({ message: 'Invalid OAuth state' })
      }
      const STATE_TOKEN_EXPIRY = 15 * 60 * 1000 // 15 minutes
      if (Date.now() - storedState.createdAt > STATE_TOKEN_EXPIRY) return res.status(403).json({ message: 'OAuth state token expired' })
      const { user, token }: { user: any, token: string } = await oAuthService.handleOAuthCallback(code as string)
      delete req.session?.oauthState
      logger.info('Successful Facebook OAuth authentication', { userId: (user as any).id, email: (user as any).email, ip: req.ip })
      const frontendRedirectUrl = new URL(process.env.FRONTEND_OAUTH_REDIRECT_URL || 'https://yourdomain.com/oauth-callback')
      frontendRedirectUrl.searchParams.append('token', token)
      frontendRedirectUrl.searchParams.append('userId', (user as any).id)
      res.redirect(frontendRedirectUrl.toString())
    } catch (error) {
      logger.error('Facebook OAuth callback processing failed', { error: error instanceof Error ? error.message : 'Unknown error', ip: req.ip, userAgent: req.get('User-Agent') })
      const errorRedirectUrl = new URL(process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error')
      errorRedirectUrl.searchParams.append('message', 'Authentication failed. Please try again.')
      res.redirect(errorRedirectUrl.toString())
    }
  }

  // Initiate GitHub OAuth authorization with enhanced security
  async initiateGithubOAuth(req: SessionRequest, res: Response) {
    try {
      const state = oAuthService.generateStateToken()
      if (!req.session) req.session = {}
      req.session.oauthState = { token: state, createdAt: Date.now() }
      const url = oAuthService.getAuthorizationUrl(state)
      logger.info('GitHub OAuth authorization initiated', { ip: req.ip, userAgent: req.get('User-Agent') })
      res.redirect(url)
    } catch (error) {
      logger.error('GitHub OAuth initiation failed', { error: error instanceof Error ? error.message : 'Unknown error', ip: req.ip, userAgent: req.get('User-Agent') })
      const errorRedirectUrl = new URL(process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error')
      errorRedirectUrl.searchParams.append('message', 'OAuth initialization failed. Please try again.')
      res.redirect(errorRedirectUrl.toString())
    }
  }

  // Handle GitHub OAuth callback with robust error handling
  async handleGithubOAuthCallback(req: SessionRequest, res: Response) {
    try {
      const { code, state } = req.query
      if (!code || !state) return res.status(400).json({ message: 'Missing OAuth code or state' })
      const storedState = req.session?.oauthState
      if (!storedState || storedState.token !== state) {
        logger.warn('Potential CSRF attack detected', { ip: req.ip, userAgent: req.get('User-Agent') })
        return res.status(403).json({ message: 'Invalid OAuth state' })
      }
      const STATE_TOKEN_EXPIRY = 15 * 60 * 1000 // 15 minutes
      if (Date.now() - storedState.createdAt > STATE_TOKEN_EXPIRY) return res.status(403).json({ message: 'OAuth state token expired' })
      const { user, token }: { user: any, token: string } = await oAuthService.handleOAuthCallback(code as string)
      delete req.session?.oauthState
      logger.info('Successful GitHub OAuth authentication', { userId: (user as any).id, email: (user as any).email, ip: req.ip })
      const frontendRedirectUrl = new URL(process.env.FRONTEND_OAUTH_REDIRECT_URL || 'https://yourdomain.com/oauth-callback')
      frontendRedirectUrl.searchParams.append('token', token)
      frontendRedirectUrl.searchParams.append('userId', (user as any).id)
      res.redirect(frontendRedirectUrl.toString())
    } catch (error) {
      logger.error('GitHub OAuth callback processing failed', { error: error instanceof Error ? error.message : 'Unknown error', ip: req.ip, userAgent: req.get('User-Agent') })
      const errorRedirectUrl = new URL(process.env.FRONTEND_OAUTH_ERROR_URL || 'https://yourdomain.com/oauth-error')
      errorRedirectUrl.searchParams.append('message', 'Authentication failed. Please try again.')
      res.redirect(errorRedirectUrl.toString())
    }
  }
}

export default new OAuthController() 