import { PrismaClient, Prisma } from '@prisma/client';
import { subDays, subMonths } from 'date-fns';

const prisma = new PrismaClient();

export interface DashboardStats {
  listings: {
    total: number;
    active: number;
    pending: number;
    disabled: number;
    byType: Record<string, Record<string, number>>;
  };
  vehicleAssistance: {
    total: number;
    pending: number;
    resolved: number;
    urgent: number;
    recentRequests: Array<{
      id: string;
      userId: string;
      category: string;
      priority: string;
      reason: string;
      createdAt: Date;
      status: string;
    }>;
  };
  supportTickets: {
    total: number;
    open: number;
    inProgress: number;
    resolved: number;
    escalated: number;
    recentTickets: Array<{
      id: string;
      category: string;
      status: string;
      createdAt: Date;
    }>;
  };
  bookings: {
    total: number;
    totalRevenue: number;
    platformCommission: number;
    byStatus: Record<string, number>;
    recentBookings: Array<{
      id: string;
      vehicleModel: string;
      totalPrice: number;
      status: string;
      startDate: Date;
    }>;
  };
  users: {
    total: number;
    newThisMonth: number;
    byRole: Record<string, number>;
  };
}

export class AdminDashboardController {
  async getDashboardStats(): Promise<DashboardStats> {
    const oneMonthAgo = subMonths(new Date(), 1);
    const sevenDaysAgo = subDays(new Date(), 7);

    // Listings Statistics
    const listingsStats = await prisma.$queryRaw`
      SELECT 
        "vehicleType" as type, 
        'ACTIVE' as status, 
        COUNT(*) as count 
      FROM "Vehicle" 
      GROUP BY "vehicleType"
      UNION ALL
      SELECT 
        "vehicleType" as type, 
        'INACTIVE' as status, 
        COUNT(*) as count 
      FROM "Vehicle" 
      GROUP BY "vehicleType"
    `;

    const listingsByType: Record<string, Record<string, number>> = (listingsStats as any[]).reduce((acc, stat) => {
      const type = stat.type as string;
      const status = stat.status as string;
      const count = Number(stat.count);

      acc[type] = acc[type] || {};
      acc[type][status] = count;
      return acc;
    }, {} as Record<string, Record<string, number>>);

    // Vehicle Assistance Requests
    const assistanceStats = await prisma.$queryRaw`
      SELECT
        status,
        COUNT(*) as count
      FROM "VehicleAssistanceRequest"
      GROUP BY status
    `;

    // Replace SOS alerts with vehicle assistance requests
    const assistanceRequests = await prisma.vehicleAssistanceRequest.findMany({
      select: {
        id: true,
        userId: true,
        location: true,
        reason: true,
        category: true,
        priority: true,
        createdAt: true,
        status: true
      },
      take: 10,
      orderBy: { createdAt: 'desc' }
    });

    // Support Tickets
    const supportTickets = await prisma.supportTicket.findMany({
      where: { createdAt: { gte: sevenDaysAgo } },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        category: true,
        status: true,
        createdAt: true
      }
    });

    const supportTicketStats = await prisma.$queryRaw`
      SELECT 
        status, 
        COUNT(*) as count 
      FROM "SupportTicket" 
      GROUP BY status
    `;

    // Bookings
    const bookings = await prisma.booking.findMany({
      where: { createdAt: { gte: sevenDaysAgo } },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: { vehicle: true }
    });

    const bookingStats = await prisma.$queryRaw`
      SELECT 
        status, 
        COUNT(*) as count,
        SUM("totalPrice") as total_revenue
      FROM "Booking" 
      GROUP BY status
    `;

    const totalRevenue = (bookingStats as any[]).reduce((sum, stat) => {
      return sum + Number(stat.total_revenue);
    }, 0);
    const platformCommission = totalRevenue * 0.15; // 15% commission

    // Users
    const userStats = await prisma.$queryRaw`
      SELECT 
        role, 
        COUNT(*) as count 
      FROM "User" 
      GROUP BY role
    `;

    const newUsersThisMonth = await prisma.user.count({
      where: { createdAt: { gte: oneMonthAgo } }
    });

    return {
      listings: {
        total: (listingsStats as any[]).reduce((sum, stat) => sum + Number(stat.count), 0),
        active: (listingsStats as any[]).filter((stat: any) => stat.status === 'ACTIVE').reduce((sum, stat) => sum + Number(stat.count), 0),
        pending: 0, // No pending status in current schema
        disabled: (listingsStats as any[]).filter((stat: any) => stat.status === 'INACTIVE').reduce((sum, stat) => sum + Number(stat.count), 0),
        byType: listingsByType
      },
      vehicleAssistance: {
        total: assistanceRequests.length,
        pending: (assistanceStats as any[]).find((stat: any) => stat.status === 'PENDING')?.count ?? 0,
        resolved: (assistanceStats as any[]).find((stat: any) => stat.status === 'RESOLVED')?.count ?? 0,
        urgent: assistanceRequests.filter(req => req.priority === 'URGENT').length,
        recentRequests: assistanceRequests.map(request => ({
          id: request.id,
          userId: request.userId,
          category: request.category,
          priority: request.priority,
          reason: request.reason,
          createdAt: request.createdAt,
          status: request.status
        }))
      },
      supportTickets: {
        total: (supportTicketStats as any[]).reduce((sum, stat) => sum + Number(stat.count), 0),
        open: (supportTicketStats as any[]).find((stat: any) => stat.status === 'OPEN')?.count ?? 0,
        inProgress: (supportTicketStats as any[]).find((stat: any) => stat.status === 'IN_PROGRESS')?.count ?? 0,
        resolved: (supportTicketStats as any[]).find((stat: any) => stat.status === 'RESOLVED')?.count ?? 0,
        escalated: (supportTicketStats as any[]).find((stat: any) => stat.status === 'ESCALATED')?.count ?? 0,
        recentTickets: supportTickets
      },
      bookings: {
        total: (bookingStats as any[]).reduce((sum, stat) => sum + Number(stat.count), 0),
        totalRevenue,
        platformCommission,
        byStatus: (bookingStats as any[]).reduce((acc, stat) => {
          acc[stat.status] = Number(stat.count);
          return acc;
        }, {} as Record<string, number>),
        recentBookings: bookings.map(booking => ({
          id: booking.id,
          vehicleModel: booking.vehicle.vehicleType,
          totalPrice: Number(booking.totalPrice),
          status: booking.status,
          startDate: booking.startDate
        }))
      },
      users: {
        total: (userStats as any[]).reduce((sum, stat) => sum + Number(stat.count), 0),
        newThisMonth: newUsersThisMonth,
        byRole: (userStats as any[]).reduce((acc, stat) => {
          acc[stat.role] = Number(stat.count);
          return acc;
        }, {} as Record<string, number>)
      }
    };
  }
}

export default AdminDashboardController;
