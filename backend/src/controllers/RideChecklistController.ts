import { Request, Response, NextFunction } from "express";
// import RideChecklistService from '../services/RideChecklistService'; // Future implementation
import { RideChecklistStage, DamageReportSeverity } from '@prisma/client';
import { monitoringService } from '../services/MonitoringService';

class RideChecklistController {
  /**
   * Upload ride checklist with start/end photos
   */
  async uploadRideChecklist(req: Request, res: Response) {
    try {
      const { 
        bookingId, 
        vehicleId, 
        stage, 
        conditionNotes 
      } = req.body;

      // Validate required fields
      if (!bookingId || !vehicleId || !stage) {
        return res.status(400).json({ 
          error: 'Missing required fields' 
        });
      }

      // Validate stage
      if (!['START', 'END'].includes(stage)) {
        return res.status(400).json({ 
          error: 'Invalid checklist stage' 
        });
      }

      // Get uploaded files
      const images = req.files as any[];
      if (!images || images.length === 0) {
        return res.status(400).json({ 
          error: 'At least one image is required' 
        });
      }

      // Upload checklist
      // Stub: RideChecklistService removed - future implementation
      const rideChecklist = {
        id: 'stub-checklist-id',
        bookingId,
        userId: req.user?.id,
        vehicleId,
        stage: stage as RideChecklistStage,
        conditionNotes,
        images: [],
        createdAt: new Date()
      };
      // const rideChecklist = await RideChecklistService.uploadRideChecklist({
      //   bookingId,
      //   userId: req.user?.id,
      //   vehicleId,
      //   stage: stage as RideChecklistStage,
      //   conditionNotes,
      //   images
      // });

      // Log successful upload
      monitoringService.log({
        level: 'info',
        message: `Ride checklist uploaded for booking ${bookingId}`,
        metadata: { 
          stage, 
          imageCount: images.length 
        }
      });

      res.status(201).json(rideChecklist);
    } catch (error) {
      // Log and handle errors
      monitoringService.captureException(error, {
        context: 'ride-checklist-upload-controller'
      });

      res.status(500).json({ 
        error: error.message || 'Failed to upload ride checklist' 
      });
    }
  }

  /**
   * Report vehicle damage
   */
  async reportDamage(req: Request, res: Response) {
    try {
      const { 
        bookingId, 
        note, 
        severity 
      } = req.body;

      // Validate required fields
      if (!bookingId || !note) {
        return res.status(400).json({ 
          error: 'Missing required fields' 
        });
      }

      // Get uploaded files (optional)
      const images = req.files as any[];

      // Report damage
      // Stub: RideChecklistService removed - future implementation
      const damageReport = {
        id: 'stub-damage-report-id',
        bookingId,
        userId: req.user?.id,
        note,
        images: [],
        severity: severity as DamageReportSeverity,
        createdAt: new Date()
      };
      // const damageReport = await RideChecklistService.reportDamage({
      //   bookingId,
      //   userId: req.user?.id,
      //   note,
      //   images,
      //   severity: severity as DamageReportSeverity
      // });

      // Log damage report
      monitoringService.log({
        level: 'warn',
        message: `Damage reported for booking ${bookingId}`,
        metadata: { 
          severity,
          imageCount: images?.length || 0 
        }
      });

      res.status(201).json(damageReport);
    } catch (error) {
      // Log and handle errors
      monitoringService.captureException(error, {
        context: 'damage-report-controller'
      });

      res.status(500).json({ 
        error: error.message || 'Failed to report damage' 
      });
    }
  }

  /**
   * Get ride checklists for a booking
   */
  async getRideChecklists(req: Request, res: Response) {
    try {
      const { bookingId } = req.params;

      // Validate booking ownership
      // Stub: RideChecklistService removed - future implementation
      const checklists = [];
      // const checklists = await RideChecklistService.getRideChecklists(bookingId);

      res.status(200).json(checklists);
    } catch (error) {
      // Log and handle errors
      monitoringService.captureException(error, {
        context: 'get-ride-checklists-controller'
      });

      res.status(500).json({ 
        error: error.message || 'Failed to retrieve ride checklists' 
      });
    }
  }

  /**
   * Get damage reports for a booking
   */
  async getDamageReports(req: Request, res: Response) {
    try {
      const { bookingId } = req.params;

      // Retrieve damage reports
      // Stub: RideChecklistService removed - future implementation
      const damageReports = [];
      // const damageReports = await RideChecklistService.getDamageReports(bookingId);

      res.status(200).json(damageReports);
    } catch (error) {
      // Log and handle errors
      monitoringService.captureException(error, {
        context: 'get-damage-reports-controller'
      });

      res.status(500).json({ 
        error: error.message || 'Failed to retrieve damage reports' 
      });
    }
  }
}

export default new RideChecklistController(); 