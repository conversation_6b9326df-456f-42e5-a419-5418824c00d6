import { Request, Response } from 'express';
import VehicleManagementService from '../services/VehicleManagementService';
import { logger } from '../utils/logger';

// Import VehicleCatalogController for suggestions
const VehicleCatalogController = require('./VehicleCatalogController').default;

export class VehicleManagementController {
  /**
   * Get all vehicles for current provider
   * GET /api/providers/vehicles
   */
  static async getProviderVehicles(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const result = await VehicleManagementService.getProviderVehicles(userId, page, limit);
      
      res.json({
        success: true,
        data: result.vehicles,
        pagination: {
          page,
          limit,
          total: result.total,
          pages: result.pages
        }
      });
    } catch (error) {
      logger.error('Error in getProviderVehicles:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicles',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicle by ID for current provider
   * GET /api/providers/vehicles/:id
   */
  static async getVehicleById(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      const vehicle = await VehicleManagementService.getVehicleById(id, userId);
      
      if (!vehicle) {
        return res.status(404).json({
          success: false,
          error: 'Vehicle not found'
        });
      }

      res.json({
        success: true,
        data: vehicle
      });
    } catch (error) {
      logger.error('Error in getVehicleById:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Create new vehicle for current provider
   * POST /api/providers/vehicles
   */
  static async createVehicle(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const {
        vehicleType,
        brand,
        model,
        year,
        category,
        dailyRate,
        weeklyRate,
        monthlyRate,
        depositAmount,
        location_city,
        location_address,
        availableUnits,
        hasInsurance,
        insurancePrice,
        deliveryAvailable,
        pickupAvailable,
        dropoffAvailable,
        helmetsIncluded,
        raincoatsIncluded,
        fullTank,
        smartTags,
        addOns
      } = req.body;

      // Basic validation
      if (!vehicleType) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle type is required'
        });
      }

      const vehicle = await VehicleManagementService.createVehicle(userId, {
        vehicleType,
        brand,
        model,
        year,
        category,
        dailyRate: dailyRate ? parseFloat(dailyRate) : undefined,
        weeklyRate: weeklyRate ? parseFloat(weeklyRate) : undefined,
        monthlyRate: monthlyRate ? parseFloat(monthlyRate) : undefined,
        depositAmount: depositAmount ? parseFloat(depositAmount) : undefined,
        location_city,
        location_address,
        availableUnits: availableUnits ? parseInt(availableUnits) : undefined,
        hasInsurance: Boolean(hasInsurance),
        insurancePrice: insurancePrice ? parseFloat(insurancePrice) : undefined,
        deliveryAvailable: Boolean(deliveryAvailable),
        pickupAvailable: Boolean(pickupAvailable),
        dropoffAvailable: Boolean(dropoffAvailable),
        helmetsIncluded: helmetsIncluded ? parseInt(helmetsIncluded) : undefined,
        raincoatsIncluded: Boolean(raincoatsIncluded),
        fullTank: Boolean(fullTank),
        smartTags: Array.isArray(smartTags) ? smartTags : [],
        addOns: Array.isArray(addOns) ? addOns : []
      });

      res.status(201).json({
        success: true,
        data: vehicle,
        message: 'Vehicle created successfully'
      });
    } catch (error) {
      logger.error('Error in createVehicle:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update vehicle for current provider
   * PUT /api/providers/vehicles/:id
   */
  static async updateVehicle(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      // TODO: Implement update functionality
      res.status(501).json({
        success: false,
        error: 'Update functionality not implemented yet'
      });
    } catch (error) {
      logger.error('Error in updateVehicle:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Delete vehicle for current provider
   * DELETE /api/providers/vehicles/:id
   */
  static async deleteVehicle(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      // TODO: Implement delete functionality
      res.status(501).json({
        success: false,
        error: 'Delete functionality not implemented yet'
      });
    } catch (error) {
      logger.error('Error in deleteVehicle:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete vehicle',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicle availability for current provider
   * GET /api/providers/vehicles/:id/availability
   */
  static async getVehicleAvailability(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID
      const { id } = req.params;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Vehicle ID is required'
        });
      }

      // TODO: Implement availability functionality
      res.status(501).json({
        success: false,
        error: 'Availability functionality not implemented yet'
      });
    } catch (error) {
      logger.error('Error in getVehicleAvailability:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle availability',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get vehicle suggestions from catalog for providers
   * GET /api/providers/vehicle-suggestions?query=honda&limit=10
   */
  static async getVehicleSuggestions(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const query = req.query.query as string || '';
      const limit = parseInt(req.query.limit as string) || 20;

      // If no query provided, get popular vehicles
      if (!query) {
        // Get all vehicles and return first 20
        const catalogReq = { query: {} } as Request;
        const catalogRes = {
          json: (data: any) => {
            const suggestions = (data.data || []).slice(0, limit).map((vehicle: any) => ({
              id: vehicle.id,
              make: vehicle.make,
              model: vehicle.model,
              year: vehicle.year,
              category: vehicle.category,
              type: vehicle.type,
              engine: vehicle.engine,
              image_url: vehicle.image_url,
              suggested_daily_rate: vehicle.daily_rate,
              features: vehicle.features || []
            }));

            res.json({
              success: true,
              data: suggestions,
              message: 'Popular vehicle suggestions'
            });
          },
          status: (code: number) => ({
            json: (data: any) => res.status(code).json(data)
          })
        } as Response;

        return VehicleCatalogController.getAllVehicles(catalogReq, catalogRes);
      }

      // Search vehicles by query - use 'q' parameter as expected by VehicleCatalogController
      const catalogReq = {
        query: { q: query, limit: limit.toString() },
        params: {},
        body: {},
        headers: {},
        method: 'GET',
        url: '/vehicles'
      } as unknown as Request;
      const catalogRes = {
        json: (data: any) => {
          // Transform catalog data for provider suggestions
          const suggestions = (data.data || []).map((vehicle: any) => ({
            id: vehicle.id,
            make: vehicle.make,
            model: vehicle.model,
            year: vehicle.year,
            category: vehicle.category,
            type: vehicle.type,
            engine: vehicle.engine,
            image_url: vehicle.image_url,
            suggested_daily_rate: vehicle.daily_rate,
            features: vehicle.features || []
          }));

          res.json({
            success: true,
            data: suggestions,
            message: `Found ${suggestions.length} vehicle suggestions`
          });
        },
        status: (code: number) => ({
          json: (data: any) => res.status(code).json(data)
        })
      } as Response;

      return VehicleCatalogController.searchVehicles(catalogReq, catalogRes);
    } catch (error) {
      logger.error('Error in getVehicleSuggestions:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicle suggestions',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default VehicleManagementController;
