import { Request, Response } from 'express';
import VehicleDataService from '../services/VehicleDataService';

export class VehicleDataController {
  private vehicleDataService: VehicleDataService;

  constructor() {
    this.vehicleDataService = VehicleDataService.getInstance();
  }

  async updateCatalogue(req: Request, res: Response): Promise<void> {
    try {
      console.log('🔄 Starting vehicle catalogue update...');
      
      await this.vehicleDataService.updateVehicleCatalogue();
      
      res.status(200).json({
        success: true,
        message: 'Vehicle catalogue updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating vehicle catalogue:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update vehicle catalogue',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async scrapeHondaModels(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏍️ Scraping Honda models...');
      
      const vehicles = await this.vehicleDataService.scrapeHondaModels();
      
      res.status(200).json({
        success: true,
        message: `Successfully scraped ${vehicles.length} Honda models`,
        data: vehicles
      });
    } catch (error) {
      console.error('Error scraping Honda models:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to scrape Honda models',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async scrapeYamahaModels(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏍️ Scraping Yamaha models...');
      
      const vehicles = await this.vehicleDataService.scrapeYamahaModels();
      
      res.status(200).json({
        success: true,
        message: `Successfully scraped ${vehicles.length} Yamaha models`,
        data: vehicles
      });
    } catch (error) {
      console.error('Error scraping Yamaha models:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to scrape Yamaha models',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async scrapeSuzukiModels(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏍️ Scraping Suzuki models...');
      
      const vehicles = await this.vehicleDataService.scrapeSuzukiModels();
      
      res.status(200).json({
        success: true,
        message: `Successfully scraped ${vehicles.length} Suzuki models`,
        data: vehicles
      });
    } catch (error) {
      console.error('Error scraping Suzuki models:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to scrape Suzuki models',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async scrapeVespaModels(req: Request, res: Response): Promise<void> {
    try {
      console.log('🏍️ Scraping Vespa models...');
      
      const vehicles = await this.vehicleDataService.scrapeVespaModels();
      
      res.status(200).json({
        success: true,
        message: `Successfully scraped ${vehicles.length} Vespa models`,
        data: vehicles
      });
    } catch (error) {
      console.error('Error scraping Vespa models:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to scrape Vespa models',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async scrapeThailandHondaModels(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapeThailandHondaModels();
    if (res) return res.json(data);
    return data;
  }

  async scrapeVietnamHondaModels(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapeVietnamHondaModels();
    if (res) return res.json(data);
    return data;
  }

  async scrapeMalaysiaYamahaModels(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapeMalaysiaYamahaModels();
    if (res) return res.json(data);
    return data;
  }

  async scrapePhilippinesSuzukiModels(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapePhilippinesSuzukiModels();
    if (res) return res.json(data);
    return data;
  }

  async scrapeBikeWaleSEAsia(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapeBikeWaleSEAsia();
    if (res) return res.json(data);
    return data;
  }

  async scrapeBikeWaleMain(req?: any, res?: any) {
    const data = await this.vehicleDataService.scrapeBikeWaleMain();
    if (res) return res.json(data);
    return data;
  }

  async getAllManufacturers(req: Request, res: Response): Promise<void> {
    try {
      console.log('🌏 Scraping all manufacturer models...');
      
      const vehicles = await this.vehicleDataService.scrapeAllManufacturers();
      
      res.status(200).json({
        success: true,
        message: `Successfully scraped ${vehicles.length} vehicles from all manufacturers`,
        data: vehicles
      });
    } catch (error) {
      console.error('Error scraping all manufacturers:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to scrape manufacturer models',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async getCatalogueStatus(req: Request, res: Response): Promise<void> {
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      const vehicleCount = await prisma.vehicle.count();
      const manufacturers = await prisma.vehicle.groupBy({
        by: ['brand'],
        _count: {
          brand: true
        }
      });
      
      res.status(200).json({
        success: true,
        data: {
          totalVehicles: vehicleCount,
          manufacturers: manufacturers.map(m => ({
            brand: m.brand,
            count: m._count.brand
          }))
        }
      });
    } catch (error) {
      console.error('Error getting catalogue status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get catalogue status',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default VehicleDataController; 