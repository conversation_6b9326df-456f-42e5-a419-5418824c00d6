import { Request, Response, NextFunction } from "express";
import { 
  PrismaClient, 
  Prisma 
} from "@prisma/client";
import { VehicleTagService } from "../services/VehicleTagService";
import { GeospatialSearchService } from "../services/GeospatialSearchService";
import { uploadToCloudStorage } from "../utils/fileUpload";
import { logger } from "../utils/logger";

export class VehicleController {
  private prisma: PrismaClient;
  private tagService: VehicleTagService;
  private geoService: GeospatialSearchService;

  constructor() {
    this.prisma = new PrismaClient();
    this.tagService = new VehicleTagService(this.prisma);
    this.geoService = new GeospatialSearchService();
  }

  // Create a new vehicle
  async createVehicle(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        vehicleType,
        dailyRate,
        weeklyRate,
        monthlyRate,
        hasInsurance,
        insurancePrice,
        addOns,
        addOnPrices,
        smartTags,
        deliveryAvailable,
        deliveryRadius,
        deliveryOptions,
        location_address,
        location_city,
        location_latitude,
        location_longitude,
        providerId
      } = req.body;

      // Validate provider
      const provider = await this.prisma.user.findUnique({
        where: { id: providerId, role: 'PROVIDER' }
      });

      if (!provider) {
        return res.status(400).json({ error: 'Invalid provider' });
      }

      // Create vehicle
      const vehicle = await this.prisma.vehicle.create({
        data: {
          providerId,
          vehicleType,
          dailyRate: dailyRate ? parseFloat(dailyRate) : null,
          weeklyRate: weeklyRate ? parseFloat(weeklyRate) : null,
          monthlyRate: monthlyRate ? parseFloat(monthlyRate) : null,
          hasInsurance: hasInsurance || false,
          insurancePrice: insurancePrice ? parseFloat(insurancePrice) : 0,
          addOns: addOns || [],
          addOnPrices: addOnPrices || {},
          smartTags: smartTags || [],
          deliveryAvailable: deliveryAvailable || false,
          deliveryRadius: deliveryRadius ? parseFloat(deliveryRadius) : null,
          deliveryOptions: deliveryOptions || {},
          location_address,
          location_city,
          location_latitude: location_latitude ? parseFloat(location_latitude) : null,
          location_longitude: location_longitude ? parseFloat(location_longitude) : null
        }
      });

      // Handle image uploads
      if (req.files && req.files.length) {
        const imageUrls = await Promise.all(
          (req.files as Express.Multer.File[]).map(
            async (file) => await uploadToCloudStorage(file)
          )
        );

        await this.prisma.vehicleImage.createMany({
          data: imageUrls.map((url, index) => ({
            vehicleId: vehicle.id,
            url,
            isPrimary: index === 0
          }))
        });
      }

      logger.info('Vehicle created successfully', { vehicleId: vehicle.id });
      res.status(201).json(vehicle);
    } catch (error) {
      logger.error('Vehicle creation error', { error });
      next(error);
    }
  }

  // Update vehicle details
  async updateVehicle(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const vehicle = await this.prisma.vehicle.update({
        where: { id },
        data: updateData as Prisma.VehicleUpdateInput
      });

      // Handle smart tags
      if (updateData.smartTags) {
        await this.tagService.addTagsToVehicle(id, updateData.smartTags);
      }

      // Handle image updates
      if (req.files && req.files.length) {
        const imageUrls = await Promise.all(
          (req.files as Express.Multer.File[]).map(
            async (file) => await uploadToCloudStorage(file)
          )
        );

        await this.prisma.vehicleImage.createMany({
          data: imageUrls.map((url, index) => ({
            vehicleId: id,
            url,
            isPrimary: index === 0
          }))
        });
      }

      logger.info('Vehicle updated successfully', { vehicleId: id });
      res.json(vehicle);
    } catch (error) {
      logger.error('Vehicle update error', { error });
      next(error);
    }
  }

  // Delete a vehicle
  async deleteVehicle(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      // Check for active bookings
      const activeBookings = await this.prisma.booking.count({
        where: { 
          vehicleId: id, 
          status: { 
            in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS'] 
          } 
        }
      });

      if (activeBookings > 0) {
        return res.status(400).json({ 
          error: 'Cannot delete vehicle with active bookings' 
        });
      }

      // Delete the vehicle (hard delete since there's no status field)
      await this.prisma.vehicle.delete({
        where: { id }
      });

      logger.info('Vehicle deleted successfully', { vehicleId: id });
      res.json({ message: 'Vehicle deleted successfully' });
    } catch (error) {
      logger.error('Vehicle deletion error', { error });
      next(error);
    }
  }

  // Advanced vehicle search with multiple filters
  async searchVehicles(req: Request, res: Response, next: NextFunction) {
    try {
      const { 
        latitude, 
        longitude, 
        radius = 10,
        vehicleType,
        minPrice,
        maxPrice,
        features,
        startDate,
        endDate
      } = req.query;

      // Build search query
      const where: Prisma.VehicleWhereInput = {};

      if (vehicleType) {
        where.vehicleType = vehicleType as string;
      }

      if (minPrice || maxPrice) {
        where.dailyRate = {};
        if (minPrice) where.dailyRate.gte = parseFloat(minPrice as string);
        if (maxPrice) where.dailyRate.lte = parseFloat(maxPrice as string);
      }

      // Get vehicles
      const vehicles = await this.prisma.vehicle.findMany({
        where,
        include: {
          images: true,
          availability: true
        }
      });

      // Filter by availability if dates provided
      let availableVehicles = vehicles;
      if (startDate && endDate) {
        availableVehicles = vehicles.filter(vehicle => 
          this.checkVehicleAvailability(vehicle.id, new Date(startDate as string), new Date(endDate as string))
        );
      }

      // Add distance calculation if coordinates provided
      if (latitude && longitude) {
        availableVehicles = availableVehicles.map(vehicle => ({
          ...vehicle,
          distance: this.calculateDistance(
            parseFloat(latitude as string),
            parseFloat(longitude as string),
            vehicle.location_latitude ? Number(vehicle.location_latitude) : 0,
            vehicle.location_longitude ? Number(vehicle.location_longitude) : 0
          )
        })).filter(vehicle => vehicle.distance <= parseFloat(radius as string));
      }

      logger.info('Vehicle search completed', { 
        total: vehicles.length, 
        available: availableVehicles.length 
      });
      
      res.json(availableVehicles);
    } catch (error) {
      logger.error('Vehicle search error', { error });
      next(error);
    }
  }

  // Get vehicle details
  async getVehicleDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;

      const vehicle = await this.prisma.vehicle.findUnique({
        where: { id },
        include: {
          images: true,
          availability: true,
          reviews: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  profileImage: true
                }
              }
            }
          }
        }
      });

      if (!vehicle) {
        return res.status(404).json({ error: 'Vehicle not found' });
      }

      // Calculate average rating
      const avgRating = vehicle.reviews.length > 0 
        ? vehicle.reviews.reduce((sum, review) => sum + review.rating, 0) / vehicle.reviews.length 
        : 0;

      const vehicleWithRating = {
        ...vehicle,
        avgRating,
        reviewCount: vehicle.reviews.length
      };

      res.json(vehicleWithRating);
    } catch (error) {
      logger.error('Get vehicle details error', { error });
      next(error);
    }
  }

  // Check vehicle availability
  private async checkVehicleAvailability(
    vehicleId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<boolean> {
    try {
      const conflictingBookings = await this.prisma.booking.count({
        where: {
          vehicleId,
          OR: [
            {
              startDate: { lte: startDate },
              endDate: { gte: startDate }
            },
            {
              startDate: { lte: endDate },
              endDate: { gte: endDate }
            },
            {
              startDate: { gte: startDate },
              endDate: { lte: endDate }
            }
          ],
          status: {
            in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS']
          }
        }
      });

      return conflictingBookings === 0;
    } catch (error) {
      logger.error('Check vehicle availability error', { vehicleId, error });
      return false;
    }
  }

  // Calculate distance between two points
  private calculateDistance(
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }
}

export default new VehicleController();
