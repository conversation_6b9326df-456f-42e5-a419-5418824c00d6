import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import vehicleSearchService, { SEAsiaVehicle } from '../services/vehicleSearchService';
import { asyncHandler } from '../utils/asyncHandler';

const prisma = new PrismaClient();

export interface CreateVehicleRequest {
  // Vehicle Details
  brand?: string;
  model?: string;
  year?: string;
  engine?: string;
  type?: string;
  category?: string;
  vehicleId?: number; // ID from SE Asia database
  
  // Inventory Management
  availableUnits: number;
  
  // Pricing
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  yearlyRate?: number;
  
  // Rental Options
  insuranceOffered: boolean;
  dropoffAvailable: boolean;
  pickupAvailable: boolean;
  helmetsIncluded: number;
  raincoatsIncluded: boolean;
  fullTank: boolean;
  depositAmount: number;
  
  // Location
  location_address?: string;
  location_city?: string;
  location_latitude?: number;
  location_longitude?: number;
  
  // Images
  images: string[];
}

export const vehicleListingController = {
  /**
   * Search vehicles in SE Asia database
   */
  searchVehicles: asyncHandler(async (req: Request, res: Response) => {
    const { query, limit = 10 } = req.query;
    
    const result = vehicleSearchService.searchVehicles(
      query as string, 
      parseInt(limit as string)
    );
    
    res.json({
      success: true,
      data: result
    });
  }),

  /**
   * Get vehicle suggestions for auto-complete
   */
  getVehicleSuggestions: asyncHandler(async (req: Request, res: Response) => {
    const { query, limit = 5 } = req.query;
    
    const suggestions = vehicleSearchService.getSuggestions(
      query as string,
      parseInt(limit as string)
    );
    
    res.json({
      success: true,
      data: suggestions
    });
  }),

  /**
   * Get vehicle by ID from SE Asia database
   */
  getVehicleById: asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    
    const vehicle = vehicleSearchService.getVehicleById(parseInt(id));
    
    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }
    
    res.json({
      success: true,
      data: vehicle
    });
  }),

  /**
   * Get popular vehicles
   */
  getPopularVehicles: asyncHandler(async (req: Request, res: Response) => {
    const vehicles = vehicleSearchService.getPopularVehicles();
    
    res.json({
      success: true,
      data: vehicles
    });
  }),

  /**
   * Get vehicles by category
   */
  getVehiclesByCategory: asyncHandler(async (req: Request, res: Response) => {
    const { category } = req.params;
    
    const vehicles = vehicleSearchService.getVehiclesByCategory(category);
    
    res.json({
      success: true,
      data: vehicles
    });
  }),

  /**
   * Get vehicles by type
   */
  getVehiclesByType: asyncHandler(async (req: Request, res: Response) => {
    const { type } = req.params;
    
    const vehicles = vehicleSearchService.getVehiclesByType(type);
    
    res.json({
      success: true,
      data: vehicles
    });
  }),

  /**
   * Get all brands
   */
  getAllBrands: asyncHandler(async (req: Request, res: Response) => {
    const brands = vehicleSearchService.getAllBrands();
    
    res.json({
      success: true,
      data: brands
    });
  }),

  /**
   * Get all categories
   */
  getAllCategories: asyncHandler(async (req: Request, res: Response) => {
    const categories = vehicleSearchService.getAllCategories();
    
    res.json({
      success: true,
      data: categories
    });
  }),

  /**
   * Get all types
   */
  getAllTypes: asyncHandler(async (req: Request, res: Response) => {
    const types = vehicleSearchService.getAllTypes();
    
    res.json({
      success: true,
      data: types
    });
  }),

  /**
   * Get the full SE Asia vehicle list (no filtering)
   */
  getAllSEAsiaVehicles: asyncHandler(async (req: Request, res: Response) => {
    const vehicles = vehicleSearchService.getAllVehicles();
    res.json({
      success: true,
      data: vehicles
    });
  }),

  /**
   * Create a new vehicle listing
   */
  createVehicleListing: asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const vehicleData: CreateVehicleRequest = req.body;

    // Validate required fields
    if (!vehicleData.availableUnits || !vehicleData.dailyRate) {
      return res.status(400).json({
        success: false,
        message: 'Available units and daily rate are required'
      });
    }

    // If vehicleId is provided, get vehicle details from SE Asia database
    let vehicleDetails: Partial<SEAsiaVehicle> = {};
    if (vehicleData.vehicleId) {
      const seAsiaVehicle = vehicleSearchService.getVehicleById(vehicleData.vehicleId);
      if (seAsiaVehicle) {
        vehicleDetails = {
          brand: seAsiaVehicle.brand,
          model: seAsiaVehicle.model,
          year: seAsiaVehicle.year,
          engine: seAsiaVehicle.engine,
          type: seAsiaVehicle.type,
          category: seAsiaVehicle.category
        };
      }
    }

    // Create vehicle in database
    const vehicle = await prisma.vehicle.create({
      data: {
        providerId: userId,
        vehicleType: vehicleData.type || vehicleDetails.type || 'scooter',
        
        // Vehicle details
        brand: vehicleData.brand || vehicleDetails.brand,
        model: vehicleData.model || vehicleDetails.model,
        year: vehicleData.year || vehicleDetails.year,
        engine: vehicleData.engine || vehicleDetails.engine,
        type: vehicleData.type || vehicleDetails.type,
        category: vehicleData.category || vehicleDetails.category,
        
        // Inventory
        availableUnits: vehicleData.availableUnits,
        
        // Pricing
        dailyRate: vehicleData.dailyRate,
        weeklyRate: vehicleData.weeklyRate,
        monthlyRate: vehicleData.monthlyRate,
        yearlyRate: vehicleData.yearlyRate,
        
        // Rental options
        insuranceOffered: vehicleData.insuranceOffered,
        dropoffAvailable: vehicleData.dropoffAvailable,
        pickupAvailable: vehicleData.pickupAvailable,
        helmetsIncluded: vehicleData.helmetsIncluded,
        raincoatsIncluded: vehicleData.raincoatsIncluded,
        fullTank: vehicleData.fullTank,
        depositAmount: vehicleData.depositAmount,
        
        // Location
        location_address: vehicleData.location_address,
        location_city: vehicleData.location_city,
        location_latitude: vehicleData.location_latitude,
        location_longitude: vehicleData.location_longitude,
        
        // Legacy fields for backward compatibility
        hasInsurance: vehicleData.insuranceOffered,
        insurancePrice: vehicleData.depositAmount,
        deliveryAvailable: vehicleData.dropoffAvailable || vehicleData.pickupAvailable
      }
    });

    // Create vehicle images if provided
    if (vehicleData.images && vehicleData.images.length > 0) {
      const imagePromises = vehicleData.images.map((imageUrl, index) =>
        prisma.vehicleImage.create({
          data: {
            vehicleId: vehicle.id,
            url: imageUrl,
            isPrimary: index === 0
          }
        })
      );
      
      await Promise.all(imagePromises);
    }

    res.status(201).json({
      success: true,
      message: 'Vehicle listing created successfully',
      data: vehicle
    });
  }),

  /**
   * Update vehicle listing
   */
  updateVehicleListing: asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    const { id } = req.params;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid vehicle ID format (must be UUID)'
      });
    }

    // Check if vehicle belongs to user
    const existingVehicle = await prisma.vehicle.findFirst({
      where: {
        id,
        providerId: userId
      }
    });

    if (!existingVehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found or not owned by user'
      });
    }

    const updateData = req.body;

    const updatedVehicle = await prisma.vehicle.update({
      where: { id },
      data: updateData
    });

    res.json({
      success: true,
      message: 'Vehicle listing updated successfully',
      data: updatedVehicle
    });
  }),

  /**
   * Get user's vehicle listings
   */
  getUserVehicleListings: asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const vehicles = await prisma.vehicle.findMany({
      where: {
        providerId: userId
      },
      include: {
        images: true,
        availability: true
      },
      orderBy: {
        id: 'desc'
      }
    });

    res.json({
      success: true,
      data: vehicles
    });
  }),

  /**
   * Delete vehicle listing
   */
  deleteVehicleListing: asyncHandler(async (req: Request, res: Response) => {
    const userId = (req as any).user?.id;
    const { id } = req.params;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid vehicle ID format (must be UUID)'
      });
    }

    // Check if vehicle belongs to user
    const existingVehicle = await prisma.vehicle.findFirst({
      where: {
        id,
        providerId: userId
      }
    });

    if (!existingVehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found or not owned by user'
      });
    }

    // Check if vehicle has active bookings
    const activeBookings = await prisma.booking.findFirst({
      where: {
        vehicleId: id,
        status: {
          in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS']
        }
      }
    });

    if (activeBookings) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete vehicle with active bookings'
      });
    }

    // Delete vehicle images first
    await prisma.vehicleImage.deleteMany({
      where: { vehicleId: id }
    });

    // Delete vehicle availability
    await prisma.vehicleAvailability.deleteMany({
      where: { vehicleId: id }
    });

    // Delete vehicle
    await prisma.vehicle.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Vehicle listing deleted successfully'
    });
  }),

  /**
   * Get vehicle listing by ID
   */
  getVehicleListing: asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Validate UUID format
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(id)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid vehicle ID format (must be UUID)'
      });
    }

    const vehicle = await prisma.vehicle.findUnique({
      where: { id },
      include: {
        images: true,
        availability: true,
      }
    });

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    res.json({
      success: true,
      data: vehicle
    });
  }),

  /**
   * Add a manually entered vehicle to the database
   */
  addManualVehicle: asyncHandler(async (req: Request, res: Response) => {
    const { brand, model, year, engine, type, category } = req.body;

    if (!brand || !model) {
      return res.status(400).json({
        success: false,
        message: 'Brand and model are required'
      });
    }

    try {
      // Create new vehicle object
      const newVehicle = {
        id: Date.now(), // Generate unique ID
        brand,
        model,
        year: year || '2023',
        engine: engine || '',
        type: type || 'scooter',
        category: category || 'medium'
      };

      // In a real implementation, you would save this to your database
      // For now, we'll add it to the in-memory vehicles list
      const vehicleSearchService = require('../services/vehicleSearchService');
      vehicleSearchService.addVehicle(newVehicle);

      res.status(201).json({
        success: true,
        message: 'Vehicle added successfully',
        data: newVehicle
      });
    } catch (error) {
      console.error('Error adding manual vehicle:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add vehicle'
      });
    }
  })
};

export default vehicleListingController;