import { Request, Response, NextFunction } from "express";
import { PrismaClient } from '@prisma/client';
import { EmailService } from '../services/EmailService';
import { NotificationService } from '../services/CommunicationServices';

export class SupportController {
  private prisma: PrismaClient;
  private emailService: EmailService;
  private notificationService: NotificationService;

  constructor() {
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    this.notificationService = new NotificationService();
  }

  async createSupportTicket(req: Request, res: Response) {
    try {
      const { 
        subject, 
        description, 
        category, 
        priority 
      } = req.body;
      const userId = req.user?.id;
      const role = req.user?.role;

      // Validate input
      if (!description || description.trim().length < 10) {
        return res.status(400).json({ message: 'Support ticket description must be at least 10 characters long' });
      }

      const ticket = await this.prisma.supportTicket.create({
        data: {
          userId,
          subject,
          description,
          category,
          priority: priority || 'MEDIUM',
          status: 'OPEN'
        }
      });

      // Send email notification
      await this.emailService.sendEmail({
        to: process.env.SUPPORT_EMAIL || '<EMAIL>',
        subject: 'New Support Ticket Submitted',
        template: 'support_ticket',
        data: {
          ticketId: ticket.id,
          category: ticket.category,
          description: ticket.description
        }
      });

      res.status(201).json(ticket);
    } catch (error) {
      console.error('Support ticket creation error:', error);
      res.status(500).json({ message: 'Failed to create support ticket', error: error instanceof Error ? error.message : error });
    }
  }

  async getUserSupportTickets(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const role = req.user?.role;

      const tickets = await this.prisma.supportTicket.findMany({
        where: {
          userId: userId
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      res.json(tickets);
    } catch (error) {
      res.status(500).json({ message: 'Failed to retrieve support tickets', error: error instanceof Error ? error.message : error });
    }
  }

  async getAllSupportTickets(req: Request, res: Response) {
    try {
      // Ensure only admin can access
      if (req.user?.role !== 'ADMIN') {
        return res.status(403).json({ message: 'Unauthorized access' });
      }

      const { status, category } = req.query;

      const tickets = await this.prisma.supportTicket.findMany({
        where: {
          ...(status ? { status: status as string } : {}),
          ...(category ? { category: category as string } : {})
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      res.json(tickets);
    } catch (error) {
      res.status(500).json({ message: 'Failed to retrieve support tickets', error: error instanceof Error ? error.message : error });
    }
  }

  async updateSupportTicketStatus(req: Request, res: Response) {
    try {
      // Ensure only admin can update
      if (req.user?.role !== 'ADMIN') {
        return res.status(403).json({ message: 'Unauthorized access' });
      }

      const { id } = req.params;
      const { status, description } = req.body;

      const ticket = await this.prisma.supportTicket.update({
        where: { id },
        data: {
          status,
          description,
          updatedAt: new Date()
        }
      });

      // Notify user about ticket status update
      await this.emailService.sendEmail({
        to: ticket.userId || '',
        subject: 'Support Ticket Status Update',
        template: 'support_ticket_update',
        data: {
          ticketId: ticket.id,
          status: ticket.status,
          description: ticket.description
        }
      });

      res.json(ticket);
    } catch (error) {
      res.status(500).json({ message: 'Failed to update support ticket', error: error instanceof Error ? error.message : error });
    }
  }

  async createSOSAlert(req: Request, res: Response) {
    try {
      const { location, message } = req.body;
      const userId = req.user?.id;

      // Validate input
      if (!message || message.trim().length < 10) {
        return res.status(400).json({ message: 'SOS alert message must be at least 10 characters long' });
      }

      // SOS system has been replaced with Vehicle Assistance Request system
      return res.status(410).json({
        message: 'SOS Alert system has been replaced with Vehicle Assistance Request system',
        redirectTo: '/api/vehicle-assistance',
        deprecated: true
      });


    } catch (error) {
      res.status(500).json({ message: 'Failed to create SOS alert', error: error instanceof Error ? error.message : error });
    }
  }

  async getAdminSOSAlerts(req: Request, res: Response) {
    try {
      // SOS system has been replaced with Vehicle Assistance Request system
      return res.status(410).json({
        message: 'SOS Alert system has been replaced with Vehicle Assistance Request system',
        redirectTo: '/api/vehicle-assistance',
        deprecated: true
      });
    } catch (error) {
      res.status(500).json({ message: 'Service unavailable', error: error instanceof Error ? error.message : error });
    }
  }
}

export default SupportController;
