import { Request, Response } from 'express';
import ProviderService from '../services/ProviderService';
import { logger } from '../utils/logger';

export class ProviderController {
  /**
   * Get provider profile by ID
   * GET /api/providers/:id
   */
  static async getProvider(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Provider ID is required'
        });
      }

      const provider = await ProviderService.getProviderById(id);

      if (!provider) {
        return res.status(404).json({
          success: false,
          error: 'Provider not found'
        });
      }

      res.json({
        success: true,
        data: provider
      });
    } catch (error) {
      logger.error('Error in getProvider:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch provider',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get current provider's profile (authenticated)
   * GET /api/providers/me
   */
  static async getCurrentProvider(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const provider = await ProviderService.getProviderById(userId);

      if (!provider) {
        console.log('❌ Provider not found:', userId);
        return res.status(404).json({
          success: false,
          error: 'Provider not found',
          showCTA: true,
          ctaType: 'COMPLETE_PROFILE',
          ctaMessage: 'Complete your provider profile to get started',
          ctaAction: '/provider-dashboard/profile/setup'
        });
      }

      res.json({
        success: true,
        data: provider
      });
    } catch (error) {
      logger.error('Error in getCurrentProvider:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch provider profile',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get provider dashboard statistics
   * GET /api/providers/dashboard/stats
   */
  static async getDashboardStats(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const headerUserId = req.headers['x-user-id'] as string;
      const userId = headerUserId || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID

      console.log('🔍 Dashboard stats request:');
      console.log('  - Header user ID:', headerUserId);
      console.log('  - Using user ID:', userId);
      console.log('  - All headers:', req.headers);

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      try {
        const stats = await ProviderService.getProviderDashboardStats(userId);
        res.json({
          success: true,
          data: stats
        });
      } catch (error: any) {
        console.error('❌ Dashboard stats error:', error);

        // Check if it's a "no data" scenario vs actual error
        if (error.message === 'User not found') {
          return res.status(404).json({
            success: false,
            error: 'Provider not found',
            showCTA: true,
            ctaType: 'COMPLETE_PROFILE',
            ctaMessage: 'Complete your provider profile to access dashboard features',
            ctaAction: '/provider-dashboard/profile'
          });
        }

        if (error.message === 'User is not a provider') {
          return res.status(403).json({
            success: false,
            error: 'Access denied',
            showCTA: true,
            ctaType: 'BECOME_PROVIDER',
            ctaMessage: 'Become a provider to access this dashboard',
            ctaAction: '/become-provider'
          });
        }

        // For other errors, return empty state with CTA
        res.status(500).json({
          success: false,
          error: 'Failed to load dashboard data',
          showCTA: true,
          ctaType: 'RETRY',
          ctaMessage: 'Unable to load dashboard. Please try again.',
          ctaAction: 'RETRY'
        });
      }
    } catch (error) {
      logger.error('Error in getDashboardStats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dashboard statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update provider profile
   * PUT /api/providers/me
   */
  static async updateProvider(req: Request, res: Response) {
    try {
      // TODO: Get user ID from JWT token
      const userId = req.headers['x-user-id'] as string || 'cmd7hwwhm0000xcxshowpwrwd'; // Temporary test provider ID

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const {
        fullName,
        phone,
        businessName,
        businessAddress,
        city,
        state,
        zipCode,
        country,
        bio,
        avatar
      } = req.body;

      const updatedProvider = await ProviderService.updateProvider(userId, {
        name: fullName,
        phoneNumber: phone,
        companyName: businessName,
        // businessAddress, city, state, zipCode, country, bio, and avatar fields not available in User model
      });

      res.json({
        success: true,
        data: updatedProvider,
        message: 'Provider profile updated successfully'
      });
    } catch (error) {
      logger.error('Error in updateProvider:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update provider profile',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get all providers (admin only)
   * GET /api/providers?page=1&limit=20&search=query
   */
  static async getAllProviders(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const search = req.query.search as string;

      const result = await ProviderService.getAllProviders(page, limit, search);

      res.json({
        success: true,
        data: result.providers,
        pagination: {
          page,
          limit,
          total: result.total,
          pages: result.pages
        }
      });
    } catch (error) {
      logger.error('Error in getAllProviders:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch providers',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Health check for provider service
   * GET /api/providers/health
   */
  static async healthCheck(req: Request, res: Response) {
    try {
      // Get basic provider statistics
      const result = await ProviderService.getAllProviders(1, 1);

      res.json({
        success: true,
        message: 'Provider service is healthy',
        stats: {
          totalProviders: result.total,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error in provider health check:', error);
      res.status(500).json({
        success: false,
        error: 'Provider service is unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get all providers (admin only)
   */
  static async getProviders(req: Request, res: Response): Promise<Response> {
    try {
      const providers = await ProviderService.getAllProviders();
      return res.json({
        success: true,
        data: providers
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch providers',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get provider by ID
   */
  static async getProviderById(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      const provider = await ProviderService.getProviderById(id);

      if (!provider) {
        return res.status(404).json({
          success: false,
          error: 'Provider not found'
        });
      }

      return res.json({
        success: true,
        data: provider
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch provider',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Create provider profile
   */
  static async createProviderProfile(req: Request, res: Response): Promise<Response> {
    try {
      const providerData = req.body;
      const provider = await ProviderService.createProvider(providerData);

      return res.status(201).json({
        success: true,
        data: provider
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create provider profile',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update provider profile
   */
  static async updateProviderProfile(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const provider = await ProviderService.updateProvider(id, updateData);

      return res.json({
        success: true,
        data: provider
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Failed to update provider profile',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default ProviderController;
