import { Request, Response, NextFunction } from "express";
import SavedVehiclesService from '../services/SavedVehiclesService';

class SavedVehiclesController {
  /**
   * Save a vehicle to user's wishlist
   */
  static async saveVehicle(req: Request, res: Response) {
    try {
      // Basic validation
      if (!req.body.vehicleId) {
        return res.status(400).json({ message: 'Vehicle ID is required' });
      }

      const userId = req.user?.id; // Assuming authenticated user
      const { vehicleId } = req.body;

      // Save vehicle
      const savedVehicle = await SavedVehiclesService.saveVehicle({
        userId,
        vehicleId
      });

      res.status(201).json({
        message: 'Vehicle saved successfully',
        savedVehicle
      });
    } catch (error) {
      // Handle errors
      res.status(error.status || 500).json({
        message: error.message || 'Error saving vehicle',
        error: error.toString()
      });
    }
  }

  /**
   * Remove a vehicle from user's wishlist
   */
  static async removeSavedVehicle(req: Request, res: Response) {
    try {
      const userId = req.user?.id; // Assuming authenticated user
      const { vehicleId } = req.params;

      // Remove saved vehicle
      await SavedVehiclesService.removeSavedVehicle({
        userId,
        vehicleId
      });

      res.status(200).json({
        message: 'Vehicle removed from wishlist'
      });
    } catch (error) {
      // Handle errors
      res.status(error.status || 500).json({
        message: error.message || 'Error removing vehicle',
        error: error.toString()
      });
    }
  }

  /**
   * Get all saved vehicles for a user
   */
  static async getSavedVehicles(req: Request, res: Response) {
    try {
      const userId = req.user?.id; // Assuming authenticated user

      // Get saved vehicles
      const savedVehicles = await SavedVehiclesService.getSavedVehicles(userId);

      res.status(200).json({
        message: 'Saved vehicles retrieved successfully',
        savedVehicles
      });
    } catch (error) {
      // Handle errors
      res.status(error.status || 500).json({
        message: error.message || 'Error retrieving saved vehicles',
        error: error.toString()
      });
    }
  }

  /**
   * Check if a vehicle is saved by a user
   */
  static async isVehicleSaved(req: Request, res: Response) {
    try {
      const userId = req.user?.id; // Assuming authenticated user
      const { vehicleId } = req.params;

      // Check if vehicle is saved
      const isSaved = await SavedVehiclesService.isVehicleSaved(
        userId, 
        vehicleId
      );

      res.status(200).json({
        message: 'Vehicle saved status retrieved',
        isSaved
      });
    } catch (error) {
      // Handle errors
      res.status(error.status || 500).json({
        message: error.message || 'Error checking vehicle saved status',
        error: error.toString()
      });
    }
  }
}

export default SavedVehiclesController; 