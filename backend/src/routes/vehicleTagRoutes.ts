import express, { Request, Response, NextFunction } from "express";
import { VehicleTagService } from '../services/VehicleTagService';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import { PrismaClient } from '@prisma/client';

const router = express.Router();
const prisma = new PrismaClient();
const vehicleTagService = new VehicleTagService(prisma);

// Get predefined tags
router.get('/tags', async (req: Request, res: Response) => {
  try {
    const tags = [
      'Electric',
      'Hybrid',
      'SUV',
      'Sedan',
      'Truck',
      'Van',
      'Luxury',
      'Economy',
      'Sports',
      'Family',
      'Business',
      'Off-road',
      'Convertible',
      'Wagon',
      'Hatchback'
    ];
    res.json(tags);
  } catch (error) {
    res.status(500).json({ error: 'Failed to retrieve tags' });
  }
});

// Add tags to a vehicle (provider only)
router.post('/:vehicleId/tags', 
  authMiddleware, 
  accessControlMiddleware(['PROVIDER']),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { tags } = req.body;

      if (!Array.isArray(tags)) {
        return res.status(400).json({ error: 'Tags must be an array' });
      }

      await vehicleTagService.addTagsToVehicle(vehicleId, tags);
      res.status(200).json({ message: 'Tags added successfully' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
);

// Filter vehicles by tags
router.get('/filter', async (req: Request, res: Response) => {
  try {
    const { tags } = req.query;

    if (!tags || !Array.isArray(tags)) {
      return res.status(400).json({ error: 'Tags must be provided as an array' });
    }

    // Get vehicles that have any of the specified tags
    const vehicles = await prisma.vehicle.findMany({
      where: {
        smartTags: {
          hasSome: tags as string[]
        }
      },
      include: {
        images: true
      }
    });

    res.json(vehicles);
  } catch (error) {
    res.status(500).json({ error: 'Failed to filter vehicles' });
  }
});

// Remove tags from a vehicle (provider only)
router.delete('/:vehicleId/tags', 
  authMiddleware, 
  accessControlMiddleware(['PROVIDER']),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { tags } = req.body;

      if (!Array.isArray(tags)) {
        return res.status(400).json({ error: 'Tags must be an array' });
      }

      // Get current tags and remove specified ones
      const vehicle = await prisma.vehicle.findUnique({
        where: { id: vehicleId },
        select: { smartTags: true }
      });

      if (!vehicle) {
        return res.status(404).json({ error: 'Vehicle not found' });
      }

      const updatedTags = vehicle.smartTags.filter(tag => !tags.includes(tag));

      await prisma.vehicle.update({
        where: { id: vehicleId },
        data: { smartTags: updatedTags }
      });

      res.status(200).json({ message: 'Tags removed successfully' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }
);

export default router;
