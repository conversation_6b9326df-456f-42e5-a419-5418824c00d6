import express from 'express';
import { vehicleListingController } from '../controllers/vehicleListingController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Public routes (no authentication required)
router.get('/search', vehicleListingController.searchVehicles);
router.get('/suggestions', vehicleListingController.getVehicleSuggestions);
router.get('/popular', vehicleListingController.getPopularVehicles);
router.get('/category/:category', vehicleListingController.getVehiclesByCategory);
router.get('/type/:type', vehicleListingController.getVehiclesByType);
router.get('/brands', vehicleListingController.getAllBrands);
router.get('/categories', vehicleListingController.getAllCategories);
router.get('/types', vehicleListingController.getAllTypes);
router.get('/se-asia/:id', vehicleListingController.getVehicleById);
router.get('/se-asia/all', vehicleListingController.getAllSEAsiaVehicles);
router.get('/:id', vehicleListingController.getVehicleListing);

// Protected routes (authentication required)
router.post('/create', authMiddleware, vehicleListingController.createVehicleListing);
router.put('/:id', authMiddleware, vehicleListingController.updateVehicleListing);
router.delete('/:id', authMiddleware, vehicleListingController.deleteVehicleListing);
router.get('/user/listings', authMiddleware, vehicleListingController.getUserVehicleListings);
router.post('/add-manual-vehicle', vehicleListingController.addManualVehicle);

export default router; 