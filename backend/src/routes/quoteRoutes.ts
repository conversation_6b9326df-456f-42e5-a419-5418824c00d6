import express, { Request, Response, NextFunction } from "express";
import QuoteService from '../services/QuoteService';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

router.post('/generate', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { 
      vehicleId, 
      startDate, 
      endDate, 
      selectedAddOns, 
      includeInsurance, 
      discountCode 
    } = req.body;

    // Validate input
    if (!vehicleId || !startDate || !endDate) {
      return res.status(400).json({ 
        error: 'Missing required booking details' 
      });
    }

    const quote = await QuoteService.generateQuote({
      vehicleId,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      selectedAddOns,
      includeInsurance,
      discountCode
    });

    res.json(quote);
  } catch (error) {
    console.error('Quote generation error:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Quote generation failed' 
    });
  }
});

export default router;
