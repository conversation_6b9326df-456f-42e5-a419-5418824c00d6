import express, { Request, Response, NextFunction } from "express";
import RideChecklistController from '../controllers/RideChecklistController';
import { authMiddleware } from '../middleware/authMiddleware';
import multer from 'multer';

const router = express.Router();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 6 // Max 6 files
  },
  fileFilter: (req, file, cb) => {
    // Allow only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Upload ride checklist (start or end)
router.post(
  '/upload', 
  authMiddleware,
  upload.array('images', 6),
  RideChecklistController.uploadRideChecklist
);

// Report vehicle damage
router.post(
  '/damage', 
  authMiddleware,
  upload.array('images', 6),
  RideChecklistController.reportDamage
);

// Get ride checklists for a booking
router.get(
  '/:bookingId', 
  authMiddleware,
  RideChecklistController.getRideChecklists
);

// Get damage reports for a booking
router.get(
  '/:bookingId/damage', 
  authMiddleware,
  RideChecklistController.getDamageReports
);

export default router; 