import express, { Request, Response, NextFunction } from "express";
import { authMiddleware } from '../middleware/authMiddleware';
import { adminRoleMiddleware } from '../middleware/roleMiddleware';
// import PricingAnalyticsService from '../services/PricingAnalyticsService'; // Future implementation
import DemandPredictionService from '../services/DemandPredictionService';
import MachineLearningPricingService from '../services/MachineLearningPricingService';
import { AppError } from '../middleware/errorMiddleware';
import analyticsService from '../services/AnalyticsService';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import DiscountService from '../services/DiscountService';
import { UserRole } from '@prisma/client';

const router = express.Router();

/**
 * Get comprehensive pricing analytics
 * Requires admin authentication
 */
router.get(
    '/pricing-analytics', 
    authMiddleware, 
    adminRoleMiddleware,
    async (req, res, next) => {
        try {
            const { 
                startDate, 
                endDate, 
                vehicleCategories 
            } = req.query;

            const analytics = {
                // Stub: PricingAnalyticsService removed - future implementation
                totalRevenue: 0,
                bookingFeeRevenue: 0,
                discountTotals: {
                    totalDiscountsApplied: 0,
                    discountByType: { percentage: 0, fixed: 0 },
                    topDiscountCodes: []
                },
                pricingTrends: {
                    averageDailyRateByCategory: {},
                    bookingVolumeByPriceRange: []
                },
                seasonalPricing: {
                    monthlyAverageRates: [],
                    peakSeasons: []
                }
            };
            // const analytics = await PricingAnalyticsService.getPricingAnalytics({
            //     startDate: startDate ? new Date(startDate as string) : undefined,
            //     endDate: endDate ? new Date(endDate as string) : undefined,
            //     vehicleCategories: vehicleCategories 
            //         ? (vehicleCategories as string).split(',') 
            //         : undefined
            // });

            res.json(analytics);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * Predict demand for a specific vehicle
 * Requires admin authentication
 */
router.post(
    '/demand-prediction', 
    authMiddleware, 
    adminRoleMiddleware,
    async (req, res, next) => {
        try {
            const demandFeatures = req.body;

            const prediction = await DemandPredictionService.predictDemand(
                demandFeatures
            );

            res.json(prediction);
        } catch (error) {
            next(error);
        }
    }
);

/**
 * Train machine learning models
 * Requires admin authentication
 */
router.post(
    '/train-models', 
    authMiddleware, 
    adminRoleMiddleware,
    async (req, res, next) => {
        try {
            const results = await Promise.all([
                MachineLearningPricingService.trainPricingModel(),
                DemandPredictionService.trainDemandPredictionModel()
            ]);

            res.json({
                message: 'Models trained successfully',
                results
            });
        } catch (error) {
            next(error);
        }
    }
);

/**
 * Create advanced discount code
 * Requires admin authentication
 */
router.post(
    '/discount-codes', 
    authMiddleware, 
    adminRoleMiddleware,
    async (req, res, next) => {
        try {
            const { discountCode, createdBy } = req.body;

            const newDiscountCode = await DiscountService.createDiscountCode({
                code: discountCode, 
                createdBy
            });

            res.status(201).json(newDiscountCode);
        } catch (error) {
            next(error);
        }
    }
);

// Platform Overview (Admin Only)
router.get('/platform/overview', 
  authMiddleware, 
  authMiddleware,
  adminRoleMiddleware,
  async (req: Request, res: Response) => {
    try {
      const days = req.query.days ? parseInt(req.query.days as string) : 30
      const overview = await analyticsService.getPlatformOverview()
      res.json(overview)
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch platform overview' })
    }
  }
)

// Owner Performance (Owner & Admin)
router.get('/owner/performance', 
  authMiddleware, 
  accessControlMiddleware([UserRole.ADMIN, UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const ownerId = req.user.role === 'PROVIDER' ? req.user.id : req.query.ownerId as string
      const days = req.query.days ? parseInt(req.query.days as string) : 30
      const performance = await analyticsService.getOwnerPerformance(ownerId)
      res.json(performance)
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch owner performance' })
    }
  }
)

// Geospatial Vehicle Search
router.get('/vehicles/nearby', 
  authMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { latitude, longitude, radius } = req.query
      
      if (!latitude || !longitude) {
        return res.status(400).json({ error: 'Latitude and longitude are required' })
      }

      const vehicles = await analyticsService.geospatialVehicleSearch(
        parseFloat(latitude as string)
      )

      res.json(vehicles)
    } catch (error) {
      res.status(500).json({ error: 'Failed to perform geospatial search' })
    }
  }
)

// Booking Trends (Admin & Owner)
router.get('/booking-trends', 
  authMiddleware,
  accessControlMiddleware([UserRole.ADMIN, UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const days = req.query.days ? parseInt(req.query.days as string) : 90
      const trends = await analyticsService.getBookingTrends(days)
      res.json(trends)
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch booking trends' })
    }
  }
)

export default router; 