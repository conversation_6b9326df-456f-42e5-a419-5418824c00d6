import express from 'express';
import { AuthController } from '../controllers/AuthController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Auth service is running',
    timestamp: new Date().toISOString()
  });
});

// Public routes
router.post('/register', AuthController.register);
router.post('/login', AuthController.login);

// Password Reset Routes
router.post('/password-reset/request', AuthController.requestPasswordReset);
router.post('/password-reset/confirm', AuthController.resetPassword);

// Email Verification Routes
router.post('/email-verification/request', AuthController.requestEmailVerification);
router.post('/email-verification/verify', AuthController.verifyEmail);

// Protected Routes (require authentication)
router.get('/me', authMiddleware, (req, res) => {
  // Return current user info
  const user = (req as any).user;
  res.json({
    success: true,
    user: {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified,
      status: user.status
    }
  });
});

router.post(
  '/profile/setup',
  authMiddleware,
  AuthController.setupProfile
);

router.post(
  '/mfa/setup', 
  authMiddleware, 
  AuthController.setupMFA
);

router.post(
  '/logout', 
  authMiddleware, 
  (req, res) => {
    // Implement logout logic (e.g., invalidate token)
    res.status(200).json({ message: 'Logged out successfully' });
  }
);

// Example of a route that requires profile completion
router.get(
  '/dashboard', 
  authMiddleware, 
  (req, res) => {
    // Dashboard logic
    res.json({ 
      message: 'Welcome to your dashboard', 
      user: (req as any).user 
    });
  }
);

export default router;
