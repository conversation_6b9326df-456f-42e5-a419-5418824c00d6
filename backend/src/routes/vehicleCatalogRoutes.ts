import express, { Request, Response } from 'express';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Initialize Supabase client with service role key for backend operations
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables for vehicle catalog');
}

const supabase = createClient(supabaseUrl!, supabaseServiceKey!);

// GET /api/vehicle-catalog - Get all vehicles
router.get('/', async (req: Request, res: Response) => {
  try {
    const { limit = 100, offset = 0 } = req.query;

    console.log('📋 Fetching all vehicles from catalog...');

    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('*')
      .range(parseInt(offset as string), parseInt(offset as string) + parseInt(limit as string) - 1)
      .order('make')
      .order('model');

    if (error) {
      console.error('❌ Supabase vehicles error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch vehicles',
        message: error.message
      });
    }

    // Transform data for frontend compatibility
    const vehicles = data?.map(vehicle => ({
      id: vehicle.id,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      engine: vehicle.engine,
      type: vehicle.type,
      category: vehicle.category,
      image_url: vehicle.image_url,
      source: vehicle.source,
      daily_rate: vehicle.daily_rate,
      rating: vehicle.rating,
      reviews: vehicle.reviews,
      features: vehicle.features,
      available_units: vehicle.available_units,
      has_insurance: vehicle.has_insurance,
      insurance_price: vehicle.insurance_price,
      location_city: vehicle.location_city,
      is_reference_only: vehicle.is_reference_only,
      created_at: vehicle.created_at,
      updated_at: vehicle.updated_at,
      displayName: `${vehicle.make} ${vehicle.model}`,
      searchTerms: `${vehicle.make} ${vehicle.model} ${vehicle.engine || ''} ${vehicle.category || ''}`.toLowerCase()
    })) || [];

    console.log(`✅ Found ${vehicles.length} vehicles in catalog`);

    res.json({
      success: true,
      data: vehicles,
      count: vehicles.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Vehicle catalog fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vehicles',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/vehicle-catalog/search - Search vehicles for autocomplete
router.get('/search', async (req: Request, res: Response) => {
  try {
    const { query, limit = 15 } = req.query;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Query parameter is required',
        message: 'Please provide a search query'
      });
    }

    console.log(`🔍 Searching vehicle catalog for: "${query}"`);

    // Search in Supabase vehicle_catalog table
    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('make, model, category, image_url, source')
      .or(`make.ilike.%${query}%,model.ilike.%${query}%`)
      .limit(parseInt(limit as string))
      .order('make')
      .order('model');

    if (error) {
      console.error('❌ Supabase search error:', error);
      return res.status(500).json({
        success: false,
        error: 'Database search failed',
        message: error.message
      });
    }

    // Transform data for frontend
    const results = data?.map(vehicle => ({
      id: `${vehicle.make}-${vehicle.model}`,
      make: vehicle.make,
      model: vehicle.model,
      category: vehicle.category,
      image_url: vehicle.image_url,
      source: vehicle.source,
      displayName: `${vehicle.make} ${vehicle.model}`,
      label: `${vehicle.make} ${vehicle.model} (${vehicle.category})`
    })) || [];

    console.log(`✅ Found ${results.length} vehicles matching "${query}"`);

    res.json({
      success: true,
      data: results,
      query: query,
      count: results.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Vehicle catalog search error:', error);
    res.status(500).json({
      success: false,
      error: 'Search failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/vehicle-catalog/categories - Get all available categories
router.get('/categories', async (req: Request, res: Response) => {
  try {
    console.log('📋 Fetching vehicle categories...');

    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      console.error('❌ Supabase categories error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch categories',
        message: error.message
      });
    }

    // Get unique categories
    const categories = [...new Set(data?.map(item => item.category).filter(Boolean))].sort();

    console.log(`✅ Found ${categories.length} unique categories`);

    res.json({
      success: true,
      data: categories,
      count: categories.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Categories fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch categories',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/vehicle-catalog/makes - Get all available makes
router.get('/makes', async (req: Request, res: Response) => {
  try {
    console.log('🏭 Fetching vehicle makes...');

    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('make')
      .not('make', 'is', null);

    if (error) {
      console.error('❌ Supabase makes error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch makes',
        message: error.message
      });
    }

    // Get unique makes
    const makes = [...new Set(data?.map(item => item.make).filter(Boolean))].sort();

    console.log(`✅ Found ${makes.length} unique makes`);

    res.json({
      success: true,
      data: makes,
      count: makes.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Makes fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch makes',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/vehicle-catalog/:make/:model - Get specific vehicle details
router.get('/:make/:model', async (req: Request, res: Response) => {
  try {
    const { make, model } = req.params;
    
    // Validate parameters
    if (!make || make === 'undefined' || !model || model === 'undefined') {
      return res.status(400).json({
        success: false,
        error: 'Invalid make or model parameter',
        message: 'Make and model parameters are required'
      });
    }
    
    console.log(`🔍 Fetching vehicle details for: ${make} ${model}`);

    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('*')
      .eq('make', make)
      .eq('model', model)
      .single();

    if (error) {
      console.error('❌ Supabase vehicle details error:', error);
      return res.status(404).json({
        success: false,
        error: 'Vehicle not found',
        message: `No vehicle found for ${make} ${model}`
      });
    }

    console.log(`✅ Found vehicle: ${data.make} ${data.model}`);

    res.json({
      success: true,
      data: {
        id: data.id,
        make: data.make,
        model: data.model,
        category: data.category,
        image_url: data.image_url,
        source: data.source,
        displayName: `${data.make} ${data.model}`,
        label: `${data.make} ${data.model} (${data.category})`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Vehicle details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vehicle details',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router; 