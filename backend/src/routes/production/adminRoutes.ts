// =============================================================================
// ADMIN ROUTES
// =============================================================================

import { Router } from 'express';
import ProductionAuthService from '../../services/ProductionAuthService';

const router = Router();

router.get('/dashboard', ProductionAuthService.authenticateToken, ProductionAuthService.requireAdmin, (req, res) => {
  res.json({ success: true, message: 'Admin dashboard coming soon' });
});

export default router;
