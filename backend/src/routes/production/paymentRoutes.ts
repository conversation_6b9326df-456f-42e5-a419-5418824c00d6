// =============================================================================
// PAYMENT ROUTES
// =============================================================================

import { Router, Request, Response } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';
import ProductionStripeService from '../../services/ProductionStripeService';

const router = Router();

/**
 * POST /api/payments/create-intent
 * Create a payment intent for a booking
 */
router.post('/create-intent', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { booking_id } = req.body;

    if (!booking_id) {
      return res.status(400).json({ success: false, error: 'Booking ID is required' });
    }

    const result = await ProductionStripeService.createPaymentIntent(booking_id);

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      data: {
        client_secret: result.clientSecret
      }
    });
  } catch (error) {
    console.error('❌ Create payment intent error:', error);
    res.status(500).json({ success: false, error: 'Failed to create payment intent' });
  }
});

/**
 * POST /api/payments/confirm
 * Confirm a payment
 */
router.post('/confirm', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { payment_intent_id } = req.body;

    if (!payment_intent_id) {
      return res.status(400).json({ success: false, error: 'Payment intent ID is required' });
    }

    const result = await ProductionStripeService.confirmPayment(payment_intent_id);

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      message: 'Payment confirmed successfully'
    });
  } catch (error) {
    console.error('❌ Confirm payment error:', error);
    res.status(500).json({ success: false, error: 'Failed to confirm payment' });
  }
});

/**
 * POST /api/payments/webhook
 * Handle Stripe webhooks
 */
router.post('/webhook', async (req: Request, res: Response) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    const body = req.body;

    if (!signature) {
      return res.status(400).json({ success: false, error: 'Missing stripe signature' });
    }

    const result = await ProductionStripeService.handleWebhook(body, signature);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('❌ Webhook error:', error);
    res.status(500).json({ success: false, error: 'Webhook handling failed' });
  }
});

export default router;
