// =============================================================================
// BOOKING ROUTES
// =============================================================================
// Routes for booking management and workflow

import { Router, Request, Response } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';
import DatabaseService from '../../services/DatabaseService';
import { BookingStatus, UserRole } from '../../models';

const router = Router();

// =============================================================================
// PROTECTED ROUTES (Authentication required)
// =============================================================================

/**
 * GET /api/bookings
 * Get user's bookings
 */
router.get('/', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { page = 1, limit = 20, status } = req.query;
    
    const params = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      userId: req.user.role === UserRole.CUSTOMER ? req.user.id : undefined,
      providerId: req.user.role === UserRole.PROVIDER ? req.user.id : undefined,
      status: status as string
    };

    const result = await DatabaseService.getBookings(params);

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });
  } catch (error) {
    console.error('❌ Get bookings error:', error);
    res.status(500).json({ success: false, error: 'Failed to get bookings' });
  }
});

/**
 * POST /api/bookings
 * Create a new booking
 */
router.post('/', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { vehicle_id, start_date, end_date, start_time, end_time, pickup_location, dropoff_location, special_requests } = req.body;

    // Validate required fields
    if (!vehicle_id || !start_date || !end_date) {
      return res.status(400).json({
        success: false,
        error: 'Vehicle ID, start date, and end date are required'
      });
    }

    // Get vehicle details
    const vehicle = await DatabaseService.getVehicle(vehicle_id);
    if (!vehicle) {
      return res.status(404).json({ success: false, error: 'Vehicle not found' });
    }

    // Calculate total amount (simplified)
    const days = Math.ceil((new Date(end_date).getTime() - new Date(start_date).getTime()) / (1000 * 60 * 60 * 24));
    const baseAmount = vehicle.daily_rate * days;
    const taxAmount = baseAmount * 0.1; // 10% tax
    const serviceFee = baseAmount * 0.05; // 5% service fee
    const totalAmount = baseAmount + taxAmount + serviceFee;

    const bookingData = {
      userId: req.user.id, // Use camelCase field names
      providerId: vehicle.provider_id, // Use correct field name from Vehicle model
      vehicleId: vehicle_id,
      startDate: new Date(start_date),
      endDate: new Date(end_date),
      // start_time, end_time, pickup_location, dropoff_location, special_requests not in Booking model
      status: BookingStatus.PENDING,
      totalPrice: totalAmount,
      // base_amount, tax_amount, service_fee not in Booking model
      paymentStatus: 'PENDING' as const, // Use correct enum value
      // created_at, updated_at are auto-generated
    };

    const booking = await DatabaseService.createBooking(bookingData);

    if (!booking) {
      return res.status(500).json({ success: false, error: 'Failed to create booking' });
    }

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: booking
    });
  } catch (error) {
    console.error('❌ Create booking error:', error);
    res.status(500).json({ success: false, error: 'Failed to create booking' });
  }
});

/**
 * PUT /api/bookings/:id/status
 * Update booking status
 */
router.put('/:id/status', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { id } = req.params;
    const { status, cancellation_reason } = req.body;

    // Validate status
    if (!Object.values(BookingStatus).includes(status)) {
      return res.status(400).json({ success: false, error: 'Invalid booking status' });
    }

    const updates: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (status === BookingStatus.CANCELLED) {
      updates.cancellation_reason = cancellation_reason;
      updates.cancelled_by = req.user.id;
      updates.cancelled_at = new Date().toISOString();
    }

    const updatedBooking = await DatabaseService.updateBooking(id, updates);

    if (!updatedBooking) {
      return res.status(500).json({ success: false, error: 'Failed to update booking' });
    }

    res.json({
      success: true,
      message: 'Booking status updated successfully',
      data: updatedBooking
    });
  } catch (error) {
    console.error('❌ Update booking status error:', error);
    res.status(500).json({ success: false, error: 'Failed to update booking status' });
  }
});

export default router;
