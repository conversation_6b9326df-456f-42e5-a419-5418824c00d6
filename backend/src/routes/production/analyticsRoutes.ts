// =============================================================================
// ANALYTICS ROUTES
// =============================================================================

import { Router, Response } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';
import ProductionAnalyticsService from '../../services/ProductionAnalyticsService';

const router = Router();

/**
 * GET /api/analytics/dashboard
 * Get analytics dashboard data
 */
router.get('/dashboard', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { startDate, endDate } = req.query;
    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;

    const analytics = await ProductionAnalyticsService.getDashboardAnalytics(
      providerId,
      startDate as string,
      endDate as string
    );

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('❌ Analytics dashboard error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch analytics' });
  }
});

/**
 * GET /api/analytics/cashflow
 * Get cashflow data
 */
router.get('/cashflow', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const { days = 30 } = req.query;
    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;

    const cashflowData = await ProductionAnalyticsService.getCashflowData(
      providerId,
      parseInt(days as string)
    );

    res.json({
      success: true,
      data: cashflowData
    });
  } catch (error) {
    console.error('❌ Cashflow analytics error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch cashflow data' });
  }
});

/**
 * GET /api/analytics/vehicles
 * Get vehicle performance analytics
 */
router.get('/vehicles', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;
    const vehiclePerformance = await ProductionAnalyticsService.getVehiclePerformance(providerId);

    res.json({
      success: true,
      data: vehiclePerformance
    });
  } catch (error) {
    console.error('❌ Vehicle analytics error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch vehicle analytics' });
  }
});

/**
 * GET /api/analytics/customers
 * Get customer analytics
 */
router.get('/customers', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;
    const customerAnalytics = await ProductionAnalyticsService.getCustomerAnalytics(providerId);

    res.json({
      success: true,
      data: customerAnalytics
    });
  } catch (error) {
    console.error('❌ Customer analytics error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch customer analytics' });
  }
});

/**
 * GET /api/analytics/bookings
 * Get booking analytics
 */
router.get('/bookings', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;
    const bookingAnalytics = await ProductionAnalyticsService.getBookingAnalytics(providerId);

    res.json({
      success: true,
      data: bookingAnalytics
    });
  } catch (error) {
    console.error('❌ Booking analytics error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch booking analytics' });
  }
});

/**
 * GET /api/analytics/report
 * Get comprehensive analytics report
 */
router.get('/report', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }

    const providerId = req.user.role === 'PROVIDER' ? req.user.id : undefined;
    const report = await ProductionAnalyticsService.getComprehensiveReport(providerId);

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('❌ Comprehensive report error:', error);
    res.status(500).json({ success: false, error: 'Failed to generate report' });
  }
});

export default router;
