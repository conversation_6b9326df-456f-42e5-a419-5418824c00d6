// =============================================================================
// VEHICLE ROUTES
// =============================================================================
// Routes for vehicle management, search, and availability

import { Router, Request, Response } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';
import DatabaseService from '../../services/DatabaseService';
import { VehicleCategory, UserRole } from '../../models';

const router = Router();

// =============================================================================
// PUBLIC ROUTES (No authentication required)
// =============================================================================

/**
 * GET /api/vehicles
 * Search and filter vehicles
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      city,
      minPrice,
      maxPrice,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const searchParams = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      category: category as string,
      city: city as string,
      minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
      maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined,
      startDate: startDate as string,
      endDate: endDate as string,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc'
    };

    const result = await DatabaseService.getVehicles(searchParams);

    if (!result.success) {
      return res.status(500).json(result);
    }

    res.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      metadata: {
        searchParams,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('❌ Vehicle search error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search vehicles'
    });
  }
});

/**
 * GET /api/vehicles/categories
 * Get available vehicle categories
 */
router.get('/categories', (req: Request, res: Response) => {
  const categories = Object.values(VehicleCategory).map(category => ({
    value: category,
    label: category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    description: getCategoryDescription(category)
  }));

  res.json({
    success: true,
    data: categories
  });
});

/**
 * GET /api/vehicles/price-ranges
 * Get price ranges for different categories
 */
router.get('/price-ranges', async (req: Request, res: Response) => {
  try {
    // This would typically involve complex aggregation queries
    // For now, return mock data based on current market conditions
    const priceRanges = {
      small_scooter: {
        min: 45000,
        max: 85000,
        average: 62000,
        count: 25
      },
      large_scooter: {
        min: 75000,
        max: 150000,
        average: 110000,
        count: 18
      },
      luxury_bike: {
        min: 180000,
        max: 400000,
        average: 275000,
        count: 12
      },
      car: {
        min: 200000,
        max: 800000,
        average: 450000,
        count: 8
      },
      motorcycle: {
        min: 120000,
        max: 350000,
        average: 220000,
        count: 15
      }
    };

    res.json({
      success: true,
      data: priceRanges,
      metadata: {
        currency: 'IDR',
        period: 'daily',
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('❌ Price ranges error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get price ranges'
    });
  }
});

/**
 * GET /api/vehicles/:id
 * Get vehicle details by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const vehicle = await DatabaseService.getVehicle(id);

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        error: 'Vehicle not found'
      });
    }

    res.json({
      success: true,
      data: vehicle
    });
  } catch (error) {
    console.error('❌ Get vehicle error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vehicle'
    });
  }
});

// =============================================================================
// PROTECTED ROUTES (Authentication required)
// =============================================================================

/**
 * POST /api/vehicles
 * Create a new vehicle (Provider only)
 */
router.post('/', 
  ProductionAuthService.authenticateToken,
  ProductionAuthService.requireProvider,
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'User not found'
        });
      }

      const vehicleData = {
        ...req.body,
        provider_id: req.user.id,
        status: 'available',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Validate required fields
      const requiredFields = ['make', 'model', 'year', 'category', 'daily_rate', 'location_city'];
      for (const field of requiredFields) {
        if (!vehicleData[field]) {
          return res.status(400).json({
            success: false,
            error: `${field} is required`
          });
        }
      }

      const vehicle = await DatabaseService.createVehicle(vehicleData);

      if (!vehicle) {
        return res.status(500).json({
          success: false,
          error: 'Failed to create vehicle'
        });
      }

      res.status(201).json({
        success: true,
        message: 'Vehicle created successfully',
        data: vehicle
      });
    } catch (error) {
      console.error('❌ Create vehicle error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create vehicle'
      });
    }
  }
);

/**
 * PUT /api/vehicles/:id
 * Update vehicle (Provider only - own vehicles)
 */
router.put('/:id',
  ProductionAuthService.authenticateToken,
  ProductionAuthService.requireProvider,
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'User not found'
        });
      }

      const { id } = req.params;

      // Check if vehicle exists and belongs to the provider
      const existingVehicle = await DatabaseService.getVehicle(id);
      if (!existingVehicle) {
        return res.status(404).json({
          success: false,
          error: 'Vehicle not found'
        });
      }

      if (existingVehicle.provider_id !== req.user.id && req.user.role !== UserRole.ADMIN) {
        return res.status(403).json({
          success: false,
          error: 'You can only update your own vehicles'
        });
      }

      const updates = {
        ...req.body,
        updated_at: new Date().toISOString()
      };

      // Remove fields that shouldn't be updated
      delete updates.id;
      delete updates.provider_id;
      delete updates.created_at;

      const updatedVehicle = await DatabaseService.updateVehicle(id, updates);

      if (!updatedVehicle) {
        return res.status(500).json({
          success: false,
          error: 'Failed to update vehicle'
        });
      }

      res.json({
        success: true,
        message: 'Vehicle updated successfully',
        data: updatedVehicle
      });
    } catch (error) {
      console.error('❌ Update vehicle error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update vehicle'
      });
    }
  }
);

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

function getCategoryDescription(category: VehicleCategory): string {
  const descriptions = {
    [VehicleCategory.SMALL_SCOOTER]: 'Perfect for city commuting, fuel-efficient and easy to park',
    [VehicleCategory.LARGE_SCOOTER]: 'More power and comfort for longer rides',
    [VehicleCategory.LUXURY_BIKE]: 'Premium motorcycles for enthusiasts',
    [VehicleCategory.CAR]: 'Four-wheel comfort and convenience',
    [VehicleCategory.MOTORCYCLE]: 'Powerful bikes for adventure and touring'
  };

  return descriptions[category] || 'Vehicle for rent';
}

export default router;
