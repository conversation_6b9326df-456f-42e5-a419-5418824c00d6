// =============================================================================
// USER ROUTES
// =============================================================================

import { Router } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';

const router = Router();

router.get('/profile', ProductionAuthService.authenticateToken, (req: AuthenticatedRequest, res) => {
  res.json({ success: true, data: { user: req.user } });
});

export default router;
