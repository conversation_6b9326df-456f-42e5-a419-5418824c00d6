// =============================================================================
// AUTHENTICATION ROUTES
// =============================================================================
// Routes for user authentication, registration, and session management

import { Router, Request, Response } from 'express';
import ProductionAuthService, { AuthenticatedRequest } from '../../services/ProductionAuthService';
import DatabaseService from '../../services/DatabaseService';
import { UserRole } from '../../models';

const router = Router();

// =============================================================================
// PUBLIC ROUTES (No authentication required)
// =============================================================================

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', async (req: Request, res: Response) => {
  try {
    console.log('🔍 Registration attempt started');
    const { email, password, first_name, last_name, phone, role } = req.body;
    console.log('📋 Registration fields:', { email, first_name, last_name, role, hasPassword: !!password });

    // Validate required fields
    if (!email || !password) {
      console.log('❌ Missing required fields:', { email: !!email, password: !!password });
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 6 characters long'
      });
    }

    // Validate role if provided
    if (role && !Object.values(UserRole).includes(role)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid user role'
      });
    }

    console.log('🔄 Calling ProductionAuthService.register...');
    const result = await ProductionAuthService.register({
      email,
      password,
      first_name,
      last_name,
      phone,
      role: role || UserRole.CUSTOMER
    });

    console.log('📤 ProductionAuthService result:', { success: result.success, error: result.error });

    if (!result.success) {
      console.log('❌ Registration failed:', result.error);
      return res.status(400).json(result);
    }

    console.log('✅ Registration successful, sending response');
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: result.user,
        token: result.token
      }
    });
  } catch (error) {
    console.error('❌ Registration route error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    });
  }
});

/**
 * POST /api/auth/login
 * Authenticate user and return JWT token
 */
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    const result = await ProductionAuthService.login(email, password);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: result.user,
        token: result.token
      }
    });
  } catch (error) {
    console.error('❌ Login route error:', error);
    res.status(500).json({
      success: false,
      error: 'Login failed'
    });
  }
});

/**
 * POST /api/auth/logout
 * Logout user (client-side token removal)
 */
router.post('/logout', (req: Request, res: Response) => {
  // Since we're using stateless JWT tokens, logout is handled client-side
  // by removing the token from storage
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

// =============================================================================
// PROTECTED ROUTES (Authentication required)
// =============================================================================

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not found'
      });
    }

    // Remove sensitive data
    const { password_hash, ...userWithoutPassword } = req.user as any;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword
      }
    });
  } catch (error) {
    console.error('❌ Get current user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile'
    });
  }
});

/**
 * PUT /api/auth/profile
 * Update current user profile
 */
router.put('/profile', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not found'
      });
    }

    const { first_name, last_name, phone, address, city, postal_code, country } = req.body;

    // Update user profile (excluding sensitive fields)
    const updatedUser = await DatabaseService.updateUser(req.user.id, {
      first_name,
      last_name,
      phone,
      address,
      city,
      postal_code,
      country
    });

    if (!updatedUser) {
      return res.status(500).json({
        success: false,
        error: 'Failed to update profile'
      });
    }

    // Remove sensitive data
    const { password_hash, ...userWithoutPassword } = updatedUser as any;

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: userWithoutPassword
      }
    });
  } catch (error) {
    console.error('❌ Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile'
    });
  }
});

/**
 * POST /api/auth/change-password
 * Change user password
 */
router.post('/change-password', ProductionAuthService.authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not found'
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required'
      });
    }

    // Validate new password strength
    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'New password must be at least 6 characters long'
      });
    }

    const result = await ProductionAuthService.changePassword(req.user.id, currentPassword, newPassword);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('❌ Change password error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to change password'
    });
  }
});

/**
 * POST /api/auth/verify-token
 * Verify if token is valid
 */
router.post('/verify-token', ProductionAuthService.authenticateToken, (req: AuthenticatedRequest, res: Response) => {
  res.json({
    success: true,
    message: 'Token is valid',
    data: {
      user: req.user
    }
  });
});

export default router;
