import express, { Request, Response, NextFunction } from "express";
import cors from 'cors'
import { BookingController } from '../controllers/BookingController'
import { PaymentController } from '../controllers/PaymentController'
import * as VehicleController from '../controllers/VehicleController'
import bookingRoutes from './bookingRoutes'
import vehicleRoutes from './vehicleRoutes'
import providerRoutes from './providerRoutes'
import userRoutes from './userRoutes'
import healthRoutes from './healthRoutes'
import vehicleDataRoutes from './vehicleDataRoutes'
import vehicleCatalogRoutes from './vehicleCatalogRoutes'
import authProxyRoutes from './authProxyRoutes'
import savedVehiclesRoutes from './savedVehiclesRoutes'

const router = express.Router()

// Mount routes
router.use('/bookings', bookingRoutes)
router.use('/vehicles', vehicleRoutes)
router.use('/providers', providerRoutes)
router.use('/users', userRoutes)
router.use('/', healthRoutes)
router.use('/vehicle-data', vehicleDataRoutes)
router.use('/vehicle-catalog', vehicleCatalogRoutes)
router.use('/auth-proxy', authProxyRoutes)
router.use('/saved-vehicles', savedVehiclesRoutes)

// Vehicle availability check
router.get('/vehicles/availability/check', async (req, res, next) => {
  try {
    const { vehicleId, startDate, endDate } = req.query;
    const result = await BookingController.checkVehicleAvailability(
      vehicleId as string, 
      startDate as string, 
      endDate as string
    );
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Booking routes
router.post('/bookings', BookingController.createBooking)
router.get('/bookings/:id', BookingController.getBookingById)
router.get('/users/:userId/bookings', BookingController.getUserBookings)
router.get('/providers/:providerId/bookings', BookingController.getUserBookings) // Use the same method for now
router.patch('/bookings/:id/status', BookingController.updateBooking)
router.post('/bookings/confirm-payment', BookingController.createBooking) // Use createBooking for now
router.patch('/bookings/:id/cancel', BookingController.cancelBooking)

// Payment routes
router.post('/payments/create-payment-intent', PaymentController.createPaymentIntent)
router.post('/payments/create-checkout-session', PaymentController.createCheckoutSession)
router.post('/payments/confirm-payment', PaymentController.confirmPayment)
router.get('/payments/checkout-session/:sessionId', PaymentController.getCheckoutSession)
router.get('/payments/currency/:currency/symbol', PaymentController.getCurrencySymbol)
router.post('/payments/convert-price', PaymentController.convertPrice)

// Stripe Connect routes for providers
router.post('/providers/connect/create-account', PaymentController.createConnectAccount)
router.post('/providers/connect/create-account-link', PaymentController.createAccountLink)

// Provider routes are handled by providerRoutes middleware above

export default router 