import express from 'express';
import { Request, Response, NextFunction } from "express";
import { FleetManagementService } from '../services/FleetManagementService';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import { UserRole } from '@prisma/client';

const router = express.Router();
const fleetService = new FleetManagementService();

// Get predefined smart tags
router.get('/tags', async (req: Request, res: Response) => {
  try {
    const tags = await fleetService.getPredefinedTags();
    res.json(tags);
  } catch (error) {
    res.status(500).json({ error: 'Failed to retrieve tags' });
  }
});

// Add tags to a vehicle (provider only)
router.post('/:vehicleId/tags', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { tags } = req.body;

      if (!Array.isArray(tags)) {
        return res.status(400).json({ error: 'Tags must be an array' });
      }

      await fleetService.addTagsToVehicle(vehicleId, tags);
      res.json({ message: 'Tags added successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Remove tags from a vehicle (provider only)
router.delete('/:vehicleId/tags', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { tags } = req.body;

      if (!Array.isArray(tags)) {
        return res.status(400).json({ error: 'Tags must be an array' });
      }

      await fleetService.removeTagsFromVehicle(vehicleId, tags);
      res.json({ message: 'Tags removed successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Search vehicles by tags
router.get('/search', async (req: Request, res: Response) => {
  try {
    const { tags } = req.query;

    if (!tags || !Array.isArray(tags)) {
      return res.status(400).json({ error: 'Tags must be provided as an array' });
    }

    const vehicles = await fleetService.getVehiclesByTags(tags as string[]);
    res.json(vehicles);
  } catch (error) {
    res.status(500).json({ error: 'Failed to search vehicles' });
  }
});

// Update vehicle pricing strategy (provider only)
router.put('/:vehicleId/pricing', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { strategy, minRate, maxRate } = req.body;

      await fleetService.updatePricingStrategy(vehicleId, strategy, minRate, maxRate);
      res.json({ message: 'Pricing strategy updated successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Block vehicle for maintenance (provider only)
router.post('/:vehicleId/maintenance', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { startDate, endDate, reason } = req.body;

      await fleetService.blockVehicleForMaintenance(
        vehicleId, 
        new Date(startDate), 
        new Date(endDate), 
        reason
      );
      res.json({ message: 'Vehicle blocked for maintenance successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Get vehicle performance metrics
router.get('/:vehicleId/performance', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER, UserRole.ADMIN]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { startDate, endDate } = req.query;

      const performance = await fleetService.calculateVehiclePerformance(
        vehicleId, 
        startDate ? new Date(startDate as string) : undefined,
        endDate ? new Date(endDate as string) : undefined
      );
      res.json(performance);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

// Generate earnings projection
router.get('/:vehicleId/earnings-projection', 
  authMiddleware, 
  accessControlMiddleware([UserRole.PROVIDER]),
  async (req: Request, res: Response) => {
    try {
      const { vehicleId } = req.params;
      const { months } = req.query;

      const projection = await fleetService.generateEarningsProjection(
        vehicleId, 
        months ? parseInt(months as string) : 12
      );
      res.json(projection);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
});

export default router;
