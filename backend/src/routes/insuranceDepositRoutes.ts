import express, { Request, Response, NextFunction } from "express";
import { InsuranceDepositService } from '../services/InsuranceDepositService';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();
const insuranceDepositService = new InsuranceDepositService();

// Create insurance configuration
router.post('/insurance', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { vehicleId, insuranceConfig } = req.body;
    
    // Validate input
    if (!vehicleId || !insuranceConfig) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const configuration = await insuranceDepositService.createInsuranceConfiguration(
      vehicleId, 
      insuranceConfig
    );

    res.status(201).json(configuration);
  } catch (error) {
    console.error('Error creating insurance configuration:', error);
    res.status(500).json({ error: 'Failed to create insurance configuration' });
  }
});

// Create deposit configuration
router.post('/deposit', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { vehicleId, depositConfig } = req.body;
    
    // Validate input
    if (!vehicleId || !depositConfig) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const configuration = await insuranceDepositService.createDepositConfiguration(
      vehicleId, 
      depositConfig
    );

    res.status(201).json(configuration);
  } catch (error) {
    console.error('Error creating deposit configuration:', error);
    res.status(500).json({ error: 'Failed to create deposit configuration' });
  }
});

// Get insurance configuration for a vehicle
router.get('/insurance/:vehicleId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { vehicleId } = req.params;
    
    const configuration = await insuranceDepositService.getInsuranceConfigurationForVehicle(vehicleId);

    if (!configuration) {
      return res.status(404).json({ error: 'Insurance configuration not found' });
    }

    res.json(configuration);
  } catch (error) {
    console.error('Error retrieving insurance configuration:', error);
    res.status(500).json({ error: 'Failed to retrieve insurance configuration' });
  }
});

// Get deposit configuration for a vehicle
router.get('/deposit/:vehicleId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { vehicleId } = req.params;
    
    const configuration = await insuranceDepositService.getDepositConfigurationForVehicle(vehicleId);

    if (!configuration) {
      return res.status(404).json({ error: 'Deposit configuration not found' });
    }

    res.json(configuration);
  } catch (error) {
    console.error('Error retrieving deposit configuration:', error);
    res.status(500).json({ error: 'Failed to retrieve deposit configuration' });
  }
});

// Generate insurance and deposit report
router.get('/report', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Start and end dates are required' });
    }

    const report = await insuranceDepositService.generateInsuranceDepositReport(
      new Date(startDate as string), 
      new Date(endDate as string)
    );

    res.json(report);
  } catch (error) {
    console.error('Error generating insurance deposit report:', error);
    res.status(500).json({ error: 'Failed to generate report' });
  }
});

export default router; 