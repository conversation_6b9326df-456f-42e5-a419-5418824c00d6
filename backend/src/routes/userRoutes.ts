import { Router, Request, Response, NextFunction } from 'express';
import UserService from '../services/UserService';
import AuthService from '../services/AuthService';

const router = Router();
const userService = new UserService();
const authService = new AuthService();

// User Registration
router.post('/register', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userData = req.body;
    const newUser = await userService.createUser(userData);
    res.status(201).json({
      success: true,
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name
      }
    });
  } catch (error) {
    next(error);
  }
});

// User Login
router.post('/login', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password } = req.body;
    const user = await userService.findByEmail(email);
    
    if (!user) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }
    
    // Use bcrypt directly since verifyPassword is private
    const bcrypt = require('bcrypt');
    const isValidPassword = await bcrypt.compare(password, user.password || '');
    if (!isValidPassword) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    // Generate JWT token using AuthService
    const loginResult = await authService.login(email, password);
    if (!loginResult.success || !loginResult.token) {
      return res.status(401).json({ success: false, message: 'Failed to generate token' });
    }
    const token = loginResult.token;
    
    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name
      },
      token
    });
  } catch (error) {
    next(error);
  }
});

// Get User Profile
router.get('/profile/:userId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params;
    const profile = await userService.getUserProfile(userId);
    res.json({
      success: true,
      profile
    });
  } catch (error) {
    next(error);
  }
});

// Update User Profile
router.put('/profile/:userId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;
    const updatedProfile = await userService.updateUser(userId, updateData);
    res.json({
      success: true,
      profile: updatedProfile
    });
  } catch (error) {
    next(error);
  }
});

export default router; 