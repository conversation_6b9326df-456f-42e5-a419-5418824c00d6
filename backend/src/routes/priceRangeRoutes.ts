import express, { Request, Response } from 'express';
import { asyncHand<PERSON> } from '../utils/asyncHandler';
import PriceRangeService from '../services/PriceRangeService';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * GET /api/vehicles/price-ranges
 * Get dynamic price ranges by vehicle category
 */
router.get('/price-ranges', asyncHandler(async (req: Request, res: Response) => {
  try {
    logger.info('📊 Price ranges request received');

    // Get base price ranges from database
    const baseRanges = await PriceRangeService.getPriceRangesByCategory();
    
    // Apply dynamic pricing adjustments
    const dynamicRanges = await PriceRangeService.applyDynamicPricing(baseRanges);
    
    // Calculate overall market multiplier for metadata
    const sampleMultiplier = dynamicRanges.small_scooter.avgPrice / baseRanges.small_scooter.avgPrice;
    
    res.json({
      success: true,
      data: dynamicRanges,
      metadata: {
        lastUpdated: new Date().toISOString(),
        demandMultiplier: sampleMultiplier.toFixed(2),
        marketConditions: PriceRangeService.getMarketConditions(sampleMultiplier),
        totalVehicles: Object.values(dynamicRanges).reduce((sum, range) => sum + range.count, 0)
      }
    });

    logger.info('✅ Price ranges sent successfully');

  } catch (error) {
    logger.error('❌ Error fetching price ranges:', error);
    
    // Send fallback data
    res.json({
      success: true,
      data: {
        small_scooter: { min: 50000, max: 80000, count: 0, avgPrice: 65000 },
        large_scooter: { min: 80000, max: 120000, count: 0, avgPrice: 100000 },
        luxury_bike: { min: 200000, max: 350000, count: 0, avgPrice: 275000 }
      },
      metadata: {
        lastUpdated: new Date().toISOString(),
        demandMultiplier: '1.00',
        marketConditions: 'Normal',
        totalVehicles: 0,
        fallback: true
      }
    });
  }
}));

/**
 * GET /api/vehicles/price-ranges/:category
 * Get price range for specific category
 */
router.get('/price-ranges/:category', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    
    logger.info(`📊 Price range request for category: ${category}`);

    if (!['small_scooter', 'large_scooter', 'luxury_bike'].includes(category)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid category. Must be one of: small_scooter, large_scooter, luxury_bike'
      });
    }

    const allRanges = await PriceRangeService.getPriceRangesByCategory();
    const dynamicRanges = await PriceRangeService.applyDynamicPricing(allRanges);
    
    const categoryRange = dynamicRanges[category as keyof typeof dynamicRanges];

    res.json({
      success: true,
      data: {
        category,
        ...categoryRange
      },
      metadata: {
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error(`❌ Error fetching price range for category ${req.params.category}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch price range'
    });
  }
}));

export default router;
