import express, { Request, Response, NextFunction } from 'express';
import { createClient } from '@supabase/supabase-js';
import prisma from '../lib/database';
import { supabase } from '../lib/supabase';
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  validationErrorResponse,
  serverErrorResponse,
  paginatedResponse
} from '../utils/apiResponse';
import { VALIDATION_PATTERNS } from '../utils/apiStandards';

const router = express.Router();

// Using centralized Supabase client from lib/supabase.ts

const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) =>
  (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };

// Helper function to validate UUID v4
function isValidUUID(uuid: string) {
  return VALIDATION_PATTERNS.ID.test(uuid);
}

// GET /api/vehicles/suggestions - Get vehicle suggestions from Supabase catalog
router.get('/suggestions', asyncHandler(async (req: Request, res: Response) => {
  const { query, limit = 20 } = req.query;
  
  try {
    console.log(`🔍 Backend: Searching vehicles for query: "${query}"`);
    
    let supabaseQuery = supabase
      .from('vehicles')
      .select('*')
      .eq('active', true)
      .limit(parseInt(limit as string));

    // Add search filter if query provided
    if (query && typeof query === 'string' && query !== 'undefined') {
      supabaseQuery = supabaseQuery.or(`make.ilike.%${query}%,model.ilike.%${query}%`);
    }

    const { data, error } = await supabaseQuery;

    if (error) {
      console.error('❌ Backend: Supabase error:', error);
      return serverErrorResponse(res, `Database error: ${error.message}`);
    }

    // Transform data for frontend compatibility
    const vehicles = (data || []).map((v: any) => ({
      id: v.id,
      brand: v.make, // Map make to brand
      model: v.model,
      year: v.year,
      engine: v.engine || `${v.engine_size}cc`, // Map engine_size to engine
      type: v.transmission || 'automatic',
      category: v.category || 'scooter',
      engine_size: v.engine_size,
      transmission: v.transmission,
      fuel_type: v.fuel_type,
      description: v.description,
      images: v.images || [],
      daily_rate: v.daily_rate,
      weekly_rate: v.weekly_rate,
      monthly_rate: v.monthly_rate,
      security_deposit: v.security_deposit,
      location: v.location || {
        address: '',
        city: '',
        latitude: 0,
        longitude: 0,
        postal_code: ''
      },
      active: v.active,
      // Add displayName for frontend compatibility
      displayName: `${v.make} ${v.model}`,
      searchTerms: `${v.make} ${v.model} ${v.engine || v.engine_size}cc ${v.category}`.toLowerCase()
    }));

    console.log(`✅ Backend: Found ${vehicles.length} vehicles matching "${query}"`);

    return successResponse(res, vehicles, `Found ${vehicles.length} vehicle suggestions`, 200, {
      total: vehicles.length,
      // source field not available in pagination metadata
    });
  } catch (error) {
    console.error('❌ Backend: Error in suggestions route:', error);
    return serverErrorResponse(res, 'Failed to fetch vehicle suggestions');
  }
}));

// GET /api/vehicles - show only real, listed vehicles
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 50 } = req.query;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const [vehicles, total] = await Promise.all([
    prisma.listedVehicle.findMany({
      where: { is_available: true },
      orderBy: { created_at: 'desc' },
      skip,
      take: limitNum
    }),
    prisma.listedVehicle.count({
      where: { is_available: true }
    })
  ]);

  return paginatedResponse(res, vehicles, pageNum, limitNum, total, 'Listed vehicles retrieved successfully');
}));

// GET /api/vehicles/autocomplete - show make/model suggestions from vehicle_catalog only
router.get('/autocomplete', asyncHandler(async (req: Request, res: Response) => {
  const { query, limit = 10 } = req.query;

  if (!query || typeof query !== 'string') {
    return validationErrorResponse(res, ['Query parameter is required']);
  }

  const limitNum = parseInt(limit as string);

  // Always filter for reference only
  const vehicles = await prisma.vehicleCatalog.findMany({
    where: {
      is_reference_only: true,
      OR: [
        { make: { contains: query, mode: 'insensitive' } },
        { model: { contains: query, mode: 'insensitive' } }
      ]
    },
    select: { make: true, model: true, image_url: true, category: true },
    take: limitNum,
    orderBy: { make: 'asc' }
  });

  // Unique make/model combinations
  const unique = vehicles.reduce((acc: any[], v) => {
    const key = `${v.make} ${v.model}`;
    if (!acc.find(item => item.value === key)) {
      acc.push({
        value: key,
        label: key,
        make: v.make,
        model: v.model,
        category: v.category,
        image: v.image_url
      });
    }
    return acc;
  }, []);

  return successResponse(res, unique, `Found ${unique.length} autocomplete suggestions`, 200, {
    total: unique.length,
    // source field not available in pagination metadata
  });
}));

// GET /api/vehicles/:id - vehicle detail page (listed_vehicles only)
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!isValidUUID(id)) {
    return validationErrorResponse(res, ['Invalid vehicle ID format (must be UUID)']);
  }

  const vehicle = await prisma.listedVehicle.findUnique({ where: { id } });

  if (!vehicle) {
    return notFoundResponse(res, 'Vehicle');
  }

  return successResponse(res, vehicle, 'Vehicle retrieved successfully');
}));

// GET /api/vehicles/search - filter real listings only
router.get('/search', asyncHandler(async (req: Request, res: Response) => {
  const { make, model, category, city, minPrice, maxPrice, page = 1, limit = 50 } = req.query;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const where: any = { is_available: true };

  if (make) where.make = { contains: make as string, mode: 'insensitive' };
  if (model) where.model = { contains: model as string, mode: 'insensitive' };
  if (category) where.category = { contains: category as string, mode: 'insensitive' };
  if (city) where.location_city = { contains: city as string, mode: 'insensitive' };

  if (minPrice || maxPrice) {
    where.price_per_day = {};
    if (minPrice) where.price_per_day.gte = parseInt(minPrice as string);
    if (maxPrice) where.price_per_day.lte = parseInt(maxPrice as string);
  }

  const [vehicles, total] = await Promise.all([
    prisma.listedVehicle.findMany({
      where,
      orderBy: { created_at: 'desc' },
      skip,
      take: limitNum
    }),
    prisma.listedVehicle.count({ where })
  ]);

  return paginatedResponse(res, vehicles, pageNum, limitNum, total, 'Vehicle search completed successfully');
}));

export default router;
