import { Request, Response, NextFunction } from "express";
import express from 'express';
import { BookingController } from '../controllers/BookingController';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import { roleMiddleware } from '../middleware/roleMiddleware';
import { UserRole } from '@prisma/client';

const router = express.Router();

/**
 * Create a new booking
 * Accessible to authenticated customers
 */
router.post('/', 
  authMiddleware,
  accessControlMiddleware([UserRole.CUSTOMER]),
  (req, res, next) => BookingController.createBooking(req, res, next)
);

/**
 * Get unavailable dates for a specific vehicle
 * Accessible to all authenticated users
 */
router.get('/unavailable-dates/:vehicleId', 
  authMiddleware,
  (req, res, next) => BookingController.getUnavailableDates(req, res, next)
);

/**
 * Confirm a cash booking (for providers)
 * Accessible only to providers
 */
router.post('/:bookingId/confirm', 
  authMiddleware,
  accessControlMiddleware([UserRole.PROVIDER]),
  (req, res, next) => BookingController.confirmCashBooking(req, res, next)
);

/**
 * Reject a cash booking (for providers)
 * Accessible only to providers
 */
router.post('/:bookingId/reject', 
  authMiddleware,
  accessControlMiddleware([UserRole.PROVIDER]),
  (req, res, next) => BookingController.rejectCashBooking(req, res, next)
);

// Get booking by ID (for authenticated users)
router.get(
  '/:bookingId', 
  authMiddleware, 
  BookingController.getBookingById
);

// List bookings with filtering (for authenticated users)
router.get(
  '/', 
  authMiddleware, 
  BookingController.listBookings
);

// Update booking status (for providers and admins)
router.patch(
  '/:bookingId/status', 
  authMiddleware, 
  roleMiddleware(['provider', 'admin']), 
  BookingController.updateBookingStatus
);

// Cancel a booking (for authenticated users)
router.post(
  '/:bookingId/cancel', 
  authMiddleware, 
  BookingController.cancelBooking
);

// Confirm payment for a booking (for providers and admins)
router.post(
  '/confirm-payment', 
  authMiddleware, 
  roleMiddleware(['provider', 'admin']), 
  BookingController.confirmPayment
);

// Check vehicle availability (for all users)
router.get(
  '/check-availability', 
  BookingController.checkAvailability
);

// Get booking statistics (for admins)
router.get(
  '/stats', 
  authMiddleware, 
  roleMiddleware(['admin']), 
  BookingController.getBookingStats
);

// Get user's bookings
router.get(
  '/user/:userId', 
  authMiddleware, 
  BookingController.getUserBookings
);

// Get provider's bookings
router.get(
  '/provider/:providerId', 
  authMiddleware, 
  roleMiddleware(['provider', 'admin']), 
  BookingController.getProviderBookings
);

export default router; 