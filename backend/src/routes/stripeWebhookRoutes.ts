import express, { Request, Response, NextFunction } from "express";
import { StripeWebhookService } from '../services/integrations/stripeWebhookService';

const router = express.Router();
const stripeWebhookService = new StripeWebhookService(process.env.STRIPE_SECRET_KEY || '');

// Middleware to parse raw body for webhook verification
router.post('/webhook', 
  express.raw({ 
    type: 'application/json',
    verify: (req, res, buf) => {
      // Store raw body directly on the request for later verification
      (req as any).rawBody = buf;
    }
  }), 
  async (req: Request, res: Response) => {
    const signature = req.headers['stripe-signature'] as string;
    const rawBody = (req as any).rawBody;
    await stripeWebhookService.handleWebhookEvent(rawBody, signature, res);
  }
);

export default router;
