import express from 'express';
import { Request, Response } from 'express';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Stub geospatial service for production compatibility
const geospatialService = {
  searchNearbyVehicles: async (params: any) => ({ vehicles: [] }),
  getAreaInfo: async (areaId: string) => ({}),
  getVehicleTypesInArea: async (areaId: string) => ({ vehicleTypes: [] }),
  getSearchFilters: async () => ({ filters: [] })
};

// Search nearby vehicles
router.get('/search', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { lat, lng, radius, vehicleType } = req.query;

    if (!lat || !lng) {
      return res.status(400).json({
        error: 'Latitude and longitude are required'
      });
    }

    const searchParams = {
      latitude: parseFloat(lat as string),
      longitude: parseFloat(lng as string),
      radius: radius ? parseFloat(radius as string) : 10,
      vehicleType: vehicleType as string
    };

    const vehicles = await geospatialService.searchNearbyVehicles(searchParams);

    res.json({
      success: true,
      vehicles: vehicles.vehicles,
      count: vehicles.vehicles.length
    });
  } catch (error) {
    console.error('Geospatial search error:', error);
    res.status(500).json({
      error: 'Failed to search vehicles'
    });
  }
});

// Get area information
router.get('/area/:areaId', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { areaId } = req.params;
    const areaInfo = await geospatialService.getAreaInfo(areaId);

    res.json({
      success: true,
      areaInfo
    });
  } catch (error) {
    console.error('Area info error:', error);
    res.status(500).json({
      error: 'Failed to get area information'
    });
  }
});

// Get available filters
router.get('/filters', authMiddleware, async (req: Request, res: Response) => {
  try {
    const filters = await geospatialService.getSearchFilters();

    res.json({
      success: true,
      filters
    });
  } catch (error) {
    console.error('Filters error:', error);
    res.status(500).json({
      error: 'Failed to get search filters'
    });
  }
});

export default router;