import express, { Request, Response, NextFunction } from "express";
import LanguageService from '../services/LanguageService'
import { authMiddleware } from '../middleware/authMiddleware'

const router = express.Router()

// Get supported languages
router.get('/supported', async (req: Request, res: Response) => {
  try {
    const languages = await LanguageService.getSupportedLanguages()
    res.json(languages)
  } catch (error) {
    res.status(500).json({ 
      error: 'Failed to retrieve supported languages',
      details: error.message 
    })
  }
})

// Update user language preference
router.post('/preference', 
  authMiddleware, 
  async (req: Request, res: Response) => {
    try {
      const { languageCode } = req.body
      const userId = req.user.id

      const updatedPreference = await LanguageService.updateUserLanguagePreference(
        userId, 
        languageCode
      )

      res.json(updatedPreference)
    } catch (error) {
      res.status(400).json({ 
        error: 'Failed to update language preference',
        details: error.message 
      })
    }
  }
)

// Get user's current language preference
router.get('/preference', 
  authMiddleware, 
  async (req: Request, res: Response) => {
    try {
      const userId = req.user.id
      const languagePreference = await LanguageService.getUserLanguagePreference(userId)
      res.json(languagePreference)
    } catch (error) {
      res.status(500).json({ 
        error: 'Failed to retrieve language preference',
        details: error.message 
      })
    }
  }
)

export default router 