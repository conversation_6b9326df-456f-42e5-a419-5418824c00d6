import { Router } from 'express';
import { createClient } from '@supabase/supabase-js';

const router = Router();

// Initialize Supabase client for proxy
const supabaseUrl = process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Proxy authentication requests to Supabase
 * This bypasses browser DNS issues
 */

// Sign in with email/password
router.post('/signin', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Auth proxy: Sign in request for', email);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      console.error('❌ Auth proxy: Sign in failed:', error.message);
      return res.status(400).json({ error: error.message });
    }
    
    console.log('✅ Auth proxy: Sign in successful for', email);
    res.json({ data });
    
  } catch (error: any) {
    console.error('❌ Auth proxy: Sign in error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// Sign up with email/password
router.post('/signup', async (req, res) => {
  try {
    const { email, password, options } = req.body;
    
    console.log('🔐 Auth proxy: Sign up request for', email);
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options
    });
    
    if (error) {
      console.error('❌ Auth proxy: Sign up failed:', error.message);
      return res.status(400).json({ error: error.message });
    }
    
    console.log('✅ Auth proxy: Sign up successful for', email);
    res.json({ data });
    
  } catch (error: any) {
    console.error('❌ Auth proxy: Sign up error:', error);
    res.status(500).json({ error: 'Sign up failed' });
  }
});

// Get session
router.get('/session', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.json({ data: { session: null } });
    }
    
    const token = authHeader.replace('Bearer ', '');
    const { data, error } = await supabase.auth.getUser(token);
    
    if (error) {
      return res.json({ data: { session: null } });
    }
    
    res.json({ data: { session: { user: data.user } } });
    
  } catch (error: any) {
    console.error('❌ Auth proxy: Session error:', error);
    res.json({ data: { session: null } });
  }
});

// Sign out
router.post('/signout', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.json({ error: null });
    }
    
    const token = authHeader.replace('Bearer ', '');
    const { error } = await supabase.auth.admin.signOut(token);
    
    if (error) {
      console.error('❌ Auth proxy: Sign out failed:', error.message);
      return res.status(400).json({ error: error.message });
    }
    
    console.log('✅ Auth proxy: Sign out successful');
    res.json({ error: null });
    
  } catch (error: any) {
    console.error('❌ Auth proxy: Sign out error:', error);
    res.status(500).json({ error: 'Sign out failed' });
  }
});

// Test connectivity
router.get('/test', async (req, res) => {
  try {
    console.log('🔍 Auth proxy: Testing Supabase connectivity...');
    
    // Test basic connectivity
    const response = await fetch(`${supabaseUrl}/auth/v1/settings`, {
      headers: {
        'apikey': supabaseAnonKey,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Auth proxy: Supabase connectivity test successful');
      res.json({ 
        status: 'success', 
        message: 'Supabase is reachable from backend',
        settings: data
      });
    } else {
      console.error('❌ Auth proxy: Supabase connectivity test failed:', response.status);
      res.status(500).json({ 
        status: 'error', 
        message: `Supabase returned status ${response.status}` 
      });
    }
    
  } catch (error: any) {
    console.error('❌ Auth proxy: Connectivity test error:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error.message 
    });
  }
});

export default router;
