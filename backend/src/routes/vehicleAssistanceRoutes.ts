import express, { Request, Response } from 'express';
import { VehicleAssistanceService } from '../services/VehicleAssistanceService';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();
const assistanceService = new VehicleAssistanceService();

/**
 * Get available assistance categories
 * GET /api/vehicle-assistance/categories
 */
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = [
      { id: 'BREAKDOWN', name: 'Vehicle Breakdown', priority: 'HIGH' },
      { id: 'ACCIDENT', name: 'Accident', priority: 'URGENT' },
      { id: 'FLAT_TIRE', name: 'Flat Tire', priority: 'MEDIUM' },
      { id: 'BATTERY_DEAD', name: 'Dead Battery', priority: 'MEDIUM' },
      { id: 'OUT_OF_FUEL', name: 'Out of Fuel', priority: 'MEDIUM' },
      { id: 'KEY_LOCKED', name: 'Keys Locked Inside', priority: 'LOW' },
      { id: 'ENGINE_OVERHEATING', name: 'Engine Overheating', priority: 'HIGH' },
      { id: 'ELECTRICAL_ISSUE', name: 'Electrical Problem', priority: 'MEDIUM' },
      { id: 'OTHER', name: 'Other Issue', priority: 'LOW' }
    ];

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching assistance categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assistance categories'
    });
  }
});

/**
 * Create a new vehicle assistance request
 * POST /api/vehicle-assistance
 */
router.post('/', authMiddleware, async (req: Request, res: Response) => {
  try {
    const {
      bookingId,
      providerId,
      vehicleId,
      location,
      message,
      reason,
      category,
      priority
    } = req.body;

    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!reason || !category) {
      return res.status(400).json({
        success: false,
        error: 'Reason and category are required'
      });
    }

    const assistanceRequest = await assistanceService.createAssistanceRequest({
      userId,
      bookingId,
      providerId,
      vehicleId,
      location,
      message,
      reason,
      category,
      priority
    });

    res.status(201).json({
      success: true,
      data: assistanceRequest,
      message: 'Vehicle assistance request created successfully'
    });
  } catch (error) {
    console.error('Error creating assistance request:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create assistance request'
    });
  }
});

/**
 * Get user's assistance requests
 * GET /api/vehicle-assistance/user
 */
router.get('/user', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const requests = await assistanceService.getAssistanceRequestsByUser(userId);

    res.json({
      success: true,
      data: requests
    });
  } catch (error) {
    console.error('Error fetching user assistance requests:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assistance requests'
    });
  }
});

/**
 * Get provider's assistance requests
 * GET /api/vehicle-assistance/provider
 */
router.get('/provider', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId || userRole !== 'PROVIDER') {
      return res.status(403).json({
        success: false,
        error: 'Provider access required'
      });
    }

    const requests = await assistanceService.getAssistanceRequestsByProvider(userId);

    res.json({
      success: true,
      data: requests
    });
  } catch (error) {
    console.error('Error fetching provider assistance requests:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assistance requests'
    });
  }
});

/**
 * Update assistance request status (Admin only)
 * PUT /api/vehicle-assistance/:id/status
 */
router.put('/:id/status', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status, adminNotes } = req.body;
    const userRole = req.user?.role;

    if (userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Status is required'
      });
    }

    const validStatuses = ['PENDING', 'ASSIGNED', 'IN_PROGRESS', 'RESOLVED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status'
      });
    }

    const updatedRequest = await assistanceService.updateAssistanceRequestStatus(
      id,
      status,
      adminNotes
    );

    res.json({
      success: true,
      data: updatedRequest,
      message: 'Assistance request status updated successfully'
    });
  } catch (error) {
    console.error('Error updating assistance request status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update assistance request status'
    });
  }
});

/**
 * Get all assistance requests (Admin only)
 * GET /api/vehicle-assistance/admin/all
 */
router.get('/admin/all', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;

    if (userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const { status, priority, limit = '50', offset = '0' } = req.query;

    const result = await assistanceService.getAllAssistanceRequests(
      status as string,
      priority as string,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: result.requests,
      total: result.total,
      pagination: {
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: result.total > parseInt(offset as string) + parseInt(limit as string)
      }
    });
  } catch (error) {
    console.error('Error fetching all assistance requests:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assistance requests'
    });
  }
});

/**
 * Get assistance request by ID
 * GET /api/vehicle-assistance/:id
 */
router.get('/:id', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Get the assistance request
    const request = await assistanceService.prisma.vehicleAssistanceRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true
          }
        },
        provider: {
          select: {
            id: true,
            name: true,
            email: true,
            phoneNumber: true
          }
        },
        booking: {
          select: {
            id: true,
            vehicleId: true,
            startDate: true,
            endDate: true
          }
        },
        vehicle: {
          select: {
            id: true,
            brand: true,
            model: true,
            vehicleType: true
          }
        }
      }
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        error: 'Assistance request not found'
      });
    }

    // Check access permissions
    const hasAccess = userRole === 'ADMIN' || 
                     request.userId === userId || 
                     request.providerId === userId;

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: request
    });
  } catch (error) {
    console.error('Error fetching assistance request:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assistance request'
    });
  }
});

/**
 * Get assistance request statistics (Admin only)
 * GET /api/vehicle-assistance/admin/stats
 */
router.get('/admin/stats', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;

    if (userRole !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    const [
      totalRequests,
      pendingRequests,
      urgentRequests,
      resolvedRequests,
      categoryStats,
      priorityStats
    ] = await Promise.all([
      assistanceService.prisma.vehicleAssistanceRequest.count(),
      assistanceService.prisma.vehicleAssistanceRequest.count({
        where: { status: 'PENDING' }
      }),
      assistanceService.prisma.vehicleAssistanceRequest.count({
        where: { priority: 'URGENT' }
      }),
      assistanceService.prisma.vehicleAssistanceRequest.count({
        where: { status: 'RESOLVED' }
      }),
      assistanceService.prisma.vehicleAssistanceRequest.groupBy({
        by: ['category'],
        _count: { category: true }
      }),
      assistanceService.prisma.vehicleAssistanceRequest.groupBy({
        by: ['priority'],
        _count: { priority: true }
      })
    ]);

    res.json({
      success: true,
      data: {
        total: totalRequests,
        pending: pendingRequests,
        urgent: urgentRequests,
        resolved: resolvedRequests,
        categoryBreakdown: categoryStats,
        priorityBreakdown: priorityStats
      }
    });
  } catch (error) {
    console.error('Error fetching assistance request statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics'
    });
  }
});

export default router;
