import express, { Request, Response, NextFunction } from "express";
import { SupportService } from '../services/SupportService';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();
const supportService = new SupportService();

// Create a support ticket
router.post('/tickets', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { 
      category, 
      message, 
      bookingId 
    } = req.body;

    const userId = req.user?.id;
    const userRole = req.user?.role;
    
    // Map UserRole to SupportService expected roles
    const role = userRole === 'CUSTOMER' ? 'USER' : 
                 userRole === 'PROVIDER' ? 'PROVIDER' : 'ADMIN';

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const ticket = await supportService.createSupportTicket({
      userId,
      category,
      message,
      bookingId,
      role
    });

    res.status(201).json(ticket);
  } catch (error) {
    console.error('Error creating support ticket:', error);
    res.status(500).json({ error: 'Failed to create support ticket' });
  }
});

// Get user's support tickets
router.get('/tickets', authMiddleware, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const tickets = await supportService.getSupportTickets();
    res.json(tickets);
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    res.status(500).json({ error: 'Failed to fetch support tickets' });
  }
});

export default router;
