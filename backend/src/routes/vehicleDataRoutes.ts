import { Router } from 'express';
import VehicleDataController from '../controllers/VehicleDataController';
import VehicleDataService from '../services/VehicleDataService';

const router = Router();
const vehicleDataController = new VehicleDataController();

// Update entire vehicle catalogue
router.post('/update-catalogue', vehicleDataController.updateCatalogue.bind(vehicleDataController));

// Scrape individual manufacturers
router.get('/scrape/honda', vehicleDataController.scrapeHondaModels.bind(vehicleDataController));
router.get('/scrape/yamaha', vehicleDataController.scrapeYamahaModels.bind(vehicleDataController));
router.get('/scrape/suzuki', vehicleDataController.scrapeSuzukiModels.bind(vehicleDataController));
router.get('/scrape/vespa', vehicleDataController.scrapeVespaModels.bind(vehicleDataController));

// Scrape all manufacturers
router.get('/scrape/all', vehicleDataController.getAllManufacturers.bind(vehicleDataController));

// Thailand Honda
router.get('/scrape/thailand-honda', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapeThailandHondaModels();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Vietnam Honda
router.get('/scrape/vietnam-honda', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapeVietnamHondaModels();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Malaysia Yamaha
router.get('/scrape/malaysia-yamaha', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapeMalaysiaYamahaModels();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Philippines Suzuki
router.get('/scrape/philippines-suzuki', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapePhilippinesSuzukiModels();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// BikeWale Southeast Asia
router.get('/scrape/bikewale-sea', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapeBikeWaleSEAsia();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// BikeWale Main Site
router.get('/scrape/bikewale-main', async (req, res) => {
  try {
    const data = await vehicleDataController.scrapeBikeWaleMain();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// User-provided URL scraping endpoints
router.get('/scrape/user-urls', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const data = await vehicleDataService.scrapeUserProvidedURLs();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Individual user URL scraping endpoints
router.get('/scrape/honda-click-motoxpress', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const data = await vehicleDataService.scrapeHondaClickFromMotoxpress();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

router.get('/scrape/honda-beat-astraotoshop', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const data = await vehicleDataService.scrapeHondaBeatFromAstraotoshop();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

router.get('/scrape/yamaha-nmax-yamaha-motor', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const data = await vehicleDataService.scrapeYamahaNMAXFromYamahaMotor();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

router.get('/scrape/suzuki-address-sepeda-motor', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const data = await vehicleDataService.scrapeSuzukiAddressFromSepedaMotor();
    res.json({ success: true, data });
  } catch (err) {
    res.status(500).json({ success: false, error: err.message });
  }
});

// Get catalogue status
router.get('/status', vehicleDataController.getCatalogueStatus.bind(vehicleDataController));

// Get all scraped vehicle data for frontend selection
router.get('/available-models', async (req, res) => {
  try {
    const vehicleDataService = VehicleDataService.getInstance();
    const vehicles = await vehicleDataService.getVehiclesWithCache();
    
    // Transform to a simpler format for frontend selection with deduplication
    const vehicleMap = new Map();
    
    vehicles.forEach(vehicle => {
      const baseId = `${vehicle.brand.toLowerCase()}-${vehicle.model.toLowerCase().replace(/\s+/g, '-')}`;
      const existingVehicle = vehicleMap.get(baseId);
      
      // If we already have this vehicle, merge images and keep the one with more data
      if (existingVehicle) {
        // Merge images from both entries
        const allImages = [...(existingVehicle.images || []), ...(vehicle.images || [])];
        const uniqueImages = [...new Set(allImages)];
        
        // Keep the entry with more complete data (prefer the one with images)
        if (vehicle.images && vehicle.images.length > 0 && (!existingVehicle.images || existingVehicle.images.length === 0)) {
          vehicleMap.set(baseId, {
            id: baseId,
            brand: vehicle.brand,
            model: vehicle.model,
            year: vehicle.year,
            engine: vehicle.engine || 'Unknown',
            type: vehicle.type,
            category: vehicle.category || 'General',
            images: uniqueImages,
            specs: vehicle.specs || [],
            displayName: `${vehicle.brand} ${vehicle.model}`,
            searchTerms: `${vehicle.brand} ${vehicle.model} ${vehicle.engine || ''} ${vehicle.type}`.toLowerCase()
          });
        } else {
          // Update existing entry with merged images
          existingVehicle.images = uniqueImages;
        }
      } else {
        // First time seeing this vehicle
        vehicleMap.set(baseId, {
          id: baseId,
          brand: vehicle.brand,
          model: vehicle.model,
          year: vehicle.year,
          engine: vehicle.engine,
          type: vehicle.type,
          category: vehicle.category,
          images: vehicle.images || [],
          specs: vehicle.specs || [],
          displayName: `${vehicle.brand} ${vehicle.model}`,
          searchTerms: `${vehicle.brand} ${vehicle.model} ${vehicle.engine} ${vehicle.type}`.toLowerCase()
        });
      }
    });
    
    const availableModels = Array.from(vehicleMap.values());
    
    res.json({
      success: true,
      data: availableModels,
      count: availableModels.length
    });
  } catch (error) {
    console.error('Error fetching available models:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch available models',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router; 