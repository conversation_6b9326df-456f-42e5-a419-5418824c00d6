import { Router } from 'express';
import { PayoutController } from '../controllers/PayoutController';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import { UserRole } from '@prisma/client';

const router = Router();
const payoutController = new PayoutController();

// Apply auth middleware to all routes
router.use(authMiddleware);

// Provider routes (requires authentication)
router.post('/', payoutController.createPayoutRequest);
router.get('/provider', payoutController.getProviderPayouts);

// Admin routes (requires admin role)
router.get('/', accessControlMiddleware([UserRole.ADMIN]), payoutController.getAllPayouts);
router.get('/stats', accessControlMiddleware([UserRole.ADMIN]), payoutController.getPayoutStats);
router.post('/:id/process', accessControlMiddleware([UserRole.ADMIN]), payoutController.processPayoutRequest);
router.patch('/:id/status', accessControlMiddleware([UserRole.ADMIN]), payoutController.updatePayoutStatus);

// Get payout by ID (authorized users only)
router.get('/:id', payoutController.getPayoutById);

export default router; 