import express, { Request, Response, NextFunction } from "express";
import ProviderController from '../controllers/ProviderController';
import { authMiddleware } from '../middleware/authMiddleware';
import { roleMiddleware } from '../middleware/roleMiddleware';

const router = express.Router();

// Get all providers
router.get(
  '/', 
  ProviderController.getProviders
);

// Get provider by ID
router.get(
  '/:id', 
  ProviderController.getProviderById
);

// Create a new provider profile
router.post(
  '/', 
  authMiddleware, 
  ProviderController.createProviderProfile
);

// Update provider profile
router.put(
  '/:id', 
  authMiddleware, 
  roleMiddleware(['provider']), 
  ProviderController.updateProviderProfile
);

export default router; 