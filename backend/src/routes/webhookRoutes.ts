import express, { Request, Response, NextFunction } from "express";
import { WebhookController } from '../controllers/webhookController';
import { authMiddleware, requireAdmin } from '../middleware/authMiddleware';

const router = express.Router();

// Stripe webhook endpoint (public, uses signature verification)
router.post('/stripe', express.raw({ type: 'application/json' }), WebhookController.handleStripeWebhook);

// Generic webhook endpoint for other services (requires authentication)
router.post('/:provider', 
  requireAdmin, 
  express.json(), 
  WebhookController.handleGenericWebhook
);

// Retrieve webhook logs (admin-only)
router.get('/logs', 
  requireAdmin, 
  WebhookController.getWebhookLogs
);

export default router;
