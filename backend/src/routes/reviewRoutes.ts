import express, { Request, Response, NextFunction } from "express";
import { ReviewService } from '../services/ReviewService';
import { authMiddleware } from '../middleware/authMiddleware';
import { PrismaClient } from '@prisma/client';

const router = express.Router();
const reviewService = new ReviewService(new PrismaClient());

// Submit a review for a booking
router.post('/submit', authMiddleware, async (req: Request, res: Response) => {
  try {
    const reviewData = req.body;
    const review = await reviewService.submitReview(reviewData);
    res.status(201).json(review);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Get reviews for a specific vehicle
router.get('/vehicle/:vehicleId', async (req: Request, res: Response) => {
  try {
    const { vehicleId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const reviews = await reviewService.getVehicleReviews(
      vehicleId, 
      Number(page), 
      Number(limit)
    );
    res.json(reviews);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get reviews for a specific provider
router.get('/provider/:providerId', async (req: Request, res: Response) => {
  try {
    const { providerId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const reviews = await reviewService.getProviderReviews(
      providerId, 
      Number(page), 
      Number(limit)
    );
    res.json(reviews);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Provider responds to a review
router.post('/:reviewId/respond', authMiddleware, async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const { response } = req.body;
    const updatedReview = await reviewService.respondToReview(reviewId, response);
    res.json(updatedReview);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

export default router;
