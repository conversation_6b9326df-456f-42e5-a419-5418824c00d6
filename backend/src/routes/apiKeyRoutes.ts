import express, { Request, Response, NextFunction } from "express";
import { APIKeyController } from '../controllers/apiKeyController';
import { authMiddleware, requireAdmin } from '../middleware/authMiddleware';

const router = express.Router();

// Create a new API key (admin-only)
router.post('/', 
  requireAdmin, 
  APIKeyController.createAPIKey
);

// List API keys (admin-only)
router.get('/', 
  requireAdmin, 
  APIKeyController.listAPIKeys
);

// Delete an API key (admin-only)
router.delete('/:id', 
  requireAdmin, 
  APIKeyController.deleteAPIKey
);

// Check service status (admin-only)
router.get('/status', 
  requireAdmin, 
  APIKeyController.checkServiceStatus
);

export default router;
