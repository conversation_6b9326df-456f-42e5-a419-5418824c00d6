import express, { Request, Response, NextFunction } from "express";
import PaymentController from '../controllers/PaymentController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();
const paymentController = new PaymentController();

// Initialize payment for a booking
router.post(
  '/initialize',
  authMiddleware,
  (req, res) => paymentController.initializePayment(req, res)
);

// Confirm payment
router.post(
  '/confirm',
  authMiddleware,
  (req, res) => paymentController.confirmPayment(req, res)
);

// Get payment details
router.get(
  '/:paymentId',
  authMiddleware,
  (req, res) => paymentController.getPaymentDetails(req, res)
);

// List user payments
router.get(
  '/user/:userId',
  authMiddleware,
  (req, res) => paymentController.listUserPayments(req, res)
);

// Process refund
router.post(
  '/refund',
  authMiddleware,
  (req, res) => paymentController.processRefund(req, res)
);

// PayPal webhook endpoint
router.post(
  '/webhooks/paypal', 
  (req, res) => paymentController.handlePayPalWebhook(req, res)
);

// Stripe Payment Routes
router.post(
  '/stripe/payment-intent', 
  authMiddleware, 
  (req, res) => paymentController.createStripePaymentIntent(req, res)
);

router.post(
  '/stripe/confirm', 
  authMiddleware, 
  (req, res) => paymentController.confirmStripePayment(req, res)
);

// Cash Payment Routes
router.post(
  '/cash/confirm/:bookingId',
  authMiddleware,
  (req, res) => paymentController.confirmCashPayment(req, res)
);

// Provider Earnings Routes
router.get(
  '/provider/earnings',
  authMiddleware,
  (req, res) => paymentController.getProviderEarnings(req, res)
);

// Bank Details Routes
router.post(
  '/provider/bank-details',
  authMiddleware,
  (req, res) => paymentController.updateProviderBankDetails(req, res)
);

// Admin Payout Routes
router.post(
  '/admin/process-payouts',
  authMiddleware,
  (req, res) => paymentController.processProviderPayouts(req, res)
);

export default router;
