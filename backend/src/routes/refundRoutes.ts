import express, { Request, Response, NextFunction } from "express";
import { StripeRefundService } from '../services/integrations/StripeRefundService';
import { authMiddleware } from '../middleware/authMiddleware';
import { logger } from '../utils/logger';
import { PrismaClient } from '@prisma/client';

const router = express.Router();
const prisma = new PrismaClient();

// Initialize Stripe Refund Service
const stripeRefundService = new StripeRefundService(process.env.STRIPE_SECRET_KEY!);

// Route to create a refund
router.post('/payments/:paymentId/refund', 
  authMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { paymentId } = req.params;
      const { amount, reason } = req.body;

      // Validate input
      if (!paymentId) {
        return res.status(400).json({ error: 'Payment ID is required' });
      }

      // Create refund
      const refundResult = await stripeRefundService.createRefund(
        paymentId, 
        amount ? parseFloat(amount) : undefined, 
        reason
      );

      // Log successful refund
      logger.info(`Refund processed for payment ${paymentId}`, { 
        refundId: refundResult.refund.id,
        amount: refundResult.refund.amount
      });

      res.status(200).json({
        message: 'Refund processed successfully',
        refund: {
          refundId: refundResult.refund.id,
          amount: refundResult.refund.amount,
          status: refundResult.refund.status
        }
      });
    } catch (error) {
      // Log refund error
      logger.error('Refund processing error', { error, paymentId: req.params.paymentId });

      // Send error response
      res.status(500).json({ 
        error: 'Failed to process refund', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
);

// Route to get refund details
router.get('/payments/:paymentId/refunds', 
  authMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { paymentId } = req.params;

      // Validate input
      if (!paymentId) {
        return res.status(400).json({ error: 'Payment ID is required' });
      }

      // Fetch refunds for the payment
      const refunds = await prisma.refund.findMany({
        where: { bookingId: paymentId }, // Using bookingId instead of paymentId
        orderBy: { createdAt: 'desc' }
      });

      res.status(200).json({
        refunds,
        count: refunds.length
      });
    } catch (error) {
      // Log error
      logger.error('Error fetching refunds', { error, paymentId: req.params.paymentId });

      // Send error response
      res.status(500).json({ 
        error: 'Failed to fetch refunds', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
);

export default router;
