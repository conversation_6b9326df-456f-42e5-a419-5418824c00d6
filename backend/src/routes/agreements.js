const express = require('express');
const router = express.Router();
const AgreementController = require('../controllers/agreementController');
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Middleware for authentication (placeholder - replace with actual auth middleware)
const authenticateProvider = (req, res, next) => {
  // For now, just pass through - replace with actual JWT validation
  next();
};

/**
 * @route GET /api/providers/agreement-templates
 * @desc Get available agreement templates
 * @access Private (Provider)
 */
router.get('/templates', authenticateProvider, AgreementController.getTemplates);

/**
 * @route POST /api/providers/generate-agreement
 * @desc Generate agreement content from template and booking
 * @access Private (Provider)
 */
router.post('/generate', authenticateProvider, AgreementController.generateAgreement);

/**
 * @route POST /api/providers/send-agreement
 * @desc Send agreement to customer
 * @access Private (Provider)
 */
router.post('/send', authenticateProvider, AgreementController.sendAgreement);

/**
 * @route GET /api/providers/agreements
 * @desc Get agreements for provider
 * @access Private (Provider)
 */
router.get('/', authenticateProvider, AgreementController.getAgreements);

/**
 * @route GET /api/providers/agreements/:id
 * @desc Get specific agreement details
 * @access Private (Provider)
 */
router.get('/:id', authenticateProvider, async (req, res) => {
  try {
    const { id } = req.params;
    const providerId = req.user?.id || req.headers['x-user-id'];

    const query = `
      SELECT 
        a.*,
        u.name as customer_name,
        u.email as customer_email,
        u.phone as customer_phone,
        v.brand || ' ' || v.model as vehicle_name,
        json_agg(
          json_build_object(
            'id', s.id,
            'signer_type', s.signer_type,
            'signature_method', s.signature_method,
            'signed_at', s.signed_at,
            'is_verified', s.is_verified
          )
        ) FILTER (WHERE s.id IS NOT NULL) as signatures,
        json_agg(
          json_build_object(
            'id', h.id,
            'action', h.action,
            'actor_type', h.actor_type,
            'created_at', h.created_at,
            'notes', h.notes
          ) ORDER BY h.created_at DESC
        ) FILTER (WHERE h.id IS NOT NULL) as history
      FROM agreements a
      JOIN users u ON a.customer_id = u.id
      JOIN bookings b ON a.booking_id = b.id
      JOIN vehicles v ON b.vehicle_id = v.id
      LEFT JOIN agreement_signatures s ON a.id = s.agreement_id
      LEFT JOIN agreement_history h ON a.id = h.agreement_id
      WHERE a.id = $1 AND a.provider_id = $2
      GROUP BY a.id, u.name, u.email, u.phone, v.brand, v.model
    `;

    const result = await pool.query(query, [id, providerId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Agreement not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error fetching agreement details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agreement details'
    });
  }
});

/**
 * @route POST /api/providers/agreements/:id/resend
 * @desc Resend agreement to customer
 * @access Private (Provider)
 */
router.post('/:id/resend', authenticateProvider, async (req, res) => {
  try {
    const { id } = req.params;
    const providerId = req.user?.id || req.headers['x-user-id'];

    // Update sent timestamp
    const updateQuery = `
      UPDATE agreements 
      SET sent_at = NOW()
      WHERE id = $1 AND provider_id = $2 AND status IN ('sent', 'viewed')
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [id, providerId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Agreement not found or cannot be resent'
      });
    }

    // Log resend action
    await AgreementController.logAgreementAction(id, 'resent', providerId, 'provider');

    // TODO: Send email/SMS notification
    // await this.sendAgreementNotification(result.rows[0]);

    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error resending agreement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resend agreement'
    });
  }
});

/**
 * @route PUT /api/providers/agreements/:id/cancel
 * @desc Cancel agreement
 * @access Private (Provider)
 */
router.put('/:id/cancel', authenticateProvider, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const providerId = req.user?.id || req.headers['x-user-id'];

    const updateQuery = `
      UPDATE agreements 
      SET status = 'cancelled'
      WHERE id = $1 AND provider_id = $2 AND status NOT IN ('signed', 'cancelled')
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [id, providerId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Agreement not found or cannot be cancelled'
      });
    }

    // Log cancellation
    await AgreementController.logAgreementAction(id, 'cancelled', providerId, 'provider', reason);

    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error cancelling agreement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel agreement'
    });
  }
});

/**
 * Customer-facing routes for agreement signing
 */

/**
 * @route GET /api/agreements/:id/view
 * @desc View agreement (customer access)
 * @access Public (with token)
 */
router.get('/:id/view', async (req, res) => {
  try {
    const { id } = req.params;
    const { token } = req.query; // Optional access token for security

    // Update viewed status if not already viewed
    const updateQuery = `
      UPDATE agreements 
      SET status = CASE 
        WHEN status = 'sent' THEN 'viewed'
        ELSE status
      END,
      viewed_at = CASE 
        WHEN viewed_at IS NULL THEN NOW()
        ELSE viewed_at
      END
      WHERE id = $1
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Agreement not found'
      });
    }

    const agreement = result.rows[0];

    // Log view action
    await AgreementController.logAgreementAction(id, 'viewed', agreement.customer_id, 'customer');

    res.json({
      success: true,
      data: {
        id: agreement.id,
        title: agreement.title,
        content: agreement.generated_content,
        status: agreement.status,
        metadata: agreement.metadata,
        created_at: agreement.created_at,
        sent_at: agreement.sent_at
      }
    });
  } catch (error) {
    console.error('Error viewing agreement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to view agreement'
    });
  }
});

/**
 * @route POST /api/agreements/:id/respond
 * @desc Respond to agreement (customer - agree/disagree)
 * @access Public (with validation)
 */
router.post('/:id/respond', async (req, res) => {
  try {
    const { id } = req.params;
    const { response, customerInfo } = req.body;

    // Validate response
    if (!['agree', 'disagree'].includes(response)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid response. Must be "agree" or "disagree"'
      });
    }

    // Validate agreement can be responded to
    const agreementQuery = `
      SELECT a.*, u.email as customer_email, u.phone as customer_phone,
             p.email as provider_email
      FROM agreements a
      JOIN users u ON a.customer_id = u.id
      JOIN providers p ON a.provider_id = p.id
      WHERE a.id = $1 AND a.status IN ('sent', 'viewed')
    `;

    const agreementResult = await pool.query(agreementQuery, [id]);

    if (agreementResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Agreement not found or cannot be responded to'
      });
    }

    const agreement = agreementResult.rows[0];

    // Start transaction
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Update agreement status
      const newStatus = response === 'agree' ? 'agreed' : 'declined';
      const timestampField = response === 'agree' ? 'signed_at' : 'declined_at';

      await client.query(`
        UPDATE agreements
        SET status = $1, ${timestampField} = NOW()
        WHERE id = $2
      `, [newStatus, id]);

      // Log response action
      const action = response === 'agree' ? 'agreed' : 'declined';
      await client.query(`
        INSERT INTO agreement_history (
          agreement_id, action, actor_id, actor_type, ip_address, user_agent, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        id,
        action,
        agreement.customer_id,
        'customer',
        req.ip,
        req.get('User-Agent'),
        `Customer ${response}d to the agreement terms`
      ]);

      await client.query('COMMIT');

      // Send confirmation emails
      if (response === 'agree') {
        try {
          const AgreementNotificationService = require('../services/AgreementNotificationService');
          const notificationService = new AgreementNotificationService();

          await notificationService.sendAgreementConfirmedNotification(
            agreement,
            agreement.customer_email,
            agreement.provider_email
          );
        } catch (emailError) {
          console.error('Failed to send confirmation emails:', emailError);
          // Don't fail the request if email fails
        }
      }

      res.json({
        success: true,
        message: `Agreement ${response}d successfully`,
        data: {
          status: newStatus,
          response: response
        }
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error responding to agreement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to respond to agreement'
    });
  }
});

module.exports = router;
