import express, { Request, Response, NextFunction } from "express";
import OAuthController from '../controllers/OAuthController';

const router = express.Router()

// Middleware to make authentication optional
const optionalAuth = (req: Request, res: Response, next: NextFunction) => {
  // Optional authentication logic can be added here if needed
  next()
}

// Google OAuth routes
router.get('/google', 
  optionalAuth, 
  (req: Request, res: Response) => OAuthController.initiateGoogleOAuth(req, res)
)

router.get('/google/callback',
  (req: Request, res: Response) => OAuthController.handleGoogleOAuthCallback(req, res)
)

router.post('/google/callback',
  (req: Request, res: Response) => OAuthController.handleGoogleOAuthCallback(req, res)
)

export default router 