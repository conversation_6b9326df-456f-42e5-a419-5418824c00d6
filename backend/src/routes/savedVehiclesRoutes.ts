import express, { Request, Response, NextFunction } from "express";
import SavedVehiclesController from "../controllers/SavedVehiclesController";
import { authMiddleware } from "../middleware/authMiddleware";

const router = express.Router();

// Get all saved vehicles for a user
router.get('/', authMiddleware, SavedVehiclesController.getSavedVehicles);

// Save a vehicle
router.post('/', authMiddleware, SavedVehiclesController.saveVehicle);

// Remove a saved vehicle
router.delete('/:vehicleId', authMiddleware, SavedVehiclesController.removeSavedVehicle);

// Check if a vehicle is saved
router.get('/check/:vehicleId', authMiddleware, SavedVehiclesController.isVehicleSaved);

export default router;
