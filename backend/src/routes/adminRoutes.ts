import express, { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authMiddleware } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/roleBasedAccessControl';
import { UserRole } from '@prisma/client';
import { asyncHandler } from '../utils/asyncHandler';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * Get all vehicle listings with filtering and pagination
 */
router.get('/listings',
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { 
      page = 1, 
      limit = 10, 
      type, 
      status, 
      providerRegion 
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    const vehicles = await prisma.vehicle.findMany({
      where: {
        ...(type ? { vehicleType: type as string } : {}),
        ...(providerRegion ? { 
          providerId: {
            in: await prisma.user.findMany({
              where: { role: 'PROVIDER' },
              select: { id: true }
            }).then(users => users.map(u => u.id))
          }
        } : {})
      },
      include: {
        _count: {
          select: {
            bookings: true,
            reviews: true
          }
        },
        images: true
      },
      orderBy: {
        id: 'desc'
      },
      skip,
      take: Number(limit)
    });

    const total = await prisma.vehicle.count({
      where: {
        ...(type ? { vehicleType: type as string } : {}),
        ...(providerRegion ? { 
          providerId: {
            in: await prisma.user.findMany({
              where: { role: 'PROVIDER' },
              select: { id: true }
            }).then(users => users.map(u => u.id))
          }
        } : {})
      }
    });

    res.status(200).json({
      message: 'Vehicle listings retrieved successfully',
      vehicles,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  })
);

/**
 * Approve a vehicle listing
 */
router.post('/listings/:id/approve',
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { reason, adminNotes } = req.body;

    const updatedVehicle = await prisma.vehicle.update({
      where: { id },
      data: {
        // Note: No status field in current schema, so we'll just update notes
        // In a real implementation, you might want to add a status field
        // For now, we'll just log the approval
      }
    });

    logger.info('Vehicle listing approved', { 
      vehicleId: id, 
      reason, 
      adminNotes 
    });

    res.status(200).json({
      message: 'Vehicle listing approved successfully',
      vehicle: updatedVehicle
    });
  })
);

/**
 * Disable a vehicle listing
 */
router.post('/listings/:id/disable',
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { reason, adminNotes } = req.body;

    // Since there's no status field, we'll delete the vehicle
    await prisma.vehicle.delete({
      where: { id }
    });

    logger.info('Vehicle listing disabled', { 
      vehicleId: id, 
      reason, 
      adminNotes 
    });

    res.status(200).json({
      message: 'Vehicle listing disabled successfully'
    });
  })
);

/**
 * Resolve an SOS alert
 */
router.post('/sos-alerts/:id/resolve',
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { notes } = req.body;

    // SOS Alert system has been replaced with Vehicle Assistance Request system
    return res.status(410).json({
      message: 'SOS Alert system has been replaced with Vehicle Assistance Request system',
      redirectTo: '/api/vehicle-assistance',
      deprecated: true
    });
  })
);

/**
 * Update support ticket status
 */
router.put('/support-tickets/:id',
  authMiddleware,
  accessControlMiddleware([UserRole.ADMIN]),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { status, notes } = req.body;

    const updatedTicket = await prisma.supportTicket.update({
      where: { id },
      data: { 
        status: status as string,
        // Note: No adminNotes field in current schema
        // We'll just log the admin notes
      },
      include: {
        user: {
          select: {
            email: true,
            name: true
          }
        }
      }
    });

    logger.info('Support ticket updated', { 
      ticketId: id, 
      status, 
      notes 
    });

    res.status(200).json({
      message: 'Support ticket updated successfully',
      ticket: updatedTicket
    });
  })
);

/**
 * Get pending verification documents
 */
router.get('/verification-documents',
  authMiddleware,
  accessControlMiddleware([UserRole.ADMIN]),
  asyncHandler(async (req: Request, res: Response) => {
    const { page = 1, limit = 10, status = 'PENDING' } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    // Note: verificationDocument table doesn't exist in current schema
    // This is a placeholder implementation
    const pendingDocuments: any[] = [];
    const total = 0;

    res.status(200).json({
      message: 'Verification documents retrieved successfully',
      documents: pendingDocuments,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  })
);

/**
 * Approve verification document
 */
router.post('/verification-documents/:id/approve',
  authMiddleware,
  accessControlMiddleware([UserRole.ADMIN]),
  asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { notes } = req.body;

    // Note: verificationDocument table doesn't exist in current schema
    // This is a placeholder implementation
    logger.info('Verification document approved', { 
      documentId: id, 
      notes 
    });

    res.status(200).json({
      message: 'Verification document approved successfully'
    });
  })
);

/**
 * Get admin dashboard statistics
 */
router.get('/dashboard/stats',
  authMiddleware,
  accessControlMiddleware([UserRole.ADMIN]),
  asyncHandler(async (req: Request, res: Response) => {
    const [
      totalUsers,
      totalVehicles,
      totalBookings,
      totalRevenue,
      pendingVerifications,
      activeAssistanceRequests,
      openSupportTickets
    ] = await Promise.all([
      prisma.user.count(),
      prisma.vehicle.count(),
      prisma.booking.count(),
      prisma.payment.aggregate({
        _sum: { amount: true }
      }),
      // Placeholder for verification documents
      Promise.resolve(0),
      prisma.vehicleAssistanceRequest.count({
        where: { status: 'PENDING' }
      }),
      prisma.supportTicket.count({
        where: { status: 'OPEN' }
      })
    ]);

    res.status(200).json({
      message: 'Dashboard statistics retrieved successfully',
      stats: {
        totalUsers,
        totalVehicles,
        totalBookings,
        totalRevenue: totalRevenue._sum.amount || 0,
        pendingVerifications,
        activeAssistanceRequests,
        openSupportTickets
      }
    });
  })
);

export default router;
