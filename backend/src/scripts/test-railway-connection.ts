import { PrismaClient } from '@prisma/client'
import { Client } from 'pg'
import 'dotenv/config'

const prisma = new PrismaClient()

async function testRailwayConnection() {
  console.log('🔍 Railway Database Connection Test')
  console.log('=====================================')
  
  // Environment info
  console.log('📍 Environment Variables:')
  console.log('   - NODE_ENV:', process.env.NODE_ENV)
  console.log('   - RAILWAY_SERVICE_NAME:', process.env.RAILWAY_SERVICE_NAME)
  console.log('   - RAILWAY_PUBLIC_DOMAIN:', process.env.RAILWAY_PUBLIC_DOMAIN)
  console.log('   - PORT:', process.env.PORT)
  
  // Database URL info (masked)
  const dbUrl = process.env.DATABASE_URL || '';
  const maskedUrl = dbUrl.replace(/(\/\/.*:)(.*)(@)/, '$1*****$3');
  console.log('   - DATABASE_URL (masked):', maskedUrl);
  console.log('   - SSL Mode:', dbUrl.includes('sslmode=require') ? 'Required' : 'Not specified');
  console.log('   - Connection Pool:', dbUrl.includes('connection_limit') ? 'Configured' : 'Not configured');
  
  console.log('\n🔗 Testing Prisma Connection...')
  
  try {
    // Test 1: Prisma connection
    console.log('\n1️⃣ Testing Prisma connection...')
    await prisma.$connect()
    console.log('✅ Prisma connection successful')
    
    // Test 2: Simple query
    console.log('\n2️⃣ Testing simple query...')
    const result = await prisma.$queryRaw`SELECT 1 as test, current_timestamp as time`
    console.log('✅ Query successful:', result)
    
    // Test 3: Schema access
    console.log('\n3️⃣ Testing schema access...')
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
      LIMIT 10
    `
    console.log('✅ Schema access successful. Tables found:', (tables as any[]).length)
    console.log('   Sample tables:', (tables as any[]).slice(0, 5).map(t => t.table_name))
    
    // Test 4: User table check
    console.log('\n4️⃣ Testing user table access...')
    const userCount = await prisma.user.count()
    console.log('✅ User table accessible. Count:', userCount)
    
    console.log('\n🎉 All Prisma tests passed!')
    
  } catch (error) {
    console.log('❌ Prisma connection failed:', error instanceof Error ? error.message : String(error))
    
    // Try direct PostgreSQL connection as fallback
    console.log('\n🔄 Trying direct PostgreSQL connection...')
    await testDirectPostgresConnection()
  }
  
  console.log('\n🔗 Testing Direct PostgreSQL Connection...')
  await testDirectPostgresConnection()
  
  await prisma.$disconnect()
}

async function testDirectPostgresConnection() {
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    console.error('❌ DATABASE_URL is not set');
    return;
  }

  const client = new Client({
    connectionString,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    await client.connect();
    console.log('✅ Direct PostgreSQL connection successful!');
    
    const res = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('🕒 Current Database Time:', res.rows[0].current_time);
    console.log('📊 PostgreSQL Version:', res.rows[0].pg_version.split(' ')[0]);

    // Test user table
    const userQuery = await client.query('SELECT COUNT(*) FROM users');
    console.log('👥 User Count:', userQuery.rows[0].count);

    await client.end();
    console.log('✅ Direct PostgreSQL tests completed successfully');
    
  } catch (error) {
    console.error('❌ Direct PostgreSQL connection failed:', error instanceof Error ? error.message : String(error));
    
    // Network diagnostics
    console.log('\n🌐 Network Diagnostics:');
    console.log('   - This appears to be a network connectivity issue');
    console.log('   - Railway may not be able to reach Supabase');
    console.log('   - Possible solutions:');
    console.log('     1. Check Supabase IP whitelist settings');
    console.log('     2. Ensure Supabase database is not paused');
    console.log('     3. Verify DATABASE_URL format');
    console.log('     4. Check Railway outbound network policies');
  }
}

// Run the test
testRailwayConnection()
  .then(() => {
    console.log('\n✅ Railway connection test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Railway connection test failed:', error);
    process.exit(1);
  }); 