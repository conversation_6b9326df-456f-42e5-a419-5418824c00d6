import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

interface VehicleData {
  name: string;
  make: string;
  model: string;
  year: string;
  engineSize: string;
  transmission: string;
  color: string;
  images: string[];
  price: number | null;
  specs: any[];
  brand: string;
}

async function seedVehicleCatalog() {
  try {
    console.log('🚀 Starting vehicle catalog seeding...');

    // Read the vehicle data from JSON file
    const jsonPath = path.join(__dirname, '../../../indonesia_scooters_essential.json');
    
    if (!fs.existsSync(jsonPath)) {
      console.error('❌ Vehicle data file not found:', jsonPath);
      return;
    }

    const jsonContent = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    const vehicleData: VehicleData[] = jsonContent.data || [];
    console.log(`📊 Found ${vehicleData.length} vehicles to seed`);

    if (vehicleData.length === 0) {
      console.log('❌ No vehicles found to seed');
      return;
    }

    let importedCount = 0;
    let skippedCount = 0;

    for (const vehicle of vehicleData) {
      try {
        // Check if vehicle already exists in vehicle_catalog
        const existingVehicle = await prisma.vehicleCatalog.findFirst({
          where: {
            make: vehicle.make,
            model: vehicle.model,
            year: vehicle.year?.toString() || null
          }
        });

        if (existingVehicle) {
          console.log(`⏭️  Skipping existing vehicle: ${vehicle.make} ${vehicle.model}`);
          skippedCount++;
          continue;
        }

        // Create vehicle catalog entry
        await prisma.vehicleCatalog.create({
          data: {
            make: vehicle.make,
            model: vehicle.model,
            year: vehicle.year?.toString() || null,
            category: 'Motorcycle',
            image_url: vehicle.images?.[0] || null,
            source: 'indonesia_scooters_essential.json',
            daily_rate: vehicle.price ? new Decimal(vehicle.price) : null,
            rating: null,
            reviews: 0,
            features: vehicle.specs ? Object.keys(vehicle.specs) : [],
            available_units: 1,
            has_insurance: false,
            insurance_price: new Decimal(0),
            location_city: 'Indonesia',
            is_reference_only: true
          }
        });

        console.log(`✅ Seeded: ${vehicle.make} ${vehicle.model}`);
        importedCount++;

      } catch (error) {
        console.error(`❌ Error seeding vehicle ${vehicle.make} ${vehicle.model}:`, error);
        skippedCount++;
      }
    }

    console.log('\n📈 Seeding Summary:');
    console.log(`✅ Successfully seeded: ${importedCount} vehicles`);
    console.log(`⏭️  Skipped (duplicates/errors): ${skippedCount} vehicles`);
    console.log(`📊 Total processed: ${vehicleData.length} vehicles`);

  } catch (error) {
    console.error('❌ Error during seeding:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  seedVehicleCatalog()
    .then(() => {
      console.log('🎉 Vehicle catalog seeding completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

export { seedVehicleCatalog }; 