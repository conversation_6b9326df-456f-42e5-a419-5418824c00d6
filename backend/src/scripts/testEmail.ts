#!/usr/bin/env tsx

// =============================================================================
// EMAIL SERVICE TEST SCRIPT
// =============================================================================
// Script to test email service configuration and functionality

import ProductionEmailService from '../services/ProductionEmailService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testEmailService() {
  console.log('📧 Testing Email Service Configuration...');
  console.log('=========================================');

  try {
    // Test connection
    console.log('🔍 Testing email service connection...');
    const connectionTest = await ProductionEmailService.testConnection();
    
    if (!connectionTest) {
      console.log('❌ Email service connection failed');
      console.log('\n📝 Email Configuration Required:');
      console.log('   Add these to your .env file:');
      console.log('   SMTP_HOST=smtp.gmail.com');
      console.log('   SMTP_PORT=587');
      console.log('   SMTP_USER=<EMAIL>');
      console.log('   SMTP_PASS=your-app-password');
      console.log('   FROM_EMAIL=<EMAIL>');
      console.log('   FROM_NAME=RentaHub');
      console.log('\n💡 For Gmail:');
      console.log('   1. Enable 2-factor authentication');
      console.log('   2. Generate an app password');
      console.log('   3. Use the app password as SMTP_PASS');
      return;
    }

    console.log('✅ Email service connection successful');

    // Test welcome email
    console.log('\n📨 Testing welcome email...');
    const testUser = {
      id: 'test-user-id',
      email: process.env.TEST_EMAIL || '<EMAIL>',
      name: 'Test User', // Use name instead of first_name/last_name
      password: 'test-password',
      role: 'CUSTOMER' as any,
      status: 'ACTIVE' as any,
      googleId: '',
      emailVerified: true,
      phoneVerified: false,
      phoneNumber: '+**********',
      profileImage: null,
      companyName: null,
      stripeCustomerId: null,
      stripeAccountId: null,
      bankAccountNumber: null,
      bankName: null,
      bankAccountHolderName: null,
      bankRoutingNumber: null,
      bankCountry: 'US',
      createdAt: new Date(),
      updatedAt: new Date()
    } as any;

    const welcomeSent = await ProductionEmailService.sendWelcomeEmail(testUser);
    if (welcomeSent) {
      console.log('✅ Welcome email sent successfully');
    } else {
      console.log('❌ Failed to send welcome email');
    }

    // Test email verification
    console.log('\n🔐 Testing email verification...');
    const verificationSent = await ProductionEmailService.sendEmailVerification(testUser, 'test-verification-token');
    if (verificationSent) {
      console.log('✅ Email verification sent successfully');
    } else {
      console.log('❌ Failed to send email verification');
    }

    // Test provider verification request
    console.log('\n🏢 Testing provider verification email...');
    const testProvider = {
      ...testUser,
      role: 'PROVIDER' as any,
      companyName: 'Test Rental Business', // Use companyName instead of business_name
      phoneNumber: '+***********', // Use phoneNumber instead of phone
      // city field doesn't exist in User model
    };

    const providerVerificationSent = await ProductionEmailService.sendProviderVerificationRequest(testProvider);
    if (providerVerificationSent) {
      console.log('✅ Provider verification emails sent successfully');
    } else {
      console.log('❌ Failed to send provider verification emails');
    }

    console.log('\n🎉 Email service testing completed!');
    console.log('====================================');
    console.log('✅ Connection tested');
    console.log('✅ Welcome email tested');
    console.log('✅ Verification email tested');
    console.log('✅ Provider notification tested');
    console.log('\n📧 Email service is ready for production!');

  } catch (error) {
    console.error('❌ Email service testing failed:', error);
    
    console.log('\n💡 Common Issues and Solutions:');
    console.log('   1. Gmail: Use app passwords, not regular password');
    console.log('   2. Outlook: Enable SMTP authentication');
    console.log('   3. Custom SMTP: Check server settings and ports');
    console.log('   4. Firewall: Ensure SMTP ports are not blocked');
    
    process.exit(1);
  }
}

async function sendTestEmails() {
  console.log('\n📬 Sending Test Emails...');
  console.log('==========================');

  const testEmail = process.env.TEST_EMAIL;
  if (!testEmail) {
    console.log('⚠️ TEST_EMAIL not set in environment variables');
    console.log('   Set TEST_EMAIL=<EMAIL> to receive test emails');
    return;
  }

  try {
    // Test booking confirmation
    console.log('📅 Sending test booking confirmation...');
    const testBooking = {
      id: 'test-booking-123',
      start_date: '2024-02-01',
      end_date: '2024-02-03',
      pickup_location: 'Jakarta Central Station',
      total_amount: 225000,
      customer_id: 'test-customer',
      provider_id: 'test-provider',
      vehicle_id: 'test-vehicle'
    };

    const testCustomer = {
      id: 'test-customer',
      email: testEmail,
      first_name: 'John',
      last_name: 'Doe',
      phone: '+***********'
    };

    const testProvider = {
      id: 'test-provider',
      email: '<EMAIL>',
      first_name: 'Ahmad',
      last_name: 'Rental',
      business_name: 'Jakarta Bike Rental',
      phone: '+***********'
    };

    const testVehicle = {
      id: 'test-vehicle',
      make: 'Honda',
      model: 'Scoopy',
      year: 2023,
      category: 'small_scooter'
    };

    const bookingConfirmationSent = await ProductionEmailService.sendBookingConfirmation(
      testBooking as any,
      testCustomer as any,
      testProvider as any,
      testVehicle as any
    );

    if (bookingConfirmationSent) {
      console.log('✅ Booking confirmation emails sent');
    } else {
      console.log('❌ Failed to send booking confirmation emails');
    }

    // Test payment confirmation
    console.log('💳 Sending test payment confirmation...');
    const testPayment = {
      id: 'test-payment-123',
      amount: 225000,
      currency: 'IDR',
      payment_status: 'completed'
    };

    const paymentConfirmationSent = await ProductionEmailService.sendPaymentConfirmation(
      testPayment as any,
      testBooking as any,
      testCustomer as any
    );

    if (paymentConfirmationSent) {
      console.log('✅ Payment confirmation email sent');
    } else {
      console.log('❌ Failed to send payment confirmation email');
    }

    console.log('\n✅ Test emails completed!');
    console.log(`📧 Check ${testEmail} for test emails`);

  } catch (error) {
    console.error('❌ Failed to send test emails:', error);
  }
}

// Run the tests
if (require.main === module) {
  console.log('🚀 Starting email service tests...\n');
  
  testEmailService()
    .then(() => sendTestEmails())
    .then(() => {
      console.log('\n✅ Email service testing completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Email service testing failed:', error);
      process.exit(1);
    });
}

export { testEmailService, sendTestEmails };
