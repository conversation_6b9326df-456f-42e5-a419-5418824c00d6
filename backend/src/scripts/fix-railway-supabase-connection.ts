import { PrismaClient } from '@prisma/client'
import 'dotenv/config'

const prisma = new PrismaClient()

async function fixRailwaySupabaseConnection() {
  console.log('🔧 Railway-Supabase Connection Fix')
  console.log('====================================')
  
  console.log('\n📋 Current Configuration:')
  console.log('   - DATABASE_URL:', process.env.DATABASE_URL?.replace(/(\/\/.*:)(.*)(@)/, '$1*****$3'))
  console.log('   - NODE_ENV:', process.env.NODE_ENV)
  console.log('   - RAILWAY_SERVICE_NAME:', process.env.RAILWAY_SERVICE_NAME)
  
  console.log('\n🔍 Testing current connection...')
  
  try {
    await prisma.$connect()
    console.log('✅ Connection successful! No fix needed.')
    return
  } catch (error) {
    console.log('❌ Connection failed. Applying fixes...')
  }
  
  console.log('\n🔧 Applying Railway-Supabase Connection Fixes:')
  console.log('===============================================')
  
  // Fix 1: Update DATABASE_URL with better SSL configuration
  console.log('\n1️⃣ SSL Configuration Fix:')
  console.log('   Current DATABASE_URL should include:')
  console.log('   - sslmode=require')
  console.log('   - ssl=true')
  console.log('   - connection_limit=5')
  console.log('   - pool_timeout=20')
  
  const currentUrl = process.env.DATABASE_URL || '';
  const hasSSL = currentUrl.includes('sslmode=require') && currentUrl.includes('ssl=true');
  console.log('   ✅ SSL properly configured:', hasSSL);
  
  // Fix 2: Check for connection pooling issues
  console.log('\n2️⃣ Connection Pooling Fix:')
  const hasPooling = currentUrl.includes('connection_limit') && currentUrl.includes('pool_timeout');
  console.log('   ✅ Connection pooling configured:', hasPooling);
  
  // Fix 3: Alternative connection methods
  console.log('\n3️⃣ Alternative Connection Methods:')
  console.log('   If the connection still fails, try these alternatives:')
  console.log('   ')
  console.log('   A. Use DIRECT_URL for direct connections:')
  console.log('      DIRECT_URL=****************************************/postgres?sslmode=require')
  console.log('   ')
  console.log('   B. Use connection pooling service:')
  console.log('      DATABASE_URL=****************************************/postgres?sslmode=require')
  console.log('   ')
  console.log('   C. Use Supabase connection pooler:')
  console.log('      DATABASE_URL=****************************************/postgres?sslmode=require&pgbouncer=true')
  
  // Fix 4: Railway-specific recommendations
  console.log('\n4️⃣ Railway-Specific Recommendations:')
  console.log('   ')
  console.log('   A. Check Supabase IP whitelist:')
  console.log('      - Go to Supabase Dashboard > Settings > Database')
  console.log('      - Add Railway IP ranges to whitelist')
  console.log('      - Common Railway IPs: 0.0.0.0/0 (allow all)')
  console.log('   ')
  console.log('   B. Enable Supabase connection pooling:')
  console.log('      - Go to Supabase Dashboard > Settings > Database')
  console.log('      - Enable "Connection Pooling"')
  console.log('      - Use port 6543 instead of 5432')
  console.log('   ')
  console.log('   C. Check Supabase database status:')
  console.log('      - Ensure database is not paused')
  console.log('      - Check for any maintenance windows')
  
  // Fix 5: Environment variable updates
  console.log('\n5️⃣ Environment Variable Updates:')
  console.log('   Add these to your Railway environment variables:')
  console.log('   ')
  console.log('   DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require&ssl=true&connection_limit=5&pool_timeout=20')
  console.log('   DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require')
  console.log('   SHADOW_DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require')
  
  // Fix 6: Test alternative connection
  console.log('\n6️⃣ Testing Alternative Connection...')
  
  // Create alternative Prisma client with different configuration
  const alternativePrisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    log: ['error', 'warn'],
  })
  
  try {
    await alternativePrisma.$connect()
    console.log('✅ Alternative connection successful!')
    console.log('💡 Try using this configuration in your main database.ts file')
  } catch (error) {
    console.log('❌ Alternative connection also failed')
    console.log('   Error:', error instanceof Error ? error.message : String(error))
  }
  
  await alternativePrisma.$disconnect()
  
  console.log('\n📋 Summary of Required Actions:')
  console.log('================================')
  console.log('1. ✅ Check SSL configuration in DATABASE_URL')
  console.log('2. ✅ Verify connection pooling settings')
  console.log('3. 🔄 Update Railway environment variables')
  console.log('4. 🔄 Check Supabase IP whitelist')
  console.log('5. 🔄 Enable Supabase connection pooling')
  console.log('6. 🔄 Test with alternative connection methods')
  
  console.log('\n🚀 Next Steps:')
  console.log('===============')
  console.log('1. Update Railway environment variables with the provided DATABASE_URL')
  console.log('2. Go to Supabase Dashboard and whitelist Railway IPs')
  console.log('3. Enable connection pooling in Supabase')
  console.log('4. Redeploy the Railway service')
  console.log('5. Test the connection again')
  
  await prisma.$disconnect()
}

// Run the fix
fixRailwaySupabaseConnection()
  .then(() => {
    console.log('\n✅ Railway-Supabase connection fix completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Railway-Supabase connection fix failed:', error);
    process.exit(1);
  }); 