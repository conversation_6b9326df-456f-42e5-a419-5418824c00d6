import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import axios from 'axios';

const prisma = new PrismaClient();

interface ScrapedVehicle {
  name: string;
  make: string;
  model: string;
  year: string;
  engineSize: string;
  transmission: string;
  color: string;
  images: string[];
  price: null;
  specs: any[];
  brand: string;
}

interface DatabaseVehicle {
  vehicleType: string;
  brand: string;
  model: string;
  type: string;
  year: string;
  engine: string;
  providerId: string;
  dailyRate: Decimal;
  weeklyRate: Decimal;
  monthlyRate: Decimal;
  yearlyRate: Decimal;
  depositAmount: Decimal;
  insurancePrice: Decimal;
  availableUnits: number;
  hasInsurance: boolean;
  insuranceOffered: boolean;
  deliveryAvailable: boolean;
  pickupAvailable: boolean;
  dropoffAvailable: boolean;
  fullTank: boolean;
  helmetsIncluded: number;
  raincoatsIncluded: boolean;
  addOns: string[];
  smartTags: string[];
  location_address?: string;
  location_city?: string;
  category?: string;
}

async function importVehicleData() {
  try {
    console.log('🚀 Starting vehicle data import...');

    // Fetch scraped data from our API
    const response = await axios.get('http://localhost:3001/api/vehicle-data/scrape/bikewale-main');
    const scrapedVehicles: ScrapedVehicle[] = response.data.data || [];

    console.log(`📊 Found ${scrapedVehicles.length} vehicles to import`);

    if (scrapedVehicles.length === 0) {
      console.log('❌ No vehicles found to import');
      return;
    }

    let importedCount = 0;
    let skippedCount = 0;

    for (const scrapedVehicle of scrapedVehicles) {
      try {
        // Transform scraped data to database format
        const dbVehicle: DatabaseVehicle = {
          vehicleType: 'Motorcycle',
          brand: scrapedVehicle.make || scrapedVehicle.brand,
          model: scrapedVehicle.model,
          type: scrapedVehicle.name, // Use name as type
          year: scrapedVehicle.year.toString(), // Convert to string
          engine: scrapedVehicle.engineSize || '125cc',
          providerId: 'system-import', // Default provider ID for imported vehicles
          dailyRate: new Decimal(0), // Will be set by actual providers
          weeklyRate: new Decimal(0),
          monthlyRate: new Decimal(0),
          yearlyRate: new Decimal(0),
          depositAmount: new Decimal(0),
          insurancePrice: new Decimal(0),
          availableUnits: 1,
          hasInsurance: false,
          insuranceOffered: false,
          deliveryAvailable: false,
          pickupAvailable: true,
          dropoffAvailable: true,
          fullTank: true,
          helmetsIncluded: 2,
          raincoatsIncluded: false,
          addOns: [],
          smartTags: [],
          location_address: 'Jakarta, Indonesia',
          location_city: 'Jakarta',
          category: 'Motorcycle'
        };

        // Check if vehicle already exists (by brand and model)
        const existingVehicle = await prisma.vehicle.findFirst({
          where: {
            brand: dbVehicle.brand,
            model: dbVehicle.model
          }
        });

        if (existingVehicle) {
          console.log(`⏭️  Skipping existing vehicle: ${dbVehicle.brand} ${dbVehicle.model}`);
          skippedCount++;
          continue;
        }

        // Import to database
        const createdVehicle = await prisma.vehicle.create({
          data: dbVehicle
        });

        // Import images if available
        if (scrapedVehicle.images && scrapedVehicle.images.length > 0) {
          for (let i = 0; i < scrapedVehicle.images.length; i++) {
            await prisma.vehicleImage.create({
              data: {
                vehicleId: createdVehicle.id,
                url: scrapedVehicle.images[i],
                caption: `${dbVehicle.brand} ${dbVehicle.model}`,
                isPrimary: i === 0 // First image is primary
              }
            });
          }
        }

        console.log(`✅ Imported: ${dbVehicle.brand} ${dbVehicle.model}`);
        importedCount++;

      } catch (error) {
        console.error(`❌ Error importing vehicle ${scrapedVehicle.name}:`, error);
        skippedCount++;
      }
    }

    console.log('\n📈 Import Summary:');
    console.log(`✅ Successfully imported: ${importedCount} vehicles`);
    console.log(`⏭️  Skipped (duplicates/errors): ${skippedCount} vehicles`);
    console.log(`📊 Total processed: ${scrapedVehicles.length} vehicles`);

  } catch (error) {
    console.error('❌ Error during import:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import if this script is executed directly
if (require.main === module) {
  importVehicleData()
    .then(() => {
      console.log('🎉 Import completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import failed:', error);
      process.exit(1);
    });
}

export { importVehicleData }; 