import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface ScrapedVehicle {
  id: number;
  brand: string;
  model: string;
  year: string;
  engine: string;
  type: string;
  category: string;
  images: string[];
}

interface MockVehicle {
  id: string;
  brand: string;
  model: string;
  name: string;
  description: string;
  images: string[];
  dailyRate: number;
  location: {
    city: string;
  };
  rating: number;
  reviews: number;
  features: string[];
  type: string;
  category: string;
  year: string;
  engine: string;
  availableUnits: number;
  hasInsurance: boolean;
  insurancePrice: number;
}

const seedVehicleCatalog = async () => {
  try {
    console.log('🚀 Starting vehicle catalog seeding...');

    // Load scraped data from existing comprehensive dataset
    const scrapedDataPath = path.join(__dirname, '../data/seAsiaVehicles.json');
    const scrapedData: ScrapedVehicle[] = JSON.parse(fs.readFileSync(scrapedDataPath, 'utf8'));
    
    console.log(`📦 Loaded ${scrapedData.length} scraped vehicles from existing dataset`);

    // Transform scraped data for catalog (removing pricing as requested)
    const scrapedVehicles = scrapedData.map(vehicle => ({
      make: vehicle.brand,
      model: vehicle.model,
      year: vehicle.year,
      engine: vehicle.engine,
      type: vehicle.type,
      category: vehicle.category,
      image_url: vehicle.images?.[0] || null,
      source: 'Southeast Asia Scrape',
      // Remove pricing fields - providers will set their own prices
      daily_rate: null,
      rating: null,
      reviews: null,
      features: [],
      available_units: null,
      has_insurance: null,
      insurance_price: null,
      location_city: 'Indonesia'
    }));

    // Additional mock vehicles for comprehensive coverage
    const mockVehicles = [
      {
        make: 'Yamaha',
        model: 'NMAX',
        year: '2024',
        engine: '155cc',
        type: 'scooter',
        category: 'medium',
        image_url: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Honda',
        model: 'PCX',
        year: '2024',
        engine: '160cc',
        type: 'scooter',
        category: 'large',
        image_url: 'https://images.unsplash.com/photo-1503736334956-4c8f8e92946d?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Suzuki',
        model: 'Burgman',
        year: '2024',
        engine: '200cc',
        type: 'scooter',
        category: 'large',
        image_url: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Kawasaki',
        model: 'Vulcan',
        year: '2024',
        engine: '650cc',
        type: 'motorcycle',
        category: 'luxury',
        image_url: 'https://images.unsplash.com/photo-1503736334956-4c8f8e92946d?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      }
    ];

    // Combine all vehicles
    const allVehicles = [...scrapedVehicles, ...mockVehicles];
    console.log(`📊 Total vehicles to seed: ${allVehicles.length}`);
    console.log(`  - Scraped vehicles: ${scrapedVehicles.length}`);
    console.log(`  - Mock vehicles: ${mockVehicles.length}`);

    // Clear existing data
    console.log('🧹 Clearing existing vehicle catalog...');
    const { error: deleteError } = await supabase
      .from('vehicle_catalog')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteError) {
      console.error('❌ Error clearing existing data:', deleteError);
      return;
    }

    // Insert new data
    console.log('📥 Inserting vehicle catalog data...');
    const { data, error } = await supabase
      .from('vehicle_catalog')
      .insert(allVehicles)
      .select();

    if (error) {
      console.error('❌ Error inserting data:', error);
      return;
    }

    console.log(`✅ Successfully seeded ${data?.length || 0} vehicles into catalog`);
    
    // Show some statistics
    const { data: stats } = await supabase
      .from('vehicle_catalog')
      .select('source, category, type')
      .order('source');

    if (stats) {
      const sourceCounts = stats.reduce((acc, item) => {
        acc[item.source] = (acc[item.source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const categoryCounts = stats.reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const typeCounts = stats.reduce((acc, item) => {
        acc[item.type] = (acc[item.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      console.log('📈 Seeding statistics:');
      console.log('  By source:');
      Object.entries(sourceCounts).forEach(([source, count]) => {
        console.log(`    ${source}: ${count} vehicles`);
      });
      
      console.log('  By category:');
      Object.entries(categoryCounts).forEach(([category, count]) => {
        console.log(`    ${category}: ${count} vehicles`);
      });
      
      console.log('  By type:');
      Object.entries(typeCounts).forEach(([type, count]) => {
        console.log(`    ${type}: ${count} vehicles`);
      });
    }

    // Show sample vehicles
    console.log('\n📋 Sample vehicles in catalog:');
    const { data: samples } = await supabase
      .from('vehicle_catalog')
      .select('make, model, year, engine, category, type')
      .limit(10);

    if (samples) {
      samples.forEach((vehicle, index) => {
        console.log(`  ${index + 1}. ${vehicle.make} ${vehicle.model} (${vehicle.year}) - ${vehicle.engine} ${vehicle.category} ${vehicle.type}`);
      });
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  }
};

// Run the seeding
seedVehicleCatalog().then(() => {
  console.log('🎉 Vehicle catalog seeding completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Seeding failed:', error);
  process.exit(1);
}); 