#!/usr/bin/env tsx

// =============================================================================
// DATABASE SETUP SCRIPT
// =============================================================================
// Automated script to set up the production database schema and sample data

import { readFileSync } from 'fs';
import { join } from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltfrq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function setupDatabase() {
  console.log('🗄️ Setting up RentaHub Production Database...');
  console.log('================================================');

  try {
    // Step 1: Create schema
    console.log('📋 Step 1: Creating database schema...');
    const schemaPath = join(process.cwd(), '../../database/production_schema.sql');
    const schemaSQL = readFileSync(schemaPath, 'utf8');
    
    // Split SQL into individual statements
    const schemaStatements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of schemaStatements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error && !error.message.includes('already exists')) {
          console.error(`❌ Schema error:`, error);
        }
      }
    }
    console.log('✅ Database schema created successfully');

    // Step 2: Insert sample data
    console.log('📊 Step 2: Inserting sample data...');
    const sampleDataPath = join(process.cwd(), '../../database/production_sample_data.sql');
    const sampleDataSQL = readFileSync(sampleDataPath, 'utf8');
    
    // Split SQL into individual statements
    const dataStatements = sampleDataSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of dataStatements) {
      if (statement.trim() && statement.includes('INSERT')) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error && !error.message.includes('duplicate key')) {
          console.error(`❌ Data insertion error:`, error);
        }
      }
    }
    console.log('✅ Sample data inserted successfully');

    // Step 3: Verify setup
    console.log('🔍 Step 3: Verifying database setup...');
    
    // Check users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Users table verification failed:', usersError);
    } else {
      console.log('✅ Users table verified');
    }

    // Check vehicles table
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('count')
      .limit(1);
    
    if (vehiclesError) {
      console.error('❌ Vehicles table verification failed:', vehiclesError);
    } else {
      console.log('✅ Vehicles table verified');
    }

    // Check bookings table
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('count')
      .limit(1);
    
    if (bookingsError) {
      console.error('❌ Bookings table verification failed:', bookingsError);
    } else {
      console.log('✅ Bookings table verified');
    }

    console.log('\n🎉 Database setup completed successfully!');
    console.log('================================================');
    console.log('📊 Summary:');
    console.log('   ✅ Database schema created');
    console.log('   ✅ Sample data inserted');
    console.log('   ✅ Tables verified');
    console.log('\n🚀 Your RentaHub database is ready for production!');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function setupDatabaseDirect() {
  console.log('🗄️ Setting up RentaHub Database (Direct Method)...');
  console.log('==================================================');

  try {
    // Create users table
    console.log('👥 Creating users table...');
    const { error: usersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          first_name VARCHAR(100),
          last_name VARCHAR(100),
          phone VARCHAR(20),
          role VARCHAR(20) NOT NULL DEFAULT 'CUSTOMER',
          email_verified BOOLEAN DEFAULT FALSE,
          phone_verified BOOLEAN DEFAULT FALSE,
          avatar_url TEXT,
          business_name VARCHAR(255),
          verification_status VARCHAR(20) DEFAULT 'PENDING',
          rating DECIMAL(3,2) DEFAULT 0.00,
          total_reviews INTEGER DEFAULT 0,
          total_vehicles INTEGER DEFAULT 0,
          total_bookings INTEGER DEFAULT 0,
          total_earnings DECIMAL(15,2) DEFAULT 0.00,
          city VARCHAR(100),
          country VARCHAR(100) DEFAULT 'Indonesia',
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_active BOOLEAN DEFAULT TRUE
        );
      `
    });

    if (usersError && !usersError.message.includes('already exists')) {
      console.error('❌ Users table error:', usersError);
    } else {
      console.log('✅ Users table created');
    }

    // Create vehicles table
    console.log('🚗 Creating vehicles table...');
    const { error: vehiclesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS vehicles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          provider_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          make VARCHAR(100) NOT NULL,
          model VARCHAR(100) NOT NULL,
          year INTEGER NOT NULL,
          category VARCHAR(50) NOT NULL,
          daily_rate DECIMAL(10,2) NOT NULL,
          status VARCHAR(20) DEFAULT 'available',
          location_city VARCHAR(100) NOT NULL,
          location_address TEXT,
          description TEXT,
          features TEXT[],
          images TEXT[],
          license_plate VARCHAR(20),
          engine_size VARCHAR(20),
          fuel_type VARCHAR(20),
          transmission VARCHAR(20),
          helmet_included BOOLEAN DEFAULT FALSE,
          insurance_included BOOLEAN DEFAULT FALSE,
          deposit_required DECIMAL(10,2) DEFAULT 0.00,
          rating DECIMAL(3,2) DEFAULT 0.00,
          total_reviews INTEGER DEFAULT 0,
          total_bookings INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_active BOOLEAN DEFAULT TRUE
        );
      `
    });

    if (vehiclesError && !vehiclesError.message.includes('already exists')) {
      console.error('❌ Vehicles table error:', vehiclesError);
    } else {
      console.log('✅ Vehicles table created');
    }

    // Create bookings table
    console.log('📅 Creating bookings table...');
    const { error: bookingsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS bookings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          customer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          provider_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          vehicle_id UUID NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          start_time TIME,
          end_time TIME,
          pickup_location TEXT,
          dropoff_location TEXT,
          status VARCHAR(20) DEFAULT 'pending',
          total_amount DECIMAL(12,2) NOT NULL,
          base_amount DECIMAL(12,2) NOT NULL,
          tax_amount DECIMAL(12,2) DEFAULT 0.00,
          service_fee DECIMAL(12,2) DEFAULT 0.00,
          payment_status VARCHAR(20) DEFAULT 'pending',
          payment_method VARCHAR(20),
          stripe_payment_intent_id VARCHAR(255),
          special_requests TEXT,
          cancellation_reason TEXT,
          cancelled_by UUID REFERENCES users(id),
          cancelled_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (bookingsError && !bookingsError.message.includes('already exists')) {
      console.error('❌ Bookings table error:', bookingsError);
    } else {
      console.log('✅ Bookings table created');
    }

    console.log('\n🎉 Database setup completed!');
    console.log('🚀 RentaHub database is ready!');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  console.log('🚀 Starting database setup...\n');
  
  // Try direct method first (more reliable)
  setupDatabaseDirect()
    .then(() => {
      console.log('\n✅ Database setup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Database setup failed:', error);
      process.exit(1);
    });
}

export { setupDatabase, setupDatabaseDirect };
