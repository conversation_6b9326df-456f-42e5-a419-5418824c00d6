import { PrismaClient } from '@prisma/client'
import { config } from 'dotenv'

// Load environment variables
config()

const prisma = new PrismaClient()

async function testConnection() {
  console.log('🔍 Testing database connection...')
  console.log('📋 DATABASE_URL:', process.env.DATABASE_URL?.replace(/(\/\/.*:)(.*)(@)/, '$1*****$3'))
  
  try {
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing basic connection...')
    await prisma.$connect()
    console.log('✅ Basic connection successful')
    
    // Test 2: Simple query
    console.log('\n2️⃣ Testing simple query...')
    const result = await prisma.$queryRaw`SELECT 1 as test, current_timestamp as time`
    console.log('✅ Query successful:', result)
    
    // Test 3: Check if tables exist
    console.log('\n3️⃣ Checking database schema...')
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `
    console.log('✅ Schema check successful. Tables found:', (tables as any[]).length)
    
    console.log('\n🎉 All connection tests passed!')
    return true
    
  } catch (error) {
    console.error('\n❌ Connection test failed:', error)
    
    // Provide specific error guidance
    if (error instanceof Error) {
      if (error.message.includes('P1001')) {
        console.log('\n💡 P1001 Error - Network/Firewall Issue:')
        console.log('   - Check if Supabase allows external connections')
        console.log('   - Verify SSL configuration')
        console.log('   - Check Railway IP whitelist in Supabase')
      } else if (error.message.includes('authentication')) {
        console.log('\n💡 Authentication Error:')
        console.log('   - Check DATABASE_URL credentials')
        console.log('   - Verify password is correct')
      } else if (error.message.includes('SSL')) {
        console.log('\n💡 SSL Error:')
        console.log('   - Add ?sslmode=require to DATABASE_URL')
        console.log('   - Check SSL certificate configuration')
      }
    }
    
    return false
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testConnection()
  .then(success => {
    process.exit(success ? 0 : 1)
  })
  .catch(error => {
    console.error('Test failed:', error)
    process.exit(1)
  }) 