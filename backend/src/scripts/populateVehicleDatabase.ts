import { VehicleDataService } from '../services/VehicleDataService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function populateVehicleDatabase() {
  try {
    console.log('🚀 Starting vehicle database population...');
    
    const vehicleDataService = VehicleDataService.getInstance();
    
    // Force refresh cache and populate database
    await vehicleDataService.forceRefreshCache();
    
    // Verify data was saved
    const vehicleCount = await prisma.vehicle.count();
    console.log(`✅ Database populated with ${vehicleCount} vehicles`);
    
    // Show sample vehicles
    const sampleVehicles = await prisma.vehicle.findMany({
      take: 5,
      select: {
        id: true,
        brand: true,
        model: true,
        dailyRate: true
      }
    });
    
    console.log('📋 Sample vehicles in database:');
    sampleVehicles.forEach(vehicle => {
      console.log(`  - ${vehicle.brand} ${vehicle.model}: $${vehicle.dailyRate}/day`);
    });
    
  } catch (error) {
    console.error('❌ Error populating database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  populateVehicleDatabase();
}

export { populateVehicleDatabase }; 