#!/usr/bin/env tsx

// =============================================================================
// DATABASE SEEDING SCRIPT
// =============================================================================
// Script to populate the database with sample data for testing

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltfrq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function seedDatabase() {
  console.log('🌱 Seeding RentaHub Database...');
  console.log('================================');

  try {
    // Step 1: Create sample users
    console.log('👥 Creating sample users...');
    
    const sampleUsers = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK',
        first_name: 'John',
        last_name: 'Doe',
        phone: '+***********',
        role: 'CUSTOMER',
        email_verified: true,
        city: 'Jakarta',
        country: 'Indonesia'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440010',
        email: '<EMAIL>',
        password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK',
        first_name: 'Ahmad',
        last_name: 'Rental',
        phone: '+***********',
        role: 'PROVIDER',
        email_verified: true,
        business_name: 'Jakarta Bike Rental',
        verification_status: 'VERIFIED',
        rating: 4.8,
        total_reviews: 45,
        total_vehicles: 8,
        total_earnings: 15750000.00,
        city: 'Jakarta',
        country: 'Indonesia'
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440020',
        email: '<EMAIL>',
        password_hash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK',
        first_name: 'Admin',
        last_name: 'RentaHub',
        phone: '+***********',
        role: 'ADMIN',
        email_verified: true,
        city: 'Jakarta',
        country: 'Indonesia'
      }
    ];

    for (const user of sampleUsers) {
      const { error } = await supabase
        .from('users')
        .upsert(user, { onConflict: 'email' });
      
      if (error && !error.message.includes('duplicate key')) {
        console.error(`❌ Error creating user ${user.email}:`, error);
      }
    }
    console.log('✅ Sample users created');

    // Step 2: Create sample vehicles
    console.log('🚗 Creating sample vehicles...');
    
    const sampleVehicles = [
      {
        id: '660e8400-e29b-41d4-a716-446655440001',
        provider_id: '550e8400-e29b-41d4-a716-446655440010',
        make: 'Honda',
        model: 'Scoopy',
        year: 2023,
        category: 'small_scooter',
        daily_rate: 75000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jl. Sudirman No. 123, Jakarta Pusat',
        description: 'Perfect city scooter for daily commuting. Fuel efficient and easy to park.',
        features: ['Helmet included', 'Fuel efficient', 'Easy to park', 'Storage compartment'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        license_plate: 'B 1234 ABC',
        engine_size: '110cc',
        fuel_type: 'gasoline',
        transmission: 'automatic',
        helmet_included: true,
        insurance_included: false,
        deposit_required: 200000,
        rating: 4.5,
        total_reviews: 12,
        total_bookings: 25
      },
      {
        id: '660e8400-e29b-41d4-a716-446655440002',
        provider_id: '550e8400-e29b-41d4-a716-446655440010',
        make: 'Yamaha',
        model: 'NMAX',
        year: 2023,
        category: 'large_scooter',
        daily_rate: 120000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jl. Thamrin No. 456, Jakarta Pusat',
        description: 'Comfortable scooter for longer rides with premium features.',
        features: ['Helmet included', 'Storage space', 'Comfortable seat', 'USB charging'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        license_plate: 'B 5678 DEF',
        engine_size: '155cc',
        fuel_type: 'gasoline',
        transmission: 'automatic',
        helmet_included: true,
        insurance_included: true,
        deposit_required: 300000,
        rating: 4.7,
        total_reviews: 18,
        total_bookings: 32
      },
      {
        id: '660e8400-e29b-41d4-a716-446655440003',
        provider_id: '550e8400-e29b-41d4-a716-446655440010',
        make: 'Kawasaki',
        model: 'Ninja 250',
        year: 2022,
        category: 'luxury_bike',
        daily_rate: 250000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jl. Gatot Subroto No. 789, Jakarta Selatan',
        description: 'Sport bike for enthusiasts. High performance and thrilling ride.',
        features: ['Full gear included', 'High performance', 'Sport riding position', 'ABS brakes'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        license_plate: 'B 9012 GHI',
        engine_size: '249cc',
        fuel_type: 'gasoline',
        transmission: 'manual',
        helmet_included: true,
        insurance_included: true,
        deposit_required: 500000,
        rating: 4.8,
        total_reviews: 8,
        total_bookings: 15
      }
    ];

    for (const vehicle of sampleVehicles) {
      const { error } = await supabase
        .from('vehicles')
        .upsert(vehicle, { onConflict: 'id' });
      
      if (error && !error.message.includes('duplicate key')) {
        console.error(`❌ Error creating vehicle ${vehicle.make} ${vehicle.model}:`, error);
      }
    }
    console.log('✅ Sample vehicles created');

    // Step 3: Create sample bookings
    console.log('📅 Creating sample bookings...');
    
    const sampleBookings = [
      {
        id: '770e8400-e29b-41d4-a716-446655440001',
        customer_id: '550e8400-e29b-41d4-a716-446655440001',
        provider_id: '550e8400-e29b-41d4-a716-446655440010',
        vehicle_id: '660e8400-e29b-41d4-a716-446655440001',
        start_date: '2024-01-15',
        end_date: '2024-01-17',
        start_time: '09:00',
        end_time: '18:00',
        pickup_location: 'Hotel Indonesia, Jakarta',
        dropoff_location: 'Hotel Indonesia, Jakarta',
        status: 'completed',
        total_amount: 165000,
        base_amount: 150000,
        tax_amount: 15000,
        service_fee: 0,
        payment_status: 'paid',
        payment_method: 'stripe',
        stripe_payment_intent_id: 'pi_test_completed_001'
      }
    ];

    for (const booking of sampleBookings) {
      const { error } = await supabase
        .from('bookings')
        .upsert(booking, { onConflict: 'id' });
      
      if (error && !error.message.includes('duplicate key')) {
        console.error(`❌ Error creating booking:`, error);
      }
    }
    console.log('✅ Sample bookings created');

    // Step 4: Verify data
    console.log('🔍 Verifying seeded data...');
    
    const { data: userCount } = await supabase
      .from('users')
      .select('id', { count: 'exact' });
    
    const { data: vehicleCount } = await supabase
      .from('vehicles')
      .select('id', { count: 'exact' });
    
    const { data: bookingCount } = await supabase
      .from('bookings')
      .select('id', { count: 'exact' });

    console.log('\n📊 Database seeding completed!');
    console.log('================================');
    console.log(`👥 Users: ${userCount?.length || 0}`);
    console.log(`🚗 Vehicles: ${vehicleCount?.length || 0}`);
    console.log(`📅 Bookings: ${bookingCount?.length || 0}`);
    console.log('\n🎉 Database is ready for testing!');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
}

// Run the seeding
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('\n✅ Database seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Database seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
