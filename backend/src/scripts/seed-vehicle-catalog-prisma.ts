import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
require('dotenv').config();

const prisma = new PrismaClient();

interface ScrapedVehicle {
  id: number;
  brand: string;
  model: string;
  year: string;
  engine: string;
  type: string;
  category: string;
  images: string[];
}

const seedVehicleCatalog = async () => {
  try {
    console.log('🚀 Starting vehicle catalog seeding with Prisma...');

    // Load scraped data from existing comprehensive dataset
    const scrapedDataPath = path.join(__dirname, '../data/seAsiaVehicles.json');
    const scrapedData: ScrapedVehicle[] = JSON.parse(fs.readFileSync(scrapedDataPath, 'utf8'));
    
    console.log(`📦 Loaded ${scrapedData.length} scraped vehicles from existing dataset`);

    // Transform scraped data for catalog (removing pricing as requested)
    const scrapedVehicles = scrapedData.map(vehicle => ({
      make: vehicle.brand,
      model: vehicle.model,
      year: vehicle.year,
      engine: vehicle.engine,
      type: vehicle.type,
      category: vehicle.category,
      image_url: vehicle.images?.[0] || null,
      source: 'Southeast Asia Scrape',
      // Remove pricing fields - providers will set their own prices
      daily_rate: null,
      rating: null,
      reviews: null,
      features: [],
      available_units: null,
      has_insurance: null,
      insurance_price: null,
      location_city: 'Indonesia'
    }));

    // Additional mock vehicles for comprehensive coverage
    const mockVehicles = [
      {
        make: 'Yamaha',
        model: 'NMAX',
        year: '2024',
        engine: '155cc',
        type: 'scooter',
        category: 'medium',
        image_url: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Honda',
        model: 'PCX',
        year: '2024',
        engine: '160cc',
        type: 'scooter',
        category: 'large',
        image_url: 'https://images.unsplash.com/photo-1503736334956-4c8f8e92946d?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Suzuki',
        model: 'Burgman',
        year: '2024',
        engine: '200cc',
        type: 'scooter',
        category: 'large',
        image_url: 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      },
      {
        make: 'Kawasaki',
        model: 'Vulcan',
        year: '2024',
        engine: '650cc',
        type: 'motorcycle',
        category: 'luxury',
        image_url: 'https://images.unsplash.com/photo-1503736334956-4c8f8e92946d?w=800',
        source: 'Mock',
        daily_rate: null,
        rating: null,
        reviews: null,
        features: [],
        available_units: null,
        has_insurance: null,
        insurance_price: null,
        location_city: 'Indonesia'
      }
    ];

    // Combine all vehicles
    const allVehicles = [...scrapedVehicles, ...mockVehicles];
    console.log(`📊 Total vehicles to seed: ${allVehicles.length}`);
    console.log(`  - Scraped vehicles: ${scrapedVehicles.length}`);
    console.log(`  - Mock vehicles: ${mockVehicles.length}`);

    // Clear existing data using Prisma
    console.log('🧹 Clearing existing vehicle catalog...');
    await prisma.vehicleCatalog.deleteMany({});

    // Insert new data using Prisma
    console.log('📥 Inserting vehicle catalog data...');
    const result = await prisma.vehicleCatalog.createMany({
      data: allVehicles,
      skipDuplicates: true
    });

    console.log(`✅ Successfully seeded ${result.count} vehicles into catalog`);
    
    // Show some statistics
    const stats = await prisma.vehicleCatalog.groupBy({
      by: ['source', 'category', 'type'],
      _count: true
    });

    console.log('📈 Seeding statistics:');
    
    // Group by source
    const sourceCounts = stats.reduce((acc, item) => {
      acc[item.source] = (acc[item.source] || 0) + item._count;
      return acc;
    }, {} as Record<string, number>);

    console.log('  By source:');
    Object.entries(sourceCounts).forEach(([source, count]) => {
      console.log(`    ${source}: ${count} vehicles`);
    });
    
    // Group by category
    const categoryCounts = stats.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + item._count;
      return acc;
    }, {} as Record<string, number>);

    console.log('  By category:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`    ${category}: ${count} vehicles`);
    });
    
    // Group by type
    const typeCounts = stats.reduce((acc, item) => {
      acc[item.type] = (acc[item.type] || 0) + item._count;
      return acc;
    }, {} as Record<string, number>);

    console.log('  By type:');
    Object.entries(typeCounts).forEach(([type, count]) => {
      console.log(`    ${type}: ${count} vehicles`);
    });

    // Show sample vehicles
    console.log('\n📋 Sample vehicles in catalog:');
    const samples = await prisma.vehicleCatalog.findMany({
      select: {
        make: true,
        model: true,
        year: true,
        engine: true,
        category: true,
        type: true
      },
      take: 10
    });

    samples.forEach((vehicle, index) => {
      console.log(`  ${index + 1}. ${vehicle.make} ${vehicle.model} (${vehicle.year}) - ${vehicle.engine} ${vehicle.category} ${vehicle.type}`);
    });

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
};

// Run the seeding
seedVehicleCatalog().then(() => {
  console.log('🎉 Vehicle catalog seeding completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Seeding failed:', error);
  process.exit(1);
}); 