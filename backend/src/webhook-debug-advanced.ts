import express, { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from 'stripe';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

const app = express();
const port = 3004;

// Stripe configuration
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

// Logging function to write detailed logs
function writeDebugLog(message: string, data?: any) {
  const logDir = path.join(__dirname, '..', 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const logFile = path.join(logDir, 'webhook-debug.log');
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - ${message}\n${data ? JSON.stringify(data, null, 2) : ''}\n\n`;
  
  fs.appendFileSync(logFile, logEntry);
  console.log(message, data);
}

// Middleware to parse raw body for Stripe webhooks
app.use(
  '/webhook',
  express.raw({ 
    type: 'application/json',
    verify: (req, res, buf) => {
      // Store raw body directly on the request for later verification
      (req as any).rawBody = buf;
    }
  })
);

// Advanced webhook debugging endpoint
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  const rawBody: Buffer = (req as any).rawBody;

  writeDebugLog('=== ADVANCED WEBHOOK DEBUG ===', {
    signature: sig,
    rawBodyLength: rawBody.length,
    contentType: req.headers['content-type']
  });

  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      writeDebugLog('ERROR: Webhook secret is not configured');
      return res.status(400).send('Webhook secret not configured');
    }

    // Detailed signature parsing
    const signingParts = sig.split(',');
    const signingMap = signingParts.reduce((acc, part) => {
      const [key, value] = part.split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    writeDebugLog('Signature Parsing', { 
      signingMap,
      rawBodyString: rawBody.toString('utf8').substring(0, 500) // Limit log size
    });

    // Manual signature verification
    const timestamp = signingMap['t'];
    const signedPayload = `${timestamp}.${rawBody.toString('utf8')}`;
    
    const computedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(signedPayload)
      .digest('hex');

    writeDebugLog('Signature Verification', {
      timestamp,
      computedSignature,
      receivedSignature: signingMap['v1']
    });

    // Stripe's official verification
    const event = stripe.webhooks.constructEvent(
      rawBody, 
      sig, 
      webhookSecret
    );

    writeDebugLog('Webhook Event Verified', {
      type: event.type,
      id: event.id,
      data: event.data.object
    });

    // Optional: Log event to file for further investigation
    const eventLogDir = path.join(__dirname, '..', 'logs', 'webhook-events');
    if (!fs.existsSync(eventLogDir)) {
      fs.mkdirSync(eventLogDir, { recursive: true });
    }
    const eventLogFile = path.join(eventLogDir, `${event.id}-${event.type}.json`);
    fs.writeFileSync(eventLogFile, JSON.stringify(event, null, 2));

    res.json({ received: true });
  } catch (error) {
    writeDebugLog('WEBHOOK VERIFICATION ERROR', {
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
      stack: (error as Error).stack
    });

    res.status(400).send(`Webhook Error: ${(error as Error).message}`);
  }
});

app.listen(port, () => {
  console.log(`Advanced Webhook Debugging Server running on port ${port}`);
});

export default app; 