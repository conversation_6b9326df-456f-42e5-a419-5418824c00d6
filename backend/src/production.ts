// =============================================================================
// PRODUCTION SERVER ENTRY POINT
// =============================================================================
// Main entry point for production deployment

import app from './server';

const PORT = parseInt(process.env.PORT || '3001', 10);

console.log('🚀 Starting RentaHub Production Server...');
console.log('=====================================');

// Start the production server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Production server running on port ${PORT}`);
  console.log(`📊 Health check: http://0.0.0.0:${PORT}/health`);
  console.log(`🔗 API endpoints: http://0.0.0.0:${PORT}/api`);
  console.log('🎉 RentaHub is ready for production!');
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
