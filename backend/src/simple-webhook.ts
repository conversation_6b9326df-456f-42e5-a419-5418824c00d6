import express, { Request, Response, NextFunction } from "express";
import <PERSON><PERSON> from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const port = 3001;

// This is your Stripe CLI webhook secret for testing your endpoint locally.
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Use raw body parser for Stripe webhooks
app.post('/webhook', express.raw({type: 'application/json'}), (request, response) => {
  const sig = request.headers['stripe-signature'];

  console.log('Received webhook!');
  console.log('Signature:', sig);
  
  if (!sig || !endpointSecret) {
    console.error('Missing signature or webhook secret');
    return response.status(400).send('Missing signature or webhook secret');
  }

  let event;

  try {
    // Verify the webhook signature
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '');
    event = stripe.webhooks.constructEvent(request.body, sig, endpointSecret);
    
    console.log('Webhook verified! Event type:', event.type);
    
    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('PaymentIntent was successful!', paymentIntent.id);
        break;
      case 'payment_method.attached':
        const paymentMethod = event.data.object as Stripe.PaymentMethod;
        console.log('PaymentMethod was attached!', paymentMethod.id);
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    response.json({received: true});
  } catch (err) {
    console.error('Webhook Error:', err);
    response.status(400).send(`Webhook Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
  }
});

app.listen(port, () => {
  console.log(`Simple webhook server running at http://localhost:${port}`);
  console.log(`Webhook endpoint: http://localhost:${port}/webhook`);
});
