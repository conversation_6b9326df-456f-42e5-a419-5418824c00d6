// Authentication Type Definitions
// This file contains all authentication-related type definitions

import { UserRole, UserStatus } from '@prisma/client';

// JWT Payload Interface
export interface JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  iat: number;
  exp: number;
}

// Login Request Interface
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Register Request Interface
export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  role?: UserRole;
  phoneNumber?: string;
  companyName?: string;
}

// Login Response Interface
export interface LoginResponse {
  user: AuthUser;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

// Auth User Interface
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  profileImage?: string;
  companyName?: string;
  phoneNumber?: string;
  lastLogin?: Date;
  createdAt: Date;
}

// Password Reset Request
export interface PasswordResetRequest {
  email: string;
}

// Password Reset Confirm
export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

// Email Verification Request
export interface EmailVerificationRequest {
  email: string;
}

// Email Verification Confirm
export interface EmailVerificationConfirm {
  token: string;
}

// Change Password Request
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Profile Update Request
export interface ProfileUpdateRequest {
  name?: string;
  phoneNumber?: string;
  companyName?: string;
  profileImage?: string;
}

// OAuth Provider Types
export enum OAuthProvider {
  GOOGLE = 'google',
  FACEBOOK = 'facebook',
  APPLE = 'apple'
}

// OAuth User Info
export interface OAuthUserInfo {
  id: string;
  email: string;
  name: string;
  picture?: string;
  provider: OAuthProvider;
}

// OAuth Login Request
export interface OAuthLoginRequest {
  provider: OAuthProvider;
  code?: string;
  accessToken?: string;
  idToken?: string;
}

// MFA Setup Request
export interface MFASetupRequest {
  method: 'sms' | 'email' | 'totp';
  phoneNumber?: string;
}

// MFA Verify Request
export interface MFAVerifyRequest {
  code: string;
  method: 'sms' | 'email' | 'totp';
}

// Session Info
export interface SessionInfo {
  id: string;
  userId: string;
  deviceInfo?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  lastActivity: Date;
  isActive: boolean;
}

// Auth Middleware Options
export interface AuthMiddlewareOptions {
  required?: boolean;
  roles?: UserRole[];
  permissions?: string[];
  skipEmailVerification?: boolean;
}

// Permission Interface
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

// Role Permission Interface
export interface RolePermission {
  role: UserRole;
  permissions: Permission[];
}

// Auth Service Interface
export interface AuthServiceInterface {
  login(credentials: LoginRequest): Promise<LoginResponse>;
  register(userData: RegisterRequest): Promise<AuthUser>;
  verifyToken(token: string): Promise<JwtPayload>;
  refreshToken(refreshToken: string): Promise<LoginResponse>;
  logout(userId: string): Promise<void>;
  requestPasswordReset(email: string): Promise<void>;
  resetPassword(token: string, newPassword: string): Promise<void>;
  verifyEmail(token: string): Promise<void>;
  changePassword(userId: string, passwords: ChangePasswordRequest): Promise<void>;
  updateProfile(userId: string, updates: ProfileUpdateRequest): Promise<AuthUser>;
}
