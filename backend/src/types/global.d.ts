// Custom type declarations for project-specific types and overrides

import { JwtPayload } from 'jsonwebtoken'

// Extend the default JWT payload with our custom claims
declare module 'jsonwebtoken' {
  export interface CustomJwtPayload extends JwtPayload {
    id: string;
    email: string;
    role: string;
    type: 'access' | 'refresh';
  }
}

// Extend global process.env with our custom environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      GOOGLE_CLIENT_ID: string;
      GOOGLE_CLIENT_SECRET: string;
      GOOGLE_REDIRECT_URI: string;
      JWT_SECRET: string;
      JWT_EXPIRATION: string;
      FRONTEND_OAUTH_REDIRECT_URL: string;
      FRONTEND_OAUTH_ERROR_URL: string;
    }
  }
} 