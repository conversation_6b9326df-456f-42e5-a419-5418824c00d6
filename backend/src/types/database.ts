// Basic database type definition for Supabase
export interface Database {
  public: {
    Tables: {
      [key: string]: any;
    };
    Views: {
      [key: string]: any;
    };
    Functions: {
      [key: string]: any;
    };
  };
}

// Add missing types that are being imported
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
}

export interface Vehicle {
  id: string;
  brand: string;
  model: string;
  name: string;
  description: string;
  dailyRate: number;
  type: string;
  category: string;
  year: string;
  engine: string;
  availableUnits: number;
  hasInsurance: boolean;
  insurancePrice: number;
}

export interface Booking {
  id: string;
  userId: string;
  vehicleId: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  status: BookingStatus;
  paymentStatus: string;
}

export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}
