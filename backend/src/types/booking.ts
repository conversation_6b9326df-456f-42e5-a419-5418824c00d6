import { Booking } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

export interface MultiVehicleBookingRequest {
  vehicles: {
    vehicleId: string;
    startDate: Date;
    endDate: Date;
  }[];
  userId: string;
  paymentMethod: string;
  totalPrice: number;
}

export interface MultiVehicleBookingResult {
  bookings: Booking[];
  totalPrice: Decimal;
  paymentIntentId?: string;
  status: 'success' | 'partial' | 'failed';
  errors?: string[];
}

export interface CreateBookingData {
  userId: string;
  providerId: string;
  vehicleId: string;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  paymentMethod: string;
  stripePaymentIntentId?: string;
  pointsRedeemed?: number;
  discountApplied?: number;
}

export interface BookingAvailabilityCheck {
  vehicleId: string;
  startDate: Date;
  endDate: Date;
  isAvailable: boolean;
  conflictingBookings?: any[];
}
