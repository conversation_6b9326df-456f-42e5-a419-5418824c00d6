// API Type Definitions
// This file contains all API-related type definitions

import { Request, Response, NextFunction } from 'express';
import { UserRole as PrismaUserRole, UserStatus as PrismaUserStatus } from '@prisma/client';

// Standard API Response Interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: string[];
  meta?: ApiMeta;
  timestamp: string;
}

// API Metadata Interface
export interface ApiMeta {
  page?: number;
  limit?: number;
  total?: number;
  totalPages?: number;
  source?: string;
  version?: string;
}

// Paginated Request Query
export interface PaginatedQuery {
  page?: string;
  limit?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

// Search Query Interface
export interface SearchQuery extends PaginatedQuery {
  q?: string;
  filter?: Record<string, any>;
  fields?: string;
  include?: string;
  exclude?: string;
  [key: string]: any; // Index signature for compatibility with ParsedQs
}

// Extended Request Interface
export interface ApiRequest<T = any> extends Request {
  user?: AuthenticatedUser;
  body: T;
  query: SearchQuery;
}

// Extended Response Interface
export interface ApiResponseHandler extends Response {
  success: <T>(data?: T, message?: string, statusCode?: number, meta?: ApiMeta) => Response;
  error: (error: string, statusCode?: number, errors?: string[]) => Response;
  paginated: <T>(data: T[], page: number, limit: number, total: number, message?: string) => Response;
}

// Route Handler Type
export type RouteHandler<T = any> = (
  req: ApiRequest<T>,
  res: ApiResponseHandler,
  next: NextFunction
) => Promise<void> | void;

// Async Route Handler Type
export type AsyncRouteHandler<T = any> = (
  req: ApiRequest<T>,
  res: ApiResponseHandler,
  next: NextFunction
) => Promise<void>;

// Controller Method Type
export type ControllerMethod<T = any> = AsyncRouteHandler<T>;

// Service Method Type
export type ServiceMethod<TInput = any, TOutput = any> = (
  input: TInput
) => Promise<TOutput>;

// Authenticated User Interface
export interface AuthenticatedUser {
  id: string;
  email: string;
  role: PrismaUserRole; // Use Prisma's UserRole enum
  status: PrismaUserStatus; // Use Prisma's UserStatus enum
  emailVerified: boolean;
  phoneVerified: boolean;
}

// Use Prisma's UserRole and UserStatus enums instead of local definitions
// These are imported from @prisma/client above

// Error Types
export enum ApiErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR'
}

// API Error Interface
export interface ApiError extends Error {
  type: ApiErrorType;
  statusCode: number;
  errors?: string[];
  meta?: Record<string, any>;
}

// Validation Error Interface
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// File Upload Interface
export interface FileUpload {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  destination: string;
  filename: string;
  path: string;
  buffer: Buffer;
}

// Health Check Response
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'connected' | 'disconnected';
    redis?: 'connected' | 'disconnected';
    stripe?: 'configured' | 'not_configured';
    email?: 'configured' | 'not_configured';
  };
}

// Rate Limit Info
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}
