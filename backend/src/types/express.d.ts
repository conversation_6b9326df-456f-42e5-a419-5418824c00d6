import { Request, Response, NextFunction } from 'express';
import { User, UserRole } from '@prisma/client';

// Define ExtendedUser with necessary properties for authenticated requests
export interface ExtendedUser {
  id: string;
  email: string;
  role: UserRole;
  name?: string;
  status?: string;
  getDisplayName?(): string;
}

declare global {
  namespace Express {
    interface Request {
      user?: ExtendedUser;
      session?: {
        oauthState?: {
          state?: string;
          token?: string;
          timestamp?: number;
          createdAt?: number;
          provider?: string;
        };
      };
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: ExtendedUser;
}

export interface SessionRequest extends Request {
  session: {
    oauthState?: {
      state?: string;
      token?: string;
      timestamp?: number;
      createdAt?: number;
      provider?: string;
    };
  };
  ip?: string;
}

export interface ExtendedRequest extends Request {
  user?: ExtendedUser;
}

export interface ExtendedResponse extends Response {}
export interface ExtendedNextFunction extends NextFunction {}
