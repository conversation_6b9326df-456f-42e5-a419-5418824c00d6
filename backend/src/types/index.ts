// Main Types Barrel Export
// This file provides a centralized export point for all type definitions

// API Types
export * from './api';

// Authentication Types
export * from './auth';

// Database Types (re-export Prisma types)
export type {
  User,
  Vehicle,
  Booking,
  Payment,
  Review,
  VehicleStatus,
  BookingStatus,
  PaymentStatus,
  UserRole,
  UserStatus
} from '@prisma/client';

// Common Utility Types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type ID = string;
export type Timestamp = Date;

// Generic CRUD Operations
export interface CreateInput<T> {
  data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
}

export interface UpdateInput<T> {
  id: ID;
  data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;
}

export interface DeleteInput {
  id: ID;
}

export interface FindInput {
  id: ID;
}

export interface ListInput {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filter?: Record<string, any>;
}

// Service Response Types
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  meta?: Record<string, any>;
}

// Database Connection Types
export interface DatabaseConfig {
  url: string;
  ssl?: boolean;
  poolSize?: number;
  timeout?: number;
}

// Cache Types
export interface CacheConfig {
  ttl: number;
  maxSize?: number;
  prefix?: string;
}

// File Storage Types
export interface FileStorageConfig {
  provider: 'local' | 's3' | 'gcs';
  bucket?: string;
  region?: string;
  maxSize: number;
  allowedTypes: string[];
}

// Email Configuration Types
export interface EmailConfig {
  provider: 'smtp' | 'sendgrid' | 'ses';
  host?: string;
  port?: number;
  secure?: boolean;
  auth?: {
    user: string;
    pass: string;
  };
  from: string;
}

// SMS Configuration Types
export interface SMSConfig {
  provider: 'twilio' | 'aws-sns';
  accountSid?: string;
  authToken?: string;
  from: string;
}

// Payment Configuration Types
export interface PaymentConfig {
  stripe?: {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
  };
  paypal?: {
    clientId: string;
    clientSecret: string;
    sandbox: boolean;
  };
}

// Monitoring Types
export interface MonitoringConfig {
  sentry?: {
    dsn: string;
    environment: string;
  };
  analytics?: {
    provider: string;
    apiKey: string;
  };
}
