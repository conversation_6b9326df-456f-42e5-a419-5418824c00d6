import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/database'

// Validate environment variables with fallbacks for Railway deployment
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables:');
  console.error('SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.error('SUPABASE_ANON_KEY:', supabaseAnonKey ? '✅ Set' : '❌ Missing');
  console.error('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing');
  console.error('Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')));
  throw new Error('Missing Supabase environment variables: SUPABASE_URL and SUPABASE_ANON_KEY are required')
}

// Create Supabase client with service role key for backend operations
export const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey || supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false, // Backend doesn't need session persistence
    detectSessionInUrl: false
  },
  db: {
    schema: 'public'
  }
})

// Create public client for user-facing operations
export const supabasePublic = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database connection test
export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...')
    
    // Test 1: Basic connection
    const { data, error } = await supabase.from('User').select('count').limit(1)
    
    if (error) {
      console.log('❌ Supabase connection failed:', error.message)
      return false
    }
    
    console.log('✅ Supabase connection successful')
    console.log('📊 User count:', data?.length || 0)
    
    // Test 2: Check if tables exist
    const { data: tables, error: tablesError } = await supabase
      .from('User')
      .select('id')
      .limit(1)
    
    if (tablesError) {
      console.log('❌ Table access failed:', tablesError.message)
      return false
    }
    
    console.log('✅ Database schema accessible')
    return true
    
  } catch (error) {
    console.error('❌ Supabase connection test failed:', error)
    return false
  }
}

// Health check function
export const getDatabaseHealth = async () => {
  try {
    const { data, error } = await supabase.from('User').select('count').limit(1)
    
    if (error) {
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString()
      }
    }
    
    return {
      status: 'ok',
      message: 'Supabase connection healthy',
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }
  }
}

export default supabase 