import { PrismaClient } from '@prisma/client'

// Configure Prisma with optimized connection settings
const prismaClientSingleton = () => {
  return new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  })
}

declare global {
  var __prisma: undefined | ReturnType<typeof prismaClientSingleton>
}

const prisma = globalThis.__prisma ?? prismaClientSingleton()

if (process.env.NODE_ENV !== 'production') globalThis.__prisma = prisma

// Enhanced database connection with Railway-specific retry logic
export const connectDatabase = async (maxRetries = 15, retryDelay = 2000) => {
  console.log('🚀 Railway Database Connection Attempt')
  console.log('📍 Environment:', process.env.NODE_ENV)
  console.log('🔗 Railway Service:', process.env.RAILWAY_SERVICE_NAME)
  console.log('🌐 Railway Domain:', process.env.RAILWAY_PUBLIC_DOMAIN)
  
  // Log connection details (masked for security)
  const dbUrl = process.env.DATABASE_URL || '';
  const maskedUrl = dbUrl.replace(/(\/\/.*:)(.*)(@)/, '$1*****$3');
  console.log('🔗 Database URL (masked):', maskedUrl);
  console.log('🔒 SSL Mode:', dbUrl.includes('sslmode=require') ? 'Required' : 'Not specified');
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔗 Database connection attempt ${attempt}/${maxRetries}...`)
      
      // Test connection with timeout
      const connectionPromise = prisma.$connect()
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout after 30 seconds')), 30000)
      )
      
      await Promise.race([connectionPromise, timeoutPromise])
      
      console.log('✅ Database connected successfully')
      
      // Test a simple query to ensure full connectivity
      const testResult = await prisma.$queryRaw`SELECT 1 as test, current_timestamp as time`
      console.log('✅ Database query test successful:', testResult)
      
      // Test if we can access the schema
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
        LIMIT 5
      `
      console.log('✅ Schema access confirmed. Tables found:', (tables as any[]).length)
      
      return true
    } catch (error) {
      console.log(`❌ Database connection attempt ${attempt} failed:`)
      console.log('   Error:', error instanceof Error ? error.message : String(error))
      
      // Provide specific Railway debugging info on first attempt
      if (attempt === 1) {
        console.log('🔍 Railway Debug Info:')
        console.log('   - Service:', process.env.RAILWAY_SERVICE_NAME)
        console.log('   - Environment:', process.env.RAILWAY_ENVIRONMENT)
        console.log('   - Public Domain:', process.env.RAILWAY_PUBLIC_DOMAIN)
        console.log('   - Private Domain:', process.env.RAILWAY_PRIVATE_DOMAIN)
        console.log('   - Project ID:', process.env.RAILWAY_PROJECT_ID)
        console.log('   - Service ID:', process.env.RAILWAY_SERVICE_ID)
        
        // Check if it's a network connectivity issue
        if (error instanceof Error) {
          const errorMessage = error.message.toLowerCase();
          if (errorMessage.includes('timeout') || errorMessage.includes('connection')) {
            console.log('🌐 Network Issue Detected:')
            console.log('   - This appears to be a network connectivity issue')
            console.log('   - Railway may not be able to reach Supabase')
            console.log('   - Check Supabase IP whitelist settings')
          }
        }
      }
      
      if (attempt < maxRetries) {
        const nextDelay = retryDelay * Math.pow(1.2, attempt - 1) // More gradual backoff
        console.log(`⏳ Retrying in ${Math.round(nextDelay/1000)} seconds...`)
        await new Promise(resolve => setTimeout(resolve, nextDelay))
      } else {
        console.log('❌ All database connection attempts failed')
        console.log('💡 Railway + Supabase Troubleshooting:')
        console.log('   1. Check if Supabase allows external connections')
        console.log('   2. Verify SSL configuration in DATABASE_URL')
        console.log('   3. Check Railway IP whitelist in Supabase')
        console.log('   4. Ensure Supabase database is not paused')
        console.log('   5. Check Supabase connection pool settings')
        console.log('   6. Verify DATABASE_URL format and credentials')
        return false
      }
    }
  }
  return false
}

// Simple connection test
export const testConnection = async () => {
  try {
    await prisma.$queryRaw`SELECT 1 as test`
    return true
  } catch (error) {
    console.error('Connection test failed:', error)
    return false
  }
}

export default prisma 