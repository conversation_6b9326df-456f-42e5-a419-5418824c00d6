import express, { Request, Response, NextFunction } from "express";
import Strip<PERSON> from 'stripe';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const port = 3003;

// Stripe configuration
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

// Middleware to parse raw body for Stripe webhooks
app.use(
  '/webhook',
  express.raw({ 
    type: 'application/json',
    verify: (req, res, buf) => {
      // Store raw body directly on the request for later verification
      (req as any).rawBody = buf;
    }
  })
);

// Detailed webhook debugging endpoint
app.post('/webhook', async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  const rawBody: Buffer = (req as any).rawBody;

  console.log('=== WEBHOOK DEBUG ===');
  console.log('Received Signature:', sig);
  console.log('Raw Body Length:', rawBody.length);
  console.log('Raw Body:', rawBody.toString('utf8'));
  console.log('Content-Type:', req.headers['content-type']);

  // Detailed signature verification logging
  try {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('Webhook secret is not configured');
      return res.status(400).send('Webhook secret not configured');
    }

    // Manual signature verification for debugging
    const signingParts = sig.split(',');
    const signingMap = signingParts.reduce((acc, part) => {
      const [key, value] = part.split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    console.log('Signing Map:', signingMap);

    // Compute the expected signature
    const timestamp = signingMap['t'];
    const signedPayload = `${timestamp}.${rawBody.toString('utf8')}`;
    
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(signedPayload)
      .digest('hex');

    console.log('Expected Signature:', expectedSignature);
    console.log('Received Signature:', signingMap['v1']);

    // Verify the signature
    const event = stripe.webhooks.constructEvent(
      rawBody, 
      sig, 
      webhookSecret
    );

    console.log('Webhook Event Verified:', {
      type: event.type,
      id: event.id
    });

    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        console.log('Payment Intent Succeeded', event.data.object);
        break;
      
      case 'charge.succeeded':
        console.log('Charge Succeeded', event.data.object);
        break;
      
      case 'payment_intent.created':
        console.log('Payment Intent Created', event.data.object);
        break;

      case 'charge.updated':
        console.log('Charge Updated', event.data.object);
        break;
      
      default:
        console.log('Unhandled Webhook Event', event.type);
    }

    res.json({ received: true });
  } catch (err: any) {
    console.error('Webhook Verification Error', {
      message: err.message,
      name: err.name,
      rawBody: rawBody.toString('utf8'),
      signature: sig
    });

    res.status(400).send(`Webhook Error: ${err.message}`);
  }
});

app.listen(port, () => {
  console.log(`Webhook Debug Server running at http://localhost:${port}`);
  console.log(`Webhook endpoint: http://localhost:${port}/webhook`);
});

export default app; 