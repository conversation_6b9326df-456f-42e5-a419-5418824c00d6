// =============================================================================
// RENTAHUB PRODUCTION SERVER
// =============================================================================
// Full-featured Express.js server with proper architecture

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import cookieParser from 'cookie-parser';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// =============================================================================
// SERVER CONFIGURATION
// =============================================================================

const app = express();
const PORT = parseInt(process.env.PORT || '3001', 10);

// Trust proxy for Railway deployment (fixes X-Forwarded-For header warning)
app.set('trust proxy', true);

// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Initialize Supabase client
export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🔗 Supabase client initialized for production');

// =============================================================================
// SECURITY & MIDDLEWARE
// =============================================================================

// Security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https:"],
      scriptSrc: ["'self'", "https:"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:"],
      fontSrc: ["'self'", "https:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Compression
app.use(compression());

// CORS Configuration
const corsOptions = {
  origin: [
    'https://rentahub.info',
    'https://admin.rentahub.info',
    'http://localhost:5173', // Development frontend
    'http://localhost:3002'  // Development admin
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'x-user-id', // Required for user identification
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Request logging middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const ip = req.ip || req.connection.remoteAddress;
  
  console.log(`📡 [${timestamp}] ${method} ${url} - ${ip}`);
  
  // Log request body for POST/PUT requests (excluding sensitive data)
  if (['POST', 'PUT', 'PATCH'].includes(method) && req.body) {
    const sanitizedBody = { ...req.body };
    // Remove sensitive fields
    delete sanitizedBody.password;
    delete sanitizedBody.token;
    delete sanitizedBody.apiKey;
    console.log(`📝 Request body:`, sanitizedBody);
  }
  
  next();
});

// =============================================================================
// HEALTH CHECK & STATUS
// =============================================================================

app.get('/health', async (req: Request, res: Response) => {
  try {
    // Test database connection
    const { data, error } = await supabase
      .from('Vehicle')
      .select('count')
      .limit(1);

    const dbStatus = error ? 'disconnected' : 'connected';
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      database: dbStatus,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      services: {
        supabase: dbStatus,
        stripe: process.env.STRIPE_SECRET_KEY ? 'configured' : 'not configured',
        email: process.env.SMTP_HOST ? 'configured' : 'not configured'
      }
    });
  } catch (error) {
    console.error('❌ Health check failed:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Service unavailable'
    });
  }
});

// API status endpoint
app.get('/api/status', (req: Request, res: Response) => {
  res.json({
    api: 'RentaHub Production API',
    version: '1.0.0',
    status: 'operational',
    timestamp: new Date().toISOString(),
    endpoints: {
      vehicles: '/api/vehicles',
      bookings: '/api/bookings',
      users: '/api/users',
      providers: '/api/providers',
      payments: '/api/payments',
      analytics: '/api/analytics'
    }
  });
});

// =============================================================================
// API ROUTES
// =============================================================================

import productionRoutes from './routes/production';

// Mount production API routes
app.use('/api', productionRoutes);

// =============================================================================
// ERROR HANDLING MIDDLEWARE
// =============================================================================

// 404 handler
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `The requested endpoint ${req.method} ${req.originalUrl} does not exist`,
    timestamp: new Date().toISOString()
  });
});

// Global error handler
app.use((error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('❌ Server Error:', error);
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(error.status || 500).json({
    success: false,
    error: isDevelopment ? error.message : 'Internal server error',
    ...(isDevelopment && { stack: error.stack }),
    timestamp: new Date().toISOString()
  });
});

// =============================================================================
// SERVER STARTUP
// =============================================================================

const startServer = async () => {
  try {
    // Test Supabase connection
    console.log('🔍 Testing database connection...');
    const { data, error } = await supabase
      .from('Vehicle')
      .select('count')
      .limit(1);
    
    if (error) {
      console.warn('⚠️ Database connection warning:', error.message);
    } else {
      console.log('✅ Database connection successful');
    }

    // Start server
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('🚀 RentaHub Production Server Started');
      console.log('=====================================');
      console.log(`📍 Port: ${PORT}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 Health Check: http://0.0.0.0:${PORT}/health`);
      console.log(`🔗 API Status: http://0.0.0.0:${PORT}/api/status`);
      console.log('✅ All systems operational');
    });

    // Graceful shutdown
    const shutdown = (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Gracefully shutting down...`);
      server.close(() => {
        console.log('🔌 HTTP server closed');
        console.log('👋 Goodbye!');
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
if (require.main === module) {
  startServer();
}

export default app;
