// Production server with real Supabase data
import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';

const app = express();
const PORT = 3001;

// Supabase configuration with hardcoded credentials for production
const SUPABASE_URL = 'https://rocxjzukyqelvuyltfrq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Initialize Supabase client with service role key for admin access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🔗 Supabase client initialized for production data access');

// CORS Configuration for production domains
const corsOptions = {
  origin: [
    'https://rentahub.info',
    'https://admin.rentahub.info',
    'http://localhost:5173', // Development frontend
    'http://localhost:3002'  // Development admin
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200
};

// Basic middleware
app.use(cors(corsOptions));
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📡 ${req.method} ${req.url}`, req.body);
  next();
});

// Test route
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Minimal server running' });
});

// Test booking route
app.post('/api/bookings', (req, res) => {
  console.log('📝 Booking request received:', req.body);

  // Simulate successful booking creation
  const booking = {
    id: 'booking_' + Date.now(),
    vehicleId: req.body.vehicleId,
    userId: req.body.userId,
    startDate: req.body.startDate,
    endDate: req.body.endDate,
    status: 'CONFIRMED',
    totalPrice: req.body.totalPrice || 150000,
    createdAt: new Date().toISOString()
  };

  res.json({
    success: true,
    message: 'Booking created successfully',
    data: { booking }
  });
});

// Test saved vehicles routes
app.get('/api/saved-vehicles', (req, res) => {
  console.log('📋 Saved vehicles request received');
  res.json({
    success: true,
    data: [
      {
        id: 'sv1',
        vehicleId: 'honda-vario-160',
        userId: 'user123',
        vehicle: {
          id: 'honda-vario-160',
          name: 'Honda Vario 160',
          dailyRate: 150000,
          provider: { businessName: 'Jakarta Rentals' }
        }
      }
    ]
  });
});

app.delete('/api/saved-vehicles/:id', (req, res) => {
  console.log('🗑️ Remove saved vehicle request:', req.params.id);
  res.json({
    success: true,
    message: 'Vehicle removed from saved list'
  });
});

// Get dynamic price ranges by category
app.get('/api/vehicles/price-ranges', (req, res) => {
  console.log('📊 Price ranges request received');

  // Simulate realistic market fluctuations
  const baseTime = Date.now();
  const hourOfDay = new Date().getHours();
  const dayOfWeek = new Date().getDay();

  // Peak hours and weekend adjustments
  const peakMultiplier = (hourOfDay >= 7 && hourOfDay <= 9) || (hourOfDay >= 17 && hourOfDay <= 19) ? 1.2 : 1.0;
  const weekendMultiplier = (dayOfWeek === 0 || dayOfWeek === 6) ? 1.15 : 1.0;
  const demandMultiplier = peakMultiplier * weekendMultiplier;

  // Mock dynamic price ranges with realistic market simulation
  const priceRanges = {
    small_scooter: {
      min: Math.round(45000 * demandMultiplier),
      max: Math.round(85000 * demandMultiplier),
      count: Math.floor(Math.random() * 10) + 20, // 20-30 vehicles
      avgPrice: Math.round(62000 * demandMultiplier)
    },
    large_scooter: {
      min: Math.round(75000 * demandMultiplier),
      max: Math.round(150000 * demandMultiplier),
      count: Math.floor(Math.random() * 8) + 15, // 15-23 vehicles
      avgPrice: Math.round(110000 * demandMultiplier)
    },
    luxury_bike: {
      min: Math.round(180000 * demandMultiplier),
      max: Math.round(400000 * demandMultiplier),
      count: Math.floor(Math.random() * 6) + 8, // 8-14 vehicles
      avgPrice: Math.round(275000 * demandMultiplier)
    }
  };

  console.log('📈 Dynamic pricing applied:', {
    peakMultiplier,
    weekendMultiplier,
    totalMultiplier: demandMultiplier,
    timeOfDay: `${hourOfDay}:00`,
    dayOfWeek: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek]
  });

  res.json({
    success: true,
    data: priceRanges,
    metadata: {
      lastUpdated: new Date().toISOString(),
      demandMultiplier: demandMultiplier.toFixed(2),
      marketConditions: demandMultiplier > 1.1 ? 'High Demand' : 'Normal',
      totalVehicles: Object.values(priceRanges).reduce((sum, range) => sum + range.count, 0)
    }
  });
});

// Test endpoint to simulate different market conditions
app.get('/api/vehicles/price-ranges/test/:condition', (req, res) => {
  console.log('🧪 Test pricing condition:', req.params.condition);

  let multiplier = 1.0;
  let condition = 'Normal';

  switch (req.params.condition) {
    case 'peak':
      multiplier = 1.3;
      condition = 'Peak Hours';
      break;
    case 'weekend':
      multiplier = 1.15;
      condition = 'Weekend';
      break;
    case 'holiday':
      multiplier = 1.5;
      condition = 'Holiday Rush';
      break;
    case 'low':
      multiplier = 0.8;
      condition = 'Low Demand';
      break;
    default:
      multiplier = 1.0;
      condition = 'Normal';
  }

  const testRanges = {
    small_scooter: {
      min: Math.round(45000 * multiplier),
      max: Math.round(85000 * multiplier),
      count: Math.floor(Math.random() * 10) + 20,
      avgPrice: Math.round(62000 * multiplier)
    },
    large_scooter: {
      min: Math.round(75000 * multiplier),
      max: Math.round(150000 * multiplier),
      count: Math.floor(Math.random() * 8) + 15,
      avgPrice: Math.round(110000 * multiplier)
    },
    luxury_bike: {
      min: Math.round(180000 * multiplier),
      max: Math.round(400000 * multiplier),
      count: Math.floor(Math.random() * 6) + 8,
      avgPrice: Math.round(275000 * multiplier)
    }
  };

  res.json({
    success: true,
    data: testRanges,
    metadata: {
      lastUpdated: new Date().toISOString(),
      demandMultiplier: multiplier.toFixed(2),
      marketConditions: condition,
      testMode: true
    }
  });
});

// Provider API endpoints
app.get('/api/providers/me', async (req, res) => {
  try {
    console.log('👤 Provider profile request - fetching from Supabase');

    // Try to get provider data from Supabase
    // First check if we can connect to Supabase at all
    const { data: providers, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'PROVIDER')
      .limit(1)
      .single();

    if (error) {
      console.error('❌ Error fetching provider:', error);
      // Fallback to mock data if database query fails
      return res.json({
        success: true,
        data: {
          id: 'provider-fallback',
          businessName: 'Jakarta Bike Rentals',
          email: '<EMAIL>',
          phone: '+62-812-3456-7890',
          address: 'Jl. Sudirman No. 123, Jakarta',
          verified: true,
          rating: 4.8,
          totalVehicles: 0,
          activeBookings: 0,
          totalEarnings: 0,
          joinedDate: new Date().toISOString(),
          fallback: true
        }
      });
    }

    console.log('✅ Provider data fetched from Supabase:', providers?.email);

    res.json({
      success: true,
      data: {
        id: providers.id,
        businessName: providers.business_name || `${providers.first_name} ${providers.last_name}`,
        email: providers.email,
        phone: providers.phone,
        address: providers.address,
        verified: providers.email_verified || false,
        rating: 4.5, // Default rating
        totalVehicles: 0, // Will be calculated separately
        activeBookings: 0, // Will be calculated separately
        totalEarnings: 0, // Will be calculated separately
        joinedDate: providers.created_at
      }
    });
  } catch (error) {
    console.error('❌ Provider profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch provider profile'
    });
  }
});

app.get('/api/providers/dashboard/stats', async (req, res) => {
  try {
    console.log('📊 Provider dashboard stats request - calculating from Supabase');

    // Get vehicle counts
    const { count: totalVehicles } = await supabase
      .from('vehicles')
      .select('*', { count: 'exact', head: true });

    const { count: activeVehicles } = await supabase
      .from('vehicles')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'available');

    // Get booking counts (if bookings table exists)
    let totalBookings = 0;
    let activeBookings = 0;

    try {
      const totalResult = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true });
      totalBookings = totalResult.count || 0;
    } catch (error) {
      console.log('Could not fetch total bookings:', error);
    }

    try {
      const activeResult = await supabase
        .from('bookings')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');
      activeBookings = activeResult.count || 0;
    } catch (error) {
      console.log('Could not fetch active bookings:', error);
    }

    // Calculate revenue (mock for now since we need booking amounts)
    const totalRevenue = (totalBookings || 0) * 100000; // Estimate
    const monthlyRevenue = totalRevenue * 0.2; // Estimate 20% monthly

    console.log(`✅ Stats calculated: ${totalVehicles} vehicles, ${totalBookings} bookings`);

    res.json({
      success: true,
      data: {
        totalVehicles: totalVehicles || 0,
        activeVehicles: activeVehicles || 0,
        totalBookings: totalBookings || 0,
        activeBookings: activeBookings || 0,
        pendingBookings: Math.max(0, (activeBookings || 0) - 2),
        completedBookings: Math.max(0, (totalBookings || 0) - (activeBookings || 0)),
        totalRevenue,
        monthlyRevenue,
        averageRating: 4.5, // Would calculate from reviews
        totalReviews: Math.floor((totalBookings || 0) * 0.6), // Estimate
        occupancyRate: totalVehicles ? (activeBookings || 0) / totalVehicles : 0,
        popularVehicles: [
          { name: 'Honda Scoopy', bookings: Math.floor((totalBookings || 0) * 0.3) },
          { name: 'Yamaha NMAX', bookings: Math.floor((totalBookings || 0) * 0.25) },
          { name: 'Honda Vario', bookings: Math.floor((totalBookings || 0) * 0.2) }
        ]
      }
    });
  } catch (error) {
    console.error('❌ Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics'
    });
  }
});

app.get('/api/providers/vehicles', async (req, res) => {
  try {
    console.log('🚗 Provider vehicles request - fetching from Supabase');

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = (page - 1) * limit;

    // Try to fetch vehicles from Supabase - check multiple possible table names
    let vehicles, error, count;

    // Try 'vehicles' table first
    const vehiclesResult = await supabase
      .from('vehicles')
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1);

    if (vehiclesResult.error) {
      // If vehicles table doesn't exist, try 'Vehicle' (capitalized)
      const VehicleResult = await supabase
        .from('Vehicle')
        .select('*', { count: 'exact' })
        .range(offset, offset + limit - 1);

      vehicles = VehicleResult.data;
      error = VehicleResult.error;
      count = VehicleResult.count;
    } else {
      vehicles = vehiclesResult.data;
      error = vehiclesResult.error;
      count = vehiclesResult.count;
    }

    if (error) {
      console.error('❌ Error fetching vehicles:', error);
      // Fallback to mock data
      const mockVehicles = [
        {
          id: 'vehicle-fallback-1',
          name: 'Honda Scoopy 2023',
          category: 'small_scooter',
          dailyRate: 75000,
          status: 'available',
          location: 'Jakarta Pusat',
          bookings: 0,
          rating: 0,
          images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
          fallback: true
        }
      ];

      return res.json({
        success: true,
        data: mockVehicles,
        pagination: {
          page: 1,
          limit: 20,
          total: mockVehicles.length,
          totalPages: 1
        }
      });
    }

    console.log(`✅ Fetched ${vehicles?.length || 0} vehicles from Supabase`);

    // Transform Supabase data to match frontend expectations
    const transformedVehicles = vehicles?.map(vehicle => ({
      id: vehicle.id,
      name: `${vehicle.make} ${vehicle.model} ${vehicle.year}`,
      category: vehicle.category,
      dailyRate: vehicle.daily_rate,
      status: vehicle.status,
      location: vehicle.location_city || 'Jakarta',
      bookings: 0, // Would need to calculate from bookings table
      rating: vehicle.rating || 0,
      images: vehicle.images || ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400']
    })) || [];

    res.json({
      success: true,
      data: transformedVehicles,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('❌ Provider vehicles error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vehicles'
    });
  }
});

app.get('/api/providers/bookings/active', (req, res) => {
  console.log('📅 Active bookings request');
  res.json({
    success: true,
    data: [
      {
        id: 'booking-1',
        vehicleName: 'Honda Scoopy 2023',
        customerName: 'Ahmad Rizki',
        startDate: '2024-01-20T09:00:00Z',
        endDate: '2024-01-22T18:00:00Z',
        status: 'active',
        totalAmount: 150000,
        pickupLocation: 'Jakarta Pusat'
      },
      {
        id: 'booking-2',
        vehicleName: 'Yamaha NMAX 2023',
        customerName: 'Sari Dewi',
        startDate: '2024-01-19T14:00:00Z',
        endDate: '2024-01-21T10:00:00Z',
        status: 'active',
        totalAmount: 250000,
        pickupLocation: 'Jakarta Selatan'
      }
    ]
  });
});

// Removed pickup-dropoff-events and payment-states endpoints
// These should return 404 or empty data as they did before

app.get('/api/providers/profile', (req, res) => {
  console.log('👤 Provider profile request (detailed)');
  res.json({
    success: true,
    data: {
      id: 'provider-123',
      businessName: 'Jakarta Bike Rentals',
      email: '<EMAIL>',
      phone: '+62-812-3456-7890',
      address: 'Jl. Sudirman No. 123, Jakarta',
      city: 'Jakarta',
      postalCode: '12190',
      verified: true,
      verificationDocuments: ['ktp.jpg', 'business_license.pdf'],
      bankAccount: {
        bankName: 'Bank BCA',
        accountNumber: '**********',
        accountHolder: 'Jakarta Bike Rentals'
      },
      settings: {
        autoAcceptBookings: true,
        requireDeposit: true,
        depositAmount: 500000,
        cancellationPolicy: 'flexible'
      }
    }
  });
});

// Removed message-threads, extension-requests, and agreements endpoints
// These should return 404 as they did before, letting the frontend handle empty states

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Minimal server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});
