import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface RefundPolicy {
  id: string;
  name: string;
  description: string;
  rules: RefundRule[];
}

interface RefundRule {
  hoursBeforeStart: number;
  refundPercentage: number;
  bookingFeeRefund: boolean;
}

class RefundPolicyService {
  /**
   * Get default refund policy
   */
  static getDefaultPolicy(): RefundPolicy {
    return {
      id: 'default',
      name: 'Standard Refund Policy',
      description: 'Standard refund policy for vehicle rentals',
      rules: [
        { hoursBeforeStart: 24, refundPercentage: 100, bookingFeeRefund: true },
        { hoursBeforeStart: 12, refundPercentage: 50, bookingFeeRefund: false },
        { hoursBeforeStart: 0, refundPercentage: 0, bookingFeeRefund: false }
      ]
    };
  }

  /**
   * Calculate refund amount based on policy
   */
  static calculateRefund(
    totalAmount: number,
    hoursBeforeStart: number,
    policy?: RefundPolicy
  ): { refundAmount: number; refundPercentage: number } {
    const activePolicy = policy || this.getDefaultPolicy();

    // Find applicable rule
    const applicableRule = activePolicy.rules
      .sort((a, b) => b.hoursBeforeStart - a.hoursBeforeStart)
      .find(rule => hoursBeforeStart >= rule.hoursBeforeStart);

    if (!applicableRule) {
      return { refundAmount: 0, refundPercentage: 0 };
    }

    const refundAmount = (totalAmount * applicableRule.refundPercentage) / 100;
    return { refundAmount, refundPercentage: applicableRule.refundPercentage };
  }
}

export default RefundPolicyService;