import { PrismaClient } from '@prisma/client';
import { Booking, BookingStatus } from '../types/database';
import { logger } from '../utils/logger';

export enum AvailabilityStatus {
  AVAILABLE = 'AVAILABLE',
  UNAVAILABLE = 'UNAVAILABLE',
  MAINTENANCE = 'MAINTENANCE',
  BOOKED = 'BOOKED'
}

export class VehicleAvailabilityService {
  private prisma: PrismaClient;
  private logger = logger;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Block vehicle availability for a booking
   */
  async blockVehicleAvailability(booking: Booking): Promise<void> {
    try {
      // Calculate date range for blocking
      const startDate = new Date(booking.startDate);
      const endDate = new Date(booking.endDate);

      // Block each day in the range
      const datesToBlock = this.generateDateRange(startDate, endDate);
      
      await Promise.all(datesToBlock.map(async (date) => {
        await this.prisma.vehicleAvailability.upsert({
          where: {
            vehicleId_date: {
              vehicleId: booking.vehicleId,
              date: date
            }
          },
          update: {
            status: AvailabilityStatus.BOOKED
          },
          create: {
            vehicleId: booking.vehicleId,
            date: date,
            status: AvailabilityStatus.BOOKED
          }
        });
      }));

      this.logger.info('Vehicle availability blocked for booking', {
        bookingId: booking.id,
        vehicleId: booking.vehicleId,
        dates: datesToBlock.length
      });
    } catch (error) {
      this.logger.error('Failed to block vehicle availability', error);
      throw new Error('Vehicle availability blocking failed');
    }
  }

  /**
   * Release vehicle availability after booking completion/cancellation
   */
  async releaseVehicleAvailability(booking: Booking): Promise<void> {
    try {
      // Calculate date range for releasing
      const startDate = new Date(booking.startDate);
      const endDate = new Date(booking.endDate);

      // Release each day in the range
      const datesToRelease = this.generateDateRange(startDate, endDate);
      
      await Promise.all(datesToRelease.map(async (date) => {
        // Check if there are no other active bookings for this vehicle on this date
        const conflictingBookings = await this.prisma.booking.count({
          where: {
            vehicleId: booking.vehicleId,
            status: {
              in: [
                BookingStatus.CONFIRMED,
                BookingStatus.PENDING
              ]
            },
            startDate: { lte: date },
            endDate: { gte: date }
          }
        });

        // If no other bookings, mark as available
        if (conflictingBookings === 0) {
          await this.prisma.vehicleAvailability.update({
            where: {
              vehicleId_date: {
                vehicleId: booking.vehicleId,
                date: date
              }
            },
            data: {
              status: AvailabilityStatus.AVAILABLE
            }
          });
        }
      }));

      this.logger.info('Vehicle availability unblocked for booking', {
        bookingId: booking.id,
        vehicleId: booking.vehicleId
      });
    } catch (error) {
      this.logger.error('Failed to unblock vehicle availability', error);
      throw new Error('Vehicle availability releasing failed');
    }
  }

  /**
   * Check vehicle availability for a specific date range
   */
  async checkVehicleAvailability(
    vehicleId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<boolean> {
    try {
      // Generate date range to check
      const datesToCheck = this.generateDateRange(startDate, endDate);

      // Check availability for each date
      const unavailableDates = await Promise.all(
        datesToCheck.map(async (date) => {
          const availability = await this.prisma.vehicleAvailability.findUnique({
            where: {
              vehicleId_date: {
                vehicleId,
                date
              }
            }
          });

          return availability && availability.status !== AvailabilityStatus.AVAILABLE;
        })
      );

      // If any date is unavailable, return false
      const isAvailable = !unavailableDates.some(Boolean);

      this.logger.info('Vehicle availability checked', {
        vehicleId,
        startDate,
        endDate,
        isAvailable
      });

      return isAvailable;
    } catch (error) {
      this.logger.error('Error checking vehicle availability:', error);
      throw new Error('Vehicle availability check failed');
    }
  }

  /**
   * Generate an array of dates between start and end dates (inclusive)
   */
  private generateDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }

  /**
   * Cleanup stale availability records
   */
  async cleanupStaleAvailability(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Remove availability records older than 30 days
      const result = await this.prisma.vehicleAvailability.deleteMany({
        where: {
          date: { lt: thirtyDaysAgo },
          status: AvailabilityStatus.AVAILABLE
        }
      });

      this.logger.info('Cleanup completed', {
        recordsRemoved: result.count
      });
    } catch (error) {
      this.logger.error('Error during cleanup:', error);
    }
  }
}

export default VehicleAvailabilityService;
