import { PrismaClient } from '@prisma/client';
import monitoringService from '../utils/monitoring';

interface SaveVehicleParams {
  userId: string;
  vehicleId: string;
}

class SavedVehiclesService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Save a vehicle to user's wishlist
   */
  async saveVehicle(params: SaveVehicleParams) {
    try {
      // Check if vehicle exists
      const vehicle = await this.prisma.vehicle.findUnique({
        where: { id: params.vehicleId }
      });

      if (!vehicle) {
        throw new Error('Vehicle not found');
      }

      // Check if already saved
      const existingSavedVehicle = await this.prisma.savedVehicle.findUnique({
        where: {
          userId_vehicleId: {
            userId: params.userId,
            vehicleId: params.vehicleId
          }
        }
      });

      if (existingSavedVehicle) {
        throw new Error('Vehicle already saved');
      }

      // Save vehicle
      const savedVehicle = await this.prisma.savedVehicle.create({
        data: {
          userId: params.userId,
          vehicleId: params.vehicleId
        },
        include: {
          vehicle: true
        }
      });

      // Log saved vehicle
      monitoringService.trackEvent('vehicle_saved', {
        userId: params.userId,
        vehicleId: params.vehicleId
      });

      return savedVehicle;
    } catch (error) {
      // Log and handle errors
      monitoringService.trackError('Error saving vehicle to wishlist', {
        context: 'save-vehicle',
        userId: params.userId, 
        vehicleId: params.vehicleId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Remove a vehicle from user's wishlist
   */
  async removeSavedVehicle(params: SaveVehicleParams) {
    try {
      // Remove saved vehicle
      const removedVehicle = await this.prisma.savedVehicle.delete({
        where: {
          userId_vehicleId: {
            userId: params.userId,
            vehicleId: params.vehicleId
          }
        }
      });

      // Log removed vehicle
      monitoringService.trackEvent('vehicle_removed', {
        userId: params.userId,
        vehicleId: params.vehicleId
      });

      return removedVehicle;
    } catch (error) {
      // Log and handle errors
      monitoringService.trackError('Error removing saved vehicle', {
        context: 'remove-saved-vehicle',
        userId: params.userId, 
        vehicleId: params.vehicleId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get all saved vehicles for a user
   */
  async getSavedVehicles(userId: string) {
    try {
      const savedVehicles = await this.prisma.savedVehicle.findMany({
        where: { userId },
        include: {
          vehicle: {
            include: {
              // Add any additional vehicle details you want to return
              reviews: true,
              images: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return savedVehicles;
    } catch (error) {
      // Log and handle errors
      monitoringService.trackError('Error getting saved vehicles', {
        context: 'get-saved-vehicles',
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Check if a vehicle is saved by a user
   */
  async isVehicleSaved(userId: string, vehicleId: string) {
    try {
      const savedVehicle = await this.prisma.savedVehicle.findUnique({
        where: {
          userId_vehicleId: {
            userId,
            vehicleId
          }
        }
      });

      return !!savedVehicle;
    } catch (error) {
      // Log and handle errors
      monitoringService.trackError('Error checking saved vehicle', {
        context: 'check-saved-vehicle',
        userId, 
        vehicleId,
        error: error.message
      });
      throw error;
    }
  }
}

export default new SavedVehiclesService(new PrismaClient()); 