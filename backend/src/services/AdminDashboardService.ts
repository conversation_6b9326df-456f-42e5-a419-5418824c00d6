import { supabase } from '../utils/supabaseClient';
import { logger } from '../utils/logger';

export interface RevenueInsights {
  totalRevenue: number;
  platformFees: number;
  payoutToOwners: number;
  paymentMethodBreakdown: {
    stripe: number;
    paypal: number;
    bankTransfer: number;
  };
  monthlyRevenuetrend: Array<{
    month: string;
    totalRevenue: number;
    platformFees: number;
  }>;
  topPerformingVehicles: Array<{
    vehicleId: string;
    vehicleName: string;
    totalBookings: number;
    totalRevenue: number;
  }>;
}

export class AdminDashboardService {
  private logger = console; // Simple console logger replacement

  constructor() {
    // Simple constructor
  }

  /**
   * Get comprehensive revenue insights
   * @param startDate Optional start date for filtering
   * @param endDate Optional end date for filtering
   */
  async getRevenueInsights(
    startDate?: Date, 
    endDate?: Date
  ): Promise<RevenueInsights> {
    try {
      // Base query with optional date filtering
      let query = supabase
        .from('payments')
        .select('*');

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }

      if (endDate) {
        query = query.lte('created_at', endDate.toISOString());
      }

      const { data: payments, error } = await query;

      if (error) {
        throw error;
      }

      // Calculate total revenue and platform fees
      const totalRevenue = payments.reduce(
        (sum, payment) => sum + payment.total_amount, 
        0
      );
      const platformFees = payments.reduce(
        (sum, payment) => sum + payment.booking_fee_amount, 
        0
      );
      const payoutToOwners = totalRevenue - platformFees;

      // Payment method breakdown
      const paymentMethodBreakdown = payments.reduce(
        (breakdown, payment) => {
          switch (payment.payment_method) {
            case 'stripe':
              breakdown.stripe += payment.total_amount;
              break;
            case 'paypal':
              breakdown.paypal += payment.total_amount;
              break;
            case 'bank_transfer':
              breakdown.bankTransfer += payment.total_amount;
              break;
          }
          return breakdown;
        },
        { stripe: 0, paypal: 0, bankTransfer: 0 }
      );

      // Monthly revenue trend
      const monthlyRevenueQuery = await supabase
        .rpc('get_monthly_revenue_trend', {
          start_date: startDate?.toISOString(),
          end_date: endDate?.toISOString()
        });

      // Top performing vehicles
      const topVehiclesQuery = await supabase
        .rpc('get_top_performing_vehicles', {
          start_date: startDate?.toISOString(),
          end_date: endDate?.toISOString(),
          limit: 10
        });

      return {
        totalRevenue,
        platformFees,
        payoutToOwners,
        paymentMethodBreakdown,
        monthlyRevenuetrend: monthlyRevenueQuery.data || [],
        topPerformingVehicles: topVehiclesQuery.data || []
      };

    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Get detailed payment splits
   * @param page Page number for pagination
   * @param limit Number of records per page
   */
  async getPaymentSplits(
    page = 1, 
    limit = 20
  ): Promise<{
    paymentSplits: Array<{
      paymentId: string;
      platformFee: number;
      ownerPayout: number;
      vehicleOwner: string;
      paymentDate: string;
    }>;
    pagination: {
      total: number;
      page: number;
      limit: number;
    }
  }> {
    try {
      const offset = (page - 1) * limit;

      const { data: paymentSplits, count, error } = await supabase
        .from('payment_splits')
        .select(`
          payment_id,
          platform_fee,
          owner_payout,
          vehicle_owner_id,
          payments (created_at)
        `)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return {
        paymentSplits: paymentSplits.map(split => ({
          paymentId: split.payment_id,
          platformFee: split.platform_fee,
          ownerPayout: split.owner_payout,
          vehicleOwner: split.vehicle_owner_id,
          paymentDate: Array.isArray(split.payments) && split.payments.length > 0 
            ? split.payments[0].created_at 
            : new Date()
        })),
        pagination: {
          total: count || 0,
          page,
          limit
        }
      };

    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Generate financial report
   * @param startDate Report start date
   * @param endDate Report end date
   */
  async generateFinancialReport(
    startDate: Date, 
    endDate: Date
  ): Promise<{
    reportPeriod: { start: string; end: string };
    summary: RevenueInsights;
    detailedTransactions: Array<{
      paymentId: string;
      amount: number;
      method: string;
      date: string;
      status: string;
    }>;
  }> {
    try {
      // Get revenue insights
      const summary = await this.getRevenueInsights(startDate, endDate);

      // Fetch detailed transactions
      const { data: transactions, error } = await supabase
        .from('payments')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return {
        reportPeriod: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        summary,
        detailedTransactions: transactions.map(transaction => ({
          paymentId: transaction.id,
          amount: transaction.total_amount,
          method: transaction.payment_method,
          date: transaction.created_at,
          status: transaction.status
        }))
      };

    } catch (error) {
      this.logger.info()
      throw error;
    }
  }
}

export default AdminDashboardService;
