import { PrismaClient, User, UserRole, UserStatus } from '@prisma/client'
import { compare, hash } from 'bcrypt'
import jwt from 'jsonwebtoken'
import emailService from '../utils/emailService'
import { createLogger, transports, format } from 'winston'
import crypto from 'crypto'
// import { generateTOTP, verifyTOTP } from 'otplib'; // Commented out - missing package
// import { SMSService } from './SMSService'; // Commented out - missing service
// const smsService = new SMSService();

// Create a logger for authentication events
const authLogger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.json()
  ),
  transports: [
    new transports.File({ filename: 'logs/auth.log' }),
    new transports.Console()
  ]
});

const prisma = new PrismaClient()

export class AuthService {
  private JWT_SECRET = process.env.JWT_SECRET || this.generateFallbackSecret()
  private EMAIL_VERIFICATION_SECRET = process.env.EMAIL_VERIFICATION_SECRET || this.generateFallbackSecret()

  private generateFallbackSecret(): string {
    return crypto.randomBytes(64).toString('base64')
  }

  async register(userData: {
    email: string, 
    password: string, 
    name?: string, 
    role?: 'CUSTOMER' | 'PROVIDER' | 'ADMIN',
    phoneNumber?: string
  }): Promise<User> {
    const { email, password, name, role = 'CUSTOMER', phoneNumber } = userData

    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({ where: { email } })
      if (existingUser) {
        authLogger.warn('Registration attempt with existing email', { email });
        throw new Error('User already exists')
      }

      // Hash password
      const hashedPassword = await hash(password, 10)

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          role,
          phoneNumber,
          status: 'ACTIVE',
          emailVerified: false,
          phoneVerified: false
        }
      })

      if (phoneNumber) {
        // Phone verification disabled for now
        // const phoneVerificationCode = crypto.randomBytes(6).toString('hex'); // Generate a simple code
        // await smsService.sendVerificationCode(phoneNumber, phoneVerificationCode); // Send via SMS
        // user.phoneVerificationCode = phoneVerificationCode; // Store temporarily
        // user.phoneVerified = false;
      }

      // Generate email verification token
      const verificationToken = jwt.sign(
        { 
          id: user.id, 
          email: user.email 
        }, 
        this.EMAIL_VERIFICATION_SECRET, 
        { expiresIn: '24h' }
      )

      // Send verification email
      const emailSent = await emailService.sendVerificationEmail(
        user.email, 
        user.name || 'User', 
        verificationToken
      )

      // Log registration and email sending
      authLogger.info('User registered successfully', { 
        userId: user.id, 
        email: user.email, 
        emailSent 
      });

      return user
    } catch (error) {
      authLogger.error('Registration failed', { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error
    }
  }

  async verifyEmail(token: string): Promise<User> {
    try {
      const decoded = jwt.verify(token, this.EMAIL_VERIFICATION_SECRET) as { 
        id: string, 
        type: string 
      }

      if (decoded.type !== 'email_verification') {
        throw new Error('Invalid token type')
      }

      const user = await prisma.user.update({
        where: { id: decoded.id },
        data: { status: 'ACTIVE' as UserStatus }
      })

      authLogger.info('Email verified', { userId: user.id, email: user.email })

      return user
    } catch (error) {
      authLogger.error('Email verification failed', { error })
      throw new Error('Invalid or expired verification token')
    }
  }

  async verifyPhone(userId: string, code: string): Promise<User> {
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (user && user.phoneVerificationCode === code) {
      return await prisma.user.update({
        where: { id: userId },
        data: { phoneVerified: true, phoneVerificationCode: null } // Clear code after verification
      });
    }
    throw new Error('Invalid verification code');
  }

  async uploadVerificationDocument(
    userId: string, 
    documentType: string, 
    fileUrl: string
  ): Promise<VerificationDocument> {
    try {
      // Ensure user exists and is a provider
      const user = await prisma.user.findUnique({ 
        where: { 
          id: userId, 
          role: 'PROVIDER' 
        } 
      })

      if (!user) {
        authLogger.warn('Document upload attempt by unauthorized user', { userId });
        throw new Error('User not found or not authorized to upload documents')
      }

      // Create verification document
      const document = await prisma.user.create({
        data: {
          userId,
          documentType,
          fileUrl,
          status: 'PENDING'
        }
      })

      // Send document upload confirmation email
      const emailSent = await emailService.sendDocumentUploadConfirmation(
        user.email, 
        user.name || 'User', 
        documentType
      )

      // Log document upload
      authLogger.info('Verification document uploaded', { 
        userId, 
        documentId: document.id, 
        documentType,
        emailSent
      });

      return document
    } catch (error) {
      authLogger.error('Document upload failed', { 
        userId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error
    }
  }

  async approveVerificationDocument(
    documentId: string, 
    adminId: string
  ): Promise<VerificationDocument> {
    try {
      // Ensure admin is approving
      const admin = await prisma.user.findUnique({ 
        where: { 
          id: adminId, 
          role: 'ADMIN' 
        } 
      })

      if (!admin) {
        authLogger.warn('Document approval attempt by unauthorized user', { adminId });
        throw new Error('Only admins can approve documents')
      }

      // Update document status
      const document = await prisma.user.update({
        where: { id: documentId },
        data: { 
          status: 'APPROVED' 
        },
        include: {
          user: true // Include user details for email
        }
      })

      // If document is approved, update user's verification status
      if (document.status === 'APPROVED') {
        await prisma.user.update({
          where: { id: document.userId },
          data: { status: 'ACTIVE' }
        })

        // Send document approval email
        const emailSent = await emailService.sendDocumentApprovalEmail(
          document.user.email, 
          document.user.name || 'User', 
          document.documentType
        )

        // Log document approval
        authLogger.info('Verification document approved', { 
          documentId, 
          userId: document.userId,
          documentType: document.documentType,
          emailSent
        });
      }

      return document
    } catch (error) {
      authLogger.error('Document approval failed', { 
        documentId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error
    }
  }

  async rejectVerificationDocument(
    documentId: string, 
    adminId: string,
    reason?: string
  ): Promise<VerificationDocument> {
    try {
      // Ensure admin is rejecting
      const admin = await prisma.user.findUnique({ 
        where: { 
          id: adminId, 
          role: 'ADMIN' 
        } 
      })

      if (!admin) {
        authLogger.warn('Document rejection attempt by unauthorized user', { adminId });
        throw new Error('Only admins can reject documents')
      }

      // Update document status
      const document = await prisma.user.update({
        where: { id: documentId },
        data: { 
          status: 'REJECTED' 
        },
        include: {
          user: true // Include user details for email
        }
      })

      // Send document rejection email
      const emailSent = await emailService.sendDocumentRejectionEmail(
        document.user.email, 
        document.user.name || 'User', 
        document.documentType,
        reason
      )

      // Log document rejection
      authLogger.info('Verification document rejected', { 
        documentId, 
        userId: document.userId,
        documentType: document.documentType,
        emailSent
      });

      return document
    } catch (error) {
      authLogger.error('Document rejection failed', { 
        documentId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error
    }
  }

  async login(email: string, password: string, totpCode?: string): Promise<{
    token: string, 
    user: any
  }> {
    const user = await prisma.user.findUnique({ where: { email } })
    if (!user) {
      throw new Error('Invalid credentials')
    }

    // Compare passwords
    const isPasswordValid = await compare(password, user.password || '')
    if (!isPasswordValid) {
      throw new Error('Invalid credentials')
    }

    if (user.mfaEnabled && totpCode) { // Assuming mfaEnabled is a field we'll add
      const isValidTOTP = verifyTOTP(user.mfaSecret, totpCode); // Use otplib to verify
      if (!isValidTOTP) {
        throw new Error('Invalid MFA code');
      }
    }

    // Check email verification for certain roles
    if (user.role === 'PROVIDER' && !user.emailVerified) {
      throw new Error('Please verify your email before logging in')
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.role 
      }, 
      this.JWT_SECRET, 
      { expiresIn: '24h' }
    )

    return { token, user }
  }

  async googleSignIn(googleId: string, email: string, name: string): Promise<{
    token: string, 
    user: any
  }> {
    // Find or create user with Google ID
    let user = await prisma.user.findUnique({ 
      where: { 
        googleId: googleId 
      } 
    })

    if (!user) {
      user = await prisma.user.create({
        data: {
          email,
          name,
          googleId,
          emailVerified: true,
          role: 'CUSTOMER'
        }
      })
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.role 
      }, 
      this.JWT_SECRET, 
      { expiresIn: '24h' }
    )

    return { token, user }
  }

  async registerWithEmail(email: string, password: string, name: string = ''): Promise<User> {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } })
    if (existingUser) {
      throw new Error('Email already registered')
    }

    // Hash password
    const hashedPassword = await hash(password, 12)

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        name,
        passwordHash: hashedPassword,
        role: 'CUSTOMER' as UserRole,
        status: 'ACTIVE' as UserStatus
      }
    })

    // Generate email verification token
    const verificationToken = this.generateEmailVerificationToken(user.id)

    // Send verification email
    await this.sendEmailVerification(user.email, verificationToken)

    authLogger.info('User registered', { userId: user.id, email: user.email })

    return user
  }

  private generateEmailVerificationToken(userId: string): string {
    const token = jwt.sign(
      { 
        id: userId, 
        type: 'email_verification' 
      }, 
      this.EMAIL_VERIFICATION_SECRET, 
      { expiresIn: '1h' }
    )
    return token
  }

  private async sendEmailVerification(email: string, token: string) {
    const verificationLink = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${token}`
    
    await emailService.sendEmail({
      to: email,
      subject: 'Verify Your Email - RentAHub',
      html: `
        <h1>Email Verification</h1>
        <p>Click the link below to verify your email:</p>
        <a href="${verificationLink}">Verify Email</a>
        <p>This link will expire in 1 hour.</p>
      `
    })
  }

  async initiatePasswordReset(email: string) {
    const user = await prisma.user.findUnique({ where: { email } })
    if (!user) {
      throw new Error('No account associated with this email')
    }

    const resetToken = this.generatePasswordResetToken(user.id)
    await this.sendPasswordResetEmail(email, resetToken)
  }

  private generatePasswordResetToken(userId: string): string {
    return jwt.sign(
      { 
        id: userId, 
        type: 'password_reset' 
      }, 
      this.JWT_SECRET, 
      { expiresIn: '30m' }
    )
  }

  private async sendPasswordResetEmail(email: string, token: string) {
    const resetLink = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`
    
    await emailService.sendEmail({
      to: email,
      subject: 'Password Reset - RentAHub',
      html: `
        <h1>Password Reset</h1>
        <p>Click the link below to reset your password:</p>
        <a href="${resetLink}">Reset Password</a>
        <p>This link will expire in 30 minutes.</p>
      `
    })
  }

  async resetPassword(token: string, newPassword: string) {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as { 
        id: string, 
        type: string 
      }

      if (decoded.type !== 'password_reset') {
        throw new Error('Invalid token type')
      }

      const hashedPassword = await hash(newPassword, 12)

      const user = await prisma.user.update({
        where: { id: decoded.id },
        data: { passwordHash: hashedPassword }
      })

      authLogger.info('Password reset successful', { userId: user.id })

      return user
    } catch (error) {
      authLogger.error('Password reset failed', { error })
      throw new Error('Invalid or expired reset token')
    }
  }

  // Login method for email-based authentication
  async loginWithEmail(email: string, password: string): Promise<{ user: any, token: string }> {
    const user = await prisma.user.findUnique({ where: { email } })
    if (!user) {
      throw new Error('Invalid email or password')
    }

    const isPasswordValid = await compare(password, user.passwordHash)
    if (!isPasswordValid) {
      throw new Error('Invalid email or password')
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user.id, 
        email: user.email, 
        role: user.role 
      }, 
      this.JWT_SECRET, 
      { expiresIn: '24h' }
    )

    return { user, token }
  }

  async enableMFA(userId: string): Promise<string> { // Returns secret for user to set up
    const secret = generateTOTP(); // Generate secret from otplib
    await prisma.user.update({ where: { id: userId }, data: { mfaEnabled: true, mfaSecret: secret } });
    return secret;
  }
}

export default new AuthService()