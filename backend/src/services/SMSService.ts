import twilio from 'twilio';

class SMSService {
  private client: twilio.Twilio;
  private fromNumber: string;

  constructor() {
    const accountSid = process.env.TWILIO_ACCOUNT_SID || '';
    const authToken = process.env.TWILIO_AUTH_TOKEN || '';
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '';

    this.client = twilio(accountSid, authToken);
  }

  async sendBookingReminder(phoneNumber: string, booking: any) {
    try {
      const message = await this.client.messages.create({
        body: `RentaHub Reminder: Your booking for ${booking.vehicleType} is coming up on ${booking.pickupDate}. Booking ID: ${booking.id}`,
        from: this.fromNumber,
        to: phoneNumber
      });

      console.log(`SMS sent: ${message.sid}`);
      return message;
    } catch (error) {
      console.error('Error sending SMS', error);
      throw new Error('Failed to send SMS reminder');
    }
  }

  async sendVerificationCode(phoneNumber: string, code: string) {
    try {
      const message = await this.client.messages.create({
        body: `Your RentaHub verification code is: ${code}`,
        from: this.fromNumber,
        to: phoneNumber
      });

      console.log(`Verification SMS sent: ${message.sid}`);
      return message;
    } catch (error) {
      console.error('Error sending verification SMS', error);
      throw new Error('Failed to send verification code');
    }
  }
}

export default new SMSService(); 