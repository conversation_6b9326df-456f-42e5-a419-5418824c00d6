import { PrismaClient, User, Prisma } from '@prisma/client'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { OAuth2Client } from 'google-auth-library'
import { logger } from '../utils/logger'
import axios from 'axios'

export class OAuthService {
  private prisma: PrismaClient
  private googleClient: OAuth2Client
  private JWT_SECRET: string
  private JWT_EXPIRATION: string

  constructor() {
    this.prisma = new PrismaClient()
    
    // Secure secret handling
    this.JWT_SECRET = this.getJWTSecret()
    this.JWT_EXPIRATION = process.env.JWT_EXPIRATION || '30d'

    // Initialize Google OAuth client with additional security
    this.googleClient = new OAuth2Client({
      clientId: this.getRequiredEnv('GOOGLE_CLIENT_ID'),
      clientSecret: this.getRequiredEnv('GOOGLE_CLIENT_SECRET'),
      redirectUri: this.getRequiredEnv('GOOGLE_CALLBACK_URL')
    })
  }

  // Retrieve required environment variable with error handling
  private getRequiredEnv(key: keyof NodeJS.ProcessEnv): string {
    const value = process.env[key]
    if (!value) {
      logger.error('Missing environment variable', { 
        missingKey: key,
        action: 'Throwing configuration error'
      })
      throw new Error(`Configuration error: ${key} is not set`)
    }
    return value
  }

  // Enhanced secret retrieval with fallback and logging
  private getJWTSecret(): string {
    const secret = process.env.JWT_SECRET || this.generateFallbackSecret()
    
    if (secret === this.generateFallbackSecret()) {
      logger.warn('Using fallback JWT secret', { 
        message: 'Using generated fallback JWT secret. Not recommended for production.',
        action: 'Generate a secure secret'
      })
    }

    return secret
  }

  // Generate a cryptographically secure fallback secret
  private generateFallbackSecret(): string {
    return crypto.randomBytes(64).toString('base64')
  }

  // Generate a secure state token with additional entropy
  public generateStateToken(): string {
    const stateToken = crypto.randomBytes(48).toString('hex')
    
    logger.info('State token generated', {
      tokenLength: stateToken.length
    })

    return stateToken
  }

  // Get Google authorization URL with enhanced security parameters
  public getAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: this.getRequiredEnv('GOOGLE_CLIENT_ID'),
      redirect_uri: this.getRequiredEnv('GOOGLE_CALLBACK_URL'),
      response_type: 'code',
      state,
      scope: 'openid email profile',
      access_type: 'offline',  // Allow refresh tokens
      prompt: 'consent'  // Always ask for consent
    })

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`
  }

  // Handle Google OAuth callback with enhanced validation
  public async handleOAuthCallback(code: string): Promise<{
    user: any, 
    token: string
  }> {
    try {
      // Exchange code for tokens with additional validation
      const { tokens } = await this.googleClient.getToken({
        code,
        redirect_uri: this.getRequiredEnv('GOOGLE_CALLBACK_URL')
      })

      // Verify ID token with strict checks
      const ticket = await this.googleClient.verifyIdToken({
        idToken: tokens.id_token!,
        audience: this.getRequiredEnv('GOOGLE_CLIENT_ID')
      })
      const payload = ticket.getPayload()

      if (!payload) {
        throw new Error('Invalid Google ID token')
      }

      // Comprehensive user info extraction
      const userInfo = {
        googleId: payload['sub']!,
        email: payload['email']!,
        name: payload['name'] || '',
        picture: payload['picture']
      }

      // Upsert user with comprehensive details
      const user = await this.prisma.user.upsert({
        where: { googleId: userInfo.googleId },
        update: {
          email: userInfo.email,
          name: userInfo.name,
        },
        create: {
          email: userInfo.email,
          name: userInfo.name,
          googleId: userInfo.googleId,
          role: 'CUSTOMER',
          status: 'ACTIVE',
          password: crypto.randomBytes(64).toString('hex')  // Generate a random hash for OAuth users
        }
      })

      // Generate secure JWT token
      const token = this.generateJWTToken(user)

      // Log successful authentication
      logger.info('OAuth authentication successful', { 
        userId: user.id, 
        email: user.email,
        provider: 'Google'
      })

      return { user, token }
    } catch (error) {
      // Comprehensive error logging
      logger.error('OAuth callback error', { 
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      })
      throw error
    }
  }

  // Generate JWT token with enhanced security
  public generateJWTToken(user: any): string {
    const payload = { 
      id: user.id, 
      email: user.email, 
      role: user.role,
      type: 'access'  // Token type for additional verification
    };
    
    // Ensure JWT_SECRET is a string
    const secret = String(this.JWT_SECRET);
    
    const token = jwt.sign(payload, secret);

    logger.info('JWT token generated', {
      userId: user.id,
      tokenExpiration: this.JWT_EXPIRATION
    })

    return token
  }

  // Optional: Token refresh mechanism
  public async refreshToken(user: any): Promise<string> {
    // Implement token refresh logic if needed
    return this.generateJWTToken(user)
  }
}

export default new OAuthService() 