import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { PrismaClient } from '@prisma/client';
import { EmailService } from './EmailService';

export interface NotificationData {
  id: string;
  type: 'ASSISTANCE_REQUEST' | 'ASSISTANCE_UPDATE' | 'ASSISTANCE_RESOLVED';
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  userId?: string;
  adminId?: string;
  providerId?: string;
  assistanceRequestId?: string;
  data?: any;
  createdAt: Date;
}

export interface SocketUser {
  userId: string;
  role: 'USER' | 'PROVIDER' | 'ADMIN';
  socketId: string;
}

export class RealTimeNotificationService {
  private io: SocketIOServer;
  private prisma: PrismaClient;
  private emailService: EmailService;
  private connectedUsers: Map<string, SocketUser> = new Map();
  private userSockets: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      }
    });
    
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    this.setupSocketHandlers();
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      console.log(`Socket connected: ${socket.id}`);

      // Handle user authentication
      socket.on('authenticate', (data: { userId: string; role: string; token: string }) => {
        try {
          // TODO: Verify JWT token here
          const user: SocketUser = {
            userId: data.userId,
            role: data.role as 'USER' | 'PROVIDER' | 'ADMIN',
            socketId: socket.id
          };

          this.connectedUsers.set(socket.id, user);
          
          // Add to user sockets map
          if (!this.userSockets.has(data.userId)) {
            this.userSockets.set(data.userId, new Set());
          }
          this.userSockets.get(data.userId)!.add(socket.id);

          // Join role-based rooms
          socket.join(`role:${data.role.toLowerCase()}`);
          socket.join(`user:${data.userId}`);

          console.log(`User ${data.userId} (${data.role}) authenticated on socket ${socket.id}`);
          
          // Send authentication success
          socket.emit('authenticated', { success: true });
          
          // Send any pending notifications
          this.sendPendingNotifications(data.userId);
        } catch (error) {
          console.error('Authentication error:', error);
          socket.emit('authentication_error', { error: 'Invalid token' });
        }
      });

      // Handle joining assistance request room
      socket.on('join_assistance_request', (requestId: string) => {
        const user = this.connectedUsers.get(socket.id);
        if (user) {
          socket.join(`assistance:${requestId}`);
          console.log(`User ${user.userId} joined assistance request room: ${requestId}`);
        }
      });

      // Handle leaving assistance request room
      socket.on('leave_assistance_request', (requestId: string) => {
        socket.leave(`assistance:${requestId}`);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        const user = this.connectedUsers.get(socket.id);
        if (user) {
          // Remove from user sockets map
          const userSocketSet = this.userSockets.get(user.userId);
          if (userSocketSet) {
            userSocketSet.delete(socket.id);
            if (userSocketSet.size === 0) {
              this.userSockets.delete(user.userId);
            }
          }
          
          this.connectedUsers.delete(socket.id);
          console.log(`User ${user.userId} disconnected from socket ${socket.id}`);
        }
      });
    });
  }

  /**
   * Send notification for new assistance request
   */
  async notifyAssistanceRequest(assistanceRequest: any): Promise<void> {
    const notification: NotificationData = {
      id: `assistance_${assistanceRequest.id}_${Date.now()}`,
      type: 'ASSISTANCE_REQUEST',
      title: '🚨 New Vehicle Assistance Request',
      message: `${assistanceRequest.category.replace('_', ' ')} - ${assistanceRequest.priority} priority`,
      priority: assistanceRequest.priority,
      assistanceRequestId: assistanceRequest.id,
      userId: assistanceRequest.userId,
      providerId: assistanceRequest.providerId,
      data: {
        category: assistanceRequest.category,
        location: assistanceRequest.location,
        reason: assistanceRequest.reason
      },
      createdAt: new Date()
    };

    // Notify all admins
    this.io.to('role:admin').emit('assistance_request', notification);

    // Notify specific provider if applicable
    if (assistanceRequest.providerId) {
      this.io.to(`user:${assistanceRequest.providerId}`).emit('assistance_request', notification);
    }

    // Store notification in database
    await this.storeNotification(notification);

    // Send high-priority email notifications
    if (assistanceRequest.priority === 'URGENT' || assistanceRequest.priority === 'HIGH') {
      await this.sendUrgentEmailNotification(assistanceRequest);
    }
  }

  /**
   * Send notification for assistance request update
   */
  async notifyAssistanceUpdate(assistanceRequest: any, oldStatus: string): Promise<void> {
    const notification: NotificationData = {
      id: `assistance_update_${assistanceRequest.id}_${Date.now()}`,
      type: 'ASSISTANCE_UPDATE',
      title: '🔄 Assistance Request Updated',
      message: `Status changed from ${oldStatus} to ${assistanceRequest.status}`,
      priority: assistanceRequest.priority,
      assistanceRequestId: assistanceRequest.id,
      userId: assistanceRequest.userId,
      providerId: assistanceRequest.providerId,
      data: {
        oldStatus,
        newStatus: assistanceRequest.status,
        adminNotes: assistanceRequest.adminNotes
      },
      createdAt: new Date()
    };

    // Notify the user who made the request
    this.io.to(`user:${assistanceRequest.userId}`).emit('assistance_update', notification);

    // Notify provider if applicable
    if (assistanceRequest.providerId) {
      this.io.to(`user:${assistanceRequest.providerId}`).emit('assistance_update', notification);
    }

    // Notify admins
    this.io.to('role:admin').emit('assistance_update', notification);

    // Notify anyone in the assistance request room
    this.io.to(`assistance:${assistanceRequest.id}`).emit('assistance_update', notification);

    // Store notification
    await this.storeNotification(notification);
  }

  /**
   * Send notification when assistance request is resolved
   */
  async notifyAssistanceResolved(assistanceRequest: any): Promise<void> {
    const notification: NotificationData = {
      id: `assistance_resolved_${assistanceRequest.id}_${Date.now()}`,
      type: 'ASSISTANCE_RESOLVED',
      title: '✅ Assistance Request Resolved',
      message: `Your ${assistanceRequest.category.replace('_', ' ').toLowerCase()} issue has been resolved`,
      priority: 'MEDIUM',
      assistanceRequestId: assistanceRequest.id,
      userId: assistanceRequest.userId,
      providerId: assistanceRequest.providerId,
      data: {
        resolvedAt: assistanceRequest.resolvedAt,
        adminNotes: assistanceRequest.adminNotes
      },
      createdAt: new Date()
    };

    // Notify the user
    this.io.to(`user:${assistanceRequest.userId}`).emit('assistance_resolved', notification);

    // Notify provider if applicable
    if (assistanceRequest.providerId) {
      this.io.to(`user:${assistanceRequest.providerId}`).emit('assistance_resolved', notification);
    }

    // Store notification
    await this.storeNotification(notification);
  }

  /**
   * Send real-time statistics update to admins
   */
  async broadcastAssistanceStats(stats: any): Promise<void> {
    this.io.to('role:admin').emit('assistance_stats_update', {
      type: 'STATS_UPDATE',
      data: stats,
      timestamp: new Date()
    });
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): { total: number; admins: number; providers: number; users: number } {
    let admins = 0, providers = 0, users = 0;
    
    this.connectedUsers.forEach(user => {
      switch (user.role) {
        case 'ADMIN':
          admins++;
          break;
        case 'PROVIDER':
          providers++;
          break;
        case 'USER':
          users++;
          break;
      }
    });

    return {
      total: this.connectedUsers.size,
      admins,
      providers,
      users
    };
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId: string): boolean {
    return this.userSockets.has(userId);
  }

  /**
   * Send notification to specific user
   */
  async sendToUser(userId: string, event: string, data: any): Promise<boolean> {
    const socketIds = this.userSockets.get(userId);
    if (socketIds && socketIds.size > 0) {
      socketIds.forEach(socketId => {
        this.io.to(socketId).emit(event, data);
      });
      return true;
    }
    return false;
  }

  /**
   * Store notification in database
   */
  private async storeNotification(notification: NotificationData): Promise<void> {
    try {
      // Store in database for offline users
      // This would typically go to a notifications table
      console.log('Storing notification:', notification.id);
      
      // TODO: Implement database storage
      // await this.prisma.notification.create({
      //   data: {
      //     id: notification.id,
      //     type: notification.type,
      //     title: notification.title,
      //     message: notification.message,
      //     priority: notification.priority,
      //     userId: notification.userId,
      //     data: JSON.stringify(notification.data),
      //     createdAt: notification.createdAt
      //   }
      // });
    } catch (error) {
      console.error('Error storing notification:', error);
    }
  }

  /**
   * Send pending notifications to user
   */
  private async sendPendingNotifications(userId: string): Promise<void> {
    try {
      // TODO: Fetch and send pending notifications from database
      console.log(`Sending pending notifications to user: ${userId}`);
    } catch (error) {
      console.error('Error sending pending notifications:', error);
    }
  }

  /**
   * Send urgent email notification
   */
  private async sendUrgentEmailNotification(assistanceRequest: any): Promise<void> {
    try {
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
      
      await this.emailService.sendEmail({
        to: adminEmail,
        subject: `🚨 URGENT: Vehicle Assistance Request - ${assistanceRequest.category}`,
        template: 'urgent_assistance_alert',
        data: {
          requestId: assistanceRequest.id,
          category: assistanceRequest.category,
          priority: assistanceRequest.priority,
          reason: assistanceRequest.reason,
          location: assistanceRequest.location,
          userId: assistanceRequest.userId,
          createdAt: assistanceRequest.createdAt
        }
      });
    } catch (error) {
      console.error('Error sending urgent email notification:', error);
    }
  }

  /**
   * Cleanup and close connections
   */
  async close(): Promise<void> {
    this.io.close();
    await this.prisma.$disconnect();
  }
}

export default RealTimeNotificationService;
