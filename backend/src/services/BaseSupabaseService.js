const { supabase } = require('../utils/supabaseClient');

class BaseSupabaseService {
  constructor(tableName) {
    this.tableName = tableName;
    this.client = supabase;
  }

  // Create a new record
  async create(data) {
    try {
      const { data: newRecord, error } = await this.client
        .from(this.tableName)
        .insert(data)
        .select()
        .single();

      if (error) throw error;
      return newRecord;
    } catch (error) {
      console.error(`Error creating record in ${this.tableName}:`, error);
      throw error;
    }
  }

  // Get all records with optional filtering and pagination
  async findAll(options = {}) {
    const { 
      filter = {}, 
      page = 1, 
      limit = 10, 
      orderBy = 'created_at', 
      ascending = false 
    } = options;

    try {
      const query = this.client
        .from(this.tableName)
        .select('*', { count: 'exact' })
        .range((page - 1) * limit, page * limit - 1)
        .order(orderBy, { ascending });

      // Apply filters
      Object.entries(filter).forEach(([key, value]) => {
        query.eq(key, value);
      });

      const { data, count, error } = await query;

      if (error) throw error;

      return {
        records: data,
        totalRecords: count,
        page,
        limit
      };
    } catch (error) {
      console.error(`Error fetching records from ${this.tableName}:`, error);
      throw error;
    }
  }

  // Find by ID
  async findById(id) {
    try {
      const { data, error } = await this.client
        .from(this.tableName)
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error finding record in ${this.tableName}:`, error);
      throw error;
    }
  }

  // Update a record
  async update(id, data) {
    try {
      const { data: updatedRecord, error } = await this.client
        .from(this.tableName)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return updatedRecord;
    } catch (error) {
      console.error(`Error updating record in ${this.tableName}:`, error);
      throw error;
    }
  }

  // Delete a record
  async delete(id) {
    try {
      const { error } = await this.client
        .from(this.tableName)
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error(`Error deleting record in ${this.tableName}:`, error);
      throw error;
    }
  }

  // Advanced search with complex filtering
  async search(searchOptions) {
    try {
      const query = this.client.from(this.tableName).select('*');

      // Apply complex search conditions
      Object.entries(searchOptions).forEach(([key, value]) => {
        if (typeof value === 'object') {
          // Handle range queries
          if (value.min !== undefined) query.gte(key, value.min);
          if (value.max !== undefined) query.lte(key, value.max);
        } else {
          // Exact match
          query.eq(key, value);
        }
      });

      const { data, error } = await query;

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error searching records in ${this.tableName}:`, error);
      throw error;
    }
  }
}

module.exports = BaseSupabaseService;
