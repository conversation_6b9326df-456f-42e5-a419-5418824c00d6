import { PrismaClient } from '@prisma/client'

export class GeospatialSearchService {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  // Enhanced geospatial vehicle search
  async searchNearbyVehicles(
    latitude: number, 
    longitude: number, 
    radius: number = 10, // km
    filters?: {
      vehicleType?: string
      minPrice?: number
      maxPrice?: number
      features?: string[]
    },
    userId?: string
  ) {
    // Skip search logging since geospatialSearchLog model doesn't exist
    console.log('Geospatial search performed for:', latitude, longitude, radius);

    // Construct dynamic filter conditions
    const whereConditions: any = {
      availability: true
    }

    if (filters?.vehicleType) {
      whereConditions.type = filters.vehicleType
    }

    if (filters?.minPrice || filters?.maxPrice) {
      whereConditions.basePrice = {}
      if (filters.minPrice) whereConditions.basePrice.gte = filters.minPrice
      if (filters.maxPrice) whereConditions.basePrice.lte = filters.maxPrice
    }

    if (filters?.features) {
      whereConditions.features = {
        path: ['features'],
        array_contains: filters.features
      }
    }

    // Haversine formula for distance calculation (simplified for backend schema)
    const vehicles = await this.prisma.$queryRaw`
      SELECT 
        v.id, 
        v."vehicleType",
        v."dailyRate",
        v."location_latitude", 
        v."location_longitude",
        v."location_city",
        v."location_address",
        (6371 * acos(cos(radians(${latitude})) * cos(radians(v."location_latitude")) * 
        cos(radians(v."location_longitude") - radians(${longitude})) + 
        sin(radians(${latitude})) * sin(radians(v."location_latitude")))) AS distance
      FROM "Vehicle" v
      WHERE 
        v."location_latitude" IS NOT NULL 
        AND v."location_longitude" IS NOT NULL
        AND (6371 * acos(cos(radians(${latitude})) * cos(radians(v."location_latitude")) * 
        cos(radians(v."location_longitude") - radians(${longitude})) + 
        sin(radians(${latitude})) * sin(radians(v."location_latitude")))) <= ${radius}
        ${filters?.vehicleType ? `AND v."vehicleType" = '${filters.vehicleType}'` : ''}
        ${filters?.minPrice ? `AND v."dailyRate" >= ${filters.minPrice}` : ''}
        ${filters?.maxPrice ? `AND v."dailyRate" <= ${filters.maxPrice}` : ''}
      ORDER BY distance
      LIMIT 50
    `;

    return (vehicles as any[]).map((vehicle: any) => ({
      id: vehicle.id,
      vehicleType: vehicle.vehicleType,
      dailyRate: vehicle.dailyRate,
      location: {
        latitude: vehicle.location_latitude,
        longitude: vehicle.location_longitude,
        city: vehicle.location_city,
        address: vehicle.location_address
      },
      distance: vehicle.distance
    }));
  }

  // Advanced filtering options
  async getSearchFilters() {
    const vehicleTypes = await this.prisma.vehicle.findMany({
      distinct: ['vehicleType'],
      select: { vehicleType: true }
    });

    const priceRanges = await this.prisma.$queryRaw`
      SELECT 
        MIN("dailyRate") as minPrice, 
        MAX("dailyRate") as maxPrice 
      FROM "Vehicle"
    `;

    return {
      vehicleTypes: vehicleTypes.map(v => v.vehicleType),
      priceRanges: (priceRanges as any[])[0],
      features: [] // Simplify since features structure is complex
    };
  }
}

export default new GeospatialSearchService(); 