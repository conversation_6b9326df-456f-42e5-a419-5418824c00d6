import { PrismaClient } from '@prisma/client';
import { differenceInDays } from 'date-fns';

// Local enum since not exported from Prisma
enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FIXED = 'FIXED'
}

interface QuoteRequest {
  vehicleId: string;
  startDate: Date;
  endDate: Date;
  selectedAddOns?: string[];
  includeInsurance?: boolean;
  discountCode?: string;
}

interface QuoteResult {
  baseCost: number;
  addOnsCost: number;
  insuranceFee: number;
  bookingFee: number;
  discountAmount: number;
  totalPayable: number;
  providerEarnings: number;
}

export class QuoteService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async generateQuote(request: QuoteRequest): Promise<QuoteResult> {
    // Fetch vehicle details
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: request.vehicleId }
    });

    if (!vehicle) {
      throw new Error('Vehicle not found');
    }

    // Calculate rental days
    const rentalDays = differenceInDays(request.endDate, request.startDate) + 1;
    const dailyRate = vehicle.dailyRate?.toNumber() || 0;
    const baseCost = dailyRate * rentalDays;

    // Calculate add-ons cost
    let addOnsCost = 0;
    if (request.selectedAddOns && vehicle.addOnPrices) {
      const addOnPrices = JSON.parse(vehicle.addOnPrices as string);
      request.selectedAddOns.forEach(addOn => {
        addOnsCost += addOnPrices[addOn] || 0;
      });
    }

    // Calculate insurance fee
    const insuranceFee = request.includeInsurance && vehicle.hasInsurance 
      ? (vehicle.insurancePrice?.toNumber() || 0) * rentalDays 
      : 0;

    // Calculate subtotal
    const subtotal = baseCost + addOnsCost + insuranceFee;

    // Apply discount
    let discountAmount = 0;
    if (request.discountCode) {
      const discount = await this.validateDiscountCode(
        request.discountCode, 
        subtotal
      );
      discountAmount = discount?.amount || 0;
    }

    // Calculate booking fee (15%)
    const bookingFee = subtotal * 0.15;

    // Calculate total payable and provider earnings
    const totalPayable = subtotal + bookingFee - discountAmount;
    const providerEarnings = subtotal - discountAmount;

    return {
      baseCost,
      addOnsCost,
      insuranceFee,
      bookingFee,
      discountAmount,
      totalPayable,
      providerEarnings
    };
  }

  private async validateDiscountCode(
    code: string, 
    subtotal: number
  ): Promise<{ amount: number } | null> {
    // Stub: Discount code validation - would need discountCode model in Prisma
    // For now, return null (no discount)
    return null;
    
    /* Future implementation:
    const discountCode = await this.prisma.discountCode.findUnique({
      where: { code }
    });

    if (!discountCode) {
      return null;
    }

    // Check discount code validity
    const now = new Date();
    if (!discountCode.isActive || 
        now < discountCode.startDate || 
        now > discountCode.endDate ||
        (discountCode.usageLimit && discountCode.timesUsed >= discountCode.usageLimit) ||
        subtotal < discountCode.minBookingAmount.toNumber()) {
      return null;
    }

    // Calculate discount amount
    let discountAmount = 0;
    if (discountCode.discountType === DiscountType.PERCENTAGE) {
      discountAmount = subtotal * discountCode.value.toNumber() / 100;
    } else {
      discountAmount = discountCode.value.toNumber();
    }

    return { amount: discountAmount };
    */
  }
}

export default new QuoteService();
