import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface VehicleFormData {
  vehicleType: string;
  brand?: string;
  model?: string;
  year?: string;
  category?: string;
  dailyRate?: number;
  weeklyRate?: number;
  monthlyRate?: number;
  depositAmount?: number;
  location_city?: string;
  location_address?: string;
  availableUnits?: number;
  hasInsurance?: boolean;
  insurancePrice?: number;
  deliveryAvailable?: boolean;
  pickupAvailable?: boolean;
  dropoffAvailable?: boolean;
  helmetsIncluded?: number;
  raincoatsIncluded?: boolean;
  fullTank?: boolean;
  smartTags?: string[];
  addOns?: string[];
}

export interface VehicleWithDetails {
  id: string;
  vehicleType: string;
  brand?: string;
  model?: string;
  year?: string;
  category?: string;
  dailyRate?: number;
  weeklyRate?: number;
  monthlyRate?: number;
  depositAmount?: number;
  location_city?: string;
  location_address?: string;
  availableUnits: number;
  hasInsurance: boolean;
  insurancePrice?: number;
  deliveryAvailable: boolean;
  pickupAvailable: boolean;
  dropoffAvailable: boolean;
  helmetsIncluded: number;
  raincoatsIncluded: boolean;
  fullTank: boolean;
  smartTags: string[];
  addOns: string[];
  totalBookings: number;
  activeBookings: number;
  totalRevenue: number;
  averageRating: number;
  images: Array<{
    id: string;
    url: string;
    caption?: string;
    isPrimary: boolean;
  }>;
  createdAt: Date;
}

export class VehicleManagementService {
  /**
   * Get all vehicles for a provider
   */
  static async getProviderVehicles(
    providerId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    vehicles: VehicleWithDetails[];
    total: number;
    pages: number;
  }> {
    try {
      console.log('🚗 Fetching vehicles for provider:', providerId);
      const skip = (page - 1) * limit;

      const [vehicles, total] = await Promise.all([
        prisma.vehicle.findMany({
          where: { providerId },
          skip,
          take: limit
        }),
        prisma.vehicle.count({
          where: { providerId }
        })
      ]);

      // Simplify vehicle mapping for now
      const vehiclesWithDetails = vehicles.map(vehicle => ({
        id: vehicle.id,
        vehicleType: vehicle.vehicleType,
        brand: vehicle.brand || undefined,
        model: vehicle.model || undefined,
        year: vehicle.year || undefined,
        category: vehicle.category || undefined,
        dailyRate: vehicle.dailyRate ? Number(vehicle.dailyRate) : undefined,
        weeklyRate: vehicle.weeklyRate ? Number(vehicle.weeklyRate) : undefined,
        monthlyRate: vehicle.monthlyRate ? Number(vehicle.monthlyRate) : undefined,
        depositAmount: Number(vehicle.depositAmount),
        location_city: vehicle.location_city || undefined,
        location_address: vehicle.location_address || undefined,
        availableUnits: vehicle.availableUnits,
        hasInsurance: vehicle.hasInsurance,
        insurancePrice: vehicle.insurancePrice ? Number(vehicle.insurancePrice) : undefined,
        deliveryAvailable: vehicle.deliveryAvailable,
        pickupAvailable: vehicle.pickupAvailable,
        dropoffAvailable: vehicle.dropoffAvailable,
        helmetsIncluded: vehicle.helmetsIncluded,
        raincoatsIncluded: vehicle.raincoatsIncluded,
        fullTank: vehicle.fullTank,
        smartTags: vehicle.smartTags,
        addOns: vehicle.addOns,
        totalBookings: 0, // TODO: Calculate from bookings
        activeBookings: 0, // TODO: Calculate from active bookings
        totalRevenue: 0, // TODO: Calculate from completed bookings
        averageRating: 0, // TODO: Calculate from reviews
        images: [], // TODO: Get from images table
        createdAt: new Date() // TODO: Add createdAt field to Vehicle model
      }));

      console.log('✅ Found vehicles:', vehiclesWithDetails.length);

      return {
        vehicles: vehiclesWithDetails,
        total,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error fetching provider vehicles:', error);
      throw new Error('Failed to fetch vehicles');
    }
  }

  /**
   * Get vehicle by ID (for provider)
   */
  static async getVehicleById(vehicleId: string, providerId: string): Promise<VehicleWithDetails | null> {
    try {
      const vehicle = await prisma.vehicle.findFirst({
        where: { 
          id: vehicleId,
          providerId 
        },
        include: {
          images: true,
          _count: {
            select: {
              bookings: true
            }
          },
          bookings: {
            where: {
              status: { in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS'] }
            }
          },
          reviews: {
            select: {
              rating: true
            }
          }
        }
      });

      if (!vehicle) {
        return null;
      }

      const completedBookings = await prisma.booking.findMany({
        where: {
          vehicleId: vehicle.id,
          status: 'COMPLETED'
        },
        select: {
          totalPrice: true
        }
      });

      const totalRevenue = completedBookings.reduce(
        (sum, booking) => sum + Number(booking.totalPrice), 
        0
      );

      const averageRating = vehicle.reviews.length > 0
        ? vehicle.reviews.reduce((sum, review) => sum + review.rating, 0) / vehicle.reviews.length
        : 0;

      return {
        id: vehicle.id,
        vehicleType: vehicle.vehicleType,
        brand: vehicle.brand || undefined,
        model: vehicle.model || undefined,
        year: vehicle.year || undefined,
        category: vehicle.category || undefined,
        dailyRate: vehicle.dailyRate ? Number(vehicle.dailyRate) : undefined,
        weeklyRate: vehicle.weeklyRate ? Number(vehicle.weeklyRate) : undefined,
        monthlyRate: vehicle.monthlyRate ? Number(vehicle.monthlyRate) : undefined,
        depositAmount: Number(vehicle.depositAmount),
        location_city: vehicle.location_city || undefined,
        location_address: vehicle.location_address || undefined,
        availableUnits: vehicle.availableUnits,
        hasInsurance: vehicle.hasInsurance,
        insurancePrice: vehicle.insurancePrice ? Number(vehicle.insurancePrice) : undefined,
        deliveryAvailable: vehicle.deliveryAvailable,
        pickupAvailable: vehicle.pickupAvailable,
        dropoffAvailable: vehicle.dropoffAvailable,
        helmetsIncluded: vehicle.helmetsIncluded,
        raincoatsIncluded: vehicle.raincoatsIncluded,
        fullTank: vehicle.fullTank,
        smartTags: vehicle.smartTags,
        addOns: vehicle.addOns,
        totalBookings: vehicle._count.bookings,
        activeBookings: vehicle.bookings.length,
        totalRevenue,
        averageRating,
        images: vehicle.images.map(img => ({
          id: img.id,
          url: img.url,
          caption: img.caption || undefined,
          isPrimary: img.isPrimary
        })),
        createdAt: new Date() // Vehicle model doesn't have createdAt, using current date
      };
    } catch (error) {
      logger.error('Error fetching vehicle by ID:', error);
      throw new Error('Failed to fetch vehicle');
    }
  }

  /**
   * Create new vehicle
   */
  static async createVehicle(providerId: string, data: VehicleFormData): Promise<VehicleWithDetails> {
    try {
      const vehicle = await prisma.vehicle.create({
        data: {
          providerId,
          vehicleType: data.vehicleType,
          brand: data.brand,
          model: data.model,
          year: data.year,
          category: data.category,
          dailyRate: data.dailyRate,
          weeklyRate: data.weeklyRate,
          monthlyRate: data.monthlyRate,
          depositAmount: data.depositAmount || 0,
          location_city: data.location_city,
          location_address: data.location_address,
          availableUnits: data.availableUnits || 1,
          hasInsurance: data.hasInsurance || false,
          insurancePrice: data.insurancePrice || 0,
          deliveryAvailable: data.deliveryAvailable || false,
          pickupAvailable: data.pickupAvailable || false,
          dropoffAvailable: data.dropoffAvailable || false,
          helmetsIncluded: data.helmetsIncluded || 0,
          raincoatsIncluded: data.raincoatsIncluded || false,
          fullTank: data.fullTank || false,
          smartTags: data.smartTags || [],
          addOns: data.addOns || []
        },
        include: {
          images: true
        }
      });

      logger.info(`Vehicle created: ${vehicle.id} for provider: ${providerId}`);

      // Return the created vehicle with details
      const vehicleWithDetails = await this.getVehicleById(vehicle.id, providerId);
      return vehicleWithDetails!;
    } catch (error) {
      logger.error('Error creating vehicle:', error);
      throw new Error('Failed to create vehicle');
    }
  }
}

export default VehicleManagementService;
