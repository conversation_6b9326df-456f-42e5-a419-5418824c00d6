import Stripe from 'stripe'
import { supabase } from '../utils/supabaseClient'

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  // Use latest stable API version supported by stripe-js typings
  apiVersion: '2025-06-30.basil',
})

export interface CreatePaymentIntentPayload {
  amount: number
  currency: string
  customerEmail: string
  customerName: string
  description: string
  bookingId: string
  providerId: string
}

export interface CreateCheckoutSessionPayload {
  amount: number
  currency: string
  customerEmail: string
  customerName: string
  description: string
  bookingId: string
  providerId: string
  successUrl: string
  cancelUrl: string
}

export interface PaymentResult {
  success: boolean
  paymentIntentId?: string
  sessionId?: string
  clientSecret?: string
  customerId?: string
  error?: string
}

/**
 * Create or retrieve a Stripe customer
 */
export const createOrGetCustomer = async (email: string, name: string): Promise<Stripe.Customer> => {
  // Check if customer already exists
  const existingCustomers = await stripe.customers.list({
    email: email,
    limit: 1,
  })

  if (existingCustomers.data.length > 0) {
    return existingCustomers.data[0]
  }

  // Create new customer
  return await stripe.customers.create({
    email: email,
    name: name,
  })
}

/**
 * Create a payment intent for direct payment
 */
export const createPaymentIntent = async (payload: CreatePaymentIntentPayload): Promise<PaymentResult> => {
  try {
    // Create or get customer
    const customer = await createOrGetCustomer(payload.customerEmail, payload.customerName)

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(payload.amount * 100), // Convert to cents
      currency: payload.currency.toLowerCase(),
      customer: customer.id,
      description: payload.description,
      metadata: {
        bookingId: payload.bookingId,
        providerId: payload.providerId,
        customerEmail: payload.customerEmail,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    })

    return {
      success: true,
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      customerId: customer.id,
    }
  } catch (error) {
    console.error('Error creating payment intent:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * Create a checkout session for hosted payment page
 */
export const createCheckoutSession = async (payload: CreateCheckoutSessionPayload): Promise<PaymentResult> => {
  try {
    // Create or get customer
    const customer = await createOrGetCustomer(payload.customerEmail, payload.customerName)

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: payload.currency.toLowerCase(),
            product_data: {
              name: payload.description,
              description: `RentaHub Booking - ${payload.bookingId}`,
            },
            unit_amount: Math.round(payload.amount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: payload.successUrl,
      cancel_url: payload.cancelUrl,
      metadata: {
        bookingId: payload.bookingId,
        providerId: payload.providerId,
        customerEmail: payload.customerEmail,
      },
    })

    return {
      success: true,
      sessionId: session.id,
      customerId: customer.id,
    }
  } catch (error) {
    console.error('Error creating checkout session:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * Confirm a payment intent
 */
export const confirmPayment = async (paymentIntentId: string): Promise<PaymentResult> => {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    if (paymentIntent.status === 'succeeded') {
      return {
        success: true,
        paymentIntentId: paymentIntent.id,
      }
    }

    return {
      success: false,
      error: `Payment status: ${paymentIntent.status}`,
    }
  } catch (error) {
    console.error('Error confirming payment:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * Retrieve a checkout session
 */
export const getCheckoutSession = async (sessionId: string): Promise<Stripe.Checkout.Session | null> => {
  try {
    return await stripe.checkout.sessions.retrieve(sessionId)
  } catch (error) {
    console.error('Error retrieving checkout session:', error)
    return null
  }
}

/**
 * Create a Stripe Connect account for providers
 */
export const createConnectAccount = async (providerId: string, email: string, businessName: string): Promise<string | null> => {
  try {
    const account = await stripe.accounts.create({
      type: 'express',
      email: email,
      business_profile: {
        name: businessName,
      },
      metadata: {
        providerId: providerId,
      },
    })

    // Update provider in database with Stripe account ID
    await supabase
      .from('providers')
      .update({ stripe_account_id: account.id })
      .eq('id', providerId)

    return account.id
  } catch (error) {
    console.error('Error creating Connect account:', error)
    return null
  }
}

/**
 * Create account link for Stripe Connect onboarding
 */
export const createAccountLink = async (accountId: string, refreshUrl: string, returnUrl: string): Promise<string | null> => {
  try {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    })

    return accountLink.url
  } catch (error) {
    console.error('Error creating account link:', error)
    return null
  }
}

/**
 * Transfer funds to provider (after commission deduction)
 */
export const transferToProvider = async (
  amount: number,
  currency: string,
  providerId: string,
  bookingId: string,
  commissionRate: number = 0.15 // 15% default commission
): Promise<boolean> => {
  try {
    // Get provider's Stripe account ID
    const { data: provider, error } = await supabase
      .from('providers')
      .select('stripe_account_id')
      .eq('id', providerId)
      .single()

    if (error || !provider?.stripe_account_id) {
      console.error('Provider Stripe account not found:', error)
      return false
    }

    // Calculate provider amount after commission
    const commissionAmount = Math.round(amount * commissionRate)
    const providerAmount = amount - commissionAmount

    // Create transfer to provider
    await stripe.transfers.create({
      amount: Math.round(providerAmount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      destination: provider.stripe_account_id,
      description: `RentaHub booking payment - ${bookingId}`,
      metadata: {
        bookingId: bookingId,
        providerId: providerId,
        commissionAmount: commissionAmount.toString(),
      },
    })

    return true
  } catch (error) {
    console.error('Error transferring to provider:', error)
    return false
  }
}

/**
 * Get currency symbol for display
 */
export const getCurrencySymbol = (currency: string): string => {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    IDR: 'Rp',
    AUD: '$',
    CAD: '$',
    SGD: '$',
  }

  return symbols[currency.toUpperCase()] || currency.toUpperCase()
}

/**
 * Convert price for display (if needed for multi-currency support)
 */
export const convertPrice = async (amount: number, fromCurrency: string, toCurrency: string): Promise<number> => {
  // For now, return the same amount
  // In the future, integrate with a currency conversion API
  return amount
}

export default stripe 