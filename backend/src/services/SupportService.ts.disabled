import { PrismaClient } from '@prisma/client';
import { EmailService } from './EmailService';
import { NotificationService } from './CommunicationServices';
import { MonitoringService } from './MonitoringService';
import { config } from '../config/env';
import nodemailer from 'nodemailer';

// Custom error classes for specific support scenarios
class SupportTicketError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SupportTicketError';
  }
}

class SOSAlertError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SOSAlertError';
  }
}

export class SupportService {
  private prisma: PrismaClient;
  private emailService: EmailService;
  private notificationService: NotificationService | null = null;
  private monitoringService: MonitoringService;
  private transporter: nodemailer.Transporter;

  constructor() {
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    // Don't initialize NotificationService here to avoid startup dependency issues
    this.monitoringService = new MonitoringService();
    this.transporter = nodemailer.createTransporter({
      host: config.SMTP_HOST,
      port: config.SMTP_PORT,
      secure: false,
      auth: {
        user: config.SMTP_USER,
        pass: config.SMTP_PASS
      }
    });
  }

  // Support ticket management methods
  private prisma: PrismaClient;
  private emailService: EmailService;
  private notificationService: NotificationService;
  private monitoringService: MonitoringService;
  private transporter: nodemailer.Transporter;

  constructor() {
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    this.notificationService = new NotificationService();
    this.monitoringService = new MonitoringService();
    this.transporter = nodemailer.createTransport({
      host: config.SMTP_HOST,
      port: config.SMTP_PORT,
      secure: false,
      auth: {
        user: config.SMTP_USER,
        pass: config.SMTP_PASS
      }
    });
  }

  /**
   * Validate support ticket input
   */
  private validateSupportTicket(data: {
    message: string;
    category: string;
  }): void {
    // Message length validation
    if (data.message.trim().length < 10) {
      throw new SupportTicketError('Support ticket message must be at least 10 characters long');
    }

    // Category validation
    const validCategories = ['PAYMENT', 'VEHICLE', 'DAMAGE', 'BOOKING', 'OTHER'];
    if (!validCategories.includes(data.category)) {
      throw new SupportTicketError('Invalid support ticket category');
    }
  }

  /**
   * Submit a new support ticket with enhanced error handling
   */
  async submitSupportTicket(data: {
    userId?: string;
    providerId?: string;
    bookingId?: string;
    role: string;
    category: 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'OTHER';
    message: string;
    subject?: string;
  }) {
    try {
      // Validate input
      this.validateSupportTicket({
        message: data.message,
        category: data.category
      });

      // Check user authentication
      if (!data.userId && !data.providerId) {
        throw new SupportTicketError('User or provider must be authenticated');
      }

      const ticket = await this.prisma.supportTicket.create({
        data: {
          userId: data.userId!,
          subject: data.subject || 'Support Request',
          description: data.message,
          message: data.message,
          providerId: data.providerId,
          bookingId: data.bookingId,
          role: data.role,
          category: data.category,
          status: 'OPEN'
        }
      });

      // Log ticket creation
      this.monitoringService.trackEvent('support_ticket_created', {
        ticketId: ticket.id,
        category: ticket.category,
        role: data.role
      });

      // Notify admin via email
      await this.emailService.sendEmail({
        to: process.env.SUPPORT_EMAIL || '<EMAIL>',
        subject: 'New Support Ticket Submitted',
        template: 'support_ticket',
        data: {
          ticketId: ticket.id,
          category: ticket.category,
          message: ticket.message
        }
      });

      // Send notification to user/provider
      await this.notificationService.sendNotification({
        userId: data.userId || data.providerId || '',
        type: 'general',
        title: 'Support Ticket Created',
        message: `Your support ticket (${ticket.id}) has been submitted and is being reviewed.`
      });

      return ticket;
    } catch (error) {
      // Log and track errors
      this.monitoringService.trackError('support_ticket_submission_error', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        context: { ...data }
      });

      // Rethrow or handle specific error types
      if (error instanceof SupportTicketError) {
        throw error;
      } else {
        throw new SupportTicketError('Failed to submit support ticket');
      }
    }
  }

  /**
   * Send an SOS alert with comprehensive validation
   */
  async sendSOSAlert(data: {
    bookingId: string;
    userId?: string;
    providerId?: string;
    reason: string;
    location?: string;
    message?: string;
  }) {
    try {
      // Validate reason
      if (data.reason.trim().length < 10) {
        throw new SOSAlertError('SOS alert reason must be at least 10 characters long');
      }

      // Check if booking is active
      const booking = await this.prisma.booking.findUnique({
        where: { id: data.bookingId },
        select: { status: true }
      });

      if (!booking || booking.status !== 'IN_PROGRESS') {
        throw new SOSAlertError('SOS alerts can only be sent during active bookings');
      }

      // Check for existing active SOS alert
      const existingSOSAlert = await this.prisma.sOSAlert.findFirst({
        where: { 
          userId: data.userId,
          status: 'PENDING' 
        }
      });

      if (existingSOSAlert) {
        throw new SOSAlertError('An active SOS alert already exists for this booking');
      }

      const sosAlert = await this.prisma.sOSAlert.create({
        data: {
          userId: data.userId!,
          bookingId: data.bookingId,
          providerId: data.providerId,
          location: data.location,
          message: data.message,
          reason: data.reason,
          status: 'PENDING'
        },
        select: {
          id: true,
          userId: true,
          bookingId: true,
          providerId: true,
          location: true,
          message: true,
          reason: true,
          status: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // Log SOS alert
      this.monitoringService.trackEvent('sos_alert_created', {
        alertId: sosAlert.id,
        bookingId: sosAlert.bookingId
      });

      // Send urgent email to admin
      await this.emailService.sendEmail({
        to: process.env.EMERGENCY_EMAIL || '<EMAIL>',
        subject: '🚨 URGENT: SOS Alert During Booking',
        template: 'sos_alert',
        data: {
          alertId: sosAlert.id,
          bookingId: sosAlert.bookingId,
          reason: sosAlert.reason
        }
      });

      // Trigger immediate notification
      await this.notificationService.sendNotification({
        userId: 'admin', // Specific admin user or group
        type: 'dispute',
        title: 'SOS Alert Received',
        message: `Urgent SOS alert for Booking ${sosAlert.bookingId}`
      });

      return sosAlert;
    } catch (error) {
      // Log and track errors
      this.monitoringService.trackError('sos_alert_submission_error', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        context: { ...data }
      });

      // Rethrow or handle specific error types
      if (error instanceof SOSAlertError) {
        throw error;
      } else {
        throw new SOSAlertError('Failed to send SOS alert');
      }
    }
  }

  // Existing methods with similar error handling
  async getUserSupportTickets(userId: string, role: 'user' | 'provider') {
    try {
      return await this.prisma.supportTicket.findMany({
        where: {
          [role === 'user' ? 'userId' : 'providerId']: userId
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
    } catch (error) {
      this.monitoringService.trackError('support_tickets_retrieval_error', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        context: { userId, role }
      });
      throw new SupportTicketError('Failed to retrieve support tickets');
    }
  }

  async updateSupportTicketStatus(ticketId: string, status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'ESCALATED') {
    try {
      const updatedTicket = await this.prisma.supportTicket.update({
        where: { id: ticketId },
        data: { 
          status,
          updatedAt: new Date()
        }
      });

      // Log ticket status update
      this.monitoringService.trackEvent('support_ticket_status_updated', {
        ticketId,
        newStatus: status
      });

      // Notify user about ticket status change
      await this.notificationService.sendNotification({
        userId: updatedTicket.userId || updatedTicket.providerId || '',
        type: 'general',
        title: 'Support Ticket Update',
        message: `Your support ticket (${ticketId}) status has been updated to ${status}.`
      });

      return updatedTicket;
    } catch (error) {
      this.monitoringService.trackError('support_ticket_status_update_error', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        context: { ticketId, status }
      });
      throw new SupportTicketError('Failed to update support ticket');
    }
  }

  async createSupportTicket(data: {
    userId?: string;
    providerId?: string;
    bookingId?: string;
    category: 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'OTHER';
    message: string;
    role: 'USER' | 'PROVIDER' | 'ADMIN';
  }) {
    try {
      const ticket = await this.prisma.supportTicket.create({
        data: {
          userId: data.userId!,
          providerId: data.providerId,
          bookingId: data.bookingId,
          subject: 'Support Request',
          description: data.message,
          message: data.message,
          role: data.role,
          category: data.category,
          status: 'OPEN'
        }
      });

      // Send email notification
      await this.sendSupportTicketEmail(ticket);

      return ticket;
    } catch (error) {
      console.error('Error creating support ticket:', error);
      throw new Error('Failed to create support ticket');
    }
  }

  async createSOSAlert(data: {
    bookingId: string;
    userId?: string;
    providerId?: string;
    reason: string;
  }) {
    try {
      const alert = await this.prisma.sOSAlert.create({
        data: {
          userId: data.userId!,
          bookingId: data.bookingId,
          providerId: data.providerId,
          reason: data.reason,
          status: 'PENDING'
        }
      });

      // Send emergency email
      await this.sendSOSAlertEmail(alert);

      return alert;
    } catch (error) {
      console.error('Error creating SOS alert:', error);
      throw new Error('Failed to create SOS alert');
    }
  }

  async getSupportTicketsByUser(userId: string) {
    return this.prisma.supportTicket.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
  }

  async getSOSAlertsByUser(userId: string) {
    return this.prisma.sOSAlert.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
  }

  private async sendSupportTicketEmail(ticket: any) {
    try {
      await this.transporter.sendMail({
        from: config.SUPPORT_EMAIL,
        to: config.ADMIN_EMAIL,
        subject: `New Support Ticket - ${ticket.category}`,
        html: `
          <h2>New Support Ticket</h2>
          <p>Category: ${ticket.category}</p>
          <p>Message: ${ticket.message}</p>
          <p>Created At: ${ticket.createdAt}</p>
        `
      });
    } catch (error) {
      console.error('Failed to send support ticket email:', error);
    }
  }

  private async sendSOSAlertEmail(alert: any) {
    try {
      await this.transporter.sendMail({
        from: config.SUPPORT_EMAIL,
        to: config.EMERGENCY_EMAIL,
        subject: 'URGENT: SOS Alert Received',
        html: `
          <h2>Emergency SOS Alert</h2>
          <p>Booking ID: ${alert.bookingId}</p>
          <p>Reason: ${alert.reason}</p>
          <p>Created At: ${alert.createdAt}</p>
        `
      });
    } catch (error) {
      console.error('Failed to send SOS alert email:', error);
    }
  }
}
