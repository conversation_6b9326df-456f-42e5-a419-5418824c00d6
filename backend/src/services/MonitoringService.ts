import { logger } from '../utils/logger';

export interface ErrorTrackingData {
  errorCode: string;
  errorMessage: string;
  context?: Record<string, any>;
  timestamp?: Date;
}

export interface NotificationAttemptData {
  userId: string;
  notificationType: string;
  attempts: Record<string, boolean>;
  timestamp?: Date;
}

export class MonitoringService {
  private logger: typeof logger;

  constructor() {
    this.logger = logger;
  }

  /**
   * Track application events
   */
  trackEvent(event: string, data?: any): void {
    this.logger.info(`Event: ${event}`, data);
  }

  /**
   * Track application errors
   */
  trackError(errorCode: string, data?: any): void {
    const errorEntry: ErrorTrackingData = {
      errorCode,
      errorMessage: typeof data === 'string' ? data : data?.message || 'Unknown error',
      context: typeof data === 'object' ? data : { data },
      timestamp: new Date()
    };

    // Log to file
    this.logger.error(`Error: ${errorCode}`, errorEntry);

    // In a real-world scenario, you might also:
    // - Send to error tracking service like Sentry
    // - Store in a database for later analysis
    // - Trigger alerts
    this.sendErrorAlert(errorEntry);
  }

  /**
   * Log general information
   */
  log(data: any): void {
    this.logger.info('Monitoring Log', data);
  }

  /**
   * Capture exceptions
   */
  captureException(error: Error, context?: any): void {
    this.logger.error('Exception captured', { error: error.message, context });
  }

  /**
   * Track metrics
   */
  trackMetric(metric: string, data?: any): void {
    this.logger.info(`Metric: ${metric}`, data);
  }

  /**
   * Track notification attempts
   */
  trackNotificationAttempts(data: NotificationAttemptData): void {
    const attemptEntry = {
      ...data,
      timestamp: new Date()
    };

    // Log notification attempts
    this.logger.info('Notification attempts tracked', attemptEntry);

    // Analyze success rate
    const successRate = this.calculateNotificationSuccessRate(data.attempts);
    if (successRate < 0.5) {
      this.sendNotificationFailureAlert(attemptEntry);
    }
  }

  /**
   * Calculate notification success rate
   */
  private calculateNotificationSuccessRate(attempts: Record<string, boolean>): number {
    const totalAttempts = Object.keys(attempts).length;
    const successfulAttempts = Object.values(attempts).filter(Boolean).length;
    return totalAttempts > 0 ? successfulAttempts / totalAttempts : 1;
  }

  /**
   * Send error alert (placeholder for actual alerting mechanism)
   */
  private sendErrorAlert(errorEntry: ErrorTrackingData): void {
    // Implement alerting logic
    // Could integrate with Slack, PagerDuty, etc.
    console.error('ALERT:', errorEntry);
  }

  /**
   * Send notification failure alert
   */
  private sendNotificationFailureAlert(attemptEntry: NotificationAttemptData): void {
    // Implement alerting logic for notification failures
    console.warn('NOTIFICATION FAILURE ALERT:', attemptEntry);
  }
}

// Export a singleton instance
export const monitoringService = new MonitoringService();
export default monitoringService; 