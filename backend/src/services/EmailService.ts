import nodemailer from 'nodemailer';
import { supportTicketTemplate, sosAlertTemplate } from '../utils/emailTemplates/supportTicket';
import { config } from '../config/env';
import { logger } from '../utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.EMAIL_HOST,
      port: config.EMAIL_PORT,
      secure: config.EMAIL_SECURE,
      auth: {
        user: config.EMAIL_USER,
        pass: config.EMAIL_PASS
      }
    });
  }

  async sendEmail(options: {
    to: string;
    subject: string;
    template: string;
    data: Record<string, any>;
  }) {
    try {
      let htmlContent = '';

      switch (options.template) {
        case 'support_ticket':
          htmlContent = supportTicketTemplate({
            ticketId: options.data.ticketId || 'N/A',
            category: options.data.category || 'General',
            message: options.data.message || 'No message',
            userName: options.data.userName,
            userEmail: options.data.userEmail
          });
          break;
        case 'sos_alert':
          htmlContent = sosAlertTemplate({
            alertId: options.data.alertId || 'N/A',
            bookingId: options.data.bookingId || 'N/A',
            reason: options.data.reason || 'Unknown',
            userName: options.data.userName,
            userEmail: options.data.userEmail
          });
          break;
        default:
          htmlContent = `<p>${JSON.stringify(options.data)}</p>`;
      }

      await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to: options.to,
        subject: options.subject,
        html: htmlContent
      });
    } catch (error) {
      logger.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string, resetLink: string) {
    try {
      const htmlContent = `
        <html>
          <body>
            <h1>Password Reset Request</h1>
            <p>You have requested to reset your password for your RentaHub account.</p>
            <p>Click the link below to reset your password:</p>
            <a href="${resetLink}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Reset Password
            </a>
            <p>If you didn't request this password reset, please ignore this email.</p>
            <p>This link will expire in 1 hour.</p>
            <p>Best regards,<br>The RentaHub Team</p>
          </body>
        </html>
      `;

      await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to: email,
        subject: 'Password Reset Request - RentaHub',
        html: htmlContent
      });

      logger.info('Password reset email sent', { email });
    } catch (error) {
      logger.error('Failed to send password reset email', { email, error });
      throw new Error('Failed to send password reset email');
    }
  }

  /**
   * Send email verification email
   */
  async sendEmailVerification(email: string, verificationLink: string) {
    try {
      const htmlContent = `
        <html>
          <body>
            <h1>Email Verification</h1>
            <p>Thank you for signing up with RentaHub!</p>
            <p>Please verify your email address by clicking the link below:</p>
            <a href="${verificationLink}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Verify Email
            </a>
            <p>If you didn't create an account with RentaHub, please ignore this email.</p>
            <p>Best regards,<br>The RentaHub Team</p>
          </body>
        </html>
      `;

      await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to: email,
        subject: 'Verify Your Email - RentaHub',
        html: htmlContent
      });

      logger.info('Email verification sent', { email });
    } catch (error) {
      logger.error('Failed to send email verification', { email, error });
      throw new Error('Failed to send email verification');
    }
  }

  // Existing methods from previous implementation
  async sendRiskAssessmentAlert(
    email: string, 
    riskScore: number, 
    vehicleId: string
  ) {
    try {
      await this.transporter.sendMail({
        from: config.EMAIL_FROM,
        to: email,
        subject: 'Risk Assessment Alert',
        html: this.generateRiskAssessmentEmail(riskScore, vehicleId)
      });
    } catch (error) {
      logger.error('Failed to send risk assessment email', { email, error });
    }
  }

  private generateRiskAssessmentEmail(
    riskScore: number, 
    vehicleId: string
  ): string {
    const riskLevel = riskScore < 0.3 ? 'Low' : 
                      riskScore < 0.7 ? 'Medium' : 'High';

    return `
      <html>
        <body>
          <h1>Risk Assessment Alert</h1>
          <p>A new risk assessment has been generated for Vehicle ID: ${vehicleId}</p>
          <ul>
            <li><strong>Risk Score:</strong> ${(riskScore * 100).toFixed(2)}%</li>
            <li><strong>Risk Level:</strong> ${riskLevel}</li>
          </ul>
          <p>Please review the vehicle's insurance and deposit configuration.</p>
        </body>
      </html>
    `;
  }
}

export default EmailService;
