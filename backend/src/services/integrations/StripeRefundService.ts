import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';
import { logger } from '../../utils/logger';
import monitoringService from '../../utils/monitoring';
import { NotificationService } from '../NotificationService';

export class StripeRefundService {
  private stripe: Stripe;
  private prisma: PrismaClient;
  private notificationService: NotificationService | null = null;

  constructor(stripeSecretKey: string = process.env.STRIPE_SECRET_KEY || '') {
    this.stripe = new Stripe(stripeSecretKey);
    this.prisma = new PrismaClient();
    // Don't initialize NotificationService here to avoid startup dependency issues
  }

  private getNotificationService(): NotificationService {
    if (!this.notificationService) {
      this.notificationService = new NotificationService();
    }
    return this.notificationService;
  }

  /**
   * Comprehensive refund handling for webhook events
   * @param event Stripe refund webhook event
   */
  async handleRefundWebhook(event: Stripe.Event) {
    const refund = event.data.object as Stripe.Refund;

    try {
      // Validate refund data
      if (!refund.id || !refund.payment_intent) {
        throw new Error('Invalid refund data: Missing ID or payment intent');
      }

      // Retrieve associated payment intent for additional context
      const paymentIntent = await this.stripe.paymentIntents.retrieve(
        refund.payment_intent as string
      );

      // Determine refund type and amount
      const isFullRefund = paymentIntent.amount === refund.amount;
      const refundReason = this.determineRefundReason(refund);

      // Upsert refund record with comprehensive details
      const refundRecord = await this.prisma.refund.upsert({
        where: { id: refund.id },
        update: {
          status: refund.status || 'pending',
          amount: refund.amount / 100, // Convert to dollars
          reason: refundReason,
          updatedAt: new Date()
        },
        create: {
          id: refund.id,
          bookingId: '00000000-0000-0000-0000-000000000000', // Placeholder booking ID
          amount: refund.amount / 100,
          reason: refundReason,
          status: refund.status || 'pending'
        }
      });

      // Update associated booking or transaction status
      await this.updateAssociatedEntities({
        paymentId: refund.payment_intent as string,
        isFullRefund
      }, isFullRefund);

      // Log detailed refund information
      logger.info('Processed Stripe refund webhook', {
        refundId: refund.id,
        status: refund.status,
        amount: refund.amount / 100,
        isFullRefund,
        reason: refundReason
      });

      // Send notifications if needed
      await this.notifyStakeholders({
        id: refundRecord.id,
        amount: Number(refundRecord.amount),
        status: refundRecord.status
      });

      // Capture monitoring metrics
      monitoringService.trackEvent('stripe_refund', {
        amount: refund.amount / 100,
        status: refund.status,
        isFullRefund
      });

      return refundRecord;
    } catch (error) {
      // Comprehensive error handling
      logger.error('Error processing refund webhook', { 
        refundId: refund.id,
        error 
      });

      // Capture critical exception
      monitoringService.trackError('stripe-refund-webhook', {
        context: 'stripe-refund-webhook',
        refundData: {
          id: refund.id,
          amount: refund.amount,
          paymentIntent: refund.payment_intent
        }
      });

      // Trigger alert for manual review
      await this.prisma.systemAlert.create({
        data: {
          type: 'STRIPE_REFUND_ERROR',
          severity: 'CRITICAL',
          message: `Stripe Refund Webhook Processing Failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        }
      });

      throw error;
    }
  }

  /**
   * Determine the reason for refund
   * @param refund Stripe refund object
   * @returns Reason for refund
   */
  private determineRefundReason(refund: Stripe.Refund): string {
    // Map Stripe refund reasons to our system
    const reasonMap: Record<string, string> = {
      'requested_by_customer': 'Customer Request',
      'duplicate': 'Duplicate Charge',
      'fraudulent': 'Fraudulent Activity',
      'subscription_canceled': 'Subscription Cancellation'
    };

    return refund.reason 
      ? reasonMap[refund.reason] || refund.reason 
      : 'Unspecified Reason';
  }

  /**
   * Update associated booking or transaction status
   * @param refundRecord Refund record
   * @param isFullRefund Whether this is a full refund
   */
  private async updateAssociatedEntities(
    refundRecord: { paymentId: string, isFullRefund: boolean }, 
    isFullRefund: boolean
  ) {
    try {
      // Find associated booking
      const booking = await this.prisma.booking.findFirst({
        where: { payment: { id: refundRecord.paymentId } }
      });

      if (booking) {
        await this.prisma.booking.update({
          where: { id: booking.id },
          data: {
            status: isFullRefund ? 'CANCELLED' : 'CONFIRMED'
            // Note: paymentStatus doesn't exist in schema
          }
        });
      }
    } catch (error) {
      logger.error('Error updating associated entities', { 
        refundId: refundRecord.paymentId, 
        error 
      });
    }
  }

  /**
   * Notify relevant stakeholders about the refund
   * @param refundRecord Refund record
   */
  private async notifyStakeholders(refundRecord: { 
    id: string, 
    amount: number, 
    status: string 
  }) {
    try {
      // Notify customer (would need to fetch booking first)
      // await this.notificationService.sendRefundFailedNotification(booking, 'Refund failed');
      logger.error('Refund failed - notification needed', {
        refundId: refundRecord.id,
        amount: refundRecord.amount,
        status: refundRecord.status
      });

      // Notify admin if needed
      if (refundRecord.status === 'failed') {
        // Admin alert would be sent via monitoring service
        logger.error('Refund failed - admin alert needed', {
          type: 'REFUND_FAILED',
          details: `Refund ${refundRecord.id} failed`
        });
      }
    } catch (error) {
      logger.error('Error sending refund notifications', { 
        refundId: refundRecord.id, 
        error 
      });
    }
  }

  /**
   * Manually initiate a refund
   * @param paymentIntentId Payment intent to refund
   * @param amount Amount to refund (optional, defaults to full amount)
   */
  async initiateRefund(paymentIntentId: string, amount?: number) {
    try {
      // Retrieve payment intent to get original charge details
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      
      // Determine refund amount
      const refundAmount = amount 
        ? Math.round(amount * 100)  // Convert to cents
        : paymentIntent.amount;  // Full amount

      // Create refund
      const refund = await this.stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: refundAmount
      });

      // Log refund initiation
      logger.info('Refund initiated', {
        paymentIntentId,
        refundId: refund.id,
        amount: refundAmount / 100
      });

      return refund;
    } catch (error) {
      logger.error('Error initiating refund', { 
        paymentIntentId, 
        error 
      });

      monitoringService.trackError('Refund upsert error', {
        context: 'manual-refund-initiation',
        paymentIntentId
      });

      throw error;
    }
  }

  /**
   * Create a refund for a given payment
   * @param paymentIntentId Stripe payment intent ID
   * @param amount Amount to refund in cents
   * @param reason Reason for refund
   */
  async createRefund(paymentIntentId: string, amount?: number, reason?: string) {
    try {
      const refund = await this.stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount,
        reason: 'requested_by_customer' as any,
      });

      // Store refund record in database
      const refundRecord = await this.prisma.refund.create({
        data: {
          id: refund.id,
          status: refund.status || 'pending',
          amount: refund.amount / 100,
          reason: reason || 'requested_by_customer',
          bookingId: '', // This should be determined from the payment intent
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      return {
        success: true,
        refund: refundRecord
      };
    } catch (error) {
      logger.error('Failed to create refund:', error);
      throw error;
    }
  }
}
