import Stripe from 'stripe';
import { Request, Response, NextFunction } from "express";
// import { PrismaClient, BookingStatus, Prisma } from '@prisma/client';
import { logger } from '../../utils/logger';
import monitoringService from '../../utils/monitoring';

export class StripeWebhookService {
  private stripe: Stripe | null = null;
  // private prisma: PrismaClient;

  constructor(stripeSecretKey?: string) {
    if (stripeSecretKey) {
      this.stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2025-06-30.basil'
      });
    } else {
      logger.warn('Stripe secret key not provided, webhook service will be disabled');
    }
    // this.prisma = new PrismaClient();
  }

  /**
   * Main webhook event handler
   * @param rawBody Raw request body buffer
   * @param signature Stripe signature header
   * @param res Express response object
   */
  async handleWebhookEvent(rawBody: Buffer, signature: string, res: Response) {
    try {
      // Check if Stripe is initialized
      if (!this.stripe) {
        throw new Error('Stripe service is not initialized - missing API key');
      }

      // Log raw details for debugging
      logger.info('Webhook Verification Attempt', {
        rawBodyLength: rawBody.length,
        signatureLength: signature.length,
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET ? 'SET' : 'MISSING'
      });

      // More explicit error handling
      if (!rawBody) {
        throw new Error('Raw body is empty or undefined');
      }
      if (!signature) {
        throw new Error('Signature header is missing');
      }
      if (!process.env.STRIPE_WEBHOOK_SECRET) {
        throw new Error('STRIPE_WEBHOOK_SECRET is not configured');
      }

      // Verify webhook signature with Stripe's CryptoProvider
      const event = this.stripe.webhooks.constructEvent(
        rawBody, 
        signature, 
        process.env.STRIPE_WEBHOOK_SECRET
      );

      // Log event for traceability
      logger.info('Stripe Webhook Received', {
        eventType: event.type,
        eventId: event.id
      });

      // Process event based on type
      const processingResult = await this.processWebhookEvent(event);

      // Track webhook processing metrics
      monitoringService.trackEvent('stripe_webhook_processed', {
        eventType: event.type,
        processingTime: processingResult.processingTime
      });

      return { 
        success: true, 
        statusCode: 200, 
        message: processingResult.message 
      };
    } catch (error: any) {
      // Enhanced error logging
      logger.error('Webhook Verification Failed', {
        errorMessage: error.message,
        errorType: error.type,
        rawBodyLength: rawBody?.length || 0,
        signature: signature?.substring(0, 10) + '...'
      });

      // Rethrow for standard error handling
      throw error;
    }
  }

  /**
   * Process different Stripe webhook event types
   * @param event Stripe webhook event
   */
  private async processWebhookEvent(event: Stripe.Event) {
    const startTime = Date.now();

    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event);
          break;
        
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event);
          break;
        
        case 'charge.succeeded':
          await this.handleChargeSucceeded(event);
          break;
        
        case 'charge.failed':
          await this.handleChargeFailed(event);
          break;
        
        case 'charge.refunded':
          await this.handleRefund(event);
          break;
        
        case 'payment_intent.created':
          await this.handlePaymentIntentCreated(event);
          break;
        
        default:
          await this.logUnknownEvent(event);
      }

      return {
        processingTime: Date.now() - startTime,
        message: `Successfully processed ${event.type} event`
      };
    } catch (error: any) {
      logger.error('Webhook event processing error', {
        eventType: event.type,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Handle unknown Stripe webhook events
   */
  private async logUnknownEvent(event: Stripe.Event) {
    try {
      // await this.prisma.integrationLog.create({
      //   data: {
      //     service: 'stripe_webhook',
      //     request_body: JSON.stringify({
      //       type: event.type,
      //       id: event.id
      //     }),
      //     status_code: 200,
      //     error_message: null,
      //     response_body: null,
      //     created_at: new Date()
      //   }
      // });

      logger.warn(`Unhandled Stripe event type: ${event.type}`);
    } catch (error) {
      logger.error('Error logging unknown event', { error });
    }
  }

  /**
   * Handle webhook processing errors
   */
  private async handleWebhookError(
    message: string, 
    metadata: Record<string, any>, 
    statusCode: number = 400
  ) {
    try {
      logger.error('Webhook Processing Error', { 
        message, 
        ...metadata 
      });

      // await this.prisma.systemAlert.create({
      //   data: {
      //     type: 'WEBHOOK_ERROR',
      //     severity: 'CRITICAL',
      //     message: message
      //   }
      // });
    } catch (error) {
      logger.error('Failed to log webhook error', { error });
    }
  }

  /**
   * Handle successful payment intent
   */
  private async handlePaymentIntentSucceeded(event: Stripe.Event) {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    
    // Basic logging and minimal processing
    logger.info('Payment Intent Succeeded', { 
      paymentIntentId: paymentIntent.id 
    });
  }

  /**
   * Handle failed payment intent
   */
  private async handlePaymentIntentFailed(event: Stripe.Event) {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    
    // Basic logging and minimal processing
    logger.warn('Payment Intent Failed', { 
      paymentIntentId: paymentIntent.id 
    });
  }

  /**
   * Handle successful charge
   */
  private async handleChargeSucceeded(event: Stripe.Event) {
    const charge = event.data.object as Stripe.Charge;
    
    // Basic logging and minimal processing
    logger.info('Charge Succeeded', { 
      chargeId: charge.id 
    });
  }

  /**
   * Handle failed charge
   */
  private async handleChargeFailed(event: Stripe.Event) {
    const charge = event.data.object as Stripe.Charge;
    
    // Basic logging and minimal processing
    logger.warn('Charge Failed', { 
      chargeId: charge.id 
    });
  }

  /**
   * Handle refund event
   */
  private async handleRefund(event: Stripe.Event) {
    const refund = event.data.object as Stripe.Refund;
    
    // Basic logging and minimal processing
    logger.info('Refund Processed', { 
      refundId: refund.id 
    });
  }

  /**
   * Handle payment intent creation
   */
  private async handlePaymentIntentCreated(event: Stripe.Event) {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    
    // Basic logging and minimal processing
    logger.info('Payment Intent Created', { 
      paymentIntentId: paymentIntent.id 
    });
  }

  private async createPaymentRecord(
    bookingId: string, 
    paymentAmount: any, // Prisma.Decimal, // This type was removed, so using 'any' for now
    providerId: string
  ) {
    // return this.prisma.payment.create({
    //   data: {
    //     bookingId: bookingId,
    //     amount: paymentAmount,
    //     status: 'SUCCEEDED',
    //     paymentMethod: 'STRIPE',
    //     providerId: providerId,
    //     providerEarnings: new Prisma.Decimal(0)
    //   }
    // });
    return Promise.resolve(null); // Placeholder as prisma is commented out
  }

  private async createRefundRecord(
    bookingId: string, 
    refundAmount: any // Prisma.Decimal // This type was removed, so using 'any' for now
  ) {
    // return this.prisma.refund.create({
    //   data: {
    //     bookingId: bookingId,
    //     amount: refundAmount,
    //     status: 'PENDING'
    //   }
    // });
    return Promise.resolve(null); // Placeholder as prisma is commented out
  }
}
