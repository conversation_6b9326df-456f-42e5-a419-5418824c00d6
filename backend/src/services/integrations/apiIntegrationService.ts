import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface ExternalAPICallOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  data?: any;
  headers?: Record<string, string>;
  service: string;
  maxRetries?: number;
}

export class APIIntegrationService {
  /**
   * Make a secure external API call with logging and retry mechanism
   * @param options API call configuration
   * @returns Promise with API response
   */
  static async callExternalAPI<T>({
    method, 
    url, 
    data, 
    headers = {}, 
    service,
    maxRetries = 3
  }: ExternalAPICallOptions): Promise<T> {
    const requestId = crypto.randomUUID();
    
    // Retrieve API key for the service
    const apiKey = await this.getServiceAPIKey(service);
    
    // Prepare request configuration
    const config: AxiosRequestConfig = {
      method,
      url,
      data,
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`,
        'X-Request-ID': requestId,
        'Content-Type': 'application/json'
      }
    };

    let retries = 0;
    while (retries < maxRetries) {
      try {
        // Log the outgoing request
        await this.logIntegrationRequest(service, requestId, config);

        // Make the API call
        const response = await axios(config);

        // Log the successful response
        await this.logIntegrationResponse(service, requestId, response);

        return response.data;
      } catch (error) {
        retries++;
        
        // Log the error
        await this.logIntegrationError(service, requestId, error);

        // If it's the last retry, throw the error
        if (retries >= maxRetries) {
          throw error;
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries)));
      }
    }

    throw new Error(`API call to ${service} failed after ${maxRetries} attempts`);
  }

  /**
   * Retrieve API key for a specific service
   * @param service Name of the service
   * @returns API key
   */
  private static async getServiceAPIKey(service: string): Promise<string> {
    const apiKeyRecord = await prisma.apiKey.findFirst({
      where: { keyName: service }
    });

    if (!apiKeyRecord) {
      throw new Error(`No API key found for service: ${service}`);
    }

    return apiKeyRecord.keyValue;
  }

  /**
   * Log external API request
   * @param service Service name
   * @param requestId Unique request identifier
   * @param config Request configuration
   */
  private static async logIntegrationRequest(
    service: string, 
    requestId: string, 
    config: AxiosRequestConfig
  ) {
    await prisma.integrationLog.create({
      data: {
        service,
        request_body: JSON.stringify(config.data || {}),
        response_body: JSON.stringify(config.headers || {}),
        status_code: 200 // Default since we don't have the actual response yet
      }
    });
  }

  /**
   * Log external API response
   * @param service Service name
   * @param requestId Unique request identifier
   * @param response API response
   */
  private static async logIntegrationResponse(
    service: string, 
    requestId: string, 
    response: AxiosResponse
  ) {
    await prisma.integrationLog.update({
      where: { id: requestId },
      data: {
        response_body: JSON.stringify(response.data),
        status_code: response.status
      }
    });
  }

  /**
   * Log API call errors
   * @param service Service name
   * @param requestId Unique request identifier
   * @param error Error object
   */
  private static async logIntegrationError(
    service: string, 
    requestId: string, 
    error: any
  ) {
    await prisma.integrationLog.update({
      where: { id: requestId },
      data: {
        error_message: error.message,
        response_body: JSON.stringify(error),
        status_code: error.response?.status || 500
      }
    });
  }
}
