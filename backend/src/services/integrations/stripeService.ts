import Stripe from 'stripe';
import { APIIntegrationService } from './apiIntegrationService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class StripeService {
  private static stripe: Stripe;

  /**
   * Initialize Stripe client
   */
  private static initStripe() {
    if (!this.stripe) {
      const stripeKey = process.env.STRIPE_SECRET_KEY;
      if (!stripeKey) {
        throw new Error('Stripe secret key is not configured');
      }
      this.stripe = new Stripe(stripeKey, {
        apiVersion: '2025-06-30.basil'
      });
    }
    return this.stripe;
  }

  /**
   * Create a payment intent for a booking
   * @param amount Total amount in cents
   * @param currency Currency code (e.g., 'usd')
   * @returns Payment intent details
   */
  static async createPaymentIntent(amount: number, currency: string = 'usd') {
    const stripe = this.initStripe();

    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency,
        payment_method_types: ['card']
      });

      // Log payment intent creation
      await prisma.integrationLog.create({
        data: {
          service: 'stripe',
          request_body: JSON.stringify({ amount, currency }),
          response_body: JSON.stringify(paymentIntent),
          status_code: 200
        }
      });

      return paymentIntent;
    } catch (error) {
      // Log error
      await prisma.integrationLog.create({
        data: {
          service: 'stripe',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          status_code: 500
        }
      });

      throw error;
    }
  }

  /**
   * Handle Stripe webhook events
   * @param payload Webhook payload
   * @param signature Stripe signature header
   * @returns Processed event
   */
  static async handleWebhook(payload: Buffer, signature: string) {
    const stripe = this.initStripe();
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!webhookSecret) {
      throw new Error('Stripe webhook secret is not configured');
    }

    try {
      // Verify webhook signature
      const event = stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret
      );

      // Log webhook event
      await prisma.webhookLog.create({
        data: {
          service: 'stripe',
          event_type: event.type,
          payload: JSON.stringify(event.data.object),
          processed: true
        }
      });

      // Handle different event types
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSuccess(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailure(event.data.object);
          break;
        // Add more event handlers as needed
      }

      return event;
    } catch (error) {
      // Log webhook error
      await prisma.webhookLog.create({
        data: {
          service: 'stripe',
          event_type: 'error',
          payload: JSON.stringify(error),
          processed: false
        }
      });

      throw error;
    }
  }

  /**
   * Handle successful payment
   * @param paymentIntent Stripe payment intent
   */
  private static async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    // Update booking status, trigger notifications, etc.
    // Implement your specific business logic here
    console.log('Payment succeeded:', paymentIntent.id);
  }

  /**
   * Handle payment failure
   * @param paymentIntent Stripe payment intent
   */
  private static async handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
    // Handle payment failure, notify user, etc.
    console.log('Payment failed:', paymentIntent.id);
  }

  /**
   * Refund a payment
   * @param paymentIntentId Stripe payment intent ID
   * @param amount Amount to refund in cents
   * @returns Refund details
   */
  static async refundPayment(paymentIntentId: string, amount?: number) {
    const stripe = this.initStripe();

    try {
      const refund = await stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount
      });

      // Log refund
      await prisma.integrationLog.create({
        data: {
          service: 'stripe',
          request_body: JSON.stringify({ paymentIntentId, amount }),
          response_body: JSON.stringify(refund),
          status_code: 200
        }
      });

      return refund;
    } catch (error) {
      // Log refund error
      await prisma.integrationLog.create({
        data: {
          service: 'stripe',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          status_code: 500
        }
      });

      throw error;
    }
  }
}
