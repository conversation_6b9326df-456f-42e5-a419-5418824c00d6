import { Booking } from '../types/database';
import * as nodemailer from 'nodemailer';
import { logger } from '../utils/logger';

export enum NotificationType {
  BOOKING_CONFIRMATION = 'booking_confirmation',
  BOOKING_CANCELLATION = 'booking_cancellation',
  BOOKING_REMINDER = 'booking_reminder',
  REFUND_PROCESSED = 'refund_processed',
  REFUND_FAILED = 'refund_failed'
}

export interface NotificationRecipient {
  id: string;
  email: string;
  name: string;
  role: 'customer' | 'provider' | 'admin';
  preferredLanguage?: string;
  communicationPreferences?: {
    email: boolean;
    sms: boolean;
    pushNotification: boolean;
  };
}

export class NotificationService {
  private transporter: nodemailer.Transporter | null = null;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      // Only initialize if email config is available
      if (process.env.SMTP_HOST && process.env.SMTP_USER) {
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: false,
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        });
        logger.info('Email transporter initialized successfully');
      } else {
        logger.warn('Email configuration not found - notifications will be logged only');
      }
    } catch (error) {
      logger.error('Failed to initialize email transporter', error);
    }
  }

  /**
   * Send booking confirmation notification
   */
  async sendBookingConfirmationNotification(booking: Booking): Promise<void> {
    try {
      logger.info('Booking confirmation notification', {
        bookingId: booking.id,
        userId: booking.userId
      });
    } catch (error) {
      logger.error('Failed to send booking confirmation', {
        bookingId: booking.id,
        userId: booking.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send refund failed notification
   */
  async sendRefundFailedNotification(booking: Booking, reason: string): Promise<void> {
    try {
      logger.info('Refund failed notification', {
        bookingId: booking.id,
        userId: booking.userId,
        reason
      });
    } catch (error) {
      logger.error('Failed to send refund failed notification', {
        bookingId: booking.id,
        userId: booking.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfig(): Promise<boolean> {
    try {
      if (!this.transporter) {
        logger.info('No email transporter configured - test skipped');
        return false;
      }
      await this.transporter.verify();
      logger.info('Email configuration verified successfully');
      return true;
    } catch (error) {
      logger.error('Email configuration test failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}

export default NotificationService;
