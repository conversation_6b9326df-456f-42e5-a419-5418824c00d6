import Stripe from 'stripe';
import { Request, Response, NextFunction } from "express";
import { PrismaClient } from '@prisma/client';
import monitoringService from '../utils/monitoring';
import { logger } from '../utils/logger';

class StripeWebhookService {
  private stripe: Stripe;
  private prisma: PrismaClient;

  constructor(stripeSecretKey?: string) {
    this.stripe = new Stripe(stripeSecretKey || process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-06-30.basil'
    });
    this.prisma = new PrismaClient();
  }

  /**
   * Verify and handle Stripe webhook events
   * @param rawBody Raw request body
   * @param signature Stripe-Signature header
   */
  async handleWebhook(rawBody: Buffer, signature: string) {
    try {
      // Verify webhook signature
      const event = this.stripe.webhooks.constructEvent(
        rawBody,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      // Check if we've already processed this event (idempotency)
      const existingLog = await this.prisma.webhookLog.findFirst({
        where: {
          service: 'stripe',
          payload: {
            contains: event.id
          }
        }
      });

      if (existingLog && existingLog.processed) {
        logger.info('Webhook event already processed', { eventId: event.id });
        return { success: true, message: 'Event already processed' };
      }

      // Log webhook event
      const webhookLog = await this.prisma.webhookLog.create({
        data: {
          service: 'stripe',
          event_type: event.type,
          payload: JSON.stringify({
            id: event.id,
            type: event.type,
            data: event.data.object
          }),
          processed: false
        }
      });

      // Handle different event types
      let result;
      switch (event.type) {
        case 'payment_intent.succeeded':
          result = await this.handlePaymentSuccess(event.data.object as Stripe.PaymentIntent);
          break;

        case 'payment_intent.payment_failed':
          result = await this.handlePaymentFailure(event.data.object as Stripe.PaymentIntent);
          break;

        case 'charge.refunded':
          result = await this.handleRefund(event.data.object as Stripe.Charge);
          break;

        default:
          logger.info('Unhandled webhook event type', { eventType: event.type });
          monitoringService.trackEvent('unhandled-stripe-event', {
            eventType: event.type
          });
          result = { success: true, message: 'Event type not handled' };
      }

      // Mark event as processed
      await this.prisma.webhookLog.update({
        where: {
          id: webhookLog.id
        },
        data: {
          processed: true
        }
      });

      return result;
    } catch (error) {
      logger.error('Stripe webhook error:', error);
      monitoringService.trackError('stripe-webhook-verification-error', {
        context: 'stripe-webhook-verification',
        severity: 'critical',
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  /**
   * Handle successful payment
   */
  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    try {
      const bookingId = paymentIntent.metadata.bookingId;
      const userId = paymentIntent.metadata.userId;
      const providerId = paymentIntent.metadata.providerId;

      if (!bookingId) {
        throw new Error('Booking ID not found in payment intent metadata');
      }

      // Use transaction to ensure atomicity and handle idempotency
      const result = await this.prisma.$transaction(async (tx) => {
        // Check current booking status
        const booking = await tx.booking.findUnique({
          where: { id: bookingId },
          include: {
            vehicle: {
              // provider relation not available in Vehicle model
            }
          }
        });

        if (!booking) {
          throw new Error(`Booking ${bookingId} not found`);
        }

        // If already confirmed, this is a duplicate webhook - return success
        if (booking.paymentStatus === 'COMPLETED' && booking.status === 'CONFIRMED') {
          logger.info('Payment already processed for booking', { bookingId });
          return { booking, payment: null, duplicate: true };
        }

        // Update booking status
        const updatedBooking = await tx.booking.update({
          where: { id: bookingId },
          data: {
            status: 'CONFIRMED',
            paymentStatus: 'COMPLETED',
            updatedAt: new Date()
          }
        });

        // Create or update payment record
        const payment = await tx.payment.upsert({
          where: {
            bookingId: bookingId
          },
          update: {
            status: 'COMPLETED',
            // stripePaymentIntentId field not available in Payment model
            amount: paymentIntent.amount,
            // currency field doesn't exist in Payment model
            // updatedAt field doesn't exist in Payment model
          },
          create: {
            bookingId,
            // userId field not available in Payment model
            providerId: providerId || booking.providerId,
            // stripePaymentIntentId field doesn't exist in Payment model
            amount: paymentIntent.amount,
            // currency field doesn't exist in Payment model
            paymentMethod: 'STRIPE',
            status: 'COMPLETED'
          }
        });

        return { booking: updatedBooking, payment, duplicate: false };
      });

      if (!result.duplicate) {
        logger.info('Payment processed successfully via webhook', {
          bookingId,
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount
        });

        monitoringService.trackEvent('payment-succeeded', {
          bookingId,
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount
        });
      }

      return { success: true, booking: result.booking };

    } catch (error) {
      logger.error('Payment success handler error:', error);
      monitoringService.trackError('stripe-payment-success-handler-error', {
        context: 'stripe-payment-success-handler',
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentIntentId: paymentIntent.id
      });
      throw error;
    }
  }

  /**
   * Handle payment failure
   */
  private async handlePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
    try {
      const bookingId = paymentIntent.metadata.bookingId;
      
      // Update booking status
      await this.prisma.booking.update({
        where: { id: bookingId },
        data: { 
          paymentStatus: 'FAILED',
          status: 'CANCELLED'
        }
      });

      // Create system alert
      await this.prisma.systemAlert.create({
        data: {
          type: 'PAYMENT_FAILED',
          severity: 'WARNING',
          message: `Payment failed for booking ${bookingId}`,
          details: JSON.stringify({
            paymentIntentId: paymentIntent.id,
            failureReason: paymentIntent.last_payment_error?.message
          })
        }
      });

      // Notify user (could integrate with email service)
      monitoringService.trackEvent('payment-failed', {
        bookingId,
        failureReason: paymentIntent.last_payment_error?.message
      });
    } catch (error) {
      monitoringService.trackError('stripe-payment-failure-handler-error', {
        context: 'stripe-payment-failure-handler',
        paymentIntentId: paymentIntent.id,
        error: error.message
      });
    }
  }

  /**
   * Handle refund events
   */
  private async handleRefund(charge: Stripe.Charge) {
    try {
      const bookingId = charge.metadata.bookingId;
      
      // Update booking and payment status
      await this.prisma.booking.update({
        where: { id: bookingId },
        data: { 
          paymentStatus: 'REFUNDED',
          status: 'CANCELLED'
        }
      });

      // Create system alert
      await this.prisma.systemAlert.create({
        data: {
          type: 'REFUND_PROCESSED',
          severity: 'INFO',
          message: `Refund processed for booking ${bookingId}`,
          details: JSON.stringify({
            chargeId: charge.id,
            refundAmount: charge.amount_refunded
          })
        }
      });

      monitoringService.trackEvent('refund-processed', {
        bookingId,
        refundAmount: charge.amount_refunded
      });
    } catch (error) {
      monitoringService.trackError('stripe-refund-handler-error', {
        context: 'stripe-refund-handler',
        chargeId: charge.id,
        error: error.message
      });
    }
  }

  /**
   * Webhook route handler
   */
  async webhookHandler(req: Request, res: Response) {
    const sig = req.headers['stripe-signature'] as string;

    try {
      // Raw body is required for signature verification
      await this.handleWebhook(req.body, sig);
      res.json({ received: true });
    } catch (error) {
      // Send 400 for signature verification failures
      res.status(400).send(`Webhook Error: ${error.message}`);
    }
  }
}

export default new StripeWebhookService(); 