const BaseSupabaseService = require('./BaseSupabaseService');
const { supabase } = require('../utils/supabaseClient');

class VehicleService extends BaseSupabaseService {
  constructor() {
    super('vehicles');
  }

  // Advanced vehicle search with availability check
  async searchAvailableVehicles(searchParams) {
    try {
      const { 
        start_date, 
        end_date, 
        location, 
        vehicle_type, 
        min_price, 
        max_price 
      } = searchParams;

      // Base query for vehicles
      let query = this.client
        .from('vehicles')
        .select('*');

      // Apply filters
      if (location) query = query.eq('location', location);
      if (vehicle_type) query = query.eq('vehicle_type', vehicle_type);
      if (min_price) query = query.gte('price_per_day', min_price);
      if (max_price) query = query.lte('price_per_day', max_price);

      // If dates are provided, check availability
      if (start_date && end_date) {
        // Subquery to find vehicles not booked in the given date range
        query = query.not('id', 'in', 
          `(select vehicle_id from bookings 
            where (start_date <= '${end_date}' and end_date >= '${start_date}'))`
        );
      }

      const { data, error } = await query;

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Vehicle search error:', error);
      throw error;
    }
  }

  // Add a new vehicle with validation
  async addVehicle(vehicleData) {
    try {
      // Validate required fields
      const requiredFields = [
        'provider_id', 
        'vehicle_type', 
        'make', 
        'model', 
        'year', 
        'price_per_day'
      ];

      requiredFields.forEach(field => {
        if (!vehicleData[field]) {
          throw new Error(`${field} is required`);
        }
      });

      // Insert vehicle
      const { data, error } = await this.client
        .from('vehicles')
        .insert(vehicleData)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Add vehicle error:', error);
      throw error;
    }
  }

  // Get vehicles by provider
  async getVehiclesByProvider(providerId) {
    try {
      const { data, error } = await this.client
        .from('vehicles')
        .select('*')
        .eq('provider_id', providerId);

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Get provider vehicles error:', error);
      throw error;
    }
  }
}

module.exports = VehicleService;
