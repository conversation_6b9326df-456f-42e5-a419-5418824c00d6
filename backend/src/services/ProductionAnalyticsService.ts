// =============================================================================
// PRODUCTION ANALYTICS SERVICE
// =============================================================================
// Comprehensive analytics and reporting for RentaHub

import DatabaseService from './DatabaseService';
import { supabase } from '../server';

interface AnalyticsData {
  totalRevenue: number;
  totalExpenses: number;
  netCashflow: number;
  grossMargin: number;
  totalBookings: number;
  totalVehicles: number;
  activeVehicles: number;
  occupancyRate: number;
  averageBookingValue: number;
  conversionRate: number;
  customerAcquisitionCost: number;
  customerLifetimeValue: number;
}

interface CashflowData {
  date: string;
  revenue: number;
  expenses: number;
  netCashflow: number;
  bookings: number;
}

class ProductionAnalyticsService {
  constructor() {
    console.log('📊 ProductionAnalyticsService initialized');
  }

  // =============================================================================
  // DASHBOARD ANALYTICS
  // =============================================================================

  async getDashboardAnalytics(providerId?: string, startDate?: string, endDate?: string): Promise<AnalyticsData> {
    try {
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 days ago
      const end = endDate || new Date().toISOString();

      // Get bookings data
      const bookingsResult = await DatabaseService.getBookings({
        providerId,
        page: 1,
        limit: 1000 // Get all bookings for analytics
      });

      const bookings = bookingsResult.data || [];
      
      // Get vehicles data
      const vehiclesResult = await DatabaseService.getVehicles({
        page: 1,
        limit: 1000
      });

      const vehicles = vehiclesResult.data || [];

      // Calculate metrics
      const totalRevenue = bookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
      const totalBookings = bookings.length;
      const totalVehicles = vehicles.length;
      const activeVehicles = vehicles.filter(v => v.status === 'available').length;
      
      // Estimated calculations (in production, these would be more sophisticated)
      const totalExpenses = totalRevenue * 0.3; // 30% estimated expenses
      const netCashflow = totalRevenue - totalExpenses;
      const grossMargin = totalRevenue > 0 ? (netCashflow / totalRevenue) : 0;
      const occupancyRate = totalVehicles > 0 ? (bookings.filter(b => b.status === 'active').length / totalVehicles) : 0;
      const averageBookingValue = totalBookings > 0 ? (totalRevenue / totalBookings) : 0;
      
      // Mock additional metrics (would be calculated from real data)
      const conversionRate = 0.15; // 15% conversion rate
      const customerAcquisitionCost = 50000; // IDR 50,000
      const customerLifetimeValue = 500000; // IDR 500,000

      return {
        totalRevenue,
        totalExpenses,
        netCashflow,
        grossMargin,
        totalBookings,
        totalVehicles,
        activeVehicles,
        occupancyRate,
        averageBookingValue,
        conversionRate,
        customerAcquisitionCost,
        customerLifetimeValue
      };
    } catch (error) {
      console.error('❌ Dashboard analytics error:', error);
      
      // Return empty analytics on error
      return {
        totalRevenue: 0,
        totalExpenses: 0,
        netCashflow: 0,
        grossMargin: 0,
        totalBookings: 0,
        totalVehicles: 0,
        activeVehicles: 0,
        occupancyRate: 0,
        averageBookingValue: 0,
        conversionRate: 0,
        customerAcquisitionCost: 0,
        customerLifetimeValue: 0
      };
    }
  }

  // =============================================================================
  // CASHFLOW ANALYTICS
  // =============================================================================

  async getCashflowData(providerId?: string, days: number = 30): Promise<CashflowData[]> {
    try {
      const cashflowData: CashflowData[] = [];
      const today = new Date();

      // Generate daily cashflow data for the specified period
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0];

        // Get bookings for this date
        const bookingsResult = await DatabaseService.getBookings({
          providerId,
          page: 1,
          limit: 100
        });

        const dayBookings = (bookingsResult.data || []).filter(booking => 
          booking.created_at.startsWith(dateString)
        );

        const revenue = dayBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
        const expenses = revenue * 0.3; // 30% estimated expenses
        const netCashflow = revenue - expenses;

        cashflowData.push({
          date: dateString,
          revenue,
          expenses,
          netCashflow,
          bookings: dayBookings.length
        });
      }

      return cashflowData;
    } catch (error) {
      console.error('❌ Cashflow data error:', error);
      return [];
    }
  }

  // =============================================================================
  // VEHICLE PERFORMANCE ANALYTICS
  // =============================================================================

  async getVehiclePerformance(providerId?: string): Promise<any[]> {
    try {
      const vehiclesResult = await DatabaseService.getVehicles({
        page: 1,
        limit: 100
      });

      const vehicles = vehiclesResult.data || [];

      // Get performance data for each vehicle
      const performanceData = await Promise.all(
        vehicles.map(async (vehicle) => {
          const bookingsResult = await DatabaseService.getBookings({
            providerId,
            page: 1,
            limit: 100
          });

          const vehicleBookings = (bookingsResult.data || []).filter(
            booking => booking.vehicle_id === vehicle.id
          );

          const totalRevenue = vehicleBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
          const totalBookings = vehicleBookings.length;
          const averageRating = vehicle.rating || 0;
          const utilizationRate = totalBookings > 0 ? Math.min(totalBookings / 30, 1) : 0; // Simplified calculation

          return {
            vehicleId: vehicle.id,
            name: `${vehicle.make} ${vehicle.model}`,
            category: vehicle.category,
            totalRevenue,
            totalBookings,
            averageRating,
            utilizationRate,
            dailyRate: vehicle.daily_rate
          };
        })
      );

      return performanceData.sort((a, b) => b.totalRevenue - a.totalRevenue);
    } catch (error) {
      console.error('❌ Vehicle performance error:', error);
      return [];
    }
  }

  // =============================================================================
  // CUSTOMER ANALYTICS
  // =============================================================================

  async getCustomerAnalytics(providerId?: string): Promise<any> {
    try {
      // Get user data from Supabase
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'CUSTOMER');

      if (error) {
        console.error('❌ Error fetching users:', error);
        return {
          totalCustomers: 0,
          newCustomers: 0,
          returningCustomers: 0,
          customerRetentionRate: 0,
          averageCustomerValue: 0
        };
      }

      const totalCustomers = users?.length || 0;
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const newCustomers = users?.filter(user => 
        new Date(user.created_at) > thirtyDaysAgo
      ).length || 0;

      // Get booking data to calculate customer metrics
      const bookingsResult = await DatabaseService.getBookings({
        providerId,
        page: 1,
        limit: 1000
      });

      const bookings = bookingsResult.data || [];
      const customerBookingCounts = new Map<string, number>();
      let totalRevenue = 0;

      bookings.forEach(booking => {
        const customerId = booking.customer_id;
        customerBookingCounts.set(customerId, (customerBookingCounts.get(customerId) || 0) + 1);
        totalRevenue += booking.total_amount || 0;
      });

      const returningCustomers = Array.from(customerBookingCounts.values()).filter(count => count > 1).length;
      const customerRetentionRate = totalCustomers > 0 ? (returningCustomers / totalCustomers) : 0;
      const averageCustomerValue = totalCustomers > 0 ? (totalRevenue / totalCustomers) : 0;

      return {
        totalCustomers,
        newCustomers,
        returningCustomers,
        customerRetentionRate,
        averageCustomerValue
      };
    } catch (error) {
      console.error('❌ Customer analytics error:', error);
      return {
        totalCustomers: 0,
        newCustomers: 0,
        returningCustomers: 0,
        customerRetentionRate: 0,
        averageCustomerValue: 0
      };
    }
  }

  // =============================================================================
  // BOOKING ANALYTICS
  // =============================================================================

  async getBookingAnalytics(providerId?: string): Promise<any> {
    try {
      const bookingsResult = await DatabaseService.getBookings({
        providerId,
        page: 1,
        limit: 1000
      });

      const bookings = bookingsResult.data || [];

      // Calculate booking status distribution
      const statusCounts = bookings.reduce((acc, booking) => {
        acc[booking.status] = (acc[booking.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Calculate booking trends
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const recentBookings = bookings.filter(booking => 
        new Date(booking.created_at) > thirtyDaysAgo
      );

      const totalBookings = bookings.length;
      const completedBookings = statusCounts.completed || 0;
      const cancelledBookings = statusCounts.cancelled || 0;
      const completionRate = totalBookings > 0 ? (completedBookings / totalBookings) : 0;
      const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) : 0;

      return {
        totalBookings,
        recentBookings: recentBookings.length,
        completedBookings,
        cancelledBookings,
        completionRate,
        cancellationRate,
        statusDistribution: statusCounts
      };
    } catch (error) {
      console.error('❌ Booking analytics error:', error);
      return {
        totalBookings: 0,
        recentBookings: 0,
        completedBookings: 0,
        cancelledBookings: 0,
        completionRate: 0,
        cancellationRate: 0,
        statusDistribution: {}
      };
    }
  }

  // =============================================================================
  // COMPREHENSIVE REPORT
  // =============================================================================

  async getComprehensiveReport(providerId?: string): Promise<any> {
    try {
      const [
        dashboardAnalytics,
        cashflowData,
        vehiclePerformance,
        customerAnalytics,
        bookingAnalytics
      ] = await Promise.all([
        this.getDashboardAnalytics(providerId),
        this.getCashflowData(providerId, 30),
        this.getVehiclePerformance(providerId),
        this.getCustomerAnalytics(providerId),
        this.getBookingAnalytics(providerId)
      ]);

      return {
        dashboard: dashboardAnalytics,
        cashflow: cashflowData,
        vehicles: vehiclePerformance,
        customers: customerAnalytics,
        bookings: bookingAnalytics,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Comprehensive report error:', error);
      return {
        error: 'Failed to generate comprehensive report'
      };
    }
  }
}

// Export singleton instance
export default new ProductionAnalyticsService();
