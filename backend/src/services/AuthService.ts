import { PrismaClient, User, UserRole } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export interface AuthServiceInterface {
  login(email: string, password: string): Promise<{ success: boolean; token?: string; user?: User; message?: string }>;
  register(userData: any): Promise<{ success: boolean; user?: User; message?: string }>;
  verifyToken(token: string): Promise<{ success: boolean; user?: User; message?: string }>;
  refreshToken(token: string): Promise<{ success: boolean; token?: string; message?: string }>;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
  phoneNumber?: string;
  role?: UserRole;
}

class AuthService implements AuthServiceInterface {
  private jwtSecret: string;
  private jwtExpiration: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'default-secret';
    this.jwtExpiration = process.env.JWT_EXPIRATION || '24h';
  }

  /**
   * User login
   */
  async login(email: string, password: string): Promise<{ success: boolean; token?: string; user?: User; message?: string }> {
    try {
      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        return { success: false, message: 'Invalid credentials' };
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return { success: false, message: 'Invalid credentials' };
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id, email: user.email, role: user.role } as any,
        this.jwtSecret,
        { expiresIn: this.jwtExpiration } as any
      );

      return {
        success: true,
        token,
        user: { ...user, password: undefined } as any, // Remove password from response
        message: 'Login successful'
      };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Internal server error' };
    }
  }

  /**
   * User registration
   */
  async register(userData: RegisterData): Promise<{ success: boolean; user?: User; message?: string }> {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUser) {
        return { success: false, message: 'User already exists' };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Create user
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          password: hashedPassword,
          name: userData.name,
          phoneNumber: userData.phoneNumber,
          role: userData.role || UserRole.CUSTOMER,
          emailVerified: false,
          phoneVerified: false
        }
      });

      return {
        success: true,
        user: { ...user, password: undefined } as any, // Remove password from response
        message: 'Registration successful'
      };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, message: 'Internal server error' };
    }
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token: string): Promise<{ success: boolean; user?: User; message?: string }> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user) {
        return { success: false, message: 'User not found' };
      }

      return {
        success: true,
        user: { ...user, password: undefined } as any, // Remove password from response
        message: 'Token valid'
      };
    } catch (error) {
      console.error('Token verification error:', error);
      return { success: false, message: 'Invalid token' };
    }
  }

  /**
   * Refresh JWT token
   */
  async refreshToken(token: string): Promise<{ success: boolean; token?: string; message?: string }> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any;
      
      // Generate new token
      const newToken = jwt.sign(
        { userId: decoded.userId, email: decoded.email, role: decoded.role } as any,
        this.jwtSecret,
        { expiresIn: this.jwtExpiration } as any
      );

      return {
        success: true,
        token: newToken,
        message: 'Token refreshed'
      };
    } catch (error) {
      console.error('Token refresh error:', error);
      return { success: false, message: 'Invalid token' };
    }
  }
}

export default AuthService;
