import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface BookingWithDetails {
  id: string;
  userId: string;
  providerId: string;
  vehicleId: string;
  totalPrice: number;
  status: string;
  startDate: Date;
  endDate: Date;
  paymentMethod: string;
  paymentStatus: string;
  createdAt: Date;
  updatedAt: Date;
  customer: {
    id: string;
    name: string;
    email: string;
    phoneNumber?: string;
  };
  vehicle: {
    id: string;
    brand?: string;
    model?: string;
    vehicleType: string;
    category?: string;
  };
}

export interface BookingStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  monthlyRevenue: number;
}

export class BookingManagementService {
  /**
   * Get all bookings for a provider
   */
  static async getProviderBookings(
    providerId: string,
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<{
    bookings: BookingWithDetails[];
    total: number;
    pages: number;
  }> {
    try {
      console.log('📋 Fetching bookings for provider:', providerId, { page, limit, status });
      
      const skip = (page - 1) * limit;
      const whereClause: any = { providerId };
      
      if (status) {
        whereClause.status = status;
      }
      
      const [bookings, total] = await Promise.all([
        prisma.booking.findMany({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phoneNumber: true
              }
            },
            vehicle: {
              select: {
                id: true,
                brand: true,
                model: true,
                vehicleType: true,
                category: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip,
          take: limit
        }),
        prisma.booking.count({
          where: whereClause
        })
      ]);

      const bookingsWithDetails: BookingWithDetails[] = bookings.map(booking => ({
        id: booking.id,
        userId: booking.userId,
        providerId: booking.providerId,
        vehicleId: booking.vehicleId,
        totalPrice: Number(booking.totalPrice),
        status: booking.status,
        startDate: booking.startDate,
        endDate: booking.endDate,
        paymentMethod: booking.paymentMethod,
        paymentStatus: booking.paymentStatus,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
        customer: {
          id: booking.user.id,
          name: booking.user.name,
          email: booking.user.email,
          phoneNumber: booking.user.phoneNumber || undefined
        },
        vehicle: {
          id: booking.vehicle.id,
          brand: booking.vehicle.brand || undefined,
          model: booking.vehicle.model || undefined,
          vehicleType: booking.vehicle.vehicleType,
          category: booking.vehicle.category || undefined
        }
      }));

      console.log('✅ Found bookings:', bookingsWithDetails.length);

      return {
        bookings: bookingsWithDetails,
        total,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error fetching provider bookings:', error);
      throw new Error('Failed to fetch bookings');
    }
  }

  /**
   * Get booking by ID (for provider)
   */
  static async getBookingById(bookingId: string, providerId: string): Promise<BookingWithDetails | null> {
    try {
      const booking = await prisma.booking.findFirst({
        where: { 
          id: bookingId,
          providerId 
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true
            }
          },
          vehicle: {
            select: {
              id: true,
              brand: true,
              model: true,
              vehicleType: true,
              category: true
            }
          }
        }
      });

      if (!booking) {
        return null;
      }

      return {
        id: booking.id,
        userId: booking.userId,
        providerId: booking.providerId,
        vehicleId: booking.vehicleId,
        totalPrice: Number(booking.totalPrice),
        status: booking.status,
        startDate: booking.startDate,
        endDate: booking.endDate,
        paymentMethod: booking.paymentMethod,
        paymentStatus: booking.paymentStatus,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
        customer: {
          id: booking.user.id,
          name: booking.user.name,
          email: booking.user.email,
          phoneNumber: booking.user.phoneNumber || undefined
        },
        vehicle: {
          id: booking.vehicle.id,
          brand: booking.vehicle.brand || undefined,
          model: booking.vehicle.model || undefined,
          vehicleType: booking.vehicle.vehicleType,
          category: booking.vehicle.category || undefined
        }
      };
    } catch (error) {
      logger.error('Error fetching booking by ID:', error);
      throw new Error('Failed to fetch booking');
    }
  }

  /**
   * Update booking status
   */
  static async updateBookingStatus(
    bookingId: string, 
    providerId: string, 
    status: string
  ): Promise<BookingWithDetails> {
    try {
      // Verify booking belongs to provider
      const existingBooking = await prisma.booking.findFirst({
        where: { 
          id: bookingId,
          providerId 
        }
      });

      if (!existingBooking) {
        throw new Error('Booking not found or access denied');
      }

      // Update booking status
      const updatedBooking = await prisma.booking.update({
        where: { id: bookingId },
        data: { 
          status: status as any,
          updatedAt: new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true
            }
          },
          vehicle: {
            select: {
              id: true,
              brand: true,
              model: true,
              vehicleType: true,
              category: true
            }
          }
        }
      });

      logger.info(`Booking ${bookingId} status updated to ${status} by provider ${providerId}`);

      return {
        id: updatedBooking.id,
        userId: updatedBooking.userId,
        providerId: updatedBooking.providerId,
        vehicleId: updatedBooking.vehicleId,
        totalPrice: Number(updatedBooking.totalPrice),
        status: updatedBooking.status,
        startDate: updatedBooking.startDate,
        endDate: updatedBooking.endDate,
        paymentMethod: updatedBooking.paymentMethod,
        paymentStatus: updatedBooking.paymentStatus,
        createdAt: updatedBooking.createdAt,
        updatedAt: updatedBooking.updatedAt,
        customer: {
          id: updatedBooking.user.id,
          name: updatedBooking.user.name,
          email: updatedBooking.user.email,
          phoneNumber: updatedBooking.user.phoneNumber || undefined
        },
        vehicle: {
          id: updatedBooking.vehicle.id,
          brand: updatedBooking.vehicle.brand || undefined,
          model: updatedBooking.vehicle.model || undefined,
          vehicleType: updatedBooking.vehicle.vehicleType,
          category: updatedBooking.vehicle.category || undefined
        }
      };
    } catch (error) {
      logger.error('Error updating booking status:', error);
      throw new Error('Failed to update booking status');
    }
  }

  /**
   * Get booking statistics for provider
   */
  static async getBookingStats(providerId: string): Promise<BookingStats> {
    try {
      const [
        totalBookings,
        pendingBookings,
        confirmedBookings,
        completedBookings,
        cancelledBookings,
        totalRevenue,
        monthlyRevenue
      ] = await Promise.all([
        prisma.booking.count({ where: { providerId } }),
        prisma.booking.count({ where: { providerId, status: 'PENDING' } }),
        prisma.booking.count({ where: { providerId, status: 'CONFIRMED' } }),
        prisma.booking.count({ where: { providerId, status: 'COMPLETED' } }),
        prisma.booking.count({ where: { providerId, status: 'CANCELLED' } }),
        prisma.booking.aggregate({
          where: { providerId, status: 'COMPLETED' },
          _sum: { totalPrice: true }
        }),
        prisma.booking.aggregate({
          where: {
            providerId,
            status: 'COMPLETED',
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: { totalPrice: true }
        })
      ]);

      return {
        totalBookings,
        pendingBookings,
        confirmedBookings,
        completedBookings,
        cancelledBookings,
        totalRevenue: Number(totalRevenue._sum.totalPrice) || 0,
        monthlyRevenue: Number(monthlyRevenue._sum.totalPrice) || 0
      };
    } catch (error) {
      logger.error('Error fetching booking stats:', error);
      throw new Error('Failed to fetch booking statistics');
    }
  }
}

export default BookingManagementService;
