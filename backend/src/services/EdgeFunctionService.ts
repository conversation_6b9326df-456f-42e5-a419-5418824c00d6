import axios from 'axios';
import { logger } from '../utils/logger';

export class EdgeFunctionService {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.SUPABASE_URL || '';
    this.apiKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  }

  /**
   * Test database connection through Edge Function
   */
  async testConnection(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/functions/v1/database-proxy/health`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      logger.info('Edge Function health check successful', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      logger.error('Edge Function health check failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check database tables through Edge Function
   */
  async checkTables(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/functions/v1/database-proxy/tables`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 15000
        }
      );

      logger.info('Edge Function tables check successful', response.data);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      logger.error('Edge Function tables check failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get users through Edge Function
   */
  async getUsers(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/functions/v1/database-proxy/users`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      logger.error('Edge Function get users failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create user through Edge Function
   */
  async createUser(userData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/functions/v1/database-proxy/users`,
        userData,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      logger.error('Edge Function create user failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Fallback method - try Edge Function if direct Prisma fails
   */
  async executeWithFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<{ success: boolean; data?: T; error?: string }>
  ): Promise<T> {
    try {
      // Try primary operation (direct Prisma)
      return await primaryOperation();
    } catch (primaryError) {
      logger.warn('Primary operation failed, trying Edge Function fallback:', primaryError);
      
      try {
        // Try fallback operation (Edge Function)
        const fallbackResult = await fallbackOperation();
        
        if (fallbackResult.success && fallbackResult.data) {
          logger.info('Edge Function fallback successful');
          return fallbackResult.data;
        } else {
          throw new Error(fallbackResult.error || 'Edge Function fallback failed');
        }
      } catch (fallbackError) {
        logger.error('Both primary and fallback operations failed:', {
          primaryError: primaryError instanceof Error ? primaryError.message : primaryError,
          fallbackError: fallbackError instanceof Error ? fallbackError.message : fallbackError
        });
        
        // Throw the original error
        throw primaryError;
      }
    }
  }
}

export default EdgeFunctionService;
