import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface VehicleCatalogItem {
  id: string;
  make: string;
  model: string;
  year: string;
  engine?: string;
  type?: string;
  category?: string;
  image_url?: string;
  daily_rate?: number;
  rating?: number;
  reviews?: number;
  features?: string[];
  available_units?: number;
  has_insurance?: boolean;
  insurance_price?: number;
  location_city?: string;
}

export interface VehicleSearchFilters {
  make?: string;
  model?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  year?: string;
}

export class VehicleCatalogService {
  /**
   * Get all vehicles from catalog for autocomplete
   */
  static async getAllVehicles(): Promise<VehicleCatalogItem[]> {
    try {
      const vehicles = await prisma.$queryRaw<VehicleCatalogItem[]>`
        SELECT id, make, model, year, engine, type, category, 
               image_url, daily_rate, rating, reviews, features,
               available_units, has_insurance, insurance_price, location_city
        FROM vehicle_catalog 
        WHERE is_reference_only = true
        ORDER BY make, model
      `;

      logger.info(`Retrieved ${vehicles.length} vehicles from catalog`);
      return vehicles;
    } catch (error) {
      logger.error('Error fetching vehicles from catalog:', error);
      throw new Error('Failed to fetch vehicle catalog');
    }
  }

  /**
   * Search vehicles by make/model for autocomplete
   */
  static async searchVehicles(query: string): Promise<VehicleCatalogItem[]> {
    try {
      const searchTerm = `%${query.toLowerCase()}%`;
      
      const vehicles = await prisma.$queryRaw<VehicleCatalogItem[]>`
        SELECT id, make, model, year, engine, type, category, 
               image_url, daily_rate, rating, reviews, features,
               available_units, has_insurance, insurance_price, location_city
        FROM vehicle_catalog 
        WHERE is_reference_only = true
        AND (
          LOWER(make) LIKE ${searchTerm} OR 
          LOWER(model) LIKE ${searchTerm} OR
          LOWER(CONCAT(make, ' ', model)) LIKE ${searchTerm}
        )
        ORDER BY make, model
        LIMIT 20
      `;

      logger.info(`Found ${vehicles.length} vehicles matching query: ${query}`);
      return vehicles;
    } catch (error) {
      logger.error('Error searching vehicles:', error);
      throw new Error('Failed to search vehicle catalog');
    }
  }

  /**
   * Get vehicles by make for autocomplete
   */
  static async getVehiclesByMake(make: string): Promise<VehicleCatalogItem[]> {
    try {
      const vehicles = await prisma.$queryRaw<VehicleCatalogItem[]>`
        SELECT id, make, model, year, engine, type, category, 
               image_url, daily_rate, rating, reviews, features,
               available_units, has_insurance, insurance_price, location_city
        FROM vehicle_catalog 
        WHERE is_reference_only = true
        AND LOWER(make) = LOWER(${make})
        ORDER BY model
      `;

      logger.info(`Found ${vehicles.length} vehicles for make: ${make}`);
      return vehicles;
    } catch (error) {
      logger.error('Error fetching vehicles by make:', error);
      throw new Error('Failed to fetch vehicles by make');
    }
  }

  /**
   * Get unique makes for autocomplete dropdown
   */
  static async getUniqueMakes(): Promise<string[]> {
    try {
      const makes = await prisma.$queryRaw<{ make: string }[]>`
        SELECT DISTINCT make 
        FROM vehicle_catalog 
        WHERE is_reference_only = true
        ORDER BY make
      `;

      const makesList = makes.map(item => item.make);
      logger.info(`Retrieved ${makesList.length} unique makes`);
      return makesList;
    } catch (error) {
      logger.error('Error fetching unique makes:', error);
      throw new Error('Failed to fetch vehicle makes');
    }
  }

  /**
   * Get vehicle suggestions for autocomplete (make + model combinations)
   */
  static async getVehicleSuggestions(query: string): Promise<{ make: string; model: string; year: string; image_url?: string }[]> {
    try {
      const searchTerm = `%${query.toLowerCase()}%`;
      
      const suggestions = await prisma.$queryRaw<{ make: string; model: string; year: string; image_url?: string }[]>`
        SELECT DISTINCT make, model, year, image_url
        FROM vehicle_catalog 
        WHERE is_reference_only = true
        AND (
          LOWER(make) LIKE ${searchTerm} OR 
          LOWER(model) LIKE ${searchTerm} OR
          LOWER(CONCAT(make, ' ', model)) LIKE ${searchTerm}
        )
        ORDER BY make, model
        LIMIT 10
      `;

      logger.info(`Generated ${suggestions.length} suggestions for query: ${query}`);
      return suggestions;
    } catch (error) {
      logger.error('Error generating vehicle suggestions:', error);
      throw new Error('Failed to generate vehicle suggestions');
    }
  }

  /**
   * Get vehicle by ID
   */
  static async getVehicleById(id: string): Promise<VehicleCatalogItem | null> {
    try {
      const vehicle = await prisma.$queryRaw<VehicleCatalogItem[]>`
        SELECT id, make, model, year, engine, type, category, 
               image_url, daily_rate, rating, reviews, features,
               available_units, has_insurance, insurance_price, location_city
        FROM vehicle_catalog 
        WHERE id = ${id}
        AND is_reference_only = true
      `;

      return vehicle[0] || null;
    } catch (error) {
      logger.error('Error fetching vehicle by ID:', error);
      throw new Error('Failed to fetch vehicle');
    }
  }

  /**
   * Filter vehicles with advanced search
   */
  static async filterVehicles(filters: VehicleSearchFilters): Promise<VehicleCatalogItem[]> {
    try {
      const whereConditions = ['is_reference_only = true'];
      const params: any[] = [];

      if (filters.make) {
        whereConditions.push(`LOWER(make) = LOWER($${params.length + 1})`);
        params.push(filters.make);
      }

      if (filters.model) {
        whereConditions.push(`LOWER(model) LIKE LOWER($${params.length + 1})`);
        params.push(`%${filters.model}%`);
      }

      if (filters.category) {
        whereConditions.push(`category = $${params.length + 1}`);
        params.push(filters.category);
      }

      if (filters.minPrice) {
        whereConditions.push(`daily_rate >= $${params.length + 1}`);
        params.push(filters.minPrice);
      }

      if (filters.maxPrice) {
        whereConditions.push(`daily_rate <= $${params.length + 1}`);
        params.push(filters.maxPrice);
      }

      if (filters.year) {
        whereConditions.push(`year = $${params.length + 1}`);
        params.push(filters.year);
      }

      const whereClause = whereConditions.join(' AND ');
      
      const vehicles = await prisma.$queryRawUnsafe<VehicleCatalogItem[]>(`
        SELECT id, make, model, year, engine, type, category, 
               image_url, daily_rate, rating, reviews, features,
               available_units, has_insurance, insurance_price, location_city
        FROM vehicle_catalog 
        WHERE ${whereClause}
        ORDER BY make, model
        LIMIT 50
      `, ...params);

      logger.info(`Filtered ${vehicles.length} vehicles with filters:`, filters);
      return vehicles;
    } catch (error) {
      logger.error('Error filtering vehicles:', error);
      throw new Error('Failed to filter vehicles');
    }
  }
}

export default VehicleCatalogService;
