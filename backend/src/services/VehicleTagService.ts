import { PrismaClient } from "@prisma/client";

export class VehicleTagService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  // Add tags to a vehicle
  async addTagsToVehicle(vehicleId: string, tags: string[]): Promise<void> {
    // Remove existing tags
    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        smartTags: tags
      }
    });
  }

  // Get tags for a vehicle
  async getVehicleTags(vehicleId: string): Promise<string[]> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: { smartTags: true }
    });

    return vehicle?.smartTags || [];
  }

  // Search vehicles by tags
  async searchVehiclesByTags(tags: string[]): Promise<string[]> {
    const vehicles = await this.prisma.vehicle.findMany({
      where: {
        smartTags: {
          hasSome: tags
        }
      },
      select: { id: true }
    });

    return vehicles.map(v => v.id);
  }
}

export default VehicleTagService;
