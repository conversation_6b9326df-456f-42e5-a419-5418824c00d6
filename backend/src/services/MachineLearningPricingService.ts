import { PrismaClient } from '@prisma/client';
import * as tf from '@tensorflow/tfjs-node';
import { AppError } from '../middleware/errorMiddleware';

export interface PricingFeatures {
    vehicle_id: string;
    category: string;
    make: string;
    model: string;
    year: number;
    location: string;
    mileage: number;
    condition: string;
    seasonal_factor: number;
    historical_bookings: number;
    cancellation_rate: number;
    avg_rating: number;
}

export class MachineLearningPricingService {
    private prisma: PrismaClient;
    private model: tf.Sequential | null = null;

    constructor() {
        this.prisma = new PrismaClient();
    }

    /**
     * Prepare training data for the pricing model
     */
    async prepareTrainingData(): Promise<{
        features: number[][];
        labels: number[];
    }> {
        try {
            // Fetch historical booking and vehicle data
            const bookings = await this.prisma.booking.findMany({
                include: {
                    vehicle: true
                },
                where: {
                    status: 'COMPLETED'
                }
            });

            const features: number[][] = [];
            const labels: number[] = [];

            for (const booking of bookings) {
                const vehicle = booking.vehicle;
                
                // Extract features (using available fields with defaults)
                const feature = [
                    this.encodeCategory(vehicle.vehicleType),
                    this.encodeMake('default'), // Stub: vehicle.make not in schema
                    2020, // Stub: vehicle.year not in schema
                    this.encodeLocation('default'), // Stub: vehicle.location not in schema
                    0, // Stub: vehicle.mileage not in schema
                    this.encodeCondition('good'), // Stub: vehicle.condition not in schema
                    this.calculateSeasonalFactor(booking.startDate),
                    Math.ceil((booking.endDate.getTime() - booking.startDate.getTime()) / (1000 * 60 * 60 * 24)),
                    Number(booking.totalPrice)
                ];

                features.push(feature);
                labels.push(Number(booking.totalPrice));
            }

            return { features, labels };
        } catch (error) {
            throw new AppError('Failed to prepare training data', 500);
        }
    }

    /**
     * Train machine learning model for pricing prediction
     */
    async trainPricingModel() {
        try {
            const { features, labels } = await this.prepareTrainingData();

            // Normalize features
            const featureTensor = tf.tensor2d(features);
            const labelTensor = tf.tensor1d(labels);

            // Create and compile the model
            this.model = tf.sequential({
                layers: [
                    tf.layers.dense({
                        inputShape: [features[0].length],
                        units: 64,
                        activation: 'relu'
                    }),
                    tf.layers.dense({
                        units: 32,
                        activation: 'relu'
                    }),
                    tf.layers.dense({
                        units: 1,
                        activation: 'linear'
                    })
                ]
            });

            this.model.compile({
                optimizer: 'adam',
                loss: 'meanSquaredError',
                metrics: ['mae']
            });

            // Train the model
            await this.model.fit(featureTensor, labelTensor, {
                epochs: 100,
                batchSize: 32,
                validationSplit: 0.2
            });

            // Save the model (stub - in production would save to file)
            // await this.model.save('file://./pricing_model');

            return {
                status: 'Model trained successfully',
                features: features[0].length
            };
        } catch (error) {
            throw new AppError('Failed to train pricing model', 500);
        }
    }

    /**
     * Predict pricing for a vehicle
     */
    async predictPrice(features: PricingFeatures): Promise<number> {
        try {
            // Load model if not already loaded
            if (!this.model) {
                // Stub: In production, load from file or create simple model
                this.model = tf.sequential({
                    layers: [
                        tf.layers.dense({inputShape: [9], units: 50, activation: 'relu'}),
                        tf.layers.dense({units: 1})
                    ]
                });
            }

            // Prepare input features
            const inputFeature = [
                this.encodeCategory(features.category),
                this.encodeMake(features.make),
                features.year,
                this.encodeLocation(features.location),
                features.mileage,
                this.encodeCondition(features.condition),
                features.seasonal_factor,
                features.historical_bookings,
                features.cancellation_rate,
                features.avg_rating
            ];

            // Convert to tensor and predict
            const inputTensor = tf.tensor2d([inputFeature]);
            const prediction = this.model.predict(inputTensor) as tf.Tensor;

            // Get predicted price
            const predictedPrice = prediction.dataSync()[0];

            return predictedPrice;
        } catch (error) {
            throw new AppError('Failed to predict pricing', 500);
        }
    }

    /**
     * Encoding helper methods
     */
    private encodeCategory(category: string): number {
        const categories = ['economy', 'standard', 'luxury', 'premium'];
        return categories.indexOf(category.toLowerCase()) / categories.length;
    }

    private encodeMake(make: string): number {
        // Simple hash-based encoding
        let hash = 0;
        for (let i = 0; i < make.length; i++) {
            hash = ((hash << 5) - hash) + make.charCodeAt(i);
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash) / Number.MAX_SAFE_INTEGER;
    }

    private encodeLocation(location: string): number {
        const locations = ['urban', 'suburban', 'rural'];
        return locations.indexOf(location.toLowerCase()) / locations.length;
    }

    private encodeCondition(condition: string): number {
        const conditions = ['poor', 'fair', 'good', 'excellent'];
        return conditions.indexOf(condition.toLowerCase()) / conditions.length;
    }

    private calculateSeasonalFactor(date: Date): number {
        const month = date.getMonth();
        // Simple seasonal factor calculation
        const seasonalFactors = [
            0.8, 0.85, 0.9, 1.0, 1.1, 1.2, 
            1.3, 1.2, 1.1, 1.0, 0.9, 0.85
        ];
        return seasonalFactors[month];
    }
}

export default new MachineLearningPricingService(); 