import { PrismaClient } from '@prisma/client';
import * as tf from '@tensorflow/tfjs-node';
import { subDays, subMonths } from 'date-fns';

interface PricingFeatures {
  vehicleType: string;
  seasonality: number;
  demandForecast: number;
  historicalOccupancy: number;
  competitorPrices: number;
  specialEvents: boolean;
}

export class DynamicPricingService {
  private prisma: PrismaClient;
  private pricingModel: tf.Sequential | null = null;

  constructor() {
    this.prisma = new PrismaClient();
    this.initializePricingModel();
  }

  /**
   * Initialize machine learning pricing model
   */
  private async initializePricingModel() {
    // Create a sequential model
    this.pricingModel = tf.sequential();

    // Add layers
    this.pricingModel.add(tf.layers.dense({
      inputShape: [6], // Number of features
      units: 10,
      activation: 'relu'
    }));
    this.pricingModel.add(tf.layers.dense({
      units: 5,
      activation: 'relu'
    }));
    this.pricingModel.add(tf.layers.dense({
      units: 1, // Output: price adjustment factor
      activation: 'linear'
    }));

    // Compile the model
    this.pricingModel.compile({
      optimizer: 'adam',
      loss: 'meanSquaredError'
    });
  }

  /**
   * Extract pricing features for a vehicle
   * @param vehicleId Vehicle identifier
   */
  private async extractPricingFeatures(vehicleId: string): Promise<PricingFeatures> {
    const now = new Date();
    
    // Seasonality (month of the year)
    const seasonality = now.getMonth() / 11;

    // Demand forecast
    const demandForecast = await this.calculateDemandForecast(vehicleId);

    // Historical occupancy rate
    const historicalOccupancy = await this.calculateHistoricalOccupancy(vehicleId);

    // Competitor prices (simulated)
    const competitorPrices = await this.fetchCompetitorPrices(vehicleId);

    // Special events (e.g., holidays, local events)
    const specialEvents = await this.checkSpecialEvents(now);

    // Vehicle details
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });

    return {
      vehicleType: vehicle?.vehicleType || 'standard',
      seasonality,
      demandForecast,
      historicalOccupancy,
      competitorPrices,
      specialEvents
    };
  }

  /**
   * Calculate demand forecast for a vehicle
   * @param vehicleId Vehicle identifier
   */
  private async calculateDemandForecast(vehicleId: string): Promise<number> {
    const bookings = await this.prisma.booking.findMany({
      where: {
        vehicleId,
        createdAt: {
          gte: subMonths(new Date(), 12)
        }
      }
    });

    // Simple moving average forecast
    const monthlyBookings = bookings.reduce((acc, booking) => {
      const month = booking.createdAt.getMonth();
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, new Array(12).fill(0));

    const averageMonthlyBookings = monthlyBookings.reduce((a, b) => a + b, 0) / 12;
    return averageMonthlyBookings / 30; // Normalize to daily forecast
  }

  /**
   * Calculate historical occupancy rate
   * @param vehicleId Vehicle identifier
   */
  private async calculateHistoricalOccupancy(vehicleId: string): Promise<number> {
    const totalDays = 365;
    const bookedDays = await this.prisma.booking.aggregate({
      _sum: { durationInDays: true },
      where: { 
        vehicleId,
        status: 'CONFIRMED',
        createdAt: {
          gte: subDays(new Date(), 365)
        }
      }
    });

    return (bookedDays._sum.durationInDays || 0) / totalDays;
  }

  /**
   * Fetch competitor prices (simulated)
   * @param vehicleId Vehicle identifier
   */
  private async fetchCompetitorPrices(vehicleId: string): Promise<number> {
    // In a real-world scenario, this would fetch actual competitor pricing
    // For now, we'll simulate with a random factor
    return Math.random();
  }

  /**
   * Check for special events
   * @param date Date to check
   */
  private async checkSpecialEvents(date: Date): Promise<boolean> {
    // Implement logic to check for holidays or local events
    const holidays = [
      new Date(date.getFullYear(), 0, 1),   // New Year's Day
      new Date(date.getFullYear(), 11, 25)  // Christmas
    ];

    return holidays.some(holiday => 
      holiday.getMonth() === date.getMonth() && 
      holiday.getDate() === date.getDate()
    );
  }

  /**
   * Predict dynamic pricing adjustment
   * @param vehicleId Vehicle identifier
   * @param basePrice Base price of the vehicle
   */
  async predictPriceAdjustment(
    vehicleId: string, 
    basePrice: number
  ): Promise<number> {
    if (!this.pricingModel) {
      await this.initializePricingModel();
    }

    // Extract features
    const features = await this.extractPricingFeatures(vehicleId);

    // Convert features to tensor
    const featureTensor = tf.tensor2d([
      features.seasonality,
      features.demandForecast,
      features.historicalOccupancy,
      features.competitorPrices,
      features.specialEvents ? 1 : 0,
      // Vehicle type as encoded feature
      features.vehicleType === 'premium' ? 1 : 0
    ], [1, 6]);

    // Predict price adjustment factor
    const predictionTensor = this.pricingModel.predict(featureTensor) as tf.Tensor;
    const adjustmentFactor = await predictionTensor.data();

    // Clean up tensors
    featureTensor.dispose();
    predictionTensor.dispose();

    // Apply price adjustment
    const priceAdjustment = basePrice * (1 + adjustmentFactor[0]);

    // Log pricing decision
    await this.logPricingDecision(
      vehicleId, 
      basePrice, 
      priceAdjustment, 
      features
    );

    return priceAdjustment;
  }

  /**
   * Log pricing decision for future model training
   * @param vehicleId Vehicle identifier
   * @param basePrice Base price
   * @param adjustedPrice Adjusted price
   * @param features Pricing features
   */
  private async logPricingDecision(
    vehicleId: string, 
    basePrice: number, 
    adjustedPrice: number,
    features: PricingFeatures
  ) {
    await this.prisma.pricingLog.create({
      data: {
        vehicleId,
        basePrice,
        adjustedPrice,
        seasonality: features.seasonality,
        demandForecast: features.demandForecast,
        historicalOccupancy: features.historicalOccupancy,
        competitorPrices: features.competitorPrices,
        specialEvents: features.specialEvents
      }
    });
  }

  /**
   * Train pricing model with historical data
   */
  async trainPricingModel() {
    // Fetch historical pricing logs
    const pricingLogs = await this.prisma.pricingLog.findMany({
      take: 1000, // Limit to prevent memory issues
      orderBy: { createdAt: 'desc' }
    });

    // Prepare training data
    const features = pricingLogs.map(log => [
      log.seasonality,
      log.demandForecast,
      log.historicalOccupancy,
      log.competitorPrices,
      log.specialEvents ? 1 : 0,
      // Vehicle type as encoded feature
      log.vehicleType === 'premium' ? 1 : 0
    ]);

    const labels = pricingLogs.map(log => 
      (log.adjustedPrice - log.basePrice) / log.basePrice
    );

    // Convert to tensors
    const featuresTensor = tf.tensor2d(features);
    const labelsTensor = tf.tensor1d(labels);

    // Train the model
    await this.pricingModel?.fit(featuresTensor, labelsTensor, {
      epochs: 50,
      batchSize: 32
    });

    // Clean up tensors
    featuresTensor.dispose();
    labelsTensor.dispose();
  }
}

export default new DynamicPricingService(); 