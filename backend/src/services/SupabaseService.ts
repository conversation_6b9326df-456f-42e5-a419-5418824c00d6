import { supabase, supabasePublic } from '../lib/supabase'
import type { Database } from '../types/database'

type User = Database['public']['Tables']['users']['Row']
type Vehicle = Database['public']['Tables']['vehicles']['Row']
type Booking = Database['public']['Tables']['bookings']['Row']

export class SupabaseService {
  
  // User operations
  async getUsers() {
    const { data, error } = await supabase
      .from('users')
      .select('*')
    
    if (error) throw error
    return data
  }

  async getUserById(id: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  }

  async createUser(userData: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .insert(userData)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  async updateUser(id: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // Vehicle operations
  async getVehicles() {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
    
    if (error) throw error
    return data
  }

  async getVehicleById(id: string) {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  }

  async createVehicle(vehicleData: Partial<Vehicle>) {
    const { data, error } = await supabase
      .from('vehicles')
      .insert(vehicleData)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  async updateVehicle(id: string, updates: Partial<Vehicle>) {
    const { data, error } = await supabase
      .from('vehicles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // Booking operations
  async getBookings() {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
    
    if (error) throw error
    return data
  }

  async getBookingById(id: string) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  }

  async createBooking(bookingData: Partial<Booking>) {
    const { data, error } = await supabase
      .from('bookings')
      .insert(bookingData)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  async updateBooking(id: string, updates: Partial<Booking>) {
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  // Auth operations
  async signUp(email: string, password: string, userData?: Partial<User>) {
    const { data, error } = await supabasePublic.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    
    if (error) throw error
    return data
  }

  async signIn(email: string, password: string) {
    const { data, error } = await supabasePublic.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    return data
  }

  async signOut() {
    const { error } = await supabasePublic.auth.signOut()
    if (error) throw error
  }

  async getCurrentUser() {
    const { data: { user }, error } = await supabasePublic.auth.getUser()
    if (error) throw error
    return user
  }

  // Generic query method
  async query<T = any>(table: string, options: {
    select?: string
    where?: Record<string, any>
    orderBy?: string
    limit?: number
  } = {}) {
    let query = supabase.from(table).select(options.select || '*')
    
    if (options.where) {
      Object.entries(options.where).forEach(([key, value]) => {
        query = query.eq(key, value)
      })
    }
    
    if (options.orderBy) {
      query = query.order(options.orderBy)
    }
    
    if (options.limit) {
      query = query.limit(options.limit)
    }
    
    const { data, error } = await query
    
    if (error) throw error
    return data as T[]
  }

  // Health check
  async healthCheck() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('count')
        .limit(1)
      
      if (error) {
        return {
          status: 'error',
          message: error.message
        }
      }
      
      return {
        status: 'ok',
        message: 'Supabase connection healthy'
      }
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();

export default SupabaseService;