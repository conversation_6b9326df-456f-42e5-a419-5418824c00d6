import { supabase } from '../utils/supabaseClient';

interface PaymentMethodBreakdown {
  [key: string]: {
    count: number;
    total: number;
  };
}

export class TransactionService {
  async getTotalRevenue(): Promise<number> {
    const { data, error } = await supabase
      .from('bookings')
      .select('total')
      .eq('payment_status', 'paid');
    
    if (error) throw error;
    
    const totalRevenue = data?.reduce((sum, booking) => sum + (booking.total || 0), 0) || 0;
    return Number(totalRevenue.toFixed(2));
  }

  async getMonthlyRevenue(): Promise<number> {
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const { data, error } = await supabase
      .from('bookings')
      .select('total')
      .eq('payment_status', 'paid')
      .gte('created_at', firstDayOfMonth.toISOString());
    
    if (error) throw error;
    
    const monthlyRevenue = data?.reduce((sum, booking) => sum + (booking.total || 0), 0) || 0;
    return Number(monthlyRevenue.toFixed(2));
  }

  async getPendingPayments(): Promise<number> {
    const { count, error } = await supabase
      .from('bookings')
      .select('*', { count: 'exact' })
      .eq('payment_status', 'pending');
    
    if (error) throw error;
    return count || 0;
  }

  async getPaymentMethodBreakdown(): Promise<PaymentMethodBreakdown> {
    const { data, error } = await supabase
      .from('bookings')
      .select('payment_method, total')
      .eq('payment_status', 'paid');
    
    if (error) throw error;

    const breakdown: PaymentMethodBreakdown = {};
    data?.forEach(booking => {
      const method = booking.payment_method;
      if (!breakdown[method]) {
        breakdown[method] = { count: 0, total: 0 };
      }
      breakdown[method].count += 1;
      breakdown[method].total += booking.total || 0;
    });

    return breakdown;
  }
}

export default TransactionService;