// =============================================================================
// PRODUCTION EMAIL SERVICE
// =============================================================================
// Comprehensive email service for notifications, verification, and communication

import nodemailer from 'nodemailer';
import { Booking, User, Vehicle, Payment } from '@prisma/client';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

class ProductionEmailService {
  private transporter: nodemailer.Transporter | null = null;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.FROM_NAME || 'RentaHub';
    
    this.initializeTransporter();
    console.log('📧 ProductionEmailService initialized');
  }

  private initializeTransporter() {
    const smtpHost = process.env.SMTP_HOST;
    const smtpPort = parseInt(process.env.SMTP_PORT || '587');
    const smtpUser = process.env.SMTP_USER;
    const smtpPass = process.env.SMTP_PASS;

    if (!smtpHost || !smtpUser || !smtpPass) {
      console.warn('⚠️ SMTP configuration not found. Email service will be disabled.');
      console.log('📝 To enable email service, set these environment variables:');
      console.log('   SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS, FROM_EMAIL, FROM_NAME');
      return;
    }

    try {
      this.transporter = nodemailer.createTransport({
        host: smtpHost,
        port: smtpPort,
        secure: smtpPort === 465, // true for 465, false for other ports
        auth: {
          user: smtpUser,
          pass: smtpPass,
        },
        tls: {
          rejectUnauthorized: false // Allow self-signed certificates
        }
      });

      console.log('✅ Email transporter configured successfully');
    } catch (error) {
      console.error('❌ Failed to configure email transporter:', error);
    }
  }

  // =============================================================================
  // CORE EMAIL METHODS
  // =============================================================================

  async sendEmail(to: string, subject: string, html: string, text?: string): Promise<boolean> {
    if (!this.transporter) {
      console.warn('⚠️ Email service not configured, skipping email send');
      return false;
    }

    try {
      const mailOptions = {
        from: `${this.fromName} <${this.fromEmail}>`,
        to,
        subject,
        html,
        text: text || this.stripHtml(html)
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      return false;
    }
  }

  async testConnection(): Promise<boolean> {
    if (!this.transporter) {
      return false;
    }

    try {
      await this.transporter.verify();
      console.log('✅ Email service connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }

  // =============================================================================
  // USER VERIFICATION EMAILS
  // =============================================================================

  async sendWelcomeEmail(user: User): Promise<boolean> {
    const template = this.getWelcomeTemplate(user);
    return this.sendEmail(user.email, template.subject, template.html, template.text);
  }

  async sendEmailVerification(user: User, verificationToken: string): Promise<boolean> {
    const template = this.getEmailVerificationTemplate(user, verificationToken);
    return this.sendEmail(user.email, template.subject, template.html, template.text);
  }

  async sendPasswordReset(user: User, resetToken: string): Promise<boolean> {
    const template = this.getPasswordResetTemplate(user, resetToken);
    return this.sendEmail(user.email, template.subject, template.html, template.text);
  }

  private getPasswordResetTemplate(user: User, resetToken: string) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    return {
      subject: 'Reset Your RentaHub Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Password Reset Request</h2>
          <p>Hello ${user.name || user.email},</p>
          <p>You requested to reset your password. Click the link below to reset it:</p>
          <a href="${resetUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
          <p>If you didn't request this, please ignore this email.</p>
          <p>This link will expire in 1 hour.</p>
        </div>
      `,
      text: `Password reset requested. Visit: ${resetUrl}`
    };
  }

  // =============================================================================
  // BOOKING NOTIFICATION EMAILS
  // =============================================================================

  async sendBookingConfirmation(booking: Booking, customer: User, provider: User, vehicle: Vehicle): Promise<boolean> {
    const template = this.getBookingConfirmationTemplate(booking, customer, provider, vehicle);
    
    // Send to customer
    const customerSent = await this.sendEmail(customer.email, template.subject, template.html, template.text);
    
    // Send to provider
    const providerTemplate = this.getBookingNotificationTemplate(booking, customer, provider, vehicle);
    const providerSent = await this.sendEmail(provider.email, providerTemplate.subject, providerTemplate.html, providerTemplate.text);
    
    return customerSent && providerSent;
  }

  async sendBookingCancellation(booking: Booking, customer: User, provider: User, vehicle: Vehicle, reason: string): Promise<boolean> {
    const template = this.getBookingCancellationTemplate(booking, customer, provider, vehicle, reason);
    
    // Send to both customer and provider
    const customerSent = await this.sendEmail(customer.email, template.subject, template.html, template.text);
    const providerSent = await this.sendEmail(provider.email, template.subject, template.html, template.text);
    
    return customerSent && providerSent;
  }

  async sendBookingReminder(booking: Booking, customer: User, vehicle: Vehicle): Promise<boolean> {
    const template = this.getBookingReminderTemplate(booking, customer, vehicle);
    return this.sendEmail(customer.email, template.subject, template.html, template.text);
  }

  // =============================================================================
  // PAYMENT NOTIFICATION EMAILS
  // =============================================================================

  async sendPaymentConfirmation(payment: Payment, booking: Booking, customer: User): Promise<boolean> {
    const template = this.getPaymentConfirmationTemplate(payment, booking, customer);
    return this.sendEmail(customer.email, template.subject, template.html, template.text);
  }

  async sendPaymentFailed(payment: Payment, booking: Booking, customer: User): Promise<boolean> {
    const template = this.getPaymentFailedTemplate(payment, booking, customer);
    return this.sendEmail(customer.email, template.subject, template.html, template.text);
  }

  async sendRefundNotification(payment: Payment, booking: Booking, customer: User, refundAmount: number): Promise<boolean> {
    const template = this.getRefundNotificationTemplate(payment, booking, customer, refundAmount);
    return this.sendEmail(customer.email, template.subject, template.html, template.text);
  }

  // =============================================================================
  // PROVIDER NOTIFICATION EMAILS
  // =============================================================================

  async sendProviderVerificationRequest(provider: User): Promise<boolean> {
    const template = this.getProviderVerificationTemplate(provider);
    
    // Send to provider
    const providerSent = await this.sendEmail(provider.email, template.subject, template.html, template.text);
    
    // Send to admin
    const adminTemplate = this.getAdminVerificationNotificationTemplate(provider);
    const adminSent = await this.sendEmail('<EMAIL>', adminTemplate.subject, adminTemplate.html, adminTemplate.text);
    
    return providerSent && adminSent;
  }

  async sendProviderVerificationApproved(provider: User): Promise<boolean> {
    const template = this.getProviderApprovedTemplate(provider);
    return this.sendEmail(provider.email, template.subject, template.html, template.text);
  }

  async sendProviderVerificationRejected(provider: User, reason: string): Promise<boolean> {
    const template = this.getProviderRejectedTemplate(provider, reason);
    return this.sendEmail(provider.email, template.subject, template.html, template.text);
  }

  // =============================================================================
  // EMAIL TEMPLATES
  // =============================================================================

  private getWelcomeTemplate(user: User): EmailTemplate {
    return {
      subject: 'Welcome to RentaHub! 🎉',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Welcome to RentaHub!</h1>
          <p>Hi ${user.name || 'there'},</p>
          <p>Welcome to RentaHub, Indonesia's premier vehicle rental platform! We're excited to have you join our community.</p>
          <p>With RentaHub, you can:</p>
          <ul>
            <li>🛵 Rent scooters, bikes, and vehicles easily</li>
            <li>💰 Enjoy competitive prices and transparent fees</li>
            <li>🔒 Book with confidence using our secure platform</li>
            <li>⭐ Read reviews from verified customers</li>
          </ul>
          <p>Ready to start your journey? <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}" style="color: #2563eb;">Browse available vehicles</a></p>
          <p>If you have any questions, our support team is here to help at ${this.fromEmail}</p>
          <p>Happy riding!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Welcome to RentaHub! Hi ${user.name || 'there'}, welcome to RentaHub, Indonesia's premier vehicle rental platform!`
    };
  }

  private getEmailVerificationTemplate(user: User, token: string): EmailTemplate {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${token}`;
    
    return {
      subject: 'Verify your RentaHub email address',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Verify Your Email Address</h1>
          <p>Hi ${user.name || 'there'},</p>
          <p>Please verify your email address to complete your RentaHub registration.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Verify Email Address</a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
          <p>If you didn't create a RentaHub account, you can safely ignore this email.</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Verify your RentaHub email address. Click this link: ${verificationUrl}`
    };
  }

  private getBookingConfirmationTemplate(booking: Booking, customer: User, provider: User, vehicle: Vehicle): EmailTemplate {
    return {
      subject: `Booking Confirmed - ${vehicle.brand || 'Vehicle'} ${vehicle.model}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Booking Confirmed! ✅</h1>
          <p>Hi ${customer.name || customer.email},</p>
          <p>Your booking has been confirmed! Here are the details:</p>
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Booking Details</h3>
            <p><strong>Vehicle:</strong> ${vehicle.brand || 'Vehicle'} ${vehicle.model} (${vehicle.year})</p>
            <p><strong>Dates:</strong> ${booking.startDate} to ${booking.endDate}</p>
            <p><strong>Pickup Location:</strong> TBD</p>
            <p><strong>Total Amount:</strong> IDR ${Number(booking.totalPrice).toLocaleString()}</p>
            <p><strong>Booking ID:</strong> ${booking.id}</p>
          </div>
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4>Provider Contact</h4>
            <p><strong>Name:</strong> ${provider.name || provider.email}</p>
            <p><strong>Business:</strong> ${provider.companyName || 'Individual Provider'}</p>
            <p><strong>Phone:</strong> ${provider.phoneNumber}</p>
          </div>
          <p>Please arrive on time for pickup. Don't forget to bring a valid ID!</p>
          <p>Need help? Contact us at ${this.fromEmail}</p>
          <p>Safe travels!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Booking confirmed for ${vehicle.brand || 'Vehicle'} ${vehicle.model} from ${booking.startDate} to ${booking.endDate}. Total: IDR ${Number(booking.totalPrice).toLocaleString()}`
    };
  }

  private getProviderVerificationTemplate(provider: User): EmailTemplate {
    return {
      subject: 'Provider Verification Request Submitted',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">Verification Request Submitted</h1>
          <p>Hi ${provider.name || provider.email},</p>
          <p>Thank you for submitting your provider verification request for <strong>${provider.name || 'your business'}</strong>.</p>
          <p>Our team will review your documents and get back to you within 2-3 business days.</p>
          <p>We'll notify you via email once the verification is complete.</p>
          <p>If you have any questions, please contact us at ${this.fromEmail}</p>
          <p>Thank you for choosing RentaHub!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Provider verification request submitted for ${provider.name || 'your business'}. We'll review and get back to you within 2-3 business days.`
    };
  }

  private getAdminVerificationNotificationTemplate(provider: User): EmailTemplate {
    return {
      subject: `New Provider Verification Request - ${provider.name || provider.email}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc2626;">New Provider Verification Request</h1>
          <p>A new provider verification request has been submitted:</p>
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Business Name:</strong> ${provider.companyName || 'Not specified'}</p>
            <p><strong>Owner:</strong> ${provider.name || provider.email}</p>
            <p><strong>Email:</strong> ${provider.email}</p>
            <p><strong>Phone:</strong> ${provider.phoneNumber}</p>
            <p><strong>City:</strong> ${provider.companyName || 'Not specified'}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          <p>Please review the verification documents and approve/reject the request.</p>
          <p><a href="${process.env.ADMIN_URL || 'http://localhost:3000/admin'}/providers/verification" style="color: #2563eb;">Review in Admin Panel</a></p>
        </div>
      `,
      text: `New provider verification request from ${provider.name || provider.email} (${provider.email}). Please review in admin panel.`
    };
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  private getBookingNotificationTemplate(booking: Booking, customer: User, provider: User, vehicle: Vehicle): EmailTemplate {
    return {
      subject: `New Booking - ${vehicle.brand || 'Vehicle'} ${vehicle.model}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2563eb;">New Booking Received! 📅</h1>
          <p>Hi ${provider.name || provider.email},</p>
          <p>You have a new booking for your ${vehicle.brand || 'Vehicle'} ${vehicle.model}!</p>
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Booking Details</h3>
            <p><strong>Customer:</strong> ${customer.name || customer.email}</p>
            <p><strong>Phone:</strong> ${customer.phoneNumber}</p>
            <p><strong>Dates:</strong> ${booking.startDate} to ${booking.endDate}</p>
            <p><strong>Pickup:</strong> TBD</p>
            <p><strong>Amount:</strong> IDR ${Number(booking.totalPrice).toLocaleString()}</p>
          </div>
          <p>Please prepare your vehicle and be ready for pickup!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `New booking from ${customer.name || customer.email} for ${vehicle.brand || 'Vehicle'} ${vehicle.model}`
    };
  }

  private getBookingCancellationTemplate(booking: Booking, customer: User, provider: User, vehicle: Vehicle, reason: string): EmailTemplate {
    return {
      subject: `Booking Cancelled - ${vehicle.brand || 'Vehicle'} ${vehicle.model}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc2626;">Booking Cancelled</h1>
          <p>The booking for ${vehicle.brand || 'Vehicle'} ${vehicle.model} has been cancelled.</p>
          <p><strong>Reason:</strong> ${reason}</p>
          <p><strong>Booking ID:</strong> ${booking.id}</p>
          <p>If payment was made, a refund will be processed within 3-5 business days.</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Booking cancelled for ${vehicle.brand || 'Vehicle'} ${vehicle.model}. Reason: ${reason}`
    };
  }

  private getBookingReminderTemplate(booking: Booking, customer: User, vehicle: Vehicle): EmailTemplate {
    return {
      subject: `Booking Reminder - Tomorrow's Pickup`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #f59e0b;">Booking Reminder 🔔</h1>
          <p>Hi ${customer.name || customer.email},</p>
          <p>This is a reminder that your booking starts tomorrow!</p>
          <p><strong>Vehicle:</strong> ${vehicle.brand || 'Vehicle'} ${vehicle.model}</p>
          <p><strong>Pickup Date:</strong> ${booking.startDate}</p>
          <p><strong>Pickup Location:</strong> TBD</p>
          <p>Don't forget to bring your ID and arrive on time!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Reminder: Your booking for ${vehicle.brand || 'Vehicle'} ${vehicle.model} starts tomorrow`
    };
  }

  private getPaymentConfirmationTemplate(payment: Payment, booking: Booking, customer: User): EmailTemplate {
    return {
      subject: 'Payment Confirmed - RentaHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Payment Confirmed! ✅</h1>
          <p>Hi ${customer.name || customer.email},</p>
          <p>Your payment has been successfully processed.</p>
          <p><strong>Amount:</strong> IDR ${payment.amount.toLocaleString()}</p>
          <p><strong>Payment ID:</strong> ${payment.id}</p>
          <p><strong>Booking ID:</strong> ${booking.id}</p>
          <p>Your booking is now confirmed and ready!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Payment confirmed for IDR ${payment.amount.toLocaleString()}. Booking ID: ${booking.id}`
    };
  }

  private getPaymentFailedTemplate(payment: Payment, booking: Booking, customer: User): EmailTemplate {
    return {
      subject: 'Payment Failed - RentaHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc2626;">Payment Failed ❌</h1>
          <p>Hi ${customer.name || customer.email},</p>
          <p>Unfortunately, your payment could not be processed.</p>
          <p>Please try again or contact your bank for assistance.</p>
          <p><strong>Booking ID:</strong> ${booking.id}</p>
          <p>Need help? Contact us at ${this.fromEmail}</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Payment failed for booking ${booking.id}. Please try again or contact support.`
    };
  }

  private getRefundNotificationTemplate(payment: Payment, booking: Booking, customer: User, refundAmount: number): EmailTemplate {
    return {
      subject: 'Refund Processed - RentaHub',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Refund Processed ✅</h1>
          <p>Hi ${customer.name || customer.email},</p>
          <p>Your refund has been processed successfully.</p>
          <p><strong>Refund Amount:</strong> IDR ${refundAmount.toLocaleString()}</p>
          <p><strong>Original Payment:</strong> IDR ${payment.amount.toLocaleString()}</p>
          <p><strong>Booking ID:</strong> ${booking.id}</p>
          <p>The refund will appear in your account within 3-5 business days.</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Refund of IDR ${refundAmount.toLocaleString()} processed for booking ${booking.id}`
    };
  }

  private getProviderApprovedTemplate(provider: User): EmailTemplate {
    return {
      subject: 'Provider Verification Approved! 🎉',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Verification Approved! 🎉</h1>
          <p>Hi ${provider.name || provider.email},</p>
          <p>Congratulations! Your provider verification for <strong>${provider.companyName || 'your business'}</strong> has been approved.</p>
          <p>You can now start listing your vehicles and accepting bookings on RentaHub!</p>
          <p><a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/provider-dashboard" style="color: #2563eb;">Go to Provider Dashboard</a></p>
          <p>Welcome to the RentaHub provider community!</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Provider verification approved for ${provider.companyName || 'your business'}! You can now start listing vehicles.`
    };
  }

  private getProviderRejectedTemplate(provider: User, reason: string): EmailTemplate {
    return {
      subject: 'Provider Verification Update',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc2626;">Verification Update</h1>
          <p>Hi ${provider.name || provider.email},</p>
          <p>Thank you for your interest in becoming a RentaHub provider.</p>
          <p>Unfortunately, we need additional information before we can approve your verification:</p>
          <p><strong>Reason:</strong> ${reason}</p>
          <p>Please update your information and resubmit your verification request.</p>
          <p>If you have questions, please contact us at ${this.fromEmail}</p>
          <p>The RentaHub Team</p>
        </div>
      `,
      text: `Provider verification needs additional information: ${reason}. Please resubmit your request.`
    };
  }
}

// Export singleton instance
export default new ProductionEmailService();
