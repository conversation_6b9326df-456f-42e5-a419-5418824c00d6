import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface ProviderWithStats {
  id: string;
  email: string;
  name: string;
  phoneNumber?: string;
  companyName?: string;
  rating: number;
  responseRate: number;
  totalVehicles: number;
  totalBookings: number;
  totalRevenue: number;
  joinedAt: Date;
  verified: boolean;
  active: boolean;
  profileImage?: string;
  bankDetails?: {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
    branchName?: string;
  };
}

export interface ProviderDashboardStats {
  totalVehicles: number;
  activeVehicles: number;
  totalBookings: number;
  pendingBookings: number;
  completedBookings: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageRating: number;
  responseRate: number;
  totalViews?: number;
  conversionRate?: number;
  showCTA?: boolean;
  cta?: {
    type: string;
    title: string;
    message: string;
    action: string;
    buttonText: string;
  };
  recentBookings: Array<{
    id: string;
    vehicleId: string;
    vehicleName: string;
    customerName: string;
    startDate: Date;
    endDate: Date;
    status: string;
    totalAmount: number;
  }>;
  topVehicles: Array<{
    id: string;
    name: string;
    bookings: number;
    revenue: number;
    rating: number;
  }>;
  earningsChart?: Array<{
    date: string;
    earnings: number;
    bookings: number;
  }>;
  bookingsByCategory?: Array<{
    category: string;
    count: number;
    earnings: number;
  }>;
  recentActivity?: Array<{
    id: string;
    type: 'booking' | 'payment' | 'review';
    description: string;
    amount?: number;
    timestamp: string;
  }>;
}

export class ProviderService {
  /**
   * Get provider by ID with stats
   */
  static async getProviderById(id: string): Promise<ProviderWithStats | null> {
    try {
      console.log('🔍 Looking for provider with ID:', id);

      const user = await prisma.user.findUnique({
        where: {
          id,
          role: 'PROVIDER'
        },
        include: {
          _count: {
            select: {
              listedVehicles: true,
              providerBookings: true
            }
          }
        }
      });

      if (!user) {
        console.log('❌ Provider not found with ID:', id);
        return null;
      }

      console.log('✅ Provider found:', user.name, user.email);

      // Calculate additional stats (with proper error handling)
      let totalRevenue = { _sum: { totalPrice: 0 } };
      let avgRating = { _avg: { rating: 0 } };

      try {
        const revenueResult = await prisma.booking.aggregate({
          where: {
            providerId: id,
            status: 'COMPLETED'
          },
          _sum: {
            totalPrice: true
          }
        });
        totalRevenue = { _sum: { totalPrice: Number(revenueResult._sum.totalPrice || 0) } };
      } catch (error) {
        console.log('⚠️ Error fetching revenue stats:', error);
      }

      try {
        // Get reviews through bookings since Review doesn't have providerId directly
        avgRating = await prisma.review.aggregate({
          where: {
            booking: {
              providerId: id
            }
          },
          _avg: {
            rating: true
          }
        });
      } catch (error) {
        console.log('⚠️ Error fetching rating stats:', error);
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name || 'Unknown Provider',
        phoneNumber: user.phoneNumber || undefined,
        companyName: user.companyName || undefined,
        rating: avgRating._avg.rating || 0,
        responseRate: 95, // Default response rate
        totalVehicles: user._count.listedVehicles,
        totalBookings: user._count.providerBookings,
        totalRevenue: Number(totalRevenue._sum.totalPrice) || 0,
        joinedAt: user.createdAt,
        verified: user.emailVerified,
        active: user.status === 'ACTIVE',
        profileImage: user.profileImage || undefined,
        bankDetails: {
          accountName: user.bankAccountName || undefined,
          accountNumber: user.bankAccountNumber || undefined,
          bankName: user.bankName || undefined,
          branchName: user.bankBranch || undefined,
        }
      };
    } catch (error) {
      logger.error('Error fetching provider by ID:', error);
      throw new Error('Failed to fetch provider');
    }
  }

  /**
   * Get provider dashboard statistics
   */
  static async getProviderDashboardStats(providerId: string): Promise<ProviderDashboardStats> {
    try {
      console.log('📊 Fetching dashboard stats for provider:', providerId);

      // First, verify the user exists and has provider role
      const user = await prisma.user.findUnique({
        where: { id: providerId },
        select: { id: true, role: true, name: true, email: true }
      });

      if (!user) {
        console.log('❌ User not found:', providerId);
        throw new Error('User not found');
      }

      if (user.role !== 'PROVIDER') {
        console.log('❌ User is not a provider:', user.role);
        throw new Error('User is not a provider');
      }

      console.log('✅ Provider found:', user.name, user.email);

      // Get basic counts
      const [
        totalVehicles,
        totalBookings,
        pendingBookings,
        completedBookings,
        totalRevenue,
        monthlyRevenue,
        avgRating
      ] = await Promise.all([
        // Total vehicles
        prisma.vehicle.count({
          where: { providerId }
        }),

        // Total bookings
        prisma.booking.count({
          where: { providerId }
        }),

        // Pending bookings
        prisma.booking.count({
          where: {
            providerId,
            status: 'PENDING'
          }
        }),

        // Completed bookings
        prisma.booking.count({
          where: {
            providerId,
            status: 'COMPLETED'
          }
        }),

        // Total revenue
        prisma.booking.aggregate({
          where: {
            providerId,
            status: 'COMPLETED'
          },
          _sum: {
            totalPrice: true
          }
        }),

        // Monthly revenue (current month)
        prisma.booking.aggregate({
          where: {
            providerId,
            status: 'COMPLETED',
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: {
            totalPrice: true
          }
        }),

        // Average rating (through bookings)
        prisma.review.aggregate({
          where: {
            booking: {
              providerId
            }
          },
          _avg: {
            rating: true
          }
        })
      ]);

      // Get recent bookings
      const recentBookings = await prisma.booking.findMany({
        where: { providerId },
        include: {
          vehicle: {
            select: {
              brand: true,
              model: true
            }
          },
          user: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      });

      // Get top vehicles by bookings
      const topVehicles = await prisma.vehicle.findMany({
        where: { providerId },
        include: {
          _count: {
            select: {
              bookings: true
            }
          },
          bookings: {
            where: {
              status: 'COMPLETED'
            },
            select: {
              totalPrice: true
            }
          },
          reviews: {
            select: {
              rating: true
            }
          }
        },
        orderBy: {
          bookings: {
            _count: 'desc'
          }
        },
        take: 5
      });

      console.log('✅ Dashboard stats calculated successfully');
      console.log('📊 Stats summary:', {
        totalVehicles,
        totalBookings,
        pendingBookings,
        completedBookings,
        totalRevenue: Number(totalRevenue._sum.totalPrice) || 0,
        monthlyRevenue: Number(monthlyRevenue._sum.totalPrice) || 0,
        recentBookingsCount: recentBookings.length,
        topVehiclesCount: topVehicles.length
      });

      // Generate some basic analytics data
      const totalRevenueNum = Number(totalRevenue._sum.totalPrice) || 0;
      const monthlyRevenueNum = Number(monthlyRevenue._sum.totalPrice) || 0;

      // Check if provider has any data and determine appropriate CTAs
      const hasVehicles = totalVehicles > 0;
      const hasBookings = totalBookings > 0;

      let cta = null;
      if (!hasVehicles) {
        cta = {
          type: 'ADD_VEHICLE',
          title: 'Add Your First Vehicle',
          message: 'Start earning by adding your first vehicle to the platform',
          action: '/provider-dashboard/vehicles/add',
          buttonText: 'Add Vehicle'
        };
      } else if (!hasBookings) {
        cta = {
          type: 'PROMOTE_VEHICLES',
          title: 'Get Your First Booking',
          message: 'Your vehicles are listed! Share your profile to get your first booking',
          action: '/provider-dashboard/promote',
          buttonText: 'Promote Vehicles'
        };
      }

      const stats = {
        totalVehicles,
        activeVehicles: totalVehicles, // For now, assume all vehicles are active
        totalBookings,
        pendingBookings,
        completedBookings,
        totalRevenue: totalRevenueNum,
        monthlyRevenue: monthlyRevenueNum,
        averageRating: avgRating._avg.rating || 0,
        responseRate: 95, // TODO: Calculate actual response rate
        totalViews: totalBookings * 5, // Estimate: 5 views per booking
        conversionRate: totalBookings > 0 ? totalBookings / (totalBookings * 5) : 0,
        // Add CTA information
        showCTA: cta !== null,
        cta,
        // Only include data arrays if there's actual data
        recentBookings: hasBookings ? recentBookings.map(booking => ({
          id: booking.id,
          vehicleId: booking.vehicleId,
          vehicleName: `${booking.vehicle?.brand || ''} ${booking.vehicle?.model || ''}`.trim(),
          customerName: booking.user?.name || 'Unknown',
          startDate: booking.startDate,
          endDate: booking.endDate,
          status: booking.status,
          totalAmount: Number(booking.totalPrice)
        })) : [],
        topVehicles: hasVehicles ? topVehicles.map(vehicle => ({
          id: vehicle.id,
          name: `${vehicle.brand || ''} ${vehicle.model || ''}`.trim(),
          bookings: vehicle._count.bookings,
          revenue: vehicle.bookings.reduce((sum, booking) => sum + Number(booking.totalPrice), 0),
          rating: vehicle.reviews.length > 0
            ? vehicle.reviews.reduce((sum, review) => sum + review.rating, 0) / vehicle.reviews.length
            : 0
        })) : [],
        // Generate chart data only if there's revenue data
        earningsChart: hasBookings ? Array.from({ length: 7 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - (6 - i));
          return {
            date: date.toISOString().split('T')[0],
            earnings: Math.floor(monthlyRevenueNum / 30), // Rough daily average
            bookings: Math.floor(totalBookings / 30) // Rough daily average
          };
        }) : [],
        // Category breakdown only if there are bookings
        bookingsByCategory: hasBookings ? [
          { category: 'Cars', count: Math.floor(totalBookings * 0.6), earnings: Math.floor(totalRevenueNum * 0.6) },
          { category: 'Bikes', count: Math.floor(totalBookings * 0.3), earnings: Math.floor(totalRevenueNum * 0.3) },
          { category: 'Scooters', count: Math.floor(totalBookings * 0.1), earnings: Math.floor(totalRevenueNum * 0.1) }
        ] : [],
        // Recent activity from bookings
        recentActivity: hasBookings ? recentBookings.slice(0, 5).map(booking => ({
          id: booking.id,
          type: 'booking' as const,
          description: `New booking for ${booking.vehicle?.brand || ''} ${booking.vehicle?.model || ''}`,
          amount: Number(booking.totalPrice),
          timestamp: booking.createdAt.toISOString()
        })) : []
      };

      return stats;
    } catch (error) {
      logger.error('Error fetching provider dashboard stats:', error);
      throw new Error('Failed to fetch dashboard statistics');
    }
  }

  /**
   * Update provider profile
   */
  static async updateProvider(id: string, data: {
    name?: string;
    phoneNumber?: string;
    companyName?: string;
    profileImage?: string;
  }): Promise<ProviderWithStats> {
    try {
      const updatedUser = await prisma.user.update({
        where: { 
          id,
          role: 'PROVIDER'
        },
        data: {
          name: data.name,
          phoneNumber: data.phoneNumber,
          companyName: data.companyName,
          profileImage: data.profileImage,
          updatedAt: new Date()
        }
      });

      // Return updated provider with stats
      const provider = await this.getProviderById(id);
      if (!provider) {
        throw new Error('Provider not found after update');
      }

      logger.info(`Provider profile updated: ${id}`);
      return provider;
    } catch (error) {
      logger.error('Error updating provider:', error);
      throw new Error('Failed to update provider profile');
    }
  }

  /**
   * Get all providers (for admin)
   */
  static async getAllProviders(
    page: number = 1,
    limit: number = 20,
    search?: string
  ): Promise<{
    providers: ProviderWithStats[];
    total: number;
    pages: number;
  }> {
    try {
      const skip = (page - 1) * limit;
      
      const whereClause = {
        role: 'PROVIDER' as const,
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
            { businessName: { contains: search, mode: 'insensitive' as const } }
          ]
        })
      };

      const [providers, total] = await Promise.all([
        prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc'
          }
        }),
        prisma.user.count({
          where: whereClause
        })
      ]);

      // Get stats for each provider
      const providersWithStats = await Promise.all(
        providers.map(async (provider) => {
          const stats = await this.getProviderById(provider.id);
          return stats!;
        })
      );

      return {
        providers: providersWithStats,
        total,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error fetching all providers:', error);
      throw new Error('Failed to fetch providers');
    }
  }

  // Duplicate methods removed - using existing implementations above

  /**
   * Create provider
   */
  static async createProvider(data: any) {
    try {
      return await prisma.user.create({
        data: {
          ...data,
          role: 'PROVIDER'
        }
      });
    } catch (error) {
      console.error('Error creating provider:', error);
      throw new Error('Failed to create provider');
    }
  }
}

export default ProviderService;
