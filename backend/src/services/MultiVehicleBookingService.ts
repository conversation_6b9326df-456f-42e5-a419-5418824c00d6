import { PrismaClient, Booking } from '@prisma/client';
import { PaymentService } from './PaymentService';
import { Prisma } from '@prisma/client';

// Stub BookingService and types
interface BookingService {
  createBooking(data: any): Promise<any>;
  calculatePrice(data: any): Promise<any>;
  checkVehicleAvailability(vehicleId: string, startDate: Date, endDate: Date): Promise<boolean>;
  cancelBooking(bookingId: string, userId: string): Promise<any>;
}

interface CreateBookingData {
  userId: string;
  providerId: string;
  vehicleId: string;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  paymentMethod?: string;
}

export interface MultiVehicleBookingRequest {
  userId: string;
  providerId: string;
  vehicles: Array<{
    vehicleId: string;
    startDate: Date;
    endDate: Date;
  }>;
  paymentMethod: 'CARD' | 'CASH';
}

export interface MultiVehicleBookingResult {
  id?: string; // Optional id for compatibility
  bookingIds: string[];
  totalPrice: Prisma.Decimal;
  status: 'PENDING' | 'CONFIRMED' | 'PARTIAL_CONFIRMATION';
}

export class MultiVehicleBookingService {
  private prisma: PrismaClient;
  private bookingService: BookingService;

  constructor() {
    this.prisma = new PrismaClient();
    const paymentService = new PaymentService(this.prisma);
    // Stub BookingService
    this.bookingService = {
      createBooking: async (data: any) => ({ id: 'stub-booking-id', ...data }),
      calculatePrice: async (data: any) => ({ totalPrice: 100 }),
      checkVehicleAvailability: async (vehicleId: string, startDate: Date, endDate: Date) => true,
      cancelBooking: async (bookingId: string, userId: string) => ({ id: bookingId, status: 'CANCELLED' })
    };
  }

  /**
   * Create a multi-vehicle booking
   * @param bookingRequest Multi-vehicle booking request
   */
  async createMultiVehicleBooking(
    bookingRequest: MultiVehicleBookingRequest
  ): Promise<MultiVehicleBookingResult> {
    const { userId, vehicles, paymentMethod } = bookingRequest;

    // Validate vehicle availability
    const availabilityChecks = await Promise.all(
      vehicles.map(async (vehicle) => {
        try {
          await this.bookingService.checkVehicleAvailability(
            vehicle.vehicleId, 
            vehicle.startDate, 
            vehicle.endDate
          );
          return { vehicleId: vehicle.vehicleId, available: true };
        } catch {
          return { vehicleId: vehicle.vehicleId, available: false };
        }
      })
    );

    // Separate available and unavailable vehicles
    const availableVehicles = vehicles.filter((_, index) => 
      availabilityChecks[index].available
    );
    const unavailableVehicles = vehicles.filter((_, index) => 
      !availabilityChecks[index].available
    );

    // Create bookings for available vehicles
    const bookingPromises = availableVehicles.map(async (vehicle) => {
      const bookingData: CreateBookingData = {
        userId,
        providerId: bookingRequest.providerId,
        vehicleId: vehicle.vehicleId,
        startDate: vehicle.startDate,
        endDate: vehicle.endDate,
        paymentMethod,
        totalPrice: 0
      };
      return this.bookingService.createBooking(bookingData);
    });

    const bookings = (await Promise.all(bookingPromises)) as Booking[];

    // Calculate total price
    const totalPrice = bookings.reduce((sum, booking) => sum + Number(booking.totalPrice), 0);

    // Determine overall booking status
    const status = availableVehicles.length === vehicles.length 
      ? 'CONFIRMED' 
      : (availableVehicles.length > 0 ? 'PARTIAL_CONFIRMATION' : 'PENDING');

    return {
      bookingIds: bookings.map(booking => booking.id),
      totalPrice: new Prisma.Decimal(totalPrice),
      status
    };
  }

  /**
   * Get multi-vehicle booking details
   * @param bookingIds Array of booking IDs
   */
  async getMultiVehicleBookingDetails(bookingIds: string[]) {
    return this.prisma.booking.findMany({
      where: { id: { in: bookingIds } },
      include: {
        vehicle: true,
        user: true
      }
    });
  }

  /**
   * Cancel multi-vehicle booking
   * @param bookingIds Array of booking IDs
   * @param userId User ID
   */
  async cancelMultiVehicleBooking(
    bookingIds: string[], 
    userId: string
  ) {
    const cancellationPromises = bookingIds.map(async (bookingId) => {
      return this.bookingService.cancelBooking(bookingId, userId);
    });

    return Promise.all(cancellationPromises);
  }

  /**
   * Find recommended vehicle combinations
   * @param startDate Start date
   * @param endDate End date
   * @param groupSize Number of people
   */
  async findRecommendedVehicleCombinations(
    startDate: Date, 
    endDate: Date, 
    groupSize: number
  ) {
    // Find vehicles that can accommodate the group
    const availableVehicles = await this.prisma.vehicle.findMany();

    // Check availability for each vehicle
    const availabilityChecks = await Promise.all(
      availableVehicles.map(async (vehicle) => {
        try {
          await this.bookingService.checkVehicleAvailability(
            vehicle.id, 
            startDate, 
            endDate
          );
          return vehicle;
        } catch {
          return null;
        }
      })
    );

    // Filter out unavailable vehicles
    const fullyAvailableVehicles = availabilityChecks.filter(v => v !== null);

    // Generate vehicle combinations
    const vehicleCombinations = this.generateVehicleCombinations(
      fullyAvailableVehicles, 
      groupSize
    );

    return vehicleCombinations;
  }

  /**
   * Generate vehicle combinations for a group
   * @param vehicles Available vehicles
   * @param groupSize Group size
   */
  private generateVehicleCombinations(
    vehicles: any, 
    groupSize: number
  ): Promise<any>[] {
    const combinations: any[] = [];

    // Recursive combination generator
    const generateCombos = (
      currentCombo: any, 
      remainingVehicles: any, 
      remainingCapacity: number
    ) => {
      // If group size is satisfied, add combination
      if (remainingCapacity <= 0) {
        combinations.push(currentCombo);
        return;
      }

      // Try adding each remaining vehicle
      for (let i = 0; i < remainingVehicles.length; i++) {
        const vehicle = remainingVehicles[i];
        const newRemainingCapacity = remainingCapacity - vehicle.capacity;
        
        if (newRemainingCapacity >= 0) {
          generateCombos(
            [...currentCombo, vehicle], 
            remainingVehicles.slice(i + 1), 
            newRemainingCapacity
          );
        }
      }
    };

    generateCombos([], vehicles, groupSize);

    return combinations;
  }
}

export default new MultiVehicleBookingService(); 