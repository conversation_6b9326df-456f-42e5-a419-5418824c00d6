const BaseSupabaseService = require('./BaseSupabaseService');
const { supabase } = require('../utils/supabaseClient');

class BookingService extends BaseSupabaseService {
  constructor() {
    super('bookings');
  }

  // Create a new booking with availability check
  async createBooking(bookingData) {
    try {
      // Validate required booking fields
      const requiredFields = [
        'user_id', 
        'vehicle_id', 
        'start_date', 
        'end_date', 
        'total_price'
      ];

      requiredFields.forEach(field => {
        if (!bookingData[field]) {
          throw new Error(`${field} is required`);
        }
      });

      // Check vehicle availability
      const availabilityCheck = await this.checkVehicleAvailability(
        bookingData.vehicle_id, 
        bookingData.start_date, 
        bookingData.end_date
      );

      if (!availabilityCheck.isAvailable) {
        throw new Error('Vehicle is not available for the selected dates');
      }

      // Create booking
      const { data, error } = await this.client
        .from('bookings')
        .insert(bookingData)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Create booking error:', error);
      throw error;
    }
  }

  // Check vehicle availability
  async checkVehicleAvailability(vehicleId, startDate, endDate) {
    try {
      const { data: conflictingBookings, error } = await this.client
        .from('bookings')
        .select('*')
        .eq('vehicle_id', vehicleId)
        .or(
          `start_date.lte.${endDate},end_date.gte.${startDate}` +
          `,start_date.lte.${startDate},end_date.gte.${endDate}` +
          `,start_date.gte.${startDate},end_date.lte.${endDate}`
        );

      if (error) throw error;

      return {
        isAvailable: conflictingBookings.length === 0,
        conflictingBookings
      };
    } catch (error) {
      console.error('Availability check error:', error);
      throw error;
    }
  }

  // Get user's booking history
  async getUserBookings(userId, status = 'all') {
    try {
      let query = this.client
        .from('bookings')
        .select('*, vehicle:vehicles(*)')
        .eq('user_id', userId);

      if (status !== 'all') {
        query = query.eq('status', status);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Get user bookings error:', error);
      throw error;
    }
  }

  // Cancel a booking
  async cancelBooking(bookingId, userId) {
    try {
      // First, verify the booking belongs to the user
      const { data: existingBooking, error: fetchError } = await this.client
        .from('bookings')
        .select('*')
        .eq('id', bookingId)
        .eq('user_id', userId)
        .single();

      if (fetchError) throw fetchError;
      if (!existingBooking) {
        throw new Error('Booking not found or not authorized');
      }

      // Update booking status to cancelled
      const { data, error } = await this.client
        .from('bookings')
        .update({ status: 'cancelled' })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Cancel booking error:', error);
      throw error;
    }
  }
}

module.exports = BookingService;
