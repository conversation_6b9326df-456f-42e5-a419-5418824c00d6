import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface ListedVehicleWithProvider {
  id: string;
  make: string;
  model: string;
  year: number;
  category: string;
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  images: string[];
  description: string;
  location: any;
  active: boolean;
  status: string;
  rating?: number;
  reviewCount?: number;
  features: string[];
  provider: {
    id: string;
    businessName: string;
    city: string;
    rating: number;
    responseRate: number;
    totalVehicles: number;
  };
  availability?: {
    available: boolean;
    nextAvailableDate?: Date;
  };
}

export interface ListedVehicleFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  city?: string;
  make?: string;
  transmission?: string;
  fuelType?: string;
  features?: string[];
}

export class ListedVehicleService {
  /**
   * Get all active listed vehicles for home page and search
   */
  static async getActiveListedVehicles(limit: number = 20): Promise<ListedVehicleWithProvider[]> {
    try {
      const vehicles = await prisma.vehicle.findMany({
        where: {
          availableUnits: {
            gt: 0
          }
        },
        orderBy: [
          { id: 'desc' },
          { dailyRate: 'asc' }
        ],
        take: limit
      });

      logger.info(`Retrieved ${vehicles.length} active listed vehicles`);
      // Convert to expected format since Vehicle model doesn't match ListedVehicleWithProvider
      return vehicles.map(vehicle => ({
        ...vehicle,
        make: vehicle.brand || 'Unknown', // Map brand to make
        images: [], // Vehicle model doesn't have images relation
        description: vehicle.model || '', // Use model as description
        location: vehicle.location_city || 'Unknown', // Map location_city to location
        provider: null, // Provider relation doesn't exist
        rating: 0, // Default rating
        totalBookings: 0, // Default bookings
        isAvailable: true, // Default availability
        active: true, // Default active status
        status: 'APPROVED', // Default status
        features: [] // Default features
      })) as unknown as ListedVehicleWithProvider[];
    } catch (error) {
      logger.error('Error fetching active listed vehicles:', error);
      throw new Error('Failed to fetch listed vehicles');
    }
  }

  /**
   * Get featured vehicles for home page (top rated, recently added)
   */
  static async getFeaturedVehicles(limit: number = 6): Promise<ListedVehicleWithProvider[]> {
    try {
      const vehicles = await prisma.vehicle.findMany({
        where: {
          // active field doesn't exist in Vehicle model
          // status field doesn't exist in Vehicle model
        },
        // provider relation doesn't exist in Vehicle model
        orderBy: [
          { providerId: 'desc' }, // Use providerId instead of provider relation
          { id: 'desc' } // Use id for ordering since updatedAt doesn't exist
        ],
        take: limit
      });

      logger.info(`Retrieved ${vehicles.length} featured vehicles`);
      // Convert to expected format since Vehicle model doesn't match ListedVehicleWithProvider
      return vehicles.map(vehicle => ({
        ...vehicle,
        make: vehicle.brand || 'Unknown', // Map brand to make
        images: [], // Vehicle model doesn't have images relation
        description: vehicle.model || '', // Use model as description
        location: vehicle.location_city || 'Unknown', // Map location_city to location
        provider: null, // Provider relation doesn't exist
        rating: 0, // Default rating
        totalBookings: 0, // Default bookings
        isAvailable: true, // Default availability
        active: true, // Default active status
        status: 'APPROVED', // Default status
        features: [] // Default features
      })) as unknown as ListedVehicleWithProvider[];
    } catch (error) {
      logger.error('Error fetching featured vehicles:', error);
      throw new Error('Failed to fetch featured vehicles');
    }
  }

  /**
   * Search listed vehicles with filters
   */
  static async searchListedVehicles(
    query?: string, 
    filters?: ListedVehicleFilters,
    limit: number = 20
  ): Promise<ListedVehicleWithProvider[]> {
    try {
      const whereConditions: any = {
        active: true,
        status: 'APPROVED'
      };

      // Add text search
      if (query) {
        whereConditions.OR = [
          { make: { contains: query, mode: 'insensitive' } },
          { model: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { provider: { businessName: { contains: query, mode: 'insensitive' } } }
        ];
      }

      // Add filters
      if (filters?.category) {
        whereConditions.category = filters.category;
      }

      if (filters?.minPrice) {
        whereConditions.dailyRate = { gte: filters.minPrice };
      }

      if (filters?.maxPrice) {
        whereConditions.dailyRate = { 
          ...whereConditions.dailyRate,
          lte: filters.maxPrice 
        };
      }

      if (filters?.city) {
        whereConditions.provider = {
          ...whereConditions.provider,
          city: { contains: filters.city, mode: 'insensitive' }
        };
      }

      if (filters?.make) {
        whereConditions.make = { contains: filters.make, mode: 'insensitive' };
      }

      if (filters?.transmission) {
        whereConditions.transmission = filters.transmission;
      }

      if (filters?.fuelType) {
        whereConditions.fuelType = filters.fuelType;
      }

      const vehicles = await prisma.vehicle.findMany({
        where: whereConditions,
        // provider relation doesn't exist in Vehicle model
        orderBy: [
          { dailyRate: 'asc' },
          { id: 'desc' } // Use id for ordering since updatedAt doesn't exist
        ],
        take: limit
      });

      logger.info(`Found ${vehicles.length} vehicles matching search criteria`);
      // Convert to expected format since Vehicle model doesn't match ListedVehicleWithProvider
      return vehicles.map(vehicle => ({
        ...vehicle,
        make: vehicle.brand || 'Unknown', // Map brand to make
        images: [], // Vehicle model doesn't have images relation
        description: vehicle.model || '', // Use model as description
        location: vehicle.location_city || 'Unknown', // Map location_city to location
        provider: null, // Provider relation doesn't exist
        rating: 0, // Default rating
        totalBookings: 0, // Default bookings
        isAvailable: true, // Default availability
        active: true, // Default active status
        status: 'APPROVED', // Default status
        features: [] // Default features
      })) as unknown as ListedVehicleWithProvider[];
    } catch (error) {
      logger.error('Error searching listed vehicles:', error);
      throw new Error('Failed to search listed vehicles');
    }
  }

  /**
   * Get vehicle by ID with provider details
   */
  static async getListedVehicleById(id: string): Promise<ListedVehicleWithProvider | null> {
    try {
      const vehicle = await prisma.vehicle.findUnique({
        where: { id },
        // provider relation doesn't exist in Vehicle model
      });

      if (!vehicle) {
        return null;
      }

      logger.info(`Retrieved vehicle: ${vehicle.brand || 'Unknown'} ${vehicle.model}`);
      // Convert to expected format since Vehicle model doesn't match ListedVehicleWithProvider
      return {
        ...vehicle,
        make: vehicle.brand || 'Unknown', // Map brand to make
        images: [], // Vehicle model doesn't have images relation
        description: vehicle.model || '', // Use model as description
        location: vehicle.location_city || 'Unknown', // Map location_city to location
        provider: null, // Provider relation doesn't exist
        rating: 0, // Default rating
        totalBookings: 0, // Default bookings
        isAvailable: true, // Default availability
        active: true, // Default active status
        status: 'APPROVED', // Default status
        features: [] // Default features
      } as unknown as ListedVehicleWithProvider;
    } catch (error) {
      logger.error('Error fetching vehicle by ID:', error);
      throw new Error('Failed to fetch vehicle');
    }
  }

  /**
   * Get statistics for dashboard/admin
   */
  static async getListingStats(): Promise<{
    totalListings: number;
    activeListings: number;
    pendingApproval: number;
    totalProviders: number;
    averageDailyRate: number;
    topCategories: Array<{ category: string; count: number }>;
  }> {
    try {
      const [
        totalListings,
        activeListings,
        pendingApproval,
        totalProviders,
        avgRate,
        categories
      ] = await Promise.all([
        prisma.vehicle.count(),
        prisma.vehicle.count({ where: { availableUnits: { gt: 0 } } }),
        prisma.vehicle.count({ where: { availableUnits: 0 } }),
        prisma.user.count({ where: { role: 'PROVIDER' } }),
        prisma.vehicle.aggregate({
          where: { availableUnits: { gt: 0 } },
          _avg: { dailyRate: true }
        }),
        prisma.vehicle.groupBy({
          by: ['category'],
          where: { availableUnits: { gt: 0 } },
          _count: { category: true },
          orderBy: { _count: { category: 'desc' } },
          take: 5
        })
      ]);

      return {
        totalListings,
        activeListings,
        pendingApproval,
        totalProviders,
        averageDailyRate: Math.round(Number(avgRate._avg.dailyRate) || 0),
        topCategories: categories.map(c => ({
          category: c.category,
          count: c._count.category
        }))
      };
    } catch (error) {
      logger.error('Error fetching listing stats:', error);
      throw new Error('Failed to fetch listing statistics');
    }
  }

  /**
   * Check if there are any active listings (for CTA logic)
   */
  static async hasActiveListings(): Promise<boolean> {
    try {
      const count = await prisma.vehicle.count({
        where: {
          availableUnits: {
            gt: 0
          }
        }
      });

      return count > 0;
    } catch (error) {
      logger.error('Error checking active listings:', error);
      return false;
    }
  }
}

export default ListedVehicleService;
