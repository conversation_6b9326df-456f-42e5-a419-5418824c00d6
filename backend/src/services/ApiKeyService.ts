import { PrismaClient } from '@prisma/client';

export class ApiKeyService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get API key by name from database
   * @param keyName Name of the API key
   * @returns API key value
   */
  async getApiKey(keyName: string): Promise<string | null> {
    try {
      const apiKey = await this.prisma.apiKey.findFirst({
        where: { 
          keyName: keyName,
          isActive: true
        }
      });

      return apiKey?.keyValue || null;
    } catch (error) {
      console.error(`Error getting API key ${keyName}:`, error);
      return null;
    }
  }

  /**
   * Get Stripe secret key from database
   * @returns Stripe secret key
   */
  async getStripeSecretKey(): Promise<string | null> {
    return this.getApiKey('STRIPE_SECRET_KEY');
  }

  /**
   * Get Stripe webhook secret from database
   * @returns Stripe webhook secret
   */
  async getStripeWebhookSecret(): Promise<string | null> {
    return this.getApiKey('STRIPE_WEBHOOK_SECRET');
  }

  /**
   * Create or update API key
   * @param keyName Name of the API key
   * @param keyValue Value of the API key
   * @param permissions Optional permissions array
   */
  async upsertApiKey(keyName: string, keyValue: string, permissions: string[] = []): Promise<void> {
    try {
      await this.prisma.apiKey.upsert({
        where: { keyValue: keyValue },
        update: {
          keyValue: keyValue,
          permissions: permissions,
          isActive: true,
          updatedAt: new Date()
        },
        create: {
          keyName: keyName,
          keyValue: keyValue,
          permissions: permissions,
          isActive: true
        }
      });
    } catch (error) {
      console.error(`Error upserting API key ${keyName}:`, error);
      throw error;
    }
  }

  /**
   * Deactivate API key
   * @param keyName Name of the API key
   */
  async deactivateApiKey(keyName: string): Promise<void> {
    try {
      await this.prisma.apiKey.updateMany({
        where: { keyName: keyName },
        data: { isActive: false }
      });
    } catch (error) {
      console.error(`Error deactivating API key ${keyName}:`, error);
      throw error;
    }
  }
}

// Export default instance for convenience
export default new ApiKeyService(); 