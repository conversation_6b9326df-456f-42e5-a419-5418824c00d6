import { PrismaClient } from '@prisma/client'
import PDFDocument from 'pdfkit'
import { format } from 'date-fns'
import fs from 'fs'
import path from 'path'
import nodemailer from 'nodemailer'

export class ReportService {
  private prisma: PrismaClient
  private transporter: nodemailer.Transporter

  constructor() {
    this.prisma = new PrismaClient()
    this.transporter = nodemailer.createTransport({
      // Configure your email service
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    })
  }

  // Generate PDF Report
  async generatePDFReport(
    reportType: 'platform' | 'owner', 
    userId?: string,
    options?: { 
      startDate?: Date, 
      endDate?: Date 
    }
  ): Promise<string> {
    const doc = new PDFDocument()
    const reportPath = path.join(
      process.cwd(), 
      'reports', 
      `${reportType}_report_${Date.now()}.pdf`
    )

    // Ensure reports directory exists
    if (!fs.existsSync(path.dirname(reportPath))) {
      fs.mkdirSync(path.dirname(reportPath), { recursive: true })
    }

    const writeStream = fs.createWriteStream(reportPath)
    doc.pipe(writeStream)

    // Report Header
    doc.fontSize(20).text(`RentaHub ${reportType.toUpperCase()} Report`, { align: 'center' })
    doc.moveDown()

    // Fetch Report Data
    let reportData
    if (reportType === 'platform') {
      reportData = await this.getPlatformReportData(options)
    } else {
      reportData = await this.getOwnerReportData(userId, options)
    }

    // Populate PDF with data
    doc.fontSize(12)
    Object.entries(reportData).forEach(([key, value]) => {
      doc.text(`${key}: ${value}`)
    })

    doc.end()

    return new Promise((resolve, reject) => {
      writeStream.on('finish', () => resolve(reportPath))
      writeStream.on('error', reject)
    })
  }

  // Generate CSV Report
  async generateCSVReport(
    reportType: 'platform' | 'owner', 
    userId?: string,
    options?: { 
      startDate?: Date, 
      endDate?: Date 
    }
  ): Promise<string> {
    const reportPath = path.join(
      process.cwd(), 
      'reports', 
      `${reportType}_report_${Date.now()}.csv`
    )

    // Ensure reports directory exists
    if (!fs.existsSync(path.dirname(reportPath))) {
      fs.mkdirSync(path.dirname(reportPath), { recursive: true })
    }

    // Fetch Report Data
    let reportData
    if (reportType === 'platform') {
      reportData = await this.getPlatformReportData(options)
    } else {
      reportData = await this.getOwnerReportData(userId, options)
    }

    // Convert data to CSV
    const csvContent = this.convertToCSV(reportData)
    
    fs.writeFileSync(reportPath, csvContent)
    return reportPath
  }

  // Email Report
  async emailReport(
    reportPath: string, 
    recipientEmail: string, 
    reportType: string
  ) {
    try {
      await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: recipientEmail,
        subject: `RentaHub ${reportType.toUpperCase()} Report`,
        text: 'Please find attached your RentaHub report.',
        attachments: [{ 
          filename: path.basename(reportPath),
          path: reportPath 
        }]
      })
    } catch (error) {
      console.error('Failed to email report:', error)
    }
  }

  // Scheduled Report Generation
  async generateScheduledReports() {
    const scheduledReports = await this.prisma.scheduledReport.findMany({
      where: { 
        nextRunAt: { lte: new Date() } 
      }
    })

    for (const report of scheduledReports) {
      try {
        // Generate report based on configuration
        const config = report.reportConfig as any;
        const reportPath = await this.generatePDFReport(
          config.type, 
          config.userId
        )

        // Email to recipients
        for (const recipient of report.recipients) {
          await this.emailReport(reportPath, recipient, report.name)
        }

        // Update next run time
        await this.updateReportSchedule(report.id)
      } catch (error) {
        console.error(`Failed to generate report ${report.id}:`, error)
      }
    }
  }

  // Helper methods for data retrieval
  private async getPlatformReportData(options?: { 
    startDate?: Date, 
    endDate?: Date 
  }) {
    const startDate = options?.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const endDate = options?.endDate || new Date()

    const totalBookings = await this.prisma.booking.count({
      where: { 
        createdAt: { 
          gte: startDate, 
          lte: endDate 
        } 
      }
    })

    const totalRevenue = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: { 
        createdAt: { 
          gte: startDate, 
          lte: endDate 
        } 
      }
    })

    return {
      'Report Period': `${format(startDate, 'PP')} - ${format(endDate, 'PP')}`,
      'Total Bookings': totalBookings,
      'Total Revenue': totalRevenue._sum.totalPrice?.toFixed(2) || '0'
    }
  }

  private async getOwnerReportData(
    userId?: string, 
    options?: { 
      startDate?: Date, 
      endDate?: Date 
    }
  ) {
    if (!userId) throw new Error('User ID is required')

    const startDate = options?.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const endDate = options?.endDate || new Date()

    const ownerBookings = await this.prisma.booking.findMany({
      where: { 
        vehicle: { providerId: userId },
        createdAt: { 
          gte: startDate, 
          lte: endDate 
        } 
      },
      include: { vehicle: true }
    })

    return {
      'Report Period': `${format(startDate, 'PP')} - ${format(endDate, 'PP')}`,
      'Total Bookings': ownerBookings.length,
      'Total Revenue': ownerBookings.reduce((sum, booking) => sum + Number(booking.totalPrice), 0).toFixed(2),
      'Top Vehicle': this.getTopVehicle(ownerBookings)
    }
  }

  private getTopVehicle(bookings: any) {
    const vehicleBookings = bookings.reduce((acc, booking) => {
      acc[booking.vehicle.name] = (acc[booking.vehicle.name] || 0) + 1
      return acc
    }, {})

    return Object.entries(vehicleBookings)
      .sort((a: any, b: any) => b[1] - a[1])[0]?.[0] || 'No bookings'
  }

  private updateReportSchedule(reportId: string) {
    // Logic to update next run time based on frequency
    return this.prisma.scheduledReport.update({
      where: { id: reportId },
      data: { 
        lastRunAt: new Date(),
        nextRunAt: this.calculateNextRunTime() 
      }
    })
  }

  private calculateNextRunTime(frequency: string = 'monthly'): Date {
    const now = new Date()
    switch (frequency) {
      case 'daily':
        return new Date(now.setDate(now.getDate() + 1))
      case 'weekly':
        return new Date(now.setDate(now.getDate() + 7))
      case 'monthly':
      default:
        return new Date(now.setMonth(now.getMonth() + 1))
    }
  }

  private convertToCSV(data: Record<string, any>): string {
    const headers = Object.keys(data)
    const values = Object.values(data)

    return [
      headers.join(','),
      values.map(val => `"${val}"`).join(',')
    ].join('\n')
  }
}

export default new ReportService() 