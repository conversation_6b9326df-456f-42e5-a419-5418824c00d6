import { PrismaClient } from '@prisma/client';
import { InsuranceConfiguration } from './InsuranceDepositService';

export interface HistoricalDataPoint {
  vehicleId: string;
  bookingId: string;
  coverageAmount: number;
  additionalCoverageCount: number;
  vehicleAge: number;
  claimAmount: number;
  claimStatus: string;
  riskScore: number;
  createdAt: Date;
}

export class HistoricalDataService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async collectHistoricalData(): Promise<HistoricalDataPoint[]> {
    // Collect data from bookings, insurance configurations, and insurance claim reports
    const bookings = await this.prisma.booking.findMany({
      include: {
        vehicle: true,
        user: true
      }
    });

    // Since insurance models don't exist, use placeholder data
    const historicalData: HistoricalDataPoint[] = bookings.map(booking => {
      return {
        vehicleId: booking.vehicleId,
        bookingId: booking.id,
        coverageAmount: 0, // Placeholder
        additionalCoverageCount: 0, // Placeholder
        vehicleAge: 2, // Placeholder
        claimAmount: 0, // Placeholder
        claimStatus: 'NO_CLAIM', // Placeholder
        riskScore: 0, // Placeholder since insuranceConfig doesn't exist
        createdAt: booking.createdAt
      };
    });

    return historicalData;
  }

  async saveTrainingDataset(dataPoints: HistoricalDataPoint[]) {
    // Save processed historical data for model training
    // Since historicalRiskDataset doesn't exist, log the data instead
    console.log('Training dataset would be saved:', dataPoints.length, 'data points');
    
    // Or you could save to a file or external service
    // await this.saveToFile(dataPoints);
  }

  async preprocessDataForTraining(): Promise<any[]> {
    const historicalData = await this.collectHistoricalData();
    
    // Basic preprocessing
    const processedData = historicalData.map(data => ({
      // Normalize features
      coverageAmount: data.coverageAmount / 10000,
      additionalCoverageCount: data.additionalCoverageCount / 5,
      vehicleAge: data.vehicleAge / 20, // Normalize vehicle age
      claimAmount: data.claimAmount / 5000,
      // Convert claim status to numeric
      claimStatus: this.mapClaimStatusToNumeric(data.claimStatus),
      // Actual risk score as target variable
      actualRiskScore: this.calculateActualRiskScore(data)
    }));

    await this.saveTrainingDataset(historicalData);

    return processedData;
  }

  private mapClaimStatusToNumeric(status: string): number {
    switch (status) {
      case 'APPROVED': return 1;
      case 'PENDING': return 0.5;
      case 'REJECTED': return 0;
      default: return 0;
    }
  }

  private calculateActualRiskScore(data: HistoricalDataPoint): number {
    // Complex risk score calculation
    const claimImpact = data.claimAmount / 5000; // Normalize claim amount
    const coverageImpact = data.coverageAmount / 10000;
    const ageImpact = 1 - (data.vehicleAge / 20); // Younger vehicles less risky
    const additionalCoverageImpact = data.additionalCoverageCount / 5;

    // Weighted calculation
    return (
      (claimImpact * 0.4) + 
      (coverageImpact * 0.3) + 
      (ageImpact * 0.2) + 
      (additionalCoverageImpact * 0.1)
    );
  }
}

export default HistoricalDataService;