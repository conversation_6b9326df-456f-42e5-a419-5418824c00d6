import { PrismaClient } from '@prisma/client';

export interface CategoryPriceRange {
  min: number;
  max: number;
  count: number;
  avgPrice: number;
}

export interface PriceRangesResponse {
  small_scooter: CategoryPriceRange;
  large_scooter: CategoryPriceRange;
  luxury_bike: CategoryPriceRange;
}

export class PriceRangeService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get dynamic price ranges by vehicle category from database
   */
  async getPriceRangesByCategory(): Promise<PriceRangesResponse> {
    try {
      console.log('📊 Calculating dynamic price ranges from database...');

      // Query actual vehicle data by category
      const [smallScooters, largeScooters, luxuryBikes] = await Promise.all([
        this.getCategoryStats('small_scooter'),
        this.getCategoryStats('large_scooter'), 
        this.getCategoryStats('luxury_bike')
      ]);

      const result = {
        small_scooter: smallScooters,
        large_scooter: largeScooters,
        luxury_bike: luxuryBikes
      };

      console.log('✅ Price ranges calculated:', result);
      return result;

    } catch (error) {
      console.error('❌ Error calculating price ranges:', error);
      
      // Fallback to reasonable defaults
      return this.getFallbackPriceRanges();
    }
  }

  /**
   * Get price statistics for a specific category
   */
  private async getCategoryStats(category: string): Promise<CategoryPriceRange> {
    try {
      // Query vehicles in this category
      const vehicles = await this.prisma.vehicle.findMany({
        where: {
          category: category,
          dailyRate: { gt: 0 }
        },
        select: {
          dailyRate: true
        }
      });

      if (vehicles.length === 0) {
        return this.getFallbackCategoryRange(category);
      }

      // Calculate statistics
      const rates = vehicles.map(v => Number(v.dailyRate));
      const min = Math.min(...rates);
      const max = Math.max(...rates);
      const avgPrice = Math.round(rates.reduce((sum, rate) => sum + rate, 0) / rates.length);

      return {
        min,
        max,
        count: vehicles.length,
        avgPrice
      };

    } catch (error) {
      console.error(`❌ Error getting stats for category ${category}:`, error);
      return this.getFallbackCategoryRange(category);
    }
  }

  /**
   * Apply dynamic pricing adjustments based on market conditions
   */
  async applyDynamicPricing(baseRanges: PriceRangesResponse): Promise<PriceRangesResponse> {
    const now = new Date();
    const hourOfDay = now.getHours();
    const dayOfWeek = now.getDay();
    
    // Calculate demand multipliers
    const peakMultiplier = this.getPeakHourMultiplier(hourOfDay);
    const weekendMultiplier = this.getWeekendMultiplier(dayOfWeek);
    const seasonalMultiplier = await this.getSeasonalMultiplier();
    
    const totalMultiplier = peakMultiplier * weekendMultiplier * seasonalMultiplier;

    console.log('📈 Applying dynamic pricing:', {
      peakMultiplier,
      weekendMultiplier,
      seasonalMultiplier,
      totalMultiplier,
      timeOfDay: `${hourOfDay}:00`,
      dayOfWeek: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek]
    });

    // Apply multiplier to all categories
    const adjustedRanges: PriceRangesResponse = {} as PriceRangesResponse;
    
    for (const [category, range] of Object.entries(baseRanges)) {
      adjustedRanges[category as keyof PriceRangesResponse] = {
        min: Math.round(range.min * totalMultiplier),
        max: Math.round(range.max * totalMultiplier),
        count: range.count,
        avgPrice: Math.round(range.avgPrice * totalMultiplier)
      };
    }

    return adjustedRanges;
  }

  /**
   * Get peak hour pricing multiplier
   */
  private getPeakHourMultiplier(hour: number): number {
    // Morning rush: 7-9 AM
    if (hour >= 7 && hour <= 9) return 1.2;
    
    // Evening rush: 5-7 PM  
    if (hour >= 17 && hour <= 19) return 1.2;
    
    // Late night: 10 PM - 6 AM
    if (hour >= 22 || hour <= 6) return 0.9;
    
    return 1.0;
  }

  /**
   * Get weekend pricing multiplier
   */
  private getWeekendMultiplier(dayOfWeek: number): number {
    // Weekend (Saturday = 6, Sunday = 0)
    if (dayOfWeek === 0 || dayOfWeek === 6) return 1.15;
    
    // Friday
    if (dayOfWeek === 5) return 1.05;
    
    return 1.0;
  }

  /**
   * Get seasonal pricing multiplier
   */
  private async getSeasonalMultiplier(): Promise<number> {
    const month = new Date().getMonth();
    
    // High season (tourist months in Indonesia)
    if ([5, 6, 7, 11].includes(month)) return 1.1; // Jun, Jul, Aug, Dec
    
    // Low season
    if ([1, 2, 9, 10].includes(month)) return 0.95; // Feb, Mar, Oct, Nov
    
    return 1.0;
  }

  /**
   * Get fallback price ranges when database is unavailable
   */
  private getFallbackPriceRanges(): PriceRangesResponse {
    return {
      small_scooter: { min: 50000, max: 80000, count: 0, avgPrice: 65000 },
      large_scooter: { min: 80000, max: 120000, count: 0, avgPrice: 100000 },
      luxury_bike: { min: 200000, max: 350000, count: 0, avgPrice: 275000 }
    };
  }

  /**
   * Get fallback range for specific category
   */
  private getFallbackCategoryRange(category: string): CategoryPriceRange {
    const fallbacks = {
      small_scooter: { min: 50000, max: 80000, count: 0, avgPrice: 65000 },
      large_scooter: { min: 80000, max: 120000, count: 0, avgPrice: 100000 },
      luxury_bike: { min: 200000, max: 350000, count: 0, avgPrice: 275000 }
    };

    return fallbacks[category as keyof typeof fallbacks] || fallbacks.small_scooter;
  }

  /**
   * Get market conditions description
   */
  getMarketConditions(multiplier: number): string {
    if (multiplier >= 1.2) return 'Very High Demand';
    if (multiplier >= 1.1) return 'High Demand';
    if (multiplier >= 1.05) return 'Moderate Demand';
    if (multiplier >= 0.95) return 'Normal';
    return 'Low Demand';
  }
}

export default new PriceRangeService();
