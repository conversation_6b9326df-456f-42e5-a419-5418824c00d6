import { PrismaClient } from '@prisma/client';
import { subDays, addDays } from 'date-fns';

// Define enums locally since they don't exist in Prisma
export enum PricingStrategy {
  FIXED = 'FIXED',
  DYNAMIC = 'DYNAMIC',
  SEASONAL = 'SEASONAL'
}

export enum VehicleAvailabilityStatus {
  AVAILABLE = 'AVAILABLE',
  BOOKED = 'BOOKED',
  MAINTENANCE = 'MAINTENANCE',
  UNAVAILABLE = 'UNAVAILABLE'
}

export class FleetManagementService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  // Predefined smart tags for vehicle discovery
  private PREDEFINED_SMART_TAGS = [
    'Beginner-Friendly',
    'Great for Couples',
    'Adventure-Ready',
    'Lightweight',
    'Long-Term Friendly',
    'Luxury Experience',
    'Fuel Efficient',
    'Easy City Navigation',
    'Good for Bali Roads'
  ];

  /**
   * Get predefined smart tags
   */
  async getPredefinedTags(): Promise<string[]> {
    return this.PREDEFINED_SMART_TAGS;
  }

  /**
   * Add tags to a vehicle
   * @param vehicleId Vehicle identifier
   * @param tags Array of tags to add
   */
  async addTagsToVehicle(vehicleId: string, tags: string[]): Promise<void> {
    // Validate tags
    const invalidTags = tags.filter(tag => !this.PREDEFINED_SMART_TAGS.includes(tag));
    if (invalidTags.length > 0) {
      throw new Error(`Invalid tags: ${invalidTags.join(', ')}`);
    }

    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        smartTags: tags
      }
    });
  }

  /**
   * Remove tags from a vehicle
   * @param vehicleId Vehicle identifier
   * @param tags Array of tags to remove
   */
  async removeTagsFromVehicle(vehicleId: string, tags: string[]): Promise<void> {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      select: { smartTags: true }
    });

    if (!vehicle) {
      throw new Error('Vehicle not found');
    }

    const updatedTags = vehicle.smartTags.filter(tag => !tags.includes(tag));

    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        smartTags: updatedTags
      }
    });
  }

  /**
   * Get vehicles by tags
   * @param tags Array of tags to filter by
   */
  async getVehiclesByTags(tags: string[]): Promise<any[]> {
    return this.prisma.vehicle.findMany({
      where: {
        smartTags: {
          hasSome: tags
        }
      },
      include: {
        images: true
      }
    });
  }

  /**
   * Update vehicle pricing strategy
   * @param vehicleId Vehicle identifier
   * @param strategy Pricing strategy
   * @param minRate Minimum daily rate
   * @param maxRate Maximum daily rate
   */
  async updatePricingStrategy(
    vehicleId: string, 
    strategy: PricingStrategy, 
    minRate?: number, 
    maxRate?: number
  ): Promise<void> {
    // Update vehicle with available fields only
    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        dailyRate: minRate || undefined,
        weeklyRate: minRate ? minRate * 6 : undefined,
        monthlyRate: minRate ? minRate * 25 : undefined,
        // Store pricing strategy in addOnPrices JSON field
        addOnPrices: {
          pricingStrategy: strategy,
          minDailyRate: minRate,
          maxDailyRate: maxRate
        }
      }
    });
  }

  /**
   * Block vehicle availability for maintenance
   * @param vehicleId Vehicle identifier
   * @param startDate Maintenance start date
   * @param endDate Maintenance end date
   * @param reason Reason for maintenance
   */
  async blockVehicleForMaintenance(
    vehicleId: string, 
    startDate: Date, 
    endDate: Date, 
    reason?: string
  ): Promise<void> {
    // Update vehicle status and add maintenance info
    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        // Store maintenance info in addOnPrices JSON field
        addOnPrices: {
          maintenance: {
            vehicleId,
            maintenanceType: 'ROUTINE',
            scheduledDate: startDate,
            completionDate: endDate,
            description: reason,
            status: 'SCHEDULED'
          }
        }
      }
    });
  }

  /**
   * Generate a range of dates between start and end dates
   * @param startDate Start date
   * @param endDate End date
   */
  private generateDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    let currentDate = startDate;

    while (currentDate <= endDate) {
      dates.push(currentDate);
      currentDate = addDays(currentDate, 1);
    }

    return dates;
  }

  /**
   * Calculate vehicle performance metrics
   * @param vehicleId Vehicle identifier
   * @param startDate Start date for metrics
   * @param endDate End date for metrics
   */
  async calculateVehiclePerformance(
    vehicleId: string, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<any> {
    // Default to last 30 days if no dates provided
    const defaultStartDate = subDays(new Date(), 30);
    const defaultEndDate = new Date();

    const bookings = await this.prisma.booking.findMany({
      where: {
        vehicleId,
        startDate: {
          gte: startDate || defaultStartDate,
          lte: endDate || defaultEndDate
        },
        status: 'CONFIRMED'
      }
    });

    // Calculate metrics
    const totalBookings = bookings.length;
    const totalRevenue = bookings.reduce((sum, booking) => sum + Number(booking.totalPrice), 0);
    const averageBookingDuration = bookings.reduce((sum, booking) => {
      const duration = (new Date(booking.endDate).getTime() - new Date(booking.startDate).getTime()) / (1000 * 3600 * 24);
      return sum + duration;
    }, 0) / totalBookings;

    // Calculate utilization rate
    const totalDays = (new Date(endDate || defaultEndDate).getTime() - new Date(startDate || defaultStartDate).getTime()) / (1000 * 3600 * 24);
    const utilizationRate = (totalBookings * averageBookingDuration) / totalDays * 100;

    // Store performance metric in vehicle's addOnPrices JSON field
    await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        addOnPrices: {
          performanceMetric: {
            vehicleId,
            metricDate: new Date(),
            viewsCount: 0, // This would require tracking page views
            bookingRequests: totalBookings,
            confirmedBookings: totalBookings,
            totalEarnings: totalRevenue,
            ownerEarnings: totalRevenue * 0.85, // 15% RentaHub fee
            utilizationRate,
            averageBookingDuration
          }
        }
      }
    });

    return {
      totalBookings,
      totalRevenue,
      averageBookingDuration,
      utilizationRate
    };
  }

  /**
   * Generate earnings projection for a vehicle
   * @param vehicleId Vehicle identifier
   * @param projectionMonths Number of months to project
   */
  async generateEarningsProjection(
    vehicleId: string, 
    projectionMonths: number = 12
  ): Promise<any> {
    // Get historical performance from vehicle's addOnPrices
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      include: {
        bookings: {
          where: {
            startDate: {
              gte: subDays(new Date(), projectionMonths * 30)
            }
          }
        }
      }
    });

    if (!vehicle) {
      return { error: 'Vehicle not found' };
    }

    // Use bookings for calculations instead of missing performance metrics
    const historicalMetrics = vehicle.bookings;

    if (!historicalMetrics || historicalMetrics.length === 0) {
      return { error: 'No historical data available' };
    }

    // Calculate average monthly earnings
    const averageMonthlyEarnings = historicalMetrics.reduce((sum, booking) => sum + Number(booking.totalPrice), 0) / historicalMetrics.length;

    // Project future earnings
    const projectedEarnings = Array.from({ length: projectionMonths }, (_, i) => ({
      month: addDays(new Date(), i * 30),
      projectedEarnings: averageMonthlyEarnings,
      rentahubFees: averageMonthlyEarnings * 0.15,
      ownerEarnings: averageMonthlyEarnings * 0.85
    }));

    return {
      averageMonthlyEarnings,
      projectedEarnings
    };
  }
}

export default FleetManagementService;
