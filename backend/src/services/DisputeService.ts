import { supabase } from '../utils/supabaseClient';
import { Booking, User } from '../types/database';

export enum DisputeStatus {
  Pending = 'pending',
  InReview = 'in_review',
  Resolved = 'resolved',
  Closed = 'closed'
}

export enum DisputeType {
  Damage = 'damage',
  LateReturn = 'late_return',
  Billing = 'billing',
  Other = 'other'
}

export interface Dispute {
  id: string;
  booking_id: string;
  user_id: string;
  provider_id: string;
  dispute_type: DisputeType;
  status: DisputeStatus;
  description: string;
  evidence_files?: string[];
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
}

export class DisputeService {
  /**
   * Create a new dispute
   * @param bookingId Booking related to the dispute
   * @param userId User filing the dispute
   * @param disputeType Type of dispute
   * @param description Dispute description
   * @param evidenceFiles Optional evidence files
   * @returns Created dispute
   */
  async createDispute(
    bookingId: string,
    userId: string,
    disputeType: DisputeType,
    description: string,
    evidenceFiles?: string[]
  ): Promise<Dispute> {
    // Validate booking exists and is eligible for dispute
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', bookingId)
      .eq('user_id', userId)
      .single();

    if (bookingError || !booking) {
      throw new Error('Invalid booking for dispute');
    }

    // Check dispute eligibility (e.g., within 30 days of booking)
    const bookingDate = new Date(booking.created_at);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    if (bookingDate < thirtyDaysAgo) {
      throw new Error('Dispute can only be filed within 30 days of booking');
    }

    // Create dispute
    const { data, error } = await supabase
      .from('disputes')
      .insert({
        booking_id: bookingId,
        user_id: userId,
        provider_id: booking.provider_id,
        dispute_type: disputeType,
        status: DisputeStatus.Pending,
        description,
        evidence_files: evidenceFiles,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    // Notify provider
    await this.notifyProviderOfDispute(data);

    return data;
  }

  /**
   * Update dispute status
   * @param disputeId Dispute identifier
   * @param newStatus New dispute status
   * @param resolutionNotes Optional resolution notes
   * @returns Updated dispute
   */
  async updateDisputeStatus(
    disputeId: string,
    newStatus: DisputeStatus,
    resolutionNotes?: string
  ): Promise<Dispute> {
    // Validate status transition
    const { data: currentDispute, error: fetchError } = await supabase
      .from('disputes')
      .select('*')
      .eq('id', disputeId)
      .single();

    if (fetchError || !currentDispute) {
      throw new Error('Dispute not found');
    }

    // Complex status transition logic
    const validTransitions = {
      [DisputeStatus.Pending]: [DisputeStatus.InReview],
      [DisputeStatus.InReview]: [DisputeStatus.Resolved, DisputeStatus.Closed]
    };

    if (!validTransitions[currentDispute.status]?.includes(newStatus)) {
      throw new Error('Invalid dispute status transition');
    }

    // Update dispute
    const { data, error } = await supabase
      .from('disputes')
      .update({
        status: newStatus,
        resolution_notes: resolutionNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', disputeId)
      .select()
      .single();

    if (error) throw error;

    // Handle resolution actions
    if (newStatus === DisputeStatus.Resolved) {
      await this.processDisputeResolution(data);
    }

    return data;
  }

  /**
   * Process dispute resolution
   * @param dispute Resolved dispute
   */
  private async processDisputeResolution(dispute: Dispute): Promise<void> {
    // Retrieve booking and calculate potential refund/compensation
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', dispute.booking_id)
      .single();

    if (bookingError || !booking) {
      console.error('Booking not found for dispute resolution');
      return;
    }

    // Calculate compensation based on dispute type
    let compensationAmount = 0;
    switch (dispute.dispute_type) {
      case DisputeType.Damage:
        compensationAmount = booking.total * 0.5; // 50% refund for damages
        break;
      case DisputeType.LateReturn:
        compensationAmount = booking.total * 0.25; // 25% refund for late return
        break;
      case DisputeType.Billing:
        compensationAmount = booking.total; // Full refund for billing issues
        break;
      default:
        compensationAmount = 0;
    }

    // Process refund or credit
    if (compensationAmount > 0) {
      await this.issueCompensation(
        dispute.user_id, 
        compensationAmount, 
        dispute.id
      );
    }
  }

  /**
   * Issue compensation to user
   * @param userId User to compensate
   * @param amount Compensation amount
   * @param disputeId Related dispute ID
   */
  private async issueCompensation(
    userId: string, 
    amount: number, 
    disputeId: string
  ): Promise<void> {
    // In a real-world scenario, integrate with payment system
    // For now, we'll log and create a credit record
    await supabase
      .from('user_credits')
      .insert({
        user_id: userId,
        amount,
        reason: `Dispute resolution (${disputeId})`,
        created_at: new Date().toISOString()
      });
  }

  /**
   * Notify provider about new dispute
   * @param dispute Newly created dispute
   */
  private async notifyProviderOfDispute(dispute: Dispute): Promise<void> {
    // In a real implementation, use email/push notification service
    console.log(`Dispute ${dispute.id} created. Notifying provider.`);
    
    // Create a system message
    await supabase
      .from('messages')
      .insert({
        conversation_id: `dispute_${dispute.id}`,
        sender_id: dispute.user_id,
        recipient_id: dispute.provider_id,
        content: `New dispute filed for booking ${dispute.booking_id}: ${dispute.description}`,
        type: 'system',
        created_at: new Date().toISOString()
      });
  }
}

export default new DisputeService(); 