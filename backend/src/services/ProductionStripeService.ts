// =============================================================================
// PRODUCTION STRIPE SERVICE
// =============================================================================
// Stripe payment processing for production environment

import Stripe from 'stripe';
import DatabaseService from './DatabaseService';
import { Booking, Payment } from '../models';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

class ProductionStripeService {
  private stripe: Stripe;
  private webhookSecret: string;

  constructor() {
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    this.webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';

    if (!stripeSecretKey) {
      console.warn('⚠️ STRIPE_SECRET_KEY not found. Payment processing will be disabled.');
      return;
    }

    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil',
      typescript: true,
    });

    console.log('💳 ProductionStripeService initialized');
  }

  // =============================================================================
  // PAYMENT INTENT OPERATIONS
  // =============================================================================

  async createPaymentIntent(bookingId: string): Promise<{ success: boolean; clientSecret?: string; error?: string }> {
    try {
      if (!this.stripe) {
        return { success: false, error: 'Stripe not configured' };
      }

      // Get booking details using Prisma
      const bookingData = await prisma.booking.findUnique({
        where: { id: bookingId },
        include: { vehicle: true, user: true }
      });

      if (!bookingData) {
        return { success: false, error: 'Booking not found' };
      }

      // Create payment intent
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(Number(bookingData.totalPrice) * 100), // Amount in smallest currency unit (cents)
        currency: 'usd',
        metadata: {
          booking_id: bookingId,
          customer_id: bookingData.userId,
          provider_id: bookingData.providerId,
          vehicle_id: bookingData.vehicleId
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      // Save payment record
      await prisma.payment.create({
        data: {
          bookingId: bookingId,
          providerId: bookingData.providerId,
          amount: Number(bookingData.totalPrice),
          paymentMethod: 'stripe',
          status: 'PENDING'
        }
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret || undefined
      };
    } catch (error) {
      console.error('❌ Create payment intent error:', error);
      return {
        success: false,
        error: 'Failed to create payment intent'
      };
    }
  }

  async confirmPayment(paymentIntentId: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.stripe) {
        return { success: false, error: 'Stripe not configured' };
      }

      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status === 'succeeded') {
        // Update payment status
        await this.updatePaymentStatus(paymentIntentId, 'completed');
        
        // Update booking status
        const bookingId = paymentIntent.metadata.booking_id;
        if (bookingId) {
          await DatabaseService.updateBooking(bookingId, {
            payment_status: 'paid',
            status: 'confirmed'
          });
        }

        return { success: true };
      }

      return { success: false, error: 'Payment not completed' };
    } catch (error) {
      console.error('❌ Confirm payment error:', error);
      return { success: false, error: 'Failed to confirm payment' };
    }
  }

  // =============================================================================
  // WEBHOOK HANDLING
  // =============================================================================

  async handleWebhook(body: string, signature: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.stripe || !this.webhookSecret) {
        return { success: false, error: 'Stripe not configured' };
      }

      const event = this.stripe.webhooks.constructEvent(body, signature, this.webhookSecret);

      console.log('📡 Stripe webhook received:', event.type);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;
        
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
          break;
        
        case 'charge.dispute.created':
          await this.handleChargeDispute(event.data.object as Stripe.Dispute);
          break;
        
        default:
          console.log(`🔔 Unhandled webhook event type: ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      console.error('❌ Webhook handling error:', error);
      return { success: false, error: 'Webhook handling failed' };
    }
  }

  private async handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      console.log('✅ Payment succeeded:', paymentIntent.id);

      // Update payment status
      await this.updatePaymentStatus(paymentIntent.id, 'completed');

      // Update booking status
      const bookingId = paymentIntent.metadata.booking_id;
      if (bookingId) {
        await DatabaseService.updateBooking(bookingId, {
          payment_status: 'paid',
          status: 'confirmed'
        });

        console.log('✅ Booking confirmed:', bookingId);
      }
    } catch (error) {
      console.error('❌ Handle payment succeeded error:', error);
    }
  }

  private async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    try {
      console.log('❌ Payment failed:', paymentIntent.id);

      // Update payment status
      await this.updatePaymentStatus(paymentIntent.id, 'failed');

      // Update booking status
      const bookingId = paymentIntent.metadata.booking_id;
      if (bookingId) {
        await DatabaseService.updateBooking(bookingId, {
          payment_status: 'failed',
          status: 'cancelled',
          cancellation_reason: 'Payment failed'
        });

        console.log('❌ Booking cancelled due to payment failure:', bookingId);
      }
    } catch (error) {
      console.error('❌ Handle payment failed error:', error);
    }
  }

  private async handleChargeDispute(dispute: Stripe.Dispute): Promise<void> {
    try {
      console.log('⚠️ Charge dispute created:', dispute.id);
      
      // Log dispute for manual review
      // In production, you might want to send notifications to admins
      console.log('Dispute details:', {
        id: dispute.id,
        amount: dispute.amount,
        currency: dispute.currency,
        reason: dispute.reason,
        status: dispute.status
      });
    } catch (error) {
      console.error('❌ Handle charge dispute error:', error);
    }
  }

  // =============================================================================
  // REFUND OPERATIONS
  // =============================================================================

  async createRefund(paymentIntentId: string, amount?: number, reason?: string): Promise<{ success: boolean; refund?: Stripe.Refund; error?: string }> {
    try {
      if (!this.stripe) {
        return { success: false, error: 'Stripe not configured' };
      }

      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId,
        reason: reason as Stripe.RefundCreateParams.Reason || 'requested_by_customer'
      };

      if (amount) {
        refundParams.amount = Math.round(amount);
      }

      const refund = await this.stripe.refunds.create(refundParams);

      // Update payment status
      await this.updatePaymentStatus(paymentIntentId, 'refunded', amount);

      return {
        success: true,
        refund
      };
    } catch (error) {
      console.error('❌ Create refund error:', error);
      return {
        success: false,
        error: 'Failed to create refund'
      };
    }
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  private async updatePaymentStatus(paymentIntentId: string, status: string, refundAmount?: number): Promise<void> {
    try {
      // This would need to be implemented in DatabaseService
      // For now, just log the update
      console.log(`💳 Payment status updated: ${paymentIntentId} -> ${status}`, { refundAmount });
    } catch (error) {
      console.error('❌ Update payment status error:', error);
    }
  }

  async getPaymentStatus(paymentIntentId: string): Promise<{ success: boolean; status?: string; error?: string }> {
    try {
      if (!this.stripe) {
        return { success: false, error: 'Stripe not configured' };
      }

      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      return {
        success: true,
        status: paymentIntent.status
      };
    } catch (error) {
      console.error('❌ Get payment status error:', error);
      return {
        success: false,
        error: 'Failed to get payment status'
      };
    }
  }

  isConfigured(): boolean {
    return !!this.stripe;
  }
}

// Export singleton instance
export default new ProductionStripeService();
