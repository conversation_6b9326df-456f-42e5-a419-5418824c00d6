import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

class VehicleServiceError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'VehicleServiceError';
  }
}

export async function filterVehicles(
  type?: string, 
  minPrice?: number, 
  maxPrice?: number, 
  insurance?: boolean, 
  addOns?: string[]
) {
  try {
    // Validate input parameters
    if (minPrice !== undefined && minPrice < 0) {
      throw new VehicleServiceError('Minimum price cannot be negative', 'INVALID_MIN_PRICE');
    }

    if (maxPrice !== undefined && maxPrice < 0) {
      throw new VehicleServiceError('Maximum price cannot be negative', 'INVALID_MAX_PRICE');
    }

    if (minPrice !== undefined && maxPrice !== undefined && minPrice > maxPrice) {
      throw new VehicleServiceError('Minimum price cannot be greater than maximum price', 'INVALID_PRICE_RANGE');
    }

    const whereCondition: any = {};

    if (type) {
      whereCondition.vehicleType = type;
    }

    if (minPrice !== undefined && maxPrice !== undefined) {
      whereCondition.dailyRate = {
        gte: minPrice,
        lte: maxPrice
      };
    }

    if (insurance !== undefined) {
      whereCondition.hasInsurance = insurance;
    }

    if (addOns && addOns.length > 0) {
      whereCondition.addOns = {
        hasSome: addOns
      };
    }

    const vehicles = await prisma.vehicle.findMany({
      where: whereCondition,
      include: {
        images: true
      }
    });

    this.logger.info('Vehicles searched successfully', {
      type, 
      minPrice, 
      maxPrice, 
      insurance, 
      addOns, 
      resultCount: vehicles.length 
    });

    return vehicles;
  } catch (error) {
    this.logger.error('Error searching vehicles:', {
      error, 
      type, 
      minPrice, 
      maxPrice, 
      insurance, 
      addOns 
    });

    if (error instanceof VehicleServiceError) {
      throw error;
    }

    // Return empty array instead of throwing a generic error
    return [];
  }
}

export async function getVehicleTypes() {
  try {
    const vehicles = await prisma.vehicle.findMany({
      select: { vehicleType: true },
      distinct: ['vehicleType']
    });

    const types = vehicles
      .map(v => v.vehicleType)
      .filter(type => type !== null) as string[];

    

    return types;
  } catch (error) {
    
    throw new VehicleServiceError('Failed to retrieve vehicle types', 'TYPES_ERROR');
  }
}

export async function getVehicleAddOns() {
  try {
    const vehicles = await prisma.vehicle.findMany({
      select: { addOns: true }
    });

    const allAddOns = vehicles.flatMap(v => v.addOns);
    const uniqueAddOns = [...new Set(allAddOns)];

    

    return uniqueAddOns;
  } catch (error) {
    
    throw new VehicleServiceError('Failed to retrieve vehicle add-ons', 'ADDONS_ERROR');
  }
}

export async function getVehicleById(id: string) {
  try {
    const vehicle = await prisma.vehicle.findUnique({
      where: { id },
      include: {
        images: true,
        reviews: true
      }
    });
    return vehicle;
  } catch (error) {
    logger.error('Error fetching vehicle by ID:', error);
    throw new VehicleServiceError('Failed to fetch vehicle');
  }
}

export { VehicleServiceError };

// Default export for compatibility
const VehicleService = {
  filterVehicles,
  getVehicleById,
  getVehicleAddOns
};

export default VehicleService;
