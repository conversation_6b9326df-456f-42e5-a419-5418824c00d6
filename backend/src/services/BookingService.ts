import { PrismaClient, BookingStatus, PaymentMethod, Booking, Prisma } from '@prisma/client';
import { PaymentService } from './PaymentService';
import { MonitoringService } from './MonitoringService';
import { InsuranceDepositService } from './InsuranceDepositService';
import { MultiVehicleBookingRequest, MultiVehicleBookingResult, CreateBookingData } from '../types/booking';
import dynamicPricingService, { DynamicPricingService } from './DynamicPricingService';

export interface BookingResponse {
  success: boolean;
  bookingId?: string;
  paymentIntent?: any;
  error?: string;
}

export class BookingService {
  private prisma: PrismaClient;
  private paymentService: PaymentService;
  private monitoringService?: MonitoringService;
  private dynamicPricingService: DynamicPricingService;
  public supabase: any;

  constructor(
    prisma?: PrismaClient, 
    paymentService?: PaymentService, 
    monitoringService?: MonitoringService
  ) {
    this.prisma = prisma || new PrismaClient();
    this.paymentService = paymentService || new PaymentService(this.prisma);
    this.monitoringService = monitoringService;
    this.dynamicPricingService = new DynamicPricingService();

    // Stub supabase property to satisfy controllers that rely on it
    // Replace with actual supabase client if needed
    // @ts-ignore
    this.supabase = (global as any).supabase || {};
  }

  /**
   * Check vehicle availability for a specific date range
   */
  async checkVehicleAvailability(
    vehicleId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<boolean> {
    const conflictingBookings = await this.prisma.booking.findMany({
      where: {
        vehicleId,
        status: {
          in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS']
        },
        OR: [
          {
            startDate: { lte: endDate },
            endDate: { gte: startDate }
          },
          {
            startDate: { lte: endDate },
            endDate: { gte: endDate }
          },
          {
            startDate: { gte: startDate },
            endDate: { lte: endDate }
          }
        ]
      }
    });

    return conflictingBookings.length === 0;
  }

  /**
   * Create a new booking
   */
  async createBooking(bookingData: CreateBookingData | MultiVehicleBookingRequest): Promise<Booking | MultiVehicleBookingResult> {
    if ('vehicles' in bookingData) {
      const availabilityChecks = await Promise.all(
        (bookingData as MultiVehicleBookingRequest).vehicles.map(async (vehicle: { vehicleId: string; startDate: Date; endDate: Date }) => {
          const isAvailable = await this.checkVehicleAvailability(vehicle.vehicleId, vehicle.startDate, vehicle.endDate);
          return { vehicleId: vehicle.vehicleId, available: isAvailable };
        })
      );

      const unavailableVehicles: { vehicleId: string; available: boolean }[] = availabilityChecks.filter((check: { vehicleId: string; available: boolean }) => !check.available);
      if (unavailableVehicles.length > 0) {
        throw new Error(`Some vehicles are unavailable: ${unavailableVehicles.map((v: { vehicleId: string }) => v.vehicleId).join(', ')}`);
      }

      const bookingPromises = (bookingData as MultiVehicleBookingRequest).vehicles.map((vehicle: { vehicleId: string; startDate: Date; endDate: Date }) =>
        this.prisma.booking.create({
          data: {
            userId: bookingData.userId,
            vehicleId: vehicle.vehicleId,
            startDate: vehicle.startDate,
            endDate: vehicle.endDate,
            totalPrice: 0,  // Temporarily set to 0 as dynamic pricing method is undefined; this needs manual review
            providerId: bookingData.userId, // Use userId as providerId for now
            paymentMethod: 'CARD' // Default to CARD payment method
          }
        })
      );

      const bookings = await Promise.all(bookingPromises);
      const totalPrice = bookings.reduce((sum, b) => 
        Prisma.Decimal.add(sum, b.totalPrice), 
        new Prisma.Decimal(0)
      );
      return {
        bookings: bookings,
        totalPrice: totalPrice,
        status: 'success'
      } as MultiVehicleBookingResult;
    } else {
      const isAvailable = await this.checkVehicleAvailability(bookingData.vehicleId, bookingData.startDate, bookingData.endDate);
      if (!isAvailable) {
        throw new Error('Vehicle is not available');
      }
      // Removed dynamic pricing call due to non-existent method
      return this.prisma.booking.create({
        data: {
          userId: bookingData.userId,
          vehicleId: bookingData.vehicleId,
          startDate: bookingData.startDate,
          endDate: bookingData.endDate,
          totalPrice: 0,  // Temporarily set to 0; update with actual pricing logic
          providerId: bookingData.providerId,
          paymentMethod: 'CARD' // Default to CARD payment method
        }
      });
    }
  }

  /**
   * Cancel a booking
   */
  async cancelBooking(
    bookingId: string, 
    reason: string = 'User requested cancellation'
  ): Promise<Booking> {
    return this.prisma.booking.update({
      where: { id: bookingId },
      data: { 
        status: BookingStatus.CANCELLED,
        cancellationReason: reason
      }
    });
  }

  /**
   * Get booking by ID
   */
  async getBookingById(bookingId: string): Promise<Booking | null> {
    return this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: {
        user: true,
        provider: true,
        vehicle: true,
        payment: true
      }
    });
  }

  /**
   * Get bookings by user ID
   */
  async getBookingsByUserId(userId: string): Promise<Booking[]> {
    return this.prisma.booking.findMany({
      where: { userId },
      include: {
        vehicle: true,
        provider: true,
        payment: true
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get bookings by provider ID
   */
  async getBookingsByProviderId(providerId: string): Promise<Booking[]> {
    return this.prisma.booking.findMany({
      where: { providerId },
      include: {
        user: true,
        vehicle: true,
        payment: true
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(
    bookingId: string, 
    newStatus: BookingStatus
  ): Promise<Booking> {
    return this.prisma.booking.update({
      where: { id: bookingId },
      data: { 
        status: newStatus,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Confirm a cash booking by provider
   */
  async confirmCashBooking(bookingId: string, providerId: string): Promise<Booking> {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: { vehicle: true }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    if (booking.providerId !== providerId) {
      throw new Error('Unauthorized booking confirmation');
    }

    if (booking.status !== BookingStatus.PENDING) {
      throw new Error('Booking cannot be confirmed');
    }

    return this.prisma.booking.update({
      where: { id: bookingId },
      data: { 
        status: BookingStatus.CONFIRMED 
      }
    });
  }

  /**
   * Reject a cash booking by provider
   */
  async rejectCashBooking(
    bookingId: string, 
    providerId: string, 
    reason?: string
  ): Promise<Booking> {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    if (booking.providerId !== providerId) {
      throw new Error('Unauthorized booking rejection');
    }

    if (booking.status !== BookingStatus.PENDING) {
      throw new Error('Booking cannot be rejected');
    }

    return this.prisma.booking.update({
      where: { id: bookingId },
      data: { 
        status: BookingStatus.CANCELLED,
        cancellationReason: reason || 'Rejected by provider'
      }
    });
  }

  /**
   * Get unavailable dates for a specific vehicle
   */
  async getUnavailableDates(vehicleId: string): Promise<{ startDate: Date; endDate: Date }[]> {
    const bookings = await this.prisma.booking.findMany({
      where: {
        vehicleId,
        status: {
          in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS']
        }
      },
      select: {
        startDate: true,
        endDate: true
      }
    });

    return bookings;
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(): Promise<{
    totalBookings: number;
    activeBookings: number;
    completedBookings: number;
    cancelledBookings: number;
  }> {
    const [totalBookings, activeBookings, completedBookings, cancelledBookings] = await Promise.all([
      this.prisma.booking.count(),
      this.prisma.booking.count({
        where: { status: { in: ['PENDING', 'CONFIRMED', 'IN_PROGRESS'] } }
      }),
      this.prisma.booking.count({
        where: { status: 'COMPLETED' }
      }),
      this.prisma.booking.count({
        where: { status: 'CANCELLED' }
      })
    ]);

    return {
      totalBookings,
      activeBookings,
      completedBookings,
      cancelledBookings
    };
  }
}

export default BookingService;