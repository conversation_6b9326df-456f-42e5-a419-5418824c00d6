import fs from 'fs';
import path from 'path';

export interface SEAsiaVehicle {
  id: number;
  brand: string;
  model: string;
  year?: string;
  engine: string;
  type: 'scooter' | 'motorcycle';
  category: 'small' | 'medium' | 'large' | 'luxury';
}

export interface VehicleSearchResult {
  vehicles: SEAsiaVehicle[];
  total: number;
  hasMore: boolean;
}

class VehicleSearchService {
  private vehicles: SEAsiaVehicle[] = [];

  constructor() {
    this.loadVehicles();
  }

  private loadVehicles(): void {
    try {
      const dataPath = path.join(__dirname, '../data/seAsiaVehicles.json');
      const data = fs.readFileSync(dataPath, 'utf8');
      this.vehicles = JSON.parse(data);
    } catch (error) {
      console.error('Error loading vehicle database:', error);
      this.vehicles = [];
    }
  }

  /**
   * Search vehicles by brand, model, or type
   */
  public searchVehicles(query: string, limit: number = 10): VehicleSearchResult {
    if (!query || query.trim().length === 0) {
      return {
        vehicles: this.vehicles.slice(0, limit),
        total: this.vehicles.length,
        hasMore: this.vehicles.length > limit
      };
    }

    const searchTerm = query.toLowerCase().trim();
    const filtered = this.vehicles.filter(vehicle => 
      vehicle.brand.toLowerCase().includes(searchTerm) ||
      vehicle.model.toLowerCase().includes(searchTerm) ||
      vehicle.type.toLowerCase().includes(searchTerm) ||
      vehicle.category.toLowerCase().includes(searchTerm) ||
      vehicle.engine.toLowerCase().includes(searchTerm)
    );

    return {
      vehicles: filtered.slice(0, limit),
      total: filtered.length,
      hasMore: filtered.length > limit
    };
  }

  /**
   * Get vehicle by ID
   */
  public getVehicleById(id: number): SEAsiaVehicle | null {
    return this.vehicles.find(vehicle => vehicle.id === id) || null;
  }

  /**
   * Get vehicles by category
   */
  public getVehiclesByCategory(category: string): SEAsiaVehicle[] {
    return this.vehicles.filter(vehicle => 
      vehicle.category.toLowerCase() === category.toLowerCase()
    );
  }

  /**
   * Get vehicles by type
   */
  public getVehiclesByType(type: string): SEAsiaVehicle[] {
    return this.vehicles.filter(vehicle => 
      vehicle.type.toLowerCase() === type.toLowerCase()
    );
  }

  /**
   * Get all brands
   */
  public getAllBrands(): string[] {
    const brands = new Set(this.vehicles.map(vehicle => vehicle.brand));
    return Array.from(brands).sort();
  }

  /**
   * Get all categories
   */
  public getAllCategories(): string[] {
    const categories = new Set(this.vehicles.map(vehicle => vehicle.category));
    return Array.from(categories).sort();
  }

  /**
   * Get all types
   */
  public getAllTypes(): string[] {
    const types = new Set(this.vehicles.map(vehicle => vehicle.type));
    return Array.from(types).sort();
  }

  /**
   * Get all vehicles
   */
  public getAllVehicles(): SEAsiaVehicle[] {
    return this.vehicles;
  }

  /**
   * Get popular vehicles (most common in SE Asia)
   */
  public getPopularVehicles(): SEAsiaVehicle[] {
    const popularBrands = ['Honda', 'Yamaha', 'Suzuki', 'Kawasaki'];
    return this.vehicles.filter(vehicle => 
      popularBrands.includes(vehicle.brand)
    ).slice(0, 20);
  }

  /**
   * Get electric vehicles
   */
  public getElectricVehicles(): SEAsiaVehicle[] {
    return this.vehicles.filter(vehicle => 
      vehicle.engine.toLowerCase().includes('electric')
    );
  }

  /**
   * Get luxury vehicles
   */
  public getLuxuryVehicles(): SEAsiaVehicle[] {
    return this.vehicles.filter(vehicle => 
      vehicle.category === 'luxury'
    );
  }

  /**
   * Get small vehicles (suitable for beginners)
   */
  public getSmallVehicles(): SEAsiaVehicle[] {
    return this.vehicles.filter(vehicle => 
      vehicle.category === 'small'
    );
  }

  /**
   * Validate if a vehicle exists in the database
   */
  public validateVehicle(brand: string, model: string): boolean {
    return this.vehicles.some(vehicle => 
      vehicle.brand.toLowerCase() === brand.toLowerCase() &&
      vehicle.model.toLowerCase() === model.toLowerCase()
    );
  }

  /**
   * Get vehicle suggestions based on partial input
   */
  public getSuggestions(partial: string, limit: number = 5): SEAsiaVehicle[] {
    if (!partial || partial.trim().length === 0) {
      return [];
    }

    const searchTerm = partial.toLowerCase().trim();
    const suggestions = this.vehicles.filter(vehicle => 
      vehicle.brand.toLowerCase().startsWith(searchTerm) ||
      vehicle.model.toLowerCase().startsWith(searchTerm)
    );

    return suggestions.slice(0, limit);
  }

  public addVehicle(vehicle: SEAsiaVehicle): void {
    this.vehicles.push(vehicle);
  }
}

export default new VehicleSearchService(); 