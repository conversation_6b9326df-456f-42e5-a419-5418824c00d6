import { Vehicle, Booking, User } from '../types/database';
import { PrismaClient } from '@prisma/client';
import { AppError } from '../middleware/errorMiddleware';

export interface DiscountRule {
  id: string;
  name: string;
  type: 'percentage' | 'fixed';
  value: number;
  conditions: {
    minBookingDays?: number;
    maxBookingDays?: number;
    vehicleCategories?: string[];
    userTotalBookings?: number;
    seasonStart?: string;
    seasonEnd?: string;
    minTotalAmount?: number;
  };
  priority: number;
  code: string;
  description?: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  
  // Enhanced Applicability Rules
  applicabilityRules: {
    vehicleTypes?: string[];
    userTypes?: string[];
    minBookingDays?: number;
    maxBookingDays?: number;
    minTotalAmount?: number;
    maxTotalAmount?: number;
    dayOfWeek?: number[];
    timeOfDay?: {
      start: string;
      end: string;
    };
    userAgeRange?: {
      min?: number;
      max?: number;
    };
    userMembershipTier?: string[];
    recurringBookingCount?: number;
  };

  startDate?: Date;
  expirationDate?: Date;
  usageLimit?: number;
  isActive: boolean;
}

export interface DiscountCodeConfig {
    code: string;
    description?: string;
    discountType: 'percentage' | 'fixed';
    discountValue: number;
    minPurchaseAmount?: number;
    maxDiscountAmount?: number;
    startDate?: Date;
    expirationDate?: Date;
    usageLimit?: number;
    appliesTo?: string[];
}

export class DiscountService {
  private discountRules: DiscountRule[] = [
    // Loyalty Discount
    {
      id: 'loyalty-discount',
      name: 'Loyalty Discount',
      type: 'percentage',
      value: 10,
      conditions: {
        userTotalBookings: 10
      },
      priority: 1,
      code: 'loyalty-discount',
      discountType: 'percentage',
      discountValue: 10,
      applicabilityRules: {},
      isActive: true
    },
    // Seasonal Discount
    {
      id: 'off-peak-discount',
      name: 'Off-Peak Season Discount',
      type: 'percentage',
      value: 15,
      conditions: {
        seasonStart: '2024-01-01',
        seasonEnd: '2024-03-31'
      },
      priority: 2,
      code: 'off-peak-discount',
      discountType: 'percentage',
      discountValue: 15,
      applicabilityRules: {},
      isActive: true
    },
    // Long-term Rental Discount
    {
      id: 'long-term-rental',
      name: 'Extended Rental Discount',
      type: 'percentage',
      value: 20,
      conditions: {
        minBookingDays: 30
      },
      priority: 3,
      code: 'long-term-rental',
      discountType: 'percentage',
      discountValue: 20,
      applicabilityRules: {},
      isActive: true
    }
  ];

  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Calculate applicable discounts
   * @param booking Booking details
   * @param user User information
   * @param vehicle Vehicle details
   * @returns Calculated discounts
   */
  calculateDiscounts(
    booking: Booking, 
    user: any, 
    vehicle: Vehicle
  ): { 
    totalDiscount: number, 
    appliedRules: DiscountRule[] 
  } {
    const applicableRules = this.findApplicableRules(
      booking, 
      user, 
      vehicle
    );

    // Sort rules by priority (highest first)
    const sortedRules = applicableRules.sort(
      (a, b) => b.priority - a.priority
    );

    // Calculate total discount
    let totalDiscount = 0;
    const appliedRules: DiscountRule[] = [];

    for (const rule of sortedRules) {
      const discountAmount = this.calculateRuleDiscount(
        rule, 
        booking.total
      );

      // Prevent stacking multiple high-priority discounts
      if (discountAmount > 0) {
        totalDiscount += discountAmount;
        appliedRules.push(rule);
        break; // Apply only the highest priority rule
      }
    }

    return { 
      totalDiscount, 
      appliedRules 
    };
  }

  /**
   * Find rules applicable to a specific booking
   * @param booking Booking details
   * @param user User information
   * @param vehicle Vehicle details
   * @returns Applicable discount rules
   */
  private findApplicableRules(
    booking: Booking, 
    user: any, 
    vehicle: Vehicle
  ): DiscountRule[] {
    return this.discountRules.filter(rule => {
      // Check booking days condition
      if (rule.conditions.minBookingDays && 
          booking.total_days < rule.conditions.minBookingDays) {
        return false;
      }

      if (rule.conditions.maxBookingDays && 
          booking.total_days > rule.conditions.maxBookingDays) {
        return false;
      }

      // Check vehicle category
      if (rule.conditions.vehicleCategories && 
          !rule.conditions.vehicleCategories.includes(vehicle.category)) {
        return false;
      }

      // Check user total bookings
      if (rule.conditions.userTotalBookings && 
          user.total_bookings < rule.conditions.userTotalBookings) {
        return false;
      }

      // Check seasonal conditions
      if (rule.conditions.seasonStart && rule.conditions.seasonEnd) {
        const now = new Date();
        const seasonStart = new Date(rule.conditions.seasonStart);
        const seasonEnd = new Date(rule.conditions.seasonEnd);
        
        if (now < seasonStart || now > seasonEnd) {
          return false;
        }
      }

      // Check minimum total amount
      if (rule.conditions.minTotalAmount && 
          booking.total < rule.conditions.minTotalAmount) {
        return false;
      }

      return true;
    });
  }

  /**
   * Calculate discount amount for a specific rule
   * @param rule Discount rule
   * @param totalAmount Total booking amount
   * @returns Discount amount
   */
  private calculateRuleDiscount(
    rule: DiscountRule, 
    totalAmount: number
  ): number {
    if (rule.type === 'percentage') {
      return totalAmount * (rule.value / 100);
    } else {
      return Math.min(rule.value, totalAmount);
    }
  }

  /**
   * Add a new discount rule
   * @param rule Discount rule to add
   */
  addDiscountRule(rule: DiscountRule): void {
    this.discountRules.push(rule);
  }

  /**
   * Remove a discount rule
   * @param ruleId ID of the rule to remove
   */
  removeDiscountRule(ruleId: string): void {
    this.discountRules = this.discountRules.filter(
      rule => rule.id !== ruleId
    );
  }

  /**
   * Create a new discount code
   */
  async createDiscountCode(config: DiscountCodeConfig, createdBy: string) {
    // Validate input
    this.validateDiscountCodeConfig(config);

    try {
      return await this.prisma.discount_codes.create({
        data: {
          code: config.code,
          description: config.description,
          discount_type: config.discountType,
          discount_value: config.discountValue,
          min_purchase_amount: config.minPurchaseAmount,
          max_discount_amount: config.maxDiscountAmount,
          start_date: config.startDate || new Date(),
          expiration_date: config.expirationDate,
          usage_limit: config.usageLimit,
          applies_to: config.appliesTo || ['all'],
          created_by: createdBy,
          is_active: true
        }
      });
    } catch (error) {
      throw new AppError('Failed to create discount code', 500, error);
    }
  }

  /**
   * Validate and apply a discount code with advanced rules
   */
  async validateDiscountCode(
    discountCode: string, 
    bookingDetails: {
      totalAmount: number;
      vehicleType: string;
      userId: string;
      bookingDays: number;
      userAge?: number;
      userMembershipTier?: string;
      userBookingHistory?: number;
    }
  ) {
    try {
      // Find the discount code with full details
      const discount = await this.prisma.discount_codes.findUnique({
        where: { code: discountCode }
      });

      if (!discount || !discount.is_active) {
        return {
          isValid: false,
          message: 'Invalid or inactive discount code'
        };
      }

      // Parse applicability rules
      const applicabilityRules = JSON.parse(
        discount.applicability_rules || '{}'
      ) as DiscountRule['applicabilityRules'];

      // Validate each applicability rule
      const validationResults = this.validateApplicabilityRules(
        applicabilityRules, 
        bookingDetails
      );

      if (!validationResults.isValid) {
        return validationResults;
      }

      // Calculate discount amount
      let discountAmount = 0;
      if (discount.discount_type === 'percentage') {
        discountAmount = bookingDetails.totalAmount * (discount.discount_value / 100);
      } else {
        discountAmount = discount.discount_value;
      }

      // Apply max discount amount if specified
      if (discount.max_discount_amount) {
        discountAmount = Math.min(
          discountAmount, 
          discount.max_discount_amount
        );
      }

      // Prevent discount from exceeding total amount
      discountAmount = Math.min(
        discountAmount, 
        bookingDetails.totalAmount
      );

      return {
        isValid: true,
        discountCode: discount.code,
        discountType: discount.discount_type,
        discountValue: discount.discount_value,
        discountAmount,
        message: 'Discount applied successfully'
      };
    } catch (error) {
      throw new AppError('Error validating discount code', 500, error);
    }
  }

  /**
   * Validate advanced applicability rules
   */
  private validateApplicabilityRules(
    rules: DiscountRule['applicabilityRules'], 
    bookingDetails: {
      totalAmount: number;
      vehicleType: string;
      userId: string;
      bookingDays: number;
      userAge?: number;
      userMembershipTier?: string;
      userBookingHistory?: number;
    }
  ): { isValid: boolean; message?: string } {
    // Vehicle Type Check
    if (rules.vehicleTypes && 
      !rules.vehicleTypes.includes(bookingDetails.vehicleType)) {
      return {
        isValid: false,
        message: 'Discount not applicable to this vehicle type'
      };
    }

    // Booking Days Range
    if (rules.minBookingDays && 
      bookingDetails.bookingDays < rules.minBookingDays) {
      return {
        isValid: false,
        message: `Minimum ${rules.minBookingDays} days required`
      };
    }

    if (rules.maxBookingDays && 
      bookingDetails.bookingDays > rules.maxBookingDays) {
      return {
        isValid: false,
        message: `Maximum ${rules.maxBookingDays} days allowed`
      };
    }

    // Total Amount Range
    if (rules.minTotalAmount && 
      bookingDetails.totalAmount < rules.minTotalAmount) {
      return {
        isValid: false,
        message: `Minimum total amount of $${rules.minTotalAmount} required`
      };
    }

    if (rules.maxTotalAmount && 
      bookingDetails.totalAmount > rules.maxTotalAmount) {
      return {
        isValid: false,
        message: `Maximum total amount of $${rules.maxTotalAmount} allowed`
      };
    }

    // User Age Check
    if (rules.userAgeRange) {
      if (bookingDetails.userAge === undefined) {
        return {
          isValid: false,
          message: 'User age is required'
        };
      }

      if (rules.userAgeRange.min && 
        bookingDetails.userAge < rules.userAgeRange.min) {
        return {
          isValid: false,
          message: `Minimum age of ${rules.userAgeRange.min} required`
        };
      }

      if (rules.userAgeRange.max && 
        bookingDetails.userAge > rules.userAgeRange.max) {
        return {
          isValid: false,
          message: `Maximum age of ${rules.userAgeRange.max} allowed`
        };
      }
    }

    // Membership Tier Check
    if (rules.userMembershipTier && 
      !rules.userMembershipTier.includes(
        bookingDetails.userMembershipTier || ''
      )) {
      return {
        isValid: false,
        message: 'Discount not applicable to your membership tier'
      };
    }

    // Recurring Booking Check
    if (rules.recurringBookingCount && 
      (bookingDetails.userBookingHistory || 0) < rules.recurringBookingCount) {
      return {
        isValid: false,
        message: `Minimum ${rules.recurringBookingCount} previous bookings required`
      };
    }

    // If all checks pass
    return { isValid: true };
  }

  /**
   * Create a new discount code with advanced rules
   */
  async createAdvancedDiscountCode(
    discountCode: DiscountRule, 
    createdBy: string
  ) {
    try {
      return await this.prisma.discount_codes.create({
        data: {
          code: discountCode.code,
          description: discountCode.description,
          discount_type: discountCode.discountType,
          discount_value: discountCode.discountValue,
          applicability_rules: JSON.stringify(
            discountCode.applicabilityRules
          ),
          start_date: discountCode.startDate,
          expiration_date: discountCode.expirationDate,
          usage_limit: discountCode.usageLimit,
          created_by: createdBy,
          is_active: discountCode.isActive
        }
      });
    } catch (error) {
      throw new AppError('Failed to create advanced discount code', 500, error);
    }
  }

  /**
   * Update an existing discount code
   */
  async updateDiscountCode(id: string, updates: Partial<DiscountCodeConfig>) {
    // Validate input
    if (updates.discountType || updates.discountValue) {
      this.validateDiscountCodeConfig(updates as DiscountCodeConfig);
    }

    try {
      return await this.prisma.discount_codes.update({
        where: { id },
        data: {
          ...updates,
          updated_at: new Date()
        }
      });
    } catch (error) {
      throw new AppError('Failed to update discount code', 500, error);
    }
  }

  /**
   * Deactivate a discount code
   */
  async deactivateDiscountCode(id: string) {
    try {
      return await this.prisma.discount_codes.update({
        where: { id },
        data: { 
          is_active: false,
          updated_at: new Date()
        }
      });
    } catch (error) {
      throw new AppError('Failed to deactivate discount code', 500, error);
    }
  }

  /**
   * List discount codes with optional filtering
   */
  async listDiscountCodes(filters: {
    isActive?: boolean;
    discountType?: 'percentage' | 'fixed';
    appliesTo?: string;
  } = {}) {
    try {
      return await this.prisma.discount_codes.findMany({
        where: {
          is_active: filters.isActive,
          discount_type: filters.discountType,
          applies_to: filters.appliesTo 
            ? { has: filters.appliesTo } 
            : undefined
        },
        orderBy: { created_at: 'desc' }
      });
    } catch (error) {
      throw new AppError('Failed to list discount codes', 500, error);
    }
  }

  /**
   * Validate discount code configuration
   */
  private validateDiscountCodeConfig(config: DiscountCodeConfig) {
    // Validate discount type
    if (!['percentage', 'fixed'].includes(config.discountType)) {
      throw new AppError('Invalid discount type', 400);
    }

    // Validate discount value
    if (config.discountType === 'percentage' && 
        (config.discountValue < 0 || config.discountValue > 100)) {
      throw new AppError('Percentage discount must be between 0 and 100', 400);
    }

    if (config.discountType === 'fixed' && config.discountValue <= 0) {
      throw new AppError('Fixed discount must be greater than 0', 400);
    }

    // Validate dates
    if (config.startDate && config.expirationDate && 
        config.startDate > config.expirationDate) {
      throw new AppError('Start date must be before expiration date', 400);
    }
  }
}

export default new DiscountService(); 