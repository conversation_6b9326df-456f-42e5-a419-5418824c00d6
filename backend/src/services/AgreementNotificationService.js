const nodemailer = require('nodemailer');
const twilio = require('twilio');

class AgreementNotificationService {
  constructor() {
    // Initialize email transporter
    this.emailTransporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    // Initialize Twilio client
    this.twilioClient = process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN
      ? twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)
      : null;
  }

  /**
   * Send agreement notification to customer
   */
  async sendAgreementNotification(agreement, customerEmail, customerPhone = null) {
    try {
      const results = {
        email: null,
        sms: null
      };

      // Send email notification
      if (customerEmail) {
        results.email = await this.sendAgreementEmail(agreement, customerEmail);
      }

      // Send SMS notification if phone number provided
      if (customerPhone && this.twilioClient) {
        results.sms = await this.sendAgreementSMS(agreement, customerPhone);
      }

      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Error sending agreement notification:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send agreement email
   */
  async sendAgreementEmail(agreement, customerEmail) {
    try {
      const metadata = JSON.parse(agreement.metadata || '{}');
      const agreementUrl = `${process.env.FRONTEND_URL}/agreements/${agreement.id}/view`;

      const emailTemplate = this.generateBookingConfirmationTemplate(agreement, metadata, agreementUrl);

      const mailOptions = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: customerEmail,
        subject: `🚗 Booking Confirmation - ${metadata.vehicleName || 'Vehicle Rental'} | RentaHub`,
        html: emailTemplate
      };

      const result = await this.emailTransporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Error sending agreement email:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send agreement SMS
   */
  async sendAgreementSMS(agreement, customerPhone) {
    try {
      if (!this.twilioClient) {
        throw new Error('Twilio not configured');
      }

      const metadata = JSON.parse(agreement.metadata || '{}');
      const agreementUrl = `${process.env.FRONTEND_URL}/agreements/${agreement.id}/view`;

      const message = `Hi ${metadata.customerName || 'Customer'}! Your rental agreement for ${metadata.vehicleName || 'your vehicle'} is ready for review and signature. Please visit: ${agreementUrl}`;

      const result = await this.twilioClient.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: customerPhone
      });

      return {
        success: true,
        sid: result.sid
      };
    } catch (error) {
      console.error('Error sending agreement SMS:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate booking confirmation email template (like airlines/booking.com)
   */
  generateBookingConfirmationTemplate(agreement, metadata, agreementUrl) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Booking Confirmation - RentaHub</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 0;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                margin: 20px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }
            .confirmation-number {
                background-color: rgba(255,255,255,0.2);
                padding: 10px 20px;
                border-radius: 20px;
                display: inline-block;
                margin-top: 15px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            .content {
                padding: 30px;
            }
            .booking-card {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 25px;
                margin: 20px 0;
                background-color: #fafafa;
            }
            .booking-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #e0e0e0;
            }
            .vehicle-info {
                font-size: 18px;
                font-weight: bold;
                color: #333;
            }
            .status-badge {
                background-color: #4CAF50;
                color: white;
                padding: 5px 15px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
            }
            .detail-row {
                display: flex;
                justify-content: space-between;
                margin: 10px 0;
                padding: 8px 0;
            }
            .detail-label {
                color: #666;
                font-weight: 500;
            }
            .detail-value {
                font-weight: bold;
                color: #333;
            }
            .price-total {
                background-color: #f0f8ff;
                border: 1px solid #2196F3;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                text-align: center;
            }
            .price-amount {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
            }
            .cta-section {
                text-align: center;
                margin: 30px 0;
                padding: 20px;
                background-color: #fff3e0;
                border-radius: 8px;
                border-left: 4px solid #ff9800;
            }
            .cta-button {
                display: inline-block;
                background-color: #ff9800;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 15px 0;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .important-info {
                background-color: #e8f5e8;
                border: 1px solid #4CAF50;
                border-radius: 5px;
                padding: 20px;
                margin: 20px 0;
            }
            .footer {
                background-color: #f5f5f5;
                padding: 20px;
                text-align: center;
                color: #666;
                font-size: 14px;
                border-top: 1px solid #e0e0e0;
            }
            .contact-info {
                display: flex;
                justify-content: space-around;
                margin: 20px 0;
                text-align: center;
            }
            .contact-item {
                flex: 1;
            }
            @media (max-width: 600px) {
                .container { margin: 10px; }
                .content { padding: 20px; }
                .booking-header { flex-direction: column; align-items: flex-start; }
                .contact-info { flex-direction: column; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚗 Booking Confirmed</h1>
                <div class="confirmation-number">
                    Confirmation #${agreement.id.substring(0, 8).toUpperCase()}
                </div>
            </div>

            <div class="content">
                <p>Hi <strong>${metadata.customerName || 'Customer'}</strong>,</p>
                <p>Great news! Your vehicle rental has been confirmed. Please review the details below and confirm your agreement to proceed.</p>

                <div class="booking-card">
                    <div class="booking-header">
                        <div class="vehicle-info">${metadata.vehicleName || 'Vehicle'}</div>
                        <div class="status-badge">Confirmed</div>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">📅 Pickup Date & Time</span>
                        <span class="detail-value">${metadata.rentalPeriod?.split(' - ')[0] || 'TBD'}</span>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">📅 Return Date & Time</span>
                        <span class="detail-value">${metadata.rentalPeriod?.split(' - ')[1] || 'TBD'}</span>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">📍 Pickup Location</span>
                        <span class="detail-value">Provider Location</span>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">🆔 Booking Reference</span>
                        <span class="detail-value">${agreement.id.substring(0, 12).toUpperCase()}</span>
                    </div>
                </div>

                <div class="price-total">
                    <div>Total Amount</div>
                    <div class="price-amount">$${metadata.totalAmount?.toFixed(2) || '0.00'}</div>
                    <div style="font-size: 14px; color: #666; margin-top: 5px;">All taxes and fees included</div>
                </div>

                <div class="cta-section">
                    <h3 style="margin-top: 0; color: #e65100;">⚠️ Action Required</h3>
                    <p>Please review and confirm your rental agreement to complete your booking:</p>
                    <a href="${agreementUrl}" class="cta-button">
                        Review & Confirm Agreement
                    </a>
                    <p style="font-size: 14px; color: #666; margin-bottom: 0;">
                        You must confirm within 24 hours to secure your booking
                    </p>
                </div>

                <div class="important-info">
                    <h3 style="margin-top: 0; color: #2e7d32;">📋 What to Bring</h3>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Valid driver's license</li>
                        <li>Credit card for security deposit</li>
                        <li>This confirmation email</li>
                    </ul>
                </div>

                <div class="contact-info">
                    <div class="contact-item">
                        <strong>📞 Call Us</strong><br>
                        <span style="color: #2196F3;">(*************</span>
                    </div>
                    <div class="contact-item">
                        <strong>✉️ Email Us</strong><br>
                        <span style="color: #2196F3;"><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <strong>🆘 Emergency</strong><br>
                        <span style="color: #f44336;">(555) 911-HELP</span>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p><strong>RentaHub</strong> - Your trusted vehicle rental partner</p>
                <p>This is an automated confirmation. Please do not reply to this email.</p>
                <p style="font-size: 12px; margin-top: 15px;">
                    © 2025 RentaHub. All rights reserved. |
                    <a href="#" style="color: #666;">Privacy Policy</a> |
                    <a href="#" style="color: #666;">Terms of Service</a>
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send agreement reminder
   */
  async sendAgreementReminder(agreement, customerEmail, customerPhone = null) {
    try {
      const metadata = JSON.parse(agreement.metadata || '{}');
      const agreementUrl = `${process.env.FRONTEND_URL}/agreements/${agreement.id}/view`;

      const results = {
        email: null,
        sms: null
      };

      // Send email reminder
      if (customerEmail) {
        const reminderEmailTemplate = this.generateReminderEmailTemplate(agreement, metadata, agreementUrl);
        
        const mailOptions = {
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: customerEmail,
          subject: `⏰ Reminder: Please Sign Your Rental Agreement - ${metadata.vehicleName || 'Vehicle Rental'}`,
          html: reminderEmailTemplate
        };

        results.email = await this.emailTransporter.sendMail(mailOptions);
      }

      // Send SMS reminder
      if (customerPhone && this.twilioClient) {
        const reminderMessage = `Reminder: Your rental agreement for ${metadata.vehicleName || 'your vehicle'} still needs to be signed. Please visit: ${agreementUrl}`;
        
        results.sms = await this.twilioClient.messages.create({
          body: reminderMessage,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: customerPhone
        });
      }

      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Error sending agreement reminder:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate reminder email template
   */
  generateReminderEmailTemplate(agreement, metadata, agreementUrl) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Agreement Signature Reminder</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #ff9800;
                color: white;
                padding: 20px;
                text-align: center;
                border-radius: 8px 8px 0 0;
            }
            .content {
                background-color: #f9f9f9;
                padding: 30px;
                border-radius: 0 0 8px 8px;
            }
            .urgent {
                background-color: #ffebee;
                border: 2px solid #f44336;
                color: #c62828;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                text-align: center;
            }
            .cta-button {
                display: inline-block;
                background-color: #f44336;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>⏰ Agreement Signature Reminder</h1>
        </div>
        
        <div class="content">
            <h2>Hello ${metadata.customerName || 'Customer'}!</h2>
            
            <div class="urgent">
                <h3>🚨 Action Required</h3>
                <p>Your rental agreement for <strong>${metadata.vehicleName || 'your vehicle'}</strong> is still pending signature.</p>
            </div>
            
            <p>To ensure a smooth rental experience, please sign your agreement as soon as possible. Unsigned agreements may result in rental delays or cancellation.</p>
            
            <div style="text-align: center;">
                <a href="${agreementUrl}" class="cta-button">
                    📝 Sign Agreement Now
                </a>
            </div>
            
            <p>If you're experiencing any issues or have questions, please contact us immediately.</p>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send agreement confirmed confirmation (after customer agrees)
   */
  async sendAgreementConfirmedNotification(agreement, customerEmail, providerEmail) {
    try {
      const metadata = JSON.parse(agreement.metadata || '{}');
      
      const results = {
        customer: null,
        provider: null
      };

      // Send confirmation to customer
      if (customerEmail) {
        const customerTemplate = this.generateAgreementConfirmedTemplate(agreement, metadata, 'customer');
        
        const customerMailOptions = {
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: customerEmail,
          subject: `✅ Agreement Signed - ${metadata.vehicleName || 'Vehicle Rental'}`,
          html: customerTemplate
        };

        results.customer = await this.emailTransporter.sendMail(customerMailOptions);
      }

      // Send notification to provider
      if (providerEmail) {
        const providerTemplate = this.generateSignedConfirmationTemplate(agreement, metadata, 'provider');
        
        const providerMailOptions = {
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: providerEmail,
          subject: `✅ Customer Signed Agreement - ${metadata.customerName || 'Customer'}`,
          html: providerTemplate
        };

        results.provider = await this.emailTransporter.sendMail(providerMailOptions);
      }

      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Error sending signed confirmation:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate agreement confirmed template (like airline booking confirmation)
   */
  generateAgreementConfirmedTemplate(agreement, metadata, recipient) {
    const isCustomer = recipient === 'customer';
    
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Booking Confirmed - Ready for Pickup</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 0;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                margin: 20px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .success-badge {
                background-color: rgba(255,255,255,0.2);
                padding: 10px 20px;
                border-radius: 20px;
                display: inline-block;
                margin-top: 15px;
                font-weight: bold;
            }
            .content {
                padding: 30px;
            }
            .booking-summary {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }
            .next-steps {
                background-color: #fff3e0;
                border-left: 4px solid #ff9800;
                padding: 20px;
                margin: 20px 0;
            }
            .pickup-info {
                background-color: #e3f2fd;
                border: 1px solid #2196F3;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ Booking Confirmed!</h1>
                <div class="success-badge">
                    Ready for Pickup
                </div>
            </div>

            <div class="content">
                <p>Hi <strong>${metadata.customerName || 'Customer'}</strong>,</p>
                <p>${isCustomer
                  ? 'Excellent! You have successfully confirmed your rental agreement. Your booking is now complete and ready for pickup.'
                  : `Great news! ${metadata.customerName || 'Customer'} has confirmed their rental agreement. The booking is ready for pickup.`
                }</p>

                <div class="booking-summary">
                    <h3 style="margin-top: 0; color: #333;">📋 Booking Summary</h3>
                    <p><strong>Vehicle:</strong> ${metadata.vehicleName || 'N/A'}</p>
                    <p><strong>Customer:</strong> ${metadata.customerName || 'N/A'}</p>
                    <p><strong>Rental Period:</strong> ${metadata.rentalPeriod || 'N/A'}</p>
                    <p><strong>Total Amount:</strong> $${metadata.totalAmount?.toFixed(2) || '0.00'}</p>
                    <p><strong>Confirmation:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>Reference:</strong> ${agreement.id.substring(0, 12).toUpperCase()}</p>
                </div>

                ${isCustomer ? `
                <div class="next-steps">
                    <h3 style="margin-top: 0; color: #e65100;">🚗 Next Steps</h3>
                    <ol>
                        <li><strong>Prepare Documents:</strong> Valid driver's license, credit card</li>
                        <li><strong>Arrive on Time:</strong> Be punctual for your pickup appointment</li>
                        <li><strong>Vehicle Inspection:</strong> Check the vehicle condition with the provider</li>
                        <li><strong>Enjoy Your Rental:</strong> Drive safely and follow traffic rules</li>
                    </ol>
                </div>

                <div class="pickup-info">
                    <h3 style="margin-top: 0; color: #1976d2;">📍 Pickup Information</h3>
                    <p><strong>What to Bring:</strong></p>
                    <ul>
                        <li>Valid driver's license (must match booking name)</li>
                        <li>Credit/debit card for security deposit</li>
                        <li>This confirmation email</li>
                    </ul>
                    <p><strong>Important:</strong> Late arrivals may result in additional charges or booking cancellation.</p>
                </div>
                ` : `
                <div class="next-steps">
                    <h3 style="margin-top: 0; color: #e65100;">🔧 Provider Action Required</h3>
                    <ul>
                        <li>Prepare the vehicle for pickup</li>
                        <li>Ensure all documentation is ready</li>
                        <li>Confirm pickup appointment with customer</li>
                        <li>Conduct thorough vehicle inspection</li>
                    </ul>
                </div>
                `}

                <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f0f8ff; border-radius: 8px;">
                    <h3 style="color: #1976d2; margin-top: 0;">Need Help?</h3>
                    <p>📞 <strong>(*************</strong> | ✉️ <strong><EMAIL></strong></p>
                    <p style="color: #f44336; font-weight: bold;">🆘 Emergency: (555) 911-HELP</p>
                </div>

                <p style="text-align: center; color: #666; font-size: 14px;">
                    Thank you for choosing RentaHub for your vehicle rental needs!
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
  }
}

module.exports = AgreementNotificationService;
