import { PrismaClient } from '@prisma/client';
import { EmailService } from './EmailService';
// import { RiskAssessmentModel } from './RiskAssessmentModel'; // Future implementation - removed

// Local enum since RiskAssessmentModel is removed
enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH'
}

export enum InsuranceType {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  COMPREHENSIVE = 'COMPREHENSIVE'
}

export enum DepositType {
  REFUNDABLE = 'REFUNDABLE',
  NON_REFUNDABLE = 'NON_REFUNDABLE',
  PARTIAL = 'PARTIAL'
}

export interface InsuranceConfiguration {
  type: InsuranceType;
  coverageAmount: number;
  additionalCoverage: string[];
  riskFactor: number;
}

export interface DepositConfiguration {
  type: DepositType;
  amount: number;
  refundConditions: string[];
}

export class InsuranceDepositService {
  private prisma: PrismaClient;
  private emailService: EmailService;
  // private riskAssessmentModel: RiskAssessmentModel; // Future implementation - removed

  constructor() {
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    // this.riskAssessmentModel = new RiskAssessmentModel(); // Future implementation - removed
  }

  async createInsuranceConfiguration(
    vehicleId: string, 
    insuranceConfig: InsuranceConfiguration
  ) {
    // Validate and create insurance configuration
    // Stub: Risk assessment - future implementation
    const riskScore = { level: RiskLevel.LOW, score: 0.1 };
    // const riskScore = this.riskAssessmentModel.assessRisk(insuranceConfig);
    
    // Store in vehicle's addOnPrices JSON field since insuranceConfiguration doesn't exist
    const configuration = await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        addOnPrices: {
          insuranceConfiguration: {
            type: insuranceConfig.type,
            coverageAmount: insuranceConfig.coverageAmount,
            additionalCoverage: insuranceConfig.additionalCoverage,
            riskFactor: riskScore
          }
        }
      }
    });

    return configuration;
  }

  async createDepositConfiguration(
    vehicleId: string, 
    depositConfig: DepositConfiguration
  ) {
    // Store in vehicle's addOnPrices JSON field since depositConfiguration doesn't exist
    const configuration = await this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: {
        addOnPrices: {
          depositConfiguration: {
            type: depositConfig.type,
            amount: depositConfig.amount,
            refundConditions: depositConfig.refundConditions
          }
        }
      }
    });

    return configuration;
  }

  async getInsuranceConfigurationForVehicle(vehicleId: string) {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });
    
    return (vehicle?.addOnPrices as any)?.insuranceConfiguration || null;
  }

  async getDepositConfigurationForVehicle(vehicleId: string) {
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });
    
    return (vehicle?.addOnPrices as any)?.depositConfiguration || null;
  }

  async notifyUserAboutInsuranceRequirements(
    userId: string, 
    vehicleId: string
  ) {
    const user = await this.prisma.user.findUnique({ 
      where: { id: userId } 
    });

    const insuranceConfig = await this.getInsuranceConfigurationForVehicle(vehicleId);
    const depositConfig = await this.getDepositConfigurationForVehicle(vehicleId);

    if (user && insuranceConfig && depositConfig) {
      // Skip email notification since method doesn't exist
      console.log('Insurance deposit notification would be sent to:', user.email);
    }
  }

  async generateInsuranceDepositReport(
    startDate: Date, 
    endDate: Date
  ) {
    const vehicles = await this.prisma.vehicle.findMany({
      // Remove date filter since timestamp fields don't exist
      take: 100 // Limit to avoid performance issues
    });

    return {
      totalConfigurations: vehicles.length,
      riskDistribution: this.analyzeRiskDistribution(vehicles),
      coverageBreakdown: this.analyzeCoverageBreakdown(vehicles)
    };
  }

  private analyzeRiskDistribution(report: any) {
    const riskLevels = {
      LOW: 0,
      MEDIUM: 0,
      HIGH: 0
    };

    report.forEach(config => {
      if (config.riskFactor < 0.3) riskLevels.LOW++;
      else if (config.riskFactor < 0.7) riskLevels.MEDIUM++;
      else riskLevels.HIGH++;
    });

    return riskLevels;
  }

  private analyzeCoverageBreakdown(report: any) {
    const coverageTypes = {};
    
    report.forEach(config => {
      const type = config.type;
      coverageTypes[type] = (coverageTypes[type] || 0) + 1;
    });

    return coverageTypes;
  }
}

export default new InsuranceDepositService(); 