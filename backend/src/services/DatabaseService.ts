// =============================================================================
// DATABASE SERVICE
// =============================================================================
// Centralized database operations using Supabase

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { 
  User, 
  Provider, 
  Vehicle, 
  Booking, 
  Payment, 
  Review, 
  Analytics,
  ApiResponse,
  PaginationParams,
  VehicleSearchParams 
} from '../models';

class DatabaseService {
  private supabase: SupabaseClient;

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltrfq.supabase.co';
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';
    
    this.supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('🗄️ DatabaseService initialized');
  }

  // =============================================================================
  // USER OPERATIONS
  // =============================================================================

  async getUser(id: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('User')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Error fetching user:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('❌ Database error in getUser:', error);
      return null;
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('User')
        .select('*')
        .eq('email', email)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // No rows found
        console.error('❌ Error fetching user by email:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('❌ Database error in getUserByEmail:', error);
      return null;
    }
  }

  async createUser(userData: Partial<User>): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('User')
        .insert([userData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating user:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('❌ Database error in createUser:', error);
      return null;
    }
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('User')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating user:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('❌ Database error in updateUser:', error);
      return null;
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('User')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('❌ Error deleting user:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ Database error in deleteUser:', error);
      return false;
    }
  }

  // =============================================================================
  // VEHICLE OPERATIONS
  // =============================================================================

  async getVehicles(params: VehicleSearchParams = {}): Promise<ApiResponse<Vehicle[]>> {
    try {
      const { 
        page = 1, 
        limit = 20, 
        category, 
        city, 
        minPrice, 
        maxPrice,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = params;

      let query = this.supabase
        .from('Vehicle')
        .select('*', { count: 'exact' })
        .gt('availableUnits', 0);

      // Apply filters
      if (category) {
        query = query.eq('category', category);
      }
      
      if (city) {
        query = query.ilike('location_city', `%${city}%`);
      }
      
      if (minPrice) {
        query = query.gte('daily_rate', minPrice);
      }
      
      if (maxPrice) {
        query = query.lte('daily_rate', maxPrice);
      }

      // Apply sorting and pagination
      query = query
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Error fetching vehicles:', error);

        // Return mock data if table doesn't exist
        if (error.code === '42P01') {
          console.log('📝 Vehicles table not found, returning mock data');
          return {
            success: true,
            data: this.getMockVehicles(),
            pagination: {
              page,
              limit,
              total: 5,
              totalPages: 1
            }
          };
        }

        return {
          success: false,
          error: 'Failed to fetch vehicles'
        };
      }

      return {
        success: true,
        data: data as Vehicle[],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      console.error('❌ Database error in getVehicles:', error);
      return {
        success: false,
        error: 'Database error'
      };
    }
  }

  async getVehicle(id: string): Promise<Vehicle | null> {
    try {
      const { data, error } = await this.supabase
        .from('Vehicle')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ Error fetching vehicle:', error);

        // Return mock data if table doesn't exist
        if (error.code === '42P01') {
          const mockVehicles = this.getMockVehicles();
          return mockVehicles.find(v => v.id === id) || null;
        }

        return null;
      }

      return data as Vehicle;
    } catch (error) {
      console.error('❌ Database error in getVehicle:', error);
      return null;
    }
  }

  async createVehicle(vehicleData: Partial<Vehicle>): Promise<Vehicle | null> {
    try {
      const { data, error } = await this.supabase
        .from('Vehicle')
        .insert([vehicleData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating vehicle:', error);
        return null;
      }

      return data as Vehicle;
    } catch (error) {
      console.error('❌ Database error in createVehicle:', error);
      return null;
    }
  }

  async updateVehicle(id: string, updates: Partial<Vehicle>): Promise<Vehicle | null> {
    try {
      const { data, error } = await this.supabase
        .from('Vehicle')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating vehicle:', error);
        return null;
      }

      return data as Vehicle;
    } catch (error) {
      console.error('❌ Database error in updateVehicle:', error);
      return null;
    }
  }

  // =============================================================================
  // BOOKING OPERATIONS
  // =============================================================================

  async getBookings(params: PaginationParams & { userId?: string; providerId?: string; status?: string } = {}): Promise<ApiResponse<Booking[]>> {
    try {
      const { page = 1, limit = 20, userId, providerId, status } = params;

      let query = this.supabase
        .from('Booking')
        .select('*', { count: 'exact' });

      if (userId) {
        query = query.eq('userId', userId);
      }
      
      if (providerId) {
        query = query.eq('providerId', providerId);
      }
      
      if (status) {
        query = query.eq('status', status);
      }

      query = query
        .order('createdAt', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('❌ Error fetching bookings:', error);
        return {
          success: false,
          error: 'Failed to fetch bookings'
        };
      }

      return {
        success: true,
        data: data as Booking[],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      console.error('❌ Database error in getBookings:', error);
      return {
        success: false,
        error: 'Database error'
      };
    }
  }

  async createBooking(bookingData: Partial<Booking>): Promise<Booking | null> {
    try {
      const { data, error } = await this.supabase
        .from('Booking')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating booking:', error);
        return null;
      }

      return data as Booking;
    } catch (error) {
      console.error('❌ Database error in createBooking:', error);
      return null;
    }
  }

  async updateBooking(id: string, updates: Partial<Booking>): Promise<Booking | null> {
    try {
      const { data, error } = await this.supabase
        .from('Booking')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating booking:', error);
        return null;
      }

      return data as Booking;
    } catch (error) {
      console.error('❌ Database error in updateBooking:', error);
      return null;
    }
  }

  // =============================================================================
  // ANALYTICS OPERATIONS
  // =============================================================================

  async getAnalytics(providerId?: string, startDate?: string, endDate?: string): Promise<Analytics | null> {
    try {
      // This is a simplified version - in production you'd have more complex analytics queries
      let query = this.supabase
        .from('Booking')
        .select('*')
        .gte('createdAt', startDate || '2024-01-01')
        .lte('createdAt', endDate || new Date().toISOString());

      if (providerId) {
        query = query.eq('providerId', providerId);
      }

      const { data: bookings, error } = await query;

      if (error) {
        console.error('❌ Error fetching analytics:', error);
        return null;
      }

      // Calculate analytics from bookings data
      const totalBookings = bookings?.length || 0;
      const totalRevenue = bookings?.reduce((sum, booking) => sum + (booking.totalPrice || 0), 0) || 0;
      const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;

      return {
        id: `analytics_${Date.now()}`,
        date: new Date().toISOString().split('T')[0],
        provider_id: providerId,
        total_bookings: totalBookings,
        total_revenue: totalRevenue,
        total_vehicles: 0, // Would need separate query
        active_vehicles: 0, // Would need separate query
        occupancy_rate: 0, // Would need calculation
        average_booking_value: averageBookingValue,
        created_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Database error in getAnalytics:', error);
      return null;
    }
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  async testConnection(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('User')
        .select('count')
        .limit(1);

      return !error;
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      return false;
    }
  }

  // =============================================================================
  // MOCK DATA METHODS
  // =============================================================================

  private getMockVehicles(): Vehicle[] {
    return [
      {
        id: 'mock-vehicle-1',
        provider_id: 'mock-provider-1',
        make: 'Honda',
        model: 'Scoopy',
        year: 2023,
        category: 'small_scooter',
        daily_rate: 75000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jakarta Pusat',
        description: 'Perfect city scooter for daily commuting',
        features: ['Helmet included', 'Fuel efficient', 'Easy to park'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        rating: 4.5,
        total_reviews: 12,
        total_bookings: 25,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true
      },
      {
        id: 'mock-vehicle-2',
        provider_id: 'mock-provider-1',
        make: 'Yamaha',
        model: 'NMAX',
        year: 2023,
        category: 'large_scooter',
        daily_rate: 120000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jakarta Selatan',
        description: 'Comfortable scooter for longer rides',
        features: ['Helmet included', 'Storage space', 'Comfortable seat'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        rating: 4.7,
        total_reviews: 18,
        total_bookings: 32,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true
      },
      {
        id: 'mock-vehicle-3',
        provider_id: 'mock-provider-2',
        make: 'Kawasaki',
        model: 'Ninja 250',
        year: 2022,
        category: 'luxury_bike',
        daily_rate: 250000,
        status: 'available',
        location_city: 'Jakarta',
        location_address: 'Jakarta Barat',
        description: 'Sport bike for enthusiasts',
        features: ['Full gear included', 'High performance', 'Sport riding position'],
        images: ['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'],
        rating: 4.8,
        total_reviews: 8,
        total_bookings: 15,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true
      }
    ] as Vehicle[];
  }
}

// Export singleton instance
export default new DatabaseService();
