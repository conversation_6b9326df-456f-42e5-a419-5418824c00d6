import { PrismaClient, PayoutRequest, User, Booking } from '@prisma/client';
import Stripe from 'stripe';

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

export interface CreatePayoutRequestData {
  providerId: string;
  bookingId: string;
  amount: number;
  method: 'stripe' | 'bank' | 'manual';
  description?: string;
}

export interface PayoutRequestWithDetails extends PayoutRequest {
  provider: User;
  booking: Booking;
}

export class PayoutService {
  /**
   * Create a new payout request
   */
  async createPayoutRequest(data: CreatePayoutRequestData): Promise<PayoutRequest> {
    const { providerId, bookingId, amount, method, description } = data;

    // Verify the booking exists and belongs to the provider
    const booking = await prisma.booking.findFirst({
      where: {
        id: bookingId,
        vehicle: {
          providerId: providerId
        }
      },
      include: {
        vehicle: true
      }
    });

    if (!booking) {
      throw new Error('Booking not found or does not belong to provider');
    }

    // Check if booking has been confirmed or completed (required for payout)
    if (booking.status !== 'CONFIRMED' && booking.status !== 'COMPLETED') {
      throw new Error('Payout can only be requested after booking confirmation');
    }

    // Check if payout already exists for this booking
    const existingPayout = await prisma.payoutRequest.findFirst({
      where: {
        bookingId: bookingId,
        providerId: providerId
      }
    });

    if (existingPayout) {
      throw new Error('Payout request already exists for this booking');
    }

    // Create payout request
    const payoutRequest = await prisma.payoutRequest.create({
      data: {
        providerId,
        bookingId,
        amount,
        method,
        status: 'PENDING',
        // description field not available in schema
      }
    });

    return payoutRequest;
  }

  /**
   * Process a payout request
   */
  async processPayoutRequest(payoutId: string): Promise<PayoutRequest> {
    const payoutRequest = await prisma.payoutRequest.findUnique({
      where: { id: payoutId },
      include: {
        provider: true,
        booking: true
      }
    });

    if (!payoutRequest) {
      throw new Error('Payout request not found');
    }

    if (payoutRequest.status !== 'PENDING') {
      throw new Error('Payout request is not pending');
    }

    try {
      let processedPayout: PayoutRequest;

      switch (payoutRequest.method) {
        case 'stripe':
          processedPayout = await this.processStripePayout(payoutRequest);
          break;
        case 'bank':
          processedPayout = await this.processBankPayout(payoutRequest);
          break;
        case 'manual':
          processedPayout = await this.processManualPayout(payoutRequest);
          break;
        default:
          throw new Error('Invalid payout method');
      }

      return processedPayout;
    } catch (error) {
      // Update status to failed
      await prisma.payoutRequest.update({
        where: { id: payoutId },
        data: {
          status: 'FAILED'
          // processedAt, notes fields not available in schema
        }
      });
      throw error;
    }
  }

  /**
   * Process Stripe payout
   */
  private async processStripePayout(payoutRequest: PayoutRequestWithDetails): Promise<PayoutRequest> {
    if (!payoutRequest.provider.stripeAccountId) {
      throw new Error('Provider does not have a Stripe account configured');
    }

    // Create transfer to connected account
    const transfer = await stripe.transfers.create({
      amount: Math.round(payoutRequest.amount * 100), // Convert to cents
      currency: 'usd',
      destination: payoutRequest.provider.stripeAccountId,
      description: `Payout for booking ${payoutRequest.bookingId}`,
    });

    // Update payout request
    const updatedPayout = await prisma.payoutRequest.update({
      where: { id: payoutRequest.id },
      data: {
        status: 'COMPLETED'
        // processedAt, transactionId, notes fields not available in schema
      }
    });

    return updatedPayout;
  }

  /**
   * Process bank payout
   */
  private async processBankPayout(payoutRequest: PayoutRequestWithDetails): Promise<PayoutRequest> {
    if (!payoutRequest.provider.bankAccountNumber || !payoutRequest.provider.bankName) {
      throw new Error('Provider does not have bank account details configured');
    }

    // In a real implementation, you would integrate with a bank transfer service
    // For now, we'll simulate the process
    const transactionId = `BANK_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Update payout request
    const updatedPayout = await prisma.payoutRequest.update({
      where: { id: payoutRequest.id },
      data: {
        status: 'COMPLETED'
        // processedAt, transactionId, notes fields not available in schema
      }
    });

    return updatedPayout;
  }

  /**
   * Process manual payout
   */
  private async processManualPayout(payoutRequest: PayoutRequestWithDetails): Promise<PayoutRequest> {
    // For manual payouts, we just mark it as pending manual processing
    const updatedPayout = await prisma.payoutRequest.update({
      where: { id: payoutRequest.id },
      data: {
        status: 'PENDING_MANUAL'
        // processedAt, notes fields not available in schema
      }
    });

    return updatedPayout;
  }

  /**
   * Get payout requests for a provider
   */
  async getProviderPayouts(providerId: string): Promise<PayoutRequestWithDetails[]> {
    return await prisma.payoutRequest.findMany({
      where: { providerId },
      include: {
        provider: true,
        booking: {
          include: {
            vehicle: true,
            user: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get all payout requests (admin only)
   */
  async getAllPayouts(): Promise<PayoutRequestWithDetails[]> {
    return await prisma.payoutRequest.findMany({
      include: {
        provider: true,
        booking: {
          include: {
            vehicle: true,
            user: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get payout request by ID
   */
  async getPayoutById(payoutId: string): Promise<PayoutRequestWithDetails | null> {
    return await prisma.payoutRequest.findUnique({
      where: { id: payoutId },
      include: {
        provider: true,
        booking: {
          include: {
            vehicle: true,
            user: true
          }
        }
      }
    });
  }

  /**
   * Update payout request status (admin only)
   */
  async updatePayoutStatus(payoutId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'PENDING_MANUAL', notes?: string): Promise<PayoutRequest> {
    return await prisma.payoutRequest.update({
      where: { id: payoutId },
      data: {
        status
        // notes, processedAt fields not available in schema
      }
    });
  }
}

export default PayoutService;