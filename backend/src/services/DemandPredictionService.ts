import { PrismaClient } from '@prisma/client';
import * as tf from '@tensorflow/tfjs-node';
import { AppError } from '../middleware/errorMiddleware';

export interface DemandPredictionFeatures {
    vehicleId: string;
    category: string;
    location: string;
    seasonalFactor: number;
    historicalBookings: number;
    averageRating: number;
    pricePoint: number;
    dayOfWeek: number;
    timeOfYear: number;
    previousCancellationRate: number;
}

export interface DemandPrediction {
    vehicleId: string;
    predictedBookings: number;
    confidenceScore: number;
    recommendedPricing: {
        basePrice: number;
        dynamicPriceAdjustment: number;
    };
    demandIntensity: 'low' | 'medium' | 'high';
}

export class DemandPredictionService {
    private prisma: PrismaClient;
    private demandModel: tf.LayersModel | null = null;

    constructor() {
        this.prisma = new PrismaClient();
    }

    /**
     * Prepare training data for demand prediction
     */
    async prepareTrainingData(): Promise<{
        features: number[][];
        labels: number[];
    }> {
        try {
            // Fetch historical booking data
            const bookings = await this.prisma.booking.findMany({
                include: {
                    vehicle: true
                },
                where: {
                    status: 'COMPLETED'
                }
            });

            const features: number[][] = [];
            const labels: number[] = [];

            for (const booking of bookings) {
                // Calculate booking duration in days
                const durationMs = booking.endDate.getTime() - booking.startDate.getTime();
                const durationDays = Math.ceil(durationMs / (1000 * 60 * 60 * 24));
                
                // Extract features (simplified since we don't have all the properties in the schema)
                const feature = [
                    booking.vehicleId ? 1 : 0, // Simple vehicle encoding
                    0, // location placeholder
                    this.calculateSeasonalFactor(booking.startDate),
                    durationDays,
                    0, // vehicle rating placeholder
                    Number(booking.totalPrice),
                    booking.startDate.getDay(),
                    this.calculateTimeOfYear(booking.startDate),
                    0 // cancellation rate placeholder
                ];

                features.push(feature);
                labels.push(durationDays);  // Predict booking duration as a proxy for demand
            }

            return { features, labels };
        } catch (error) {
            throw new AppError('Failed to prepare demand prediction training data', 500);
        }
    }

    /**
     * Train machine learning model for demand prediction
     */
    async trainDemandPredictionModel() {
        try {
            const { features, labels } = await this.prepareTrainingData();

            // Normalize features
            const featureTensor = tf.tensor2d(features);
            const labelTensor = tf.tensor1d(labels);

            // Create and compile the model
            this.demandModel = tf.sequential({
                layers: [
                    tf.layers.dense({
                        inputShape: [features[0].length],
                        units: 64,
                        activation: 'relu'
                    }),
                    tf.layers.dense({
                        units: 32,
                        activation: 'relu'
                    }),
                    tf.layers.dense({
                        units: 16,
                        activation: 'relu'
                    }),
                    tf.layers.dense({
                        units: 1,
                        activation: 'linear'
                    })
                ]
            });

            this.demandModel.compile({
                optimizer: tf.train.adam(0.001),
                loss: 'meanSquaredError',
                metrics: ['mae']
            });

            // Train the model
            await this.demandModel.fit(featureTensor, labelTensor, {
                epochs: 150,
                batchSize: 32,
                validationSplit: 0.2
            });

            // Save the model
            await this.demandModel.save('file://./demand_prediction_model');

            return {
                status: 'Demand prediction model trained successfully',
                features: features[0].length
            };
        } catch (error) {
            throw new AppError('Failed to train demand prediction model', 500);
        }
    }

    /**
     * Predict demand for a specific vehicle
     */
    async predictDemand(
        features: DemandPredictionFeatures
    ): Promise<DemandPrediction> {
        try {
            // Load model if not already loaded
            if (!this.demandModel) {
                this.demandModel = await tf.loadLayersModel(
                    'file://./demand_prediction_model/model.json'
                );
            }

            // Prepare input features
            const inputFeature = [
                this.encodeCategory(features.category),
                this.encodeLocation(features.location),
                features.seasonalFactor,
                features.historicalBookings,
                features.averageRating,
                features.pricePoint,
                features.dayOfWeek,
                features.timeOfYear,
                features.previousCancellationRate
            ];

            // Convert to tensor and predict
            const inputTensor = tf.tensor2d([inputFeature]);
            const prediction = this.demandModel.predict(inputTensor) as tf.Tensor;

            // Get predicted bookings
            const predictedBookings = prediction.dataSync()[0];

            // Calculate demand intensity and confidence
            const confidenceScore = this.calculateConfidenceScore(
                predictedBookings, 
                features
            );

            // Dynamic pricing adjustment
            const dynamicPriceAdjustment = this.calculateDynamicPricing(
                predictedBookings, 
                features.pricePoint
            );

            return {
                vehicleId: features.vehicleId,
                predictedBookings,
                confidenceScore,
                recommendedPricing: {
                    basePrice: features.pricePoint,
                    dynamicPriceAdjustment
                },
                demandIntensity: this.determineDemandIntensity(predictedBookings)
            };
        } catch (error) {
            throw new AppError('Failed to predict demand', 500);
        }
    }

    /**
     * Calculate confidence score for prediction
     */
    private calculateConfidenceScore(
        predictedBookings: number, 
        features: DemandPredictionFeatures
    ): number {
        // Multiple factors contribute to confidence
        const historicalBookingsFactor = 
            Math.min(features.historicalBookings / 10, 1);
        const ratingFactor = features.averageRating / 5;
        const seasonalFactor = features.seasonalFactor;
        const cancellationFactor = 1 - features.previousCancellationRate;

        // Weighted confidence calculation
        const confidenceScore = (
            historicalBookingsFactor * 0.3 +
            ratingFactor * 0.25 +
            seasonalFactor * 0.2 +
            cancellationFactor * 0.25
        );

        return Math.min(Math.max(confidenceScore, 0), 1);
    }

    /**
     * Calculate dynamic pricing adjustment
     */
    private calculateDynamicPricing(
        predictedBookings: number, 
        basePrice: number
    ): number {
        // Adjust pricing based on predicted demand
        const demandMultiplier = Math.log(predictedBookings + 1) / 5;
        return basePrice * demandMultiplier;
    }

    /**
     * Determine demand intensity
     */
    private determineDemandIntensity(
        predictedBookings: number
    ): 'low' | 'medium' | 'high' {
        if (predictedBookings < 2) return 'low';
        if (predictedBookings < 5) return 'medium';
        return 'high';
    }

    /**
     * Encoding helper methods
     */
    private encodeCategory(category: string): number {
        const categories = ['economy', 'standard', 'luxury', 'premium'];
        return categories.indexOf(category.toLowerCase()) / categories.length;
    }

    private encodeLocation(location: string): number {
        const locations = ['urban', 'suburban', 'rural'];
        return locations.indexOf(location.toLowerCase()) / locations.length;
    }

    private calculateSeasonalFactor(date: Date): number {
        const month = date.getMonth();
        // Simple seasonal factor calculation
        const seasonalFactors = [
            0.8, 0.85, 0.9, 1.0, 1.1, 1.2, 
            1.3, 1.2, 1.1, 1.0, 0.9, 0.85
        ];
        return seasonalFactors[month];
    }

    private calculateTimeOfYear(date: Date): number {
        // Normalize time of year to 0-1 range
        const dayOfYear = this.getDayOfYear(date);
        return dayOfYear / 365;
    }

    private getDayOfYear(date: Date): number {
        const start = new Date(date.getFullYear(), 0, 0);
        const diff = date.getTime() - start.getTime();
        const oneDay = 1000 * 60 * 60 * 24;
        return Math.floor(diff / oneDay);
    }
}

export default new DemandPredictionService(); 