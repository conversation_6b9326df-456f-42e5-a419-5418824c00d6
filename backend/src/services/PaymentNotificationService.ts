import Stripe from 'stripe';
import {
  Booking,
  BookingStatus
} from '@prisma/client';

// Local PaymentStatus enum since it's not exported from database types
enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}
import { supabase } from '../utils/supabaseClient';
import { 
  EmailService, 
  SMSService, 
  PushNotificationService 
} from './CommunicationServices';

export interface PaymentWebhookEvent {
  type: string;
  data: {
    object: Stripe.PaymentIntent;
  };
}

export class PaymentNotificationService {
  private stripe: Stripe;
  private emailService: EmailService;
  private smsService: SMSService;
  private pushNotificationService: PushNotificationService;

  constructor() {
    // Initialize Stripe with environment variable
    this.stripe = new Stripe(
      process.env.STRIPE_SECRET_KEY || '', 
      { apiVersion: '2025-06-30.basil' }
    );

    // Initialize communication services
    this.emailService = new EmailService();
    this.smsService = new SMSService();
    this.pushNotificationService = new PushNotificationService();
  }

  /**
   * Process Stripe payment webhook
   * @param event Webhook event from Stripe
   */
  async handleStripeWebhook(event: PaymentWebhookEvent): Promise<void> {
    const paymentIntent = event.data.object;

    switch (event.type) {
      case 'payment_intent.succeeded':
        await this.handleSuccessfulPayment(paymentIntent);
        break;
      case 'payment_intent.payment_failed':
        await this.handleFailedPayment(paymentIntent);
        break;
      case 'payment_intent.canceled':
        await this.handleCanceledPayment(paymentIntent);
        break;
    }
  }

  /**
   * Handle successful payment
   * @param paymentIntent Stripe payment intent
   */
  private async handleSuccessfulPayment(
    paymentIntent: Stripe.PaymentIntent
  ): Promise<void> {
    // Find booking associated with payment intent
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntent.id)
      .single();

    if (bookingError || !booking) {
      console.error('Booking not found for payment intent');
      return;
    }

    // Update booking status
    await supabase
      .from('bookings')
      .update({
        payment_status: PaymentStatus.COMPLETED,
        status: BookingStatus.PENDING
      })
      .eq('id', booking.id);

    // Notify user about successful payment
    await this.notifySuccessfulPayment(booking);
  }

  /**
   * Handle failed payment
   * @param paymentIntent Stripe payment intent
   */
  private async handleFailedPayment(
    paymentIntent: Stripe.PaymentIntent
  ): Promise<void> {
    // Find booking associated with payment intent
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntent.id)
      .single();

    if (bookingError || !booking) {
      console.error('Booking not found for payment intent');
      return;
    }

    // Update booking status
    await supabase
      .from('bookings')
      .update({
        payment_status: PaymentStatus.FAILED,
        booking_status: BookingStatus.CANCELLED
      })
      .eq('id', booking.id);

    // Notify user about payment failure
    await this.notifyPaymentFailure(booking);
  }

  /**
   * Handle canceled payment
   * @param paymentIntent Stripe payment intent
   */
  private async handleCanceledPayment(
    paymentIntent: Stripe.PaymentIntent
  ): Promise<void> {
    // Similar to failed payment, but with different messaging
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntent.id)
      .single();

    if (bookingError || !booking) {
      console.error('Booking not found for payment intent');
      return;
    }

    await supabase
      .from('bookings')
      .update({
        payment_status: PaymentStatus.FAILED,
        booking_status: BookingStatus.CANCELLED
      })
      .eq('id', booking.id);

    await this.notifyPaymentCanceled(booking);
  }

  /**
   * Create Stripe payment intent
   * @param booking Booking details
   * @returns Stripe payment intent
   */
  async createPaymentIntent(
    booking: Booking
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(Number(booking.totalPrice) * 100), // Convert to cents
        currency: 'usd', // Adjust based on your primary currency
        payment_method_types: ['card'],
        metadata: {
          booking_id: booking.id,
          user_id: booking.userId
        }
      });

      // Update booking with payment intent ID
      await supabase
        .from('bookings')
        .update({
          stripe_payment_intent_id: paymentIntent.id
        })
        .eq('id', booking.id);

      return paymentIntent;
    } catch (error) {
      console.error('Error creating payment intent', error);
      throw error;
    }
  }

  /**
   * Notify user about successful payment
   * @param booking Booking details
   */
  private async notifySuccessfulPayment(booking: Booking): Promise<void> {
    // Fetch user details
    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', booking.userId)
      .single();

    // Send email notification
    await this.emailService.sendEmail({
      to: user.email,
      subject: 'Payment Successful - Booking Confirmed',
      template: 'payment_success',
      data: { 
        userName: user.first_name,
        bookingId: booking.id,
        totalAmount: booking.totalPrice
      }
    });

    // Send SMS notification
    if (user.phone) {
      await this.smsService.sendSMS({
        to: user.phone,
        message: `Your booking #${booking.id} payment is confirmed. Total: $${booking.totalPrice}`
      });
    }

    // Send push notification
    await this.pushNotificationService.sendPushNotification({
      userId: user.id,
      title: 'Payment Confirmed',
      body: `Your booking #${booking.id} is now confirmed.`
    });
  }

  /**
   * Notify user about payment failure
   * @param booking Booking details
   */
  private async notifyPaymentFailure(booking: Booking): Promise<void> {
    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', booking.userId)
      .single();

    // Email notification
    await this.emailService.sendEmail({
      to: user.email,
      subject: 'Payment Failed - Booking Cancelled',
      template: 'payment_failed',
      data: { 
        userName: user.first_name,
        bookingId: booking.id,
        totalAmount: booking.totalPrice
      }
    });

    // SMS notification
    if (user.phone) {
      await this.smsService.sendSMS({
        to: user.phone,
        message: `Payment for booking #${booking.id} failed. Please retry.`
      });
    }

    // Push notification
    await this.pushNotificationService.sendPushNotification({
      userId: user.id,
      title: 'Payment Failed',
      body: `Booking #${booking.id} payment unsuccessful.`
    });
  }

  /**
   * Notify user about payment cancellation
   * @param booking Booking details
   */
  private async notifyPaymentCanceled(booking: Booking): Promise<void> {
    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', booking.userId)
      .single();

    // Email notification
    await this.emailService.sendEmail({
      to: user.email,
      subject: 'Booking Cancelled',
      template: 'payment_canceled',
      data: { 
        userName: user.first_name,
        bookingId: booking.id
      }
    });

    // SMS notification
    if (user.phone) {
      await this.smsService.sendSMS({
        to: user.phone,
        message: `Booking #${booking.id} has been cancelled.`
      });
    }

    // Push notification
    await this.pushNotificationService.sendPushNotification({
      userId: user.id,
      title: 'Booking Cancelled',
      body: `Booking #${booking.id} has been cancelled.`
    });
  }
}

export default new PaymentNotificationService(); 