import { PrismaClient } from '@prisma/client';
import { EmailService } from './EmailService';
import { NotificationService } from './NotificationService';

export interface VehicleAssistanceRequestData {
  userId: string;
  bookingId?: string;
  providerId?: string;
  vehicleId?: string;
  location?: string;
  message?: string;
  reason: string;
  category: 'BREAKDOWN' | 'ACCIDENT' | 'FLAT_TIRE' | 'FUEL_EMPTY' | 'BATTERY_DEAD' | 'KEY_LOCKED' | 'MECHANICAL_ISSUE' | 'ELECTRICAL_ISSUE' | 'OTHER';
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
}

export interface AssistanceRequestResponse {
  id: string;
  userId: string;
  bookingId?: string;
  providerId?: string;
  vehicleId?: string;
  location?: string;
  message?: string;
  reason: string;
  category: string;
  priority: string;
  status: string;
  adminNotes?: string;
  resolvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export class VehicleAssistanceService {
  public prisma: PrismaClient; // Made public for route access
  private emailService: EmailService;
  private notificationService: NotificationService;

  constructor() {
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    this.notificationService = new NotificationService();
  }

  /**
   * Create a new vehicle assistance request
   */
  async createAssistanceRequest(data: VehicleAssistanceRequestData): Promise<AssistanceRequestResponse> {
    try {
      // Validate required fields
      if (!data.userId || !data.reason || !data.category) {
        throw new Error('Missing required fields: userId, reason, and category are required');
      }

      // Check for existing active assistance request for the same booking
      if (data.bookingId) {
        const existingRequest = await this.prisma.vehicleAssistanceRequest.findFirst({
          where: {
            bookingId: data.bookingId,
            status: {
              in: ['PENDING', 'ASSIGNED', 'IN_PROGRESS']
            }
          }
        });

        if (existingRequest) {
          throw new Error('An active assistance request already exists for this booking');
        }
      }

      // Determine priority based on category
      const priority = this.determinePriority(data.category, data.priority);

      // Create the assistance request
      const assistanceRequest = await this.prisma.vehicleAssistanceRequest.create({
        data: {
          userId: data.userId,
          bookingId: data.bookingId,
          providerId: data.providerId,
          vehicleId: data.vehicleId,
          location: data.location,
          message: data.message,
          reason: data.reason,
          category: data.category,
          priority: priority,
          status: 'PENDING'
        }
      });

      // Send high-priority notifications to admin
      await this.sendAdminNotification(assistanceRequest);

      // Send confirmation to user
      await this.sendUserConfirmation(assistanceRequest);

      // If provider is involved, notify them
      if (data.providerId) {
        await this.sendProviderNotification(assistanceRequest);
      }

      return assistanceRequest as AssistanceRequestResponse;
    } catch (error) {
      console.error('Error creating vehicle assistance request:', error);
      throw error;
    }
  }

  /**
   * Get assistance requests by user
   */
  async getAssistanceRequestsByUser(userId: string): Promise<AssistanceRequestResponse[]> {
    try {
      const requests = await this.prisma.vehicleAssistanceRequest.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        include: {
          booking: {
            select: {
              id: true,
              vehicleId: true,
              startDate: true,
              endDate: true
            }
          },
          vehicle: {
            select: {
              id: true,
              brand: true,
              model: true,
              vehicleType: true
            }
          }
        }
      });

      return requests as AssistanceRequestResponse[];
    } catch (error) {
      console.error('Error fetching assistance requests:', error);
      throw error;
    }
  }

  /**
   * Get assistance requests by provider
   */
  async getAssistanceRequestsByProvider(providerId: string): Promise<AssistanceRequestResponse[]> {
    try {
      const requests = await this.prisma.vehicleAssistanceRequest.findMany({
        where: { providerId },
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true
            }
          },
          booking: {
            select: {
              id: true,
              vehicleId: true,
              startDate: true,
              endDate: true
            }
          },
          vehicle: {
            select: {
              id: true,
              brand: true,
              model: true,
              vehicleType: true
            }
          }
        }
      });

      return requests as AssistanceRequestResponse[];
    } catch (error) {
      console.error('Error fetching provider assistance requests:', error);
      throw error;
    }
  }

  /**
   * Update assistance request status
   */
  async updateAssistanceRequestStatus(
    requestId: string, 
    status: 'PENDING' | 'ASSIGNED' | 'IN_PROGRESS' | 'RESOLVED' | 'CANCELLED',
    adminNotes?: string
  ): Promise<AssistanceRequestResponse> {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date()
      };

      if (adminNotes) {
        updateData.adminNotes = adminNotes;
      }

      if (status === 'RESOLVED') {
        updateData.resolvedAt = new Date();
      }

      const updatedRequest = await this.prisma.vehicleAssistanceRequest.update({
        where: { id: requestId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phoneNumber: true
            }
          }
        }
      });

      // Notify user of status change
      await this.sendStatusUpdateNotification(updatedRequest);

      return updatedRequest as AssistanceRequestResponse;
    } catch (error) {
      console.error('Error updating assistance request status:', error);
      throw error;
    }
  }

  /**
   * Get all assistance requests for admin dashboard
   */
  async getAllAssistanceRequests(
    status?: string,
    priority?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ requests: AssistanceRequestResponse[]; total: number }> {
    try {
      const where: any = {};
      
      if (status) {
        where.status = status;
      }
      
      if (priority) {
        where.priority = priority;
      }

      const [requests, total] = await Promise.all([
        this.prisma.vehicleAssistanceRequest.findMany({
          where,
          orderBy: [
            { priority: 'desc' },
            { createdAt: 'desc' }
          ],
          take: limit,
          skip: offset,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phoneNumber: true
              }
            },
            provider: {
              select: {
                id: true,
                name: true,
                email: true,
                phoneNumber: true
              }
            },
            booking: {
              select: {
                id: true,
                vehicleId: true,
                startDate: true,
                endDate: true
              }
            },
            vehicle: {
              select: {
                id: true,
                brand: true,
                model: true,
                vehicleType: true
              }
            }
          }
        }),
        this.prisma.vehicleAssistanceRequest.count({ where })
      ]);

      return { requests: requests as AssistanceRequestResponse[], total };
    } catch (error) {
      console.error('Error fetching all assistance requests:', error);
      throw error;
    }
  }

  /**
   * Determine priority based on category
   */
  private determinePriority(
    category: string, 
    requestedPriority?: string
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
    // Override with requested priority if provided
    if (requestedPriority) {
      return requestedPriority as 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    }

    // Auto-determine based on category
    switch (category) {
      case 'ACCIDENT':
        return 'URGENT';
      case 'BREAKDOWN':
      case 'MECHANICAL_ISSUE':
      case 'ELECTRICAL_ISSUE':
        return 'HIGH';
      case 'FLAT_TIRE':
      case 'BATTERY_DEAD':
      case 'FUEL_EMPTY':
        return 'MEDIUM';
      case 'KEY_LOCKED':
      case 'OTHER':
      default:
        return 'LOW';
    }
  }

  /**
   * Send high-priority notification to admin
   */
  private async sendAdminNotification(request: any): Promise<void> {
    try {
      const subject = `🚨 ${request.priority} Vehicle Assistance Request - ${request.category}`;
      const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

      await this.emailService.sendEmail({
        to: adminEmail,
        subject,
        template: 'vehicle_assistance_alert',
        data: {
          requestId: request.id,
          category: request.category,
          priority: request.priority,
          reason: request.reason,
          location: request.location,
          message: request.message,
          bookingId: request.bookingId,
          createdAt: request.createdAt
        }
      });

      console.log(`Admin notification sent for assistance request: ${request.id}`);
    } catch (error) {
      console.error('Error sending admin notification:', error);
    }
  }

  /**
   * Send confirmation to user
   */
  private async sendUserConfirmation(request: any): Promise<void> {
    try {
      // Get user details
      const user = await this.prisma.user.findUnique({
        where: { id: request.userId },
        select: { email: true, name: true }
      });

      if (user?.email) {
        await this.emailService.sendEmail({
          to: user.email,
          subject: 'Vehicle Assistance Request Received',
          template: 'assistance_confirmation',
          data: {
            userName: user.name,
            requestId: request.id,
            category: request.category,
            priority: request.priority,
            estimatedResponse: this.getEstimatedResponseTime(request.priority)
          }
        });
      }
    } catch (error) {
      console.error('Error sending user confirmation:', error);
    }
  }

  /**
   * Send notification to provider
   */
  private async sendProviderNotification(request: any): Promise<void> {
    try {
      if (!request.providerId) return;

      const provider = await this.prisma.user.findUnique({
        where: { id: request.providerId },
        select: { email: true, name: true }
      });

      if (provider?.email) {
        await this.emailService.sendEmail({
          to: provider.email,
          subject: 'Vehicle Assistance Request for Your Vehicle',
          template: 'provider_assistance_notification',
          data: {
            providerName: provider.name,
            requestId: request.id,
            category: request.category,
            priority: request.priority,
            bookingId: request.bookingId
          }
        });
      }
    } catch (error) {
      console.error('Error sending provider notification:', error);
    }
  }

  /**
   * Send status update notification
   */
  private async sendStatusUpdateNotification(request: any): Promise<void> {
    try {
      if (request.user?.email) {
        await this.emailService.sendEmail({
          to: request.user.email,
          subject: `Vehicle Assistance Request Update - ${request.status}`,
          template: 'assistance_status_update',
          data: {
            userName: request.user.name,
            requestId: request.id,
            status: request.status,
            adminNotes: request.adminNotes,
            resolvedAt: request.resolvedAt
          }
        });
      }
    } catch (error) {
      console.error('Error sending status update notification:', error);
    }
  }

  /**
   * Get estimated response time based on priority
   */
  private getEstimatedResponseTime(priority: string): string {
    switch (priority) {
      case 'URGENT':
        return '15-30 minutes';
      case 'HIGH':
        return '30-60 minutes';
      case 'MEDIUM':
        return '1-2 hours';
      case 'LOW':
      default:
        return '2-4 hours';
    }
  }
}

export default VehicleAssistanceService;
