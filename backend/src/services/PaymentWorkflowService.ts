import { Booking, User } from '@prisma/client';
import * as StripeService from './StripeService';
import { PayPalService } from './PayPalService';
import FeeCalculationService from './FeeCalculationService';
import { supabase } from '../utils/supabaseClient';
import { logger } from '../utils/logger';

// Local enums since not exported from Prisma
enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

enum RefundReason {
  CUSTOMER_REQUEST = 'CUSTOMER_REQUEST',
  VEHICLE_UNAVAILABLE = 'VEHICLE_UNAVAILABLE',
  SERVICE_ISSUE = 'SERVICE_ISSUE'
}

export interface PaymentData {
  booking: Booking;
  customer: any;
  paymentMethod: 'stripe' | 'paypal' | 'bank_transfer' | 'cash';
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  transactionId?: string;
  amount?: number;
  status: PaymentStatus;
  error?: string;
}

export class PaymentWorkflowService {
  private logger: typeof logger;
  private paypalService: PayPalService;

  constructor() {
    this.logger = logger;
    this.paypalService = new PayPalService();
  }

  /**
   * Initialize payment for a booking
   * @param paymentData Payment and booking details
   */
  async initializePayment(paymentData: PaymentData): Promise<PaymentResult> {
    try {
      const feeCalculation = FeeCalculationService.calculateBookingFees(
        Number(paymentData.booking.totalPrice),
        (paymentData.booking.endDate.getTime() - paymentData.booking.startDate.getTime()) / (1000 * 3600 * 24)
      );
      const customerEmail = paymentData.customer.email || '';
      const customerName = `${paymentData.customer.firstName || ''} ${paymentData.customer.lastName || ''}`;

      let paymentResult;
      switch (paymentData.paymentMethod) {
        case 'stripe':
          paymentResult = await StripeService.createPaymentIntent({
            amount: feeCalculation.finalTotal,
            currency: 'USD',
            customerEmail,
            customerName,
            description: `Booking for Vehicle ${paymentData.booking.vehicleId}`,
            bookingId: paymentData.booking.id,
            providerId: paymentData.booking.providerId
          });
          break;
        case 'paypal':
          paymentResult = await this.paypalService.createPayment(paymentData.booking);
          break;
        case 'bank_transfer':
          paymentResult = await this.handleBankTransfer(paymentData);
          break;
        case 'cash':
          paymentResult = await this.handleCashPayment(paymentData);
          break;
        default:
          throw new Error('Unsupported payment method');
      }

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment initialization failed');
      }

      // Store payment record in database
      const { data: paymentRecord, error: insertError } = await supabase
        .from('payments')
        .insert({
          booking_id: paymentData.booking.id,
          customer_id: paymentData.customer.id,
          payment_method: paymentData.paymentMethod,
          payment_gateway_id: paymentResult.paymentIntentId,
          base_rental_cost: feeCalculation.baseRentalCost,
          booking_fee_rate: feeCalculation.bookingFeeRate,
          booking_fee_amount: feeCalculation.bookingFee,
          discount_amount: feeCalculation.discountAmount || 0,
          total_amount: feeCalculation.finalTotal,
          status: 'pending',
          gateway_response: paymentResult
        })
        .select('id')
        .single();

      if (insertError) {
        throw insertError;
      }

      return {
        success: true,
        paymentId: paymentRecord.id,
        transactionId: paymentResult.paymentIntentId,
        amount: feeCalculation.finalTotal,
        status: PaymentStatus.PENDING,
        // Include client secret for frontend payment processing
        ...(paymentResult.clientSecret && { 
          clientSecret: paymentResult.clientSecret 
        })
      };

    } catch (error) {
      this.logger.error('Payment initialization failed', error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Confirm payment after successful transaction
   * @param paymentId Payment record ID
   * @param transactionId Gateway transaction ID
   * @param paymentMethod Payment method used
   */
  async confirmPayment(
    paymentId: string, 
    transactionId: string,
    paymentMethod: 'stripe' | 'paypal'
  ): Promise<PaymentResult> {
    try {
      let confirmResult;
      switch (paymentMethod) {
        case 'stripe':
          confirmResult = await StripeService.confirmPayment(transactionId);
          break;
        case 'paypal':
          // For PayPal, we might need additional parameters like payerId
          // This is a simplified example
          confirmResult = await this.paypalService.executePayment(
            transactionId
          );
          break;
        default:
          throw new Error('Unsupported payment method');
      }

      if (!confirmResult.success) {
        throw new Error(confirmResult.error || 'Payment confirmation failed');
      }

      // Update payment record
      const { error: updateError } = await supabase
        .from('payments')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          gateway_response: confirmResult
        })
        .eq('id', paymentId);

      if (updateError) {
        throw updateError;
      }

      // Process payment split
      await this.processPaymentSplit(paymentId);

      return {
        success: true,
        paymentId,
        transactionId,
        status: PaymentStatus.COMPLETED
      };

    } catch (error) {
      this.logger.error('Payment confirmation failed', error);
      return {
        success: false,
        paymentId,
        transactionId,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process payment split between platform and vehicle owner
   * @param paymentId Payment record ID
   */
  private async processPaymentSplit(paymentId: string): Promise<void> {
    try {
      // Fetch payment details
      const { data: payment, error: fetchError } = await supabase
        .from('payments')
        .select('*')
        .eq('id', paymentId)
        .single();

      if (fetchError || !payment) {
        throw new Error('Payment not found');
      }

      // Calculate platform fee and owner payout
      const platformFeePercentage = 0.15; // 15% platform fee
      const platformFee = Math.round(payment.total_amount * platformFeePercentage);
      const ownerPayout = payment.total_amount - platformFee;

      // Record payment split
      await supabase
        .from('payment_splits')
        .insert({
          payment_id: paymentId,
          platform_fee: platformFee,
          owner_payout: ownerPayout,
          vehicle_owner_id: payment.provider_id,
          status: 'pending'
        });

    } catch (error) {
      logger.info('Error in updateBookingStatus:', error);
      // Log error but don't throw to prevent blocking payment confirmation
    }
  }

  /**
   * Process refund for a payment
   * @param paymentId Payment record ID
   * @param refundReason Reason for refund
   */
  async processRefund(
    paymentId: string, 
    refundReason: RefundReason
  ): Promise<PaymentResult> {
    try {
      // Fetch payment details
      const { data: payment, error: fetchError } = await supabase
        .from('payments')
        .select('*')
        .eq('id', paymentId)
        .single();

      if (fetchError || !payment) {
        throw new Error('Payment not found');
      }

      let refundResult;
      switch (payment.payment_method) {
        case 'stripe':
          // TODO: Implement refundPayment method in StripeService
          refundResult = {
            success: true,
            refundId: 'stripe_refund_' + Date.now(),
            amount: payment.total_amount
          };
          break;
        case 'paypal':
          refundResult = await this.paypalService.refundPayment(
            payment.payment_gateway_id, 
            payment.total_amount
          );
          break;
        default:
          throw new Error('Unsupported payment method for refund');
      }

      if (!refundResult.success) {
        throw new Error(refundResult.error || 'Refund processing failed');
      }

      // Update payment and refund records
      await supabase
        .from('payments')
        .update({
          status: 'refunded',
          refund_reason: refundReason
        })
        .eq('id', paymentId);

      return {
        success: true,
        paymentId,
        transactionId: refundResult.paymentIntentId,
        amount: payment.total_amount,
        status: PaymentStatus.REFUNDED
      };

    } catch (error) {
      logger.info('Error in processRefund:', error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async handleBankTransfer(paymentData: PaymentData): Promise<PaymentResult> {
    // Logic for bank transfer, e.g., generate reference and update status
    return { success: true, status: PaymentStatus.PENDING, transactionId: 'bank_ref_' + Date.now() };
  }

  private async handleCashPayment(paymentData: PaymentData): Promise<PaymentResult> {
    // Logic for cash, e.g., mark as pending for provider confirmation
    return { success: true, status: PaymentStatus.PENDING, transactionId: 'cash_ref_' + Date.now() };
  }
}

export default PaymentWorkflowService;
