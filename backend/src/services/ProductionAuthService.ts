// =============================================================================
// PRODUCTION AUTHENTICATION SERVICE
// =============================================================================
// JWT-based authentication with role-based access control

import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Request, Response, NextFunction } from 'express';
import DatabaseService from './DatabaseService';
import { User, UserRole, PrismaClient } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';

const prisma = new PrismaClient();

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.SUPABASE_URL || 'https://rocxjzukyqelvuyltrfq.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU'
);

interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

interface AuthenticatedRequest extends Request {
  user?: User & {
    getDisplayName?(): string;
  };
}

class ProductionAuthService {
  private jwtSecret: string;
  private jwtExpiration: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
    this.jwtExpiration = process.env.JWT_EXPIRATION || '24h';
    
    if (process.env.NODE_ENV === 'production' && this.jwtSecret === 'your-super-secret-jwt-key-change-in-production') {
      console.warn('⚠️ WARNING: Using default JWT secret in production! Please set JWT_SECRET environment variable.');
    }
    
    console.log('🔐 ProductionAuthService initialized');
  }

  // =============================================================================
  // PASSWORD OPERATIONS
  // =============================================================================

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  // =============================================================================
  // JWT OPERATIONS
  // =============================================================================

  generateToken(user: User): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      role: user.role as UserRole
    };

    return jwt.sign(payload as any, this.jwtSecret, {
      expiresIn: this.jwtExpiration
    } as any);
  }

  verifyToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'rentahub-api',
        audience: 'rentahub-app'
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      console.error('❌ JWT verification failed:', error);
      return null;
    }
  }

  // =============================================================================
  // AUTHENTICATION METHODS
  // =============================================================================

  async register(userData: {
    email: string;
    password: string;
    first_name?: string;
    last_name?: string;
    phone?: string;
    role?: UserRole;
  }): Promise<{ success: boolean; user?: User; token?: string; error?: string }> {
    try {
      // Check if user already exists
      const existingUser = await DatabaseService.getUserByEmail(userData.email);
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists'
        };
      }

      // Hash password
      const hashedPassword = await this.hashPassword(userData.password);

      // Create user in Supabase Auth first
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true // Auto-confirm email for development
      });

      if (authError) {
        console.error('❌ Supabase Auth user creation failed:', authError);
        return {
          success: false,
          error: authError.message || 'Failed to create auth user'
        };
      }

      // Create user in database
      const newUser = await prisma.user.create({
        data: {
          id: authData.user.id, // Use Supabase Auth user ID
          email: userData.email,
          password: hashedPassword,
          name: userData.email.split('@')[0], // Use email prefix as name
          phoneNumber: userData.phone,
          role: userData.role || UserRole.CUSTOMER,
          emailVerified: false,
          phoneVerified: false
        }
      });

      if (!newUser) {
        return {
          success: false,
          error: 'Failed to create user'
        };
      }

      // Generate token
      const token = this.generateToken(newUser);

      // Remove password from response
      const { password_hash, ...userWithoutPassword } = newUser as any;

      return {
        success: true,
        user: userWithoutPassword,
        token
      };
    } catch (error) {
      console.error('❌ Registration error:', error);
      return {
        success: false,
        error: 'Registration failed'
      };
    }
  }

  async login(email: string, password: string): Promise<{ success: boolean; user?: User; token?: string; error?: string }> {
    try {
      // Find user by email
      const user = await DatabaseService.getUserByEmail(email);
      if (!user) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Check if user is active
      if (!user.is_active) {
        return {
          success: false,
          error: 'Account is deactivated'
        };
      }

      // Verify password
      const isPasswordValid = await this.comparePassword(password, (user as any).password_hash);
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid email or password'
        };
      }

      // Update last login
      await DatabaseService.updateUser(user.id, {
        last_login: new Date().toISOString()
      });

      // Generate token - cast user to expected type
      const token = this.generateToken(user as any);

      // Remove password from response
      const { password_hash, ...userWithoutPassword } = user as any;

      return {
        success: true,
        user: userWithoutPassword,
        token
      };
    } catch (error) {
      console.error('❌ Login error:', error);
      return {
        success: false,
        error: 'Login failed'
      };
    }
  }

  // =============================================================================
  // MIDDLEWARE
  // =============================================================================

  authenticateToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

      if (!token) {
        res.status(401).json({
          success: false,
          error: 'Access token required'
        });
        return;
      }

      const decoded = this.verifyToken(token);
      if (!decoded) {
        res.status(403).json({
          success: false,
          error: 'Invalid or expired token'
        });
        return;
      }

      // Get fresh user data
      const user = await DatabaseService.getUser(decoded.userId);
      if (!user || !user.is_active) {
        res.status(403).json({
          success: false,
          error: 'User not found or inactive'
        });
        return;
      }

      req.user = user as any; // Cast to expected type
      next();
    } catch (error) {
      console.error('❌ Authentication middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Authentication failed'
      });
    }
  };

  requireRole = (roles: UserRole | UserRole[]) => {
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      if (!allowedRoles.includes(req.user.role as UserRole)) {
        res.status(403).json({
          success: false,
          error: 'Insufficient permissions'
        });
        return;
      }

      next();
    };
  };

  /**
   * Change user password
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<{ success: boolean; message?: string }> {
    try {
      // Get user with current password
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        return { success: false, message: 'User not found' };
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password || '');
      if (!isCurrentPasswordValid) {
        return { success: false, message: 'Current password is incorrect' };
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await prisma.user.update({
        where: { id: userId },
        data: { password: hashedNewPassword }
      });

      return { success: true, message: 'Password changed successfully' };
    } catch (error) {
      console.error('Error changing password:', error);
      return { success: false, message: 'Internal server error' };
    }
  }

  requireAdmin = this.requireRole([UserRole.ADMIN]);
  requireProvider = this.requireRole([UserRole.PROVIDER, UserRole.ADMIN]);
  requireCustomer = this.requireRole([UserRole.CUSTOMER, UserRole.PROVIDER, UserRole.ADMIN]);
}

// Export singleton instance
export default new ProductionAuthService();
export { AuthenticatedRequest };
