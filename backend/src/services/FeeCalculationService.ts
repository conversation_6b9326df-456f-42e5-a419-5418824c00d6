import { Booking } from '@prisma/client';

// Define enums locally since they don't exist in Prisma
export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

export enum RefundReason {
  CANCELLATION = 'CANCELLATION',
  DAMAGE = 'DAMAGE',
  DISPUTE = 'DISPUTE',
  OTHER = 'OTHER'
}

export interface BookingWithRates extends Booking {
  dailyRate?: number;
  rentalDays?: number;
}

export interface PricingCalculation {
  baseRentalCost: number;
  bookingFeeRate: number;
  bookingFee: number;
  totalCustomerPays: number;
  ownerReceives: number;
  rentaHubRevenue: number;
  discountAmount?: number;
  finalTotal: number;
}

export interface RefundCalculation {
  originalAmount: number;
  refundAmount: number;
  rentaHubFeeRefund: number;
  ownerRefundAmount: number;
  refundReason: RefundReason;
}

export class FeeCalculationService {
  private DEFAULT_BOOKING_FEE_RATE = 0.15; // 15% booking fee

  /**
   * Calculate booking fees with detailed breakdown
   * @param baseAmount Base rental cost
   * @param days Number of rental days
   * @param discountCode Optional discount code
   */
  calculateBookingFees(
    baseAmount: number, 
    days: number, 
    discountCode?: string
  ): PricingCalculation {
    const baseRentalCost = baseAmount * days;
    const bookingFeeRate = this.DEFAULT_BOOKING_FEE_RATE;
    const bookingFee = baseAmount * this.DEFAULT_BOOKING_FEE_RATE;
    
    // Placeholder discount logic - replace with actual discount service
    const discountAmount = discountCode 
      ? this.applyDiscount(baseRentalCost, discountCode) 
      : 0;

    const totalCustomerPays = baseRentalCost + bookingFee - discountAmount;
    const ownerReceives = baseRentalCost;
    const rentaHubRevenue = bookingFee;

    return {
      baseRentalCost,
      bookingFeeRate,
      bookingFee,
      totalCustomerPays,
      ownerReceives,
      rentaHubRevenue,
      discountAmount,
      finalTotal: totalCustomerPays
    };
  }

  /**
   * Calculate refund amount based on cancellation timing
   * @param booking Original booking details
   * @param cancellationDate Date of cancellation
   */
  calculateRefundFees(
    booking: BookingWithRates, 
    cancellationDate: Date
  ): RefundCalculation {
    // Calculate rental days from booking dates
    const rentalDays = Math.ceil((booking.endDate.getTime() - booking.startDate.getTime()) / (1000 * 60 * 60 * 24));
    // Use a default daily rate if not provided
    const dailyRate = booking.dailyRate || 50;
    
    const originalPricing = this.calculateBookingFees(
      dailyRate, 
      rentalDays
    );

    const hoursToCancellation = this.calculateHoursBetween(
      new Date(booking.startDate), 
      cancellationDate
    );

    let refundPercentage = 1.0; // Full refund by default
    let refundReason = RefundReason.CANCELLATION;

    // Refund policy logic
    if (hoursToCancellation < 24) {
      refundPercentage = 0.5; // 50% refund within 24 hours
      refundReason = RefundReason.CANCELLATION;
    }

    const refundAmount = originalPricing.baseRentalCost * refundPercentage;
    const rentaHubFeeRefund = originalPricing.bookingFee * refundPercentage;
    const ownerRefundAmount = refundAmount - rentaHubFeeRefund;

    return {
      originalAmount: originalPricing.baseRentalCost,
      refundAmount,
      rentaHubFeeRefund,
      ownerRefundAmount,
      refundReason
    };
  }

  /**
   * Apply discount code (placeholder - replace with actual discount service)
   * @param baseAmount Base amount to apply discount to
   * @param discountCode Discount code
   */
  private applyDiscount(baseAmount: number, discountCode: string): number {
    // Implement actual discount logic
    return baseAmount * 0.1; // 10% placeholder discount
  }

  /**
   * Calculate hours between two dates
   * @param startDate Start date
   * @param endDate End date
   */
  private calculateHoursBetween(startDate: Date, endDate: Date): number {
    const diffMs = Math.abs(endDate.getTime() - startDate.getTime());
    return diffMs / (1000 * 60 * 60);
  }
}

export default new FeeCalculationService();
