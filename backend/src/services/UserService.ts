import { BaseService, NotFoundError, ValidationError } from './BaseService';
import { supabase } from '../utils/supabaseClient';
import bcrypt from 'bcryptjs';
import { PrismaClient, UserRole } from '@prisma/client';
import { logger } from '../utils/logger';

// User model interface
export interface CommunicationPreferences {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'provider' | 'admin';
  // preferredLanguage?: string; // Commented out - doesn't exist in schema
  password?: string;
}

export class UserService extends BaseService {
  private prisma: PrismaClient;

  constructor() {
    super();
    this.prisma = new PrismaClient();
  }

  // Helper function to map Prisma roles to application roles
  private mapPrismaRoleToApp(role: any): "user" | "provider" | "admin" {
    const roleMap = {
      'CUSTOMER': 'user',
      'PROVIDER': 'provider',
      'ADMIN': 'admin'
    };
    return roleMap[role] || 'user';
  }

  // Helper function to map application roles to Prisma roles
  private mapAppRoleToPrisma(role: "user" | "provider" | "admin"): string {
    const roleMap = {
      'user': 'CUSTOMER',
      'provider': 'PROVIDER', 
      'admin': 'ADMIN'
    };
    return roleMap[role] || 'CUSTOMER';
  }

  // Create a new user
  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: userData.email }
    });

    if (existingUser) {
      throw new ValidationError('User with this email already exists', {
        email: userData.email
      });
    }

    // Hash password if provided
    const passwordHash = userData.password 
      ? await this.hashPassword(userData.password)
      : undefined;

    // Prepare user data
    // Map role to Prisma enum
    const roleMap = {
      'user': 'CUSTOMER',
      'provider': 'PROVIDER', 
      'admin': 'ADMIN'
    };

    const userToCreate = {
      ...userData,
      password: passwordHash,
      role: roleMap[userData.role || 'user' as keyof typeof roleMap] as any
    };

    // Create user
    const createdUser = await this.prisma.user.create({ 
      data: userToCreate,
      select: {
        id: true,
        email: true,
        name: true,
        role: true
      }
    });

    return {
      ...createdUser,
      role: this.mapPrismaRoleToApp(createdUser.role)
    };
  }

  // Update user details
  async updateUser(
    userId: string, 
    updates: Partial<Omit<User, 'id' | 'email'>>
  ): Promise<User> {
    // Check if user exists
    const existingUser = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      throw new NotFoundError('User', userId);
    }

    // Hash password if updating
    if (updates.password) {
      updates.password = await this.hashPassword(updates.password);
    }

    // Map role if updating
    const updateData: any = { ...updates };
    if (updateData.role) {
      updateData.role = this.mapAppRoleToPrisma(updateData.role);
    }

    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        role: true
      }
    });

    return {
      ...updatedUser,
      role: this.mapPrismaRoleToApp(updatedUser.role)
    };
  }

  /**
   * Get user by ID with comprehensive details
   */
  async getUserById(userId: string): Promise<User> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          preferredLanguage: true
        }
      });

      if (!user) {
        throw new Error(`User with ID ${userId} not found`);
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name || 'User',
        role: user.role as 'user' | 'provider' | 'admin'
      };
    } catch (error) {
      
      throw error;
    }
  }

  // List users with pagination and filtering
  async listUsers({
    page = 1,
    limit = 20,
    filters = {},
    sortBy = 'createdAt' as const,
    sortOrder = 'desc'
  }: {
    page?: number;
    limit?: number;
    filters?: any;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    return this.prisma.user.findMany({
      skip: (page - 1) * limit,
      take: limit,
      where: filters,
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        preferredLanguage: true
      }
    });
  }

  // Authenticate user
  async authenticateUser(email: string, password: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      throw new NotFoundError('User', email);
    }

    // Verify password
    const isPasswordValid = await this.verifyPassword(
      password, 
      user.password || ''
    );

    if (!isPasswordValid) {
      throw new ValidationError('Invalid credentials');
    }

    // Remove sensitive information
    const { password: _, ...userWithoutPassword } = user;
    return {
      ...userWithoutPassword,
      role: userWithoutPassword.role as 'user' | 'provider' | 'admin'
    };
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id: userId }
    });
  }

  // Private helper methods
  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  private async verifyPassword(
    plainPassword: string, 
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async getTotalUsers(): Promise<number> {
    return this.prisma.user.count();
  }

  async getRecentUsers(limit: number = 10): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        preferredLanguage: true
      }
    });

    return users.map(user => ({
      ...user,
      role: user.role as 'user' | 'provider' | 'admin'
    }));
  }

  async getUserGrowth(period: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<any> {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'daily':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        break;
      case 'weekly':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
        break;
      case 'monthly':
      default:
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    }

    const users = await this.prisma.user.findMany({
      where: { 
        createdAt: { gte: startDate }
      }
    });

    return {
      total: users.length,
      period
    };
  }

  /**
   * Get communication preferences for a user
   */
  async getCommunicationPreferences(userId: string): Promise<CommunicationPreferences> {
    try {
      const preferences = await this.prisma.userCommunicationPreferences.findUnique({
        where: { userId },
        select: {
          emailNotifications: true,
          smsNotifications: true,
          pushNotifications: true
        }
      });

      // Default to true if no preferences found
      return {
        emailNotifications: preferences?.emailNotifications ?? true,
        smsNotifications: preferences?.smsNotifications ?? true,
        pushNotifications: preferences?.pushNotifications ?? true
      };
    } catch (error) {
      
      throw error;
    }
  }

  async updateCommunicationPreferences(
    userId: string, 
    preferences: Partial<CommunicationPreferences>
  ): Promise<CommunicationPreferences> {
    try {
      const updatedPreferences = await this.prisma.userCommunicationPreferences.upsert({
        where: { userId },
        update: preferences,
        create: { 
          userId, 
          ...preferences 
        },
        select: {
          emailNotifications: true,
          smsNotifications: true,
          pushNotifications: true
        }
      });

      return {
        emailNotifications: updatedPreferences.emailNotifications,
        smsNotifications: updatedPreferences.smsNotifications,
        pushNotifications: updatedPreferences.pushNotifications
      };
    } catch (error) {

      throw error;
    }
  }

  // Find user by email
  async findByEmail(email: string): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: this.mapPrismaRoleToApp(user.role)
      };
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  // Get user profile
  async getUserProfile(userId: string): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: this.mapPrismaRoleToApp(user.role)
      };
    } catch (error) {
      logger.error('Error getting user profile:', error);
      throw error;
    }
  }
}

// Also export as default
export default UserService;