import { PrismaClient } from '@prisma/client';
import { differenceInHours } from 'date-fns';

// Local enums since imports are having issues
enum RefundStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PROCESSED = 'PROCESSED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

enum DepositTransactionType {
  DEPOSIT_HOLD = 'DEPOSIT_HOLD',
  DEPOSIT_RELEASE = 'DEPOSIT_RELEASE',
  DEPOSIT_REFUND = 'DEPOSIT_REFUND',
  DEPOSIT_DEDUCTION = 'DEPOSIT_DEDUCTION',
  RELEASE = 'RELEASE',
  PARTIAL_DEDUCTION = 'PARTIAL_DEDUCTION'
}

export class RefundService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Calculate refund amount based on cancellation timing
   * @param bookingId Booking identifier
   * @returns Refund calculation details
   */
  async calculateRefundAmount(bookingId: string): Promise<{
    refundPercentage: number;
    refundAmount: number;
    refundFee: number;
    status: RefundStatus;
  }> {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: { 
        vehicle: true 
      }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    // Calculate hours before pickup
    const hoursBefore = differenceInHours(
      new Date(booking.startDate), 
      new Date()
    );

    // Find applicable refund rule
    const refundRule = await this.prisma.refundRule.findFirst({
      where: {
        cancellationWindow: { gte: hoursBefore },
        isActive: true
      },
      orderBy: { cancellationWindow: 'desc' }
    });

    // Default to no refund if no rule matches
    if (!refundRule) {
      return {
        refundPercentage: 0,
        refundAmount: 0,
        refundFee: 0,
        status: RefundStatus.CANCELLED
      };
    }

    // Calculate refund amount
    const quote = booking.quote as any;
    const totalAmount = quote ? Number(quote.totalPrice) : Number(booking.totalPrice);
    const refundPercentage = Number(refundRule.refundPercentage);
    const refundAmount = totalAmount * (refundPercentage / 100);
    
    // Calculate refund fee (2% processing fee)
    const refundFee = refundAmount * 0.02;

    return {
      refundPercentage,
      refundAmount: refundAmount - refundFee,
      refundFee,
      status: RefundStatus.PENDING
    };
  }

  /**
   * Process refund for a booking
   * @param bookingId Booking identifier
   * @param paymentGateway Payment gateway to use
   */
  async processRefund(bookingId: string, paymentGateway: string = 'Stripe'): Promise<void> {
    const refundDetails = await this.calculateRefundAmount(bookingId);

    // Create refund transaction
    const refundTransaction = await this.prisma.refundTransaction.create({
      data: {
        booking: { connect: { id: bookingId } },
        amount: refundDetails.refundAmount + refundDetails.refundFee,
        originalAmount: refundDetails.refundAmount + refundDetails.refundFee,
        refundAmount: refundDetails.refundAmount,
        refundFee: refundDetails.refundFee,
        status: refundDetails.status,
        paymentGateway,
        reason: 'Booking Cancellation'
      }
    });

    // Simulate payment gateway refund (replace with actual gateway integration)
    try {
      // Placeholder for actual payment gateway refund logic
      const gatewayRefundId = await this.simulatePaymentGatewayRefund(
        bookingId, 
        refundDetails.refundAmount, 
        paymentGateway
      );

      // Update refund transaction with gateway response
      await this.prisma.refundTransaction.update({
        where: { id: refundTransaction.id },
        data: {
          status: RefundStatus.COMPLETED,
          gatewayRefundId
        }
      });
    } catch (error) {
      // Handle refund failure
      await this.prisma.refundTransaction.update({
        where: { id: refundTransaction.id },
        data: {
          status: RefundStatus.FAILED,
          adminNotes: error.message
        }
      });

      throw new Error(`Refund processing failed: ${error.message}`);
    }
  }

  /**
   * Simulate payment gateway refund (to be replaced with actual integration)
   * @param bookingId Booking identifier
   * @param refundAmount Amount to refund
   * @param paymentGateway Payment gateway
   */
  private async simulatePaymentGatewayRefund(
    bookingId: string, 
    refundAmount: number, 
    paymentGateway: string
  ): Promise<string> {
    // Simulate payment gateway refund
    // In a real implementation, this would call the actual payment gateway API
    return `${paymentGateway}_REFUND_${bookingId}_${Date.now()}`;
  }

  /**
   * Handle deposit release after booking return
   * @param bookingId Booking identifier
   */
  async releaseDeposit(bookingId: string): Promise<void> {
    const booking = await this.prisma.booking.findUnique({
      where: { id: bookingId },
      include: { depositTransactions: true }
    });

    if (!booking) {
      throw new Error('Booking not found');
    }

    // Check for any damage assessments
    const damageAssessment = await this.prisma.damageAssessment.findUnique({
      where: { bookingId }
    });

    // If no damage, release full deposit
    if (!damageAssessment || Number(damageAssessment.estimatedRepairCost) === 0) {
      await this.prisma.depositTransaction.create({
        data: {
          booking: { connect: { id: bookingId } },
          amount: Number(booking.depositAmount),
          type: DepositTransactionType.RELEASE,
          transactionType: DepositTransactionType.RELEASE,
          holdStartDate: booking.startDate,
          holdEndDate: new Date()
        }
      });
      return;
    }

    // Partial deposit release after damage assessment
    const depositDeduction = damageAssessment.estimatedRepairCost;
    const remainingDeposit = Math.max(0, Number(booking.depositAmount) - Number(depositDeduction));

    await this.prisma.depositTransaction.create({
      data: {
        booking: { connect: { id: bookingId } },
        amount: remainingDeposit,
        type: DepositTransactionType.PARTIAL_DEDUCTION,
        transactionType: DepositTransactionType.PARTIAL_DEDUCTION,
        damageAssessmentId: damageAssessment.id,
        deductionReason: damageAssessment.damageDescription || 'Vehicle Damage',
        deductionEvidence: JSON.stringify({
          photos: damageAssessment.damagePhotos,
          repairCost: depositDeduction
        }),
        holdStartDate: booking.startDate,
        holdEndDate: new Date()
      }
    });
  }

  /**
   * Create default refund rules
   */
  async initializeRefundRules(): Promise<void> {
    const defaultRules = [
      {
        vehicleType: 'ALL',
        cancellationWindow: 48,
        refundPercentage: 100,
        bookingFeeRefund: true
      },
      {
        vehicleType: 'ALL',
        cancellationWindow: 24,
        refundPercentage: 50,
        bookingFeeRefund: false
      },
      {
        vehicleType: 'ALL',
        cancellationWindow: 12,
        refundPercentage: 0,
        bookingFeeRefund: false
      }
    ];

    for (const rule of defaultRules) {
      await this.prisma.refundRule.upsert({
        where: { 
          cancellationWindow: rule.cancellationWindow 
        },
        update: rule,
        create: {
          vehicleType: rule.vehicleType,
          cancellationWindow: rule.cancellationWindow,
          refundPercentage: rule.refundPercentage,
          bookingFeeRefund: rule.bookingFeeRefund
        }
      });
    }
  }
}

export default RefundService;
