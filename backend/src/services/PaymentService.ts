import { BookingStatus, PrismaClient, PaymentStatus } from '@prisma/client';
import Stripe from 'stripe';
import { logger } from '../utils/logger';

export enum RefundReason {
  CANCELLATION = 'CANCELLATION',
  DAMAGE = 'DAMAGE',
  CUSTOMER_REQUEST = 'CUSTOMER_REQUEST',
  VEHICLE_UNAVAILABLE = 'VEHICLE_UNAVAILABLE'
}

export enum PaymentState {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
  CASH_CONFIRMED = 'CASH_CONFIRMED'
}

export interface PaymentResult {
  success: boolean;
  clientSecret: string | null;
  paymentIntentId: string;
  stripePaymentIntentId: string;
  error?: string;
}

export interface PaymentConfirmationResult {
  success: boolean;
  booking?: any;
  payment?: any;
  error?: string;
}

export interface ProviderEarnings {
  pendingEarnings: number;
  readyEarnings: number;
}

export class PaymentService {
  private prisma: PrismaClient;
  private stripe: Stripe;

  constructor(prisma?: PrismaClient, stripe?: Stripe) {
    this.prisma = prisma || new PrismaClient();
    this.stripe = stripe || new Stripe(process.env.STRIPE_SECRET_KEY || '', {
      apiVersion: '2025-06-30.basil'
    });
  }

  /**
   * Create Stripe PaymentIntent with proper error handling and validation
   */
  async createStripePaymentIntent(
    bookingId: string,
    amount: number,
    currency: string = 'usd'
  ): Promise<PaymentResult> {
    try {
      // Validate booking exists and is in correct state
      const booking = await this.prisma.booking.findUnique({
        where: { id: bookingId },
        include: {
          user: true,
          vehicle: true
        }
      });

      if (!booking) {
        return {
          success: false,
          clientSecret: null,
          paymentIntentId: '',
          stripePaymentIntentId: '',
          error: 'Booking not found'
        };
      }

      // Check if booking is in valid state for payment
      if (booking.status !== 'PENDING') {
        return {
          success: false,
          clientSecret: null,
          paymentIntentId: '',
          stripePaymentIntentId: '',
          error: 'Booking is not in valid state for payment'
        };
      }

      // Update booking to processing state
      await this.transitionPaymentState(bookingId, PaymentState.PENDING, PaymentState.PROCESSING);

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        customer: booking.user.email, // Use email as customer identifier
        metadata: {
          bookingId: bookingId,
          userId: booking.userId,
          providerId: booking.providerId,
          vehicleId: booking.vehicleId
        },
        automatic_payment_methods: {
          enabled: true,
        },
        description: `RentaHub Booking ${bookingId}`
      });

      // Log payment intent creation
      logger.info('Payment intent created', {
        bookingId,
        paymentIntentId: paymentIntent.id,
        amount,
        currency
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        stripePaymentIntentId: paymentIntent.id
      };
    } catch (error) {
      logger.error('Error creating Stripe payment intent:', error);

      // Revert booking state on error
      try {
        await this.transitionPaymentState(bookingId, PaymentState.PROCESSING, PaymentState.FAILED);
      } catch (revertError) {
        logger.error('Failed to revert booking state:', revertError);
      }

      return {
        success: false,
        clientSecret: null,
        paymentIntentId: '',
        stripePaymentIntentId: '',
        error: error instanceof Error ? error.message : 'Payment intent creation failed'
      };
    }
  }

  async getProviderEarnings(providerId: string): Promise<ProviderEarnings> {
    try {
      const payments = await this.prisma.payment.findMany({
        where: { providerId }
      });

      const pendingEarnings = payments
        .filter(p => p.payoutStatus === 'PENDING')
        .reduce((sum, p) => sum + Number(p.providerEarnings), 0);

      const readyEarnings = payments
        .filter(p => p.payoutStatus === 'READY')
        .reduce((sum, p) => sum + Number(p.providerEarnings), 0);

      return {
        pendingEarnings,
        readyEarnings
      };
    } catch (error) {
      console.error('Error getting provider earnings:', error);
      throw error;
    }
  }

  async createPayment(
    bookingData: {
      customerId: string;
      vehicleId: string;
      startDate: Date;
      endDate: Date;
      totalPrice: number;
      paymentMethod?: 'CARD' | 'CASH';
      pickupLocation?: string;
      dropoffLocation?: string;
    }
  ): Promise<PaymentResult> {
    // Simulated payment creation
    return {
      success: true,
      clientSecret: null,
      paymentIntentId: 'test_intent_123',
      stripePaymentIntentId: 'test_intent_123'
    };
  }

  /**
   * Confirm Stripe payment and update booking status atomically
   */
  async confirmStripePayment(
    paymentIntentId: string,
    bookingId: string
  ): Promise<PaymentConfirmationResult> {
    try {
      // 1. Verify payment with Stripe
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status !== 'succeeded') {
        logger.warn('Payment confirmation attempted for non-successful payment', {
          paymentIntentId,
          status: paymentIntent.status
        });

        return {
          success: false,
          error: `Payment not successful. Status: ${paymentIntent.status}`
        };
      }

      // 2. Update booking and create payment record atomically
      const result = await this.prisma.$transaction(async (tx) => {
        // Update booking status
        const booking = await tx.booking.update({
          where: { id: bookingId },
          data: {
            paymentStatus: 'COMPLETED',
            status: 'CONFIRMED',
            updatedAt: new Date()
          },
          include: {
            user: true,
            vehicle: {
              // provider relation doesn't exist in Vehicle model
            }
          }
        });

        // Create or update payment record
        const payment = await tx.payment.upsert({
          where: {
            bookingId: bookingId
          },
          update: {
            status: 'COMPLETED',
            amount: paymentIntent.amount / 100, // Convert from cents
            paymentMethod: 'stripe'
          },
          create: {
            bookingId,
            providerId: booking.providerId,
            amount: paymentIntent.amount / 100, // Convert from cents
            paymentMethod: 'stripe',
            status: 'COMPLETED'
          }
        });

        return { booking, payment };
      });

      logger.info('Payment confirmed successfully', {
        bookingId,
        paymentIntentId,
        amount: paymentIntent.amount
      });

      return {
        success: true,
        booking: result.booking,
        payment: result.payment
      };

    } catch (error) {
      logger.error('Payment confirmation failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment confirmation failed'
      };
    }
  }

  /**
   * Payment state machine - ensures valid state transitions
   */
  private async transitionPaymentState(
    bookingId: string,
    fromState: PaymentState,
    toState: PaymentState
  ): Promise<void> {
    const validTransitions: Record<PaymentState, PaymentState[]> = {
      [PaymentState.PENDING]: [PaymentState.PROCESSING, PaymentState.FAILED, PaymentState.CANCELLED],
      [PaymentState.PROCESSING]: [PaymentState.COMPLETED, PaymentState.FAILED, PaymentState.CANCELLED],
      [PaymentState.COMPLETED]: [PaymentState.REFUNDED],
      [PaymentState.FAILED]: [PaymentState.PENDING, PaymentState.CANCELLED], // Allow retry
      [PaymentState.CANCELLED]: [], // Terminal state
      [PaymentState.REFUNDED]: [], // Terminal state
      [PaymentState.CASH_CONFIRMED]: [PaymentState.REFUNDED]
    };

    if (!validTransitions[fromState]?.includes(toState)) {
      throw new Error(`Invalid payment state transition: ${fromState} -> ${toState}`);
    }

    await this.prisma.booking.update({
      where: {
        id: bookingId,
        paymentStatus: fromState as any
      },
      data: {
        paymentStatus: toState as any,
        updatedAt: new Date()
      }
    });

    logger.info('Payment state transition', {
      bookingId,
      fromState,
      toState
    });
  }

  /**
   * Confirm cash payment by provider
   */
  async confirmCashPayment(
    bookingId: string,
    providerId: string
  ): Promise<PaymentConfirmationResult> {
    try {
      // Verify provider owns the vehicle for this booking
      const booking = await this.prisma.booking.findFirst({
        where: {
          id: bookingId,
          vehicle: { providerId }
        },
        include: {
          vehicle: true
        }
      });

      if (!booking) {
        return {
          success: false,
          error: 'Booking not found or you are not authorized to confirm this payment'
        };
      }

      // Check if booking is in valid state for cash confirmation
      if (booking.paymentStatus !== 'PENDING') {
        return {
          success: false,
          error: 'Booking is not in valid state for cash payment confirmation'
        };
      }

      // Update booking and create payment record atomically
      const result = await this.prisma.$transaction(async (tx) => {
        const updatedBooking = await tx.booking.update({
          where: { id: bookingId },
          data: {
            paymentStatus: 'COMPLETED',
            status: 'CONFIRMED',
            updatedAt: new Date()
          },
          include: {
            vehicle: true
          }
        });

        const payment = await tx.payment.create({
          data: {
            bookingId,
            providerId: booking.providerId,
            amount: Number(booking.totalPrice),
            paymentMethod: 'CASH',
            status: 'COMPLETED'
          }
        });

        return { booking: updatedBooking, payment };
      });

      logger.info('Cash payment confirmed', {
        bookingId,
        providerId,
        amount: Number(booking.totalPrice)
      });

      return {
        success: true,
        booking: result.booking,
        payment: result.payment
      };

    } catch (error) {
      logger.error('Cash payment confirmation failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Cash payment confirmation failed'
      };
    }
  }

  async processRefund(
    bookingId: string,
    reason: RefundReason = RefundReason.CANCELLATION
  ): Promise<{ status: BookingStatus }> {
    // Simulated refund processing
    return {
      status: BookingStatus.REFUNDED
    };
  }

  // ----- Static helper methods to maintain backward compatibility ---- //
  static async confirmStripePayment(req: any, res: any) {
    return res.status(501).json({ message: 'confirmStripePayment not implemented' });
  }

  static async confirmCashPayment(req: any, res: any) {
    return res.status(501).json({ message: 'confirmCashPayment not implemented' });
  }

  static async updateProviderBankDetails(req: any, res: any) {
    return res.status(501).json({ message: 'updateProviderBankDetails not implemented' });
  }

  static async processProviderPayouts(req?: any, res?: any) {
    if (res) {
      return res.status(501).json({ message: 'processProviderPayouts not implemented' });
    }
  }



  async updateProviderBankDetails(...args: any) {
    // @ts-ignore
    return (PaymentService as any).updateProviderBankDetails(...args);
  }

  async processProviderPayoutsMethod(...args: any) {
    // @ts-ignore
    return (PaymentService as any).processProviderPayouts(...args);
  }
}

export default new PaymentService();
