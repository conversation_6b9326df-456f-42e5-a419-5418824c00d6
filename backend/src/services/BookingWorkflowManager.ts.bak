import { Booking, BookingStatus } from '../types/database';
import BookingService from './BookingService';
import { NotificationService } from './NotificationService';
import { PaymentService, RefundReason } from './PaymentService';
import { VehicleAvailabilityService } from './VehicleAvailabilityService';
// import { Logger } from '../utils/logger'; // Commented out - missing export

export class BookingWorkflowManager {
  private bookingService: BookingService;
  private notificationService: NotificationService;
  private paymentService: PaymentService;
  private availabilityService: VehicleAvailabilityService;
  // private logger: Logger; // Commented out - missing Logger type

  constructor(
    bookingService: BookingService, 
    notificationService: NotificationService,
    paymentService: PaymentService,
    availabilityService: VehicleAvailabilityService
  ) {
    this.bookingService = bookingService;
    this.notificationService = notificationService;
    this.paymentService = paymentService;
    thisService = availabilityService;
    this.logger = new Logger('BookingWorkflowManager');
  }

  // State transition validation matrix
  private static STATE_TRANSITIONS = {
    [BookingStatus.PENDING]: [BookingStatus.CONFIRMED, BookingStatus.CANCELLED],
    [BookingStatus.CONFIRMED]: [BookingStatus.IN_PROGRESS, BookingStatus.CANCELLED],
    [BookingStatus.IN_PROGRESS]: [BookingStatus.COMPLETED, BookingStatus.CANCELLED],
    [BookingStatus.COMPLETED]: [],
    [BookingStatus.CANCELLED]: []
  };

  /**
   * Validate state transition
   */
  private validateStateTransition(
    currentStatus: BookingStatus, 
    newStatus: BookingStatus
  ): boolean {
    const allowedTransitions = BookingWorkflowManager.STATE_TRANSITIONS[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  }

  /**
   * Transition booking state with validation and side effects
   */
  async transitionBookingState(
    bookingId: string, 
    newStatus: BookingStatus,
    reason?: string
  ): Promise<Booking> {
    try {
      // Fetch current booking
      const booking = await this.bookingService.getBookingById(bookingId);
      
      if (!booking) {
        throw new Error('Booking not found');
      }

      // Validate state transition
      if (!this.validateStateTransition(booking.status, newStatus)) {
        throw new Error(`Invalid state transition from ${booking.status} to ${newStatus}`);
      }

      // Log state transition
      this.logger.info()

      // Update booking status
      const updatedBooking = await this.bookingService.updateBookingStatus(bookingId, newStatus);

      // Handle specific state transition side effects
      switch (newStatus) {
        case BookingStatus.CONFIRMED:
          await this.handleConfirmation(updatedBooking);
          break;
        case BookingStatus.CANCELLED:
          await this.handleCancellation(updatedBooking, reason);
          break;
        case BookingStatus.COMPLETED:
          await this.handleCompletion(updatedBooking);
          break;
      }

      return updatedBooking;
    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Handle booking confirmation
   */
  private async handleConfirmation(booking: Booking): Promise<void> {
    try {
      // Block vehicle availability
      await thisService.blockVehicleAvailability(booking);

      // Send confirmation notifications
      await this.notificationService.sendBookingConfirmationNotification(booking);

      this.logger.info()
    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Handle booking cancellation with refund processing
   */
  private async handleCancellation(
    booking: Booking, 
    reason?: string
  ): Promise<void> {
    try {
      // Determine refund reason
      const refundReason = this.mapCancellationReason(reason);

      // Process refund
      await this.paymentService.processRefund(booking.id, refundReason);

      // Release vehicle availability
      await thisService.releaseVehicleAvailability(booking);

      // Send cancellation notification
      await this.notificationService.sendBookingCancellationNotification(
        booking, 
        reason || 'Booking cancelled'
      );

      this.logger.info()
    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Handle booking completion
   */
  private async handleCompletion(booking: Booking): Promise<void> {
    try {
      // Release vehicle availability
      await thisService.releaseVehicleAvailability(booking);

      // Send completion notifications
      await this.notificationService.sendBookingCompletionNotification(booking);

      this.logger.info()
    } catch (error) {
      this.logger.info()
      throw error;
    }
  }

  /**
   * Map cancellation reason to refund reason
   */
  private mapCancellationReason(reason?: string): RefundReason {
    // Add more sophisticated mapping if needed
    if (reason?.toLowerCase().includes('provider')) {
      return RefundReason.PROVIDER_CANCELLATION;
    }
    if (reason?.toLowerCase().includes('system') || reason?.toLowerCase().includes('error')) {
      return RefundReason.BOOKING_ERROR;
    }
    return RefundReason.CUSTOMER_CANCELLATION;
  }
}
