import * as fs from 'fs';
import * as path from 'path';

export interface MockVehicle {
  id: string;
  brand: string;
  model: string;
  name: string;
  description: string;
  images: string[];
  dailyRate: number;
  location: {
    city: string;
  };
  rating: number;
  reviews: number;
  features: string[];
  type: string;
  category: string;
  year: string;
  engine: string;
  availableUnits: number;
  hasInsurance: boolean;
  insurancePrice: number;
}

export class MockVehicleService {
  private static instance: MockVehicleService;
  private vehicles: any[];

  private constructor() {
    this.vehicles = [];
    this.loadScrapedVehicles();
  }

  private loadScrapedVehicles() {
    try {
      // Load scraped vehicles from JSON file
      const dataPath = path.join(__dirname, '../data/seAsiaVehicles.json');
      const rawData = fs.readFileSync(dataPath, 'utf8');
      const scrapedVehicles = JSON.parse(rawData);

      // Transform scraped data to match our interface
      this.vehicles = scrapedVehicles.map((vehicle: any) => ({
        id: vehicle.id.toString(),
        brand: vehicle.brand,
        model: vehicle.model,
        name: `${vehicle.brand} ${vehicle.model}`,
        description: `${vehicle.brand} ${vehicle.model} - Perfect for your journey in ${vehicle.year}`,
        images: vehicle.images || ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800'],
        dailyRate: this.calculateDailyRate(vehicle),
        location: { 
          city: 'Indonesia' 
        },
        rating: 4.5 + (Math.random() * 0.5), // Random rating between 4.5-5.0
        reviews: Math.floor(Math.random() * 100) + 20,
        features: this.generateFeatures(vehicle),
        type: vehicle.type.toUpperCase(),
        category: this.mapCategory(vehicle.category),
        year: vehicle.year,
        engine: vehicle.engine,
        availableUnits: Math.floor(Math.random() * 3) + 1,
        hasInsurance: true,
        insurancePrice: 50000 + (Math.random() * 30000)
      }));

      console.log(`✅ Loaded ${this.vehicles.length} mock vehicles from scraped data`);
    } catch (error) {
      console.error('Error loading scraped vehicles:', error);
      // Fallback to static vehicles if loading fails
      this.vehicles = [
        {
          id: 'mock-1',
          brand: 'Yamaha',
          model: 'NMAX',
          name: 'Yamaha NMAX',
          description: 'A reliable and comfortable scooter for city rides.',
          images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800'],
          dailyRate: 120000,
          location: { city: 'Jakarta' },
          rating: 4.7,
          reviews: 42,
          features: ['ABS', 'LED Headlight', 'Comfort Seat'],
          type: 'SCOOTER',
          category: 'MEDIUM_SCOOTER',
          year: '2022',
          engine: '155cc',
          availableUnits: 3,
          hasInsurance: true,
          insurancePrice: 50000
        },
        {
          id: 'mock-2',
          brand: 'Honda',
          model: 'PCX',
          name: 'Honda PCX',
          description: 'Spacious and fuel-efficient, perfect for longer trips.',
          images: ['https://images.unsplash.com/photo-1503736334956-4c8f8e92946d?w=800'],
          dailyRate: 130000,
          location: { city: 'Bali' },
          rating: 4.8,
          reviews: 55,
          features: ['Smart Key', 'Large Storage', 'LED Lighting'],
          type: 'SCOOTER',
          category: 'LARGE_SCOOTER',
          year: '2023',
          engine: '150cc',
          availableUnits: 2,
          hasInsurance: true,
          insurancePrice: 60000
        }
      ];
      console.log('⚠️ Using fallback static vehicles');
    }
  }

  private calculateDailyRate(vehicle: any): number {
    // Base rates based on engine size and type
    let baseRate = 100000; // Base rate 100k IDR
    
    // Adjust based on engine size
    if (vehicle.engine.includes('Electric')) {
      baseRate = 150000; // Electric vehicles cost more
    } else if (vehicle.engine.includes('cc')) {
      const engineSize = parseInt(vehicle.engine.replace('cc', ''));
      if (engineSize <= 125) baseRate = 80000;
      else if (engineSize <= 250) baseRate = 120000;
      else if (engineSize <= 500) baseRate = 180000;
      else baseRate = 250000;
    }
    
    // Adjust based on category
    if (vehicle.category === 'luxury') baseRate *= 1.5;
    else if (vehicle.category === 'large') baseRate *= 1.3;
    
    // Add some randomness
    return baseRate + (Math.random() * 20000);
  }

  private generateFeatures(vehicle: any): string[] {
    const features = ['Reliable', 'Well Maintained'];
    
    if (vehicle.engine.includes('Electric')) {
      features.push('Zero Emissions', 'Low Maintenance');
    } else {
      features.push('Fuel Efficient');
    }
    
    if (vehicle.category === 'luxury') {
      features.push('Premium Quality', 'Advanced Features');
    }
    
    if (vehicle.type === 'scooter') {
      features.push('Easy to Ride', 'Great for City');
    } else {
      features.push('Sporty Performance');
    }
    
    return features;
  }

  private mapCategory(category: string): string {
    const categoryMap: { [key: string]: string } = {
      'small': 'SMALL_SCOOTER',
      'medium': 'MEDIUM_SCOOTER',
      'large': 'LARGE_SCOOTER',
      'luxury': 'LUXURY_SCOOTER'
    };
    return categoryMap[category] || 'MEDIUM_SCOOTER';
  }

  public static getInstance(): MockVehicleService {
    if (!MockVehicleService.instance) {
      MockVehicleService.instance = new MockVehicleService();
    }
    return MockVehicleService.instance;
  }

  public async getAllVehicles(): Promise<any[]> {
    return this.vehicles;
  }

  public async searchVehicles(params: any): Promise<any[]> {
    // Simple search by brand/model/type/category
    return this.vehicles.filter(vehicle => {
      return (
        (!params.brand || vehicle.brand.toLowerCase().includes(params.brand.toLowerCase())) &&
        (!params.model || vehicle.model.toLowerCase().includes(params.model.toLowerCase())) &&
        (!params.type || vehicle.type.toLowerCase().includes(params.type.toLowerCase())) &&
        (!params.category || vehicle.category.toLowerCase().includes(params.category.toLowerCase()))
      );
    });
  }

  public async getVehicleById(id: string): Promise<any | null> {
    return this.vehicles.find(vehicle => vehicle.id === id) || null;
  }
}

export default MockVehicleService;