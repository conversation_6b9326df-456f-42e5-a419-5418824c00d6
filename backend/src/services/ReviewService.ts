import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

export class ReviewService {
  private prisma: PrismaClient;
  private logger = logger;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createReview(reviewData: {
    booking_id: string;
    user_id: string;
    vehicle_id: string;
    provider_id: string;
    rating: number;
    comment?: string;
  }) {
    try {
      // Check if review already exists for this booking
      const existingReview = await this.prisma.review.findFirst({
        where: { booking_id: reviewData.booking_id }
      });

      if (existingReview) {
        throw new Error('Review already exists for this booking');
      }

      const review = await this.prisma.review.create({
        data: {
          user: { connect: { id: reviewData.user_id } },
          vehicle: { connect: { id: reviewData.vehicle_id } },
          booking_id: reviewData.booking_id,
          rating: reviewData.rating,
          comment: reviewData.comment,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      // Update aggregate ratings
      await this.updateAggregateRatings(
        reviewData.vehicle_id, 
        reviewData.provider_id, 
        reviewData.rating
      );

      this.logger.info('Review created successfully', {
        reviewId: review.id, 
        bookingId: reviewData.booking_id 
      });

      return review;
    } catch (error) {
      this.logger.error('Error creating review:', error);
      throw error;
    }
  }

  // Alias for createReview to match route expectations
  async submitReview(reviewData: any) {
    return this.createReview(reviewData);
  }

  async getVehicleReviews(vehicleId: string, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;

      const reviews = await this.prisma.review.findMany({
        where: { vehicleId: vehicleId },
        include: {
          user: {
            select: {
              name: true,
              profilePicture: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit
      });

      const totalCount = await this.prisma.review.count({
        where: { vehicleId: vehicleId }
      });

      return {
        reviews,
        totalCount,
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      this.logger.error('Error fetching vehicle reviews:', error);
      throw error;
    }
  }

  async getProviderReviews(providerId: string, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;

      const reviews = await this.prisma.review.findMany({
        where: { 
          booking: {
            providerId: providerId
          }
        },
        include: {
          user: {
            select: {
              name: true,
              profilePicture: true
            }
          },
          vehicle: {
            select: {
              vehicleType: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit
      });

      const totalCount = await this.prisma.review.count({
        where: { 
          booking: {
            providerId: providerId
          }
        }
      });

      return {
        reviews,
        totalCount,
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      this.logger.error('Error fetching provider reviews:', error);
      throw error;
    }
  }

  async respondToReview(reviewId: string, providerResponse: string) {
    try {
      const review = await this.prisma.review.update({
        where: { id: reviewId },
        data: { 
          response: providerResponse,
          updatedAt: new Date()
        }
      });

      this.logger.info('Provider responded to review', {
        reviewId, 
        response: providerResponse 
      });

      return review;
    } catch (error) {
      this.logger.error('Error responding to review:', error);
      throw error;
    }
  }

  private async updateAggregateRatings(
    vehicleId: string,
    providerId: string,
    newRating: number
  ) {
    try {
      // Update vehicle average rating
      const vehicleReviews = await this.prisma.review.findMany({
        where: { vehicleId: vehicleId },
        select: { rating: true }
      });

      const vehicleAvgRating = vehicleReviews.reduce((sum, review) => sum + review.rating, 0) / vehicleReviews.length;

      // Update provider average rating based on all their bookings
      const providerReviews = await this.prisma.review.findMany({
        where: { 
          booking: {
            providerId: providerId
          }
        },
        select: { rating: true }
      });

      const providerAvgRating = providerReviews.reduce((sum, review) => sum + review.rating, 0) / providerReviews.length;

      this.logger.info('Aggregate ratings updated', {
        vehicleId,
        providerId,
        vehicleAvgRating,
        providerAvgRating
      });
    } catch (error) {
      this.logger.error('Error updating aggregate ratings:', error);
      throw error;
    }
  }
}

export default ReviewService;