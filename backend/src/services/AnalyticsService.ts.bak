import { PrismaClient } from '@prisma/client';
import { format, subDays, subMonths } from 'date-fns';

interface BookingMetrics {
  totalBookings: number;
  totalRevenue: number;
  averageBookingDuration: number;
  mostPopularVehicleTypes: Array<{
    vehicleType: string;
    bookingCount: number;
    percentageOfTotal: number;
  }>;
  bookingTrends: Array<{
    date: string;
    bookingCount: number;
    revenue: number;
  }>;
  cancellationRate: number;
}

interface UserBookingProfile {
  userId: string;
  totalBookings: number;
  totalSpent: number;
  averageBookingValue: number;
  frequentVehicleTypes: Array<{
    vehicleType: string;
    bookingCount: number;
  }>;
}

interface VehiclePerformance {
  vehicleId: string;
  name: string;
  totalBookings: number;
  totalRevenue: number;
  averageUtilizationRate: number;
  mostFrequentBookingPeriods: Array<{
    startHour: number;
    bookingCount: number;
  }>;
}

export class AnalyticsService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get comprehensive booking metrics
   * @param startDate Start date for analysis
   * @param endDate End date for analysis
   */
  async getBookingMetrics(
    startDate: Date = subMonths(new Date(), 1), 
    endDate: Date = new Date()
  ): Promise<BookingMetrics> {
    // Total bookings
    const totalBookings = await this.prisma.booking.count({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // Total revenue
    const totalRevenue = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        status: 'CONFIRMED'
      }
    });

    // Average booking duration - calculate manually since durationInDays doesn't exist
    const bookingsForDuration = await this.prisma.booking.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        startDate: true,
        endDate: true
      }
    });

    const avgDuration = bookingsForDuration.length > 0 
      ? bookingsForDuration.reduce((sum, booking) => {
          const duration = Math.ceil((booking.endDate.getTime() - booking.startDate.getTime()) / (1000 * 60 * 60 * 24));
          return sum + duration;
        }, 0) / bookingsForDuration.length
      : 0;

    // Most popular vehicle types - get through vehicle relation
    const vehicleTypeBookings = await this.prisma.booking.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        vehicle: {
          select: {
            vehicleType: true
          }
        }
      }
    });

    // Group by vehicle type manually
    const vehicleTypeStats = vehicleTypeBookings.reduce((acc, booking) => {
      const vehicleType = booking.vehicle.vehicleType;
      acc[vehicleType] = (acc[vehicleType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const popularVehicleTypes = Object.entries(vehicleTypeStats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([vehicleType, count]) => ({
        vehicleType,
        bookingCount: count,
        percentageOfTotal: (count / totalBookings) * 100
      }));

    // Booking trends (daily) - use regular query instead of raw
    const bookingTrends = await this.prisma.booking.groupBy({
      by: ['createdAt'],
      _count: { id: true },
      _sum: { totalPrice: true },
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // Format the trends data
    const formattedTrends = bookingTrends.map(trend => ({
      date: format(new Date(trend.createdAt), 'yyyy-MM-dd'),
      bookingCount: trend._count.id,
      revenue: Number(trend._sum.totalPrice) || 0
    }));

    // Cancellation rate
    const cancellations = await this.prisma.booking.count({
      where: {
        status: 'CANCELLED',
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    return {
      totalBookings,
      totalRevenue: Number(totalRevenue._sum.totalPrice) || 0,
      averageBookingDuration: avgDuration,
      mostPopularVehicleTypes: popularVehicleTypes,
      bookingTrends: formattedTrends,
      cancellationRate: (cancellations / totalBookings) * 100
    };
  }

  /**
   * Get detailed user booking profile
   * @param userId User identifier
   */
  async getUserBookingProfile(userId: string): Promise<UserBookingProfile> {
    // Total bookings
    const totalBookings = await this.prisma.booking.count({
      where: { userId, status: 'CONFIRMED' }
    });

    // Total spent
    const totalSpent = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: { 
        userId, 
        status: 'CONFIRMED' 
      }
    });

    // Frequent vehicle types
    const frequentVehicleTypes = await this.prisma.booking.groupBy({
      by: ['vehicleType'],
      _count: { vehicleType: true },
      where: { 
        userId,
        status: 'CONFIRMED'
      },
      orderBy: {
        _count: { vehicleType: 'desc' }
      },
      take: 3
    });

    return {
      userId,
      totalBookings,
      totalSpent: totalSpent._sum.totalPrice || 0,
      averageBookingValue: (totalSpent._sum.totalPrice || 0) / totalBookings,
      frequentVehicleTypes: frequentVehicleTypes.map(vt => ({
        vehicleType: vt.vehicleType,
        bookingCount: vt._count.vehicleType
      }))
    };
  }

  /**
   * Get vehicle performance metrics
   * @param vehicleId Vehicle identifier
   */
  async getVehiclePerformance(vehicleId: string): Promise<VehiclePerformance> {
    // Total bookings
    const totalBookings = await this.prisma.booking.count({
      where: { 
        vehicleId, 
        status: 'CONFIRMED' 
      }
    });

    // Total revenue
    const totalRevenue = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: { 
        vehicleId,
        status: 'CONFIRMED' 
      }
    });

    // Utilization rate
    const totalDays = await this.prisma.booking.aggregate({
      _sum: { durationInDays: true },
      where: { 
        vehicleId,
        status: 'CONFIRMED' 
      }
    });

    // Most frequent booking periods
    const bookingPeriods = await this.prisma.$queryRaw`
      SELECT 
        EXTRACT(HOUR FROM start_date) as start_hour, 
        COUNT(*) as booking_count
      FROM 
        bookings
      WHERE 
        vehicle_id = ${vehicleId} AND status = 'CONFIRMED'
      GROUP BY 
        EXTRACT(HOUR FROM start_date)
      ORDER BY 
        booking_count DESC
      LIMIT 5
    `;

    // Vehicle details
    const vehicle = await this.prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });

    return {
      vehicleId,
      name: vehicle?.name || 'Unknown Vehicle',
      totalBookings,
      totalRevenue: totalRevenue._sum.totalPrice || 0,
      averageUtilizationRate: (totalDays._sum.durationInDays || 0) / 365 * 100,
      mostFrequentBookingPeriods: bookingPeriods.map(period => ({
        startHour: period.start_hour,
        bookingCount: period.booking_count
      }))
    };
  }

  /**
   * Predict future booking demand
   * @param vehicleType Vehicle type
   * @param startDate Start date for prediction
   * @param endDate End date for prediction
   */
  async predictBookingDemand(
    vehicleType: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<number> {
    // Historical booking data
    const historicalBookings = await this.prisma.booking.findMany({
      where: {
        vehicleType,
        createdAt: {
          gte: subDays(startDate, 365),
          lte: subDays(endDate, 365)
        }
      }
    });

    // Simple moving average prediction
    const bookingCounts = historicalBookings.map(booking => 1);
    const averageBookings = bookingCounts.reduce((a, b) => a + b, 0) / bookingCounts.length;

    return Math.round(averageBookings * ((new Date(endDate).getTime() - new Date(startDate).getTime()) / (24 * 60 * 60 * 1000)));
  }

  // Platform-Level Analytics
  async getPlatformOverview(days: number = 30) {
    const startDate = subDays(new Date(), days)
    
    const totalBookings = await this.prisma.booking.count({
      where: { 
        createdAt: { gte: startDate },
        status: { not: 'CANCELLED' }
      }
    })

    const totalRevenue = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: { 
        createdAt: { gte: startDate },
        status: { not: 'CANCELLED' }
      }
    })

    const topCities = await this.prisma.booking.groupBy({
      by: ['vehicleId'],
      _count: { _all: true },
      orderBy: { _count: { _all: 'desc' } },
      take: 5,
      include: { 
        vehicle: { 
          include: { 
            area: { 
              include: { 
                city: true 
              } 
            } 
          } 
        } 
      }
    })

    return {
      totalBookings,
      totalRevenue: totalRevenue._sum.totalPrice || 0,
      topCities: topCities.map(city => ({
        name: city.vehicle.area?.city?.name,
        bookingCount: city._count._all
      }))
    }
  }

  // Owner Performance Analytics
  async getOwnerPerformance(ownerId: string, days: number = 30) {
    const startDate = subDays(new Date(), days)

    const totalBookings = await this.prisma.booking.count({
      where: { 
        vehicle: { providerId: ownerId },
        createdAt: { gte: startDate },
        status: { not: 'CANCELLED' }
      }
    })

    const totalEarnings = await this.prisma.booking.aggregate({
      _sum: { totalPrice: true },
      where: { 
        vehicle: { providerId: ownerId },
        createdAt: { gte: startDate },
        status: { not: 'CANCELLED' }
      }
    })

    const topVehicles = await this.prisma.vehicle.findMany({
      where: { providerId: ownerId },
      include: {
        _count: {
          select: { bookings: { 
            where: { 
              createdAt: { gte: startDate },
              status: { not: 'CANCELLED' } 
            } 
          } }
        }
      },
      orderBy: { 
        _count: { 
          bookings: 'desc' 
        } 
      },
      take: 5
    })

    return {
      totalBookings,
      totalEarnings: totalEarnings._sum.totalPrice || 0,
      topVehicles: topVehicles.map(vehicle => ({
        name: vehicle.name,
        bookingCount: vehicle._count.bookings
      }))
    }
  }

  // Geospatial Search with Logging
  async geospatialVehicleSearch(
    latitude: number, 
    longitude: number, 
    radius: number = 10, // km
    userId?: string
  ) {
    // Log the search
    await this.prisma.user.create({
      data: {
        userId,
        latitude,
        longitude,
        radius,
        searchParams: { latitude, longitude, radius }
      }
    })

    // Haversine formula for distance calculation
    const vehicles = await this.prisma.$queryRaw`
      SELECT 
        v.id, 
        v.name, 
        a.latitude, 
        a.longitude,
        (6371 * acos(cos(radians(${latitude})) * cos(radians(a.latitude)) * 
        cos(radians(a.longitude) - radians(${longitude})) + 
        sin(radians(${latitude})) * sin(radians(a.latitude)))) AS distance
      FROM "Vehicle" v
      JOIN "Area" a ON v."areaId" = a.id
      WHERE (6371 * acos(cos(radians(${latitude})) * cos(radians(a.latitude)) * 
        cos(radians(a.longitude) - radians(${longitude})) + 
        sin(radians(${latitude})) * sin(radians(a.latitude)))) <= ${radius}
      ORDER BY distance
      LIMIT 50
    `

    return vehicles
  }

  // Booking Trends
  async getBookingTrends(days: number = 90) {
    const startDate = subDays(new Date(), days)
    
    const dailyBookings = await this.prisma.booking.groupBy({
      by: ['createdAt'],
      _count: { _all: true },
      where: { 
        createdAt: { gte: startDate },
        status: { not: 'CANCELLED' }
      },
      orderBy: { createdAt: 'asc' }
    })

    return dailyBookings.map(booking => ({
      date: booking.createdAt,
      count: booking._count._all
    }))
  }
}

export default new AnalyticsService(); 