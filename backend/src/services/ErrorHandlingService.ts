import { logger } from '../utils/logger';
import { MonitoringService } from './MonitoringService';

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorContext {
  [key: string]: any;
}

export class ErrorHandlingService {
  private logger: typeof logger;
  private monitoringService: MonitoringService;

  constructor(monitoringService: MonitoringService) {
    this.logger = logger;
    this.monitoringService = monitoringService;
  }

  /**
   * Handle and log errors with different severity levels
   */
  handleError(
    errorCode: string, 
    message: string, 
    error?: unknown, 
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext
  ): void {
    // Determine error details
    const errorDetails = this.extractErrorDetails(error);

    // Create comprehensive error log
    const fullErrorLog = {
      errorCode,
      message,
      severity,
      ...errorDetails,
      context
    };

    // Log based on severity
    switch (severity) {
      case ErrorSeverity.LOW:
        this.logger.info(`[${errorCode}] ${message}`, fullErrorLog);
        break;
      case ErrorSeverity.MEDIUM:
        this.logger.warn(`[${errorCode}] ${message}`, fullErrorLog);
        break;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        this.logger.error(`[${errorCode}] ${message}`, fullErrorLog);
        break;
    }

    // Track error in monitoring service
    this.monitoringService.trackError(errorCode, {
      errorMessage: message,
      context: {
        ...context,
        ...errorDetails
      }
    });

    // Trigger additional actions based on severity
    this.handleErrorEscalation(severity, fullErrorLog);
  }

  /**
   * Extract detailed error information
   */
  private extractErrorDetails(error?: unknown): Record<string, any> {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5) // Limit stack trace
      };
    }

    if (typeof error === 'string') {
      return { message: error };
    }

    return {};
  }

  /**
   * Escalate errors based on severity
   */
  private handleErrorEscalation(
    severity: ErrorSeverity, 
    errorLog: Record<string, any>
  ): void {
    switch (severity) {
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        // Send urgent notifications
        this.sendUrgentAlert(errorLog);
        break;
      case ErrorSeverity.MEDIUM:
        // Send less urgent notifications
        this.sendMediumPriorityAlert(errorLog);
        break;
    }
  }

  /**
   * Send urgent alert for critical errors
   */
  private sendUrgentAlert(errorLog: Record<string, any>): void {
    // Implement urgent alerting mechanism
    // Could use SMS, phone call, or high-priority messaging system
    console.error('URGENT ALERT:', errorLog);
  }

  /**
   * Send medium priority alert
   */
  private sendMediumPriorityAlert(errorLog: Record<string, any>): void {
    // Implement medium priority alerting
    // Could use email, Slack message, etc.
    console.warn('MEDIUM PRIORITY ALERT:', errorLog);
  }

  /**
   * Create a safe error wrapper for async operations
   */
  async safeExecute<T>(
    fn: () => Promise<T>, 
    errorCode: string,
    context?: ErrorContext
  ): Promise<T | null> {
    try {
      return await fn();
    } catch (error) {
      this.handleError(
        errorCode, 
        `Safe execution failed: ${errorCode}`, 
        error, 
        ErrorSeverity.MEDIUM,
        context
      );
      return null;
    }
  }
}

export default ErrorHandlingService;