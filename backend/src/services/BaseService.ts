import { supabase } from '../utils/supabaseClient';
import { PostgrestError } from '@supabase/supabase-js';

// Custom error classes
export class ServiceError extends Error {
  statusCode: number;
  details?: any;

  constructor(
    message: string, 
    statusCode: number = 500, 
    details?: any
  ) {
    super(message);
    this.name = 'ServiceError';
    this.statusCode = statusCode;
    this.details = details;
  }
}

export class NotFoundError extends ServiceError {
  constructor(resourceName: string, identifier?: string) {
    super(
      `${resourceName} not found${identifier ? `: ${identifier}` : ''}`, 
      404
    );
    this.name = 'NotFoundError';
  }
}

export class ValidationError extends ServiceError {
  constructor(message: string, details?: any) {
    super(message, 400, details);
    this.name = 'ValidationError';
  }
}

export class AuthorizationError extends ServiceError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 403);
    this.name = 'AuthorizationError';
  }
}

export abstract class BaseService {
  // Centralized error handling method
  protected handleSupabaseError(error: PostgrestError | null): never {
    if (!error) {
      throw new ServiceError('Unknown Supabase error');
    }

    switch (error.code) {
      case '23505':  // Unique constraint violation
        throw new ValidationError('Resource already exists', { 
          constraint: error.details 
        });
      
      case '23503':  // Foreign key constraint violation
        throw new ValidationError('Invalid reference', { 
          constraint: error.details 
        });
      
      case '42501':  // Insufficient privileges
        throw new AuthorizationError('Insufficient database permissions');
      
      case '42P01':  // Undefined table
        throw new ServiceError('Database configuration error', 500);
      
      default:
        throw new ServiceError(
          error.message || 'Database operation failed', 
          500, 
          { code: error.code, details: error.details }
        );
    }
  }

  // Generic method to fetch a single record by ID
  protected async findById<T>(
    table: string, 
    id: string, 
    selectColumns: string = '*'
  ): Promise<T> {
    const { data, error } = await supabase
      .from(table)
      .select(selectColumns)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(table, id);
      }
      this.handleSupabaseError(error);
    }

    if (!data) {
      throw new NotFoundError(table, id);
    }

    return data as T;
  }

  // Generic method to list records with pagination and filtering
  protected async list<T>({
    table,
    page = 1,
    limit = 20,
    filters = {},
    sortBy = 'created_at',
    sortOrder = 'desc',
    selectColumns = '*'
  }: {
    table: string;
    page?: number;
    limit?: number;
    filters?: Record<string, any>;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    selectColumns?: string;
  }): Promise<{
    data: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }
  }> {
    // Calculate range for pagination
    const start = (page - 1) * limit;
    const end = start + limit - 1;

    // Build filter conditions
    let query = supabase.from(table).select(selectColumns, { count: 'exact' });
    
    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Execute query with range
    const { data, error, count } = await query.range(start, end);

    if (error) {
      this.handleSupabaseError(error);
    }

    return {
      data: (data || []) as T[],
      pagination: {
        total: count || 0,
        page,
        limit,
        totalPages: count ? Math.ceil(count / limit) : 0
      }
    };
  }

  // Generic method to create a record
  protected async create<T>(
    table: string, 
    record: Partial<T>
  ): Promise<T> {
    const { data, error } = await supabase
      .from(table)
      .insert(record)
      .select()
      .single();

    if (error) {
      this.handleSupabaseError(error);
    }

    return data;
  }

  // Generic method to update a record
  protected async update<T>(
    table: string, 
    id: string, 
    updates: Partial<T>
  ): Promise<T> {
    const { data, error } = await supabase
      .from(table)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(table, id);
      }
      this.handleSupabaseError(error);
    }

    return data;
  }

  // Generic method to delete a record
  protected async delete(
    table: string, 
    id: string
  ): Promise<void> {
    const { error } = await supabase
      .from(table)
      .delete()
      .eq('id', id);

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(table, id);
      }
      this.handleSupabaseError(error);
    }
  }
}

// Global error handler for services
export function handleServiceError(
  error: unknown, 
  defaultMessage: string = 'An unexpected error occurred'
) {
  if (error instanceof ServiceError) {
    return {
      success: false,
      message: error.message,
      statusCode: error.statusCode,
      details: error.details
    };
  }

  // Log unexpected errors
  console.error('Unhandled service error:', error);

  return {
    success: false,
    message: defaultMessage,
    statusCode: 500
  };
}

export default BaseService;