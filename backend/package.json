{"name": "rentahub-backend", "version": "1.0.1", "description": "Backend for RentAHub", "main": "src/index.ts", "dependencies": {"@mui/lab": "^7.0.0-beta.14", "@sentry/node": "^9.35.0", "@sentry/profiling-node": "^9.35.0", "@supabase/supabase-js": "^2.52.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/express-rate-limit": "^6.0.2", "@types/multer": "^2.0.0", "@types/pg": "^8.15.4", "@types/react-signature-canvas": "^1.0.7", "@types/socket.io": "^3.0.1", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cheerio": "^1.1.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "google-auth-library": "^10.1.0", "helmet": "^8.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "multer": "^2.0.1", "nocache": "^4.0.0", "nodemailer": "^7.0.4", "pdfkit": "^0.17.1", "pg": "^8.16.3", "react-i18next": "^15.6.0", "react-signature-canvas": "^1.1.0-alpha.2", "socket.io": "^4.8.1", "twilio": "^5.7.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.74"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@jest/globals": "^30.0.3", "@playwright/test": "^1.54.1", "@prisma/client": "^6.12.0", "@sentry/types": "^9.34.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.4", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.14.0", "@types/stripe": "^8.0.417", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "axios": "^1.10.0", "babel-jest": "^30.0.2", "codecov": "^3.8.3", "dotenv": "^16.3.1", "esbuild": "^0.17.19", "eslint": "^9.30.1", "istanbul": "^0.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "nodemon": "^3.0.1", "playwright": "^1.54.1", "prisma": "^6.12.0", "stripe": "^18.3.0", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^3.12.7", "typescript": "^5.1.6", "vite": "^4.4.9"}, "engines": {"node": "20"}, "scripts": {"dev": "npx tsx src/index.ts", "dev:minimal": "npx tsx src/minimal-server.ts", "dev:production": "npx tsx src/production.ts", "start": "node dist/production.js", "start:minimal": "node dist/minimal-server.js", "start:prod": "NODE_ENV=production node dist/production.js", "build": "npx prisma generate && tsc", "build:production": "npx prisma generate && tsc && echo 'Production build complete'", "test": "npm run build && jest", "test:unit": "npm run build && jest --testPathPattern=unit", "test:integration": "npm run build && jest --testPathPattern=integration", "test:coverage": "npm run build && jest --coverage", "test:watch": "npm run build && jest --watch", "lint": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "test:all": "./run-tests.sh", "benchmark": "node scripts/benchmark.js", "maintenance": "bash scripts/maintenance.sh", "validate-env": "tsx scripts/validate-environment.ts", "rotate-credentials": "bash ../scripts/rotate-credentials.sh", "import-vehicles": "tsx src/scripts/importVehicleData.ts", "populate-db": "tsx src/scripts/populateVehicleDatabase.ts", "scrape-vehicles": "tsx src/scripts/importVehicleData.ts", "seed-catalog": "tsx src/scripts/seedVehicleCatalog.ts", "setup-database": "tsx src/scripts/setupDatabase.ts", "seed-database": "tsx src/scripts/seedDatabase.ts", "reset-database": "npm run setup-database && npm run seed-database", "setup-stripe": "tsx src/scripts/setupStripe.ts", "test-email": "tsx src/scripts/testEmail.ts", "setup-production": "npm run setup-database && npm run seed-database && npm run setup-stripe", "test:e2e": "npm run build && jest --testPathPattern=e2e", "test:playwright": "playwright test", "test:playwright:ui": "playwright test --ui", "test:playwright:headed": "playwright test --headed", "test:playwright:debug": "playwright test --debug", "test:playwright:simple": "playwright test --config=playwright-simple.config.ts", "test:playwright:simple:ui": "playwright test --config=playwright-simple.config.ts --ui", "test:playwright:simple:headed": "playwright test --config=playwright-simple.config.ts --headed", "test:playwright:stable": "playwright test --config=playwright-stable.config.ts", "test:playwright:stable:ui": "playwright test --config=playwright-stable.config.ts --ui", "test:playwright:stable:headed": "playwright test --config=playwright-stable.config.ts --headed", "test:playwright:chromium": "playwright test --config=playwright-stable.config.ts --project=chromium", "test:playwright:chrome": "playwright test --config=playwright-chrome.config.ts", "test:playwright:chrome:ui": "playwright test --config=playwright-chrome.config.ts --ui", "test:playwright:chrome:headed": "playwright test --config=playwright-chrome.config.ts --headed", "test:intensive": "playwright test --config=playwright-chrome.config.ts intensive-auth-tests.test.ts intensive-ui-tests.test.ts intensive-business-tests.test.ts", "test:intensive:ui": "playwright test --config=playwright-chrome.config.ts --ui intensive-auth-tests.test.ts intensive-ui-tests.test.ts intensive-business-tests.test.ts", "test:intensive:headed": "playwright test --config=playwright-chrome.config.ts --headed intensive-auth-tests.test.ts intensive-ui-tests.test.ts intensive-business-tests.test.ts", "test:setup": "tsx __tests__/setup-test-database.ts", "lint:check": "eslint src/**/*.ts", "format:check": "prettier --check src/**/*.ts"}}