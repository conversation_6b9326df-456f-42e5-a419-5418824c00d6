# RENTAHUB Production Setup Guide

## Environment Variables Configuration

### Required Environment Variables

Copy these to your `.env` file and replace placeholder values with your actual production credentials:

```bash
# Database Configuration
DATABASE_URL=****************************************/database_name
DATABASE_PROVIDER=postgresql

# Server Configuration
PORT=3000
NODE_ENV=production

# Authentication Secrets
JWT_SECRET=a8083c9511445ec40bed97b7b5e29cc068578a334abd59cedeee7f88d537962bae4da05329eb6e5493d847b7bcb8cc412b73a89b8ed18b5592e7e8b72669e9ec
JWT_EXPIRATION=1h

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
GOOGLE_CALLBACK_URL=https://rentahub.com/auth/google/callback

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_live_your_live_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret

# Email Service Configuration
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_SERVICE_API_KEY=your_sendgrid_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# Frontend Configuration
FRONTEND_URL=https://rentahub.com
FRONTEND_PORT=3000

# Backend Configuration
BACKEND_URL=https://api.rentahub.com
BACKEND_PORT=3000

# Deployment Platform
DEPLOY_PLATFORM=production

# Logging and Monitoring
LOG_LEVEL=info

# Feature Flags
ENABLE_GOOGLE_AUTH=true
ENABLE_EMAIL_AUTH=true
ENABLE_STRIPE_PAYMENTS=true
ENABLE_SOS_ALERTS=false
ENABLE_SUPPORT_TICKETS=false

# Security Settings
CORS_ALLOWED_ORIGINS=https://rentahub.com,https://www.rentahub.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password

# Support Email Configuration
SUPPORT_EMAIL=<EMAIL>
EMERGENCY_EMAIL=<EMAIL>

# SMTP Configuration (for email sending)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## Production Checklist

### 1. Environment & Secrets ✅
- [ ] All environment variables configured
- [ ] JWT_SECRET is secure (128+ characters)
- [ ] Using production Stripe keys (sk_live_)
- [ ] Using production Google OAuth credentials
- [ ] SMTP credentials configured

### 2. Database
- [ ] Production database configured
- [ ] Database backups enabled
- [ ] Connection pooling configured
- [ ] Database monitoring enabled

### 3. Security
- [ ] HTTPS enabled
- [ ] CORS origins restricted to production domains
- [ ] Rate limiting enabled
- [ ] Security headers configured
- [ ] Input validation implemented

### 4. Error Monitoring & Logging
- [ ] Centralized logging configured
- [ ] Error tracking service (Sentry) set up
- [ ] Alerting configured for critical errors

### 5. Stripe & Payments
- [ ] Live Stripe API keys configured
- [ ] Production webhook endpoint registered
- [ ] All event types handled
- [ ] Payment failure handling tested

### 6. Email
- [ ] Production SMTP configured
- [ ] Email deliverability tested
- [ ] SPF/DKIM/DMARC records configured

### 7. OAuth & Authentication
- [ ] Production Google OAuth credentials
- [ ] Secure session configuration
- [ ] Callback URLs updated for production

### 8. Frontend
- [ ] Production build created
- [ ] Environment variables configured
- [ ] API endpoints updated to production

### 9. Infrastructure
- [ ] CI/CD pipeline configured
- [ ] Health checks implemented
- [ ] Monitoring and alerting set up

### 10. Testing
- [ ] End-to-end tests passing
- [ ] Load testing completed
- [ ] Security testing performed

## Next Steps

1. Update your `.env` file with the production values above
2. Test the configuration by running the server
3. Proceed through each checklist item systematically
4. Monitor logs and metrics after deployment

## Security Notes

- Never commit `.env` files to version control
- Use a secrets manager in production
- Rotate secrets regularly
- Monitor for security vulnerabilities
- Keep dependencies updated 