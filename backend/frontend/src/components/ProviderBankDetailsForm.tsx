import React, { useState } from 'react';
import axios from 'axios';

interface BankDetailsFormProps {
  onSuccess?: (details: any) => void;
  onError?: (error: string) => void;
}

const ProviderBankDetailsForm: React.FC<BankDetailsFormProps> = ({
  onSuccess,
  onError
}) => {
  const [formData, setFormData] = useState({
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    branchCode: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      const { bankName, accountNumber, accountHolderName } = formData;
      if (!bankName || !accountNumber || !accountHolderName) {
        throw new Error('Please fill in all required fields');
      }

      const { data } = await axios.post(
        '/api/payment/provider/bank-details', 
        formData
      );

      onSuccess?.(data);
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message;
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form 
      onSubmit={handleSubmit} 
      className="provider-bank-details-form"
    >
      <h2>Bank Account Details</h2>
      <p>Add your bank details to receive payouts</p>

      <div className="form-group">
        <label htmlFor="bankName">Bank Name *</label>
        <input 
          type="text"
          id="bankName"
          name="bankName"
          value={formData.bankName}
          onChange={handleChange}
          placeholder="e.g., Bank Central Asia"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="accountNumber">Account Number *</label>
        <input 
          type="text"
          id="accountNumber"
          name="accountNumber"
          value={formData.accountNumber}
          onChange={handleChange}
          placeholder="Your bank account number"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="accountHolderName">Account Holder Name *</label>
        <input 
          type="text"
          id="accountHolderName"
          name="accountHolderName"
          value={formData.accountHolderName}
          onChange={handleChange}
          placeholder="Name on the bank account"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="branchCode">Branch Code (Optional)</label>
        <input 
          type="text"
          id="branchCode"
          name="branchCode"
          value={formData.branchCode}
          onChange={handleChange}
          placeholder="Bank branch code"
        />
      </div>

      <div className="form-actions">
        <button 
          type="submit" 
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : 'Save Bank Details'}
        </button>
      </div>

      <div className="form-disclaimer">
        <p>
          * Your bank details are securely stored and used only for 
          processing payouts. We use bank-grade encryption to protect 
          your information.
        </p>
      </div>
    </form>
  );
};

export default ProviderBankDetailsForm;
