import React, { useState, useRef } from 'react';
import { requestCameraPermission } from '../utils/mobilePermissions';

interface MobileUploadProps {
  onUpload: (file: File) => void;
  accept?: string;
  capture?: 'user' | 'environment';
  label?: string;
}

export const MobileUpload: React.FC<MobileUploadProps> = ({
  onUpload, 
  accept = 'image/*', 
  capture = 'environment',
  label = 'Upload Photo'
}) => {
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const hasCameraPermission = await requestCameraPermission();
      if (hasCameraPermission) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result as string);
        };
        reader.readAsDataURL(file);
        onUpload(file);
      }
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="mobile-upload">
      <input 
        type="file"
        ref={fileInputRef}
        accept={accept}
        capture={capture}
        onChange={handleFileChange}
        className="hidden"
      />
      <button 
        onClick={triggerFileInput}
        className="w-full p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        {label}
      </button>
      {preview && (
        <div className="mt-4 w-full">
          <img 
            src={preview} 
            alt="Upload Preview" 
            className="w-full h-auto rounded-lg object-cover"
          />
        </div>
      )}
    </div>
  );
});
