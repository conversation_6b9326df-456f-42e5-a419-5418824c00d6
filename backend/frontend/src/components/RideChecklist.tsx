import React, { useState, useCallback } from 'react';
import { 
  Box, 
  Button, 
  Text, 
  VStack, 
  HStack, 
  Image, 
  FormControl, 
  FormLabel, 
  Textarea,
  Select,
  useToast
} from '@chakra-ui/react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';

interface RideChecklistProps {
  bookingId: string;
  vehicleId: string;
  stage: 'START' | 'END';
  onChecklistComplete?: () => void;
}

const RideChecklist: React.FC<RideChecklistProps> = ({
  bookingId, 
  vehicleId, 
  stage,
  onChecklistComplete
}) => {
  const [images, setImages] = useState<File[]>([]);
  const [conditionNotes, setConditionNotes] = useState('');
  const [damageSeverity, setDamageSeverity] = useState('MINOR');
  const toast = useToast();

  // Image dropzone handler
  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Limit to 6 images
    const newImages = [...images, ...acceptedFiles].slice(0, 6);
    setImages(newImages);
  }, [images]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpeg', '.jpg'],
      'image/png': ['.png']
    },
    maxSize: 5 * 1024 * 1024 // 5MB
  });

  // Remove image from list
  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);
  };

  // Submit checklist
  const submitChecklist = async () => {
    try {
      // Validate inputs
      if (images.length === 0) {
        toast({
          title: 'Error',
          description: 'Please upload at least one image',
          status: 'error'
        });
        return;
      }

      // Create form data
      const formData = new FormData();
      formData.append('bookingId', bookingId);
      formData.append('vehicleId', vehicleId);
      formData.append('stage', stage);
      
      // Add condition notes if present
      if (conditionNotes) {
        formData.append('conditionNotes', conditionNotes);
      }

      // Append images
      images.forEach((image, index) => {
        formData.append(`images`, image);
      });

      // Submit checklist
      const response = await axios.post(
        '/api/ride-checklist/upload', 
        formData,
        {
          headers: { 
            'Content-Type': 'multipart/form-data' 
          }
        }
      );

      // Optional: Report damage if notes provided
      if (stage === 'END' && conditionNotes) {
        const damageData = new FormData();
        damageData.append('bookingId', bookingId);
        damageData.append('note', conditionNotes);
        damageData.append('severity', damageSeverity);

        // Append damage images if any
        images.forEach((image, index) => {
          damageData.append(`images`, image);
        });

        await axios.post(
          '/api/ride-checklist/damage', 
          damageData,
          {
            headers: { 
              'Content-Type': 'multipart/form-data' 
            }
          }
        );
      }

      // Success toast
      toast({
        title: 'Success',
        description: `${stage} checklist submitted successfully`,
        status: 'success'
      });

      // Reset form and call completion callback
      setImages([]);
      setConditionNotes('');
      onChecklistComplete?.();
    } catch (error) {
      // Error handling
      toast({
        title: 'Error',
        description: error.response?.data?.error || 'Failed to submit checklist',
        status: 'error'
      });
    }
  };

  return (
    <VStack spacing={4} width="full">
      <Text fontSize="xl" fontWeight="bold">
        {stage === 'START' ? 'Vehicle Pickup Checklist' : 'Vehicle Return Checklist'}
      </Text>

      {/* Image Upload */}
      <Box 
        {...getRootProps()} 
        width="full" 
        p={4} 
        borderWidth={2} 
        borderStyle="dashed"
        borderColor={isDragActive ? 'green.500' : 'gray.300'}
        textAlign="center"
      >
        <input {...getInputProps()} />
        <Text>
          {isDragActive 
            ? 'Drop images here' 
            : 'Drag & drop images, or click to select (max 6)'}
        </Text>
      </Box>

      {/* Image Preview */}
      <HStack spacing={2} overflowX="auto" width="full">
        {images.map((image, index) => (
          <Box key={index} position="relative">
            <Image 
              src={URL.createObjectURL(image)} 
              boxSize="100px" 
              objectFit="cover"
            />
            <Button 
              size="xs" 
              colorScheme="red" 
              position="absolute" 
              top={0} 
              right={0}
              onClick={() => removeImage(index)}
            >
              X
            </Button>
          </Box>
        ))}
      </HStack>

      {/* Condition Notes */}
      <FormControl>
        <FormLabel>Condition Notes</FormLabel>
        <Textarea 
          placeholder={
            stage === 'START' 
              ? 'Note any pre-existing damage or issues' 
              : 'Describe any damage or issues during the ride'
          }
          value={conditionNotes}
          onChange={(e) => setConditionNotes(e.target.value)}
        />
      </FormControl>

      {/* Damage Severity (only for end stage) */}
      {stage === 'END' && conditionNotes && (
        <FormControl>
          <FormLabel>Damage Severity</FormLabel>
          <Select 
            value={damageSeverity}
            onChange={(e) => setDamageSeverity(e.target.value)}
          >
            <option value="MINOR">Minor</option>
            <option value="MODERATE">Moderate</option>
            <option value="SEVERE">Severe</option>
          </Select>
        </FormControl>
      )}

      {/* Submit Button */}
      <Button 
        colorScheme="blue" 
        width="full"
        onClick={submitChecklist}
        isDisabled={images.length === 0}
      >
        Submit Checklist
      </Button>
    </VStack>
  );
};

export default RideChecklist;
