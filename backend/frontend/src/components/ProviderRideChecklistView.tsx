import React, { useState, useEffect } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Image, 
  Tabs, 
  Tab<PERSON>ist, 
  TabPanels, 
  Tab, 
  TabPanel,
  Badge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon
} from '@chakra-ui/react';
import axios from 'axios';

interface RideChecklistViewProps {
  bookingId: string;
}

interface RideChecklist {
  id: string;
  stage: 'START' | 'END';
  imageUrls: string[];
  conditionNotes?: string;
  createdAt: string;
}

interface DamageReport {
  id: string;
  note: string;
  images: string[];
  severity: 'MINOR' | 'MODERATE' | 'SEVERE';
  status: 'PENDING' | 'UNDER_REVIEW' | 'RESOLVED' | 'DISPUTED';
  createdAt: string;
}

const ProviderRideChecklistView: React.FC<RideChecklistViewProps> = ({ bookingId }) => {
  const [checklists, setChecklists] = useState<RideChecklist[]>([]);
  const [damageReports, setDamageReports] = useState<DamageReport[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchChecklistData = async () => {
      try {
        // Fetch ride checklists
        const checklistResponse = await axios.get(
          `/api/ride-checklist/${bookingId}`
        );
        setChecklists(checklistResponse.data);

        // Fetch damage reports
        const damageResponse = await axios.get(
          `/api/ride-checklist/${bookingId}/damage`
        );
        setDamageReports(damageResponse.data);

        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch checklist data', error);
        setLoading(false);
      }
    };

    fetchChecklistData();
  }, [bookingId]);

  const renderDamageSeverityBadge = (severity: string) => {
    const colorSchemes = {
      'MINOR': 'green',
      'MODERATE': 'yellow',
      'SEVERE': 'red'
    };

    return (
      <Badge colorScheme={colorSchemes[severity] || 'gray'}>
        {severity}
      </Badge>
    );
  };

  const renderDamageStatusBadge = (status: string) => {
    const colorSchemes = {
      'PENDING': 'yellow',
      'UNDER_REVIEW': 'blue',
      'RESOLVED': 'green',
      'DISPUTED': 'red'
    };

    return (
      <Badge colorScheme={colorSchemes[status] || 'gray'}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  if (loading) {
    return <Text>Loading checklist data...</Text>;
  }

  return (
    <Box width="full">
      <Tabs>
        <TabList>
          <Tab>Start Checklist</Tab>
          <Tab>End Checklist</Tab>
          <Tab>Damage Reports</Tab>
        </TabList>

        <TabPanels>
          {/* Start Checklist */}
          <TabPanel>
            {checklists.filter(c => c.stage === 'START').length === 0 ? (
              <Text>No start checklist available</Text>
            ) : (
              <Accordion allowToggle>
                {checklists
                  .filter(c => c.stage === 'START')
                  .map(checklist => (
                    <AccordionItem key={checklist.id}>
                      <AccordionButton>
                        <HStack width="full" justifyContent="space-between">
                          <Text>
                            Start Checklist - {new Date(checklist.createdAt).toLocaleString()}
                          </Text>
                          <AccordionIcon />
                        </HStack>
                      </AccordionButton>
                      <AccordionPanel>
                        {checklist.conditionNotes && (
                          <Text mb={2}>
                            <strong>Notes:</strong> {checklist.conditionNotes}
                          </Text>
                        )}
                        <HStack spacing={2} overflowX="auto">
                          {checklist.imageUrls.map((url, index) => (
                            <Image 
                              key={index} 
                              src={url} 
                              boxSize="150px" 
                              objectFit="cover" 
                            />
                          ))}
                        </HStack>
                      </AccordionPanel>
                    </AccordionItem>
                  ))}
              </Accordion>
            )}
          </TabPanel>

          {/* End Checklist */}
          <TabPanel>
            {checklists.filter(c => c.stage === 'END').length === 0 ? (
              <Text>No end checklist available</Text>
            ) : (
              <Accordion allowToggle>
                {checklists
                  .filter(c => c.stage === 'END')
                  .map(checklist => (
                    <AccordionItem key={checklist.id}>
                      <AccordionButton>
                        <HStack width="full" justifyContent="space-between">
                          <Text>
                            End Checklist - {new Date(checklist.createdAt).toLocaleString()}
                          </Text>
                          <AccordionIcon />
                        </HStack>
                      </AccordionButton>
                      <AccordionPanel>
                        {checklist.conditionNotes && (
                          <Text mb={2}>
                            <strong>Notes:</strong> {checklist.conditionNotes}
                          </Text>
                        )}
                        <HStack spacing={2} overflowX="auto">
                          {checklist.imageUrls.map((url, index) => (
                            <Image 
                              key={index} 
                              src={url} 
                              boxSize="150px" 
                              objectFit="cover" 
                            />
                          ))}
                        </HStack>
                      </AccordionPanel>
                    </AccordionItem>
                  ))}
              </Accordion>
            )}
          </TabPanel>

          {/* Damage Reports */}
          <TabPanel>
            {damageReports.length === 0 ? (
              <Text>No damage reports</Text>
            ) : (
              <Accordion allowToggle>
                {damageReports.map(report => (
                  <AccordionItem key={report.id}>
                    <AccordionButton>
                      <HStack width="full" justifyContent="space-between">
                        <HStack>
                          {renderDamageSeverityBadge(report.severity)}
                          {renderDamageStatusBadge(report.status)}
                        </HStack>
                        <Text>
                          {new Date(report.createdAt).toLocaleString()}
                        </Text>
                        <AccordionIcon />
                      </HStack>
                    </AccordionButton>
                    <AccordionPanel>
                      <Text mb={2}>
                        <strong>Damage Description:</strong> {report.note}
                      </Text>
                      <HStack spacing={2} overflowX="auto">
                        {report.images.map((url, index) => (
                          <Image 
                            key={index} 
                            src={url} 
                            boxSize="150px" 
                            objectFit="cover" 
                          />
                        ))}
                      </HStack>
                    </AccordionPanel>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default ProviderRideChecklistView;
