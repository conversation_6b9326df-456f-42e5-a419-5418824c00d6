import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  FormControl, 
  FormLabel, 
  VStack, 
  HStack, 
  Text, 
  Radio, 
  RadioGroup,
  DatePicker,
  Alert,
  AlertIcon
} from '@chakra-ui/react';
import { useForm, Controller } from 'react-hook-form';
import { BookingService } from '../services/BookingService';
import { VehicleService } from '../services/VehicleService';

interface BookingFormProps {
  vehicleId: string;
  dailyRate: number;
}

interface BookingFormData {
  startDate: Date;
  endDate: Date;
  paymentMethod: 'CARD' | 'CASH';
}

export const BookingForm: React.FC<BookingFormProps> = ({ vehicleId, dailyRate }) => {
  const [unavailableDates, setUnavailableDates] = useState<{ startDate: Date; endDate: Date }[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { 
    control, 
    handleSubmit, 
    watch, 
    formState: { errors } 
  } = useForm<BookingFormData>({
    defaultValues: {
      paymentMethod: 'CARD'
    }
  });

  const startDate = watch('startDate');
  const endDate = watch('endDate');

  // Fetch unavailable dates when component mounts
  useEffect(() => {
    const fetchUnavailableDates = async () => {
      try {
        const dates = await BookingService.getUnavailableDates(vehicleId);
        setUnavailableDates(dates);
      } catch (error) {
        console.error('Failed to fetch unavailable dates', error);
      }
    };

    fetchUnavailableDates();
  }, [vehicleId]);

  // Calculate total price when dates change
  useEffect(() => {
    if (startDate && endDate) {
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24));
      setTotalPrice(days * dailyRate);
    }
  }, [startDate, endDate, dailyRate]);

  // Check if a date is available
  const isDateAvailable = (date: Date) => {
    return !unavailableDates.some(unavailable => 
      date >= unavailable.startDate && date <= unavailable.endDate
    );
  };

  // Submit booking
  const onSubmit = async (data: BookingFormData) => {
    setIsSubmitting(true);
    setBookingError(null);

    try {
      const bookingPayload = {
        vehicleId,
        startDate: data.startDate,
        endDate: data.endDate,
        totalPrice,
        paymentMethod: data.paymentMethod
      };

      const result = await BookingService.createBooking(bookingPayload);

      // Handle different booking scenarios
      if (result.success) {
        // Show success message or redirect
        if (data.paymentMethod === 'CARD') {
          // Redirect to confirmation page
          // history.push(`/bookings/${result.booking.id}`);
        } else {
          // Show pending confirmation message
          // history.push('/bookings/pending');
        }
      } else {
        setBookingError(result.error || 'Booking failed');
      }
    } catch (error) {
      setBookingError('An unexpected error occurred');
      console.error('Booking error', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit)} width="100%" maxWidth="500px">
      <VStack spacing={4}>
        {bookingError && (
          <Alert status="error">
            <AlertIcon />
            {bookingError}
          </Alert>
        )}

        <FormControl>
          <FormLabel>Start Date</FormLabel>
          <Controller
            name="startDate"
            control={control}
            rules={{ 
              required: 'Start date is required',
              validate: {
                available: (value) => 
                  isDateAvailable(value) || 'Selected date is not available'
              }
            }}
            render={({ field }) => (
              <DatePicker
                {...field}
                minDate={new Date()}
                excludeDates={unavailableDates.flatMap(range => 
                  Array.from(
                    { length: Math.ceil((range.endDate.getTime() - range.startDate.getTime()) / (1000 * 3600 * 24)) }, 
                    (_, i) => new Date(range.startDate.getTime() + i * 24 * 60 * 60 * 1000)
                  )
                )}
              />
            )}
          />
          {errors.startDate && <Text color="red.500">{errors.startDate.message}</Text>}
        </FormControl>

        <FormControl>
          <FormLabel>End Date</FormLabel>
          <Controller
            name="endDate"
            control={control}
            rules={{ 
              required: 'End date is required',
              validate: {
                afterStart: (value) => 
                  value > startDate || 'End date must be after start date',
                available: (value) => 
                  isDateAvailable(value) || 'Selected date is not available'
              }
            }}
            render={({ field }) => (
              <DatePicker
                {...field}
                minDate={startDate || new Date()}
                excludeDates={unavailableDates.flatMap(range => 
                  Array.from(
                    { length: Math.ceil((range.endDate.getTime() - range.startDate.getTime()) / (1000 * 3600 * 24)) }, 
                    (_, i) => new Date(range.startDate.getTime() + i * 24 * 60 * 60 * 1000)
                  )
                )}
              />
            )}
          />
          {errors.endDate && <Text color="red.500">{errors.endDate.message}</Text>}
        </FormControl>

        <FormControl>
          <FormLabel>Payment Method</FormLabel>
          <Controller
            name="paymentMethod"
            control={control}
            render={({ field }) => (
              <RadioGroup {...field}>
                <HStack>
                  <Radio value="CARD">Card Payment</Radio>
                  <Radio value="CASH">Cash on Pickup</Radio>
                </HStack>
              </RadioGroup>
            )}
          />
        </FormControl>

        <Box>
          <Text fontWeight="bold">Total Price: ${totalPrice.toFixed(2)}</Text>
          {watch('paymentMethod') === 'CASH' && (
            <Alert status="warning" mt={2}>
              <AlertIcon />
              Cash bookings require provider confirmation
            </Alert>
          )}
        </Box>

        <Button 
          type="submit" 
          colorScheme="blue" 
          isLoading={isSubmitting}
          width="full"
        >
          Book Now
        </Button>
      </VStack>
    </Box>
  );
};
