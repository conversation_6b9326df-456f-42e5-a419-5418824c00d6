import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { 
  Elements, 
  CardElement, 
  useStripe, 
  useElements 
} from '@stripe/react-stripe-js';
import axios from 'axios';

// Stripe configuration
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

interface PaymentFlowProps {
  bookingId: string;
  totalAmount: number;
  onPaymentSuccess: (paymentDetails: any) => void;
  onPaymentError: (error: string) => void;
}

const PaymentForm: React.FC<PaymentFlowProps> = ({
  bookingId,
  totalAmount,
  onPaymentSuccess,
  onPaymentError
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'cash'>('card');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCardPayment = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      // Create PaymentIntent on backend
      const { data: paymentIntentData } = await axios.post(
        '/api/payment/stripe/payment-intent', 
        { 
          bookingId, 
          amount: totalAmount 
        }
      );

      // Confirm payment
      const result = await stripe.confirmCardPayment(
        paymentIntentData.clientSecret, 
        {
          payment_method: {
            card: elements.getElement(CardElement)!,
            billing_details: {
              name: 'Customer Name' // TODO: Replace with actual customer name
            }
          }
        }
      );

      if (result.error) {
        onPaymentError(result.error.message || 'Payment failed');
      } else {
        // Confirm payment on backend
        const { data: paymentConfirmation } = await axios.post(
          '/api/payment/stripe/confirm', 
          { 
            bookingId, 
            paymentIntentId: result.paymentIntent.id 
          }
        );

        onPaymentSuccess(paymentConfirmation);
      }
    } catch (error) {
      onPaymentError(error.response?.data?.error || 'Payment processing error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCashPayment = async () => {
    try {
      setIsProcessing(true);

      // Notify backend about cash payment
      const { data: cashPaymentConfirmation } = await axios.post(
        `/api/payment/cash/confirm/${bookingId}`
      );

      onPaymentSuccess(cashPaymentConfirmation);
    } catch (error) {
      onPaymentError(error.response?.data?.error || 'Cash payment processing error');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="payment-flow">
      <div className="payment-method-selector">
        <button 
          onClick={() => setPaymentMethod('card')}
          className={paymentMethod === 'card' ? 'active' : ''}
        >
          Card Payment
        </button>
        <button 
          onClick={() => setPaymentMethod('cash')}
          className={paymentMethod === 'cash' ? 'active' : ''}
        >
          Cash on Pickup
        </button>
      </div>

      {paymentMethod === 'card' ? (
        <form onSubmit={handleCardPayment}>
          <CardElement 
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
          <button 
            type="submit" 
            disabled={isProcessing || !stripe}
          >
            {isProcessing ? 'Processing...' : `Pay $${totalAmount}`}
          </button>
        </form>
      ) : (
        <div className="cash-payment">
          <p>
            You will pay the total amount of ${totalAmount} 
            directly to the provider at pickup.
          </p>
          <button 
            onClick={handleCashPayment}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Confirm Booking'}
          </button>
        </div>
      )}
    </div>
  );
};

const PaymentFlow: React.FC<PaymentFlowProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} />
    </Elements>
  );
};

export default PaymentFlow;
