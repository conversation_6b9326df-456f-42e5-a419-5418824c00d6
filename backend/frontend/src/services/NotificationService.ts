import { io, Socket } from 'socket.io-client';
import { toast } from 'react-toastify';

type NotificationType = 
  | 'BOOKING_CONFIRMED' 
  | 'BOOKING_REJECTED' 
  | 'BOOKING_CREATED' 
  | 'BOOKING_CANCELLED';

interface NotificationPayload {
  type: NotificationType;
  bookingId: string;
  message?: string;
}

export class NotificationService {
  private static socket: Socket | null = null;
  private static userId: string | null = null;

  /**
   * Initialize socket connection
   * @param userId User identifier
   */
  static initialize(userId: string) {
    // Disconnect existing socket if any
    if (this.socket) {
      this.socket.disconnect();
    }

    // Create new socket connection
    this.socket = io(process.env.REACT_APP_SOCKET_URL || 'http://localhost:3001', {
      auth: { token: localStorage.getItem('token') },
      query: { userId }
    });

    this.userId = userId;

    // Listen for notifications
    this.setupListeners();
  }

  /**
   * Set up socket event listeners
   */
  private static setupListeners() {
    if (!this.socket) return;

    // Booking status updates
    this.socket.on('booking_status_update', (data: NotificationPayload) => {
      this.handleNotification(data);
    });

    // Connection error handling
    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
    });
  }

  /**
   * Handle incoming notifications
   * @param notification Notification payload
   */
  private static handleNotification(notification: NotificationPayload) {
    switch (notification.type) {
      case 'BOOKING_CONFIRMED':
        toast.success(`Booking ${notification.bookingId} has been confirmed`);
        break;
      case 'BOOKING_REJECTED':
        toast.error(`Booking ${notification.bookingId} has been rejected`);
        break;
      case 'BOOKING_CREATED':
        toast.info(`New booking ${notification.bookingId} created`);
        break;
      case 'BOOKING_CANCELLED':
        toast.warning(`Booking ${notification.bookingId} has been cancelled`);
        break;
      default:
        console.warn('Unhandled notification type:', notification.type);
    }
  }

  /**
   * Send a provider notification
   * @param notification Notification details
   */
  static async sendProviderNotification(notification: NotificationPayload) {
    try {
      // Send notification via API if needed
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(notification)
      });

      if (!response.ok) {
        throw new Error('Failed to send notification');
      }
    } catch (error) {
      console.error('Notification sending error:', error);
    }
  }

  /**
   * Close socket connection
   */
  static disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Check if socket is connected
   */
  static isConnected(): boolean {
    return !!this.socket?.connected;
  }
}

// Export for easy import
export default NotificationService;
