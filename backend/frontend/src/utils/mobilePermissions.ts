export const requestCameraPermission = async (): Promise<boolean> => {
  if (!('mediaDevices' in navigator)) {
    console.warn('Camera not supported');
    return false;
  }

  try {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.error('Camera permission denied', error);
    return false;
  }
};

export const requestLocationPermission = async (): Promise<{ 
  allowed: boolean, 
  location?: GeolocationCoordinates 
}> => {
  if (!('geolocation' in navigator)) {
    console.warn('Geolocation not supported');
    return { allowed: false };
  }

  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({ 
          allowed: true, 
          location: position.coords 
        });
      },
      (error) => {
        console.error('Location permission denied', error);
        resolve({ allowed: false });
      },
      { 
        enableHighAccuracy: true, 
        timeout: 5000, 
        maximumAge: 0 
      }
    );
  });
};
