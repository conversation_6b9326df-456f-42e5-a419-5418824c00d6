import React, { useState, useEffect } from 'react';
import { 
  Box, 
  VStack, 
  Heading, 
  Table, 
  Thead, 
  Tbody, 
  Tr, 
  Th, 
  Td, 
  Button, 
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Text,
  Flex,
  Badge
} from '@chakra-ui/react';
import { BookingService } from '../services/BookingService';
import { NotificationService } from '../services/NotificationService';

interface PendingBooking {
  id: string;
  vehicleName: string;
  customerName: string;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
}

export const ProviderDashboard: React.FC = () => {
  const [pendingBookings, setPendingBookings] = useState<PendingBooking[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<PendingBooking | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Fetch pending bookings
  useEffect(() => {
    const fetchPendingBookings = async () => {
      try {
        const bookings = await BookingService.getPendingCashBookings();
        setPendingBookings(bookings);
      } catch (error) {
        console.error('Failed to fetch pending bookings', error);
      }
    };

    fetchPendingBookings();
  }, []);

  // Confirm booking handler
  const handleConfirmBooking = async () => {
    if (!selectedBooking) return;

    try {
      await BookingService.confirmCashBooking(selectedBooking.id);
      
      // Remove booking from list
      setPendingBookings(prev => 
        prev.filter(booking => booking.id !== selectedBooking.id)
      );

      // Send notification
      await NotificationService.sendProviderNotification({
        type: 'BOOKING_CONFIRMED',
        bookingId: selectedBooking.id
      });

      onClose();
    } catch (error) {
      console.error('Failed to confirm booking', error);
    }
  };

  // Reject booking handler
  const handleRejectBooking = async () => {
    if (!selectedBooking) return;

    try {
      await BookingService.rejectCashBooking(selectedBooking.id);
      
      // Remove booking from list
      setPendingBookings(prev => 
        prev.filter(booking => booking.id !== selectedBooking.id)
      );

      // Send notification
      await NotificationService.sendProviderNotification({
        type: 'BOOKING_REJECTED',
        bookingId: selectedBooking.id
      });

      onClose();
    } catch (error) {
      console.error('Failed to reject booking', error);
    }
  };

  // Open confirmation modal
  const openConfirmationModal = (booking: PendingBooking) => {
    setSelectedBooking(booking);
    onOpen();
  };

  return (
    <Box p={6}>
      <VStack spacing={6} align="stretch">
        <Heading>Pending Cash Bookings</Heading>
        
        {pendingBookings.length === 0 ? (
          <Text>No pending bookings</Text>
        ) : (
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Vehicle</Th>
                <Th>Customer</Th>
                <Th>Start Date</Th>
                <Th>End Date</Th>
                <Th>Total Price</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {pendingBookings.map(booking => (
                <Tr key={booking.id}>
                  <Td>{booking.vehicleName}</Td>
                  <Td>{booking.customerName}</Td>
                  <Td>{booking.startDate.toLocaleDateString()}</Td>
                  <Td>{booking.endDate.toLocaleDateString()}</Td>
                  <Td>${booking.totalPrice.toFixed(2)}</Td>
                  <Td>
                    <Flex>
                      <Button 
                        colorScheme="green" 
                        size="sm" 
                        mr={2}
                        onClick={() => openConfirmationModal(booking)}
                      >
                        Confirm
                      </Button>
                      <Button 
                        colorScheme="red" 
                        size="sm"
                        onClick={() => {
                          setSelectedBooking(booking);
                          onOpen();
                        }}
                      >
                        Reject
                      </Button>
                    </Flex>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}
      </VStack>

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {selectedBooking?.id ? 'Confirm Booking' : 'Reject Booking'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              Are you sure you want to 
              {selectedBooking?.id ? ' confirm' : ' reject'} 
              {' '}this booking for {selectedBooking?.vehicleName}?
            </Text>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            {selectedBooking?.id && (
              <Button 
                colorScheme="green" 
                mr={3} 
                onClick={handleConfirmBooking}
              >
                Confirm
              </Button>
            )}
            <Button 
              colorScheme="red" 
              onClick={handleRejectBooking}
            >
              Reject
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};
