coverage:
  status:
    project:
      default:
        # Basic coverage threshold
        target: 75%
        threshold: 1%
    patch:
      default:
        # Patch coverage threshold
        target: 70%
        threshold: 1%

# Ignore specific files or patterns
ignore:
  - "src/__tests__/**/*"
  - "src/__integration__/**/*"
  - "**/*.d.ts"
  - "src/config/**/*"
  - "src/utils/logger.ts"

# Comment settings
comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: false

# Flags for different types of tests
flags:
  unit:
    carryforward: true
    paths:
      - src/__tests__/
  integration:
    carryforward: true
    paths:
      - src/__integration__/

# Notification settings
github_checks:
  annotations: true 