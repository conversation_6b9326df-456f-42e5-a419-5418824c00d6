# Test Credentials Management Guide for RentaHub

## Overview
This guide provides best practices for managing test credentials in the RentaHub project.

## Credential Generation

### Using the Credentials Script
1. Navigate to the `backend` directory
2. Run the credentials generation script:
   ```bash
   ./scripts/generate-test-credentials.sh
   ```

### GitHub Secrets Setup

#### Prerequisites
- GitHub CLI installed (`brew install gh`)
- Authenticated with GitH<PERSON> (`gh auth login`)

#### Setting Secrets
```bash
# Set Stripe Test Secrets
gh secret set STRIPE_TEST_SECRET_KEY -b"sk_test_your_secret_key"
gh secret set STRIPE_TEST_PUBLISHABLE_KEY -b"pk_test_your_publishable_key"
```

## Security Best Practices

### Credential Management
- Never commit `.env.test` to version control
- Rotate test credentials every 90 days
- Use unique credentials for different environments
- Limit access to test credentials

### Stripe Test Mode Guidelines
- Always use `sk_test_` prefixed keys
- Never use real payment information
- Understand test mode limitations

## Simulating Payment Scenarios
Refer to `STRIPE_TEST_CARDS.md` for test card numbers and scenarios.

## Monitoring and Auditing
- Regularly review access logs
- Use GitHub's secret scanning features
- Implement principle of least privilege

## Troubleshooting
- Verify key format starts with `sk_test_`
- Check GitHub Actions logs for credential issues
- Regenerate keys if compromise is suspected

## Emergency Procedures
1. Immediately revoke compromised credentials
2. Generate new test keys
3. Update GitHub secrets
4. Notify team members

## Contact
For urgent credential-related issues, contact the DevOps team. 