const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkVehicleCatalog() {
  try {
    console.log('🔍 Checking vehicle_catalog table structure...');
    
    // Try to get a sample record to see the structure
    const { data, error } = await supabase
      .from('vehicle_catalog')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error accessing vehicle_catalog:', error);
      return;
    }

    if (data && data.length > 0) {
      console.log('✅ vehicle_catalog table exists with columns:', Object.keys(data[0]));
      console.log('📋 Sample record:', data[0]);
    } else {
      console.log('📭 vehicle_catalog table exists but is empty');
    }

    // Count total records
    const { data: countData, error: countError } = await supabase
      .from('vehicle_catalog')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('❌ Error counting records:', countError);
    } else {
      console.log(`📊 Total vehicles in catalog: ${countData?.length || 0}`);
    }

  } catch (error) {
    console.error('❌ Error checking vehicle_catalog:', error);
  }
}

checkVehicleCatalog().then(() => {
  console.log('🎉 Check completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Check failed:', error);
  process.exit(1);
}); 