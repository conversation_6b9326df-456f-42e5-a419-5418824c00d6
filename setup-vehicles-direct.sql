-- Create vehicle catalog table with essential fields only
CREATE TABLE IF NOT EXISTS vehicles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  make VARCHAR(100) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL,
  engine VARCHAR(50),
  color VARCHAR(50),
  fuel_type VARCHAR(50),
  transmission VARCHAR(50),
  category VARCHAR(50),
  images TEXT[], -- Array of image URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient searching
CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles(make, model);
CREATE INDEX IF NOT EXISTS idx_vehicles_category ON vehicles(category);

-- Grant permissions to anon role
GRANT SELECT, INSERT, UPDATE ON vehicles TO anon;
GRANT USAGE ON SEQUENCE vehicles_id_seq TO anon;

-- Insert scraped vehicle data
INSERT INTO vehicles (make, model, year, engine, color, fuel_type, transmission, category, images) VALUES
('Honda', 'PCX 160', 2023, '157cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-black.jpg']),
('Yamaha', 'NMAX 155', 2023, '155cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-blue.jpg']),
('Honda', 'ADV 150', 2023, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red.jpg']),
('Yamaha', 'XSR 155', 2023, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow.jpg']),
('Honda', 'CB150R', 2023, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green.jpg']),
('Yamaha', 'MT-15', 2023, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black.jpg']),
('Honda', 'Vario 160', 2023, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white.jpg']),
('Yamaha', 'Aerox 155', 2023, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red.jpg']),
('Honda', 'Beat', 2023, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue.jpg']),
('Yamaha', 'Mio', 2023, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black.jpg']),
('Honda', 'Scoopy', 2023, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink.jpg']),
('Yamaha', 'Fino', 2023, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white.jpg']),
('Honda', 'Click 160', 2023, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red.jpg']),
('Yamaha', 'FreeGo', 2023, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue.jpg']),
('Honda', 'Genio', 2023, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black.jpg']);

-- Enable Row Level Security (RLS)
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all users to read vehicles
CREATE POLICY "Allow public read access to vehicles" ON vehicles
FOR SELECT USING (true);

-- Create policy to allow authenticated users to insert vehicles
CREATE POLICY "Allow authenticated users to insert vehicles" ON vehicles
FOR INSERT WITH CHECK (auth.role() = 'authenticated'); 