const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkVehiclesCount() {
  try {
    console.log('🔍 Checking vehicles table...');
    
    // Count total vehicles
    const { data: countData, error: countError } = await supabase
      .from('vehicles')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('❌ Error counting vehicles:', countError);
      return;
    }

    console.log(`📊 Total vehicles in database: ${countData?.length || 0}`);

    // Get sample vehicles
    const { data: sampleData, error: sampleError } = await supabase
      .from('vehicles')
      .select('make, model, year, engine_size, category')
      .limit(5);

    if (sampleError) {
      console.error('❌ Error fetching sample vehicles:', sampleError);
      return;
    }

    if (sampleData && sampleData.length > 0) {
      console.log('📋 Sample vehicles:');
      sampleData.forEach((vehicle, index) => {
        console.log(`  ${index + 1}. ${vehicle.make} ${vehicle.model} (${vehicle.year}) - ${vehicle.engine_size}cc ${vehicle.category}`);
      });
    } else {
      console.log('📭 No vehicles found in database');
    }

  } catch (error) {
    console.error('❌ Error checking vehicles:', error);
  }
}

checkVehiclesCount().then(() => {
  console.log('🎉 Check completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Check failed:', error);
  process.exit(1);
}); 