datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum UserRole {
  CUSTOMER
  PROVIDER
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  PENDING
  SUSPENDED
  BANNED
}

enum VehicleType {
  CAR
  MOTORCYCLE
  TRUCK
  VAN
  SUV
}

enum VehicleCategory {
  ECONOMY
  COMPACT
  MIDSIZE
  FULLSIZE
  LUXURY
  SUV
  TRUCK
  VAN
  MOTORCYCLE
}

enum VehicleStatus {
  AVAILABLE
  UNAVAILABLE
  MAINTENANCE
  PENDING
  ACTIVE
  DISABLED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  CASH_CONFIRMED
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REFUNDED
}

enum SupportTicketCategory {
  PAYMENT
  VEHICLE
  DAMAGE
  BOOKING
  OTHER
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  ESCALATED
}

enum SOSAlertStatus {
  PENDING
  RESOLVED
}



enum PaymentMethod {
  STRIPE
  CASH
  BANK_TRANSFER
}

enum PayoutStatus {
  PENDING
  READY
  PAID
}

enum AlertSeverity {
  INFO
  WARNING
  ERROR
  CRITICAL
}

enum RideChecklistStage {
  START
  END
}

enum DamageReportSeverity {
  MINOR
  MODERATE
  SEVERE
}

enum DamageReportStatus {
  PENDING
  UNDER_REVIEW
  RESOLVED
  DISPUTED
}

enum BookingHistoryStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

model Refund {
  id          String   @id @default(uuid())
  paymentId   String
  amount      Int
  reason      String?  @default("Unspecified")
  status      String   @default("PROCESSED")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([paymentId])
}

model Dispute {
  id          String   @id @default(uuid())
  paymentId   String
  amount      Int
  reason      String?
  status      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([paymentId])
}

model api_keys {
  id         String   @id @default(uuid())
  key_name   String
  service    String
  value      String
  created_at DateTime @default(now())
}

model IntegrationLog {
  id               String    @id @default(uuid())
  service          String
  request_id       String?   @unique
  request_body     String?
  request_url      String?
  request_method   String?
  request_headers  String?
  response_body    String?
  status_code      Int?      @default(200)
  error_message    String?
  error_details    String?
  created_at       DateTime  @default(now())
}

model webhook_logs {
  id           String    @id @default(uuid())
  service      String
  event_type   String?
  payload      String?
  received_at  DateTime  @default(now())
  processed    Boolean   @default(false)
}

model User {
  id                String        @id @default(cuid())
  email             String        @unique
  password          String?       // Make it optional for existing users
  name              String
  googleId          String?       @unique
  profileImage      String?
  role              UserRole      @default(CUSTOMER)
  status            UserStatus    @default(ACTIVE)
  companyName       String?
  phoneNumber       String?
  emailVerified     Boolean       @default(false)
  phoneVerified     Boolean       @default(false)
  verificationDocuments VerificationDocument[]
  bookings          Booking[]
  vehicles          Vehicle[]
  savedVehicles     SavedVehicle[]
  loyaltyPoints     LoyaltyPoints?
  bookingHistories  BookingHistory[]
  providerBookingHistories BookingHistory[] @relation("ProviderBookings")
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  permissions       Json?         // Store additional dynamic permissions
  providerBankDetails   ProviderBankDetails?
  payments              Payment[]
  providerPayments      Payment[]            @relation("ProviderPayments")
  providers   User[]     @relation("ProviderRelation")
  phoneVerificationCode String?
  mfaEnabled            Boolean  @default(false)
  mfaSecret             String?
  resetToken          String?
  resetTokenExpiry    DateTime?
  verificationToken       String?
  verificationTokenExpiry DateTime?
  mfaBackupCodes String[]
  blacklisted     Boolean  @default(false)
  completedProfile Boolean  @default(false)
  githubId      String?    @unique
  address         String?
  dateOfBirth     DateTime?
  profilePicture  String?
}

model VerificationDocument {
  id            String        @id @default(cuid())
  userId        String
  user          User          @relation(fields: [userId], references: [id])
  documentType  String        // 'license', 'passport', etc.
  fileUrl       String
  status        VerificationStatus @default(PENDING)
  uploadedAt    DateTime      @default(now())
}

model Country {
  id        String    @id @default(cuid())
  name      String
  code      String    @unique
  states    State[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model State {
  id        String    @id @default(cuid())
  name      String
  countryId String
  country   Country   @relation(fields: [countryId], references: [id])
  cities    City[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model City {
  id        String    @id @default(cuid())
  name      String
  stateId   String
  state     State     @relation(fields: [stateId], references: [id])
  areas     Area[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Area {
  id          String     @id @default(cuid())
  name        String
  cityId      String
  city        City       @relation(fields: [cityId], references: [id])
  
  latitude    Float?
  longitude   Float?
  
  vehicles    Vehicle[]
  
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model Vehicle {
  id                String           @id @default(cuid())
  
  // Basic Information
  make              String
  model             String
  year              Int
  vin               String           @unique
  
  // Categorization
  category          VehicleCategory
  
  // Pricing
  dailyRate         Decimal
  weeklyRate        Decimal?
  monthlyRate       Decimal?
  
  // Specifications
  transmission      String
  fuelType          String
  seats             Int
  color             String
  
  // Availability
  status            VehicleStatus    @default(AVAILABLE)
  
  // Location & Ownership
  providerId        String
  provider          User             @relation(fields: [providerId], references: [id])
  
  // Media
  coverImage        String?
  images            VehicleImage[]
  
  // Availability Calendar
  bookings          Booking[]
  unavailablePeriods UnavailablePeriod[]
  
  // Metadata
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  
  // Constraints
  @@index([providerId])
  @@index([category])
  @@index([status])
}

model VehicleImage {
  id        String   @id @default(cuid())
  vehicleId String
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  
  url       String
  isDefault Boolean  @default(false)
  
  createdAt DateTime @default(now())
  
  @@index([vehicleId])
}

model UnavailablePeriod {
  id        String   @id @default(cuid())
  vehicleId String
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  
  startDate DateTime
  endDate   DateTime
  reason    String?  // Maintenance, Booked, etc.
  
  @@index([vehicleId])
  @@index([startDate, endDate])
}

model Availability {
  id            String      @id @default(cuid())
  vehicle       Vehicle     @relation(fields: [vehicleId], references: [id])
  vehicleId     String
  
  startDate     DateTime
  endDate       DateTime
  
  isAvailable   Boolean     @default(true)
  
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

model Booking {
  id            String        @id @default(uuid())
  status        BookingStatus @default(PENDING)
  paymentStatus String?       @default("PENDING")

  // Relationships
  userId        String
  user          User          @relation(fields: [userId], references: [id])
  vehicleId     String
  vehicle       Vehicle       @relation(fields: [vehicleId], references: [id])

  // Booking Details
  startDate     DateTime
  endDate       DateTime
  totalPrice    Decimal       @default(0)

  // Payment Integration
  payments      Payment[]

  // Additional Features
  sosAlerts     SOSAlert[]
  rideChecklists RideChecklist[]
  damageReports DamageReport[]
  bookingHistory BookingHistory?

  // Timestamps
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([userId])
  @@index([vehicleId])
  @@index([status])
}

model Discount {
  id            String      @id @default(cuid())
  code          String      @unique
  description   String?
  
  discountType  String      // percentage, fixed amount
  value         Float
  
  startDate     DateTime?
  endDate       DateTime?
  
  minBookingAmount Float?
  
  isActive      Boolean     @default(true)
  
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

model PricingLog {
  id                String        @id @default(cuid())
  vehicleId         String
  basePrice         Decimal
  adjustedPrice     Decimal
  seasonality       Float
  demandForecast    Float
  historicalOccupancy Float
  competitorPrices  Float
  specialEvents     Boolean
  createdAt         DateTime      @default(now())
}

model AnalyticsEvent {
  id                String        @id @default(cuid())
  eventType         String
  userId            String?
  vehicleId         String?
  bookingId         String?
  metadata          Json?
  timestamp         DateTime      @default(now())
}

model SupportTicket {
  id                String        @id @default(cuid())
  userId            String
  user              User          @relation(fields: [userId], references: [id])
  title             String
  description       String
  status            String        @default("OPEN")
  priority          String        @default("MEDIUM")
  assignedToId      String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
}

model SOSAlert {
  id          String         @id @default(cuid())
  bookingId   String
  userId      String?
  providerId  String?
  reason      String
  status      SOSAlertStatus @default(PENDING)
  createdAt   DateTime       @default(now())
  user        User?          @relation(fields: [userId], references: [id])
  provider    User?          @relation("ProviderRelation", fields: [providerId], references: [id])
  booking     Booking        @relation(fields: [bookingId], references: [id])
}

model SystemAlert {
  id         String   @id @default(uuid())
  severity   AlertSeverity
  message    String
  context    String?
  createdAt  DateTime @default(now())
}

model Payment {
  id                    String        @id @default(uuid())
  amount                Int
  currency              String        @default("usd")
  status                String        @default("PENDING")
  paymentMethod         PaymentMethod @default(STRIPE)

  // Stripe Integration
  stripePaymentIntentId String?       @unique

  // Relationships
  bookingId             String?       @unique
  booking               Booking?      @relation(fields: [bookingId], references: [id])
  userId                String
  user                  User          @relation(fields: [userId], references: [id])
  providerId            String
  provider              User          @relation("ProviderPayments", fields: [providerId], references: [id])

  // Timestamps
  createdAt             DateTime      @default(now())
  updatedAt             DateTime      @updatedAt

  @@index([userId])
  @@index([providerId])
  @@index([status])
}

model ProviderBankDetails {
  id              String    @id @default(uuid())
  providerId      String
  provider        User      @relation(fields: [providerId], references: [id])
  accountHolder   String
  accountNumber   String
  bankName        String
  branchCode      String?
  swiftCode       String?
  iban            String?
  isVerified      Boolean   @default(false)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model RideChecklist {
  id            String            @id @default(cuid())
  bookingId     String
  booking       Booking           @relation(fields: [bookingId], references: [id])
  userId        String
  user          User              @relation(fields: [userId], references: [id])
  vehicleId     String
  vehicle       Vehicle           @relation(fields: [vehicleId], references: [id])
  stage         RideChecklistStage
  conditionNotes String?
  imageUrls     String[]
  createdAt     DateTime          @default(now())
  damageReports DamageReport[]
}

model DamageReport {
  id            String         @id @default(cuid())
  bookingId     String
  booking       Booking        @relation(fields: [bookingId], references: [id])
  userId        String
  user          User           @relation(fields: [userId], references: [id])
  rideChecklistId String?
  rideChecklist RideChecklist? @relation(fields: [rideChecklistId], references: [id])
  note          String
  images        String[]
  severity      DamageReportSeverity @default(MINOR)
  status        DamageReportStatus   @default(PENDING)
  createdAt     DateTime       @default(now())
}

model SavedVehicle {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  vehicleId String
  vehicle   Vehicle  @relation(fields: [vehicleId], references: [id])
  createdAt DateTime @default(now())

  @@unique([userId, vehicleId])
}

model LoyaltyPoints {
  id        String   @id @default(cuid())
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id])
  balance   Int      @default(0)
  totalEarned Int    @default(0)
  lastEarnedAt DateTime?
}

model BookingHistory {
  id          String   @id @default(cuid())
  bookingId   String   @unique
  booking     Booking  @relation(fields: [bookingId], references: [id])
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  providerId  String
  provider    User     @relation("ProviderBookings", fields: [providerId], references: [id])
  vehicleId   String
  vehicle     Vehicle  @relation(fields: [vehicleId], references: [id])
  pointsEarned Int     @default(0)
  pointsRedeemed Int   @default(0)
  discountApplied Decimal @default(0)
  status      BookingHistoryStatus @default(COMPLETED)
  createdAt   DateTime @default(now())
}

model EmailVerificationToken {
  id                   String                @id @default(cuid())
  userId               String                @unique
  token                String                @unique
  expiresAt            DateTime
  
  // Timestamps
  createdAt            DateTime             @default(now())
}

// Optional: Location-based pricing model
model LocationPricing {
  id          String   @id @default(cuid())
  areaId      String
  area        Area     @relation(fields: [areaId], references: [id])
  
  baseRate    Decimal
  deliveryFee Decimal?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Append these models at the end of the file
model AnalyticsDashboardWidget {
  id            String   @id @default(cuid())
  userRole      String   // 'admin', 'owner'
  widgetType    String   // 'kpi', 'chart', 'table'
  name          String
  config        Json
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model ScheduledReport {
  id            String   @id @default(cuid())
  name          String
  frequency     String   // 'daily', 'weekly', 'monthly'
  recipients    String[]
  reportConfig  Json
  lastRunAt     DateTime?
  nextRunAt     DateTime
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model GeospatialSearchLog {
  id            String   @id @default(cuid())
  userId        String?
  searchParams  Json
  resultCount   Int
  latitude      Float?
  longitude     Float?
  radius        Float?
  searchedAt    DateTime @default(now())
}