{"name": "rentahub-platform", "version": "1.0.0", "description": "Premium vehicle rental platform connecting customers with trusted providers", "homepage": "https://rentahub.info", "repository": {"type": "git", "url": "https://github.com/YOUR_USERNAME/rentahub-platform.git"}, "bugs": {"url": "https://github.com/YOUR_USERNAME/rentahub-platform/issues", "email": "<EMAIL>"}, "author": "RentaHub Team <<EMAIL>>", "license": "MIT", "keywords": ["vehicle-rental", "car-rental", "bike-rental", "scooter-rental", "rental-platform", "react", "nodejs", "typescript", "supabase", "stripe", "railway"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "@paypal/paypal-server-sdk": "^1.1.0", "@supabase/supabase-js": "^2.52.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-rate-limit": "^6.0.2", "@types/helmet": "^4.0.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "react-hook-form": "^7.60.0", "stripe": "^12.18.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@types/jest": "^29.5.14", "@types/node": "^20.4.2", "@types/paypal__checkout-server-sdk": "^1.0.3", "@types/stripe": "^8.0.417", "axios": "^1.6.0", "concurrently": "^8.2.2", "helmet": "^8.1.0", "husky": "^9.1.7", "jest": "^30.0.3", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "scripts": {"start": "node start.js", "test": "jest", "generate-types": "prisma generate", "test:all": "jest --coverage --runInBand", "test:coverage": "jest --coverage", "benchmark": "node scripts/benchmark.js", "maintenance": "bash scripts/maintenance.sh", "test:watch": "jest --watch", "dev": "nodemon --exec tsx src/index.ts", "prepare": "husky install", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:admin": "cd admin && npm run dev", "build": "./scripts/build-all.sh", "build:production": "./scripts/build-all.sh --environment production", "build:staging": "./scripts/build-all.sh --environment staging", "build:clean": "./scripts/build-all.sh --clean", "build:parallel": "./scripts/build-all.sh --parallel", "build:all": "npm run build:backend && npm run build:frontend && npm run build:admin", "build:backend": "cd backend && npm install && npm run build", "build:frontend": "cd frontend && npm install && npm run build", "build:admin": "cd admin && npm install && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../admin && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules admin/node_modules", "clean:build": "rm -rf backend/dist frontend/dist admin/dist", "validate": "./scripts/validate-build.sh", "deploy:staging": "./scripts/deploy.sh --environment staging", "deploy:production": "./scripts/deploy.sh --environment production", "deploy": "./deploy.sh all", "deploy:backend": "./deploy.sh backend", "deploy:frontend": "./deploy.sh frontend", "deploy:admin": "./deploy.sh admin", "deploy:status": "./deploy.sh status"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml}": ["prettier --write"]}}