#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Detect the service type based on the current directory and files
function detectService() {
  const cwd = process.cwd();
  console.log('Current working directory:', cwd);
  
  // Check for frontend-specific files first (both in current dir and frontend subdir)
  if (fs.existsSync('vite.config.ts') && fs.existsSync('src/main.tsx')) {
    console.log('Detected frontend service (vite.config.ts + src/main.tsx)');
    return 'frontend';
  }
  
  // Check if we have a frontend subdirectory with the right files
  if (fs.existsSync('frontend/vite.config.ts') && fs.existsSync('frontend/src/main.tsx')) {
    console.log('Detected frontend service (frontend/vite.config.ts + frontend/src/main.tsx)');
    return 'frontend';
  }
  
  // Check for admin-specific files
  if (fs.existsSync('vite.config.ts') && fs.existsSync('src/pages') && !fs.existsSync('src/main.tsx')) {
    console.log('Detected admin service (vite.config.ts + src/pages)');
    return 'admin';
  }
  
  // Check if we have an admin subdirectory with the right files
  if (fs.existsSync('admin/vite.config.ts') && fs.existsSync('admin/src/pages')) {
    console.log('Detected admin service (admin/vite.config.ts + admin/src/pages)');
    return 'admin';
  }
  
  // Check for backend-specific files
  if (fs.existsSync('prisma/schema.prisma') || fs.existsSync('src/index.ts')) {
    console.log('Detected backend service (prisma or src/index.ts)');
    return 'backend';
  }

  // Check if we have a backend subdirectory with the right files
  if (fs.existsSync('backend/prisma/schema.prisma') || fs.existsSync('backend/src/index.ts')) {
    console.log('Detected backend service (backend/prisma or backend/src/index.ts)');
    return 'backend';
  }
  
  // Check if we're in a specific service directory by path
  if (cwd.includes('/frontend')) {
    console.log('Detected frontend service (path contains /frontend)');
    return 'frontend';
  }
  if (cwd.includes('/backend')) {
    console.log('Detected backend service (path contains /backend)');
    return 'backend';
  }
  if (cwd.includes('/admin')) {
    console.log('Detected admin service (path contains /admin)');
    return 'admin';
  }
  
  // Default to backend if no clear indicators
  console.log('Defaulting to backend service');
  return 'backend';
}

function startService(service) {
  console.log(`Starting ${service} service...`);
  
  try {
    switch (service) {
      case 'frontend':
        // Check if we need to change to frontend directory
        if (fs.existsSync('frontend/vite.config.ts')) {
          console.log('Changing to frontend directory...');
          process.chdir('frontend');
        }
        
        // Install dependencies if node_modules doesn't exist
        if (!fs.existsSync('node_modules')) {
          console.log('Installing frontend dependencies...');
          execSync('npm install', { stdio: 'inherit' });
        }
        
        // Build only if dist directory doesn't exist
        if (!fs.existsSync('dist')) {
          console.log('Building frontend...');
          execSync('npm run build:production', { stdio: 'inherit' });
        } else {
          console.log('Frontend already built, skipping build step...');
        }
        
        console.log('Starting frontend server...');
        execSync('npx serve -s dist -l 4173', { stdio: 'inherit' });
        break;
        
      case 'admin':
        // Check if we need to change to admin directory
        if (fs.existsSync('admin/vite.config.ts')) {
          console.log('Changing to admin directory...');
          process.chdir('admin');
        }
        
        // Install dependencies if node_modules doesn't exist
        if (!fs.existsSync('node_modules')) {
          console.log('Installing admin dependencies...');
          execSync('npm install', { stdio: 'inherit' });
        }
        
        // Build only if dist directory doesn't exist
        if (!fs.existsSync('dist')) {
          console.log('Building admin...');
          execSync('npm run build:production', { stdio: 'inherit' });
        } else {
          console.log('Admin already built, skipping build step...');
        }
        
        console.log('Starting admin server...');
        execSync('npx serve -s dist -l 4173', { stdio: 'inherit' });
        break;
        
      case 'backend':
      default:
        // Check if we need to change to backend directory
        if (fs.existsSync('backend/src/index.ts')) {
          console.log('Changing to backend directory...');
          process.chdir('backend');
        }
        console.log('Starting backend server...');
        execSync('tsx src/index.ts', { stdio: 'inherit' });
        break;
    }
  } catch (error) {
    console.error(`Failed to start ${service}:`, error.message);
    process.exit(1);
  }
}

const service = detectService();
startService(service);
