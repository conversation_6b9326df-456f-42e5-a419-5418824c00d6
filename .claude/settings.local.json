{"permissions": {"allow": ["<PERSON><PERSON>(claude)", "<PERSON><PERSON>(claude --ide)", "Bash(EDITOR=cursor claude --ide)", "<PERSON><PERSON>(git clone:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(npm --version)", "Bash(node:*)", "Bash(npm install)", "<PERSON><PERSON>(touch:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run install:all:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(git init:*)", "Bash(git remote add:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(cp:*)", "Bash(npm run install:admin:*)", "Bash(npx prisma generate:*)", "Bash(npm test)", "Bash(npx tsc:*)", "Bash(npm start)", "Bash(npx tsx:*)", "Bash(stripe:*)", "<PERSON><PERSON>(tsx:*)", "mcp__ide__getDiagnostics", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(mv:*)", "Bash(railway up:*)", "WebFetch(domain:railway.com)", "Bash(npm uninstall:*)", "Bash(railway status:*)", "<PERSON><PERSON>(sed:*)", "Bash(railway service:*)", "Bash(railway link:*)", "<PERSON><PERSON>(pkill:*)", "Bash(PORT=3002 npm start)", "Bash(PORT=3001 npm start)", "Bash(NODE_ENV=development npx tsx src/index.ts)", "Bash(PORT=3001 NODE_ENV=development npx tsx src/index.ts)", "Bash(PORT=3003 NODE_ENV=development npx tsx src/index.ts)", "Bash(ps:*)", "Bash(kill:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(true)", "Bash(npm cache clean:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-servers.sh:*)"], "deny": []}}