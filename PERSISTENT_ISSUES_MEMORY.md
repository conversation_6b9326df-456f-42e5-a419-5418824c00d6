# PERSISTENT ISSUES MEMORY - DO NOT BREAK WORKING FUNCTIONALITY

## CRITICAL: Always Check This File Before Making Changes

### 1. VEHICLE LISTING PAGE ISSUES

#### Problem: "Cannot read properties of null (reading 'filter')" Error
- **Root Cause**: VehicleList.tsx tries to call `.filter()` on `null` or `undefined` vehicles data
- **Solution**: Always use defensive programming:
  ```typescript
  const safeVehicles = Array.isArray(vehicles) ? vehicles : [];
  // Use safeVehicles for all array operations (filter, map, etc.)
  ```
- **Location**: `frontend/src/components/VehicleList.tsx`
- **Status**: ✅ FIXED - Must maintain this pattern

#### Problem: Backend Crashes Due to Missing Database Columns
- **Root Cause**: Database schema mismatch (e.g., missing `availableUnits` column)
- **Solution**: Backend must gracefully handle missing columns and use JSON backup data
- **Location**: `backend/src/controllers/vehicleController.ts`
- **Status**: ✅ FIXED - Using JSON backup data

#### Problem: Frontend Calls Wrong API Endpoint
- **Root Cause**: VehicleSearch.tsx calls `/api/vehicles` which resolves to frontend server instead of backend
- **Solution**: Use full backend URL: `http://localhost:3001/api/vehicles`
- **Location**: `frontend/src/components/VehicleSearch.tsx`
- **Status**: ✅ FIXED - Using correct backend URL

### 2. HOMEPAGE ISSUES

#### Problem: Homepage Shows Blank/No Featured Vehicles
- **Root Cause**: HomePage.tsx API call failing silently, fallback data not being set properly
- **Solution**: HomePage.tsx already has 6 fallback vehicles, but API call might be failing
- **Location**: `frontend/src/pages/HomePage.tsx` (lines 65-95)
- **Status**: ✅ FIXED - HomePage has 6 fallback vehicles, should show content
- **Debug Steps**:
  1. Check if backend API is working: `curl localhost:3001/api/vehicles` ✅
  2. Check if frontend is running: `curl localhost:5173/` ✅
  3. Check browser console for API errors
  4. Verify fallback data has 6 vehicles ✅
  5. Check if apiService.getScrapedVehicles() is working correctly
- **Current State**: HomePage.tsx has 6 fallback vehicles and should display them even if API fails

### 3. WEB SOCKET CONNECTION ISSUES

#### Problem: WebSocket Connection Failures
- **Root Cause**: Socket.io trying to connect to `ws://localhost:3001/socket.io/` but backend doesn't have socket.io
- **Solution**: Remove socket.io client or disable WebSocket connections
- **Location**: `frontend/src/services/NotificationService.ts` (line 34)
- **Status**: ⚠️ NEEDS FIX - Disable WebSocket connections

#### Problem: API Calls to `/vehicles/undefined`
- **Root Cause**: VehicleDetailPage calling `/api/vehicles/undefined` instead of `/api/vehicles/{id}`
- **Solution**: Fix vehicle ID parameter in API call
- **Location**: `frontend/src/pages/VehicleDetailPage.tsx` (line 94)
- **Status**: ⚠️ NEEDS FIX - Check vehicle ID parameter

#### Problem: React Key Warnings
- **Root Cause**: HomePage Grid2 components missing unique `key` props
- **Solution**: Add unique keys to mapped components
- **Location**: `frontend/src/pages/HomePage.tsx` (lines 281, 300, 319, 346, 356, 366, 376, 395, 406, 417, 619, 634, 649)
- **Status**: ✅ FIXED - Added unique keys to all Grid items
- **Fixed Sections**:
  - Vehicle Showcase section: Added keys "showcase-scooter", "showcase-motorcycle", "showcase-sportbike"
  - Quick Stats section: Added keys "stats-vehicles", "stats-cities", "stats-rating", "stats-support"
  - Features section: Added keys "feature-secure", "feature-convenient", "feature-support"
  - Testimonials section: Added keys "testimonial-budi", "testimonial-sari", "testimonial-ahmad"

### 4. SUPABASE CLIENT ISSUES

#### Problem: Multiple Supabase Client Instances
- **Root Cause**: Multiple supabaseClient files creating different instances
- **Solution**: Use singleton pattern and import from utils/supabaseClient.ts
- **Location**: `frontend/src/services/supabaseClient.ts`
- **Status**: ✅ FIXED - Using singleton pattern

#### Problem: MonitoringService Import Error
- **Root Cause**: MonitoringService.ts trying to import from wrong supabaseClient file
- **Solution**: Fix import to use correct supabaseClient path
- **Location**: `frontend/src/services/MonitoringService.ts`
- **Status**: ✅ FIXED - Changed import from './supabaseClient' to '../utils/supabaseClient'
- **Error**: "The requested module '/src/services/supabaseClient.ts' does not provide an export named 'supabase'"

### 5. VITE CONFIGURATION ISSUES

#### Problem: Vite Config Loading Failures
- **Root Cause**: ESM/CommonJS module conflicts in vite.config.ts
- **Solution**: Use proper ESM syntax and avoid require() statements
- **Location**: `frontend/vite.config.ts`
- **Status**: ⚠️ NEEDS FIX - Fix ESM imports

### 6. BACKEND PORT CONFLICTS

#### Problem: EADDRINUSE Port 3001 Already in Use
- **Root Cause**: Multiple backend instances running simultaneously
- **Solution**: Kill existing processes and restart cleanly
- **Command**: `lsof -ti:3001 | xargs kill -9`
- **Status**: ⚠️ NEEDS FIX - Clean process management

## COMPARTMENTALIZATION RULES

### ✅ DO:
- Only change the specific file mentioned by user
- Test backend API first: `curl localhost:3001/api/vehicles`
- Use defensive programming (Array.isArray, null checks)
- Maintain working fallback data
- Check memory file before any changes

### ❌ DON'T:
- Touch files not specifically mentioned
- Change API endpoints without testing
- Remove defensive programming patterns
- Break working fallback mechanisms
- Modify database schema without backup

## DEBUGGING CHECKLIST

Before making any changes:
1. ✅ Check if backend is running: `curl localhost:3001/health`
2. ✅ Check if frontend is running: `curl localhost:5173/`
3. ✅ Test API endpoints: `curl localhost:3001/api/vehicles`
4. ✅ Check browser console for errors
5. ✅ Verify no port conflicts: `lsof -i :3001` and `lsof -i :5173`

## EMERGENCY ROLLBACK

If something breaks:
1. Stop all servers: `pkill -f "npm run dev"`
2. Clear ports: `lsof -ti:3001 | xargs kill -9 && lsof -ti:5173 | xargs kill -9`
3. Restart backend: `cd backend && npm run dev`
4. Restart frontend: `cd frontend && npm run dev`
5. Test API: `curl localhost:3001/api/vehicles`

## CURRENT WORKING STATE

### ✅ WORKING:
- Backend API: `http://localhost:3001/api/vehicles` returns 78 vehicles
- Frontend: `http://localhost:5173/` loads homepage
- Vehicle listing page: Shows vehicles with defensive programming
- Homepage: Shows featured vehicles section

### ⚠️ NEEDS ATTENTION:
- WebSocket connections (disabled)
- Multiple Supabase instances (minor)
- Vite config issues (development only)

## LAST UPDATED: 2024-01-12
## LAST ISSUES FIXED: HomePage vehicle showcase Grid key warnings 