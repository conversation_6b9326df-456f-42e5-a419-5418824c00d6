-- Disable R<PERSON> for key tables to allow admin access
ALTER TABLE "User" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Vehicle" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Booking" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "vehicle_catalog" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "listed_vehicles" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Payment" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Review" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Notification" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "support_tickets" DISABLE ROW LEVEL SECURITY;
ALTER TABLE "Refund" DISABLE ROW LEVEL SECURITY;

-- Verify tables are accessible
SELECT 'User' as table_name, count(*) as records FROM "User"
UNION ALL
SELECT 'Vehicle', count(*) FROM "Vehicle"
UNION ALL  
SELECT 'Booking', count(*) FROM "Booking"
UNION ALL
SELECT 'vehicle_catalog', count(*) FROM "vehicle_catalog"
UNION ALL
SELECT 'listed_vehicles', count(*) FROM "listed_vehicles";
