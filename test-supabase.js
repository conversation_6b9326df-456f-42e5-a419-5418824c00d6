#!/usr/bin/env node

/**
 * Quick Supabase Connection Test
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('🔍 Testing Supabase Connection...\n');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

console.log('📋 Configuration:');
console.log(`   URL: ${supabaseUrl}`);
console.log(`   Service Key: ${supabaseServiceKey ? `${supabaseServiceKey.substring(0, 20)}...` : 'NOT SET'}`);
console.log(`   Anon Key: ${supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET'}`);

if (!supabaseUrl || !supabaseServiceKey) {
  console.log('❌ Missing Supabase credentials');
  process.exit(1);
}

async function testConnection() {
  try {
    // Test with service role key first
    console.log('\n🔍 Testing with Service Role Key...');
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Try a simple auth test first
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.listUsers();

    if (authError) {
      console.log(`❌ Service Role Auth Error: ${authError.message}`);
    } else {
      console.log(`✅ Service Role Auth successful! Found ${authData.users?.length || 0} users`);
    }

    // Test with anon key
    console.log('\n🔍 Testing with Anon Key...');
    const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

    const { data, error } = await supabaseAnon.auth.getSession();
    
    if (error) {
      console.log(`❌ Connection Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      console.log(`   Details: ${error.details}`);
      return false;
    }
    
    console.log('✅ Supabase connection successful!');
    console.log(`   Response: ${JSON.stringify(data)}`);
    return true;
    
  } catch (error) {
    console.log(`❌ Connection Exception: ${error.message}`);
    return false;
  }
}

testConnection().then(success => {
  if (success) {
    console.log('\n🎉 Supabase is ready!');
  } else {
    console.log('\n⚠️  Supabase connection failed.');
  }
  process.exit(success ? 0 : 1);
});
