<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RentaHub i18n Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .test-container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 20px;
        }
        .language-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
        }
        .lang-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .lang-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .lang-btn.active {
            background: #ffd700;
            color: #333;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
        }
        .test-key {
            font-family: monospace;
            background: rgba(0,0,0,0.3);
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 10px;
        }
        .test-value {
            font-weight: bold;
            font-size: 18px;
        }
        .instructions {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌐 RentaHub i18n Language Switching Test</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li>Open your RentaHub application at <strong>http://localhost:5174/</strong></li>
                <li>Click on different language buttons below to test each language</li>
                <li>Verify that the homepage text changes to the selected language</li>
                <li>Check that the language selector in the app shows the correct language</li>
                <li>Confirm that search placeholders and buttons update correctly</li>
            </ol>
        </div>

        <div class="language-buttons">
            <button class="lang-btn active" data-lang="en">🇺🇸 English</button>
            <button class="lang-btn" data-lang="id">🇮🇩 Bahasa Indonesia</button>
            <button class="lang-btn" data-lang="zh">🇨🇳 中文</button>
            <button class="lang-btn" data-lang="ar">🇸🇦 العربية</button>
            <button class="lang-btn" data-lang="es">🇪🇸 Español</button>
            <button class="lang-btn" data-lang="fr">🇫🇷 Français</button>
            <button class="lang-btn" data-lang="de">🇩🇪 Deutsch</button>
            <button class="lang-btn" data-lang="ja">🇯🇵 日本語</button>
            <button class="lang-btn" data-lang="ko">🇰🇷 한국어</button>
            <button class="lang-btn" data-lang="pt">🇧🇷 Português</button>
            <button class="lang-btn" data-lang="ru">🇷🇺 Русский</button>
        </div>

        <div id="current-language" class="status success">
            Current Language: <strong>English (en)</strong>
        </div>

        <div class="test-section">
            <h3>🏠 Homepage Hero Section</h3>
            <div><span class="test-key">homepage.hero.title</span><span class="test-value" id="hero-title">Rent Vehicles</span></div>
            <div><span class="test-key">homepage.hero.subtitle</span><span class="test-value" id="hero-subtitle">Across SE Asia</span></div>
            <div><span class="test-key">homepage.hero.description</span><span class="test-value" id="hero-description">Find and rent motorcycles, scooters, and luxury bikes from trusted providers in your area</span></div>
        </div>

        <div class="test-section">
            <h3>🔍 Search Section</h3>
            <div><span class="test-key">homepage.search.location</span><span class="test-value" id="search-location">Enter Location</span></div>
            <div><span class="test-key">homepage.search.searchButton</span><span class="test-value" id="search-button">Search Vehicles</span></div>
            <div><span class="test-key">homepage.search.mapView</span><span class="test-value" id="search-mapview">Map View</span></div>
        </div>

        <div class="test-section">
            <h3>🚗 Features Section</h3>
            <div><span class="test-key">homepage.features.title</span><span class="test-value" id="features-title">Featured Vehicles</span></div>
            <div><span class="test-key">homepage.features.comingSoon</span><span class="test-value" id="features-coming">Coming Soon!</span></div>
        </div>

        <div class="test-section">
            <h3>🌍 Language & Navigation</h3>
            <div><span class="test-key">language.select</span><span class="test-value" id="lang-select">Select Language</span></div>
            <div><span class="test-key">nav.home</span><span class="test-value" id="nav-home">Home</span></div>
            <div><span class="test-key">nav.vehicles</span><span class="test-value" id="nav-vehicles">Vehicles</span></div>
        </div>
    </div>

    <script>
        // Translation data for testing
        const translations = {
            en: {
                'homepage.hero.title': 'Rent Vehicles',
                'homepage.hero.subtitle': 'Across SE Asia',
                'homepage.hero.description': 'Find and rent motorcycles, scooters, and luxury bikes from trusted providers in your area',
                'homepage.search.location': 'Enter Location',
                'homepage.search.searchButton': 'Search Vehicles',
                'homepage.search.mapView': 'Map View',
                'homepage.features.title': 'Featured Vehicles',
                'homepage.features.comingSoon': 'Coming Soon!',
                'language.select': 'Select Language',
                'nav.home': 'Home',
                'nav.vehicles': 'Vehicles'
            },
            id: {
                'homepage.hero.title': 'Sewa Kendaraan',
                'homepage.hero.subtitle': 'Di Seluruh Asia Tenggara',
                'homepage.hero.description': 'Temukan dan sewa sepeda motor, skuter, dan sepeda mewah dari penyedia terpercaya di daerah Anda',
                'homepage.search.location': 'Masukkan Lokasi',
                'homepage.search.searchButton': 'Cari Kendaraan',
                'homepage.search.mapView': 'Tampilan Peta',
                'homepage.features.title': 'Kendaraan Unggulan',
                'homepage.features.comingSoon': 'Segera Hadir!',
                'language.select': 'Pilih Bahasa',
                'nav.home': 'Beranda',
                'nav.vehicles': 'Kendaraan'
            },
            zh: {
                'homepage.hero.title': '租赁车辆',
                'homepage.hero.subtitle': '遍布东南亚',
                'homepage.hero.description': '从您所在地区的可信赖供应商处寻找并租赁摩托车、踏板车和豪华自行车',
                'homepage.search.location': '输入位置',
                'homepage.search.searchButton': '搜索车辆',
                'homepage.search.mapView': '地图视图',
                'homepage.features.title': '精选车辆',
                'homepage.features.comingSoon': '即将推出！',
                'language.select': '选择语言',
                'nav.home': '首页',
                'nav.vehicles': '车辆'
            },
            ar: {
                'homepage.hero.title': 'استأجر المركبات',
                'homepage.hero.subtitle': 'في جميع أنحاء جنوب شرق آسيا',
                'homepage.hero.description': 'ابحث واستأجر الدراجات النارية والسكوتر والدراجات الفاخرة من مقدمي الخدمة الموثوقين في منطقتك',
                'homepage.search.location': 'أدخل الموقع',
                'homepage.search.searchButton': 'البحث عن المركبات',
                'homepage.search.mapView': 'عرض الخريطة',
                'homepage.features.title': 'المركبات المميزة',
                'homepage.features.comingSoon': 'قريباً!',
                'language.select': 'اختر اللغة',
                'nav.home': 'الرئيسية',
                'nav.vehicles': 'المركبات'
            },
            es: {
                'homepage.hero.title': 'Alquilar Vehículos',
                'homepage.hero.subtitle': 'En Todo el Sudeste Asiático',
                'homepage.hero.description': 'Encuentra y alquila motocicletas, scooters y bicicletas de lujo de proveedores confiables en tu área',
                'homepage.search.location': 'Ingresa Ubicación',
                'homepage.search.searchButton': 'Buscar Vehículos',
                'homepage.search.mapView': 'Vista de Mapa',
                'homepage.features.title': 'Vehículos Destacados',
                'homepage.features.comingSoon': '¡Próximamente!',
                'language.select': 'Seleccionar Idioma',
                'nav.home': 'Inicio',
                'nav.vehicles': 'Vehículos'
            }
        };

        // Language names
        const languageNames = {
            en: 'English (en)',
            id: 'Bahasa Indonesia (id)',
            zh: '中文 (zh)',
            ar: 'العربية (ar)',
            es: 'Español (es)',
            fr: 'Français (fr)',
            de: 'Deutsch (de)',
            ja: '日本語 (ja)',
            ko: '한국어 (ko)',
            pt: 'Português (pt)',
            ru: 'Русский (ru)'
        };

        // Update display for selected language
        function updateLanguageDisplay(lang) {
            const langData = translations[lang];
            if (!langData) {
                document.getElementById('current-language').innerHTML = `<strong>Language: ${languageNames[lang] || lang}</strong> - Translation data not available in this test`;
                return;
            }

            document.getElementById('current-language').innerHTML = `Current Language: <strong>${languageNames[lang]}</strong>`;
            
            // Update all test values
            Object.keys(langData).forEach(key => {
                const elementId = key.replace(/\./g, '-').replace('homepage-', '').replace('nav-', 'nav-');
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = langData[key];
                }
            });

            // Update RTL for Arabic
            if (lang === 'ar') {
                document.body.style.direction = 'rtl';
            } else {
                document.body.style.direction = 'ltr';
            }
        }

        // Add click handlers to language buttons
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // Remove active class from all buttons
                document.querySelectorAll('.lang-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                btn.classList.add('active');
                
                const lang = btn.dataset.lang;
                updateLanguageDisplay(lang);
            });
        });
    </script>
</body>
</html>
