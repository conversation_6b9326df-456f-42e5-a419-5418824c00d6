#!/bin/bash

echo "🔍 RentaHub Health Status Checker"
echo "================================="

echo ""
echo "1. 🖥️  Checking Backend Server (http://localhost:3001)..."
if curl -s http://localhost:3001/health > /dev/null 2>&1; then
    echo "   ✅ Backend is RUNNING"
    echo "   📊 Health response:"
    curl -s http://localhost:3001/health | jq 2>/dev/null || curl -s http://localhost:3001/health
else
    echo "   ❌ Backend is NOT RUNNING"
    echo "   💡 Try: cd backend && npm run dev"
fi

echo ""
echo "2. 🌐 Checking Frontend Server (http://localhost:5173)..."
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "   ✅ Frontend is RUNNING"
    echo "   🔗 Open: http://localhost:5173"
else
    echo "   ❌ Frontend is NOT RUNNING" 
    echo "   💡 Try: cd frontend && npm run dev"
fi

echo ""
echo "3. 🔧 Checking Environment Configuration..."
if [ -f "/Users/<USER>/Desktop/RENTAHUB/backend/.env" ]; then
    echo "   ✅ Backend .env exists"
else
    echo "   ❌ Backend .env missing"
fi

if [ -f "/Users/<USER>/Desktop/RENTAHUB/frontend/.env" ]; then
    echo "   ✅ Frontend .env exists"
else
    echo "   ❌ Frontend .env missing"
fi

echo ""
echo "🎯 Next Steps:"
echo "   - If backend is not running: The environment fixes should auto-restart it"
echo "   - If frontend is not running: Start with 'npm run dev' in frontend directory"
echo "   - Both should now show healthy status!"
