const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase credentials - using service role key for INSERT permissions
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function seedVehiclesToSupabase() {
  try {
    console.log('🚀 Starting vehicle seeding to Supabase vehicles table...');

    // Load scraped data from the JSON file
    const jsonPath = path.join(__dirname, 'indonesia_scooters_essential.json');
    
    if (!fs.existsSync(jsonPath)) {
      console.error('❌ Vehicle data file not found:', jsonPath);
      return;
    }

    const jsonContent = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    const vehicleData = jsonContent.data || [];
    console.log(`📊 Found ${vehicleData.length} vehicles to seed`);

    if (vehicleData.length === 0) {
      console.log('❌ No vehicles found to seed');
      return;
    }

    let importedCount = 0;
    let skippedCount = 0;

    // Transform and insert vehicles in batches
    const batchSize = 10;
    for (let i = 0; i < vehicleData.length; i += batchSize) {
      const batch = vehicleData.slice(i, i + batchSize);
      
      const vehiclesToInsert = batch.map(vehicle => {
        // Determine category based on engine size
        let category = 'small_scooter';
        const engineSize = parseInt(vehicle.engineSize?.replace('cc', '')) || 125;
        if (engineSize >= 200) category = 'luxury_bike';
        else if (engineSize >= 150) category = 'large_scooter';
        else if (engineSize >= 125) category = 'medium_scooter';
        else category = 'small_scooter';

        return {
          provider_id: null, // Will be set by actual providers
          category: category,
          make: vehicle.make || vehicle.brand,
          model: vehicle.model,
          year: parseInt(vehicle.year) || 2024,
          engine_size: engineSize,
          transmission: vehicle.transmission || 'automatic',
          fuel_type: 'petrol',
          description: `${vehicle.make || vehicle.brand} ${vehicle.model} - ${vehicle.engineSize || '125cc'} ${vehicle.transmission || 'automatic'}. Perfect for exploring Indonesia.`,
          images: vehicle.images || [],
          daily_rate: vehicle.price ? parseFloat(vehicle.price) : 50000,
          weekly_rate: vehicle.price ? parseFloat(vehicle.price) * 7 * 0.9 : 315000,
          monthly_rate: vehicle.price ? parseFloat(vehicle.price) * 30 * 0.8 : 1200000,
          security_deposit: 500000,
          minimum_rental_days: 1,
          maximum_rental_days: 365,
          delivery_available: false,
          delivery_fee: 0,
          delivery_radius: 0,
          quantity: 1,
          available_quantity: 1,
          features: vehicle.specs ? Object.keys(vehicle.specs) : [],
          add_ons: [],
          location: {
            address: 'Jakarta, Indonesia',
            city: 'Jakarta',
            latitude: -6.2088,
            longitude: 106.8456,
            postal_code: '10110'
          },
          pickup_instructions: 'Meet at our office in Jakarta. Please bring your ID and driving license.',
          active: true
        };
      });

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .insert(vehiclesToInsert)
          .select();

        if (error) {
          console.error(`❌ Error inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
          skippedCount += batch.length;
        } else {
          console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}: ${data?.length || 0} vehicles`);
          importedCount += data?.length || 0;
        }
      } catch (error) {
        console.error(`❌ Error processing batch ${Math.floor(i/batchSize) + 1}:`, error);
        skippedCount += batch.length;
      }
    }

    console.log('\n📈 Seeding Summary:');
    console.log(`✅ Successfully imported: ${importedCount} vehicles`);
    console.log(`⏭️  Skipped (errors): ${skippedCount} vehicles`);
    console.log(`📊 Total processed: ${vehicleData.length} vehicles`);

    // Verify the count
    const { data: countData, error: countError } = await supabase
      .from('vehicles')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('❌ Error counting vehicles:', countError);
    } else {
      console.log(`🎯 Total vehicles in database: ${countData?.length || 0}`);
    }

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  }
}

// Run the seeding
seedVehiclesToSupabase().then(() => {
  console.log('🎉 Vehicle seeding completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Seeding failed:', error);
  process.exit(1);
}); 