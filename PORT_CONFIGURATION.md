# 🔒 RENTAHUB PORT CONFIGURATION - LOCKED IN

## 📋 **STANDARD PORT MAPPING**

| Service | Port | URL | Status |
|---------|------|-----|--------|
| **Backend API** | `3001` | `http://localhost:3001` | 🔒 **LOCKED** |
| **Frontend Dev** | `5173` | `http://localhost:5173` | 🔒 **LOCKED** |
| **Frontend Preview** | `4173` | `http://localhost:4173` | 🔒 **LOCKED** |

## 🏥 **HEALTH CHECK ENDPOINTS**

### Backend Health Checks
- **Main Health**: `http://localhost:3001/health`
- **API Health**: `http://localhost:3001/api/health`

### Frontend Health Check
- **Dev Server**: `http://localhost:5173` (serves React app)
- **Build Preview**: `http://localhost:4173` (serves built app)

## 🔧 **API ENDPOINT STRUCTURE**

### Backend API Routes (Port 3001)
```
http://localhost:3001/api/vehicles          - Vehicle management
http://localhost:3001/api/vehicle-data      - Vehicle data scraping
http://localhost:3001/api/auth              - Authentication
http://localhost:3001/api/bookings          - Booking management  
http://localhost:3001/api/payments          - Payment processing
```

### Frontend Proxy Configuration
- All `/api/*` requests from frontend automatically proxy to `http://localhost:3001`
- WebSocket support enabled for real-time features

## 🛡️ **CORS & SECURITY**

### Allowed Origins (Backend)
```javascript
- http://localhost:5173  // Frontend dev server
- http://127.0.0.1:5173  // Frontend dev server alt
- http://localhost:3000  // Backup frontend port
- http://127.0.0.1:3000  // Backup frontend port alt
```

## 🚀 **STARTUP SEQUENCE**

### 1. Start Backend (Port 3001)
```bash
cd backend
npm start
# OR
npx tsx src/index.ts
```

### 2. Start Frontend (Port 5173)
```bash
cd frontend  
npm run dev
```

### 3. Verify Health Checks
```bash
# Backend health
curl http://localhost:3001/health

# Frontend health (should load React app)
curl http://localhost:5173
```

## 🔄 **BACKUP PORTS**

If primary ports are unavailable:

### Backend Fallback Ports
- Primary: `3001` 🔒
- Backup: `3002`, `3004`, `3005`

### Frontend Fallback
- Vite will auto-increment: `5174`, `5175`, etc.

## 🌍 **ENVIRONMENT VARIABLES**

### Backend (.env)
```bash
PORT=3001                    # Backend port
NODE_ENV=development         # Environment
```

### Frontend (.env)
```bash
VITE_API_URL=http://localhost:3001     # Backend API URL
VITE_APP_URL=http://localhost:5173     # Frontend URL
NODE_ENV=development                   # Environment
```

## ⚠️ **TROUBLESHOOTING**

### Port Conflicts
1. **Backend**: Auto-switches to backup ports (3002, 3004, 3005)
2. **Frontend**: Vite auto-increments port (5174, 5175, etc.)

### Connection Issues
1. Check CORS configuration in backend
2. Verify proxy settings in vite.config.ts
3. Confirm firewall allows localhost connections

---

**🔒 THIS CONFIGURATION IS NOW LOCKED AND SHOULD NOT BE CHANGED WITHOUT UPDATING ALL REFERENCES**