# RentaHub Railway Deployment Guide

This document provides instructions for deploying the RentaHub application to Railway.

## Prerequisites

- GitHub account connected to Railway
- Railway CLI installed (optional)
- Node.js 18.x or higher

## Deployment Configuration

The application is configured for deployment using the following files:

- `railway.json` - Main Railway configuration
- `backend/nixpacks.toml` - Backend build configuration
- `frontend/nixpacks.toml` - Frontend build configuration

## Environment Variables

### Backend Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `SUPABASE_URL` | Supabase project URL | Yes |
| `SUPABASE_KEY` | Supabase service role key | Yes |
| `JWT_SECRET` | Secret for JWT token generation | Yes |
| `PORT` | Port for the backend server (default: 3000) | No |
| `NODE_ENV` | Environment (production, development) | Yes |
| `STRIPE_SECRET_KEY` | Stripe API secret key | Yes |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook signing secret | Yes |

### Frontend Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_API_URL` | Backend API URL | Yes |
| `VITE_SUPABASE_URL` | Supabase project URL | Yes |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |

## Deployment Steps

### Using Railway Dashboard

1. Log in to Railway dashboard
2. Create a new project or select an existing one
3. Add services:
   - Backend service (GitHub repo + path: `/backend`)
   - Frontend service (GitHub repo + path: `/frontend`)
4. Set environment variables for each service
5. Deploy services

### Using Railway CLI

```bash
# Login to Railway
railway login

# Link to project
railway link

# Deploy services
railway up
```

## Health Checks

- Backend health check endpoint: `/health`
- Frontend health check: `/` (root path)

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs for TypeScript errors
   - Verify Node.js version compatibility

2. **Runtime Errors**
   - Check environment variables
   - Verify database connection
   - Check Supabase connection

3. **Health Check Failures**
   - Verify the health check endpoints are responding
   - Check for database connectivity issues

### Verification Scripts

Use the provided verification scripts to check deployment status:

```bash
# Check backend health
./backend/scripts/health-check.sh https://your-backend-url.railway.app/health

# Verify full deployment
./scripts/deployment-verify.sh https://your-backend-url.railway.app https://your-frontend-url.railway.app
```

## Monitoring

- Railway provides built-in logs and metrics
- Health check endpoints provide detailed system status
- Access frontend health dashboard at `/?health=true`

## Rollback Procedure

If a deployment fails:

1. Identify the issue from logs
2. Fix the issue locally
3. Push changes to GitHub
4. Redeploy or rollback to a previous version in Railway dashboard

## Maintenance Mode

To enable maintenance mode:

1. Set `MAINTENANCE_MODE=true` in backend environment variables
2. Deploy the change

This will serve a maintenance page instead of the application.

## Security Considerations

- Ensure all sensitive environment variables are properly set
- Verify CORS settings for production
- Ensure proper rate limiting is configured
- Review Stripe webhook security settings