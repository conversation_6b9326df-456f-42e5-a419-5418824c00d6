# Railway Deployment Guide for RentaHub

## Introduction
This guide will help you set up and deploy RentaHub on Railway, a modern deployment platform that takes care of infrastructure management.

## Prerequisites
- Railway account (https://railway.app)
- Railway CLI installed: `npm i -g @railway/cli`
- Logged in to Railway: `railway login`
- GitHub repository with RentaHub code

## Setting Up Railway Project

### 1. Create a New Project
1. Go to Railway dashboard
2. Click "New Project"
3. Choose "Deploy from GitHub repo"
4. Select your RentaHub repository

### 2. Setup Environment Variables
Railway requires specific environment variables for each service. Here's what you need to set up:
#### Backend Service Environment Variables
| Variable Name | Description | Example Value |
|---------------|-------------|---------------|
| NODE_ENV | Environment | production |
| PORT | Server port | 8080 |
| DATABASE_URL | PostgreSQL connection string | postgresql://postgres:<EMAIL>:5432/railway |
| JWT_SECRET | Secret for JWT token generation | your_jwt_secret_key_here |
| STRIPE_SECRET_KEY | Stripe API key | sk_test_51Hxxxxx |
| STRIPE_WEBHOOK_SECRET | Stripe webhook signing secret | whsec_xxxxx |
| GOOGLE_CLIENT_ID | Google OAuth client ID | your-client-id.apps.googleusercontent.com |
| GOOGLE_CLIENT_SECRET | Google OAuth client secret | your-client-secret |
| EMAIL_SERVICE_API_KEY | API key for email service | your-email-api-key |
| FRONTEND_URL | URL of your frontend service | https://rentahub-frontend-production.up.railway.app |
| ADMIN_URL | URL of your admin service | https://rentahub-admin-production.up.railway.app |

#### Frontend Service Environment Variables
| Variable Name | Description | Example Value |
|---------------|-------------|---------------|
| VITE_API_URL | Backend API URL | https://rentahub-backend-production.up.railway.app |
| VITE_SUPABASE_URL | Supabase URL | https://your-project.supabase.co |
| VITE_SUPABASE_ANON_KEY | Supabase anonymous key | eyJhbGcxxxxxx |

#### Admin Service Environment Variables
| Variable Name | Description | Example Value |
|---------------|-------------|---------------|
| VITE_API_URL | Backend API URL | https://rentahub-backend-production.up.railway.app |
| VITE_SUPABASE_URL | Supabase URL | https://your-project.supabase.co |
| VITE_SUPABASE_ANON_KEY | Supabase anonymous key | eyJhbGcxxxxxx |

### 3. Set Up Services

#### Backend Service
1. In Railway dashboard, select your project
2. Click "New Service" → "GitHub Repo"
3. Select your repository
4. Configure:
   - Root Directory: `/backend`
   - Build Command: `npm run build`
   - Start Command: `npm run start:prod`
5. Set up required environment variables from the table above
6. Deploy

#### Frontend Service
1. In Railway dashboard, select your project
2. Click "New Service" → "GitHub Repo"
3. Select your repository
4. Configure:
   - Root Directory: `/frontend`
   - Build Command: `npm run build`
   - Start Command: `npx serve -s dist`
5. Set up required environment variables from the table above
6. Deploy

#### Admin Service
1. In Railway dashboard, select your project
2. Click "New Service" → "GitHub Repo"
3. Select your repository
4. Configure:
   - Root Directory: `/admin`
   - Build Command: `npm run build`
   - Start Command: `npx serve -s dist`
5. Set up required environment variables from the table above
6. Deploy

## Troubleshooting Deployment

### Backend Health Check Failures
Railway uses the `/health` endpoint to verify that your service is running correctly. If you're experiencing health check failures:

1. Check logs with `railway logs`
2. Verify that all required environment variables are set
3. Ensure your database connection string is correct
4. Try deploying manually with `railway up`

### CORS Issues
If you're experiencing CORS issues:

1. Make sure your backend's CORS configuration includes the correct frontend and admin URLs
2. In backend/src/middleware/corsMiddleware.ts, ensure that your frontend and admin URLs are in the allowedOrigins array

### Database Issues
If you're having database connection problems:

1. Check that your DATABASE_URL is correctly set
2. Ensure your database is running and accessible
3. Check if you need to run migrations: `railway run npx prisma migrate deploy`

## Deployment Script
You can use the deployment script to automate the deployment process:

```bash
export DEPLOY_PLATFORM=railway
bash __scripts/deploy.sh
```

## Post-Deployment Steps

1. Verify each service is running by visiting:
   - Backend: https://your-backend-url.up.railway.app/health
   - Frontend: https://your-frontend-url.up.railway.app
   - Admin: https://your-admin-url.up.railway.app

2. Test user authentication and core functionality

3. Set up custom domains (optional):
   - In Railway dashboard, go to your service
   - Click on "Settings" → "Domains"
   - Add your custom domain and configure DNS settings
