#!/bin/bash

echo "🧹 Cleaning up RentaHub Frontend..."

# Kill any processes on ports 3000, 4173, 5173
echo "Killing processes on ports 3000, 4173, 5173..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || echo "No process on port 3000"
lsof -ti:4173 | xargs kill -9 2>/dev/null || echo "No process on port 4173" 
lsof -ti:5173 | xargs kill -9 2>/dev/null || echo "No process on port 5173"

# Clear Vite cache
echo "Clearing Vite cache..."
rm -rf node_modules/.vite
rm -rf dist

# Clear npm cache
echo "Clearing npm cache..."
npm cache clean --force

# Reinstall dependencies
echo "Reinstalling dependencies..."
npm install

echo "✅ Cleanup complete!"
echo ""
echo "Now run: npm run dev"
