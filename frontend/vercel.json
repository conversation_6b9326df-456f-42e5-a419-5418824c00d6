{"version": 2, "name": "rentahub-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_API_URL": "https://api.rentahub.info/api", "VITE_SUPABASE_URL": "https://rocxjzukyqelvuyltrfq.supabase.co", "VITE_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.Ej5zQJOaKzHdJhgJgKQGOJQGOJQGOJQGOJQGOJQGOJQ"}, "buildCommand": "npm install --legacy-peer-deps && npm run build", "outputDirectory": "dist", "installCommand": "npm install --legacy-peer-deps"}