{"version": 2, "name": "rentahub-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_API_URL": "@vite_api_url", "VITE_SUPABASE_URL": "@vite_supabase_url", "VITE_SUPABASE_ANON_KEY": "@vite_supabase_anon_key"}, "buildCommand": "npm install --legacy-peer-deps && npm run build", "outputDirectory": "dist", "installCommand": "npm install --legacy-peer-deps"}