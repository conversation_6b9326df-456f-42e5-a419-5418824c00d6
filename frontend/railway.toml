# =============================================================================
# RENTAHUB FRONTEND - RAILWAY CONFIGURATION
# =============================================================================
# Main Website: rentahub.info

[build]
builder = "DOCKERFILE"

[deploy]
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[project]
name = "rentahub-frontend"

# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================

[env]
NODE_ENV = "production"
VITE_NODE_ENV = "production"

# API Configuration
VITE_API_URL = "https://api.rentahub.info/api"

# Application URLs
VITE_APP_URL = "https://rentahub.info"
VITE_ADMIN_URL = "https://admin.rentahub.info"

# Google OAuth
VITE_GOOGLE_CLIENT_ID = "177790503193-5g624cighvbma0n0nrrg4oqljagdegi5.apps.googleusercontent.com"
VITE_GOOGLE_CALLBACK_URL = "https://rentahub.info/auth/google/callback"

# Stripe Configuration (Production)
VITE_STRIPE_PUBLISHABLE_KEY = "pk_live_51NcZsZQhLCaDNRIAfEOSvAaRVNTA8t9oLH7QsENMyJeYzLYHbokBfBuDRC2WVMoKRrwy0SG5qcgguBw5LdXzuPJl00jJtCKX9c"

# Supabase Configuration - SECURE: Use Railway environment variables
VITE_SUPABASE_URL = "$VITE_SUPABASE_URL"
VITE_SUPABASE_ANON_KEY = "$VITE_SUPABASE_ANON_KEY"

# Application Configuration
VITE_APP_NAME = "RentaHub"
VITE_APP_DESCRIPTION = "Premium Vehicle Rental Platform"
VITE_APP_VERSION = "1.0.0"

# Contact Information
VITE_SUPPORT_EMAIL = "<EMAIL>"
VITE_CONTACT_EMAIL = "<EMAIL>"

# Feature Flags
VITE_ENABLE_GOOGLE_AUTH = "true"
VITE_ENABLE_STRIPE_PAYMENTS = "true"
VITE_ENABLE_EMAIL_NOTIFICATIONS = "true"

# SEO Configuration
VITE_SITE_NAME = "RentaHub"
VITE_SITE_DESCRIPTION = "Premium vehicle rental platform connecting customers with trusted providers"
VITE_SITE_KEYWORDS = "vehicle rental, car rental, bike rental, scooter rental, Indonesia"
VITE_SITE_URL = "https://rentahub.info"