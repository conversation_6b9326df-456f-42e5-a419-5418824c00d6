{"permissions": {"allow": ["mcp__ide__getDiagnostics", "Bash(find:*)", "Bash(npm audit:*)", "Bash(npm outdated)", "Bash(npm outdated:*)", "Bash(ls:*)", "Bash(npm ls:*)", "Bash(git -C /Users/<USER>/Desktop/RENTAHUB status --porcelain)", "Bash(git -C /Users/<USER>/Desktop/RENTAHUB ls-files)", "Bash(git rm:*)", "<PERSON><PERSON>(true)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(git config:*)", "Bash(git add:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(psql:*)"], "deny": []}}