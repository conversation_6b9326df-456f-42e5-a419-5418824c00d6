import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import theme from './utils/theme';

// Components
import Layout from './components/Layout';
import Footer from './components/Footer';
import VehicleList from './components/VehicleList';
import ErrorBoundary from './components/ErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';

// Pages
import HomePage from './pages/HomePage';
import VehicleDetailPage from './pages/VehicleDetailPage';
import ListVehiclePage from './pages/ListVehiclePage';
import AboutUsPage from './pages/AboutUsPage';
import TermsPage from './pages/TermsPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import ContactPage from './pages/ContactPage';
import HelpCenterPage from './pages/HelpCenterPage';
import NotFoundPage from './pages/NotFoundPage';
import SignInPage from './pages/SignInPage';
import SignUpPage from './pages/SignUpPage';
import MyBookingsPage from './pages/MyBookingsPage';
import UserDashboard from './pages/UserDashboard';
import BookingPage from './pages/BookingPage';
import CheckoutPage from './pages/CheckoutPage';
import { ProviderDashboard } from './pages/ProviderDashboard';
import ProfilePage from './pages/ProfilePage';
import AgreementSigningPage from './pages/AgreementSigningPage';
import AgreementConfirmedSuccessPage from './pages/AgreementSignedSuccessPage';
import AgreementDeclinedPage from './pages/AgreementDeclinedPage';
import GoogleCallbackPage from './pages/GoogleCallbackPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';

// Context Providers
import { CountryProvider } from './components/CountrySelector';
import { CurrencyProvider } from './components/CurrencySelector';
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';

const App: React.FC = () => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('App Error Boundary caught an error:', error, errorInfo);
    
    // Log to external service in production
    if (import.meta.env.PROD) {
      // TODO: Add Sentry or other error tracking service
      console.error('Production error:', error);
    }
  };

  return (
    <ErrorBoundary onError={handleError}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <AuthProvider>
          <ToastProvider>
            <CountryProvider>
              <CurrencyProvider>
              <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
                <Layout>
                  <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/search" element={<Navigate to="/vehicles" replace />} />
                      <Route path="/vehicles" element={<VehicleList />} />
                      <Route path="/vehicle/:id" element={<VehicleDetailPage />} />
                      <Route path="/vehicle-detail/:id" element={<VehicleDetailPage />} />
                      <Route path="/vehicle-listing" element={<ListVehiclePage />} />
                      <Route path="/list-vehicle" element={<ListVehiclePage />} />
                      <Route path="/list-vehicles" element={<ListVehiclePage />} />
                      <Route path="/about" element={<AboutUsPage />} />
                      <Route path="/terms" element={<TermsPage />} />
                      <Route path="/privacy" element={<PrivacyPolicyPage />} />
                      <Route path="/contact" element={<ContactPage />} />
                      <Route path="/help" element={<HelpCenterPage />} />
                      <Route path="/support" element={<HelpCenterPage />} />
                      <Route path="/signin" element={<SignInPage />} />
                      <Route path="/login" element={<SignInPage />} />
                      <Route path="/signup" element={<SignUpPage />} />
                      <Route path="/register" element={<SignUpPage />} />
                      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
                      <Route path="/reset-password" element={<ResetPasswordPage />} />
                      <Route path="/bookings" element={<ProtectedRoute><MyBookingsPage /></ProtectedRoute>} />
                      <Route path="/my-bookings" element={<ProtectedRoute><MyBookingsPage /></ProtectedRoute>} />
                      <Route path="/user/bookings" element={<ProtectedRoute><MyBookingsPage /></ProtectedRoute>} />
                      <Route path="/user/dashboard" element={<ProtectedRoute><UserDashboard /></ProtectedRoute>} />
                      <Route path="/user-dashboard" element={<ProtectedRoute><UserDashboard /></ProtectedRoute>} />
                      <Route path="/dashboard" element={<ProtectedRoute><UserDashboard /></ProtectedRoute>} />
                      <Route path="/booking/:id" element={<ProtectedRoute><BookingPage /></ProtectedRoute>} />
                      <Route path="/booking/create/:vehicleId" element={<ProtectedRoute><BookingPage /></ProtectedRoute>} />
                      <Route path="/checkout" element={<ProtectedRoute><CheckoutPage /></ProtectedRoute>} />
                      <Route path="/provider-dashboard" element={<ProtectedRoute><ProviderDashboard /></ProtectedRoute>} />
                      <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
                      <Route path="/agreements/:agreementId/view" element={<AgreementSigningPage />} />
                      <Route path="/agreement-confirmed-success" element={<AgreementConfirmedSuccessPage />} />
                      <Route path="/agreement-declined" element={<AgreementDeclinedPage />} />
                      <Route path="/auth/google/callback" element={<GoogleCallbackPage />} />
                      
                      {/* Additional routes for footer links */}
                      <Route path="/careers" element={<AboutUsPage />} />
                      <Route path="/blog" element={<AboutUsPage />} />
                      <Route path="/safety" element={<HelpCenterPage />} />
                      <Route path="/feedback" element={<ContactPage />} />
                      <Route path="/faq" element={<HelpCenterPage />} />
                      <Route path="/cookies" element={<PrivacyPolicyPage />} />
                      <Route path="/sitemap" element={<AboutUsPage />} />
                      
                      <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Layout>
              </Router>
              </CurrencyProvider>
            </CountryProvider>
          </ToastProvider>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
