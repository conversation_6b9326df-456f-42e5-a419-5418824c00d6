// Environment configuration for frontend - SECURE VERSION
export const environment = {
  // API Configuration - Only expose what's needed for client
  apiUrl: import.meta.env.VITE_API_URL || (import.meta.env.DEV ? 'http://localhost:3001' : '/api'),

  // Supabase Configuration - Only public keys
  supabaseUrl: import.meta.env.VITE_SUPABASE_URL || '',
  supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',

  // Stripe Configuration - Only publishable key (safe for client)
  stripePublishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',

  // Google Configuration - Only client ID (safe for client)
  googleClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',

  // Environment flags
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // Feature flags - Safe to expose
  features: {
    enableAuth: true,
    enablePayments: true,
    enableMaps: true,
    enableNotifications: true
  }
};

// Validate required environment variables - SECURE VERSION
export const validateEnvironment = () => {
  const required = [
    'VITE_API_URL',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ];

  const missing = required.filter(key => !import.meta.env[key]);

  if (missing.length > 0 && import.meta.env.DEV) {
    // Only log in development mode
    console.warn('⚠️ Missing environment variables for development');
  }

  // Never expose actual environment variable names or values in production
  return missing.length === 0;
};

// Security helper to prevent sensitive data exposure
export const sanitizeForClient = (data: any) => {
  if (import.meta.env.PROD) {
    // In production, remove any potentially sensitive fields
    const sanitized = { ...data };
    delete sanitized.password;
    delete sanitized.secret;
    delete sanitized.key;
    delete sanitized.token;
    delete sanitized.apiKey;
    return sanitized;
  }
  return data;
};