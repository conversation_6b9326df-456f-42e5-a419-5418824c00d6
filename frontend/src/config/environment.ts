// Environment configuration for frontend
export const environment = {
  // API Configuration
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  
  // Supabase Configuration
  supabaseUrl: import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co',
  supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || 'your_anon_key',
  
  // Stripe Configuration
  stripePublishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_your_key',
  
  // Google Configuration
  googleClientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'your_google_maps_key',
  
  // Environment
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  
  // Feature flags
  features: {
    enableAuth: true,
    enablePayments: true,
    enableMaps: true,
    enableNotifications: true
  }
};

// Validate required environment variables
export const validateEnvironment = () => {
  const required = [
    'VITE_API_URL',
    'VITE_SUPABASE_URL', 
    'VITE_SUPABASE_ANON_KEY'
  ];
  
  const missing = required.filter(key => !import.meta.env[key]);
  
  if (missing.length > 0) {
    console.warn('⚠️ Missing environment variables:', missing);
    console.warn('Using fallback values for development');
  }
  
  return missing.length === 0;
}; 