// Security configuration and utilities for frontend
import { environment } from './environment';

/**
 * Security configuration class to handle sensitive data properly
 */
export class SecurityConfig {
  private static instance: SecurityConfig;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): SecurityConfig {
    if (!SecurityConfig.instance) {
      SecurityConfig.instance = new SecurityConfig();
    }
    return SecurityConfig.instance;
  }

  /**
   * Initialize security configuration
   */
  public initialize(): void {
    if (this.isInitialized) return;

    // Validate environment in development only
    if (environment.isDevelopment) {
      this.validateEnvironment();
    }

    // Set up security headers for API requests
    this.setupSecurityHeaders();

    this.isInitialized = true;
  }

  /**
   * Validate required environment variables
   */
  private validateEnvironment(): void {
    const required = [
      'VITE_API_URL',
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ];

    const missing = required.filter(key => !import.meta.env[key]);
    
    if (missing.length > 0) {
      console.warn('⚠️ Missing required environment variables:', missing);
      console.warn('Please check your .env file');
    }
  }

  /**
   * Set up default security headers for API requests
   */
  private setupSecurityHeaders(): void {
    // This will be used by axios interceptors
    window.__SECURITY_HEADERS__ = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
  }

  /**
   * Sanitize data before sending to client
   */
  public sanitizeClientData(data: any): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = { ...data };
    
    // Remove sensitive fields
    const sensitiveFields = [
      'password', 'secret', 'key', 'token', 'apiKey', 
      'privateKey', 'secretKey', 'accessToken', 'refreshToken',
      'sessionId', 'csrf', 'salt', 'hash'
    ];

    const removeSensitiveFields = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(removeSensitiveFields);
      }
      
      if (obj && typeof obj === 'object') {
        const cleaned = { ...obj };
        
        sensitiveFields.forEach(field => {
          delete cleaned[field];
        });
        
        // Recursively clean nested objects
        Object.keys(cleaned).forEach(key => {
          if (cleaned[key] && typeof cleaned[key] === 'object') {
            cleaned[key] = removeSensitiveFields(cleaned[key]);
          }
        });
        
        return cleaned;
      }
      
      return obj;
    };

    return removeSensitiveFields(sanitized);
  }

  /**
   * Check if current environment is secure
   */
  public isSecureEnvironment(): boolean {
    if (environment.isDevelopment) return false;
    
    // Check if we're running over HTTPS in production
    return window.location.protocol === 'https:';
  }

  /**
   * Get safe API URL (without exposing internal URLs)
   */
  public getSafeApiUrl(): string {
    const apiUrl = environment.apiUrl;
    
    // In production, never expose localhost or internal IPs
    if (environment.isProduction && (
      apiUrl.includes('localhost') || 
      apiUrl.includes('127.0.0.1') ||
      apiUrl.includes('192.168.') ||
      apiUrl.includes('10.0.') ||
      apiUrl.includes('172.')
    )) {
      return '/api'; // Use relative URL in production
    }
    
    return apiUrl;
  }

  /**
   * Log security events (only in development)
   */
  public logSecurityEvent(event: string, details?: any): void {
    if (environment.isDevelopment) {
      console.log(`🔒 Security Event: ${event}`, details);
    }
    
    // In production, you might want to send this to a logging service
    // but never expose sensitive information
  }
}

// Export singleton instance
export const securityConfig = SecurityConfig.getInstance();

// Initialize on module load
securityConfig.initialize();

// Declare global type for security headers
declare global {
  interface Window {
    __SECURITY_HEADERS__?: Record<string, string>;
  }
}
