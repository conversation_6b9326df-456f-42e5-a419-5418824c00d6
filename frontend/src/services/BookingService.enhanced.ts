import * as types from '../types'
import axiosInstance from './axiosInstance'
import * as UserService from './UserService'

/**
 * Create a new booking
 */
export const createBooking = (
  bookingData: types.CreateBookingPayload
): Promise<types.ApiResponse<{ booking: types.Booking; paymentIntent?: any }>> =>
  axiosInstance
    .post('/bookings', bookingData)
    .then((res) => res.data)

/**
 * Complete the checkout process
 */
export const checkout = (data: {
  bookingId: string
  paymentMethod: types.PaymentMethod
  paymentDetails?: any
}): Promise<types.ApiResponse<{ bookingId: string; status: string }>> =>
  axiosInstance
    .post('/bookings/checkout', data)
    .then((res) => res.data)

/**
 * Update booking
 */
export const updateBooking = (
  data: types.UpdateBookingPayload
): Promise<types.ApiResponse<types.Booking>> =>
  axiosInstance
    .put(`/bookings/${data.bookingId}`, data)
    .then((res) => res.data)

/**
 * Get bookings with filters and pagination
 */
export const getBookings = (
  payload: types.GetBookingsPayload
): Promise<types.ApiResponse<types.PaginatedResponse<types.Booking>>> =>
  axiosInstance
    .post(`/bookings/list/${payload.page}/${payload.size}/${UserService.getLanguage()}`, payload)
    .then((res) => res.data)

/**
 * Get booking by ID
 */
export const getBooking = (bookingId: string): Promise<types.ApiResponse<types.Booking>> =>
  axiosInstance
    .get(`/bookings/${bookingId}/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Get user bookings
 */
export const getUserBookings = (
  userId: string,
  page: number = 1,
  size: number = 10
): Promise<types.ApiResponse<types.PaginatedResponse<types.Booking>>> =>
  axiosInstance
    .get(`/bookings/user/${userId}/${page}/${size}`)
    .then((res) => res.data)

/**
 * Get provider bookings
 */
export const getProviderBookings = (
  providerId: string,
  page: number = 1,
  size: number = 10
): Promise<types.ApiResponse<types.PaginatedResponse<types.Booking>>> =>
  axiosInstance
    .get(`/bookings/provider/${providerId}/${page}/${size}`)
    .then((res) => res.data)

/**
 * Cancel booking
 */
export const cancelBooking = (
  bookingId: string,
  reason?: string
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/cancel`, { reason })
    .then((res) => res.data)

/**
 * Confirm pickup
 */
export const confirmPickup = (
  bookingId: string,
  confirmationData: {
    pickupCode?: string
    notes?: string
    mileage?: number
    fuelLevel?: number
    damagePhotos?: string[]
  }
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/confirm-pickup`, confirmationData)
    .then((res) => res.data)

/**
 * Confirm return
 */
export const confirmReturn = (
  bookingId: string,
  returnData: {
    returnCode?: string
    notes?: string
    mileage?: number
    fuelLevel?: number
    damagePhotos?: string[]
    extraCharges?: { description: string; amount: number }[]
  }
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/confirm-return`, returnData)
    .then((res) => res.data)

/**
 * Get booking status history
 */
export const getBookingHistory = (
  bookingId: string
): Promise<types.ApiResponse<{ status: string; timestamp: Date; notes?: string }[]>> =>
  axiosInstance
    .get(`/bookings/${bookingId}/history`)
    .then((res) => res.data)

/**
 * Request booking modification
 */
export const requestModification = (
  bookingId: string,
  modifications: {
    newStartDate?: string
    newEndDate?: string
    newVehicle?: string
    reason: string
  }
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/modify`, modifications)
    .then((res) => res.data)

/**
 * Approve/Reject booking modification (provider only)
 */
export const handleModificationRequest = (
  bookingId: string,
  requestId: string,
  action: 'approve' | 'reject',
  notes?: string
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/modification/${requestId}/${action}`, { notes })
    .then((res) => res.data)

/**
 * Get booking receipt/invoice
 */
export const getBookingReceipt = (
  bookingId: string
): Promise<types.ApiResponse<{ receiptUrl: string }>> =>
  axiosInstance
    .get(`/bookings/${bookingId}/receipt`)
    .then((res) => res.data)

/**
 * Submit booking review
 */
export const submitReview = (
  bookingId: string,
  reviewData: {
    rating: number
    comment: string
    vehicleRating?: number
    providerRating?: number
  }
): Promise<types.ApiResponse<types.Review>> =>
  axiosInstance
    .post(`/bookings/${bookingId}/review`, reviewData)
    .then((res) => res.data)

/**
 * Get booking price calculation
 */
export const calculatePrice = (params: {
  vehicleId: string
  startDate: string
  endDate: string
  addOns?: string[]
  deliveryRequested?: boolean
  promoCode?: string
}): Promise<types.ApiResponse<{
  basePrice: number
  addOnsTotal: number
  deliveryFee: number
  taxes: number
  discount: number
  total: number
  breakdown: any
}>> =>
  axiosInstance
    .post('/bookings/calculate-price', params)
    .then((res) => res.data)

/**
 * Apply promo code
 */
export const applyPromoCode = (
  vehicleId: string,
  startDate: string,
  endDate: string,
  promoCode: string
): Promise<types.ApiResponse<{
  valid: boolean
  discount: number
  message: string
}>> =>
  axiosInstance
    .post('/bookings/apply-promo', { vehicleId, startDate, endDate, promoCode })
    .then((res) => res.data)

/**
 * Get booking notifications
 */
export const getBookingNotifications = (
  bookingId: string
): Promise<types.ApiResponse<types.Notification[]>> =>
  axiosInstance
    .get(`/bookings/${bookingId}/notifications`)
    .then((res) => res.data)

/**
 * Delete temporary booking (cleanup failed checkouts)
 */
export const deleteTempBooking = (
  bookingId: string,
  sessionId?: string
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .delete(`/bookings/temp/${bookingId}${sessionId ? `/${sessionId}` : ''}`)
    .then((res) => res.data)

/**
 * Check vehicle availability for booking dates
 */
export const checkAvailability = (params: {
  vehicleId: string
  startDate: string
  endDate: string
}): Promise<types.ApiResponse<{ available: boolean; message?: string }>> =>
  axiosInstance
    .get('/bookings/availability', { params })
    .then((res) => res.data)

// Legacy exports for backward compatibility
export { createBooking as create }
export { updateBooking as update }
export { cancelBooking as cancel }
export { getBooking as getBookingById }
export { getUserBookings as getBookingsByUser }
export { getProviderBookings as getBookingsByProvider }

export default {
  createBooking,
  checkout,
  updateBooking,
  getBookings,
  getBooking,
  getUserBookings,
  getProviderBookings,
  cancelBooking,
  confirmPickup,
  confirmReturn,
  getBookingHistory,
  requestModification,
  handleModificationRequest,
  getBookingReceipt,
  submitReview,
  calculatePrice,
  applyPromoCode,
  getBookingNotifications,
  deleteTempBooking,
  checkAvailability
}
