import { Booking, Vehicle, User } from '../types';

export interface ExportOptions {
  format: 'csv' | 'excel' | 'json' | 'pdf';
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: {
    status?: string[];
    vehicles?: string[];
    customers?: string[];
    minAmount?: number;
    maxAmount?: number;
  };
  includeDetails?: boolean;
  includeVehicleInfo?: boolean;
  includeCustomerInfo?: boolean;
  includePaymentInfo?: boolean;
}

export interface ImportOptions {
  format: 'csv' | 'excel' | 'json';
  updateExisting?: boolean;
  skipDuplicates?: boolean;
  validateData?: boolean;
}

export interface ExportResult {
  success: boolean;
  data?: Blob;
  filename?: string;
  error?: string;
  recordCount?: number;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
  duplicates: number;
}

export class ExportImportService {
  private static instance: ExportImportService;

  private constructor() {}

  public static getInstance(): ExportImportService {
    if (!ExportImportService.instance) {
      ExportImportService.instance = new ExportImportService();
    }
    return ExportImportService.instance;
  }

  public async exportBookings(
    bookings: Booking[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      let filteredBookings = this.filterBookings(bookings, options.filters);
      
      if (options.dateRange) {
        filteredBookings = filteredBookings.filter(booking => {
          const bookingDate = new Date(booking.startDate);
          const startDate = new Date(options.dateRange!.start);
          const endDate = new Date(options.dateRange!.end);
          return bookingDate >= startDate && bookingDate <= endDate;
        });
      }

      let data: Blob;
      let filename: string;
      const timestamp = new Date().toISOString().split('T')[0];

      switch (options.format) {
        case 'csv':
          const csvData = this.convertToCSV(filteredBookings, options);
          data = new Blob([csvData], { type: 'text/csv' });
          filename = `bookings_${timestamp}.csv`;
          break;

        case 'excel':
          const excelData = await this.convertToExcel(filteredBookings, options);
          data = new Blob([excelData], { 
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
          });
          filename = `bookings_${timestamp}.xlsx`;
          break;

        case 'json':
          const jsonData = this.convertToJSON(filteredBookings, options);
          data = new Blob([jsonData], { type: 'application/json' });
          filename = `bookings_${timestamp}.json`;
          break;

        case 'pdf':
          const pdfData = await this.convertToPDF(filteredBookings, options);
          data = new Blob([pdfData], { type: 'application/pdf' });
          filename = `bookings_${timestamp}.pdf`;
          break;

        default:
          throw new Error('Unsupported export format');
      }

      return {
        success: true,
        data,
        filename,
        recordCount: filteredBookings.length
      };
    } catch (error) {
      console.error('Export error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Export failed'
      };
    }
  }

  public async importBookings(
    file: File,
    options: ImportOptions
  ): Promise<ImportResult> {
    try {
      let bookings: Partial<Booking>[] = [];

      switch (options.format) {
        case 'csv':
          bookings = await this.parseCSV(file);
          break;

        case 'excel':
          bookings = await this.parseExcel(file);
          break;

        case 'json':
          bookings = await this.parseJSON(file);
          break;

        default:
          throw new Error('Unsupported import format');
      }

      if (options.validateData) {
        const validationResult = this.validateBookings(bookings);
        if (!validationResult.isValid) {
          return {
            success: false,
            imported: 0,
            failed: bookings.length,
            errors: validationResult.errors,
            duplicates: 0
          };
        }
      }

      // Simulate import process
      const result = await this.processImport(bookings, options);

      return {
        success: true,
        imported: result.imported,
        failed: result.failed,
        errors: result.errors,
        duplicates: result.duplicates
      };
    } catch (error) {
      console.error('Import error:', error);
      return {
        success: false,
        imported: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : 'Import failed'],
        duplicates: 0
      };
    }
  }

  private filterBookings(bookings: Booking[], filters?: ExportOptions['filters']): Booking[] {
    if (!filters) return bookings;

    return bookings.filter(booking => {
      if (filters.status && filters.status.length > 0) {
        if (!filters.status.includes(booking.status)) return false;
      }

      if (filters.vehicles && filters.vehicles.length > 0) {
        if (!filters.vehicles.includes(booking.vehicleId)) return false;
      }

      if (filters.customers && filters.customers.length > 0) {
        if (!filters.customers.includes(booking.userId)) return false;
      }

      if (filters.minAmount && booking.totalAmount < filters.minAmount) {
        return false;
      }

      if (filters.maxAmount && booking.totalAmount > filters.maxAmount) {
        return false;
      }

      return true;
    });
  }

  private convertToCSV(bookings: Booking[], options: ExportOptions): string {
    const headers = [
      'Booking ID',
      'Vehicle',
      'Customer',
      'Start Date',
      'End Date',
      'Status',
      'Payment Status',
      'Total Amount',
      'Created At'
    ];

    if (options.includeVehicleInfo) {
      headers.push('Vehicle Brand', 'Vehicle Model', 'Daily Rate');
    }

    if (options.includeCustomerInfo) {
      headers.push('Customer Email', 'Customer Phone');
    }

    if (options.includePaymentInfo) {
      headers.push('Payment Method', 'Transaction ID');
    }

    const rows = bookings.map(booking => {
      const row = [
        booking.id,
        booking.vehicle?.name || '',
        booking.user?.name || '',
        booking.startDate,
        booking.endDate,
        booking.status,
        booking.paymentStatus,
        booking.totalAmount.toString(),
        booking.createdAt
      ];

      if (options.includeVehicleInfo) {
        row.push(
          booking.vehicle?.brand || '',
          booking.vehicle?.model || '',
          booking.vehicle?.dailyRate?.toString() || ''
        );
      }

      if (options.includeCustomerInfo) {
        row.push(
          booking.user?.email || '',
          booking.user?.phone || ''
        );
      }

      if (options.includePaymentInfo) {
        row.push(
          booking.paymentMethod || '',
          booking.transactionId || ''
        );
      }

      return row.join(',');
    });

    return [headers.join(','), ...rows].join('\n');
  }

  private async convertToExcel(bookings: Booking[], options: ExportOptions): Promise<ArrayBuffer> {
    // In a real implementation, you would use a library like xlsx
    // For now, we'll return a simple CSV-like format
    const csvData = this.convertToCSV(bookings, options);
    const encoder = new TextEncoder();
    return encoder.encode(csvData).buffer;
  }

  private convertToJSON(bookings: Booking[], options: ExportOptions): string {
    const exportData = bookings.map(booking => {
      const bookingData: any = {
        id: booking.id,
        vehicleId: booking.vehicleId,
        userId: booking.userId,
        startDate: booking.startDate,
        endDate: booking.endDate,
        status: booking.status,
        paymentStatus: booking.paymentStatus,
        totalAmount: booking.totalAmount,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt
      };

      if (options.includeVehicleInfo && booking.vehicle) {
        bookingData.vehicle = booking.vehicle;
      }

      if (options.includeCustomerInfo && booking.user) {
        bookingData.user = booking.user;
      }

      if (options.includePaymentInfo) {
        bookingData.paymentMethod = booking.paymentMethod;
        bookingData.transactionId = booking.transactionId;
      }

      return bookingData;
    });

    return JSON.stringify(exportData, null, 2);
  }

  private async convertToPDF(bookings: Booking[], options: ExportOptions): Promise<ArrayBuffer> {
    // In a real implementation, you would use a library like jsPDF
    // For now, we'll return a simple text format
    const textData = bookings.map(booking => 
      `Booking ID: ${booking.id}\nVehicle: ${booking.vehicle?.name}\nCustomer: ${booking.user?.name}\nDates: ${booking.startDate} - ${booking.endDate}\nStatus: ${booking.status}\nAmount: ${booking.totalAmount}\n\n`
    ).join('---\n');

    const encoder = new TextEncoder();
    return encoder.encode(textData).buffer;
  }

  private async parseCSV(file: File): Promise<Partial<Booking>[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string;
          const lines = text.split('\n');
          const headers = lines[0].split(',');
          const bookings: Partial<Booking>[] = [];

          for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
              const values = lines[i].split(',');
              const booking: Partial<Booking> = {};

              headers.forEach((header, index) => {
                const value = values[index];
                switch (header.trim()) {
                  case 'Booking ID':
                    booking.id = value;
                    break;
                  case 'Vehicle':
                    booking.vehicleId = value;
                    break;
                  case 'Customer':
                    booking.userId = value;
                    break;
                  case 'Start Date':
                    booking.startDate = value;
                    break;
                  case 'End Date':
                    booking.endDate = value;
                    break;
                  case 'Status':
                    booking.status = value as any;
                    break;
                  case 'Payment Status':
                    booking.paymentStatus = value as any;
                    break;
                  case 'Total Amount':
                    booking.totalAmount = parseInt(value) || 0;
                    break;
                }
              });

              bookings.push(booking);
            }
          }

          resolve(bookings);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  private async parseExcel(file: File): Promise<Partial<Booking>[]> {
    // In a real implementation, you would use a library like xlsx
    // For now, we'll treat it as CSV
    return this.parseCSV(file);
  }

  private async parseJSON(file: File): Promise<Partial<Booking>[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string;
          const bookings = JSON.parse(text);
          resolve(bookings);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  private validateBookings(bookings: Partial<Booking>[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    bookings.forEach((booking, index) => {
      if (!booking.id) {
        errors.push(`Row ${index + 1}: Missing booking ID`);
      }

      if (!booking.vehicleId) {
        errors.push(`Row ${index + 1}: Missing vehicle ID`);
      }

      if (!booking.userId) {
        errors.push(`Row ${index + 1}: Missing user ID`);
      }

      if (!booking.startDate) {
        errors.push(`Row ${index + 1}: Missing start date`);
      }

      if (!booking.endDate) {
        errors.push(`Row ${index + 1}: Missing end date`);
      }

      if (booking.startDate && booking.endDate) {
        const startDate = new Date(booking.startDate);
        const endDate = new Date(booking.endDate);
        if (startDate >= endDate) {
          errors.push(`Row ${index + 1}: End date must be after start date`);
        }
      }

      if (booking.totalAmount && booking.totalAmount <= 0) {
        errors.push(`Row ${index + 1}: Total amount must be positive`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private async processImport(
    bookings: Partial<Booking>[],
    options: ImportOptions
  ): Promise<{ imported: number; failed: number; errors: string[]; duplicates: number }> {
    // Simulate import process
    let imported = 0;
    let failed = 0;
    let duplicates = 0;
    const errors: string[] = [];

    for (const booking of bookings) {
      try {
        // Simulate API call to create booking
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (Math.random() > 0.1) { // 90% success rate
          imported++;
        } else {
          failed++;
          errors.push(`Failed to import booking ${booking.id}`);
        }
      } catch (error) {
        failed++;
        errors.push(`Error importing booking ${booking.id}: ${error}`);
      }
    }

    return { imported, failed, errors, duplicates };
  }

  public generateExportTemplate(format: 'csv' | 'excel' | 'json'): Blob {
    const templateData = [
      {
        id: 'TEMPLATE_ID',
        vehicleId: 'VEHICLE_ID',
        userId: 'USER_ID',
        startDate: '2024-01-01',
        endDate: '2024-01-03',
        status: 'pending',
        paymentStatus: 'pending',
        totalAmount: 150000
      }
    ];

    let data: Blob;
    const timestamp = new Date().toISOString().split('T')[0];

    switch (format) {
      case 'csv':
        const csvHeaders = 'Booking ID,Vehicle ID,User ID,Start Date,End Date,Status,Payment Status,Total Amount';
        const csvRow = 'TEMPLATE_ID,VEHICLE_ID,USER_ID,2024-01-01,2024-01-03,pending,pending,150000';
        const csvData = `${csvHeaders}\n${csvRow}`;
        data = new Blob([csvData], { type: 'text/csv' });
        break;

      case 'excel':
        const excelData = csvData;
        data = new Blob([excelData], { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
        break;

      case 'json':
        const jsonData = JSON.stringify(templateData, null, 2);
        data = new Blob([jsonData], { type: 'application/json' });
        break;

      default:
        throw new Error('Unsupported format');
    }

    return data;
  }

  public downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

export default ExportImportService.getInstance(); 