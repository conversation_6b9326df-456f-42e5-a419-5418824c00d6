import * as types from '../types'
import axiosInstance from './axiosInstance'

class ApiService {
  // Cache for improved performance
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  constructor() {
    // No need to setup interceptors here - they're already configured in axiosInstance
    console.log('ApiService initialized')
  }

  // Enhanced caching utility
  private setCacheItem(key: string, data: any, ttl: number = 300000) { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  private getCacheItem(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }

  // Health check method
  async healthCheck(): Promise<{ status: string; message?: string }> {
    try {
      const response = await axiosInstance.get('/health')
      return { 
        status: 'healthy', 
        message: response.data?.message || 'Backend server is running' 
      }
    } catch (error: any) {
      console.error('Health check failed:', error)
      return { 
        status: 'unhealthy', 
        message: error.response?.data?.message || 'Backend server not available' 
      }
    }
  }

  // User-related methods
  async registerUser(userData: Partial<types.User>): Promise<types.ApiResponse<{ user: types.User; token: string }>> {
    try {
      const response = await axiosInstance.post('/users/register', userData)
      return response.data
    } catch (error: any) {
      console.error('User registration failed:', error)
      throw error
    }
  }

  async loginUser(credentials: { email: string; password: string }): Promise<types.ApiResponse<{ user: types.User; token: string }>> {
    try {
      const response = await axiosInstance.post('/users/login', credentials)
      if (response.data.success && response.data.data?.token) {
        localStorage.setItem('auth-token', response.data.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.data.user))
      }
      return response.data
    } catch (error: any) {
      console.error('User login failed:', error)
      throw error
    }
  }

  // Vehicle-related methods
  async searchVehicles(searchParams: types.SearchFilters): Promise<types.ApiResponse<types.Vehicle[]>> {
    const cacheKey = `vehicles-search-${JSON.stringify(searchParams)}`
    const cached = this.getCacheItem(cacheKey)
    
    if (cached) {
      return cached
    }

    try {
      const response = await axiosInstance.get('/vehicles/search', { params: searchParams })
      this.setCacheItem(cacheKey, response.data, 60000) // Cache for 1 minute
      return response.data
    } catch (error: any) {
      console.error('Vehicle search failed:', error)
      throw error
    }
  }

  async getVehicleById(vehicleId: string): Promise<types.ApiResponse<types.Vehicle>> {
    const response = await axiosInstance.get(`/vehicles/${vehicleId}`)
    return response.data
  }

  // Booking-related methods
  async createBooking(bookingData: types.CreateBookingPayload): Promise<types.ApiResponse<types.Booking>> {
    try {
      const response = await axiosInstance.post('/bookings', bookingData)
      return response.data
    } catch (error: any) {
      // Handle specific booking errors
      if (error.response?.status === 409) {
        return {
          success: false,
          message: 'Vehicle not available for selected dates',
          error: 'VEHICLE_UNAVAILABLE'
        }
      }
      throw error
    }
  }

  // Enhanced availability check with caching
  async checkVehicleAvailability(params: { 
    vehicleId: string
    startDate: string
    endDate: string 
  }): Promise<types.ApiResponse<{ available: boolean; message?: string }>> {
    const cacheKey = `availability-${params.vehicleId}-${params.startDate}-${params.endDate}`
    const cached = this.getCacheItem(cacheKey)
    
    // Return cached result if still valid
    if (cached) {
      return cached
    }

    try {
      const response = await axiosInstance.get('/bookings/availability', { params })
      const result = response.data
      
      // Cache the result for 30 seconds
      this.setCacheItem(cacheKey, result, 30000)
      
      return result
    } catch (error: any) {
      // Fallback: assume unavailable if we can't check
      return {
        success: true,
        data: { available: false, message: 'Unable to verify availability' }
      }
    }
  }

  async getUserBookings(userId: string): Promise<types.ApiResponse<types.Booking[]>> {
    const response = await axiosInstance.get(`/bookings/user/${userId}`)
    return response.data
  }

  // Location-related methods
  async getNearbyVehicles(params: {
    latitude: number
    longitude: number
    radius?: number
    category?: string
  }): Promise<types.ApiResponse<types.Vehicle[]>> {
    const response = await axiosInstance.get('/vehicles/nearby', { params })
    return response.data
  }

  // Payment-related methods
  async createPaymentIntent(bookingId: string): Promise<types.ApiResponse<{ clientSecret: string; amount: number }>> {
    const response = await axiosInstance.post(`/payments/create-intent`, { bookingId })
    return response.data
  }

  async confirmPayment(paymentIntentId: string): Promise<types.ApiResponse<{ status: string }>> {
    const response = await axiosInstance.post(`/payments/confirm`, { paymentIntentId })
    return response.data
  }

  // User profile methods
  async updateUserProfile(userId: string, updateData: Partial<types.User>): Promise<types.ApiResponse<types.User>> {
    const response = await axiosInstance.put(`/users/${userId}`, updateData)
    return response.data
  }

  async getUserProfile(userId: string): Promise<types.ApiResponse<types.User>> {
    const response = await axiosInstance.get(`/users/${userId}`)
    return response.data
  }

  // Vehicle reviews and ratings
  async getVehicleReviews(vehicleId: string): Promise<types.ApiResponse<types.Review[]>> {
    const response = await axiosInstance.get(`/vehicles/${vehicleId}/reviews`)
    return response.data
  }

  async createReview(reviewData: {
    vehicleId: string
    userId: string
    rating: number
    comment: string
  }): Promise<types.ApiResponse<types.Review>> {
    const response = await axiosInstance.post('/reviews', reviewData)
    return response.data
  }

  // Booking management
  async cancelBooking(bookingId: string, reason?: string): Promise<types.ApiResponse<{ success: boolean }>> {
    const response = await axiosInstance.post(`/bookings/${bookingId}/cancel`, { reason })
    return response.data
  }

  async extendBooking(bookingId: string, newEndDate: string): Promise<types.ApiResponse<types.Booking>> {
    const response = await axiosInstance.post(`/bookings/${bookingId}/extend`, { newEndDate })
    return response.data
  }

  // Real-time features
  async getVehicleLocation(vehicleId: string): Promise<types.ApiResponse<{ latitude: number; longitude: number; lastUpdated: string }>> {
    const response = await axiosInstance.get(`/vehicles/${vehicleId}/location`)
    return response.data
  }

  // Clear auth data on logout
  logout(): void {
    localStorage.removeItem('auth-token')
    localStorage.removeItem('user')
    // Clear cache on logout
    this.cache.clear()
  }

  // Network and connection utilities
  async isBackendHealthy(): Promise<boolean> {
    const health = await this.healthCheck()
    return health.status === 'healthy'
  }

  // Clear cache manually
  clearCache(): void {
    this.cache.clear()
  }

  // Get cache statistics
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  // Retry failed requests
  async retryRequest<T>(
    requestFn: () => Promise<T>, 
    maxRetries: number = 3, 
    delay: number = 1000
  ): Promise<T> {
    let lastError: any
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }
    
    throw lastError
  }
}

export default new ApiService()
