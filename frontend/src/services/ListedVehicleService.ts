export interface ListedVehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  category: string;
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  images: string[];
  description: string;
  location: any;
  active: boolean;
  status: string;
  rating?: number;
  reviewCount?: number;
  features: string[];
  provider: {
    id: string;
    businessName: string;
    city: string;
    rating: number;
    responseRate: number;
    totalVehicles: number;
  };
}

export interface ListedVehicleResponse {
  success: boolean;
  data: ListedVehicle[];
  count: number;
  hasListings: boolean;
  message?: string;
  error?: string;
}

export interface ListingCheckResponse {
  success: boolean;
  hasListings: boolean;
  message: string;
}

export class ListedVehicleService {
  private static baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';

  /**
   * Check if there are any active listings
   */
  static async checkActiveListings(): Promise<ListingCheckResponse> {
    try {
      console.log('🔍 Checking for active listings...');
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles/check`);
      const result = await response.json();
      
      console.log('📊 Listing check result:', result);
      return result;
    } catch (error) {
      console.error('❌ Error checking active listings:', error);
      return {
        success: false,
        hasListings: false,
        message: 'Failed to check listings'
      };
    }
  }

  /**
   * Get featured vehicles for home page
   */
  static async getFeaturedVehicles(limit: number = 6): Promise<ListedVehicleResponse> {
    try {
      console.log('🔍 Fetching featured vehicles...');
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles/featured?limit=${limit}`);
      const result = await response.json();
      
      console.log('🏆 Featured vehicles result:', result);
      return result;
    } catch (error) {
      console.error('❌ Error fetching featured vehicles:', error);
      return {
        success: false,
        data: [],
        count: 0,
        hasListings: false,
        error: 'Failed to fetch featured vehicles'
      };
    }
  }

  /**
   * Get active vehicles for search/browse
   */
  static async getActiveVehicles(limit: number = 20): Promise<ListedVehicleResponse> {
    try {
      console.log('🔍 Fetching active vehicles...');
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles?limit=${limit}`);
      const result = await response.json();
      
      console.log('🚗 Active vehicles result:', result);
      return result;
    } catch (error) {
      console.error('❌ Error fetching active vehicles:', error);
      return {
        success: false,
        data: [],
        count: 0,
        hasListings: false,
        error: 'Failed to fetch active vehicles'
      };
    }
  }

  /**
   * Search listed vehicles
   */
  static async searchVehicles(
    query?: string,
    filters?: {
      category?: string;
      minPrice?: number;
      maxPrice?: number;
      city?: string;
      make?: string;
      transmission?: string;
      fuelType?: string;
    },
    limit: number = 20
  ): Promise<ListedVehicleResponse> {
    try {
      console.log('🔍 Searching vehicles:', { query, filters });
      
      const params = new URLSearchParams();
      
      if (query) params.append('q', query);
      if (filters?.category) params.append('category', filters.category);
      if (filters?.minPrice) params.append('minPrice', filters.minPrice.toString());
      if (filters?.maxPrice) params.append('maxPrice', filters.maxPrice.toString());
      if (filters?.city) params.append('city', filters.city);
      if (filters?.make) params.append('make', filters.make);
      if (filters?.transmission) params.append('transmission', filters.transmission);
      if (filters?.fuelType) params.append('fuelType', filters.fuelType);
      params.append('limit', limit.toString());
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles/search?${params}`);
      const result = await response.json();
      
      console.log('🔍 Search results:', result);
      return result;
    } catch (error) {
      console.error('❌ Error searching vehicles:', error);
      return {
        success: false,
        data: [],
        count: 0,
        hasListings: false,
        error: 'Failed to search vehicles'
      };
    }
  }

  /**
   * Get vehicle by ID
   */
  static async getVehicleById(id: string): Promise<{ success: boolean; data?: ListedVehicle; error?: string }> {
    try {
      console.log('🔍 Fetching vehicle by ID:', id);
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles/${id}`);
      const result = await response.json();
      
      console.log('🚗 Vehicle details:', result);
      return result;
    } catch (error) {
      console.error('❌ Error fetching vehicle by ID:', error);
      return {
        success: false,
        error: 'Failed to fetch vehicle details'
      };
    }
  }

  /**
   * Get listing statistics
   */
  static async getListingStats(): Promise<{
    success: boolean;
    data?: {
      totalListings: number;
      activeListings: number;
      pendingApproval: number;
      totalProviders: number;
      averageDailyRate: number;
      topCategories: Array<{ category: string; count: number }>;
    };
    error?: string;
  }> {
    try {
      console.log('📊 Fetching listing statistics...');
      
      const response = await fetch(`${this.baseUrl}/api/listed-vehicles/stats`);
      const result = await response.json();
      
      console.log('📊 Listing stats:', result);
      return result;
    } catch (error) {
      console.error('❌ Error fetching listing stats:', error);
      return {
        success: false,
        error: 'Failed to fetch listing statistics'
      };
    }
  }
}

export default ListedVehicleService;
