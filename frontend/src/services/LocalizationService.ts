import i18n from 'i18next'
import { format } from 'date-fns'
import { enUS, id } from 'date-fns/locale'

class LocalizationService {
  // Currency formatting
  formatCurrency(
    amount: number, 
    currency: string = 'USD', 
    locale: string = 'en-US'
  ): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  // Date formatting with localization
  formatDate(
    date: Date | number, 
    formatString: string = 'PP', 
    locale?: string
  ): string {
    const currentLocale = locale || i18n.language

    const localeMap = {
      'en': enUS,
      'id': id
    }

    return format(date, formatString, { 
      locale: localeMap[currentLocale as keyof typeof localeMap] || enUS 
    })
  }

  // Number formatting
  formatNumber(
    number: number, 
    options?: Intl.NumberFormatOptions, 
    locale?: string
  ): string {
    return new Intl.NumberFormat(
      locale || i18n.language, 
      options
    ).format(number)
  }

  // Pluralization
  pluralize(
    key: string, 
    count: number, 
    namespace: string = 'common'
  ): string {
    return i18n.t(key, { count, ns: namespace })
  }

  // Get current language direction (LTR/RTL)
  getLanguageDirection(): 'ltr' | 'rtl' {
    const rtlLanguages = ['ar', 'he', 'fa']
    return rtlLanguages.includes(i18n.language) ? 'rtl' : 'ltr'
  }

  // Translate with fallback
  translate(
    key: string, 
    options?: any, 
    namespace: string = 'common'
  ): string {
    return i18n.t(key, { 
      ...options, 
      ns: namespace, 
      defaultValue: key 
    }) as string
  }
}

export default new LocalizationService() 