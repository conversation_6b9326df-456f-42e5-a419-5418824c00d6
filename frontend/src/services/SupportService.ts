import axios from 'axios';
import { SupportTicket, SOSAlert } from '../types/support';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

export const fetchSupportTickets = async (): Promise<SupportTicket[]> => {
  const response = await axios.get(`${API_URL}/support/tickets`);
  return response.data;
};

export const submitSupportTicket = async (
  ticketData: Omit<SupportTicket, 'id' | 'createdAt'>
): Promise<SupportTicket> => {
  const response = await axios.post(`${API_URL}/support/tickets`, ticketData);
  return response.data;
};

export const sendSOSAlert = async (
  alertData: { bookingId: string; reason: string }
): Promise<SOSAlert> => {
  const response = await axios.post(`${API_URL}/support/sos`, alertData);
  return response.data;
};
