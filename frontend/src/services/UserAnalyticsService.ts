import { supabase } from '../utils/supabaseClient';

export interface UserAnalytics {
  totalBookings: number;
  completedTrips: number;
  savedVehicles: number;
  totalSpent: number;
  recentBookings: Array<{
    id: string;
    vehicleName: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    status: string;
  }>;
  monthlySpending: Array<{
    month: string;
    amount: number;
  }>;
  favoriteVehicleTypes: Array<{
    type: string;
    count: number;
  }>;
}

class UserAnalyticsService {
  /**
   * Get comprehensive user analytics from Supabase
   */
  async getUserAnalytics(userId: string): Promise<UserAnalytics> {
    try {
      console.log('🔍 Fetching user analytics for user:', userId);

      // Get user's bookings
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          start_date,
          end_date,
          total_amount,
          status,
          vehicles (
            id,
            name,
            type
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.error('❌ Error fetching bookings:', bookingsError);
        throw bookingsError;
      }

      // Get user's saved vehicles
      const { data: savedVehicles, error: savedError } = await supabase
        .from('saved_vehicles')
        .select('id')
        .eq('user_id', userId);

      if (savedError) {
        console.error('❌ Error fetching saved vehicles:', savedError);
        throw savedError;
      }

      // Process the data
      const totalBookings = bookings?.length || 0;
      const completedTrips = bookings?.filter(b => b.status === 'COMPLETED').length || 0;
      const totalSpent = bookings?.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) || 0;
      const savedVehiclesCount = savedVehicles?.length || 0;

      // Recent bookings (last 5)
      const recentBookings = (bookings || []).slice(0, 5).map(booking => ({
        id: booking.id,
        vehicleName: booking.vehicles?.name || 'Unknown Vehicle',
        startDate: booking.start_date,
        endDate: booking.end_date,
        totalAmount: booking.total_amount || 0,
        status: booking.status
      }));

      // Monthly spending (last 6 months)
      const monthlySpending = this.calculateMonthlySpending(bookings || []);

      // Favorite vehicle types
      const favoriteVehicleTypes = this.calculateFavoriteVehicleTypes(bookings || []);

      const analytics: UserAnalytics = {
        totalBookings,
        completedTrips,
        savedVehicles: savedVehiclesCount,
        totalSpent,
        recentBookings,
        monthlySpending,
        favoriteVehicleTypes
      };

      console.log('✅ User analytics fetched successfully:', analytics);
      return analytics;

    } catch (error) {
      console.error('❌ Error fetching user analytics:', error);
      
      // Return empty analytics on error
      return {
        totalBookings: 0,
        completedTrips: 0,
        savedVehicles: 0,
        totalSpent: 0,
        recentBookings: [],
        monthlySpending: [],
        favoriteVehicleTypes: []
      };
    }
  }

  /**
   * Calculate monthly spending for the last 6 months
   */
  private calculateMonthlySpending(bookings: any[]): Array<{ month: string; amount: number }> {
    const monthlyData: { [key: string]: number } = {};
    const now = new Date();
    
    // Initialize last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
      monthlyData[monthKey] = 0;
    }

    // Aggregate spending by month
    bookings.forEach(booking => {
      if (booking.start_date && booking.total_amount) {
        const monthKey = booking.start_date.slice(0, 7); // YYYY-MM format
        if (monthlyData.hasOwnProperty(monthKey)) {
          monthlyData[monthKey] += booking.total_amount;
        }
      }
    });

    // Convert to array format
    return Object.entries(monthlyData).map(([month, amount]) => ({
      month: new Date(month + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      amount
    }));
  }

  /**
   * Calculate favorite vehicle types
   */
  private calculateFavoriteVehicleTypes(bookings: any[]): Array<{ type: string; count: number }> {
    const typeCount: { [key: string]: number } = {};

    bookings.forEach(booking => {
      if (booking.vehicles?.type) {
        const type = booking.vehicles.type;
        typeCount[type] = (typeCount[type] || 0) + 1;
      }
    });

    return Object.entries(typeCount)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Top 5 vehicle types
  }

  /**
   * Check if user has any data
   */
  async hasUserData(userId: string): Promise<boolean> {
    try {
      // Check if user has any bookings
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (bookingsError) {
        console.error('❌ Error checking user bookings:', bookingsError);
        return false;
      }

      // Check if user has any saved vehicles
      const { data: savedVehicles, error: savedError } = await supabase
        .from('saved_vehicles')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (savedError) {
        console.error('❌ Error checking saved vehicles:', savedError);
        return false;
      }

      return (bookings && bookings.length > 0) || (savedVehicles && savedVehicles.length > 0);
    } catch (error) {
      console.error('❌ Error checking user data:', error);
      return false;
    }
  }
}

export default new UserAnalyticsService();
