import { Booking, Vehicle, User } from '../types';
import { io } from 'socket.io-client';

export interface NotificationTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  variables: string[];
}

export interface EmailNotification {
  to: string;
  subject: string;
  body: string;
  bookingId: string;
  type: 'booking_confirmation' | 'booking_cancellation' | 'booking_reminder' | 'payment_success' | 'payment_failed';
}

export interface SMSNotification {
  to: string;
  message: string;
  bookingId: string;
  type: 'booking_confirmation' | 'booking_cancellation' | 'booking_reminder';
}

export class NotificationService {
  private static instance: NotificationService;
  private templates: NotificationTemplate[] = [];
  private socket: any;

  private constructor() {
    this.initializeTemplates();
    // Disable WebSocket connection for now - backend doesn't have socket.io
    // this.socket = io(import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001', {
    //   transports: ['websocket'],
    //   reconnection: true,
    //   reconnectionAttempts: 5,
    //   reconnectionDelay: 1000,
    // });
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private initializeTemplates() {
    this.templates = [
      {
        id: 'booking_confirmation',
        name: 'Booking Confirmation',
        subject: 'Your booking has been confirmed - RentaHub',
        body: `
          Dear {{customerName}},
          
          Your booking has been confirmed!
          
          Booking Details:
          - Vehicle: {{vehicleName}}
          - Start Date: {{startDate}}
          - End Date: {{endDate}}
          - Booking ID: {{bookingId}}
          
          Pickup Instructions:
          {{pickupInstructions}}
          
          If you have any questions, please contact us.
          
          Best regards,
          RentaHub Team
        `,
        variables: ['customerName', 'vehicleName', 'startDate', 'endDate', 'bookingId', 'pickupInstructions']
      },
      {
        id: 'booking_cancellation',
        name: 'Booking Cancellation',
        subject: 'Your booking has been cancelled - RentaHub',
        body: `
          Dear {{customerName}},
          
          Your booking has been cancelled.
          
          Booking Details:
          - Vehicle: {{vehicleName}}
          - Start Date: {{startDate}}
          - End Date: {{endDate}}
          - Booking ID: {{bookingId}}
          
          If you have any questions, please contact us.
          
          Best regards,
          RentaHub Team
        `,
        variables: ['customerName', 'vehicleName', 'startDate', 'endDate', 'bookingId']
      },
      {
        id: 'booking_reminder',
        name: 'Booking Reminder',
        subject: 'Reminder: Your booking is tomorrow - RentaHub',
        body: `
          Dear {{customerName}},
          
          This is a friendly reminder that your booking is tomorrow!
          
          Booking Details:
          - Vehicle: {{vehicleName}}
          - Start Date: {{startDate}}
          - End Date: {{endDate}}
          - Booking ID: {{bookingId}}
          
          Pickup Instructions:
          {{pickupInstructions}}
          
          Please ensure you have your driver's license and payment method ready.
          
          Best regards,
          RentaHub Team
        `,
        variables: ['customerName', 'vehicleName', 'startDate', 'endDate', 'bookingId', 'pickupInstructions']
      },
      {
        id: 'payment_success',
        name: 'Payment Success',
        subject: 'Payment received - RentaHub',
        body: `
          Dear {{customerName}},
          
          We have received your payment for booking {{bookingId}}.
          
          Payment Details:
          - Payment Method: {{paymentMethod}}
          - Transaction ID: {{transactionId}}
          
          Your booking is now confirmed!
          
          Best regards,
          RentaHub Team
        `,
        variables: ['customerName', 'bookingId', 'paymentMethod', 'transactionId']
      },
      {
        id: 'payment_failed',
        name: 'Payment Failed',
        subject: 'Payment failed - RentaHub',
        body: `
          Dear {{customerName}},
          
          We were unable to process your payment for booking {{bookingId}}.
          
          Please update your payment method and try again, or contact us for assistance.
          
          Best regards,
          RentaHub Team
        `,
        variables: ['customerName', 'bookingId']
      }
    ];
  }

  public async sendEmailNotification(notification: EmailNotification): Promise<boolean> {
    try {
      // In a real implementation, this would call your backend API
      const response = await fetch('/api/notifications/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      if (!response.ok) {
        throw new Error('Failed to send email notification');
      }

      console.log('Email notification sent successfully:', notification);
      return true;
    } catch (error) {
      console.error('Error sending email notification:', error);
      return false;
    }
  }

  public async sendSMSNotification(notification: SMSNotification): Promise<boolean> {
    try {
      // In a real implementation, this would call your backend API
      const response = await fetch('/api/notifications/sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      if (!response.ok) {
        throw new Error('Failed to send SMS notification');
      }

      console.log('SMS notification sent successfully:', notification);
      return true;
    } catch (error) {
      console.error('Error sending SMS notification:', error);
      return false;
    }
  }

  public generateBookingConfirmationEmail(booking: Booking, vehicle: Vehicle, user: User): EmailNotification {
    const template = this.templates.find(t => t.id === 'booking_confirmation');
    if (!template) {
      throw new Error('Booking confirmation template not found');
    }

    const variables = {
      customerName: user.name,
      vehicleName: vehicle.name || `${vehicle.brand} ${vehicle.model}`,
      startDate: new Date(booking.startDate).toLocaleDateString(),
      endDate: new Date(booking.endDate).toLocaleDateString(),
      totalAmount: booking.totalAmount ? booking.totalAmount.toString() : 'N/A',
      bookingId: booking.id,
      pickupInstructions: vehicle.pickupInstructions || 'Please contact us for pickup instructions.'
    };

    const body = this.replaceTemplateVariables(template.body, variables);
    const subject = this.replaceTemplateVariables(template.subject, variables);

    return {
      to: user.email,
      subject,
      body,
      bookingId: booking.id,
      type: 'booking_confirmation'
    };
  }

  public generateBookingCancellationEmail(booking: Booking, vehicle: Vehicle, user: User): EmailNotification {
    const template = this.templates.find(t => t.id === 'booking_cancellation');
    if (!template) {
      throw new Error('Booking cancellation template not found');
    }

    const variables = {
      customerName: user.name,
      vehicleName: vehicle.name || `${vehicle.brand} ${vehicle.model}`,
      startDate: new Date(booking.startDate).toLocaleDateString(),
      endDate: new Date(booking.endDate).toLocaleDateString(),
      bookingId: booking.id
    };

    const body = this.replaceTemplateVariables(template.body, variables);
    const subject = this.replaceTemplateVariables(template.subject, variables);

    return {
      to: user.email,
      subject,
      body,
      bookingId: booking.id,
      type: 'booking_cancellation'
    };
  }

  public generateBookingReminderEmail(booking: Booking, vehicle: Vehicle, user: User): EmailNotification {
    const template = this.templates.find(t => t.id === 'booking_reminder');
    if (!template) {
      throw new Error('Booking reminder template not found');
    }

    const variables = {
      customerName: user.name,
      vehicleName: vehicle.name || `${vehicle.brand} ${vehicle.model}`,
      startDate: new Date(booking.startDate).toLocaleDateString(),
      endDate: new Date(booking.endDate).toLocaleDateString(),
      bookingId: booking.id,
      pickupInstructions: vehicle.pickupInstructions || 'Please contact us for pickup instructions.'
    };

    const body = this.replaceTemplateVariables(template.body, variables);
    const subject = this.replaceTemplateVariables(template.subject, variables);

    return {
      to: user.email,
      subject,
      body,
      bookingId: booking.id,
      type: 'booking_reminder'
    };
  }

  public generatePaymentSuccessEmail(booking: Booking, user: User, paymentDetails: any): EmailNotification {
    const template = this.templates.find(t => t.id === 'payment_success');
    if (!template) {
      throw new Error('Payment success template not found');
    }

    const variables = {
      customerName: user.name,
      bookingId: booking.id,
      totalAmount: (booking.totalAmount !== undefined && booking.totalAmount !== null) ? booking.totalAmount.toString() : "0",
      paymentMethod: paymentDetails.paymentMethod || 'Credit Card',
      transactionId: paymentDetails.transactionId || 'N/A'
    };

    const body = this.replaceTemplateVariables(template.body, variables);
    const subject = this.replaceTemplateVariables(template.subject, variables);

    return {
      to: user.email,
      subject,
      body,
      bookingId: booking.id,
      type: 'payment_success'
    };
  }

  public generatePaymentFailedEmail(booking: Booking, user: User): EmailNotification {
    const template = this.templates.find(t => t.id === 'payment_failed');
    if (!template) {
      throw new Error('Payment failed template not found');
    }

    const variables = {
      customerName: user.name,
      bookingId: booking.id
    };

    const body = this.replaceTemplateVariables(template.body, variables);
    const subject = this.replaceTemplateVariables(template.subject, variables);

    return {
      to: user.email,
      subject,
      body,
      bookingId: booking.id,
      type: 'payment_failed'
    };
  }

  private replaceTemplateVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return result;
  }

  public getTemplates(): NotificationTemplate[] {
    return this.templates;
  }

  public async sendBulkNotifications(notifications: EmailNotification[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const notification of notifications) {
      try {
        const result = await this.sendEmailNotification(notification);
        if (result) {
          success++;
        } else {
          failed++;
        }
      } catch (error) {
        failed++;
        console.error('Error sending bulk notification:', error);
      }
    }

    return { success, failed };
  }

  public async scheduleNotification(notification: EmailNotification, scheduledTime: Date): Promise<boolean> {
    try {
      const response = await fetch('/api/notifications/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...notification,
          scheduledTime: scheduledTime.toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to schedule notification');
      }

      console.log('Notification scheduled successfully:', notification);
      return true;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return false;
    }
  }

  // Create a general notification (for admin notifications)
  static async createNotification(notification: {
    type: 'info' | 'warning' | 'error' | 'success';
    title: string;
    message: string;
    userId: string;
    metadata?: any;
  }): Promise<boolean> {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...notification,
          createdAt: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create notification');
      }

      console.log('Notification created successfully:', notification);
      return true;
    } catch (error) {
      console.error('Error creating notification:', error);
      return false;
    }
  }
}

export default NotificationService.getInstance(); 