import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging and debugging
axiosInstance.interceptors.request.use(
  (config) => {
    console.log(`[Axios] Requesting: ${config.url}`, config.params);
    return config;
  },
  (error) => {
    console.error('[Axios] Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and logging
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(`[Axios] Response from ${response.config.url}:`, response.data);
    return response;
  },
  (error) => {
    console.error('[Axios] Response Error:', error.response?.data || error.message);
    
    // Handle specific error scenarios
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Redirect to login or refresh token
          window.location.href = '/signin';
          break;
        case 403:
          console.error('Forbidden: You do not have permission');
          break;
        case 404:
          console.error('Resource not found');
          break;
        case 500:
          console.error('Internal Server Error');
          break;
      }
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;
