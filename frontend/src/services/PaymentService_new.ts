import * as types from '../types'
import axiosInstance from './axiosInstance'

// Format currency utility function
export const formatCurrency = (amount: number, currencyCode: string = 'USD'): string => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    // Fallback for unsupported currencies
    return `${currencyCode} ${amount.toLocaleString()}`;
  }
};

/**
 * Create payment intent for booking
 */
export const createPaymentIntent = (
  bookingId: string,
  paymentMethod: types.PaymentMethod
): Promise<types.ApiResponse<{ 
  paymentIntentId: string
  clientSecret: string
  customerId?: string
}>> =>
  axiosInstance
    .post('/payments/create-intent', { bookingId, paymentMethod })
    .then((res) => res.data)

/**
 * Confirm payment
 */
export const confirmPayment = (
  paymentIntentId: string,
  paymentMethodId?: string
): Promise<types.ApiResponse<{ status: string; bookingId: string }>> =>
  axiosInstance
    .post('/payments/confirm', { paymentIntentId, paymentMethodId })
    .then((res) => res.data)

/**
 * Process refund
 */
export const processRefund = (
  paymentIntentId: string,
  amount?: number,
  reason?: string
): Promise<types.ApiResponse<{ refundId: string; status: string }>> =>
  axiosInstance
    .post('/payments/refund', { paymentIntentId, amount, reason })
    .then((res) => res.data)

/**
 * Get payment details
 */
export const getPaymentDetails = (
  paymentIntentId: string
): Promise<types.ApiResponse<{
  id: string
  amount: number
  currency: string
  status: string
  paymentMethod: string
  bookingId: string
  createdAt: string
}>> =>
  axiosInstance
    .get(`/payments/${paymentIntentId}`)
    .then((res) => res.data)

/**
 * Get user payment history
 */
export const getUserPaymentHistory = (
  userId: string,
  page: number = 1,
  limit: number = 10
): Promise<types.ApiResponse<types.PaginatedResponse<{
  id: string
  amount: number
  currency: string
  status: string
  paymentMethod: string
  bookingId: string
  createdAt: string
}>>> =>
  axiosInstance
    .get(`/payments/user/${userId}?page=${page}&limit=${limit}`)
    .then((res) => res.data)

/**
 * Setup customer for future payments
 */
export const setupCustomerPaymentMethod = (
  customerId: string,
  paymentMethodId: string
): Promise<types.ApiResponse<{ success: boolean }>> =>
  axiosInstance
    .post('/payments/setup-customer', { customerId, paymentMethodId })
    .then((res) => res.data)

/**
 * Get customer saved payment methods
 */
export const getCustomerPaymentMethods = (
  customerId: string
): Promise<types.ApiResponse<Array<{
  id: string
  type: string
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
}>>> =>
  axiosInstance
    .get(`/payments/customer/${customerId}/methods`)
    .then((res) => res.data)
