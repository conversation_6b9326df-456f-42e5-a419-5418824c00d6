import axios from 'axios'
import { supabase } from './supabaseClient'

export interface PlatformOverview {
  totalBookings: number
  totalRevenue: number
  topCities: Array<{
    name: string
    bookingCount: number
  }>
}

export interface OwnerPerformance {
  totalBookings: number
  totalEarnings: number
  topVehicles: Array<{
    name: string
    bookingCount: number
  }>
}

export interface BookingTrend {
  date: string
  count: number
}

export interface GeospatialVehicle {
  id: string
  name: string
  latitude: number
  longitude: number
  distance: number
}

class AnalyticsService {
  private baseUrl = '/api/analytics'

  // Fetch platform-level overview (Admin only)
  async getPlatformOverview(days: number = 30): Promise<PlatformOverview> {
    const { data } = await axios.get(`${this.baseUrl}/platform/overview`, {
      params: { days }
    })
    return data
  }

  // Fetch owner performance (Owner & Admin)
  async getOwnerPerformance(ownerId?: string, days: number = 30): Promise<OwnerPerformance> {
    const { data } = await axios.get(`${this.baseUrl}/owner/performance`, {
      params: { ownerId, days }
    })
    return data
  }

  // Geospatial vehicle search
  async findNearbyVehicles(
    latitude: number, 
    longitude: number, 
    radius: number = 10
  ): Promise<GeospatialVehicle[]> {
    const { data } = await axios.get(`${this.baseUrl}/vehicles/nearby`, {
      params: { latitude, longitude, radius }
    })
    return data
  }

  // Fetch booking trends
  async getBookingTrends(days: number = 90): Promise<BookingTrend[]> {
    const { data } = await axios.get(`${this.baseUrl}/booking-trends`, {
      params: { days }
    })
    return data
  }

  // Optional: Log geospatial search events to Supabase
  async logGeospatialSearch(
    latitude: number, 
    longitude: number, 
    radius: number = 10
  ) {
    const { data: { user } } = await supabase.auth.getUser()
    
    const { error } = await supabase
      .from('geospatial_search_logs')
      .insert({
        user_id: user?.id,
        latitude,
        longitude,
        radius,
        searched_at: new Date().toISOString()
      })

    if (error) console.error('Error logging geospatial search:', error)
  }
}

export default new AnalyticsService() 