/**
 * Dynamic Currency Service - Real-time currency conversion
 * Automatically converts prices based on user location and preferences
 */

import i18n from '../i18n';

interface ExchangeRates {
  [currency: string]: number;
}

interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  locale: string;
  decimals: number;
}

interface GeolocationCurrency {
  [countryCode: string]: string;
}

class DynamicCurrencyService {
  private exchangeRates: ExchangeRates = {};
  private baseCurrency = 'USD';
  private lastUpdated: number = 0;
  private updateInterval = 3600000; // 1 hour
  private apiKey: string = import.meta.env.VITE_EXCHANGE_RATE_API_KEY || '';

  // Currency information database
  private currencies: { [code: string]: CurrencyInfo } = {
    USD: { code: 'USD', symbol: '$', name: 'US Dollar', locale: 'en-US', decimals: 2 },
    EUR: { code: 'EUR', symbol: '€', name: 'Euro', locale: 'de-DE', decimals: 2 },
    IDR: { code: 'IDR', symbol: 'Rp', name: 'Indonesian Rupiah', locale: 'id-ID', decimals: 0 },
    SGD: { code: 'SGD', symbol: 'S$', name: 'Singapore Dollar', locale: 'en-SG', decimals: 2 },
    MYR: { code: 'MYR', symbol: 'RM', name: 'Malaysian Ringgit', locale: 'ms-MY', decimals: 2 },
    THB: { code: 'THB', symbol: '฿', name: 'Thai Baht', locale: 'th-TH', decimals: 2 },
    VND: { code: 'VND', symbol: '₫', name: 'Vietnamese Dong', locale: 'vi-VN', decimals: 0 },
    PHP: { code: 'PHP', symbol: '₱', name: 'Philippine Peso', locale: 'en-PH', decimals: 2 },
    CNY: { code: 'CNY', symbol: '¥', name: 'Chinese Yuan', locale: 'zh-CN', decimals: 2 },
    JPY: { code: 'JPY', symbol: '¥', name: 'Japanese Yen', locale: 'ja-JP', decimals: 0 },
    KRW: { code: 'KRW', symbol: '₩', name: 'South Korean Won', locale: 'ko-KR', decimals: 0 },
    GBP: { code: 'GBP', symbol: '£', name: 'British Pound', locale: 'en-GB', decimals: 2 },
    AUD: { code: 'AUD', symbol: 'A$', name: 'Australian Dollar', locale: 'en-AU', decimals: 2 },
    CAD: { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar', locale: 'en-CA', decimals: 2 },
    INR: { code: 'INR', symbol: '₹', name: 'Indian Rupee', locale: 'en-IN', decimals: 2 },
    AED: { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham', locale: 'ar-AE', decimals: 2 },
    SAR: { code: 'SAR', symbol: 'ر.س', name: 'Saudi Riyal', locale: 'ar-SA', decimals: 2 }
  };

  // Map languages to preferred currencies
  private languageCurrencyMap: { [lang: string]: string } = {
    'en': 'USD',
    'id': 'IDR',
    'zh': 'CNY',
    'ja': 'JPY',
    'ko': 'KRW',
    'th': 'THB',
    'vi': 'VND',
    'ms': 'MYR',
    'tl': 'PHP',
    'ar': 'AED',
    'de': 'EUR',
    'fr': 'EUR',
    'es': 'EUR',
    'pt': 'EUR',
    'ru': 'USD' // Fallback to USD for Russian
  };

  // Map countries to currencies (for geolocation-based detection)
  private countryCurrencyMap: GeolocationCurrency = {
    'US': 'USD', 'ID': 'IDR', 'SG': 'SGD', 'MY': 'MYR', 'TH': 'THB',
    'VN': 'VND', 'PH': 'PHP', 'CN': 'CNY', 'JP': 'JPY', 'KR': 'KRW',
    'GB': 'GBP', 'AU': 'AUD', 'CA': 'CAD', 'IN': 'INR', 'AE': 'AED',
    'SA': 'SAR', 'DE': 'EUR', 'FR': 'EUR', 'ES': 'EUR', 'IT': 'EUR',
    'NL': 'EUR', 'BE': 'EUR', 'AT': 'EUR', 'PT': 'EUR', 'IE': 'EUR'
  };

  constructor() {
    this.loadCachedRates();
    this.updateExchangeRates();
  }

  /**
   * Get user's preferred currency based on language, location, or manual selection
   */
  async getUserCurrency(): Promise<string> {
    // Check if user has manually selected a currency
    const savedCurrency = localStorage.getItem('rentahub-preferred-currency');
    if (savedCurrency && this.currencies[savedCurrency]) {
      return savedCurrency;
    }

    // Try to detect based on geolocation
    try {
      const locationCurrency = await this.detectCurrencyByLocation();
      if (locationCurrency) {
        return locationCurrency;
      }
    } catch (error) {
      console.warn('Geolocation currency detection failed:', error);
    }

    // Fallback to language-based currency
    const currentLang = i18n.language || 'en';
    const langBasedCurrency = this.languageCurrencyMap[currentLang] || 'USD';
    
    return langBasedCurrency;
  }

  /**
   * Format currency amount with proper localization
   */
  async formatCurrency(
    amount: number, 
    fromCurrency: string = 'USD',
    toCurrency?: string,
    options?: Partial<Intl.NumberFormatOptions>
  ): Promise<string> {
    const targetCurrency = toCurrency || await this.getUserCurrency();
    const convertedAmount = await this.convertCurrency(amount, fromCurrency, targetCurrency);
    
    const currencyInfo = this.currencies[targetCurrency];
    if (!currencyInfo) {
      return `${targetCurrency} ${convertedAmount.toFixed(2)}`;
    }

    try {
      return new Intl.NumberFormat(currencyInfo.locale, {
        style: 'currency',
        currency: targetCurrency,
        minimumFractionDigits: currencyInfo.decimals,
        maximumFractionDigits: currencyInfo.decimals,
        ...options
      }).format(convertedAmount);
    } catch (error) {
      // Fallback formatting
      return `${currencyInfo.symbol}${convertedAmount.toFixed(currencyInfo.decimals)}`;
    }
  }

  /**
   * Convert amount between currencies
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    await this.ensureRatesUpdated();

    const fromRate = this.exchangeRates[fromCurrency] || 1;
    const toRate = this.exchangeRates[toCurrency] || 1;

    // Convert to base currency (USD) then to target currency
    const usdAmount = amount / fromRate;
    return usdAmount * toRate;
  }

  /**
   * Get all supported currencies
   */
  getSupportedCurrencies(): CurrencyInfo[] {
    return Object.values(this.currencies);
  }

  /**
   * Set user's preferred currency
   */
  setUserCurrency(currencyCode: string) {
    if (this.currencies[currencyCode]) {
      localStorage.setItem('rentahub-preferred-currency', currencyCode);
      // Trigger currency change event
      window.dispatchEvent(new CustomEvent('currencyChanged', { 
        detail: { currency: currencyCode } 
      }));
    }
  }

  /**
   * Get currency symbol
   */
  getCurrencySymbol(currencyCode: string): string {
    return this.currencies[currencyCode]?.symbol || currencyCode;
  }

  /**
   * Detect currency based on user's location
   */
  private async detectCurrencyByLocation(): Promise<string | null> {
    try {
      // Try to get country from IP geolocation
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      if (data.country_code) {
        return this.countryCurrencyMap[data.country_code] || null;
      }
    } catch (error) {
      console.warn('IP geolocation failed:', error);
    }

    return null;
  }

  /**
   * Update exchange rates from API
   */
  private async updateExchangeRates() {
    try {
      let rates: ExchangeRates = {};

      if (this.apiKey) {
        // Use paid API service (e.g., exchangerate-api.com)
        const response = await fetch(
          `https://v6.exchangerate-api.com/v6/${this.apiKey}/latest/${this.baseCurrency}`
        );
        const data = await response.json();
        rates = data.conversion_rates || {};
      } else {
        // Use free API as fallback (limited requests)
        const response = await fetch(
          `https://api.exchangerate-api.com/v4/latest/${this.baseCurrency}`
        );
        const data = await response.json();
        rates = data.rates || {};
      }

      if (Object.keys(rates).length > 0) {
        this.exchangeRates = { [this.baseCurrency]: 1, ...rates };
        this.lastUpdated = Date.now();
        this.saveCachedRates();
        console.log('Exchange rates updated successfully');
      }
    } catch (error) {
      console.warn('Failed to update exchange rates:', error);
      // Use cached rates or fallback rates
      this.loadFallbackRates();
    }
  }

  /**
   * Ensure rates are up to date
   */
  private async ensureRatesUpdated() {
    const now = Date.now();
    if (now - this.lastUpdated > this.updateInterval) {
      await this.updateExchangeRates();
    }
  }

  /**
   * Load cached exchange rates
   */
  private loadCachedRates() {
    try {
      const cached = localStorage.getItem('rentahub-exchange-rates');
      const timestamp = localStorage.getItem('rentahub-rates-timestamp');
      
      if (cached && timestamp) {
        this.exchangeRates = JSON.parse(cached);
        this.lastUpdated = parseInt(timestamp);
      }
    } catch (error) {
      console.warn('Failed to load cached rates:', error);
    }
  }

  /**
   * Save exchange rates to cache
   */
  private saveCachedRates() {
    try {
      localStorage.setItem('rentahub-exchange-rates', JSON.stringify(this.exchangeRates));
      localStorage.setItem('rentahub-rates-timestamp', this.lastUpdated.toString());
    } catch (error) {
      console.warn('Failed to cache exchange rates:', error);
    }
  }

  /**
   * Load fallback exchange rates (approximate values)
   */
  private loadFallbackRates() {
    this.exchangeRates = {
      USD: 1,
      EUR: 0.85,
      IDR: 15000,
      SGD: 1.35,
      MYR: 4.2,
      THB: 33,
      VND: 24000,
      PHP: 55,
      CNY: 7.2,
      JPY: 110,
      KRW: 1200,
      GBP: 0.75,
      AUD: 1.4,
      CAD: 1.25,
      INR: 75,
      AED: 3.67,
      SAR: 3.75
    };
    console.log('Using fallback exchange rates');
  }

  /**
   * React hook for currency operations
   */
  useCurrency() {
    return {
      formatCurrency: (amount: number, fromCurrency?: string, toCurrency?: string) => 
        this.formatCurrency(amount, fromCurrency, toCurrency),
      convertCurrency: (amount: number, from: string, to: string) => 
        this.convertCurrency(amount, from, to),
      getUserCurrency: () => this.getUserCurrency(),
      setUserCurrency: (currency: string) => this.setUserCurrency(currency),
      getSupportedCurrencies: () => this.getSupportedCurrencies(),
      getCurrencySymbol: (code: string) => this.getCurrencySymbol(code)
    };
  }
}

export default new DynamicCurrencyService();

// React components for easy integration
export { default as CurrencySelector } from '../components/CurrencySelector';
export { default as PriceDisplay } from '../components/PriceDisplay';
