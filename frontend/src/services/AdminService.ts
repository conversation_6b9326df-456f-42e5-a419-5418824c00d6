import axiosInstance from './axiosInstance';
import { ProviderAnalytics } from '../types';

export interface DashboardStats {
  totalBookings: number;
  totalRevenue: number;
  totalVehicles: number;
  totalUsers: number;
  activeBookings: number;
  pendingBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  monthlyGrowth: number;
  revenueGrowth: number;
}

export interface BookingAnalytics {
  bookingsByMonth: Array<{ month: string; count: number; revenue: number }>;
  bookingsByVehicleType: Array<{ type: string; count: number; percentage: number }>;
  bookingsByStatus: Array<{ status: string; count: number; percentage: number }>;
  averageBookingDuration: number;
  popularVehicles: Array<{ vehicleId: string; name: string; bookings: number }>;
  peakHours: Array<{ hour: number; bookings: number }>;
  peakDays: Array<{ day: string; bookings: number }>;
}

export interface UserAnalytics {
  usersByType: Array<{ type: string; count: number; percentage: number }>;
  userGrowth: Array<{ month: string; newUsers: number; totalUsers: number }>;
  userActivity: Array<{ date: string; activeUsers: number }>;
  topProviders: Array<{ providerId: string; name: string; revenue: number; bookings: number }>;
  userRetention: Array<{ period: string; retentionRate: number }>;
}

export interface VehicleAnalytics {
  vehiclesByCategory: Array<{ category: string; count: number; percentage: number }>;
  vehiclesByLocation: Array<{ location: string; count: number; percentage: number }>;
  vehicleUtilization: Array<{ vehicleId: string; name: string; utilizationRate: number }>;
  popularFeatures: Array<{ feature: string; count: number; percentage: number }>;
  maintenanceSchedule: Array<{ vehicleId: string; name: string; nextMaintenance: string }>;
}

export interface RevenueAnalytics {
  revenueByMonth: Array<{ month: string; revenue: number; bookings: number }>;
  revenueByVehicleType: Array<{ type: string; revenue: number; percentage: number }>;
  revenueByLocation: Array<{ location: string; revenue: number; percentage: number }>;
  averageBookingValue: number;
  commissionEarned: number;
  payoutsPending: number;
  payoutsCompleted: number;
}

export interface FinancialReport {
  totalRevenue: number;
  totalCommission: number;
  totalPayouts: number;
  netProfit: number;
  taxesOwed: number;
  operatingExpenses: number;
  transactions: Array<{
    id: string;
    type: 'booking' | 'commission' | 'payout' | 'refund';
    amount: number;
    date: string;
    description: string;
  }>;
}

/**
 * AdminService - Comprehensive admin and analytics functionality
 * Based on bookcars and rentnride admin patterns
 */
export class AdminService {
  /**
   * Get dashboard statistics
   */
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      const response = await axiosInstance.get('/admin/dashboard/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalBookings: 0,
        totalRevenue: 0,
        totalVehicles: 0,
        totalUsers: 0,
        activeBookings: 0,
        pendingBookings: 0,
        completedBookings: 0,
        cancelledBookings: 0,
        monthlyGrowth: 0,
        revenueGrowth: 0
      };
    }
  }

  /**
   * Get booking analytics
   */
  static async getBookingAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<BookingAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axiosInstance.get(
        `/admin/analytics/bookings?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching booking analytics:', error);
      return {
        bookingsByMonth: [],
        bookingsByVehicleType: [],
        bookingsByStatus: [],
        averageBookingDuration: 0,
        popularVehicles: [],
        peakHours: [],
        peakDays: []
      };
    }
  }

  /**
   * Get user analytics
   */
  static async getUserAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<UserAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axiosInstance.get(
        `/admin/analytics/users?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      return {
        usersByType: [],
        userGrowth: [],
        userActivity: [],
        topProviders: [],
        userRetention: []
      };
    }
  }

  /**
   * Get vehicle analytics
   */
  static async getVehicleAnalytics(): Promise<VehicleAnalytics> {
    try {
      const response = await axiosInstance.get('/admin/analytics/vehicles');
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicle analytics:', error);
      return {
        vehiclesByCategory: [],
        vehiclesByLocation: [],
        vehicleUtilization: [],
        popularFeatures: [],
        maintenanceSchedule: []
      };
    }
  }

  /**
   * Get revenue analytics
   */
  static async getRevenueAnalytics(
    startDate?: string,
    endDate?: string
  ): Promise<RevenueAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axiosInstance.get(
        `/admin/analytics/revenue?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      return {
        revenueByMonth: [],
        revenueByVehicleType: [],
        revenueByLocation: [],
        averageBookingValue: 0,
        commissionEarned: 0,
        payoutsPending: 0,
        payoutsCompleted: 0
      };
    }
  }

  /**
   * Generate financial report
   */
  static async generateFinancialReport(
    startDate: string,
    endDate: string
  ): Promise<FinancialReport> {
    try {
      const response = await axiosInstance.post('/admin/reports/financial', {
        startDate,
        endDate
      });
      return response.data;
    } catch (error) {
      console.error('Error generating financial report:', error);
      return {
        totalRevenue: 0,
        totalCommission: 0,
        totalPayouts: 0,
        netProfit: 0,
        taxesOwed: 0,
        operatingExpenses: 0,
        transactions: []
      };
    }
  }

  /**
   * Export data
   */
  static async exportData(
    type: 'bookings' | 'users' | 'vehicles' | 'revenue',
    format: 'csv' | 'xlsx' | 'pdf',
    startDate?: string,
    endDate?: string
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await axiosInstance.get(
        `/admin/export/${type}?${params.toString()}`,
        { responseType: 'blob' }
      );
      return response.data;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  /**
   * Get system health
   */
  static async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    responseTime: number;
    errorRate: number;
    activeUsers: number;
    systemLoad: number;
    databaseStatus: 'connected' | 'disconnected';
    paymentGatewayStatus: 'active' | 'inactive';
  }> {
    try {
      const response = await axiosInstance.get('/admin/system/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching system health:', error);
      return {
        status: 'critical',
        uptime: 0,
        responseTime: 0,
        errorRate: 100,
        activeUsers: 0,
        systemLoad: 0,
        databaseStatus: 'disconnected',
        paymentGatewayStatus: 'inactive'
      };
    }
  }

  /**
   * Manage user
   */
  static async updateUserStatus(
    userId: string,
    status: 'active' | 'suspended' | 'banned'
  ): Promise<boolean> {
    try {
      await axiosInstance.put(`/admin/users/${userId}/status`, { status });
      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }

  /**
   * Verify user documents
   */
  static async verifyUserDocument(
    userId: string,
    documentId: string,
    verified: boolean,
    notes?: string
  ): Promise<boolean> {
    try {
      await axiosInstance.put(`/admin/users/${userId}/documents/${documentId}/verify`, {
        verified,
        notes
      });
      return true;
    } catch (error) {
      console.error('Error verifying user document:', error);
      return false;
    }
  }

  /**
   * Approve vehicle
   */
  static async approveVehicle(
    vehicleId: string,
    approved: boolean,
    notes?: string
  ): Promise<boolean> {
    try {
      await axiosInstance.put(`/admin/vehicles/${vehicleId}/approve`, {
        approved,
        notes
      });
      return true;
    } catch (error) {
      console.error('Error approving vehicle:', error);
      return false;
    }
  }

  /**
   * Process refund
   */
  static async processRefund(
    bookingId: string,
    amount: number,
    reason: string
  ): Promise<boolean> {
    try {
      await axiosInstance.post(`/admin/bookings/${bookingId}/refund`, {
        amount,
        reason
      });
      return true;
    } catch (error) {
      console.error('Error processing refund:', error);
      return false;
    }
  }

  /**
   * Send system notification
   */
  static async sendSystemNotification(notification: {
    title: string;
    message: string;
    type: 'info' | 'warning' | 'error' | 'success';
    targetUsers?: string[];
    targetUserTypes?: string[];
  }): Promise<boolean> {
    try {
      await axiosInstance.post('/admin/notifications/system', notification);
      return true;
    } catch (error) {
      console.error('Error sending system notification:', error);
      return false;
    }
  }

  /**
   * Update system settings
   */
  static async updateSystemSettings(settings: {
    commissionRate?: number;
    cancellationFee?: number;
    maintenanceMode?: boolean;
    maxBookingDuration?: number;
    minimumAge?: number;
    requireVerification?: boolean;
  }): Promise<boolean> {
    try {
      await axiosInstance.put('/admin/settings', settings);
      return true;
    } catch (error) {
      console.error('Error updating system settings:', error);
      return false;
    }
  }

  /**
   * Get audit logs
   */
  static async getAuditLogs(
    page: number = 1,
    limit: number = 50,
    startDate?: string,
    endDate?: string,
    userId?: string,
    action?: string
  ): Promise<{
    logs: Array<{
      id: string;
      userId: string;
      action: string;
      resource: string;
      details: any;
      timestamp: string;
      ipAddress: string;
    }>;
    pagination: any;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      if (userId) params.append('userId', userId);
      if (action) params.append('action', action);

      const response = await axiosInstance.get(
        `/admin/audit-logs?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      return { logs: [], pagination: null };
    }
  }

  /**
   * Get provider analytics data
   */
  static async getProviderAnalytics(providerId: string): Promise<ProviderAnalytics | null> {
    try {
      const response = await axiosInstance.get(`/analytics/provider/${providerId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching provider analytics:', error);
      // Return mock data for development
      return {
        totalEarnings: 15420.50,
        monthlyEarnings: 3250.75,
        totalBookings: 127,
        completedBookings: 118,
        cancelledBookings: 9,
        averageRating: 4.7,
        totalReviews: 89,
        cancellationRate: 7.1,
        responseTime: 12,
        responseRate: 95.5,
      };
    }
  }
}

export default AdminService;
