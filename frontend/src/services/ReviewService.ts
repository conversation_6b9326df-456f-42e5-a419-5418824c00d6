import axiosInstance from './axiosInstance';
import { Review, Booking, Vehicle, User } from '../types';

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface CreateReviewPayload {
  bookingId: string;
  rating: number;
  comment: string;
  reviewType: 'user_to_provider' | 'provider_to_user' | 'vehicle_review';
  photos?: string[];
}

export interface ReviewFilters {
  rating?: number;
  sortBy?: 'newest' | 'oldest' | 'highest_rating' | 'lowest_rating';
  hasPhotos?: boolean;
  verified?: boolean;
}

/**
 * ReviewService - Comprehensive review and rating management
 * Based on bookcars and rentnride feedback patterns
 */
export class ReviewService {
  /**
   * Create a new review
   */
  static async createReview(review: CreateReviewPayload): Promise<Review | null> {
    try {
      const response = await axiosInstance.post('/reviews', review);
      return response.data.review;
    } catch (error) {
      console.error('Error creating review:', error);
      return null;
    }
  }

  /**
   * Get reviews for a vehicle
   */
  static async getVehicleReviews(
    vehicleId: string,
    page: number = 1,
    limit: number = 10,
    filters?: ReviewFilters
  ): Promise<{ reviews: Review[]; pagination: any; stats: ReviewStats }> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      
      if (filters?.rating) params.append('rating', filters.rating.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.hasPhotos) params.append('hasPhotos', 'true');
      if (filters?.verified) params.append('verified', 'true');

      const response = await axiosInstance.get(
        `/vehicles/${vehicleId}/reviews?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicle reviews:', error);
      return {
        reviews: [],
        pagination: null,
        stats: {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
        }
      };
    }
  }

  /**
   * Get reviews for a user (as provider)
   */
  static async getUserReviews(
    userId: string,
    page: number = 1,
    limit: number = 10,
    filters?: ReviewFilters
  ): Promise<{ reviews: Review[]; pagination: any; stats: ReviewStats }> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      
      if (filters?.rating) params.append('rating', filters.rating.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.hasPhotos) params.append('hasPhotos', 'true');
      if (filters?.verified) params.append('verified', 'true');

      const response = await axiosInstance.get(
        `/users/${userId}/reviews?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      return {
        reviews: [],
        pagination: null,
        stats: {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
        }
      };
    }
  }

  /**
   * Get review statistics for a vehicle
   */
  static async getVehicleReviewStats(vehicleId: string): Promise<ReviewStats> {
    try {
      const response = await axiosInstance.get(`/vehicles/${vehicleId}/review-stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicle review stats:', error);
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
      };
    }
  }

  /**
   * Check if user can review a booking
   */
  static async canReviewBooking(bookingId: string): Promise<{
    canReview: boolean;
    reason?: string;
    existingReview?: Review;
  }> {
    try {
      const response = await axiosInstance.get(`/bookings/${bookingId}/can-review`);
      return response.data;
    } catch (error) {
      console.error('Error checking review eligibility:', error);
      return {
        canReview: false,
        reason: 'Unable to check review eligibility'
      };
    }
  }

  /**
   * Get featured reviews
   */
  static async getFeaturedReviews(limit: number = 5): Promise<Review[]> {
    try {
      const response = await axiosInstance.get(`/reviews/featured?limit=${limit}`);
      return response.data.reviews || [];
    } catch (error) {
      console.error('Error fetching featured reviews:', error);
      return [];
    }
  }

  /**
   * Get reviews for a provider
   */
  static async getProviderReviews(
    providerId: string,
    filters?: ReviewFilters,
    page = 1,
    limit = 10
  ): Promise<Review[]> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      
      if (filters?.rating) params.append('rating', filters.rating.toString());
      if (filters?.sortBy) params.append('sortBy', filters.sortBy);
      if (filters?.hasPhotos !== undefined) params.append('hasPhotos', filters.hasPhotos.toString());
      if (filters?.verified !== undefined) params.append('verified', filters.verified.toString());

      const response = await axiosInstance.get(`/reviews/provider/${providerId}?${params.toString()}`);
      return response.data.reviews || [];
    } catch (error) {
      console.error('Error fetching provider reviews:', error);
      return [];
    }
  }
}

// Legacy exports for backward compatibility
export const submitReview = async (reviewData: CreateReviewPayload) => {
  return ReviewService.createReview(reviewData);
};

export const getVehicleReviews = async (vehicleId: string, page = 1, limit = 10) => {
  const result = await ReviewService.getVehicleReviews(vehicleId, page, limit);
  return result.reviews;
};

export const getProviderReviews = async (providerId: string, page = 1, limit = 10) => {
  const result = await ReviewService.getUserReviews(providerId, page, limit);
  return result.reviews;
};

export const respondToReview = async (reviewId: string, response: string) => {
  try {
    const result = await axiosInstance.post(`/reviews/${reviewId}/respond`, { response });
    return result.data;
  } catch (error) {
    console.error('Error responding to review', error);
    throw error;
  }
};

export default ReviewService;
