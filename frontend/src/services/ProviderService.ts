export interface Provider {
  id: string;
  email: string;
  name: string;
  phoneNumber?: string;
  companyName?: string;
  rating: number;
  responseRate: number;
  totalVehicles: number;
  totalBookings: number;
  totalRevenue: number;
  joinedAt: Date;
  verified: boolean;
  active: boolean;
  profileImage?: string;
  bankDetails?: {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
    branchName?: string;
  };
}

export interface ProviderDashboardStats {
  totalVehicles: number;
  activeVehicles: number;
  totalBookings: number;
  pendingBookings: number;
  completedBookings: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageRating: number;
  responseRate: number;
  totalViews?: number;
  conversionRate?: number;
  recentBookings: Array<{
    id: string;
    vehicleId: string;
    vehicleName: string;
    customerName: string;
    startDate: Date;
    endDate: Date;
    status: string;
    totalAmount: number;
  }>;
  topVehicles: Array<{
    id: string;
    name: string;
    bookings: number;
    revenue: number;
    rating: number;
  }>;
  earningsChart?: Array<{
    date: string;
    earnings: number;
    bookings: number;
  }>;
  bookingsByCategory?: Array<{
    category: string;
    count: number;
    earnings: number;
  }>;
  recentActivity?: Array<{
    id: string;
    type: 'booking' | 'payment' | 'review';
    description: string;
    amount?: number;
    timestamp: string;
  }>;
}

export interface ProviderResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export class ProviderService {
  // Use relative URLs in development to work with Vite proxy
  private static baseUrl = import.meta.env.PROD ? (import.meta.env.VITE_API_URL || 'http://localhost:3001') : '';

  /**
   * Get current provider's profile
   */
  static async getCurrentProvider(): Promise<ProviderResponse<Provider>> {
    try {
      console.log('🔍 Fetching current provider profile...');
      
      const response = await fetch(`${this.baseUrl}/api/providers/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // Test user from seed data
        }
      });
      
      const result = await response.json();
      console.log('👤 Provider profile result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error fetching provider profile:', error);
      return {
        success: false,
        error: 'Failed to fetch provider profile'
      };
    }
  }

  /**
   * Update provider profile
   */
  static async updateProvider(data: {
    name?: string;
    phoneNumber?: string;
    companyName?: string;
    profileImage?: string;
  }): Promise<ProviderResponse<Provider>> {
    try {
      console.log('📝 Updating provider profile:', data);
      
      const response = await fetch(`${this.baseUrl}/api/providers/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        },
        body: JSON.stringify(data)
      });
      
      const result = await response.json();
      console.log('✅ Provider update result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error updating provider profile:', error);
      return {
        success: false,
        error: 'Failed to update provider profile'
      };
    }
  }

  /**
   * Get provider dashboard statistics
   */
  static async getDashboardStats(userId?: string): Promise<ProviderResponse<ProviderDashboardStats>> {
    try {
      console.log('📊 Fetching provider dashboard stats for user:', userId);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // Use provided userId or fallback to hardcoded test ID
      if (userId) {
        headers['x-user-id'] = userId;
      } else {
        headers['x-user-id'] = '550e8400-e29b-41d4-a716-************'; // Fallback for testing
      }

      const response = await fetch(`${this.baseUrl}/api/providers/dashboard/stats`, {
        method: 'GET',
        headers
      });

      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('📈 Dashboard stats result:', result);

      return result;
    } catch (error) {
      console.error('❌ Error fetching dashboard stats:', error);
      return {
        success: false,
        error: 'Failed to fetch dashboard statistics'
      };
    }
  }

  /**
   * Get provider by ID (public)
   */
  static async getProviderById(id: string): Promise<ProviderResponse<Provider>> {
    try {
      console.log('🔍 Fetching provider by ID:', id);
      
      const response = await fetch(`${this.baseUrl}/api/providers/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      console.log('👤 Provider by ID result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error fetching provider by ID:', error);
      return {
        success: false,
        error: 'Failed to fetch provider'
      };
    }
  }

  /**
   * Get all providers (admin only)
   */
  static async getAllProviders(
    page: number = 1,
    limit: number = 20,
    search?: string
  ): Promise<ProviderResponse<Provider[]> & { pagination?: any }> {
    try {
      console.log('🔍 Fetching all providers:', { page, limit, search });
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });
      
      if (search) {
        params.append('search', search);
      }
      
      const response = await fetch(`${this.baseUrl}/api/providers?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'temp-admin-id' // TODO: Replace with actual auth token
        }
      });
      
      const result = await response.json();
      console.log('👥 All providers result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error fetching all providers:', error);
      return {
        success: false,
        error: 'Failed to fetch providers'
      };
    }
  }

  /**
   * Check provider service health
   */
  static async healthCheck(): Promise<ProviderResponse<any>> {
    try {
      console.log('🏥 Checking provider service health...');
      
      const response = await fetch(`${this.baseUrl}/api/providers/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      console.log('💚 Provider health check result:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Error checking provider health:', error);
      return {
        success: false,
        error: 'Provider service is unhealthy'
      };
    }
  }

  // Provider Profile APIs

  /**
   * Get provider profile
   */
  static async getProviderProfile(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching provider profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update provider profile
   */
  static async updateProviderProfile(profileData: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        },
        body: JSON.stringify(profileData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating provider profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Payment Management APIs

  /**
   * Get bank accounts
   */
  static async getBankAccounts(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bank-accounts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching bank accounts:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add bank account
   */
  static async addBankAccount(accountData: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bank-accounts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        },
        body: JSON.stringify(accountData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error adding bank account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete bank account
   */
  static async deleteBankAccount(accountId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bank-accounts/${accountId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting bank account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Set default bank account
   */
  static async setDefaultBankAccount(accountId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bank-accounts/${accountId}/default`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error setting default bank account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get payment settings
   */
  static async getPaymentSettings(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/payment-settings`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment settings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update payment settings
   */
  static async updatePaymentSettings(settings: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/payment-settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating payment settings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get earnings data
   */
  static async getEarningsData(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/earnings`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching earnings data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Request payout
   */
  static async requestPayout(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/payout-request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error requesting payout:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get payment states (tracking payment flow from booking to payout)
   */
  static async getPaymentStates(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/payment-states`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment states:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get agreement templates
   */
  static async getAgreementTemplates(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/agreements/templates`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching agreement templates:', error);
      // Fallback to default templates if backend is not available
      try {
        const { defaultAgreementTemplates } = await import('../data/agreementTemplates');
        return {
          success: true,
          data: defaultAgreementTemplates
        };
      } catch (fallbackError) {
        return { success: false, error: error.message };
      }
    }
  }

  /**
   * Send agreement to customer
   */
  static async sendAgreement(agreementData: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/agreements/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        },
        body: JSON.stringify(agreementData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending agreement:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get sent agreements
   */
  static async getSentAgreements(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/agreements`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching agreements:', error);
      // Return mock data for development
      return {
        success: true,
        data: [
          {
            id: 'mock-agreement-1',
            bookingId: 'booking-123',
            status: 'sent',
            sentAt: new Date().toISOString(),
            metadata: {
              customerName: 'John Doe',
              vehicleName: '2023 Honda Civic',
              rentalPeriod: '01/20/2025 - 01/25/2025',
              totalAmount: 225.00
            }
          }
        ]
      };
    }
  }

  // Vehicle Management APIs

  /**
   * Get provider vehicles
   */
  static async getProviderVehicles(page: number = 1, limit: number = 20): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/vehicles?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching provider vehicles:', error);
      throw error;
    }
  }

  /**
   * Get vehicle suggestions from catalog
   */
  static async getVehicleSuggestions(query: string = '', limit: number = 20): Promise<any> {
    try {
      const queryParams = new URLSearchParams();
      if (query) queryParams.append('query', query);
      queryParams.append('limit', limit.toString());

      const response = await fetch(`${this.baseUrl}/api/providers/vehicle-suggestions?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching vehicle suggestions:', error);
      throw error;
    }
  }

  /**
   * Create new vehicle
   */
  static async createVehicle(vehicleData: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/vehicles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        },
        body: JSON.stringify(vehicleData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating vehicle:', error);
      throw error;
    }
  }

  // Booking Management APIs

  /**
   * Get provider bookings
   */
  static async getProviderBookings(page: number = 1, limit: number = 20, status?: string): Promise<any> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (status) {
        queryParams.append('status', status);
      }

      const response = await fetch(`${this.baseUrl}/api/providers/bookings?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching provider bookings:', error);
      throw error;
    }
  }

  /**
   * Get booking by ID
   */
  static async getBookingById(bookingId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/${bookingId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching booking:', error);
      throw error;
    }
  }

  /**
   * Accept booking
   */
  static async acceptBooking(bookingId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/${bookingId}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error accepting booking:', error);
      throw error;
    }
  }

  /**
   * Reject booking
   */
  static async rejectBooking(bookingId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/${bookingId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error rejecting booking:', error);
      throw error;
    }
  }

  /**
   * Complete booking
   */
  static async completeBooking(bookingId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/${bookingId}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error completing booking:', error);
      throw error;
    }
  }

  /**
   * Update booking status
   */
  static async updateBookingStatus(bookingId: string, status: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/${bookingId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  /**
   * Get booking statistics
   */
  static async getBookingStats(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/stats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************' // TODO: Replace with actual auth token
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      throw error;
    }
  }

  // Utility Functions

  /**
   * Format currency for display
   */
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(dateObj);
  }

  /**
   * Format date and time for display
   */
  static formatDateTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(dateObj);
  }

  /**
   * Get status color for booking status
   */
  static getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return '#ff9800'; // Orange
      case 'confirmed':
        return '#2196f3'; // Blue
      case 'active':
        return '#4caf50'; // Green
      case 'completed':
        return '#4caf50'; // Green
      case 'cancelled':
        return '#f44336'; // Red
      default:
        return '#757575'; // Gray
    }
  }

  // Booking Management APIs

  /**
   * Get active bookings
   */
  static async getActiveBookings(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/bookings/active`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching active bookings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get pickup/dropoff events
   */
  static async getPickupDropoffEvents(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/pickup-dropoff-events`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching pickup/dropoff events:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Confirm pickup or dropoff
   */
  static async confirmPickupDropoff(eventData: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/confirm-pickup-dropoff`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        },
        body: JSON.stringify(eventData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error confirming pickup/dropoff:', error);
      return { success: false, error: error.message };
    }
  }

  // Messaging APIs

  /**
   * Get message threads
   */
  static async getMessageThreads(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/message-threads`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching message threads:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get extension requests
   */
  static async getExtensionRequests(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/extension-requests`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching extension requests:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Respond to extension request
   */
  static async respondToExtensionRequest(extensionId: string, approved: boolean): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/extension-requests/${extensionId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        },
        body: JSON.stringify({ approved })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error responding to extension request:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get agreements
   */
  static async getAgreements(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/providers/agreements`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': '550e8400-e29b-41d4-a716-************'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching agreements:', error);
      return { success: false, error: error.message };
    }
  }


}

export default ProviderService;
