import * as types from '../types'
import axiosInstance from './axiosInstance'
import * as UserService from './UserService'

/**
 * Get all locations
 */
export const getLocations = (): Promise<types.ApiResponse<types.Location[]>> =>
  axiosInstance
    .get(`/locations/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Get location by ID
 */
export const getLocation = (locationId: string): Promise<types.ApiResponse<types.Location>> =>
  axiosInstance
    .get(`/locations/${locationId}/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Get locations by city
 */
export const getLocationsByCity = (city: string): Promise<types.ApiResponse<types.Location[]>> =>
  axiosInstance
    .get(`/locations/city/${city}/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Get pickup locations
 */
export const getPickupLocations = (): Promise<types.ApiResponse<types.Location[]>> =>
  axiosInstance
    .get(`/locations/pickup/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Get dropoff locations
 */
export const getDropoffLocations = (): Promise<types.ApiResponse<types.Location[]>> =>
  axiosInstance
    .get(`/locations/dropoff/${UserService.getLanguage()}`)
    .then((res) => res.data)

/**
 * Search locations by coordinates
 */
export const searchLocationsByCoordinates = (
  latitude: number,
  longitude: number,
  radius: number = 10
): Promise<types.ApiResponse<types.Location[]>> =>
  axiosInstance
    .get('/locations/search', {
      params: { latitude, longitude, radius }
    })
    .then((res) => res.data)

/**
 * Create new location (admin only)
 */
export const createLocation = (
  locationData: Omit<types.Location, 'id'>
): Promise<types.ApiResponse<types.Location>> =>
  axiosInstance
    .post('/locations', locationData)
    .then((res) => res.data)

/**
 * Update location (admin only)
 */
export const updateLocation = (
  locationId: string,
  locationData: Partial<types.Location>
): Promise<types.ApiResponse<types.Location>> =>
  axiosInstance
    .put(`/locations/${locationId}`, locationData)
    .then((res) => res.data)

/**
 * Delete location (admin only)
 */
export const deleteLocation = (locationId: string): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .delete(`/locations/${locationId}`)
    .then((res) => res.data)

/**
 * Get distance between two locations
 */
export const getDistance = (
  fromLocationId: string,
  toLocationId: string
): Promise<types.ApiResponse<{ distance: number; duration: number; route?: any }>> =>
  axiosInstance
    .get(`/locations/distance/${fromLocationId}/${toLocationId}`)
    .then((res) => res.data)

/**
 * Calculate delivery fee based on distance
 */
export const calculateDeliveryFee = (
  fromLocationId: string,
  toAddress: string
): Promise<types.ApiResponse<{ fee: number; distance: number }>> =>
  axiosInstance
    .post('/locations/delivery-fee', { fromLocationId, toAddress })
    .then((res) => res.data)

export default {
  getLocations,
  getLocation,
  getLocationsByCity,
  getPickupLocations,
  getDropoffLocations,
  searchLocationsByCoordinates,
  createLocation,
  updateLocation,
  deleteLocation,
  getDistance,
  calculateDeliveryFee
}
