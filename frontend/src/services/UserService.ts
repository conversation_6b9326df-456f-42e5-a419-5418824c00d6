import * as types from '../types'
import axiosInstance from './axiosInstance'

/**
 * Get current language from localStorage
 */
export const getLanguage = (): string => {
  return localStorage.getItem('language') || 'en'
}

/**
 * Set language in localStorage
 */
export const setLanguage = (language: string): void => {
  localStorage.setItem('language', language)
}

/**
 * Get current user from localStorage
 */
export const getCurrentUser = (): types.User | null => {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
}

/**
 * Set current user in localStorage
 */
export const setCurrentUser = (user: types.User): void => {
  localStorage.setItem('user', JSON.stringify(user))
}

/**
 * Clear current user from localStorage
 */
export const clearCurrentUser = (): void => {
  localStorage.removeItem('user')
  localStorage.removeItem('auth-token')
}

/**
 * Register a new user
 */
export const registerUser = (userData: {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  type?: types.UserType
}): Promise<types.ApiResponse<{ user: types.User; token: string }>> =>
  axiosInstance
    .post('/users/register', userData)
    .then((res) => res.data)

/**
 * Sign in user
 */
export const signIn = (credentials: {
  email: string
  password: string
}): Promise<types.ApiResponse<{ user: types.User; token: string }>> =>
  axiosInstance
    .post('/users/login', credentials)
    .then((res) => {
      if (res.data.success && res.data.data?.token) {
        localStorage.setItem('auth-token', res.data.data.token)
        setCurrentUser(res.data.data.user)
      }
      return res.data
    })

/**
 * Sign out user
 */
export const signOut = (): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/logout')
    .then((res) => {
      clearCurrentUser()
      return res.data
    })

/**
 * Get user profile
 */
export const getProfile = (userId: string): Promise<types.ApiResponse<types.User>> =>
  axiosInstance
    .get(`/users/${userId}`)
    .then((res) => res.data)

/**
 * Update user profile
 */
export const updateProfile = (
  userId: string,
  userData: Partial<types.User>
): Promise<types.ApiResponse<types.User>> =>
  axiosInstance
    .put(`/users/${userId}`, userData)
    .then((res) => res.data)

/**
 * Upload user avatar
 */
export const uploadAvatar = (
  userId: string,
  formData: FormData
): Promise<types.ApiResponse<{ avatarUrl: string }>> =>
  axiosInstance
    .post(`/users/${userId}/avatar`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    .then((res) => res.data)

/**
 * Upload user document
 */
export const uploadDocument = (
  userId: string,
  documentType: string,
  formData: FormData
): Promise<types.ApiResponse<any>> =>
  axiosInstance
    .post(`/users/${userId}/documents/${documentType}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    .then((res) => res.data)

/**
 * Get user documents
 */
export const getDocuments = (userId: string): Promise<types.ApiResponse<any[]>> =>
  axiosInstance
    .get(`/users/${userId}/documents`)
    .then((res) => res.data)

/**
 * Request email verification
 */
export const requestEmailVerification = (): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/verify-email/request')
    .then((res) => res.data)

/**
 * Verify email with token
 */
export const verifyEmail = (token: string): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/verify-email', { token })
    .then((res) => res.data)

/**
 * Request password reset
 */
export const requestPasswordReset = (email: string): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/password-reset/request', { email })
    .then((res) => res.data)

/**
 * Reset password with token
 */
export const resetPassword = (
  token: string,
  newPassword: string
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/password-reset', { token, password: newPassword })
    .then((res) => res.data)

/**
 * Change password (authenticated user)
 */
export const changePassword = (
  currentPassword: string,
  newPassword: string
): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .post('/users/change-password', { currentPassword, newPassword })
    .then((res) => res.data)

/**
 * Delete user account
 */
export const deleteAccount = (userId: string): Promise<types.ApiResponse<void>> =>
  axiosInstance
    .delete(`/users/${userId}`)
    .then((res) => res.data)

/**
 * Complete user onboarding
 */
export const completeOnboarding = (data: any): Promise<types.ApiResponse<types.User>> =>
  axiosInstance
    .post('/users/complete-onboarding', data)
    .then((res) => res.data)

/**
 * Send verification email
 */
export const sendVerificationEmail = (): Promise<types.ApiResponse<{ message: string }>> =>
  axiosInstance
    .post('/users/send-verification-email')
    .then((res) => res.data)

/**
 * Send verification SMS
 */
export const sendVerificationSMS = (phone: string): Promise<types.ApiResponse<{ message: string }>> =>
  axiosInstance
    .post('/users/send-verification-sms', { phone })
    .then((res) => res.data)

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('auth-token')
  const user = getCurrentUser()
  return !!(token && user)
}
