import * as types from '../types'
import axiosInstance from './axiosInstance'

// Format currency utility function
export const formatCurrency = (amount: number, currencyCode: string = 'IDR'): string => {
  try {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    // Fallback for unsupported currencies
    return `${currencyCode} ${amount.toLocaleString()}`;
  }
};

/**
 * Create payment intent for booking
 */
export const createPaymentIntent = (
  bookingId: string,
  paymentMethod: types.PaymentMethod
): Promise<types.ApiResponse<{ 
  paymentIntentId: string
  clientSecret: string
  customerId?: string
}>> =>
  axiosInstance
    .post('/payments/create-intent', { bookingId, paymentMethod })
    .then((res) => res.data)

/**
 * Confirm payment
 */
export const confirmPayment = (
  paymentIntentId: string,
  paymentMethodId?: string
): Promise<types.ApiResponse<{ status: string; bookingId: string }>> =>
  axiosInstance
    .post('/payments/confirm', { paymentIntentId, paymentMethodId })
    .then((res) => res.data)

/**
 * Process refund
 */
export const processRefund = (
  paymentIntentId: string,
  amount?: number,
  reason?: string
): Promise<types.ApiResponse<{ refundId: string; status: string }>> =>
  axiosInstance
    .post('/payments/refund', { paymentIntentId, amount, reason })
    .then((res) => res.data)

/**
 * Get payment status
 */
export const getPaymentStatus = (
  paymentIntentId: string
): Promise<types.ApiResponse<{ status: string; amount: number; currency: string }>> =>
  axiosInstance
    .get(`/payments/${paymentIntentId}/status`)
    .then((res) => res.data)

/**
 * Get payment history for user
 */
export const getPaymentHistory = (
  userId: string,
  page: number = 1,
  size: number = 10
): Promise<types.ApiResponse<types.PaginatedResponse<any>>> =>
  axiosInstance
    .get(`/payments/history/${userId}/${page}/${size}`)
    .then((res) => res.data)

/**
 * Create Stripe customer
 */
export const createStripeCustomer = (
  userEmail: string,
  userDetails: any
): Promise<types.ApiResponse<{ customerId: string }>> =>
  axiosInstance
    .post('/payments/stripe/customer', { userEmail, userDetails })
    .then((res) => res.data)

/**
 * Save payment method for future use
 */
export const savePaymentMethod = (
  customerId: string,
  paymentMethodId: string
): Promise<types.ApiResponse<{ success: boolean }>> =>
  axiosInstance
    .post('/payments/save-method', { customerId, paymentMethodId })
    .then((res) => res.data)

/**
 * Get saved payment methods
 */
export const getSavedPaymentMethods = (
  customerId: string
): Promise<types.ApiResponse<any[]>> =>
  axiosInstance
    .get(`/payments/methods/${customerId}`)
    .then((res) => res.data)

/**
 * Delete saved payment method
 */
export const deletePaymentMethod = (
  paymentMethodId: string
): Promise<types.ApiResponse<{ success: boolean }>> =>
  axiosInstance
    .delete(`/payments/methods/${paymentMethodId}`)
    .then((res) => res.data)

/**
 * Process cash payment confirmation
 */
export const confirmCashPayment = (
  bookingId: string,
  confirmationCode: string
): Promise<types.ApiResponse<{ success: boolean }>> =>
  axiosInstance
    .post('/payments/cash/confirm', { bookingId, confirmationCode })
    .then((res) => res.data)

/**
 * Generate payment receipt
 */
export const generateReceipt = (
  paymentIntentId: string
): Promise<types.ApiResponse<{ receiptUrl: string }>> =>
  axiosInstance
    .get(`/payments/${paymentIntentId}/receipt`)
    .then((res) => res.data)

/**
 * Calculate payment fees
 */
export const calculateFees = (
  amount: number,
  paymentMethod: types.PaymentMethod
): Promise<types.ApiResponse<{
  baseAmount: number
  processingFee: number
  platformFee: number
  total: number
}>> =>
  axiosInstance
    .post('/payments/calculate-fees', { amount, paymentMethod })
    .then((res) => res.data)

/**
 * Validate promo code
 */
export const validatePromoCode = (
  code: string,
  bookingAmount: number
): Promise<types.ApiResponse<{
  valid: boolean
  discount: number
  discountType: 'percentage' | 'fixed'
  message: string
}>> =>
  axiosInstance
    .post('/payments/validate-promo', { code, bookingAmount })
    .then((res) => res.data)

export default {
  formatCurrency,
  createPaymentIntent,
  confirmPayment,
  processRefund,
  getPaymentStatus,
  getPaymentHistory,
  createStripeCustomer,
  savePaymentMethod,
  getSavedPaymentMethods,
  deletePaymentMethod,
  confirmCashPayment,
  generateReceipt,
  calculateFees,
  validatePromoCode
}
