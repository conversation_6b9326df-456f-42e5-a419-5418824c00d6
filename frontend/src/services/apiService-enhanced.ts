import * as types from '../types'
import axiosInstance from './axiosInstance'

class ApiService {
  // Health check method
  async healthCheck(): Promise<{ status: string; message?: string }> {
    try {
      const response = await axiosInstance.get('/health')
      return { status: 'healthy', message: response.data?.message }
    } catch (error) {
      return { status: 'unhealthy', message: 'Backend server not available' }
    }
  }

  // User-related methods
  async registerUser(userData: Partial<types.User>): Promise<types.ApiResponse<{ user: types.User; token: string }>> {
    const response = await axiosInstance.post('/users/register', userData)
    return response.data
  }

  async loginUser(credentials: { email: string; password: string }): Promise<types.ApiResponse<{ user: types.User; token: string }>> {
    const response = await axiosInstance.post('/users/login', credentials)
    if (response.data.success && response.data.data?.token) {
      localStorage.setItem('auth-token', response.data.data.token)
      localStorage.setItem('user', JSON.stringify(response.data.data.user))
    }
    return response.data
  }

  // Vehicle-related methods
  async searchVehicles(searchParams: types.SearchFilters): Promise<types.ApiResponse<types.Vehicle[]>> {
    const response = await axiosInstance.get('/vehicles/search', { params: searchParams })
    return response.data
  }

  async getVehicleById(vehicleId: string): Promise<types.ApiResponse<types.Vehicle>> {
    const response = await axiosInstance.get(`/vehicles/${vehicleId}`)
    return response.data
  }

  // Enhanced booking methods with better error handling
  async createBooking(bookingData: types.CreateBookingPayload): Promise<types.ApiResponse<types.Booking>> {
    try {
      const response = await axiosInstance.post('/bookings', bookingData)
      return response.data
    } catch (error: any) {
      // Handle specific booking errors
      if (error.response?.status === 409) {
        return {
          success: false,
          message: 'Vehicle not available for selected dates',
          error: 'VEHICLE_UNAVAILABLE'
        }
      }
      throw error
    }
  }

  // Enhanced availability check with caching
  private availabilityCache = new Map<string, { data: any; timestamp: number }>()
  private CACHE_DURATION = 30000 // 30 seconds

  async checkVehicleAvailability(params: { 
    vehicleId: string
    startDate: string
    endDate: string 
  }): Promise<types.ApiResponse<{ available: boolean; message?: string }>> {
    const cacheKey = `${params.vehicleId}-${params.startDate}-${params.endDate}`
    const cached = this.availabilityCache.get(cacheKey)
    
    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data
    }

    try {
      const response = await axiosInstance.get('/bookings/availability', { params })
      const result = response.data
      
      // Cache the result
      this.availabilityCache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })
      
      return result
    } catch (error: any) {
      // Fallback: assume unavailable if we can't check
      return {
        success: true,
        data: { available: false, message: 'Unable to verify availability' }
      }
    }
  }

  async getUserBookings(userId: string): Promise<types.ApiResponse<types.Booking[]>> {
    const response = await axiosInstance.get(`/bookings/user/${userId}`)
    return response.data
  }
}

export default new ApiService()
