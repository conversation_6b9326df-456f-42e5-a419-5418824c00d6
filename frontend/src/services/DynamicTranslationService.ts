/**
 * Dynamic Translation Service - Google Translate-like functionality
 * Automatically translates any text content without requiring predefined translation keys
 */

import i18n from '../i18n';

interface TranslationCache {
  [key: string]: {
    [lang: string]: string;
  };
}

interface GoogleTranslateResponse {
  data: {
    translations: Array<{
      translatedText: string;
      detectedSourceLanguage?: string;
    }>;
  };
}

class DynamicTranslationService {
  private cache: TranslationCache = {};
  private apiKey: string = import.meta.env.VITE_GOOGLE_TRANSLATE_API_KEY || '';
  private fallbackTranslations: { [key: string]: { [lang: string]: string } } = {};

  constructor() {
    // Load cached translations from localStorage
    this.loadCacheFromStorage();
    
    // Set up fallback translations for common terms
    this.setupFallbackTranslations();
  }

  /**
   * Main translation function - works like Google Translate
   * Automatically translates any text to the current language
   */
  async translate(text: string, targetLang?: string): Promise<string> {
    if (!text || text.trim() === '') return text;

    const currentLang = targetLang || i18n.language || 'en';
    
    // If already in target language, return as-is
    if (currentLang === 'en') return text;

    // Check cache first
    const cacheKey = this.getCacheKey(text);
    if (this.cache[cacheKey]?.[currentLang]) {
      return this.cache[cacheKey][currentLang];
    }

    // Check fallback translations
    const fallbackResult = this.getFallbackTranslation(text, currentLang);
    if (fallbackResult) {
      this.cacheTranslation(text, currentLang, fallbackResult);
      return fallbackResult;
    }

    // Use Google Translate API if available
    if (this.apiKey) {
      try {
        const translated = await this.googleTranslate(text, currentLang);
        this.cacheTranslation(text, currentLang, translated);
        return translated;
      } catch (error) {
        console.warn('Google Translate failed, using fallback:', error);
      }
    }

    // Fallback: return original text
    return text;
  }

  /**
   * Batch translate multiple texts efficiently
   */
  async translateBatch(texts: string[], targetLang?: string): Promise<string[]> {
    const currentLang = targetLang || i18n.language || 'en';
    
    if (currentLang === 'en') return texts;

    const results: string[] = [];
    const textsToTranslate: { index: number; text: string }[] = [];

    // Check cache for each text
    texts.forEach((text, index) => {
      const cacheKey = this.getCacheKey(text);
      if (this.cache[cacheKey]?.[currentLang]) {
        results[index] = this.cache[cacheKey][currentLang];
      } else {
        const fallback = this.getFallbackTranslation(text, currentLang);
        if (fallback) {
          results[index] = fallback;
          this.cacheTranslation(text, currentLang, fallback);
        } else {
          textsToTranslate.push({ index, text });
        }
      }
    });

    // Translate remaining texts
    if (textsToTranslate.length > 0 && this.apiKey) {
      try {
        const translations = await this.googleTranslateBatch(
          textsToTranslate.map(item => item.text),
          currentLang
        );

        textsToTranslate.forEach((item, i) => {
          const translated = translations[i] || item.text;
          results[item.index] = translated;
          this.cacheTranslation(item.text, currentLang, translated);
        });
      } catch (error) {
        console.warn('Batch translation failed:', error);
        // Fill remaining with original texts
        textsToTranslate.forEach(item => {
          results[item.index] = item.text;
        });
      }
    } else {
      // Fill remaining with original texts
      textsToTranslate.forEach(item => {
        results[item.index] = item.text;
      });
    }

    return results;
  }

  /**
   * React hook for automatic translation
   */
  useTranslate() {
    return {
      t: (text: string) => this.translate(text),
      tBatch: (texts: string[]) => this.translateBatch(texts),
      currentLang: i18n.language
    };
  }

  /**
   * Google Translate API integration
   */
  private async googleTranslate(text: string, targetLang: string): Promise<string> {
    const response = await fetch(
      `https://translation.googleapis.com/language/translate/v2?key=${this.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          target: targetLang,
          source: 'en',
          format: 'text'
        })
      }
    );

    if (!response.ok) {
      throw new Error(`Translation API error: ${response.status}`);
    }

    const data: GoogleTranslateResponse = await response.json();
    return data.data.translations[0]?.translatedText || text;
  }

  /**
   * Batch Google Translate API call
   */
  private async googleTranslateBatch(texts: string[], targetLang: string): Promise<string[]> {
    const response = await fetch(
      `https://translation.googleapis.com/language/translate/v2?key=${this.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: texts,
          target: targetLang,
          source: 'en',
          format: 'text'
        })
      }
    );

    if (!response.ok) {
      throw new Error(`Batch translation API error: ${response.status}`);
    }

    const data: GoogleTranslateResponse = await response.json();
    return data.data.translations.map(t => t.translatedText);
  }

  /**
   * Setup common fallback translations
   */
  private setupFallbackTranslations() {
    this.fallbackTranslations = {
      // Navigation
      'Home': { id: 'Beranda', zh: '首页', ar: 'الرئيسية', es: 'Inicio', fr: 'Accueil', de: 'Startseite', ja: 'ホーム', ko: '홈', pt: 'Início', ru: 'Главная' },
      'Vehicles': { id: 'Kendaraan', zh: '车辆', ar: 'المركبات', es: 'Vehículos', fr: 'Véhicules', de: 'Fahrzeuge', ja: '車両', ko: '차량', pt: 'Veículos', ru: 'Транспорт' },
      'Search': { id: 'Cari', zh: '搜索', ar: 'بحث', es: 'Buscar', fr: 'Rechercher', de: 'Suchen', ja: '検索', ko: '검색', pt: 'Pesquisar', ru: 'Поиск' },
      'Location': { id: 'Lokasi', zh: '位置', ar: 'الموقع', es: 'Ubicación', fr: 'Emplacement', de: 'Standort', ja: '場所', ko: '위치', pt: 'Localização', ru: 'Местоположение' },
      
      // Vehicle types
      'Scooter': { id: 'Skuter', zh: '踏板车', ar: 'سكوتر', es: 'Scooter', fr: 'Scooter', de: 'Roller', ja: 'スクーター', ko: '스쿠터', pt: 'Scooter', ru: 'Скутер' },
      'Motorcycle': { id: 'Motor', zh: '摩托车', ar: 'دراجة نارية', es: 'Motocicleta', fr: 'Moto', de: 'Motorrad', ja: 'オートバイ', ko: '오토바이', pt: 'Motocicleta', ru: 'Мотоцикл' },
      'Electric': { id: 'Listrik', zh: '电动', ar: 'كهربائي', es: 'Eléctrico', fr: 'Électrique', de: 'Elektrisch', ja: '電気', ko: '전기', pt: 'Elétrico', ru: 'Электрический' },
      
      // Common actions
      'Book Now': { id: 'Pesan Sekarang', zh: '立即预订', ar: 'احجز الآن', es: 'Reservar Ahora', fr: 'Réserver Maintenant', de: 'Jetzt Buchen', ja: '今すぐ予約', ko: '지금 예약', pt: 'Reservar Agora', ru: 'Забронировать' },
      'View Details': { id: 'Lihat Detail', zh: '查看详情', ar: 'عرض التفاصيل', es: 'Ver Detalles', fr: 'Voir Détails', de: 'Details Anzeigen', ja: '詳細を見る', ko: '세부사항 보기', pt: 'Ver Detalhes', ru: 'Подробности' },
      
      // Time periods
      'per day': { id: 'per hari', zh: '每天', ar: 'في اليوم', es: 'por día', fr: 'par jour', de: 'pro Tag', ja: '1日あたり', ko: '하루당', pt: 'por dia', ru: 'в день' },
      'per week': { id: 'per minggu', zh: '每周', ar: 'في الأسبوع', es: 'por semana', fr: 'par semaine', de: 'pro Woche', ja: '週あたり', ko: '주당', pt: 'por semana', ru: 'в неделю' },
      'per month': { id: 'per bulan', zh: '每月', ar: 'في الشهر', es: 'por mes', fr: 'par mois', de: 'pro Monat', ja: '月あたり', ko: '월당', pt: 'por mês', ru: 'в месяц' }
    };
  }

  /**
   * Get fallback translation if available
   */
  private getFallbackTranslation(text: string, targetLang: string): string | null {
    return this.fallbackTranslations[text]?.[targetLang] || null;
  }

  /**
   * Cache management
   */
  private getCacheKey(text: string): string {
    return text.toLowerCase().trim();
  }

  private cacheTranslation(text: string, lang: string, translation: string) {
    const cacheKey = this.getCacheKey(text);
    if (!this.cache[cacheKey]) {
      this.cache[cacheKey] = {};
    }
    this.cache[cacheKey][lang] = translation;
    this.saveCacheToStorage();
  }

  private loadCacheFromStorage() {
    try {
      const cached = localStorage.getItem('rentahub-translation-cache');
      if (cached) {
        this.cache = JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Failed to load translation cache:', error);
    }
  }

  private saveCacheToStorage() {
    try {
      localStorage.setItem('rentahub-translation-cache', JSON.stringify(this.cache));
    } catch (error) {
      console.warn('Failed to save translation cache:', error);
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache() {
    this.cache = {};
    localStorage.removeItem('rentahub-translation-cache');
  }
}

export default new DynamicTranslationService();
