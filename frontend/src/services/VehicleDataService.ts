import { Vehicle, VehicleCategory, VehicleType, VehicleAddOn } from '../types';

export interface VehicleFilter {
  category?: VehicleCategory;
  type?: VehicleType;
  brand?: string;
  model?: string;
  minPrice?: number;
  maxPrice?: number;
  available?: boolean;
  location?: string;
  features?: string[];
  transmission?: 'automatic' | 'manual';
  fuelType?: 'petrol' | 'electric' | 'hybrid';
  engineSize?: {
    min: number;
    max: number;
  };
  deliveryAvailable?: boolean;
  insuranceOffered?: boolean;
}

export interface VehicleSearchResult {
  vehicles: Vehicle[];
  total: number;
  filters: VehicleFilter;
  suggestions: string[];
}

export interface VehicleStats {
  totalVehicles: number;
  availableVehicles: number;
  totalBookings: number;
  averageRating: number;
  totalRevenue: number;
  popularCategories: Array<{
    category: VehicleCategory;
    count: number;
    revenue: number;
  }>;
  topVehicles: Array<{
    vehicle: Vehicle;
    bookings: number;
    revenue: number;
    rating: number;
  }>;
}

export interface RecurringBooking {
  id: string;
  vehicleId: string;
  userId: string;
  pattern: 'daily' | 'weekly' | 'monthly';
  interval: number;
  startDate: string;
  endDate: string;
  daysOfWeek?: number[];
  dayOfMonth?: number;
  totalAmount: number;
  status: 'active' | 'paused' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

export class VehicleDataService {
  private static instance: VehicleDataService;
  private vehicles: Vehicle[] = [];
  private addOns: VehicleAddOn[] = [];

  private constructor() {
    this.initializeAddOns();
  }

  public static getInstance(): VehicleDataService {
    if (!VehicleDataService.instance) {
      VehicleDataService.instance = new VehicleDataService();
    }
    return VehicleDataService.instance;
  }

  private initializeAddOns() {
    this.addOns = [
      {
        id: 'helmet',
        name: 'Helmet',
        description: 'Safety helmet included',
        price: 5,
        type: 'equipment',
        available: true,
        dailyRate: 5
      },
      {
        id: 'raincoat',
        name: 'Raincoat',
        description: 'Waterproof raincoat',
        price: 3,
        type: 'equipment',
        available: true,
        dailyRate: 3
      },
      {
        id: 'gps',
        name: 'GPS Navigation',
        description: 'GPS device for navigation',
        price: 10,
        type: 'equipment',
        available: true,
        dailyRate: 10
      },
      {
        id: 'insurance_basic',
        name: 'Basic Insurance',
        description: 'Basic coverage for damages',
        price: 15,
        type: 'insurance',
        available: true,
        dailyRate: 15
      },
      {
        id: 'insurance_premium',
        name: 'Premium Insurance',
        description: 'Comprehensive coverage',
        price: 25,
        type: 'insurance',
        available: true,
        dailyRate: 25
      },
      {
        id: 'delivery',
        name: 'Delivery Service',
        description: 'Vehicle delivered to your location',
        price: 20,
        type: 'service',
        available: true,
        dailyRate: 20
      },
      {
        id: 'child_seat',
        name: 'Child Seat',
        description: 'Child safety seat',
        price: 8,
        type: 'equipment',
        available: true,
        dailyRate: 8
      },
      {
        id: 'phone_mount',
        name: 'Phone Mount',
        description: 'Universal phone mount',
        price: 2,
        type: 'equipment',
        available: true,
        dailyRate: 2
      }
    ];
  }

  public async fetchVehicles(filters?: VehicleFilter): Promise<VehicleSearchResult> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (typeof value === 'object') {
              queryParams.append(key, JSON.stringify(value));
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });
      }

      // Use /api/vehicles/search for filtered results, /api/vehicles for all
      const endpoint = filters && Object.keys(filters).length > 0 ? '/api/vehicles/search' : '/api/vehicles';
      const response = await fetch(`${endpoint}?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch vehicles');
      }

      const data = await response.json();
      this.vehicles = data.data || [];
      
      return {
        vehicles: this.vehicles,
        total: data.count || this.vehicles.length,
        filters: filters || {},
        suggestions: data.suggestions || []
      };
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      return {
        vehicles: [],
        total: 0,
        filters: filters || {},
        suggestions: []
      };
    }
  }

  public async fetchVehicleById(id: string): Promise<Vehicle | null> {
    try {
      const response = await fetch(`/api/vehicles/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch vehicle');
      }

      const data = await response.json();
      return data.data || null;
    } catch (error) {
      console.error('Error fetching vehicle:', error);
      return null;
    }
  }

  public async createVehicle(vehicleData: Partial<Vehicle>): Promise<Vehicle | null> {
    try {
      const response = await fetch('/api/vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(vehicleData),
      });

      if (!response.ok) {
        throw new Error('Failed to create vehicle');
      }

      const vehicle = await response.json();
      this.vehicles.push(vehicle);
      return vehicle;
    } catch (error) {
      console.error('Error creating vehicle:', error);
      return null;
    }
  }

  public async updateVehicle(id: string, vehicleData: Partial<Vehicle>): Promise<Vehicle | null> {
    try {
      const response = await fetch(`/api/vehicles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(vehicleData),
      });

      if (!response.ok) {
        throw new Error('Failed to update vehicle');
      }

      const vehicle = await response.json();
      const index = this.vehicles.findIndex(v => v.id === id);
      if (index !== -1) {
        this.vehicles[index] = vehicle;
      }
      return vehicle;
    } catch (error) {
      console.error('Error updating vehicle:', error);
      return null;
    }
  }

  public async deleteVehicle(id: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/vehicles/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete vehicle');
      }

      this.vehicles = this.vehicles.filter(v => v.id !== id);
      return true;
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      return false;
    }
  }

  public async getVehicleStats(): Promise<VehicleStats> {
    try {
      const response = await fetch('/api/vehicles/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch vehicle stats');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching vehicle stats:', error);
      return {
        totalVehicles: 0,
        availableVehicles: 0,
        totalBookings: 0,
        averageRating: 0,
        totalRevenue: 0,
        popularCategories: [],
        topVehicles: []
      };
    }
  }

  public async getVehicleAvailability(vehicleId: string, startDate: string, endDate: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/vehicles/${vehicleId}/availability?startDate=${startDate}&endDate=${endDate}`);
      
      if (!response.ok) {
        throw new Error('Failed to check vehicle availability');
      }

      const data = await response.json();
      return data.available;
    } catch (error) {
      console.error('Error checking vehicle availability:', error);
      return false;
    }
  }

  public async getVehicleCalendar(vehicleId: string, month: number, year: number): Promise<any> {
    try {
      const response = await fetch(`/api/vehicles/${vehicleId}/calendar?month=${month}&year=${year}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch vehicle calendar');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching vehicle calendar:', error);
      return {};
    }
  }

  public async createRecurringBooking(bookingData: Partial<RecurringBooking>): Promise<RecurringBooking | null> {
    try {
      const response = await fetch('/api/recurring-bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (!response.ok) {
        throw new Error('Failed to create recurring booking');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating recurring booking:', error);
      return null;
    }
  }

  public async getRecurringBookings(vehicleId?: string): Promise<RecurringBooking[]> {
    try {
      const url = vehicleId ? `/api/recurring-bookings?vehicleId=${vehicleId}` : '/api/recurring-bookings';
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch recurring bookings');
      }

      const data = await response.json();
      return data.recurringBookings || [];
    } catch (error) {
      console.error('Error fetching recurring bookings:', error);
      return [];
    }
  }

  public async updateRecurringBooking(id: string, bookingData: Partial<RecurringBooking>): Promise<RecurringBooking | null> {
    try {
      const response = await fetch(`/api/recurring-bookings/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (!response.ok) {
        throw new Error('Failed to update recurring booking');
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating recurring booking:', error);
      return null;
    }
  }

  public async deleteRecurringBooking(id: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/recurring-bookings/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete recurring booking');
      }

      return true;
    } catch (error) {
      console.error('Error deleting recurring booking:', error);
      return false;
    }
  }

  public getAddOns(): VehicleAddOn[] {
    return this.addOns;
  }

  public getAddOnById(id: string): VehicleAddOn | undefined {
    return this.addOns.find(addon => addon.id === id);
  }

  public async createAddOn(addonData: Partial<VehicleAddOn>): Promise<VehicleAddOn | null> {
    try {
      const response = await fetch('/api/addons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(addonData),
      });

      if (!response.ok) {
        throw new Error('Failed to create addon');
      }

      const addon = await response.json();
      this.addOns.push(addon);
      return addon;
    } catch (error) {
      console.error('Error creating addon:', error);
      return null;
    }
  }

  public async updateAddOn(id: string, addonData: Partial<VehicleAddOn>): Promise<VehicleAddOn | null> {
    try {
      const response = await fetch(`/api/addons/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(addonData),
      });

      if (!response.ok) {
        throw new Error('Failed to update addon');
      }

      const addon = await response.json();
      const index = this.addOns.findIndex(a => a.id === id);
      if (index !== -1) {
        this.addOns[index] = addon;
      }
      return addon;
    } catch (error) {
      console.error('Error updating addon:', error);
      return null;
    }
  }

  public async deleteAddOn(id: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/addons/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete addon');
      }

      this.addOns = this.addOns.filter(a => a.id !== id);
      return true;
    } catch (error) {
      console.error('Error deleting addon:', error);
      return false;
    }
  }

  public async exportVehicleData(filters?: VehicleFilter): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (typeof value === 'object') {
              queryParams.append(key, JSON.stringify(value));
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });
      }

      const response = await fetch(`/api/vehicles/export?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to export vehicle data');
      }

      return await response.blob();
    } catch (error) {
      console.error('Error exporting vehicle data:', error);
      throw error;
    }
  }

  public async importVehicleData(file: File): Promise<{ success: number; failed: number }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/vehicles/import', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to import vehicle data');
      }

      return await response.json();
    } catch (error) {
      console.error('Error importing vehicle data:', error);
      return { success: 0, failed: 1 };
    }
  }

  public getVehicleCategories(): Array<{ value: VehicleCategory; label: string; description: string }> {
    return [
      { value: VehicleCategory.SmallScooter, label: 'Small Scooter', description: '50-125cc scooters' },
      { value: VehicleCategory.MediumScooter, label: 'Medium Scooter', description: '125-250cc scooters' },
      { value: VehicleCategory.LargeScooter, label: 'Large Scooter', description: '250cc+ scooters' },
      { value: VehicleCategory.ElectricScooter, label: 'Electric Scooter', description: 'Electric scooters' },
      { value: VehicleCategory.Motorcycle, label: 'Motorcycle', description: 'Motorcycles' },
      { value: VehicleCategory.ElectricMotorcycle, label: 'Electric Motorcycle', description: 'Electric motorcycles' },
      { value: VehicleCategory.Car, label: 'Car', description: 'Cars and sedans' },
      { value: VehicleCategory.Truck, label: 'Truck', description: 'Trucks and pickups' },
      { value: VehicleCategory.Van, label: 'Van', description: 'Vans and minivans' }
    ];
  }

  public getVehicleTypes(): Array<{ value: VehicleType; label: string; description: string }> {
    return [
      { value: VehicleType.Scooter, label: 'Scooter', description: 'Automatic scooters' },
      { value: VehicleType.Motorcycle, label: 'Motorcycle', description: 'Manual motorcycles' },
      { value: VehicleType.Car, label: 'Car', description: 'Cars and sedans' },
      { value: VehicleType.Truck, label: 'Truck', description: 'Trucks and pickups' },
      { value: VehicleType.Van, label: 'Van', description: 'Vans and minivans' },
      { value: VehicleType.ElectricScooter, label: 'Electric Scooter', description: 'Electric scooters' },
      { value: VehicleType.ElectricMotorcycle, label: 'Electric Motorcycle', description: 'Electric motorcycles' }
    ];
  }

  public getPopularBrands(): string[] {
    return [
      'Honda', 'Yamaha', 'Suzuki', 'Kawasaki', 'Vespa', 'Piaggio',
      'BMW', 'Ducati', 'Harley-Davidson', 'KTM', 'Triumph',
      'Toyota', 'Honda', 'Nissan', 'Mazda', 'Subaru',
      'Ford', 'Chevrolet', 'Dodge', 'Jeep', 'GMC'
    ];
  }

  public getPopularModels(brand: string): string[] {
    const modelsByBrand: Record<string, string[]> = {
      'Honda': ['Vario', 'PCX', 'ADV', 'CB', 'CBR', 'CRF', 'Civic', 'Accord', 'CR-V', 'Pilot'],
      'Yamaha': ['NMAX', 'XSR', 'MT', 'YZF', 'TMAX'],
      'Suzuki': ['GSX', 'V-Strom', 'Burgman', 'Address'],
      'Kawasaki': ['Ninja', 'Z', 'Versys', 'Vulcan'],
      'Vespa': ['Primavera', 'GTS', 'Sprint', 'LX'],
      'Piaggio': ['Liberty', 'Beverly', 'X10', 'MP3'],
      'Toyota': ['Camry', 'Corolla', 'RAV4', 'Highlander'],
      'Nissan': ['Altima', 'Sentra', 'Rogue', 'Pathfinder']
    };

    return modelsByBrand[brand] || [];
  }

  public async getVehicleAutocomplete(query: string): Promise<Array<{ value: string; label: string; make: string; model: string; category: string; image: string }>> {
    try {
      const response = await fetch(`/api/vehicles/autocomplete?query=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch autocomplete suggestions');
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching autocomplete suggestions:', error);
      return [];
    }
  }

  public calculateVehiclePrice(vehicle: Vehicle, days: number, addOns: string[] = []): {
    basePrice: number;
    addOnsPrice: number;
    totalPrice: number;
    breakdown: Array<{ item: string; price: number; type: 'base' | 'addon' }>;
  } {
    const basePrice = (vehicle.dailyRate || 0) * days;
    const selectedAddOns = this.addOns.filter(addon => addOns.includes(addon.id));
    const addOnsPrice = selectedAddOns.reduce((total, addon) => {
      return total + ((addon.dailyRate || 0) * days);
    }, 0);

    const breakdown = [
      { item: `${vehicle.name} (${days} days)`, price: basePrice, type: 'base' as const }
    ];

    selectedAddOns.forEach(addon => {
      breakdown.push({
        item: `${addon.name} (${days} days)`,
        price: (addon.dailyRate || 0) * days,
        type: 'addon' as const
      });
    });

    return {
      basePrice,
      addOnsPrice,
      totalPrice: basePrice + addOnsPrice,
      breakdown
    };
  }
}

export default VehicleDataService.getInstance(); 