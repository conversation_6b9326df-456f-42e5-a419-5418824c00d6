import axiosInstance from './axiosInstance';

export interface CategoryPriceRange {
  min: number;
  max: number;
  count: number;
  avgPrice: number;
}

export interface PriceRangesResponse {
  small_scooter: CategoryPriceRange;
  large_scooter: CategoryPriceRange;
  luxury_bike: CategoryPriceRange;
}

/**
 * Fetch dynamic price ranges by vehicle category
 */
export const getPriceRangesByCategory = async (): Promise<PriceRangesResponse> => {
  try {
    console.log('📊 Fetching dynamic price ranges...');
    
    const response = await axiosInstance.get('/vehicles/price-ranges');
    
    if (response.data.success) {
      console.log('✅ Price ranges fetched:', response.data.data);
      return response.data.data;
    } else {
      throw new Error('Failed to fetch price ranges');
    }
  } catch (error) {
    console.error('❌ Error fetching price ranges:', error);
    
    // Fallback to static ranges if API fails
    return {
      small_scooter: { min: 50000, max: 80000, count: 0, avgPrice: 65000 },
      large_scooter: { min: 80000, max: 120000, count: 0, avgPrice: 100000 },
      luxury_bike: { min: 200000, max: 350000, count: 0, avgPrice: 275000 }
    };
  }
};

/**
 * Format price range for display
 */
export const formatPriceRange = (range: CategoryPriceRange): string => {
  const formatPrice = (amount: number): string => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `${Math.round(amount / 1000)}k`;
    }
    return amount.toString();
  };

  return `Rp ${formatPrice(range.min)}-${formatPrice(range.max)}/day`;
};

/**
 * Get category statistics text
 */
export const getCategoryStats = (range: CategoryPriceRange): string => {
  if (range.count === 0) {
    return 'No vehicles available';
  }
  
  const formatPrice = (amount: number): string => {
    return new Intl.NumberFormat('id-ID').format(amount);
  };
  
  return `${range.count} vehicles • Avg: Rp ${formatPrice(range.avgPrice)}/day`;
};
