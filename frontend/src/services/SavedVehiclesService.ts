import axios from 'axios';

export interface SavedVehicle {
  id: string;
  userId: string;
  vehicleId: string;
  createdAt: string;
  vehicle: {
    id: string;
    name: string;
    dailyRate: number;
    location_city: string;
    images?: string[];
  };
}

class SavedVehiclesService {
  private baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';

  /**
   * Get all saved vehicles for the current user
   */
  async getSavedVehicles(): Promise<SavedVehicle[]> {
    try {
      // For development with mock auth, return empty array (will use fallback mock data)
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Using fallback mock data for saved vehicles');
        return [];
      }

      const response = await axios.get(`${this.baseUrl}/api/saved-vehicles`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching saved vehicles:', error);
      throw error;
    }
  }

  /**
   * Save a vehicle to the user's wishlist
   */
  async saveVehicle(vehicleId: string): Promise<SavedVehicle> {
    try {
      const response = await axios.post(`${this.baseUrl}/api/saved-vehicles`,
        { vehicleId },
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error saving vehicle:', error);
      throw error;
    }
  }

  /**
   * Remove a vehicle from the user's wishlist
   */
  async removeSavedVehicle(vehicleId: string): Promise<void> {
    try {
      console.log('🗑️ Attempting to remove saved vehicle:', vehicleId);

      // For development with mock auth, simulate successful removal
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Simulating successful vehicle removal');
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
        console.log('✅ Successfully removed saved vehicle (simulated)');
        return;
      }

      console.log('🔑 Using token:', localStorage.getItem('token') ? 'Token exists' : 'No token found');

      const response = await axios.delete(`${this.baseUrl}/api/saved-vehicles/${vehicleId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Successfully removed saved vehicle:', response.data);
    } catch (error: any) {
      console.error('❌ Error removing saved vehicle:', error);
      console.error('❌ Error response:', error.response?.data);
      console.error('❌ Error status:', error.response?.status);

      if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 404) {
        throw new Error('Vehicle not found in saved list.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to remove vehicle from saved list.');
      }
    }
  }

  /**
   * Check if a vehicle is saved by the current user
   */
  async isVehicleSaved(vehicleId: string): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/saved-vehicles/check/${vehicleId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data.isSaved;
    } catch (error) {
      console.error('Error checking if vehicle is saved:', error);
      return false;
    }
  }
}

export default new SavedVehiclesService();
