import axios from 'axios';

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}



export interface PrivacySettings {
  dataCollection: boolean;
  usageAnalytics: boolean;
  thirdPartyIntegrations: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  marketingEmails: boolean;
  bookingReminders: boolean;
  profileVisibility: boolean;
  allowProviderContact: boolean;
}

class AccountService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
  }

  /**
   * Change user password
   */
  async changePassword(data: ChangePasswordRequest): Promise<void> {
    try {
      // For development mode, simulate successful password change
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Simulating password change');
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
        
        // Basic validation
        if (data.newPassword !== data.confirmPassword) {
          throw new Error('New passwords do not match');
        }
        if (data.newPassword.length < 8) {
          throw new Error('Password must be at least 8 characters long');
        }
        
        console.log('✅ Password changed successfully (simulated)');
        return;
      }

      const response = await axios.post(`${this.baseUrl}/api/auth/change-password`, data, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Password changed successfully:', response.data);
    } catch (error: any) {
      console.error('❌ Error changing password:', error);
      throw new Error(error.message || 'Failed to change password');
    }
  }



  /**
   * Get user's privacy settings
   */
  async getPrivacySettings(): Promise<PrivacySettings> {
    try {
      // For development mode, return default privacy settings
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Using default privacy settings');
        return {
          dataCollection: true,
          usageAnalytics: true,
          thirdPartyIntegrations: false,
          emailNotifications: true,
          smsNotifications: true,
          marketingEmails: false,
          bookingReminders: true,
          profileVisibility: true,
          allowProviderContact: false
        };
      }

      const response = await axios.get(`${this.baseUrl}/api/user/privacy-settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      return response.data;
    } catch (error: any) {
      console.error('❌ Error fetching privacy settings:', error);
      throw new Error(error.message || 'Failed to fetch privacy settings');
    }
  }

  /**
   * Update user's privacy settings
   */
  async updatePrivacySettings(settings: PrivacySettings): Promise<void> {
    try {
      // For development mode, simulate successful update
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Simulating privacy settings update');
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('✅ Privacy settings updated successfully (simulated)');
        return;
      }

      await axios.put(`${this.baseUrl}/api/user/privacy-settings`, settings, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Privacy settings updated successfully');
    } catch (error: any) {
      console.error('❌ Error updating privacy settings:', error);
      throw new Error(error.message || 'Failed to update privacy settings');
    }
  }

  /**
   * Request data export
   */
  async requestDataExport(): Promise<void> {
    try {
      // For development mode, simulate successful request
      if (import.meta.env.DEV) {
        console.log('🧪 Development mode: Simulating data export request');
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('✅ Data export requested successfully (simulated)');
        return;
      }

      await axios.post(`${this.baseUrl}/api/user/data-export`, {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      console.log('✅ Data export requested successfully');
    } catch (error: any) {
      console.error('❌ Error requesting data export:', error);
      throw new Error(error.message || 'Failed to request data export');
    }
  }
}

export default new AccountService();
