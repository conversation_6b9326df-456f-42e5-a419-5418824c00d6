import { supabase } from '../utils/supabaseClient';

export interface Vehicle {
  id: string;
  brand: string;
  model: string;
  year?: string;
  engine?: string;
  type?: string;
  category?: string;
  engine_size?: string;
  transmission?: string;
  fuel_type?: string;
  description?: string;
  images?: string[];
  daily_rate?: number;
  weekly_rate?: number;
  monthly_rate?: number;
  security_deposit?: number;
  location?: {
    address: string;
    city: string;
    latitude: number;
    longitude: number;
    postal_code: string;
  };
  active?: boolean;
  displayName?: string;
  searchTerms?: string;
}

export interface SearchFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  vehicleType?: string;
  startDate?: string;
  endDate?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
}

export interface SearchResponse {
  vehicles: Vehicle[];
  total: number;
  page: number;
  size: number;
}

export class VehicleService {
  // Demo data for when database is empty
  private static getDemoVehicles(): Vehicle[] {
    return [
      {
        id: 'demo-1',
        brand: 'Honda',
        model: 'Vario 160',
        year: '2023',
        engine: '160cc',
        type: 'scooter',
        category: 'medium_scooter',
        engine_size: 160,
        transmission: 'automatic',
        fuel_type: 'petrol',
        description: 'Popular automatic scooter perfect for city riding',
        images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'],
        daily_rate: 150000,
        weekly_rate: 900000,
        monthly_rate: 3000000,
        security_deposit: 500000,
        location: {
          address: 'Jakarta',
          city: 'Jakarta',
          latitude: -6.2088,
          longitude: 106.8456,
          postal_code: '10110'
        },
        active: true,
        displayName: 'Honda Vario 160',
        searchTerms: 'honda vario 160 160cc medium scooter automatic'
      },
      {
        id: 'demo-2',
        brand: 'Yamaha',
        model: 'NMAX 155',
        year: '2023',
        engine: '155cc',
        type: 'scooter',
        category: 'medium_scooter',
        engine_size: 155,
        transmission: 'automatic',
        fuel_type: 'petrol',
        description: 'Premium automatic scooter with modern features',
        images: ['https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'],
        daily_rate: 160000,
        weekly_rate: 950000,
        monthly_rate: 3200000,
        security_deposit: 500000,
        location: {
          address: 'Jakarta',
          city: 'Jakarta',
          latitude: -6.2088,
          longitude: 106.8456,
          postal_code: '10110'
        },
        active: true,
        displayName: 'Yamaha NMAX 155',
        searchTerms: 'yamaha nmax 155 155cc medium scooter automatic'
      },
      {
        id: 'demo-3',
        brand: 'Honda',
        model: 'PCX 160',
        year: '2023',
        engine: '160cc',
        type: 'scooter',
        category: 'medium_scooter',
        engine_size: 160,
        transmission: 'automatic',
        fuel_type: 'petrol',
        description: 'Stylish and efficient automatic scooter',
        images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'],
        daily_rate: 155000,
        weekly_rate: 920000,
        monthly_rate: 3100000,
        security_deposit: 500000,
        location: {
          address: 'Jakarta',
          city: 'Jakarta',
          latitude: -6.2088,
          longitude: 106.8456,
          postal_code: '10110'
        },
        active: true,
        displayName: 'Honda PCX 160',
        searchTerms: 'honda pcx 160 160cc medium scooter automatic'
      },
      {
        id: 'demo-4',
        brand: 'Yamaha',
        model: 'Aerox 155',
        year: '2023',
        engine: '155cc',
        type: 'scooter',
        category: 'medium_scooter',
        engine_size: 155,
        transmission: 'automatic',
        fuel_type: 'petrol',
        description: 'Sporty automatic scooter with aggressive styling',
        images: ['https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'],
        daily_rate: 165000,
        weekly_rate: 980000,
        monthly_rate: 3300000,
        security_deposit: 500000,
        location: {
          address: 'Jakarta',
          city: 'Jakarta',
          latitude: -6.2088,
          longitude: 106.8456,
          postal_code: '10110'
        },
        active: true,
        displayName: 'Yamaha Aerox 155',
        searchTerms: 'yamaha aerox 155 155cc medium scooter automatic sporty'
      },
      {
        id: 'demo-5',
        brand: 'Honda',
        model: 'Beat Street',
        year: '2023',
        engine: '110cc',
        type: 'scooter',
        category: 'small_scooter',
        engine_size: 110,
        transmission: 'automatic',
        fuel_type: 'petrol',
        description: 'Compact and fuel-efficient city scooter',
        images: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'],
        daily_rate: 120000,
        weekly_rate: 750000,
        monthly_rate: 2500000,
        security_deposit: 400000,
        location: {
          address: 'Jakarta',
          city: 'Jakarta',
          latitude: -6.2088,
          longitude: 106.8456,
          postal_code: '10110'
        },
        active: true,
        displayName: 'Honda Beat Street',
        searchTerms: 'honda beat street 110cc small scooter automatic compact'
      }
    ];
  }

  static async getVehicles(): Promise<Vehicle[]> {
    try {
      console.log('🔍 VehicleService: Starting to fetch vehicles from backend API...');

      // Use backend API instead of direct Supabase to avoid RLS issues
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/vehicle-catalog`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🔍 VehicleService: Backend API response:', result);

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch vehicles');
      }

      const data = result.data || [];

      // If no data from backend, use demo data
      if (data.length === 0) {
        console.log('⚠️ VehicleService: No vehicles in database, using demo data');
        const demoVehicles = this.getDemoVehicles();
        console.log('✅ VehicleService: Loaded', demoVehicles.length, 'demo vehicles');
        return demoVehicles;
      }

      const mappedVehicles = data.map((v: any) => ({
        id: v.id,
        brand: v.make, // Map make to brand
        model: v.model,
        year: v.year,
        engine: v.engine || 'Not specified',
        type: v.type || 'motorcycle',
        category: v.category || 'scooter_motorcycle',
        engine_size: v.engine_size || 0,
        transmission: 'automatic', // Default for scooters
        fuel_type: 'petrol', // Default
        description: v.description || `${v.make} ${v.model}`,
        images: v.image_url ? [v.image_url] : [],
        daily_rate: v.daily_rate || 0,
        weekly_rate: v.daily_rate ? v.daily_rate * 7 * 0.9 : 0, // 10% weekly discount
        monthly_rate: v.daily_rate ? v.daily_rate * 30 * 0.8 : 0, // 20% monthly discount
        security_deposit: 1000, // Default security deposit
        location: {
          address: v.location_city || 'Indonesia',
          city: v.location_city || 'Indonesia',
          latitude: 0,
          longitude: 0,
          postal_code: ''
        },
        active: true,
        // Add displayName for frontend compatibility
        displayName: `${v.make} ${v.model}`,
        searchTerms: `${v.make} ${v.model} ${v.engine || ''} ${v.category || ''}`.toLowerCase()
      }));

      console.log('✅ VehicleService: Successfully mapped', mappedVehicles.length, 'vehicles');
      console.log('✅ VehicleService: Sample vehicle:', mappedVehicles[0]);

      return mappedVehicles;

    } catch (error) {
      console.error('❌ VehicleService: Error in getVehicles:', error);
      console.log('⚠️ VehicleService: Falling back to demo data due to error');
      return this.getDemoVehicles();
    }
  }

  static async searchVehiclesByQuery(query: string): Promise<Vehicle[]> {
    try {
      console.log('🔍 VehicleService: Searching vehicles for query:', query);

      // Use backend API for search
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/vehicle-catalog/search?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        console.error('Error searching vehicles:', result.error);
        // Fallback to local search in demo data
        const allVehicles = await this.getVehicles();
        const searchLower = query.toLowerCase();
        return allVehicles.filter(vehicle =>
          vehicle.searchTerms?.includes(searchLower) ||
          vehicle.displayName?.toLowerCase().includes(searchLower) ||
          vehicle.brand?.toLowerCase().includes(searchLower) ||
          vehicle.model?.toLowerCase().includes(searchLower)
        );
      }

      const data = result.data || [];

      // If no results from backend, search in demo data
      if (data.length === 0) {
        const allVehicles = await this.getVehicles();
        const searchLower = query.toLowerCase();
        return allVehicles.filter(vehicle =>
          vehicle.searchTerms?.includes(searchLower) ||
          vehicle.displayName?.toLowerCase().includes(searchLower) ||
          vehicle.brand?.toLowerCase().includes(searchLower) ||
          vehicle.model?.toLowerCase().includes(searchLower)
        );
      }

      return (data || []).map((v: any) => ({
        id: v.id,
        brand: v.make, // Map make to brand
        model: v.model,
        year: v.year,
        engine: v.engine || `${v.engine_size}cc`, // Map engine_size to engine
        type: v.transmission || 'automatic',
        category: v.category || 'scooter',
        engine_size: v.engine_size,
        transmission: v.transmission,
        fuel_type: v.fuel_type,
        description: v.description,
        images: v.images || [],
        daily_rate: v.daily_rate,
        weekly_rate: v.weekly_rate,
        monthly_rate: v.monthly_rate,
        security_deposit: v.security_deposit,
        location: v.location || {
          address: '',
          city: '',
          latitude: 0,
          longitude: 0,
          postal_code: ''
        },
        active: v.active,
        // Add displayName for frontend compatibility
        displayName: `${v.make} ${v.model}`,
        searchTerms: `${v.make} ${v.model} ${v.engine || v.engine_size}cc ${v.category}`.toLowerCase()
      }));
    } catch (error) {
      console.error('Error searching vehicles:', error);
      // Fallback to local search in demo data
      const allVehicles = await this.getVehicles();
      const searchLower = query.toLowerCase();
      return allVehicles.filter(vehicle =>
        vehicle.searchTerms?.includes(searchLower) ||
        vehicle.displayName?.toLowerCase().includes(searchLower) ||
        vehicle.brand?.toLowerCase().includes(searchLower) ||
        vehicle.model?.toLowerCase().includes(searchLower)
      );
    }
  }

  // Compatibility method for VehicleSearch component
  static async searchVehiclesByText(query: string): Promise<Vehicle[]> {
    return this.searchVehiclesByQuery(query);
  }

  static async getVehicleById(id: string): Promise<Vehicle | null> {
    try {
      const { data, error } = await supabase
        ?.from('vehicles')
        .select('*')
        .eq('id', id)
        .eq('active', true)
        .single();

      if (error) {
        console.error('Error fetching vehicle by ID:', error);
        return null;
      }

      if (!data) return null;

      return {
        id: data.id,
        brand: data.make, // Map make to brand
        model: data.model,
        year: data.year,
        engine: data.engine || `${data.engine_size}cc`, // Map engine_size to engine
        type: data.transmission || 'automatic',
        category: data.category || 'scooter',
        engine_size: data.engine_size,
        transmission: data.transmission,
        fuel_type: data.fuel_type,
        description: data.description,
        images: data.images || [],
        daily_rate: data.daily_rate,
        weekly_rate: data.weekly_rate,
        monthly_rate: data.monthly_rate,
        security_deposit: data.security_deposit,
        location: data.location || {
          address: '',
          city: '',
          latitude: 0,
          longitude: 0,
          postal_code: ''
        },
        active: data.active,
        // Add displayName for frontend compatibility
        displayName: `${data.make} ${data.model}`,
        searchTerms: `${data.make} ${data.model} ${data.engine || data.engine_size}cc ${data.category}`.toLowerCase()
      };
    } catch (error) {
      console.error('Error fetching vehicle by ID:', error);
      return null;
    }
  }

  // For backward compatibility with MapVehicleSearch
  static async searchVehiclesWithFilters(params: { filters: SearchFilters; page: number; size: number }): Promise<SearchResponse> {
    try {
      const { filters, page, size } = params;
      let query = supabase?.from('vehicles').select('*', { count: 'exact' }).eq('active', true);

      // Apply filters
      if (filters.category) {
        query = query?.eq('category', filters.category);
      }

      if (filters.minPrice || filters.maxPrice) {
        if (filters.minPrice) {
          query = query?.gte('daily_rate', filters.minPrice);
        }
        if (filters.maxPrice) {
          query = query?.lte('daily_rate', filters.maxPrice);
        }
      }

      const { data, error, count } = await query?.limit(size).offset((page - 1) * size);

      if (error) {
        console.error('Error searching vehicles with filters:', error);
        return { vehicles: [], total: 0, page, size };
      }

      const vehicles = (data || []).map((v: any) => ({
        id: v.id,
        brand: v.make, // Map make to brand
        model: v.model,
        year: v.year,
        engine: v.engine || `${v.engine_size}cc`, // Map engine_size to engine
        type: v.transmission || 'automatic',
        category: v.category || 'scooter',
        engine_size: v.engine_size,
        transmission: v.transmission,
        fuel_type: v.fuel_type,
        description: v.description,
        images: v.images || [],
        daily_rate: v.daily_rate,
        weekly_rate: v.weekly_rate,
        monthly_rate: v.monthly_rate,
        security_deposit: v.security_deposit,
        location: v.location || {
          address: '',
          city: '',
          latitude: 0,
          longitude: 0,
          postal_code: ''
        },
        active: v.active,
        // Add displayName for frontend compatibility
        displayName: `${v.make} ${v.model}`,
        searchTerms: `${v.make} ${v.model} ${v.engine || v.engine_size}cc ${v.category}`.toLowerCase()
      }));

      return {
        vehicles,
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error searching vehicles with filters:', error);
      return { vehicles: [], total: 0, page: params.page, size: params.size };
    }
  }

  // Provider-specific methods for backward compatibility
  static async getProviderVehicles(providerId: string): Promise<Vehicle[]> {
    try {
      const { data, error } = await supabase
        ?.from('vehicles')
        .select('*')
        .eq('provider_id', providerId)
        .eq('active', true);

      if (error) {
        console.error('Error fetching provider vehicles:', error);
        return [];
      }

      return (data || []).map((v: any) => ({
        id: v.id,
        brand: v.make, // Map make to brand
        model: v.model,
        year: v.year,
        engine: v.engine || `${v.engine_size}cc`, // Map engine_size to engine
        type: v.transmission || 'automatic',
        category: v.category || 'scooter',
        engine_size: v.engine_size,
        transmission: v.transmission,
        fuel_type: v.fuel_type,
        description: v.description,
        images: v.images || [],
        daily_rate: v.daily_rate,
        weekly_rate: v.weekly_rate,
        monthly_rate: v.monthly_rate,
        security_deposit: v.security_deposit,
        location: v.location || {
          address: '',
          city: '',
          latitude: 0,
          longitude: 0,
          postal_code: ''
        },
        active: v.active,
        // Add displayName for frontend compatibility
        displayName: `${v.make} ${v.model}`,
        searchTerms: `${v.make} ${v.model} ${v.engine || v.engine_size}cc ${v.category}`.toLowerCase()
      }));
    } catch (error) {
      console.error('Error fetching provider vehicles:', error);
      return [];
    }
  }

  // Methods needed by HomePage
  static async getVehicleCounts(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('🔍 VehicleService: Fetching vehicle counts...');

      const vehicles = await this.getVehicles();
      const counts: { [key: string]: number } = {};

      vehicles.forEach(vehicle => {
        const category = vehicle.category?.toUpperCase() || 'OTHER';
        counts[category] = (counts[category] || 0) + 1;
      });

      console.log('✅ VehicleService: Vehicle counts:', counts);
      return { success: true, data: counts };
    } catch (error) {
      console.error('❌ VehicleService: Error getting vehicle counts:', error);
      return { success: false, error: 'Failed to get vehicle counts' };
    }
  }

  // Advanced search method that connects to backend
  static async searchVehiclesAdvanced(searchParams: {
    location?: string;
    startDate?: string;
    endDate?: string;
    vehicleType?: string;
    radius?: number;
    minPrice?: number;
    maxPrice?: number;
    latitude?: number;
    longitude?: number;
    page?: number;
    size?: number;
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('🔍 VehicleService: Advanced search with params:', searchParams);

      // Build query parameters
      const params = new URLSearchParams();

      if (searchParams.location) params.append('location', searchParams.location);
      if (searchParams.startDate) params.append('startDate', searchParams.startDate);
      if (searchParams.endDate) params.append('endDate', searchParams.endDate);
      if (searchParams.vehicleType) params.append('vehicleType', searchParams.vehicleType);
      if (searchParams.radius) params.append('radius', searchParams.radius.toString());
      if (searchParams.minPrice) params.append('minPrice', searchParams.minPrice.toString());
      if (searchParams.maxPrice) params.append('maxPrice', searchParams.maxPrice.toString());
      if (searchParams.latitude) params.append('latitude', searchParams.latitude.toString());
      if (searchParams.longitude) params.append('longitude', searchParams.longitude.toString());
      if (searchParams.page) params.append('page', searchParams.page.toString());
      if (searchParams.size) params.append('size', searchParams.size.toString());

      // Try backend API first
      const backendUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:3001'}/api/listed-vehicles/search?${params.toString()}`;

      try {
        const response = await fetch(backendUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          console.log('✅ VehicleService: Backend search successful:', result);
          return { success: true, data: result };
        }
      } catch (backendError) {
        console.warn('⚠️ Backend search failed, falling back to Supabase:', backendError);
      }

      // Fallback to Supabase direct search
      let query = supabase?.from('vehicles').select('*', { count: 'exact' }).eq('active', true);

      // Apply filters
      if (searchParams.vehicleType) {
        query = query?.eq('type', searchParams.vehicleType);
      }

      if (searchParams.minPrice || searchParams.maxPrice) {
        if (searchParams.minPrice) query = query?.gte('daily_rate', searchParams.minPrice);
        if (searchParams.maxPrice) query = query?.lte('daily_rate', searchParams.maxPrice);
      }

      if (searchParams.location) {
        query = query?.or(`location_city.ilike.%${searchParams.location}%,location_address.ilike.%${searchParams.location}%`);
      }

      const page = searchParams.page || 1;
      const size = searchParams.size || 10;
      const { data, error, count } = await query?.limit(size).offset((page - 1) * size);

      if (error) {
        console.error('❌ VehicleService: Supabase search error:', error);
        return { success: false, error: 'Database search failed' };
      }

      const result = {
        vehicles: data || [],
        total: count || 0,
        page,
        size,
        totalPages: Math.ceil((count || 0) / size)
      };

      console.log('✅ VehicleService: Supabase search results:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('❌ VehicleService: Error in advanced search:', error);
      return { success: false, error: 'Failed to search vehicles' };
    }
  }

  // Legacy method for backward compatibility
  static async searchVehiclesWithParams(params: { filters?: any; page?: number; size?: number }): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.searchVehiclesAdvanced({
      ...params.filters,
      page: params.page,
      size: params.size
    });
  }
}

// Named export for backward compatibility with CheckoutPage
export const getVehicleById = async (id: string): Promise<{ success: boolean; data?: Vehicle; error?: string }> => {
  try {
    const vehicle = await VehicleService.getVehicleById(id);
    if (vehicle) {
      return { success: true, data: vehicle };
    } else {
      return { success: false, error: 'Vehicle not found' };
    }
  } catch (error) {
    console.error('Error in getVehicleById:', error);
    return { success: false, error: 'Failed to fetch vehicle' };
  }
};
