import { supabase } from '../utils/supabaseClient'

export class MonitoringService {
  private static instance: MonitoringService
  private isInitialized = false

  private constructor() {}

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService()
    }
    return MonitoringService.instance
  }

  init() {
    this.isInitialized = true
  }

  async captureError(error: Error, context?: Record<string, any>) {
    console.error('Error captured:', error, context)
    
    // Log to Supabase for production tracking
    if (import.meta.env.MODE === 'production') {
      try {
        await supabase.from('frontend_errors').insert({
          message: error.message,
          stack: error.stack,
          context: context || {},
          url: window.location.href,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
        })
      } catch (logError) {
        console.error('Failed to log error to Supabase:', logError)
      }
    }
  }

  captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info') {
    console.log(`[${level.toUpperCase()}] ${message}`)
  }

  setUser(user: { id: string; email?: string; username?: string }) {
    // Store user context for error logging
    this.currentUser = user
  }

  addBreadcrumb(message: string, category: string, level: 'info' | 'warning' | 'error' = 'info') {
    console.log(`[${category}] ${message}`)
  }

  setTag(key: string, value: string) {
    // Simple tag storage for context
  }

  startTransaction(name: string, operation: string) {
    return {
      name,
      operation,
      finish: () => {},
    }
  }

  private currentUser?: { id: string; email?: string; username?: string }
}

export const monitoring = MonitoringService.getInstance()