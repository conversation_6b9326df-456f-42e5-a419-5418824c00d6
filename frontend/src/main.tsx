import React from 'react'
import ReactDOM from 'react-dom/client'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { ToastContainer } from 'react-toastify'
import { AuthProvider } from './contexts/AuthContext'
import { monitoring } from './services/MonitoringService'
import { runStartupDiagnostics } from './utils/networkDiagnostics'
import './utils/errorLogger' // Initialize global error handlers
import './utils/consoleFilter' // Initialize console filtering for dev mode
import App from './App'
import './styles/global.css'
import 'react-toastify/dist/ReactToastify.css'

// Initialize monitoring
try {
  monitoring.init()
} catch (error) {
  console.warn('Monitoring initialization failed:', error);
}

// Run network diagnostics on startup (disabled for debugging)
// runStartupDiagnostics()

// Simple Material-UI theme (no conflicting overrides)
const muiTheme = createTheme({
  palette: {
    primary: {
      main: '#2BB673', // Green primary color to match the app
    },
  },
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ThemeProvider theme={muiTheme}>
      <CssBaseline />
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <AuthProvider>
          <App />
        </AuthProvider>
      </LocalizationProvider>
    </ThemeProvider>
    <ToastContainer
      position="bottom-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop={false}
      closeOnClick
      pauseOnFocusLoss={false}
      draggable={false}
      pauseOnHover
      theme="light"
    />
  </React.StrictMode>
)

// Service worker disabled for now to prevent fetch errors
// if ('serviceWorker' in navigator && import.meta.env.PROD) {
//   window.addEventListener('load', () => {
//     navigator.serviceWorker.register('/service-worker.js')
//       .then(registration => {
//         console.log('Service Worker registered successfully:', registration.scope);
//       })
//       .catch(error => {
//         console.error('Service Worker registration failed:', error);
//       });
//   });
// }