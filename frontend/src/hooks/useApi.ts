import { useState, useCallback, useEffect } from 'react';
import apiService, { ApiResponse, ApiError } from '../services/apiService';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
  success: boolean;
}

interface UseApiOptions {
  immediate?: boolean;
  retryCount?: number;
  retryDelay?: number;
}

export function useApi<T = any>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
) {
  const { immediate = true, retryCount = 3, retryDelay = 1000 } = options;
  
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false
  });

  const [retryAttempts, setRetryAttempts] = useState(0);

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await api<PERSON>all();
      
      if (response.success) {
        setState({
          data: response.data || null,
          loading: false,
          error: null,
          success: true
        });
        setRetryAttempts(0);
      } else {
        setState({
          data: null,
          loading: false,
          error: response.error || { message: 'Unknown error occurred' },
          success: false
        });
      }
    } catch (error) {
      const apiError: ApiError = {
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        details: error
      };
      
      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false
      });
    }
  }, [apiCall]);

  const retry = useCallback(async () => {
    if (retryAttempts < retryCount) {
      setRetryAttempts(prev => prev + 1);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      await execute();
    }
  }, [execute, retryAttempts, retryCount, retryDelay]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false
    });
    setRetryAttempts(0);
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    ...state,
    execute,
    retry,
    reset,
    retryAttempts,
    canRetry: retryAttempts < retryCount
  };
}

// Specific hooks for common API calls
export function useVehicles() {
  return useApi(() => apiService.getScrapedVehicles());
}

export function useVehicleById(id: string) {
  return useApi(() => apiService.getVehicleById(id), { immediate: !!id });
}

export function useSearchVehicles(params: {
  brand?: string;
  model?: string;
  type?: string;
  location?: string;
  minPrice?: number;
  maxPrice?: number;
}) {
  return useApi(() => apiService.searchVehicles(params));
}

export function useHealthCheck() {
  return useApi(() => apiService.healthCheck());
} 