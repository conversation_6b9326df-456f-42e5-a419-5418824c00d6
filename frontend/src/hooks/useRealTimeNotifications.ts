import { useEffect, useState, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '../contexts/AuthContext';

export interface NotificationData {
  id: string;
  type: 'ASSISTANCE_REQUEST' | 'ASSISTANCE_UPDATE' | 'ASSISTANCE_RESOLVED' | 'STATS_UPDATE';
  title: string;
  message: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  userId?: string;
  adminId?: string;
  providerId?: string;
  assistanceRequestId?: string;
  data?: any;
  createdAt: Date;
  read?: boolean;
}

export interface UseRealTimeNotificationsReturn {
  notifications: NotificationData[];
  unreadCount: number;
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  joinAssistanceRequest: (requestId: string) => void;
  leaveAssistanceRequest: (requestId: string) => void;
}

const MAX_NOTIFICATIONS = 50;

export const useRealTimeNotifications = (): UseRealTimeNotificationsReturn => {
  const { user, token } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const addNotification = useCallback((notification: NotificationData) => {
    setNotifications(prev => {
      const newNotifications = [{ ...notification, read: false }, ...prev];
      return newNotifications.slice(0, MAX_NOTIFICATIONS);
    });

    // Show browser notification if permission granted
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'URGENT'
      });
    }

    // Play notification sound for high priority
    if (notification.priority === 'URGENT' || notification.priority === 'HIGH') {
      try {
        const audio = new Audio('/notification-sound.mp3');
        audio.volume = 0.5;
        audio.play().catch(console.error);
      } catch (error) {
        console.error('Error playing notification sound:', error);
      }
    }
  }, []);

  const connectSocket = useCallback(() => {
    if (!user || !token) return;

    setConnectionStatus('connecting');
    
    const newSocket = io(process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true
    });

    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setIsConnected(true);
      setConnectionStatus('connected');
      reconnectAttemptsRef.current = 0;

      // Authenticate with the server
      newSocket.emit('authenticate', {
        userId: user.id,
        role: user.role,
        token: token
      });
    });

    newSocket.on('authenticated', (data) => {
      console.log('Socket authenticated:', data);
    });

    newSocket.on('authentication_error', (error) => {
      console.error('Socket authentication error:', error);
      setConnectionStatus('error');
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setIsConnected(false);
      setConnectionStatus('disconnected');

      // Attempt to reconnect if not manually disconnected
      if (reason !== 'io client disconnect' && reconnectAttemptsRef.current < maxReconnectAttempts) {
        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
        reconnectTimeoutRef.current = setTimeout(() => {
          reconnectAttemptsRef.current++;
          connectSocket();
        }, delay);
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnectionStatus('error');
    });

    // Listen for assistance request notifications
    newSocket.on('assistance_request', (notification: NotificationData) => {
      console.log('Received assistance request notification:', notification);
      addNotification(notification);
    });

    newSocket.on('assistance_update', (notification: NotificationData) => {
      console.log('Received assistance update notification:', notification);
      addNotification(notification);
    });

    newSocket.on('assistance_resolved', (notification: NotificationData) => {
      console.log('Received assistance resolved notification:', notification);
      addNotification(notification);
    });

    newSocket.on('assistance_stats_update', (data) => {
      console.log('Received assistance stats update:', data);
      // Emit custom event for components that need stats updates
      window.dispatchEvent(new CustomEvent('assistanceStatsUpdate', { detail: data }));
    });

    setSocket(newSocket);
  }, [user, token, addNotification]);

  const disconnectSocket = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socket) {
      socket.disconnect();
      setSocket(null);
    }
    
    setIsConnected(false);
    setConnectionStatus('disconnected');
  }, [socket]);

  // Initialize socket connection
  useEffect(() => {
    if (user && token) {
      connectSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [user, token, connectSocket, disconnectSocket]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('Notification permission:', permission);
      });
    }
  }, []);

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const joinAssistanceRequest = useCallback((requestId: string) => {
    if (socket && isConnected) {
      socket.emit('join_assistance_request', requestId);
    }
  }, [socket, isConnected]);

  const leaveAssistanceRequest = useCallback((requestId: string) => {
    if (socket && isConnected) {
      socket.emit('leave_assistance_request', requestId);
    }
  }, [socket, isConnected]);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    isConnected,
    connectionStatus,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    joinAssistanceRequest,
    leaveAssistanceRequest
  };
};

export default useRealTimeNotifications;
