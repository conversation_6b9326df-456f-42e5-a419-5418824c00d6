import { useCallback } from 'react'
import { useSnackbar } from 'notistack'
import { errorLogger } from '../utils/errorLogger'

export interface UseErrorHandlerOptions {
  showUserMessage?: boolean
  logError?: boolean
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
  const { enqueueSnackbar } = useSnackbar()
  
  const {
    showUserMessage = true,
    logError = true,
    severity = 'medium'
  } = options

  const handleError = useCallback((
    error: Error | string,
    userMessage?: string,
    context?: Record<string, any>
  ) => {
    const errorMessage = typeof error === 'string' ? error : error.message
    const displayMessage = userMessage || 'Something went wrong. Please try again.'

    // Log the error
    if (logError) {
      errorLogger.logError(error, {
        severity,
        context,
      })
    }

    // Show user-friendly message
    if (showUserMessage) {
      enqueueSnackbar(displayMessage, {
        variant: 'error',
        autoHideDuration: 5000,
      })
    }

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('Error handled:', {
        error: errorMessage,
        userMessage: displayMessage,
        context,
      })
    }
  }, [enqueueSnackbar, logError, severity, showUserMessage])

  const handleApiError = useCallback((
    endpoint: string,
    status: number,
    response?: any,
    userMessage?: string
  ) => {
    const defaultMessage = status >= 500 
      ? 'Server error. Please try again later.'
      : 'Request failed. Please check your input and try again.'

    errorLogger.logApiError(endpoint, status, response)
    
    if (showUserMessage) {
      enqueueSnackbar(userMessage || defaultMessage, {
        variant: 'error',
        autoHideDuration: 5000,
      })
    }
  }, [enqueueSnackbar, showUserMessage])

  const handleNetworkError = useCallback((
    error: Error,
    userMessage = 'Network error. Please check your connection and try again.'
  ) => {
    errorLogger.logNetworkError(window.location.href, error)
    
    if (showUserMessage) {
      enqueueSnackbar(userMessage, {
        variant: 'error',
        autoHideDuration: 5000,
      })
    }
  }, [enqueueSnackbar, showUserMessage])

  const handleValidationError = useCallback((
    errors: Record<string, string[]>,
    context?: Record<string, any>
  ) => {
    const firstError = Object.values(errors)[0]?.[0]
    
    if (firstError && showUserMessage) {
      enqueueSnackbar(firstError, {
        variant: 'warning',
        autoHideDuration: 4000,
      })
    }

    errorLogger.logError('Validation Error', {
      severity: 'low',
      source: 'user-action',
      context: {
        validationErrors: errors,
        ...context,
      },
    })
  }, [enqueueSnackbar, showUserMessage])

  return {
    handleError,
    handleApiError,
    handleNetworkError,
    handleValidationError,
  }
}