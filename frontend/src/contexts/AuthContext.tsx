import React, { createContext, useState, useContext, useEffect } from 'react';
import { authService } from '../utils/supabaseClient';

interface AuthContextType {
  user: any;
  loading?: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (email: string, password: string, metadata?: any) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, metadata?: any) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<any>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check current user on mount
    const checkCurrentUser = async () => {
      try {
        if (!authService) {
          console.warn('⚠️ Auth service not available');
          setLoading(false);
          return;
        }

        // Handle both Supabase auth service and mock auth service
        let result;
        if (typeof authService.getCurrentUser === 'function') {
          // Mock auth service
          result = await authService.getCurrentUser();
          if (result) {
            setUser(result);
            setIsAuthenticated(true);
            console.log('✅ Auth: User loaded (mock):', result);
          } else {
            console.log('ℹ️ Auth: No user found (mock)');
            setUser(null);
            setIsAuthenticated(false);
          }
        } else if (typeof authService.getUser === 'function') {
          // Real Supabase auth service
          const { data, error } = await authService.getUser();
          if (error) {
            console.warn('Auth error:', error);
            setUser(null);
            setIsAuthenticated(false);
          } else if (data?.user) {
            setUser(data.user);
            setIsAuthenticated(true);
            console.log('✅ Auth: User loaded (supabase):', data.user);
          } else {
            console.log('ℹ️ Auth: No user found (supabase)');
            setUser(null);
            setIsAuthenticated(false);
          }
        } else {
          // Fallback: check session
          const { data: sessionData } = await authService.getSession();
          if (sessionData?.session?.user) {
            setUser(sessionData.session.user);
            setIsAuthenticated(true);
            console.log('✅ Auth: User loaded (session):', sessionData.session.user);
          } else {
            console.log('ℹ️ Auth: No user found (session)');
            setUser(null);
            setIsAuthenticated(false);
          }
        }
      } catch (error) {
        console.warn('Auth service not available:', error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkCurrentUser();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      if (!authService) {
        throw new Error('Auth service not available');
      }

      const { data, error } = await authService.signInWithPassword({ email, password });
      if (error) {
        throw error;
      }
      
      setUser(data.user);
      setIsAuthenticated(true);
    } catch (error) {
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (authService) {
        await authService.signOut();
      }
      setUser(null);
      setIsAuthenticated(false);

      // Redirect to signin page
      window.location.href = '/signin';
    } catch (error) {
      console.error('Logout error:', error);
      // Still redirect even if logout fails
      window.location.href = '/signin';
    }
  };

  const register = async (email: string, password: string, metadata?: any) => {
    try {
      console.log('🔍 Register called with:', { email, password: '***', metadata });

      const fullName = metadata?.name || email.split('@')[0];
      const nameParts = fullName.split(' ');

      const requestBody = {
        email,
        password,
        first_name: nameParts[0] || fullName,
        last_name: nameParts.slice(1).join(' ') || '',
        role: metadata?.role || 'CUSTOMER'
      };

      console.log('📤 Sending registration request:', { ...requestBody, password: '***' });

      // Call backend API to register user (creates user in both Supabase Auth and database)
      const response = await fetch('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();

      console.log('📥 Registration response:', { status: response.status, result });

      if (!response.ok) {
        console.error('❌ Registration failed:', result);
        throw new Error(result.error || 'Registration failed');
      }

      console.log('✅ Registration successful:', result);

      // If backend registration successful, sign in with Supabase Auth
      if (authService) {
        const { data, error } = await authService.signInWithPassword({
          email,
          password
        });

        if (error) {
          throw error;
        }

        setUser(data.user);
        setIsAuthenticated(true);
      }
    } catch (error) {
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  // Alias methods for compatibility
  const signIn = login;
  const signUp = register;
  const signOut = logout;

  return (
    <AuthContext.Provider value={{ 
      user, 
      loading,
      isAuthenticated, 
      login, 
      logout, 
      register,
      signIn,
      signUp,
      signOut
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
