import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  InputAdornment,
  Tab,
  Tabs,
  useTheme,
  useMediaQuery,
  CardActionArea
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  DirectionsBike as VehicleIcon,
  Payment as PaymentIcon,
  Security as SecurityIcon,
  Help as HelpIcon,
  ContactSupport as ContactIcon,
  Book as BookingIcon,
  Person as AccountIcon,
  LocalOffer as DiscountIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`help-tabpanel-${index}`}
      aria-labelledby={`help-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `help-tab-${index}`,
    'aria-controls': `help-tabpanel-${index}`,
  };
}

const HelpCenterPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const helpCategories = [
    {
      icon: <BookingIcon color="primary" />,
      title: 'Bookings',
      description: 'Help with making, modifying, or canceling bookings',
      tabIndex: 1 // Corresponds to the Bookings tab
    },
    {
      icon: <VehicleIcon color="primary" />,
      title: 'Vehicles',
      description: 'Information about vehicle listings and types',
      tabIndex: 0 // Corresponds to the General tab (which has vehicle info)
    },
    {
      icon: <PaymentIcon color="primary" />,
      title: 'Payments',
      description: 'Payment methods, refunds, and pricing',
      tabIndex: 2 // Corresponds to the Payments tab
    },
    {
      icon: <AccountIcon color="primary" />,
      title: 'Account',
      description: 'Account settings, verification, and profile',
      tabIndex: 3 // Corresponds to the Account tab
    },
    {
      icon: <SecurityIcon color="primary" />,
      title: 'Safety & Security',
      description: 'Safety guidelines and security measures',
      tabIndex: 0 // Corresponds to the General tab for now
    },
    {
      icon: <DiscountIcon color="primary" />,
      title: 'Promotions',
      description: 'Discounts, vouchers, and special offers',
      tabIndex: 2 // Corresponds to the Payments tab (which often includes promotions)
    }
  ];

  const faqGeneral = [
    {
      question: 'What is RentaHub?',
      answer: 'RentaHub is Indonesia\'s leading vehicle rental platform that connects renters with vehicle providers. We offer a wide range of vehicles including motorcycles, scooters, and cars across Southeast Asia, with a special focus on Indonesia.'
    },
    {
      question: 'How do I create an account?',
      answer: 'To create an account, click on the "Sign Up" button in the top right corner of our website or app. You can sign up using your email address, or through your Google or Facebook account. You\'ll need to verify your email and complete your profile information to start using our services.'
    },
    {
      question: 'Is RentaHub available throughout Indonesia?',
      answer: 'RentaHub is currently available in major cities across Indonesia including Jakarta, Bali, Yogyakarta, Surabaya, and Bandung. We\'re continuously expanding to new locations. Check our app for the most up-to-date coverage areas.'
    },
    {
      question: 'What types of vehicles can I rent on RentaHub?',
      answer: 'RentaHub offers a variety of vehicles including small scooters, large motorcycles, electric scooters, and cars. The availability may vary depending on your location.'
    },
    {
      question: 'Do I need an Indonesian driver\'s license to rent a vehicle?',
      answer: 'For motorcycles and scooters, you need either an Indonesian driver\'s license or an International Driving Permit (IDP) that is valid for motorcycles. For cars, you need a valid driver\'s license for the appropriate vehicle class. Always check local regulations as they may vary by region.'
    },
    {
      question: 'Are there any safety guidelines I should follow?',
      answer: 'Yes, we recommend always wearing appropriate safety gear (helmets for motorcycles/scooters), following local traffic laws, and inspecting the vehicle before driving. Additionally, ensure you have the necessary insurance coverage and emergency contact information readily available.'
    }
  ];

  const faqBookings = [
    {
      question: 'How do I make a booking?',
      answer: 'To make a booking, search for available vehicles in your desired location and dates, select a vehicle, review the details and pricing, and proceed to checkout. You\'ll need to be signed in to complete your booking.'
    },
    {
      question: 'Can I modify or cancel my booking?',
      answer: 'Yes, you can modify or cancel your booking through your account dashboard. Go to "My Bookings" and select the booking you wish to change. Please note that cancellation policies vary by provider, and fees may apply depending on how close to the rental start date you make changes.'
    },
    {
      question: 'What happens if I return the vehicle late?',
      answer: 'Late returns may incur additional charges as specified by the vehicle provider. It\'s important to communicate with your provider if you anticipate returning the vehicle later than scheduled. You can also request a booking extension through the app if needed.'
    },
    {
      question: 'What if the vehicle breaks down during my rental?',
      answer: 'If the vehicle breaks down, contact the provider immediately through the RentaHub app. Most providers offer roadside assistance. For emergencies, you can also use our SOS feature in the app to get immediate help.'
    },
    {
      question: 'Can I book a vehicle for someone else?',
      answer: 'Yes, you can book a vehicle for someone else, but the primary driver must meet all the requirements (valid license, minimum age, etc.). You\'ll need to specify the primary driver\'s details during the booking process.'
    },
    {
      question: 'How far in advance should I book?',
      answer: 'We recommend booking as early as possible, especially during peak seasons or holidays. However, last-minute bookings are also possible, subject to vehicle availability.'
    }
  ];

  const faqPayments = [
    {
      question: 'What payment methods are accepted?',
      answer: 'RentaHub accepts various payment methods including credit/debit cards, bank transfers, e-wallets like GoPay, OVO, and DANA, as well as cash payments for certain providers (where specified).'
    },
    {
      question: 'When am I charged for my booking?',
      answer: 'For most bookings, you\'ll be charged a deposit at the time of booking, with the remaining balance due at the start of your rental period. Some providers may require full payment upfront. The specific payment terms will be clearly displayed before you confirm your booking.'
    },
    {
      question: 'How do refunds work?',
      answer: 'Refund policies vary by provider and are displayed on each listing. Generally, cancellations made well in advance receive a higher percentage refund. Refunds are processed back to your original payment method and may take 5-10 business days to appear in your account.'
    },
    {
      question: 'Are there any additional fees I should be aware of?',
      answer: 'Potential additional fees may include insurance, delivery/pickup fees, late return fees, cleaning fees (if the vehicle is returned excessively dirty), and fuel charges if the vehicle is not returned with the same fuel level as at pickup.'
    },
    {
      question: 'Do you offer any discounts or promotions?',
      answer: 'Yes, we regularly offer seasonal promotions, first-time user discounts, and loyalty rewards. Check our "Promotions" section or subscribe to our newsletter to stay updated on the latest offers.'
    }
  ];

  const faqAccount = [
    {
      question: 'How do I create an account?',
      answer: 'Creating an account is easy! Click on the "Sign Up" button in the top right corner, enter your email address, create a password, and follow the verification steps. You can also sign up using your Google or Facebook account for a quicker process.'
    },
    {
      question: 'How do I reset my password?',
      answer: 'To reset your password, click on "Sign In", then select "Forgot Password". Enter your email address, and we\'ll send you instructions to create a new password. For security reasons, password reset links expire after 24 hours.'
    },
    {
      question: 'How do I update my profile information?',
      answer: 'Sign in to your account, go to "Profile" in the account menu, and you can update your personal information, contact details, and preferences. Don\'t forget to save your changes before leaving the page.'
    },
    {
      question: 'What verification documents do I need to provide?',
      answer: 'To rent vehicles, you\'ll need to verify your identity by uploading a valid government-issued ID (passport or national ID card) and a valid driver\'s license appropriate for the vehicle type you wish to rent. Some providers may require additional verification.'
    },
    {
      question: 'Can I have multiple payment methods on my account?',
      answer: 'Yes, you can add multiple payment methods to your account for easier checkout. Go to "Payment Methods" in your account settings to add, remove, or update your payment information.'
    },
    {
      question: 'How do I delete my account?',
      answer: 'If you wish to delete your account, please contact our customer support team. Note that account deletion is permanent and will remove all your data, including booking history and saved preferences.'
    }
  ];

  const handleCategoryClick = (tabIndex: number) => {
    setTabValue(tabIndex);
    // Scroll to the FAQ section
    document.getElementById('faq-section')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality here
    // For now, just show an alert
    if (searchQuery.trim()) {
      alert(`Searching for: ${searchQuery}`);
      // In a real implementation, you would filter FAQs based on the search query
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Help Center
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto', mb: 4 }}>
          Find answers to common questions or get in touch with our support team
        </Typography>
        
        {/* Search */}
        <Box sx={{ maxWidth: 600, mx: 'auto' }}>
          <form onSubmit={handleSearch}>
            <TextField
              fullWidth
              placeholder="Search for help..."
              variant="outlined"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Button 
                      type="submit" 
                      variant="contained" 
                      color="primary"
                      size="small"
                      sx={{ minWidth: 'unset', px: 2 }}
                    >
                      Search
                    </Button>
                  </InputAdornment>
                )
              }}
            />
          </form>
        </Box>
      </Box>

      {/* Help Categories */}
      <Typography variant="h5" gutterBottom fontWeight="medium" sx={{ mb: 3 }}>
        Browse Help Topics
      </Typography>
      <Grid container spacing={3} sx={{ mb: 6 }}>
        {helpCategories.map((category, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card 
              elevation={1} 
              sx={{ 
                height: '100%', 
                transition: '0.3s', 
                '&:hover': { 
                  transform: 'translateY(-5px)', 
                  boxShadow: 4 
                },
                cursor: 'pointer'
              }}
            >
              <CardActionArea 
                sx={{ height: '100%' }}
                onClick={() => handleCategoryClick(category.tabIndex)}
              >
                <CardContent sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {category.icon}
                    <Typography variant="h6" sx={{ ml: 1 }}>
                      {category.title}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {category.description}
                  </Typography>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* FAQ Section */}
      <Paper id="faq-section" elevation={0} sx={{ p: { xs: 2, md: 4 }, bgcolor: 'background.paper', borderRadius: 2, mb: 6 }}>
        <Typography variant="h5" gutterBottom fontWeight="medium" sx={{ mb: 3 }}>
          Frequently Asked Questions
        </Typography>
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="help topics tabs"
            variant={isMobile ? "scrollable" : "standard"}
            scrollButtons={isMobile ? "auto" : undefined}
            allowScrollButtonsMobile
            centered={!isMobile}
          >
            <Tab label="General" {...a11yProps(0)} />
            <Tab label="Bookings" {...a11yProps(1)} />
            <Tab label="Payments" {...a11yProps(2)} />
            <Tab label="Account" {...a11yProps(3)} />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          {faqGeneral.map((faq, index) => (
            <Accordion key={index} elevation={0} sx={{ '&:before': { display: 'none' } }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`general-faq-content-${index}`}
                id={`general-faq-header-${index}`}
              >
                <Typography fontWeight="medium">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography color="text.secondary">
                  {faq.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          {faqBookings.map((faq, index) => (
            <Accordion key={index} elevation={0} sx={{ '&:before': { display: 'none' } }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`bookings-faq-content-${index}`}
                id={`bookings-faq-header-${index}`}
              >
                <Typography fontWeight="medium">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography color="text.secondary">
                  {faq.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          {faqPayments.map((faq, index) => (
            <Accordion key={index} elevation={0} sx={{ '&:before': { display: 'none' } }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`payments-faq-content-${index}`}
                id={`payments-faq-header-${index}`}
              >
                <Typography fontWeight="medium">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography color="text.secondary">
                  {faq.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </TabPanel>
        
        <TabPanel value={tabValue} index={3}>
          {faqAccount.map((faq, index) => (
            <Accordion key={index} elevation={0} sx={{ '&:before': { display: 'none' } }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`account-faq-content-${index}`}
                id={`account-faq-header-${index}`}
              >
                <Typography fontWeight="medium">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography color="text.secondary">
                  {faq.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </TabPanel>
      </Paper>

      {/* Contact Options */}
      <Typography variant="h5" gutterBottom fontWeight="medium" sx={{ mb: 3 }}>
        Need More Help?
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={1} sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ContactIcon color="primary" sx={{ fontSize: 30 }} />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  Contact Support
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" paragraph>
                Our support team is available 7 days a week from 8:00 AM to 10:00 PM WIB.
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HelpIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Email: <EMAIL>" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HelpIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Phone: +62 21 1234 5678" />
                </ListItem>
                <ListItem>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HelpIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="WhatsApp: +62 812 3456 7890" />
                </ListItem>
              </List>
              <Button 
                variant="contained" 
                color="primary" 
                component={RouterLink} 
                to="/support" 
                sx={{ mt: 2 }}
              >
                Submit a Ticket
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card elevation={1} sx={{ height: '100%', bgcolor: 'error.light', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon sx={{ fontSize: 30 }} />
                <Typography variant="h6" sx={{ ml: 1 }}>
                  Emergency Assistance
                </Typography>
              </Box>
              <Typography variant="body2" paragraph>
                For urgent issues requiring immediate assistance, please use our SOS feature.
              </Typography>
              <Typography variant="body2" paragraph>
                Our emergency team is available 24/7 for situations such as accidents, breakdowns, or safety concerns.
              </Typography>
              <Button 
                variant="contained" 
                color="error" 
                component={RouterLink} 
                to="/sos" 
                sx={{ mt: 2, bgcolor: 'white', color: 'error.main', '&:hover': { bgcolor: 'grey.100' } }}
              >
                SOS Emergency Help
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default HelpCenterPage; 