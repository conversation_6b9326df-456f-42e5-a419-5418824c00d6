import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  FormControlLabel,
  Switch,
  InputAdornment,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  LocationOn as LocationIcon,
  PhotoCamera as PhotoIcon,
  Description as DescriptionIcon,
  AttachMoney as MoneyIcon,
  Security as SecurityIcon,
  LocalShipping as DeliveryIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import RentalOptions from '../components/RentalOptions';
import VehicleSpecifications from '../components/VehicleSpecifications';
import ImageUpload from '../components/ImageUpload';
import LocationPicker from '../components/LocationPicker';
import apiService from '../services/apiService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';

interface SEAsiaVehicle {
  id: number;
  brand: string;
  model: string;
  year?: string;
  engine: string;
  type: 'scooter' | 'motorcycle';
  category: 'small' | 'medium' | 'large' | 'luxury';
}

interface VehicleListingForm {
  // Vehicle Details
  selectedVehicle: Vehicle | null;
  manualVehicle: {
    brand: string;
    model: string;
    year: string;
    engine: string;
    type: string;
    category: string;
  };
  
  // Inventory
  availableUnits: number;
  
  // Pricing
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  yearlyRate: number;
  
  // Rental Options
  insuranceOffered: boolean;
  dropoffAvailable: boolean;
  pickupAvailable: boolean;
  helmetsIncluded: number;
  raincoatsIncluded: boolean;
  fullTank: boolean;
  depositAmount: number;
  
  // Images
  images: string[];
  
  // Location
  location_address: string;
  location_city: string;
  location_latitude: number;
  location_longitude: number;
}

const steps = [
  'Vehicle Selection',
  'Inventory & Pricing',
  'Rental Options',
  'Photos & Location',
  'Review & Submit'
];

const VehicleListingPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  // Add brand dropdown state
  const [brand, setBrand] = useState('');
  const [brandOptions, setBrandOptions] = useState<string[]>([]);

  // Fetch brand options on mount
  React.useEffect(() => {
    const fetchBrandOptions = async () => {
      try {
        const vehicles = await apiService.getVehicles();
        const brands = Array.from(new Set(vehicles.map(v => v.brand).filter(Boolean)));
        setBrandOptions(brands);
      } catch (err) {
        console.error('Error fetching brand options:', err);
      }
    };
    
    fetchBrandOptions();
  }, []);

  const [formData, setFormData] = useState<VehicleListingForm>({
    selectedVehicle: null,
    manualVehicle: {
      brand: '',
      model: '',
      year: '',
      engine: '',
      type: '',
      category: ''
    },
    availableUnits: 1,
    dailyRate: 0,
    weeklyRate: 0,
    monthlyRate: 0,
    yearlyRate: 0,
    insuranceOffered: false,
    dropoffAvailable: false,
    pickupAvailable: false,
    helmetsIncluded: 0,
    raincoatsIncluded: false,
    fullTank: false,
    depositAmount: 0,
    images: [],
    location_address: '',
    location_city: '',
    location_latitude: 0,
    location_longitude: 0
  });

  const handleVehicleSelect = (vehicle: Vehicle | null) => {
    setFormData(prev => ({
      ...prev,
      selectedVehicle: vehicle
    }));
  };

  const handleManualEntry = () => {
    setShowManualEntry(true);
  };

  const handleNext = () => {
    if (activeStep === steps.length - 1) {
      handleSubmit();
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.selectedVehicle && !formData.manualVehicle.brand) {
        throw new Error('Please select a vehicle or enter vehicle details manually');
      }

      if (formData.availableUnits <= 0) {
        throw new Error('Available units must be greater than 0');
      }

      if (formData.dailyRate <= 0) {
        throw new Error('Daily rate is required and must be greater than 0');
      }

      if (formData.images.length < 3) {
        throw new Error('Please upload at least 3 photos');
      }

      // Prepare vehicle data
      const vehicleData = {
        // Use selected vehicle data or manual entry
        brand: formData.selectedVehicle?.brand || formData.manualVehicle.brand,
        model: formData.selectedVehicle?.model || formData.manualVehicle.model,
        year: formData.selectedVehicle?.year || formData.manualVehicle.year,
        engine: formData.selectedVehicle?.engine || formData.manualVehicle.engine,
        type: formData.selectedVehicle?.type || formData.manualVehicle.type,
        category: formData.selectedVehicle?.category || formData.manualVehicle.category,
        vehicleId: formData.selectedVehicle?.id,
        
        // Inventory and pricing
        availableUnits: formData.availableUnits,
        dailyRate: formData.dailyRate,
        weeklyRate: formData.weeklyRate || undefined,
        monthlyRate: formData.monthlyRate || undefined,
        yearlyRate: formData.yearlyRate || undefined,
        
        // Rental options
        insuranceOffered: formData.insuranceOffered,
        dropoffAvailable: formData.dropoffAvailable,
        pickupAvailable: formData.pickupAvailable,
        helmetsIncluded: formData.helmetsIncluded,
        raincoatsIncluded: formData.raincoatsIncluded,
        fullTank: formData.fullTank,
        depositAmount: formData.depositAmount,
        
        // Location
        location_address: formData.location_address,
        location_city: formData.location_city,
        location_latitude: formData.location_latitude,
        location_longitude: formData.location_longitude,
        
        // Images
        images: formData.images
      };

      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      // Mock successful response
      console.log('Vehicle listing created successfully:', vehicleData);

      setSuccess(true);
      setShowSuccessDialog(true);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create vehicle listing');
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: // Vehicle Selection
        return formData.selectedVehicle || (formData.manualVehicle.brand && formData.manualVehicle.model);
      case 1: // Inventory & Pricing
        return formData.availableUnits > 0 && formData.dailyRate > 0;
      case 2: // Rental Options
        return true; // All rental options are optional
      case 3: // Photos & Location
        return formData.images.length >= 3;
      default:
        return true;
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <VehicleSearch
              onVehicleSelect={handleVehicleSelect}
              onManualEntry={handleManualEntry}
              selectedVehicle={formData.selectedVehicle}
            />
          </Box>
        );
      
      case 1:
        return (
          <Box>
            <InventoryManagement
              availableUnits={formData.availableUnits}
              onAvailableUnitsChange={(value) => setFormData(prev => ({ ...prev, availableUnits: value }))}
            />
            <Box sx={{ mt: 4 }}>
              <PricingSection
                dailyRate={formData.dailyRate}
                weeklyRate={formData.weeklyRate}
                monthlyRate={formData.monthlyRate}
                yearlyRate={formData.yearlyRate}
                onDailyRateChange={(value) => setFormData(prev => ({ ...prev, dailyRate: value }))}
                onWeeklyRateChange={(value) => setFormData(prev => ({ ...prev, weeklyRate: value }))}
                onMonthlyRateChange={(value) => setFormData(prev => ({ ...prev, monthlyRate: value }))}
                onYearlyRateChange={(value) => setFormData(prev => ({ ...prev, yearlyRate: value }))}
              />
            </Box>
          </Box>
        );
      
      case 2:
        return (
          <RentalOptions
            insuranceOffered={formData.insuranceOffered}
            dropoffAvailable={formData.dropoffAvailable}
            pickupAvailable={formData.pickupAvailable}
            helmetsIncluded={formData.helmetsIncluded}
            raincoatsIncluded={formData.raincoatsIncluded}
            fullTank={formData.fullTank}
            depositAmount={formData.depositAmount}
            onInsuranceOfferedChange={(value) => setFormData(prev => ({ ...prev, insuranceOffered: value }))}
            onDropoffAvailableChange={(value) => setFormData(prev => ({ ...prev, dropoffAvailable: value }))}
            onPickupAvailableChange={(value) => setFormData(prev => ({ ...prev, pickupAvailable: value }))}
            onHelmetsIncludedChange={(value) => setFormData(prev => ({ ...prev, helmetsIncluded: value }))}
            onRaincoatsIncludedChange={(value) => setFormData(prev => ({ ...prev, raincoatsIncluded: value }))}
            onFullTankChange={(value) => setFormData(prev => ({ ...prev, fullTank: value }))}
            onDepositAmountChange={(value) => setFormData(prev => ({ ...prev, depositAmount: value }))}
          />
        );
      
      case 3:
        return (
          <Box>
            <PhotoUpload
              images={formData.images}
              onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
            />
            <Box sx={{ mt: 4 }}>
              <LocationPicker
                address={formData.location_address}
                city={formData.location_city}
                latitude={formData.location_latitude}
                longitude={formData.location_longitude}
                onAddressChange={(value) => setFormData(prev => ({ ...prev, location_address: value }))}
                onCityChange={(value) => setFormData(prev => ({ ...prev, location_city: value }))}
                onLatitudeChange={(value) => setFormData(prev => ({ ...prev, location_latitude: value }))}
                onLongitudeChange={(value) => setFormData(prev => ({ ...prev, location_longitude: value }))}
              />
            </Box>
          </Box>
        );
      
      case 4:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Your Vehicle Listing
            </Typography>
            
            {formData.selectedVehicle && (
              <Alert severity="info" sx={{ mb: 2 }}>
                Selected Vehicle: {formData.selectedVehicle.displayName}
              </Alert>
            )}
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Pricing Summary
              </Typography>
              <Typography>Daily Rate: Rp {formData.dailyRate.toLocaleString()}</Typography>
              {formData.weeklyRate > 0 && <Typography>Weekly Rate: Rp {formData.weeklyRate.toLocaleString()}</Typography>}
              {formData.monthlyRate > 0 && <Typography>Monthly Rate: Rp {formData.monthlyRate.toLocaleString()}</Typography>}
            </Box>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Rental Options
              </Typography>
              <Typography>Insurance: {formData.insuranceOffered ? 'Yes' : 'No'}</Typography>
              <Typography>Pickup Available: {formData.pickupAvailable ? 'Yes' : 'No'}</Typography>
              <Typography>Dropoff Available: {formData.dropoffAvailable ? 'Yes' : 'No'}</Typography>
              <Typography>Helmets Included: {formData.helmetsIncluded}</Typography>
            </Box>
          </Box>
        );
      
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        List Your Vehicle
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>{step.label}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  {renderStepContent(index)}
                  
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      disabled={!isStepValid(index) || loading}
                      sx={{ mr: 1 }}
                    >
                      {index === steps.length - 1 ? 'Submit' : 'Continue'}
                      {loading && <CircularProgress size={20} sx={{ ml: 1 }} />}
                    </Button>
                    <Button
                      disabled={index === 0}
                      onClick={handleBack}
                      sx={{ mr: 1 }}
                    >
                      Back
                    </Button>
                  </Box>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>
      
      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onClose={() => setShowSuccessDialog(false)}>
        <DialogTitle>Success!</DialogTitle>
        <DialogContent>
          <Typography>
            Your vehicle has been listed successfully. You can now manage your listings from your dashboard.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSuccessDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default VehicleListingPage; 