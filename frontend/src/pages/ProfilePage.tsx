import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, CircularProgress } from '@mui/material';
import { authService } from '../utils/supabaseClient';

const ProfilePage = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      const { data } = await authService.getUser();
      setUser(data?.user || null);
      setLoading(false);
    };
    fetchUser();
  }, []);

  if (loading) return <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh"><CircularProgress /></Box>;
  if (!user) return <Box p={4}><Typography variant="h5">You are not logged in.</Typography></Box>;

  return (
    <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
      <Paper elevation={3} sx={{ p: 4, minWidth: 320 }}>
        <Typography variant="h4" gutterBottom>Profile</Typography>
        <Typography variant="body1"><b>Email:</b> {user.email}</Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>Welcome to your profile page!</Typography>
      </Paper>
    </Box>
  );
};

export default ProfilePage; 