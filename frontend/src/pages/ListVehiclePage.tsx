import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abe<PERSON>,
  StepContent,
  Button,
  Box,
  Paper,
  Alert,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  DirectionsBike as BikeIcon,
  Add as AddIcon,
  Search as SearchIcon,
  Business as BusinessIcon,
  PersonOutline as CustomerIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import VehicleSearch from '../components/VehicleSearch';
import { VehicleService, Vehicle } from '../services/VehicleService';
import { authService } from '../utils/supabaseClient';

// Sample vehicle images for demonstration
const vehicleImages: { [key: string]: string } = {
  'Honda Vario 160': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
  'Yamaha NMAX': 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400',
  'Honda PCX 160': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
  'Yamaha XSR 155': 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400',
  'Honda CB150R': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
  'Yamaha MT-15': 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400',
  'Kawasaki Ninja 250': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
  'Suzuki GSX-R150': 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400',
  'Honda CBR150R': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
  'Yamaha R15': 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'
};

const vehicleCategories = [
  { value: 'small_scooter', label: 'Skuter Kecil (≤125cc)' },
  { value: 'medium_scooter', label: 'Skuter Menengah (126-250cc)' },
  { value: 'large_scooter', label: 'Skuter Besar (>250cc)' },
  { value: 'motorcycle', label: 'Motor' },
  { value: 'electric_scooter', label: 'Skuter Listrik' }
];

const ListVehiclePage: React.FC = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [manualEntryMode, setManualEntryMode] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    // Basic Info
    name: '',
    make: '',
    model: '',
    year: '',
    engineSize: '',
    transmission: '',
    fuelType: '',
    color: '',
    licensePlate: '',
    mileage: '',
    description: '',
    
    // Location & Pricing
    address: '',
    city: '',
    state: '',
    zipCode: '',
    latitude: '',
    longitude: '',
    dailyRate: '',
    weeklyRate: '',
    monthlyRate: '',
    securityDeposit: '',
    
    // Features & Photos
    features: [] as string[],
    images: [] as string[],
    
    // Settings
    deliveryAvailable: false,
    deliveryFee: '',
    deliveryRadius: '',
    minimumRentalDays: '1',
    maximumRentalDays: '30',
    availableQuantity: '1',
    
    // Insurance
    hasInsurance: false,
    insurancePrice: '',
    
    // Terms
    rentalTerms: '',
    pickupInstructions: ''
  });

  const [vehicleImage, setVehicleImage] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Check user authentication and role on mount
  useEffect(() => {
    const checkUserAuth = async () => {
      try {
        const { data } = await authService.getUser();
        if (data?.user) {
          // For demo purposes, assume user role is stored in user metadata or fetch from your user service
          const mockUser = {
            id: data.user.id,
            email: data.user.email,
            role: data.user.email === '<EMAIL>' ? 'provider' : 'customer'
          };
          setUser(mockUser);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoading(false);
      }
    };
    checkUserAuth();
  }, []);

  const handleUpgradeToProvider = async () => {
    try {
      // Update user role to provider
      const updatedUser = { ...user, role: 'provider' };
      setUser(updatedUser);

      // Show success message
      alert('🎉 Congratulations! You are now a provider. You can now list your vehicles and start earning!');

      // Continue with the listing process
      setLoading(false);
    } catch (error) {
      console.error('Error upgrading to provider:', error);
      alert('Failed to upgrade to provider. Please try again.');
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async () => {
    // Handle form submission
    console.log('Form submitted:', formData);
    // Assume API call is successful
    setSuccess(true);
    setTimeout(() => {
      navigate('/my-listings');
    }, 1500); // 1.5s delay for user to see success
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleVehicleSelect = (vehicle: Vehicle) => {
    console.log('📝 ListVehiclePage: Vehicle selected:', vehicle);
    setSelectedVehicle(vehicle);
    setManualEntryMode(false);
    if (vehicle) {
      console.log('📝 ListVehiclePage: Updating form data with vehicle:', vehicle);
      setFormData(prev => ({
        ...prev,
        make: vehicle.brand,
        model: vehicle.model,
        year: vehicle.year || '',
        engineSize: vehicle.engine,
        name: vehicle.displayName || `${vehicle.brand} ${vehicle.model}`
      }));
      
      // Set image if available from scraped data
      if (vehicle.images && vehicle.images.length > 0) {
        setVehicleImage(vehicle.images[0]);
      } else if (vehicleImages[vehicle.model]) {
        setVehicleImage(vehicleImages[vehicle.model]);
      } else {
        setVehicleImage(null);
      }
    }
  };

  const handleManualEntry = () => {
    console.log('📝 ListVehiclePage: Manual entry triggered');
    setSelectedVehicle(null);
    setManualEntryMode(true);
    // Clear form data for manual entry
    setFormData(prev => ({
      ...prev,
      make: '',
      model: '',
      year: '',
      engineSize: '',
      name: ''
    }));
  };

  const handleFeatureToggle = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  const steps = [
    {
      label: 'Vehicle Information',
      description: 'Select your vehicle or enter details manually'
    },
    {
      label: 'Location & Pricing',
      description: 'Set your location and rental rates'
    },
    {
      label: 'Features & Photos',
      description: 'Add features and upload photos'
    },
    {
      label: 'Settings & Insurance',
      description: 'Configure delivery options and insurance'
    },
    {
      label: 'Review & Submit',
      description: 'Review your listing and submit'
    }
  ];

  // Show loading state
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Show login prompt if user is not authenticated
  if (!user) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Sign In Required
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            You need to sign in to list your vehicle on RentaHub.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/login?redirect=/list-vehicle')}
            sx={{ mr: 2 }}
          >
            Sign In
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => navigate('/signup?redirect=/list-vehicle')}
          >
            Create Account
          </Button>
        </Paper>
      </Container>
    );
  }

  // Show provider upgrade prompt for customers
  if (user.role === 'customer') {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center', bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <BusinessIcon sx={{ fontSize: 80, mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Become a Provider
          </Typography>
          <Typography variant="h6" sx={{ mb: 3 }}>
            To list your vehicle, you need to upgrade to a Provider account
          </Typography>

          <Grid container spacing={2} sx={{ mb: 4, textAlign: 'left' }}>
            <Grid item xs={12} md={6}>
              <Box sx={{ bgcolor: 'rgba(255,255,255,0.1)', p: 2, borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom>✨ Provider Benefits:</Typography>
                <Typography variant="body2">🚗 List unlimited vehicles</Typography>
                <Typography variant="body2">💰 Set your own prices</Typography>
                <Typography variant="body2">📊 Access to earnings dashboard</Typography>
                <Typography variant="body2">🛡️ Insurance coverage included</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ bgcolor: 'rgba(255,255,255,0.1)', p: 2, borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom>🚀 Getting Started:</Typography>
                <Typography variant="body2">⚡ Instant upgrade - no waiting</Typography>
                <Typography variant="body2">📝 Complete vehicle listing</Typography>
                <Typography variant="body2">💳 Secure payment processing</Typography>
                <Typography variant="body2">📞 24/7 provider support</Typography>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              onClick={handleUpgradeToProvider}
              startIcon={<BusinessIcon />}
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                '&:hover': { bgcolor: 'grey.100' },
                minWidth: 200
              }}
            >
              Upgrade to Provider
            </Button>
            <Button
              variant="outlined"
              color="inherit"
              size="large"
              onClick={() => navigate('/dashboard')}
              startIcon={<CustomerIcon />}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
                minWidth: 200
              }}
            >
              Back to Dashboard
            </Button>
          </Box>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        List Your Vehicle
      </Typography>
      
      <Paper sx={{ p: 3 }}>
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>{step.label}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  <Typography>{step.description}</Typography>
                  
                  {/* Step 1: Basic Information */}
                  {index === 0 && (
                    <Grid container spacing={3} sx={{ mt: 2 }}>
                      
                      {/* Vehicle Search Component */}
                      <Grid item xs={12}>
                        <Typography variant="subtitle1" gutterBottom>
                          Search for your vehicle
                        </Typography>
                        <VehicleSearch
                          onVehicleSelect={handleVehicleSelect}
                          onManualEntry={handleManualEntry}
                          selectedVehicle={selectedVehicle}
                        />
                        {manualEntryMode && (
                          <Alert severity="info" sx={{ mt: 2 }}>
                            ✏️ Manual entry mode enabled. You can now fill in the vehicle details below.
                          </Alert>
                        )}
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Vehicle Name"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Example: Honda Vario 160"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Make/Brand"
                          value={formData.make}
                          onChange={(e) => handleInputChange('make', e.target.value)}
                          placeholder="Example: Honda"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Model"
                          value={formData.model}
                          onChange={(e) => handleInputChange('model', e.target.value)}
                          placeholder="Example: Vario 160"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Year"
                          value={formData.year}
                          onChange={(e) => handleInputChange('year', e.target.value)}
                          placeholder="Example: 2023"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Engine Size"
                          value={formData.engineSize}
                          onChange={(e) => handleInputChange('engineSize', e.target.value)}
                          placeholder="Example: 160cc"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Transmission</InputLabel>
                          <Select
                            value={formData.transmission}
                            onChange={(e) => handleInputChange('transmission', e.target.value)}
                          >
                            <MenuItem value="automatic">Automatic</MenuItem>
                            <MenuItem value="manual">Manual</MenuItem>
                            <MenuItem value="semi-automatic">Semi-Automatic</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Fuel Type</InputLabel>
                          <Select
                            value={formData.fuelType}
                            onChange={(e) => handleInputChange('fuelType', e.target.value)}
                          >
                            <MenuItem value="petrol">Petrol</MenuItem>
                            <MenuItem value="electric">Electric</MenuItem>
                            <MenuItem value="hybrid">Hybrid</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Color"
                          value={formData.color}
                          onChange={(e) => handleInputChange('color', e.target.value)}
                          placeholder="Example: Red"
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Description"
                          value={formData.description}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          placeholder="Describe your vehicle, its condition, and any special features..."
                        />
                      </Grid>
                    </Grid>
                  )}
                  
                  {/* Step 2: Location & Pricing */}
                  {index === 1 && (
                    <Grid container spacing={3} sx={{ mt: 2 }}>
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Location Information
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Address"
                          value={formData.address}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                          placeholder="Street address"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="City"
                          value={formData.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          placeholder="Example: Jakarta"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="State/Province"
                          value={formData.state}
                          onChange={(e) => handleInputChange('state', e.target.value)}
                          placeholder="Example: DKI Jakarta"
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                          Pricing Information
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Daily Rate (IDR)"
                          value={formData.dailyRate}
                          onChange={(e) => handleInputChange('dailyRate', e.target.value)}
                          placeholder="Example: 150000"
                          type="number"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Weekly Rate (IDR)"
                          value={formData.weeklyRate}
                          onChange={(e) => handleInputChange('weeklyRate', e.target.value)}
                          placeholder="Example: 900000"
                          type="number"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Monthly Rate (IDR)"
                          value={formData.monthlyRate}
                          onChange={(e) => handleInputChange('monthlyRate', e.target.value)}
                          placeholder="Example: 3000000"
                          type="number"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Security Deposit (IDR)"
                          value={formData.securityDeposit}
                          onChange={(e) => handleInputChange('securityDeposit', e.target.value)}
                          placeholder="Example: 500000"
                          type="number"
                        />
                      </Grid>
                    </Grid>
                  )}
                  
                  {/* Step 3: Features & Photos */}
                  {index === 2 && (
                    <Grid container spacing={3} sx={{ mt: 2 }}>
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Vehicle Features
                        </Typography>
                        <Grid container spacing={1}>
                          {['GPS', 'Bluetooth Speaker', 'USB Charging', 'Storage Box', 'Windshield', 'LED Lights'].map((feature) => (
                            <Grid item key={feature}>
                              <Chip
                                label={feature}
                                onClick={() => handleFeatureToggle(feature)}
                                color={formData.features.includes(feature) ? 'primary' : 'default'}
                                variant={formData.features.includes(feature) ? 'filled' : 'outlined'}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Photos
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Upload at least 3 photos of your vehicle
                        </Typography>
                        {/* Photo upload component would go here */}
                        <Box sx={{ border: '2px dashed #ccc', p: 3, textAlign: 'center', borderRadius: 2 }}>
                          <Typography>Photo upload component</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  )}
                  
                  {/* Step 4: Settings & Insurance */}
                  {index === 3 && (
                    <Grid container spacing={3} sx={{ mt: 2 }}>
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Delivery Options
                        </Typography>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formData.deliveryAvailable}
                              onChange={(e) => handleInputChange('deliveryAvailable', e.target.checked)}
                            />
                          }
                          label="Offer delivery service"
                        />
                      </Grid>
                      
                      {formData.deliveryAvailable && (
                        <>
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Delivery Fee (IDR)"
                              value={formData.deliveryFee}
                              onChange={(e) => handleInputChange('deliveryFee', e.target.value)}
                              placeholder="Example: 50000"
                              type="number"
                            />
                          </Grid>
                          
                          <Grid item xs={12} sm={6}>
                            <TextField
                              fullWidth
                              label="Delivery Radius (km)"
                              value={formData.deliveryRadius}
                              onChange={(e) => handleInputChange('deliveryRadius', e.target.value)}
                              placeholder="Example: 10"
                              type="number"
                            />
                          </Grid>
                        </>
                      )}
                      
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Insurance
                        </Typography>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formData.hasInsurance}
                              onChange={(e) => handleInputChange('hasInsurance', e.target.checked)}
                            />
                          }
                          label="Offer insurance coverage"
                        />
                      </Grid>
                      
                      {formData.hasInsurance && (
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Insurance Price (IDR)"
                            value={formData.insurancePrice}
                            onChange={(e) => handleInputChange('insurancePrice', e.target.value)}
                            placeholder="Example: 25000"
                            type="number"
                          />
                        </Grid>
                      )}
                      
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom>
                          Rental Terms
                        </Typography>
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          label="Rental Terms & Conditions"
                          value={formData.rentalTerms}
                          onChange={(e) => handleInputChange('rentalTerms', e.target.value)}
                          placeholder="Enter your rental terms and conditions..."
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Pickup Instructions"
                          value={formData.pickupInstructions}
                          onChange={(e) => handleInputChange('pickupInstructions', e.target.value)}
                          placeholder="Instructions for pickup location and process..."
                        />
                      </Grid>
                    </Grid>
                  )}
                  
                  {/* Step 5: Review & Submit */}
                  {index === 4 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Review Your Listing
                      </Typography>
                      
                      {selectedVehicle && (
                        <Alert severity="info" sx={{ mb: 2 }}>
                          Selected Vehicle: {selectedVehicle.displayName}
                        </Alert>
                      )}
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle1" gutterBottom>
                            Vehicle Details
                          </Typography>
                          <Typography>Name: {formData.name}</Typography>
                          <Typography>Make: {formData.make}</Typography>
                          <Typography>Model: {formData.model}</Typography>
                          <Typography>Year: {formData.year}</Typography>
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle1" gutterBottom>
                            Pricing
                          </Typography>
                          <Typography>Daily Rate: Rp {formData.dailyRate}</Typography>
                          <Typography>Weekly Rate: Rp {formData.weeklyRate}</Typography>
                          <Typography>Monthly Rate: Rp {formData.monthlyRate}</Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                  
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      sx={{ mr: 1 }}
                    >
                      {index === steps.length - 1 ? 'Submit' : 'Continue'}
                    </Button>
                    <Button
                      disabled={index === 0}
                      onClick={handleBack}
                      sx={{ mr: 1 }}
                    >
                      Back
                    </Button>
                  </Box>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>
    </Container>
  );
};

export default ListVehiclePage; 