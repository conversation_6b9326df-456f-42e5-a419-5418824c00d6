import React, { useState } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Alert,
  Paper
} from '@mui/material';
import { Warning as WarningIcon } from '@mui/icons-material';
import Layout from '../components/Layout';

const SOSPage: React.FC = () => {
  const [bookingId, setBookingId] = useState('');
  const [reason, setReason] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSOSSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    if (reason.trim().length < 10) {
      setError('Reason must be at least 10 characters long');
      return;
    }

    // Mock SOS alert submission
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess(true);
      setBookingId('');
      setReason('');
    } catch (err) {
      setError('Failed to send SOS alert');
    }
  };

  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box display="flex" alignItems="center" mb={3}>
            <WarningIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
            <Typography variant="h4" color="error">
              Emergency SOS
            </Typography>
          </Box>

          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body1">
              If you are in an emergency situation during your booking, please provide details below. 
              Our support team will be notified immediately.
            </Typography>
          </Alert>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              SOS alert sent successfully. Our support team will contact you shortly.
            </Alert>
          )}

          <Box component="form" onSubmit={handleSOSSubmit}>
            <TextField
              fullWidth
              label="Booking ID"
              value={bookingId}
              onChange={(e) => setBookingId(e.target.value)}
              placeholder="Enter your current booking ID"
              required
              sx={{ mb: 3 }}
            />

            <TextField
              fullWidth
              multiline
              rows={4}
              label="Emergency Reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Describe your emergency situation in detail"
              required
              helperText="Please provide a detailed description of the emergency (minimum 10 characters)"
              sx={{ mb: 3 }}
            />

            <Button
              type="submit"
              variant="contained"
              color="error"
              size="large"
              fullWidth
              sx={{ py: 2 }}
            >
              Send Emergency Alert
            </Button>
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default SOSPage;
