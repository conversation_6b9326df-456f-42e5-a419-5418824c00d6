import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Cancel,
  Home,
  Search,
  Phone,
  Email,
  Support,
  Info,
  Warning,
} from '@mui/icons-material';

const AgreementDeclinedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card sx={{ mb: 3, bgcolor: 'warning.light', color: 'warning.contrastText' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Cancel sx={{ fontSize: 80, mb: 2 }} />
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Agreement Declined
          </Typography>
          <Typography variant="h6">
            You have declined the rental agreement terms. Your booking has been cancelled.
          </Typography>
        </CardContent>
      </Card>

      {/* What This Means */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Info sx={{ mr: 2 }} />
            What This Means
          </Typography>
          
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="h6">Booking Cancelled</Typography>
            <Typography>
              Since you declined the rental agreement, your booking has been automatically cancelled. 
              You will not be able to pick up the vehicle.
            </Typography>
          </Alert>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                💰 Refund Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Full Refund Processing" 
                    secondary="Your payment will be refunded within 3-5 business days"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="No Cancellation Fees" 
                    secondary="No additional charges for declining the agreement"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Refund Confirmation" 
                    secondary="You'll receive an email confirmation of the refund"
                  />
                </ListItem>
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                📧 Next Steps
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Confirmation Email" 
                    secondary="Check your email for cancellation confirmation"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Find Alternative" 
                    secondary="Browse other available vehicles"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Contact Support" 
                    secondary="Reach out if you have questions"
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Why Decline? */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Common Reasons for Declining
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                📋 Agreement Concerns
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Terms too restrictive" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Insurance coverage insufficient" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Deposit amount too high" />
                </ListItem>
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                🔄 Booking Changes
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Found better alternative" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Plans changed" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Warning /></ListItemIcon>
                  <ListItemText primary="Price concerns" />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Alternative Options */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Alternative Options
          </Typography>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="h6">Still Need a Vehicle?</Typography>
            <Typography>
              Don't worry! We have many other vehicles and providers available. 
              You can browse alternatives with different terms and conditions.
            </Typography>
          </Alert>

          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Search sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Browse Vehicles</Typography>
                <Typography variant="body2" color="textSecondary">
                  Find vehicles with terms that work for you
                </Typography>
                <Button 
                  variant="contained" 
                  sx={{ mt: 1 }}
                  onClick={() => navigate('/search')}
                >
                  Search Now
                </Button>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Phone sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Speak to Support</Typography>
                <Typography variant="body2" color="textSecondary">
                  Get help finding the right rental
                </Typography>
                <Button 
                  variant="outlined" 
                  sx={{ mt: 1 }}
                  href="tel:+15551234567"
                >
                  Call Now
                </Button>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Email sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Email Support</Typography>
                <Typography variant="body2" color="textSecondary">
                  Get personalized recommendations
                </Typography>
                <Button 
                  variant="outlined" 
                  sx={{ mt: 1 }}
                  href="mailto:<EMAIL>"
                >
                  Email Us
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Support sx={{ mr: 2 }} />
            Need Help?
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Phone sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Call Us</Typography>
                <Typography variant="body1" color="primary">
                  (*************
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  24/7 Support Available
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Email sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Email Us</Typography>
                <Typography variant="body1" color="primary">
                  <EMAIL>
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Response within 2 hours
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Support sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Live Chat</Typography>
                <Typography variant="body1" color="primary">
                  Available on Website
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Instant assistance
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Box display="flex" justifyContent="center" gap={2}>
        <Button
          variant="outlined"
          startIcon={<Home />}
          onClick={() => navigate('/')}
          size="large"
        >
          Return Home
        </Button>
        <Button
          variant="contained"
          startIcon={<Search />}
          onClick={() => navigate('/search')}
          size="large"
        >
          Find Another Vehicle
        </Button>
      </Box>

      {/* Footer Note */}
      <Box textAlign="center" mt={4}>
        <Typography variant="body2" color="textSecondary">
          We understand that rental terms may not always meet your needs.
          <br />
          Thank you for considering RentaHub, and we hope to serve you in the future!
        </Typography>
      </Box>
    </Box>
  );
};

export default AgreementDeclinedPage;
