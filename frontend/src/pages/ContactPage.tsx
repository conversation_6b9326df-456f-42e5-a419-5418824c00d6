import React, { useState } from 'react';
import {
  Container,
  <PERSON>po<PERSON>,
  Box,
  Grid,
  TextField,
  Button,
  Card,
  CardContent,
  MenuItem,
  Snackbar,
  Alert,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  WhatsApp as WhatsAppIcon,
  Send as SendIcon
} from '@mui/icons-material';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [formErrors, setFormErrors] = useState({
    name: false,
    email: false,
    subject: false,
    message: false
  });
  
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error'
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: false
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const errors = {
      name: formData.name.trim() === '',
      email: !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email),
      subject: formData.subject.trim() === '',
      message: formData.message.trim() === ''
    };
    
    setFormErrors(errors);
    
    if (Object.values(errors).some(error => error)) {
      setSnackbar({
        open: true,
        message: 'Please fill out all required fields correctly.',
        severity: 'error'
      });
      return;
    }
    
    // Mock form submission
    setTimeout(() => {
      setSnackbar({
        open: true,
        message: 'Your message has been sent successfully! We will get back to you soon.',
        severity: 'success'
      });
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    }, 1000);
  };

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  const subjectOptions = [
    'General Inquiry',
    'Booking Issue',
    'Vehicle Listing',
    'Account Problem',
    'Payment Question',
    'Partnership Opportunity',
    'Report a Problem',
    'Other'
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Contact Us
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
          Have questions or need assistance? We're here to help!
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Contact Information */}
        <Grid item xs={12} md={5}>
          <Paper elevation={0} sx={{ p: 4, height: '100%', bgcolor: 'primary.light', color: 'white', borderRadius: 2 }}>
            <Typography variant="h5" gutterBottom fontWeight="medium">
              Get in Touch
            </Typography>
            <Typography variant="body1" paragraph sx={{ mb: 4 }}>
              Our customer service team is available to assist you with any questions or concerns you may have about our services.
            </Typography>
            
            <List>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <EmailIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Email Us" 
                  secondary="<EMAIL>"
                  secondaryTypographyProps={{ color: 'white', opacity: 0.8 }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <PhoneIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Call Us" 
                  secondary="+62 21 1234 5678"
                  secondaryTypographyProps={{ color: 'white', opacity: 0.8 }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <WhatsAppIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="WhatsApp" 
                  secondary="+62 812 3456 7890"
                  secondaryTypographyProps={{ color: 'white', opacity: 0.8 }}
                />
              </ListItem>
              <ListItem sx={{ px: 0, py: 1 }}>
                <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                  <LocationIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Visit Our Office" 
                  secondary="Jl. Sudirman No. 123, Jakarta 12190, Indonesia"
                  secondaryTypographyProps={{ color: 'white', opacity: 0.8 }}
                />
              </ListItem>
            </List>
            
            <Divider sx={{ my: 4, borderColor: 'rgba(255,255,255,0.2)' }} />
            
            <Typography variant="body1" sx={{ mb: 2 }}>
              Business Hours:
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Monday - Friday: 8:00 AM - 8:00 PM WIB
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Saturday - Sunday: 9:00 AM - 5:00 PM WIB
            </Typography>
          </Paper>
        </Grid>
        
        {/* Contact Form */}
        <Grid item xs={12} md={7}>
          <Card elevation={1} sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight="medium">
                Send Us a Message
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
                Fill out the form below and we'll get back to you as soon as possible.
              </Typography>
              
              <Box component="form" onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      error={formErrors.name}
                      helperText={formErrors.name ? 'Name is required' : ''}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      error={formErrors.email}
                      helperText={formErrors.email ? 'Valid email is required' : ''}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number (Optional)"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      select
                      label="Subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      error={formErrors.subject}
                      helperText={formErrors.subject ? 'Please select a subject' : ''}
                      required
                    >
                      {subjectOptions.map(option => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Message"
                      name="message"
                      multiline
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      error={formErrors.message}
                      helperText={formErrors.message ? 'Message is required' : ''}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      size="large"
                      endIcon={<SendIcon />}
                      sx={{ mt: 2 }}
                    >
                      Send Message
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Map Section */}
      <Box sx={{ mt: 6 }}>
        <Typography variant="h5" gutterBottom fontWeight="medium" sx={{ mb: 3 }}>
          Our Location
        </Typography>
        <Paper elevation={1} sx={{ borderRadius: 2, overflow: 'hidden', height: 400 }}>
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.2904388974604!2d106.82137507584543!3d-6.224328360976667!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f3f03357ef11%3A0xc1ec9c4ff6bc6f32!2sJl.%20Jend.%20Sudirman%2C%20Kota%20Jakarta%20Selatan%2C%20Daerah%20Khusus%20Ibukota%20Jakarta!5e0!3m2!1sen!2sid!4v1690000000000!5m2!1sen!2sid"
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title="RentaHub Office Location"
          />
        </Paper>
      </Box>
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ContactPage; 