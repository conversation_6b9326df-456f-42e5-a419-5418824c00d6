import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  CheckCircle,
  Home,
  DirectionsCar,
  Phone,
  Email,
  Schedule,
  Security,
  Description,
  Support,
} from '@mui/icons-material';

const AgreementConfirmedSuccessPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      {/* Success Header */}
      <Card sx={{ mb: 3, bgcolor: 'success.light', color: 'success.contrastText' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CheckCircle sx={{ fontSize: 80, mb: 2 }} />
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Agreement Confirmed Successfully!
          </Typography>
          <Typography variant="h6">
            Your rental agreement has been confirmed and accepted. Your booking is now complete!
          </Typography>
        </CardContent>
      </Card>

      {/* What's Next Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Schedule sx={{ mr: 2 }} />
            What's Next?
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="h6">Before Pickup</Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><Description /></ListItemIcon>
                    <ListItemText primary="Bring valid driver's license" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Security /></ListItemIcon>
                    <ListItemText primary="Have payment method ready" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Phone /></ListItemIcon>
                    <ListItemText primary="Contact provider if needed" />
                  </ListItem>
                </List>
              </Alert>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Typography variant="h6">During Rental</Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><DirectionsCar /></ListItemIcon>
                    <ListItemText primary="Follow all traffic laws" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Security /></ListItemIcon>
                    <ListItemText primary="Report any issues immediately" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Phone /></ListItemIcon>
                    <ListItemText primary="Keep emergency contact handy" />
                  </ListItem>
                </List>
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Important Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Important Information
          </Typography>
          
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="h6">Pickup Requirements</Typography>
            <Typography>
              Please arrive on time for your scheduled pickup. Late arrivals may result in 
              additional charges or cancellation of your reservation.
            </Typography>
          </Alert>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Required Documents
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Valid Driver's License" 
                    secondary="Must match the name on the agreement"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Credit/Debit Card" 
                    secondary="For security deposit (if applicable)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Insurance Documentation" 
                    secondary="If using personal insurance"
                  />
                </ListItem>
              </List>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Vehicle Inspection
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Pre-Rental Inspection" 
                    secondary="Document any existing damage"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Fuel Level Check" 
                    secondary="Note starting fuel level"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Take Photos" 
                    secondary="For your own records"
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <Support sx={{ mr: 2 }} />
            Need Help?
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Phone sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Call Us</Typography>
                <Typography variant="body1" color="primary">
                  (*************
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  24/7 Support Available
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Email sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Email Us</Typography>
                <Typography variant="body1" color="primary">
                  <EMAIL>
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Response within 2 hours
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box textAlign="center" p={2}>
                <Support sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Live Chat</Typography>
                <Typography variant="body1" color="primary">
                  Available on Website
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Instant assistance
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Emergency Contact */}
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6">Emergency Contact</Typography>
        <Typography>
          For roadside assistance or emergencies during your rental: 
          <strong> (555) 911-HELP</strong>
        </Typography>
      </Alert>

      {/* Action Buttons */}
      <Box display="flex" justifyContent="center" gap={2}>
        <Button
          variant="outlined"
          startIcon={<Home />}
          onClick={() => navigate('/')}
          size="large"
        >
          Return Home
        </Button>
        <Button
          variant="contained"
          startIcon={<DirectionsCar />}
          onClick={() => navigate('/my-bookings')}
          size="large"
        >
          View My Bookings
        </Button>
      </Box>

      {/* Footer Note */}
      <Box textAlign="center" mt={4}>
        <Typography variant="body2" color="textSecondary">
          A copy of your signed agreement has been sent to your email address.
          <br />
          Thank you for choosing RentaHub for your vehicle rental needs!
        </Typography>
      </Box>
    </Box>
  );
};

export default AgreementConfirmedSuccessPage;
