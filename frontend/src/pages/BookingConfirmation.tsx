import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Grid,
  Divider,
  Chip,
  IconButton,
} from '@mui/material'
import {
  CheckCircle,
  Home,
  Receipt,
  Phone,
  LocationOn,
  CalendarToday,
  Download,
  Share,
} from '@mui/icons-material'
import { Booking } from '../types'
import * as BookingService from '../services/BookingService'
import * as PaymentService from '../services/PaymentService'

const BookingConfirmation: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>()
  const navigate = useNavigate()
  
  const [booking, setBooking] = useState<Booking | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (bookingId) {
      fetchBooking(bookingId)
    }
  }, [bookingId])

  const fetchBooking = async (id: string) => {
    try {
      setLoading(true)
      const bookingData = await BookingService.getBookingById(id)
      if (bookingData) {
        setBooking(bookingData.data)
      } else {
        setError('Booking not found')
      }
    } catch (error) {
      console.error('Error fetching booking:', error)
      setError('Failed to load booking details')
    } finally {
      setLoading(false)
    }
  }

  const handleGoHome = () => {
    navigate('/')
  }

  const handleViewBookings = () => {
    navigate('/my-bookings')
  }

  const handleDownloadReceipt = () => {
    // TODO: Implement receipt download
    console.log('Download receipt for booking:', bookingId)
  }

  const handleShareBooking = () => {
    if (navigator.share) {
      navigator.share({
        title: 'RentaHub Booking Confirmation',
        text: `My booking for ${booking?.vehicle?.make} ${booking?.vehicle?.model} is confirmed!`,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      // TODO: Show toast notification
    }
  }

  const getStatusColor = (status: string): 'success' | 'warning' | 'info' | 'error' => {
    switch (status) {
      case 'pending_payment':
        return 'warning'
      case 'pending_pickup':
        return 'info'
      case 'active':
        return 'success'
      case 'completed':
        return 'success'
      case 'cancelled':
        return 'error'
      default:
        return 'info'
    }
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading booking details...
        </Typography>
      </Container>
    )
  }

  if (error || !booking) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || 'Booking not found'}
        </Alert>
        <Button
          variant="outlined"
          onClick={handleGoHome}
        >
          Go to Homepage
        </Button>
      </Container>
    )
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Success Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
        <Typography variant="h4" gutterBottom>
          Booking Confirmed!
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Your {booking.vehicle?.make} {booking.vehicle?.model} is reserved
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Booking ID: <strong>{booking.id}</strong>
        </Typography>
      </Box>

      {/* Booking Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Booking Status
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton onClick={handleShareBooking} size="small">
                <Share />
              </IconButton>
              <IconButton onClick={handleDownloadReceipt} size="small">
                <Download />
              </IconButton>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Chip
              label={booking.status}
              color={getStatusColor(booking.bookingStatus)}
              variant="filled"
            />
            <Chip
              label={`Payment: ${booking.paymentStatus}`}
              color={booking.paymentStatus === 'paid' ? 'success' : 'warning'}
              variant="outlined"
            />
          </Box>

          {booking.status === 'pending' && booking.paymentMethod === 'cash' && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Cash Payment:</strong> Please bring the exact amount when you pick up your vehicle.
                {booking.cash_confirmation_code && (
                  <span> Your confirmation code is: <strong>{booking.cash_confirmation_code}</strong></span>
                )}
              </Typography>
            </Alert>
          )}

          {booking.status === 'confirmed' && (
            <Alert severity="success" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Payment Confirmed!</strong> Your vehicle is ready for pickup. Please contact the provider to arrange pickup.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Rental Details */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Rental Details
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <CalendarToday color="action" />
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Pickup Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(booking.startDate)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <CalendarToday color="action" />
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Return Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(booking.endDate)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary">
                Duration: {booking.totalDays} day{booking.totalDays !== 1 ? 's' : ''}
              </Typography>
            </Grid>
          </Grid>

          {booking.deliveryRequested && booking.deliveryAddress && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Delivery Service
              </Typography>
              <Typography variant="body2">
                <LocationOn sx={{ fontSize: 16, mr: 0.5 }} />
                {booking.deliveryAddress}
              </Typography>
            </Box>
          )}

          {booking.pickupInstructions && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Pickup Instructions
              </Typography>
              <Typography variant="body2">
                {booking.pickupInstructions}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Vehicle Details */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Vehicle Details
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6">
                {booking.vehicle?.make} {booking.vehicle?.model}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {booking.vehicle?.year} • {booking.vehicle?.category}
              </Typography>
            </Box>
          </Box>

          {booking.selectedAddOns && booking.selectedAddOns.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Add-ons
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {booking.selectedAddOns.map((addon: any, index: number) => (
                  <Chip
                    key={index}
                    label={`${addon.name} - ${PaymentService.formatCurrency(addon.price, 'IDR')}/day`}
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Provider Contact */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Provider Contact
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1">
              {booking.provider?.businessName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <Phone sx={{ fontSize: 16, mr: 0.5 }} />
              {booking.provider?.phone}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <LocationOn sx={{ fontSize: 16, mr: 0.5 }} />
              {booking.provider?.address}, {booking.provider?.city}
            </Typography>
          </Box>

          <Alert severity="info">
            <Typography variant="body2">
              Please contact the provider to confirm pickup time and location details.
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* Pricing Summary */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Pricing Summary
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography>Vehicle Rate ({booking.totalDays} days):</Typography>
            <Typography>{PaymentService.formatCurrency(booking.vehicleRate * booking.totalDays, 'IDR')}</Typography>
          </Box>
          
          {booking.addOnsTotal > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Add-ons:</Typography>
              <Typography>{PaymentService.formatCurrency(booking.addOnsTotal, 'IDR')}</Typography>
            </Box>
          )}
          
          {booking.deliveryFee > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography>Delivery Fee:</Typography>
              <Typography>{PaymentService.formatCurrency(booking.deliveryFee, 'IDR')}</Typography>
            </Box>
          )}
          
          <Divider sx={{ my: 1 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
            <Typography variant="h6">Total Paid:</Typography>
            <Typography variant="h6">{PaymentService.formatCurrency(booking.total, 'IDR')}</Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
        <Button
          variant="outlined"
          startIcon={<Home />}
          onClick={handleGoHome}
        >
          Go to Homepage
        </Button>
        <Button
          variant="contained"
          startIcon={<Receipt />}
          onClick={handleViewBookings}
        >
          View My Bookings
        </Button>
      </Box>
    </Container>
  )
}

export default BookingConfirmation 