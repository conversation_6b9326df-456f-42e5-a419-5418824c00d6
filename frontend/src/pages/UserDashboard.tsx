import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Person as PersonIcon,
  BookOnline as BookingIcon,
  Favorite as FavoriteIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  Payment as PaymentIcon,
  Notifications as NotificationsIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  PersonOutline as CustomerIcon,
  AdminPanelSettings as AdminIcon,
  ExitToApp as LogoutIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { mockUsers, mockBookings, mockVehicles } from '../utils/mockData';
import { useNavigate } from 'react-router-dom';
import { authService } from '../utils/supabaseClient';
import SavedVehiclesService from '../services/SavedVehiclesService';
import { useAuth } from '../contexts/AuthContext';
import AccountService, { ChangePasswordRequest, PrivacySettings } from '../services/AccountService';
import UserAnalyticsService, { UserAnalytics } from '../services/UserAnalyticsService';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  profileImage?: string;
  phoneNumber?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
}

interface Booking {
  id: string;
  vehicleId: string;
  totalPrice: number;
  status: string;
  startDate: string;
  endDate: string;
  vehicle: {
    vehicleType: string;
    providerId: string;
  };
  provider: {
    name: string;
    email: string;
  };
}

interface SavedVehicle {
  id: string;
  vehicleId: string;
  vehicle: {
    name: string;
    vehicleType: string;
    dailyRate: number;
    location_city: string;
  };
}

export default function UserDashboard() {
  const [activeTab, setActiveTab] = useState(0);
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [user, setUser] = useState<any>(null);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [savedVehicles, setSavedVehicles] = useState<SavedVehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [editingProfile, setEditingProfile] = useState(false);
  const [showDeleteProviderDialog, setShowDeleteProviderDialog] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    phoneNumber: '',
    email: ''
  });
  const [currentViewRole, setCurrentViewRole] = useState<string>('customer');

  // Helper functions for role management
  const canSwitchRoles = () => {
    return user?.role === 'provider' || user?.role === 'admin';
  };

  const getAvailableRoles = () => {
    if (user?.role === 'admin') {
      return ['admin', 'provider', 'customer'];
    } else if (user?.role === 'provider') {
      return ['provider', 'customer'];
    }
    return ['customer'];
  };

  const handleRoleSwitch = (newRole: string) => {
    setCurrentViewRole(newRole);
    // Optionally redirect to appropriate dashboard
    if (newRole === 'provider') {
      navigate('/provider-dashboard');
    } else if (newRole === 'admin') {
      navigate('/admin/dashboard');
    }
    // Stay on current dashboard for customer view
  };

  const handleUpgradeToProvider = async () => {
    try {
      // Update user role to provider
      const updatedUser = { ...user, role: 'provider' };
      setUser(updatedUser);

      // Show success message
      alert('🎉 Congratulations! You are now a provider. You can list your vehicles and start earning!');

      // Redirect to provider dashboard
      navigate('/provider-dashboard');
    } catch (error) {
      console.error('Error upgrading to provider:', error);
      alert('Failed to upgrade to provider. Please try again.');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <AdminIcon />;
      case 'provider':
        return <BusinessIcon />;
      case 'customer':
      default:
        return <CustomerIcon />;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'provider':
        return 'Provider';
      case 'customer':
      default:
        return 'Customer';
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Modal states for account actions
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [showPrivacySettings, setShowPrivacySettings] = useState(false);

  // Form states
  const [passwordForm, setPasswordForm] = useState<ChangePasswordRequest>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    dataCollection: true,
    usageAnalytics: true,
    thirdPartyIntegrations: false,
    emailNotifications: true,
    smsNotifications: true,
    marketingEmails: false,
    bookingReminders: true,
    profileVisibility: true,
    allowProviderContact: false
  });
  const [accountLoading, setAccountLoading] = useState(false);

  // Analytics state
  const [analytics, setAnalytics] = useState<UserAnalytics | null>(null);
  const [hasData, setHasData] = useState<boolean>(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);

  const handleChangePassword = () => {
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setShowChangePassword(true);
  };



  const handlePrivacySettings = async () => {
    setShowPrivacySettings(true);
    try {
      setAccountLoading(true);
      const settings = await AccountService.getPrivacySettings();
      setPrivacySettings(settings);
    } catch (error) {
      console.error('Error loading privacy settings:', error);
    } finally {
      setAccountLoading(false);
    }
  };

  // Form submission handlers
  const handlePasswordSubmit = async () => {
    try {
      setAccountLoading(true);
      await AccountService.changePassword(passwordForm);
      alert('Password changed successfully!');
      setShowChangePassword(false);
    } catch (error: any) {
      alert(error.message || 'Failed to change password');
    } finally {
      setAccountLoading(false);
    }
  };



  const handlePrivacySubmit = async () => {
    try {
      setAccountLoading(true);
      await AccountService.updatePrivacySettings(privacySettings);
      alert('Privacy settings saved successfully!');
      setShowPrivacySettings(false);
    } catch (error: any) {
      alert(error.message || 'Failed to save privacy settings');
    } finally {
      setAccountLoading(false);
    }
  };

  const handleDataExport = async () => {
    try {
      setAccountLoading(true);
      await AccountService.requestDataExport();
      alert('Data export request submitted! You will receive an email with your data within 24 hours.');
    } catch (error: any) {
      alert(error.message || 'Failed to request data export');
    } finally {
      setAccountLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    // TODO: Implement delete account functionality
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      alert('Delete Account functionality will be implemented soon!');
    }
  };

  // Load analytics data
  const loadAnalytics = async (userId: string) => {
    try {
      setAnalyticsLoading(true);

      // Check if user has any data
      const userHasData = await UserAnalyticsService.hasUserData(userId);
      setHasData(userHasData);

      if (userHasData) {
        // Load full analytics
        const analyticsData = await UserAnalyticsService.getUserAnalytics(userId);
        setAnalytics(analyticsData);
      } else {
        // No data available
        setAnalytics(null);
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      setHasData(false);
      setAnalytics(null);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // Fetch user data on mount
  useEffect(() => {
    async function fetchUserData() {
      try {
        setLoading(true);
        setError(null);

        // Get current authenticated user
        const { data: authData } = await authService.getUser();

        if (authData?.user) {
          // Create user object from auth data
          const userData = {
            id: authData.user.id,
            email: authData.user.email,
            name: authData.user.user_metadata?.name || authData.user.email?.split('@')[0] || 'User',
            role: authData.user.email === '<EMAIL>' ? 'provider' : 'customer',
            profileImage: authData.user.user_metadata?.avatar_url || null,
            phoneNumber: authData.user.user_metadata?.phone || '',
            emailVerified: authData.user.email_confirmed_at ? true : false,
            phoneVerified: false
          };

          setUser(userData);
          setEditForm({
            name: userData.name,
            phoneNumber: userData.phoneNumber,
            email: userData.email
          });

          // Load mock data for demo purposes
          setBookings(mockBookings.slice(0, 3)); // Show first 3 bookings

          // Load saved vehicles from API
          try {
            const savedVehiclesData = await SavedVehiclesService.getSavedVehicles();
            setSavedVehicles(savedVehiclesData);
          } catch (error) {
            console.error('Failed to load saved vehicles:', error);
            // Fallback to mock data if API fails
            setSavedVehicles(mockVehicles.slice(0, 4).map(vehicle => ({
              id: `saved-${vehicle.id}`,
              userId: userData.id,
              vehicleId: vehicle.id,
              createdAt: new Date().toISOString(),
              vehicle: {
                id: vehicle.id,
                name: vehicle.name,
                dailyRate: vehicle.pricePerDay,
                location_city: vehicle.location.city
              }
            })));
          }

          // Load analytics data
          await loadAnalytics(userData.id);

        } else {
          setError('No authenticated user found');
        }

      } catch (err) {
        setError('Failed to load user data');
        console.error('Error fetching user data:', err);
      } finally {
        setLoading(false);
      }
    }
    
    fetchUserData();
  }, []);

  const handleEditProfile = () => {
    // Demo: just close dialog and update local state
    setShowEditProfile(false);
  };

  const handleBookNow = (savedVehicle: any) => {
    console.log('🚗 Booking vehicle:', savedVehicle);

    // Navigate to checkout with vehicle data
    navigate('/checkout', {
      state: {
        vehicleId: savedVehicle.vehicleId,
        vehicleName: savedVehicle.vehicle.name,
        dailyRate: savedVehicle.vehicle.dailyRate,
        pickupLocation: savedVehicle.vehicle.location_city || 'Jakarta',
        dropOffLocation: savedVehicle.vehicle.location_city || 'Jakarta',
        from: new Date().toISOString(),
        to: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      }
    });
  };

  const handleRemoveSaved = async (savedVehicleId: string) => {
    console.log('🗑️ Removing saved vehicle:', savedVehicleId);

    try {
      // Find the saved vehicle to get the vehicleId
      const savedVehicle = savedVehicles.find(sv => sv.id === savedVehicleId);
      if (!savedVehicle) {
        console.error('❌ Saved vehicle not found');
        return;
      }

      // Call the API to remove the saved vehicle
      await SavedVehiclesService.removeSavedVehicle(savedVehicle.vehicle.id);

      // Remove from local state after successful API call
      setSavedVehicles(prev => prev.filter(sv => sv.id !== savedVehicleId));

      console.log('✅ Vehicle removed from saved list');
    } catch (error) {
      console.error('❌ Failed to remove saved vehicle:', error);
      // Show user-friendly error message
      alert('Failed to remove vehicle from saved list. Please try again.');
    }
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED': return 'success';
      case 'PENDING': return 'warning';
      case 'CANCELLED': return 'error';
      case 'COMPLETED': return 'info';
      default: return 'default';
    }
  };

  const getBookingStatusText = (status: string) => {
    switch (status) {
      case 'CONFIRMED': return 'Confirmed';
      case 'PENDING': return 'Pending';
      case 'CANCELLED': return 'Cancelled';
      case 'COMPLETED': return 'Completed';
      case 'IN_PROGRESS': return 'In Progress';
      default: return status;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          User not found. Please log in again.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }} data-testid="customer-dashboard">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={user.profileImage}
                sx={{ width: 64, height: 64 }}
              >
                {user.name?.charAt(0) || user.email.charAt(0)}
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" data-testid="welcome-message">
                  Welcome back, <span data-testid="user-name">{user.name || 'User'}</span>!
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {user.email}
                </Typography>
              </Box>
            </Box>
            <Box display="flex" alignItems="center" gap={2}>
              <Button
                variant="outlined"
                startIcon={<NotificationsIcon />}
                data-testid="notification-bell"
                sx={{ minWidth: 'auto', px: 2 }}
              >
                Notifications
              </Button>
            </Box>
            <Box display="flex" gap={2}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={() => window.location.href = '/search'}
                data-testid="browse-vehicles-button"
                sx={{ mr: 1 }}
              >
                Browse Vehicles
              </Button>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => setShowEditProfile(true)}
              >
                Edit Profile
              </Button>
              <Button
                variant="outlined"
                color="error"
                startIcon={<LogoutIcon />}
                onClick={handleLogout}
                data-testid="logout-button"
              >
                Sign Out
              </Button>
            </Box>
          </Box>

          {/* Analytics Section */}
          {analyticsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
              <Typography variant="body1" sx={{ ml: 2 }}>Loading your analytics...</Typography>
            </Box>
          ) : !hasData ? (
            // CTA when no data is available
            <Card sx={{ mb: 4, textAlign: 'center', py: 4 }}>
              <CardContent>
                <Typography variant="h5" gutterBottom>
                  📊 Start Your Journey with RentaHub
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  You haven't made any bookings yet. Start exploring our amazing vehicles to see your analytics here!
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/')}
                  sx={{ mr: 2 }}
                >
                  Browse Vehicles
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => setActiveTab('saved-vehicles')}
                >
                  View Saved Vehicles
                </Button>
              </CardContent>
            </Card>
          ) : (
            // Real analytics data
            <Grid container spacing={3} mb={4}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <BookingIcon color="primary" sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="h6">{analytics?.totalBookings || 0}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Total Bookings
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <HistoryIcon color="primary" sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="h6">{analytics?.completedTrips || 0}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Completed Trips
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <FavoriteIcon color="primary" sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="h6">{analytics?.savedVehicles || 0}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Saved Vehicles
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center">
                      <PaymentIcon color="primary" sx={{ mr: 2 }} />
                      <Box>
                        <Typography variant="h6">
                          ${analytics?.totalSpent?.toFixed(2) || '0.00'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Total Spent
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="My Bookings" data-testid="my-bookings-link" />
            <Tab label="Saved Vehicles" />
            <Tab label="Profile" />
            <Tab label="Settings" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {activeTab === 0 && (
          <Box>
            <Typography variant="h6" mb={3}>My Bookings</Typography>

            {analyticsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
                <Typography variant="body1" sx={{ ml: 2 }}>Loading your bookings...</Typography>
              </Box>
            ) : !hasData || !analytics?.recentBookings?.length ? (
              <Card sx={{ textAlign: 'center', py: 4 }}>
                <CardContent>
                  <BookingIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Bookings Yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    Start your adventure by booking your first vehicle!
                  </Typography>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/')}
                  >
                    Browse Vehicles
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Grid container spacing={3}>
                {analytics.recentBookings.map((booking) => (
                  <Grid item xs={12} sm={6} md={4} key={booking.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{booking.vehicleName}</Typography>
                          <Chip
                            label={booking.status.charAt(0).toUpperCase() + booking.status.slice(1).toLowerCase()}
                            color={booking.status === 'COMPLETED' ? 'success' : booking.status === 'ACTIVE' ? 'primary' : 'default'}
                            size="small"
                          />
                        </Box>

                        <Typography variant="body2" color="text.secondary" mb={1}>
                          {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                        </Typography>

                        <Typography variant="h6" color="primary">
                          ${booking.totalAmount.toFixed(2)}
                        </Typography>

                        <Box mt={2}>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<ViewIcon />}
                            fullWidth
                            onClick={() => navigate(`/booking/${booking.id}`)}
                          >
                            View Details
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}

                {analytics.totalBookings > analytics.recentBookings.length && (
                  <Grid item xs={12}>
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <Button variant="outlined" onClick={() => navigate('/bookings')}>
                        View All {analytics.totalBookings} Bookings
                      </Button>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            <Typography variant="h6" mb={3}>Saved Vehicles</Typography>
            
            {savedVehicles.length === 0 ? (
              <Alert severity="info">
                You haven't saved any vehicles yet. Start exploring to save your favorites!
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {savedVehicles.map((saved) => (
                  <Grid item xs={12} sm={6} md={4} key={saved.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" mb={2}>
                          {saved.vehicle.name}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary" mb={1}>
                          {saved.vehicle.location_city}
                        </Typography>
                        
                        <Typography variant="h6" color="primary" mb={2}>
                          ${saved.vehicle.dailyRate}/day
                        </Typography>

                        <Box display="flex" gap={1}>
                          <Button
                            variant="contained"
                            size="small"
                            fullWidth
                            onClick={() => handleBookNow(saved)}
                          >
                            Book Now
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            color="error"
                            onClick={() => handleRemoveSaved(saved.id)}
                          >
                            Remove
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {activeTab === 2 && (
          <Box>
            <Typography variant="h6" mb={3}>Profile Information</Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" mb={3}>Personal Information</Typography>
                    
                    <List>
                      <ListItem>
                        <ListItemAvatar>
                          <PersonIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Name"
                          secondary={user.name || 'Not provided'}
                        />
                      </ListItem>
                      
                      <Divider />
                      
                      <ListItem>
                        <ListItemAvatar>
                          <PersonIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Email"
                          secondary={user.email}
                        />
                      </ListItem>
                      
                      <Divider />
                      
                      <ListItem>
                        <ListItemAvatar>
                          <PersonIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Phone"
                          secondary={user.phone || 'Not provided'}
                        />
                      </ListItem>
                      
                      <Divider />
                      
                      <ListItem>
                        <ListItemAvatar>
                          <PersonIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Account Type"
                          secondary={user.type === 'customer' ? 'Customer' : 'Service Provider'}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" mb={3}>Verification Status</Typography>
                    
                    <List>
                      <ListItem>
                        <ListItemAvatar>
                          <NotificationsIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Email Verification"
                          secondary={user.verified ? 'Verified' : 'Not verified'}
                        />
                        <Chip
                          label={user.verified ? 'Verified' : 'Pending'}
                          color={user.verified ? 'success' : 'warning'}
                          size="small"
                        />
                      </ListItem>
                      
                      <Divider />
                      
                      <ListItem>
                        <ListItemAvatar>
                          <NotificationsIcon />
                        </ListItemAvatar>
                        <ListItemText
                          primary="Phone Verification"
                          secondary={user.verified ? 'Verified' : 'Not verified'}
                        />
                        <Chip
                          label={user.verified ? 'Verified' : 'Pending'}
                          color={user.verified ? 'success' : 'warning'}
                          size="small"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {activeTab === 3 && (
          <Box>
            <Typography variant="h6" mb={3}>Account Settings</Typography>

            {/* Provider Upgrade Section - Only show for customers */}
            {user?.role === 'customer' && (
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12}>
                  <Card sx={{ bgcolor: 'primary.light', color: 'primary.contrastText' }}>
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <BusinessIcon sx={{ fontSize: 40 }} />
                        <Box>
                          <Typography variant="h6">Become a Provider</Typography>
                          <Typography variant="body2">
                            Start earning by listing your vehicles for rent
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2" sx={{ mb: 2 }}>
                        🚗 List your cars, bikes, or scooters<br/>
                        💰 Set your own prices and availability<br/>
                        📊 Track your earnings with our dashboard<br/>
                        🛡️ Protected by our insurance coverage
                      </Typography>
                      <Button
                        variant="contained"
                        color="secondary"
                        onClick={handleUpgradeToProvider}
                        startIcon={<BusinessIcon />}
                        sx={{
                          bgcolor: 'white',
                          color: 'primary.main',
                          '&:hover': { bgcolor: 'grey.100' }
                        }}
                      >
                        Upgrade to Provider Account
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            {/* Role Toggle Section - Only show for providers and admins */}
            {canSwitchRoles() && (
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" mb={2}>Account Type</Typography>
                      <Typography variant="body2" color="text.secondary" mb={2}>
                        Switch between different account views
                      </Typography>
                      <ToggleButtonGroup
                        value={currentViewRole}
                        exclusive
                        onChange={(e, newRole) => newRole && handleRoleSwitch(newRole)}
                        aria-label="account role"
                        size="small"
                      >
                        {getAvailableRoles().map((role) => (
                          <ToggleButton key={role} value={role} aria-label={role}>
                            {getRoleIcon(role)}
                            <Box ml={1}>{getRoleLabel(role)}</Box>
                          </ToggleButton>
                        ))}
                      </ToggleButtonGroup>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" mb={2}>Notification Preferences</Typography>
                    
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="Email notifications for bookings"
                    />
                    
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="SMS notifications for urgent matters"
                    />
                    
                    <FormControlLabel
                      control={<Switch />}
                      label="Marketing emails"
                    />
                    
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="Booking reminders"
                    />
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" mb={2}>Account Actions</Typography>

                    <Button
                      variant="outlined"
                      fullWidth
                      sx={{ mb: 2 }}
                      onClick={handleChangePassword}
                    >
                      Change Password
                    </Button>



                    <Button
                      variant="outlined"
                      fullWidth
                      sx={{ mb: 2 }}
                      onClick={handlePrivacySettings}
                    >
                      Privacy Settings
                    </Button>

                    <Button
                      variant="outlined"
                      color="error"
                      fullWidth
                      onClick={handleDeleteAccount}
                    >
                      Delete Account
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Edit Profile Dialog */}
        <Dialog open={showEditProfile} onClose={() => setShowEditProfile(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="Full Name"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Phone Number"
                value={editForm.phoneNumber}
                onChange={(e) => setEditForm({ ...editForm, phoneNumber: e.target.value })}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Email"
                value={editForm.email}
                disabled
                sx={{ mb: 2 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowEditProfile(false)}>Cancel</Button>
            <Button onClick={handleEditProfile} variant="contained">
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>

        {/* Change Password Modal */}
        <Dialog open={showChangePassword} onClose={() => setShowChangePassword(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Change Password</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                type="password"
                label="Current Password"
                variant="outlined"
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                type="password"
                label="New Password"
                variant="outlined"
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                type="password"
                label="Confirm New Password"
                variant="outlined"
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                sx={{ mb: 2 }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowChangePassword(false)} disabled={accountLoading}>Cancel</Button>
            <Button
              variant="contained"
              onClick={handlePasswordSubmit}
              disabled={accountLoading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
            >
              {accountLoading ? <CircularProgress size={20} /> : 'Change Password'}
            </Button>
          </DialogActions>
        </Dialog>



        {/* Privacy Settings Modal */}
        <Dialog open={showPrivacySettings} onClose={() => setShowPrivacySettings(false)} maxWidth="md" fullWidth>
          <DialogTitle>Privacy Settings</DialogTitle>
          <DialogContent>
            {accountLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Box sx={{ pt: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Data & Privacy</Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.dataCollection}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, dataCollection: e.target.checked }))}
                    />
                  }
                  label="Allow data collection for service improvement"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.usageAnalytics}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, usageAnalytics: e.target.checked }))}
                    />
                  }
                  label="Share usage analytics"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.thirdPartyIntegrations}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, thirdPartyIntegrations: e.target.checked }))}
                    />
                  }
                  label="Allow third-party integrations"
                  sx={{ mb: 3, display: 'block' }}
                />

                <Typography variant="h6" sx={{ mb: 2 }}>Communication Preferences</Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.emailNotifications}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                    />
                  }
                  label="Email notifications for bookings"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.smsNotifications}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, smsNotifications: e.target.checked }))}
                    />
                  }
                  label="SMS notifications for urgent matters"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.marketingEmails}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, marketingEmails: e.target.checked }))}
                    />
                  }
                  label="Marketing emails"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.bookingReminders}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, bookingReminders: e.target.checked }))}
                    />
                  }
                  label="Booking reminders"
                  sx={{ mb: 3, display: 'block' }}
                />

                <Typography variant="h6" sx={{ mb: 2 }}>Account Visibility</Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.profileVisibility}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, profileVisibility: e.target.checked }))}
                    />
                  }
                  label="Show profile to other users"
                  sx={{ mb: 2, display: 'block' }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={privacySettings.allowProviderContact}
                      onChange={(e) => setPrivacySettings(prev => ({ ...prev, allowProviderContact: e.target.checked }))}
                    />
                  }
                  label="Allow contact from providers"
                  sx={{ mb: 2, display: 'block' }}
                />

                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Data Export:</strong> You can request a copy of your personal data at any time.
                  </Typography>
                  <Button
                    size="small"
                    sx={{ mt: 1 }}
                    onClick={handleDataExport}
                    disabled={accountLoading}
                  >
                    {accountLoading ? <CircularProgress size={16} /> : 'Request Data Export'}
                  </Button>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowPrivacySettings(false)} disabled={accountLoading}>Cancel</Button>
            <Button
              variant="contained"
              onClick={handlePrivacySubmit}
              disabled={accountLoading}
            >
              {accountLoading ? <CircularProgress size={20} /> : 'Save Settings'}
            </Button>
          </DialogActions>
        </Dialog>
    </Container>
  );
}