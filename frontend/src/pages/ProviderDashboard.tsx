import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  Chip,
  Alert,
  AlertTitle,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  CalendarToday as CalendarIcon,
  Schedule as ScheduleIcon,
  DirectionsBike as VehicleIcon,
  BookOnline as BookingIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Email as EmailIcon,
  Notifications as NotificationIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Star as StarIcon,
  DragIndicator as DragIcon,
  Close as CloseIcon,
  DirectionsBike,
  Message as MessageIcon,
  AttachMoney
} from '@mui/icons-material';

// Safely import components with error handling
// Import components using ES6 imports
import BookingCalendar from '../components/BookingCalendar';
import BookingScheduler from '../components/BookingScheduler';
import SimpleBookingScheduler from '../components/SimpleBookingScheduler';
import DragDropBooking from '../components/DragDropBooking';
import VehicleUploadForm from '../components/VehicleUploadForm';
import AdminDashboard from '../components/analytics/AdminDashboard';
import ProviderAnalytics from '../components/analytics/ProviderAnalytics';
import ProviderSettings from '../components/settings/ProviderSettings';
import EmptyVehiclesCTA from '../components/vehicles/EmptyVehiclesCTA';
import EmptyBookingsCTA from '../components/bookings/EmptyBookingsCTA';
import SimpleProviderPayments from '../components/payments/SimpleProviderPayments';
import PickupDropoffManager from '../components/bookings/PickupDropoffManager';
import ProviderMessaging from '../components/messaging/ProviderMessaging';
import ErrorBoundary from '../components/ErrorBoundary';
import { Dialog as MuiDialog } from '@mui/material';
import BookingForm from '../components/BookingForm';

import { Booking, Vehicle, User, BookingStatus } from '../types';
import NotificationService from '../services/NotificationService';
import VehicleDataService from '../services/VehicleDataService';
import ProviderService, { ProviderDashboardStats, Provider } from '../services/ProviderService';
import { useAuth } from '../contexts/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const ProviderDashboard: React.FC = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [stats, setStats] = useState<ProviderDashboardStats | null>(null);
  const [provider, setProvider] = useState<Provider | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isNotificationModalOpen, setIsNotificationModalOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [notificationType, setNotificationType] = useState<'email' | 'sms'>('email');
  const [isAddVehicleOpen, setIsAddVehicleOpen] = useState(false);
  const [isCreateBookingOpen, setIsCreateBookingOpen] = useState(false);
  const [isFilterBookingsOpen, setIsFilterBookingsOpen] = useState(false);
  const [isExportDataOpen, setIsExportDataOpen] = useState(false);
  const [isSendNotificationsOpen, setIsSendNotificationsOpen] = useState(false);

  // Add state for filter form and notification form
  const [filterValues, setFilterValues] = useState<any>({});
  const [filteredBookings, setFilteredBookings] = useState<Booking[]>(bookings);
  const [notificationMessage, setNotificationMessage] = useState('');

  // Add state for booking details modal
  const [selectedBookingDetails, setSelectedBookingDetails] = useState<Booking | null>(null);
  const [isBookingDetailsOpen, setIsBookingDetailsOpen] = useState(false);

  // Fetch provider data on component mount
  useEffect(() => {
    setLoading(true);
    setError(null);

    async function fetchProviderData() {
      try {
        console.log('🔄 Fetching provider dashboard data...');
        console.log('👤 Current user:', user);
        console.log('🆔 User ID being used:', user?.id);
        console.log('🌐 Making API calls to backend...');

        // Fetch provider profile and dashboard stats in parallel
        const [providerResponse, statsResponse] = await Promise.all([
          ProviderService.getCurrentProvider(),
          ProviderService.getDashboardStats(user?.id)
        ]);

        if (providerResponse.success && providerResponse.data) {
          setProvider(providerResponse.data);
          console.log('✅ Provider profile loaded:', providerResponse.data);
        } else {
          console.error('❌ Failed to load provider profile:', providerResponse.error);
        }

        if (statsResponse.success && statsResponse.data) {
          setStats(statsResponse.data);
          console.log('✅ Dashboard stats loaded:', statsResponse.data);

          // Set bookings from stats
          if (statsResponse.data.recentBookings) {
            setBookings(statsResponse.data.recentBookings as any[]);
          }
        } else {
          console.error('❌ Failed to load dashboard stats:', statsResponse.error);
        }

        // Fetch vehicles using ProviderService
        try {
          const vehiclesResponse = await ProviderService.getProviderVehicles();
          if (vehiclesResponse.success && vehiclesResponse.data) {
            setVehicles(vehiclesResponse.data);
            console.log('✅ Provider vehicles loaded:', vehiclesResponse.data);
          } else {
            console.log('ℹ️ No vehicles found for provider');
            setVehicles([]);
          }
        } catch (vehicleError) {
          console.error('❌ Failed to load provider vehicles:', vehicleError);
          setVehicles([]);
        }

      } catch (err) {
        console.error('❌ Error fetching provider data:', err);

        // Check if it's a network error (backend unavailable)
        if (err instanceof Error && (
          err.message.includes('fetch') ||
          err.message.includes('Network') ||
          err.message.includes('Failed to fetch')
        )) {
          setError('Backend service unavailable. Please ensure the backend server is running on http://localhost:3001');
        } else {
          setError('Failed to load provider dashboard data');
        }
      } finally {
        setLoading(false);
      }
    }

    // Always fetch data - the service has fallback logic
    console.log('🔄 useEffect triggered, user:', user);
    console.log('🚀 About to call fetchProviderData...');
    fetchProviderData();
  }, [user]); // Refetch when user changes

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleBookingUpdate = async (bookingId: string, status: BookingStatus) => {
    try {
      setLoading(true);
      
      // Update booking status
      const updatedBookings = bookings.map(booking => 
        booking.id === bookingId ? { ...booking, status } : booking
      );
      setBookings(updatedBookings);

      // Send notification
      const booking = bookings.find(b => b.id === bookingId);
      if (booking && booking.vehicle && booking.user) {
        const notificationService = NotificationService;
        
        if (status === 'confirmed') {
          const emailNotification = notificationService.generateBookingConfirmationEmail(
            booking,
            booking.vehicle,
            booking.user
          );
          await notificationService.sendEmailNotification(emailNotification);
        } else if (status === 'cancelled') {
          const emailNotification = notificationService.generateBookingCancellationEmail(
            booking,
            booking.vehicle,
            booking.user
          );
          await notificationService.sendEmailNotification(emailNotification);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Error updating booking:', error);
      setLoading(false);
    }
  };

  const handleBookingCreate = async (bookingData: Partial<Booking>) => {
    try {
      setLoading(true);
      
      // Create new booking via API
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (response.ok) {
        const newBooking = await response.json();
        setBookings([...bookings, newBooking.data]);
      } else {
        throw new Error('Failed to create booking');
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error creating booking:', error);
      setLoading(false);
    }
  };



  const handleBookingDelete = async (bookingId: string) => {
    try {
      setLoading(true);

      // Delete booking
      const updatedBookings = bookings.filter(booking => booking.id !== bookingId);
      setBookings(updatedBookings);

      setLoading(false);
    } catch (error) {
      console.error('Error deleting booking:', error);
      setLoading(false);
    }
  };

  const handleBookingReschedule = async (bookingId: string, newStartDate: string, newEndDate: string) => {
    try {
      setLoading(true);
      
      // Update booking dates
      const updatedBookings = bookings.map(booking => 
        booking.id === bookingId 
          ? { ...booking, startDate: newStartDate, endDate: newEndDate }
          : booking
      );
      setBookings(updatedBookings);
      
      setLoading(false);
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      setLoading(false);
    }
  };

  const handleExportBookings = async (filters: any) => {
    try {
      setLoading(true);
      
      // Export bookings logic
      console.log('Exporting bookings with filters:', filters);
      
      // Simulate export
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = 'data:text/csv;charset=utf-8,Booking ID,Vehicle,Customer,Start Date,End Date,Status,Amount\n1,Honda Vario 160,John Doe,2024-01-15,2024-01-17,confirmed,450000';
        link.download = 'bookings.csv';
        link.click();
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error exporting bookings:', error);
      setLoading(false);
    }
  };

  const handleImportBookings = async (file: File) => {
    try {
      setLoading(true);
      
      // Import bookings logic
      console.log('Importing bookings from file:', file.name);
      
      // Simulate import
      setTimeout(() => {
        setLoading(false);
        alert('Bookings imported successfully!');
      }, 1000);
    } catch (error) {
      console.error('Error importing bookings:', error);
      setLoading(false);
    }
  };

  const handleSendNotification = async (bookingId: string, type: 'email' | 'sms') => {
    try {
      setLoading(true);
      
      const booking = bookings.find(b => b.id === bookingId);
      if (booking && booking.vehicle && booking.user) {
        const notificationService = NotificationService;
        
        if (type === 'email') {
          const emailNotification = notificationService.generateBookingReminderEmail(
            booking,
            booking.vehicle,
            booking.user
          );
          await notificationService.sendEmailNotification(emailNotification);
        } else {
          const smsNotification = {
            to: booking.user.phone || '',
            message: `Reminder: Your booking for ${booking.vehicle.name} is tomorrow!`,
            bookingId: booking.id,
            type: 'booking_reminder' as any
          };
          await notificationService.sendSMSNotification(smsNotification);
        }
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error sending notification:', error);
      setLoading(false);
    }
  };

  const handleNotificationModalOpen = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsNotificationModalOpen(true);
  };

  const handleSendNotificationFromModal = async () => {
    if (selectedBooking) {
      await handleSendNotification(selectedBooking.id, notificationType);
      setIsNotificationModalOpen(false);
      setSelectedBooking(null);
    }
  };

  // Filter Bookings logic
  const handleApplyFilter = (filters: any) => {
    let filtered = bookings;
    if (filters.status) {
      filtered = filtered.filter(b => b.status === filters.status);
    }
    if (filters.vehicleId) {
      filtered = filtered.filter(b => b.vehicleId === filters.vehicleId);
    }
    setFilteredBookings(filtered);
    setIsFilterBookingsOpen(false);
  };

  // Export Data logic
  const handleExportData = () => {
    const csvRows = [
      'Booking ID,Vehicle,Customer,Start Date,End Date,Status,Amount',
      ...filteredBookings.map(b => `${b.id},${b.vehicle?.name},${b.user?.name},${b.startDate},${b.endDate},${b.status},${b.totalAmount}`)
    ];
    const csvContent = 'data:text/csv;charset=utf-8,' + csvRows.join('\n');
    const link = document.createElement('a');
    link.href = encodeURI(csvContent);
    link.download = 'bookings.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    setIsExportDataOpen(false);
  };

  // Send Notifications logic
  const handleSendNotificationSubmit = () => {
    // Mock send
    setTimeout(() => {
      alert('Notification sent: ' + notificationMessage);
      setIsSendNotificationsOpen(false);
      setNotificationMessage('');
    }, 500);
  };

  const handleOpenBookingDetails = (booking: Booking) => {
    setSelectedBookingDetails(booking);
    setIsBookingDetailsOpen(true);
  };
  const handleCloseBookingDetails = () => {
    setIsBookingDetailsOpen(false);
    setSelectedBookingDetails(null);
  };

  // Test function to manually trigger API call
  const testApiCall = async () => {
    console.log('🧪 Testing API call manually...');
    try {
      const response = await fetch('http://localhost:3001/api/providers/dashboard/stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'cmd7hwwhm0000xcxshowpwrwd'
        }
      });
      const data = await response.json();
      console.log('🧪 Test API response:', data);
      alert('API call successful! Check console for details.');
    } catch (error) {
      console.error('🧪 Test API error:', error);
      alert('API call failed! Check console for details.');
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    const isBackendUnavailable = error.includes('Backend service unavailable');

    return (
      <Container maxWidth="xl">
        <Box sx={{ mt: 3 }}>
          <Alert severity={isBackendUnavailable ? "warning" : "error"}>
            <AlertTitle>
              {isBackendUnavailable ? "Backend Service Unavailable" : "Error Loading Dashboard"}
            </AlertTitle>
            {error}

            {isBackendUnavailable && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  To get live data for your provider dashboard:
                </Typography>
                <Box component="ol" sx={{ pl: 2, mb: 2 }}>
                  <li>Open a terminal in the backend directory</li>
                  <li>Run: <code>npm run dev</code></li>
                  <li>Ensure the server starts on http://localhost:3001</li>
                  <li>Refresh this page</li>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  The dashboard will show demo data until the backend is connected.
                </Typography>
              </Box>
            )}

            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button variant="outlined" onClick={() => window.location.reload()}>
                Retry
              </Button>
              {isBackendUnavailable && (
                <Button
                  variant="contained"
                  onClick={() => setError(null)}
                  color="primary"
                >
                  Continue with Demo Data
                </Button>
              )}
            </Box>
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ width: '100%', mt: 3 }}>
        {/* Dashboard Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1" data-testid="provider-welcome">
            Provider Dashboard
          </Typography>
          <Box display="flex" gap={2}>
            <Button variant="outlined" startIcon={<AddIcon />} onClick={() => setIsAddVehicleOpen(true)} data-testid="add-vehicle-button">
              Add Vehicle
            </Button>
            <Button variant="contained" startIcon={<NotificationIcon />} onClick={() => setIsSendNotificationsOpen(true)}>
              Send Notifications
            </Button>
            <Button variant="outlined" color="secondary" onClick={testApiCall}>
              Test API
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} mb={3} data-testid="earnings-summary">
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <BookingIcon color="primary" />
                  <Box>
                    <Typography variant="h6">{stats?.totalBookings || 0}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Bookings
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <MoneyIcon color="success" />
                  <Box>
                    <Typography variant="h6">
                      {ProviderService.formatCurrency(stats?.totalRevenue || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <StarIcon color="warning" />
                  <Box>
                    <Typography variant="h6">{(stats?.averageRating || 0).toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Average Rating
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <TrendingUpIcon color="info" />
                  <Box>
                    <Typography variant="h6">{stats?.responseRate || 0}%</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Response Rate
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Additional Stats Row */}
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <VehicleIcon color="primary" />
                  <Box>
                    <Typography variant="h6">{stats?.totalVehicles || 0}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Vehicles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <VehicleIcon color="success" />
                  <Box>
                    <Typography variant="h6">{stats?.activeVehicles || 0}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Vehicles
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <BookingIcon color="warning" />
                  <Box>
                    <Typography variant="h6">{stats?.pendingBookings || 0}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Bookings
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2}>
                  <MoneyIcon color="info" />
                  <Box>
                    <Typography variant="h6">{ProviderService.formatCurrency(stats?.monthlyRevenue || 0)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Revenue
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* No Data CTA */}
        {stats && stats.totalVehicles === 0 && stats.totalBookings === 0 && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>Welcome to your Provider Dashboard!</AlertTitle>
            <Typography variant="body2" sx={{ mb: 2 }}>
              You haven't added any vehicles yet. Start by adding your first vehicle to begin receiving bookings.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setTabValue(4)} // Switch to Vehicles tab
            >
              Add Your First Vehicle
            </Button>
          </Alert>
        )}

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="dashboard tabs">
            <Tab icon={<DashboardIcon />} label="Overview" />
            <Tab icon={<CalendarIcon />} label="Calendar" />
            <Tab icon={<ScheduleIcon />} label="Scheduler" />
            <Tab icon={<DragIcon />} label="Drag & Drop" />
            <Tab icon={<VehicleIcon />} label="Vehicles" />
            <Tab icon={<AnalyticsIcon />} label="Analytics" />
            <Tab icon={<SettingsIcon />} label="Settings" />
            <Tab icon={<AttachMoney />} label="Payments" />
            <Tab icon={<DirectionsBike />} label="Pickup/Dropoff" />
            <Tab icon={<MessageIcon />} label="Messages" />
          </Tabs>
        </Box>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Recent Bookings
                  </Typography>
                  <List>
                    {stats?.recentBookings && stats.recentBookings.length > 0 ? (
                      stats.recentBookings.slice(0, 5).map((booking) => (
                        <ListItem key={booking.id} divider button onClick={() => handleOpenBookingDetails(booking as any)}>
                          <ListItemText
                            primary={`${booking.vehicleName} - ${booking.customerName}`}
                            secondary={`${ProviderService.formatDate(booking.startDate)} to ${ProviderService.formatDate(booking.endDate)} - ${ProviderService.formatCurrency(booking.totalAmount)}`}
                          />
                          <Chip
                            label={booking.status}
                            color={
                              booking.status.toLowerCase() === 'completed' ? 'success' :
                              booking.status.toLowerCase() === 'pending' ? 'warning' :
                              booking.status.toLowerCase() === 'confirmed' ? 'info' :
                              'error'
                            }
                            size="small"
                            sx={{
                              backgroundColor: ProviderService.getStatusColor(booking.status),
                              color: 'white'
                            }}
                          />
                          <IconButton size="small" onClick={e => { e.stopPropagation(); handleNotificationModalOpen(booking as any); }}>
                            <EmailIcon />
                          </IconButton>
                        </ListItem>
                      ))
                    ) : (
                      <ListItem>
                        <ListItemText
                          primary="No recent bookings"
                          secondary="Your recent bookings will appear here"
                        />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Top Vehicles
                  </Typography>
                  <List dense>
                    {stats?.topVehicles && stats.topVehicles.length > 0 ? (
                      stats.topVehicles.slice(0, 3).map((vehicle) => (
                        <ListItem key={vehicle.id}>
                          <ListItemText
                            primary={vehicle.name}
                            secondary={`${vehicle.bookings} bookings • ${ProviderService.formatCurrency(vehicle.revenue)} • ${vehicle.rating.toFixed(1)}⭐`}
                          />
                        </ListItem>
                      ))
                    ) : (
                      <ListItem>
                        <ListItemText
                          primary="No vehicle data"
                          secondary="Your top performing vehicles will appear here"
                        />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>

              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Quick Actions
                  </Typography>
                  <Box display="flex" flexDirection="column" gap={2}>
                    <Button variant="outlined" startIcon={<AddIcon />} onClick={() => setIsCreateBookingOpen(true)}>
                      Create Booking
                    </Button>
                    <Button variant="outlined" startIcon={<FilterIcon />} onClick={() => setIsFilterBookingsOpen(true)}>
                      Filter Bookings
                    </Button>
                    <Button variant="outlined" startIcon={<ExportIcon />} onClick={handleExportData}>
                      Export Data
                    </Button>
                    <Button variant="outlined" startIcon={<NotificationIcon />} onClick={() => setIsSendNotificationsOpen(true)}>
                      Send Notifications
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {bookings.length === 0 && vehicles.length === 0 ? (
            <EmptyBookingsCTA
              variant="provider"
              onRefresh={() => window.location.reload()}
            />
          ) : BookingCalendar ? (
            <BookingCalendar
              bookings={bookings}
              vehicles={vehicles}
              onBookingUpdate={handleBookingUpdate}
              onBookingCreate={handleBookingCreate}
              onBookingDelete={handleBookingDelete}
              onBookingReschedule={handleBookingReschedule}
              onExportBookings={handleExportBookings}
              onImportBookings={handleImportBookings}
              onSendNotification={handleSendNotification}
              loading={loading}
            />
          ) : (
            <Alert severity="error">Booking Calendar component not loaded.</Alert>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <ErrorBoundary
            fallback={
              <Alert severity="error">
                <AlertTitle>Scheduler Error</AlertTitle>
                The booking scheduler encountered an error. Please try refreshing the page or contact support if the issue persists.
              </Alert>
            }
            onError={(error, errorInfo) => {
              console.error('Scheduler Error:', error, errorInfo);
            }}
          >
            <SimpleBookingScheduler
              bookings={bookings}
              vehicles={vehicles}
              onBookingUpdate={handleBookingUpdate}
              onBookingCreate={() => setIsCreateBookingOpen(true)}
              onBookingDelete={handleBookingDelete}
              loading={loading}
            />
          </ErrorBoundary>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {DragDropBooking ? (
            <DragDropBooking
              bookings={bookings}
              vehicles={vehicles}
              onBookingReschedule={handleBookingReschedule}
              onBookingUpdate={handleBookingUpdate}
              onBookingDelete={handleBookingDelete}
              loading={loading}
            />
          ) : (
            <Alert severity="error">Drag & Drop Booking component not loaded.</Alert>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          {vehicles.length === 0 ? (
            <EmptyVehiclesCTA
              onAddVehicle={() => setIsAddVehicleOpen(true)}
              onRefresh={() => window.location.reload()}
            />
          ) : (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Your Vehicles ({vehicles.length})</Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setIsAddVehicleOpen(true)}
                >
                  Add Vehicle
                </Button>
              </Box>
              <Grid container spacing={3}>
                {vehicles.map((vehicle) => (
                  <Grid item xs={12} md={6} lg={4} key={vehicle.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {vehicle.name || `${vehicle.brand} ${vehicle.model}`}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" mb={2}>
                          {vehicle.brand} {vehicle.model} ({vehicle.year})
                        </Typography>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                          <Typography variant="h6" color="primary">
                            Rp {vehicle.dailyRate?.toLocaleString()}/day
                          </Typography>
                          <Chip
                            label={((vehicle.availableUnits ?? 0) > 0) ? 'Available' : 'Unavailable'}
                            color={((vehicle.availableUnits ?? 0) > 0) ? 'success' : 'error'}
                            size="small"
                          />
                        </Box>
                        <Box display="flex" gap={1} flexWrap="wrap">
                          {vehicle.smartTags?.slice(0, 3).map((tag) => (
                            <Chip key={tag} label={tag} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <ProviderAnalytics />
        </TabPanel>

        <TabPanel value={tabValue} index={6}>
          <ProviderSettings />
        </TabPanel>

        <TabPanel value={tabValue} index={7}>
          <SimpleProviderPayments />
        </TabPanel>

        <TabPanel value={tabValue} index={8}>
          <PickupDropoffManager />
        </TabPanel>

        <TabPanel value={tabValue} index={9}>
          <ProviderMessaging />
        </TabPanel>
      </Box>

      {/* Notification Modal */}
      <Dialog open={isNotificationModalOpen} onClose={() => setIsNotificationModalOpen(false)}>
        <DialogTitle>Send Notification</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Notification Type</InputLabel>
              <Select
                value={notificationType}
                onChange={(e) => setNotificationType(e.target.value as 'email' | 'sms')}
                label="Notification Type"
              >
                <MenuItem value="email">Email</MenuItem>
                <MenuItem value="sms">SMS</MenuItem>
              </Select>
            </FormControl>
            {selectedBooking && (
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Booking: {selectedBooking.vehicle?.name} - {selectedBooking.user?.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Dates: {selectedBooking.startDate} to {selectedBooking.endDate}
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsNotificationModalOpen(false)}>Cancel</Button>
          <Button onClick={handleSendNotificationFromModal} variant="contained">
            Send Notification
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Vehicle Modal */}
      <MuiDialog open={isAddVehicleOpen} onClose={() => setIsAddVehicleOpen(false)} maxWidth="md" fullWidth>
        <VehicleUploadForm onClose={() => setIsAddVehicleOpen(false)} />
      </MuiDialog>
      {/* Create Booking Modal */}
      <MuiDialog open={isCreateBookingOpen} onClose={() => setIsCreateBookingOpen(false)} maxWidth="sm" fullWidth>
        <Box p={3}>
          <BookingForm
            vehicle={vehicles[0]}
            onBookingComplete={() => setIsCreateBookingOpen(false)}
            onCancel={() => setIsCreateBookingOpen(false)}
          />
        </Box>
      </MuiDialog>
      {/* Filter Bookings Modal */}
      <MuiDialog open={isFilterBookingsOpen} onClose={() => setIsFilterBookingsOpen(false)} maxWidth="sm" fullWidth>
        <Box p={3}>
          <Typography variant="h6">Filter Bookings</Typography>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filterValues.status || ''}
              onChange={e => setFilterValues({ ...filterValues, status: e.target.value })}
              label="Status"
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="confirmed">Confirmed</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Vehicle</InputLabel>
            <Select
              value={filterValues.vehicleId || ''}
              onChange={e => setFilterValues({ ...filterValues, vehicleId: e.target.value })}
              label="Vehicle"
            >
              <MenuItem value="">All</MenuItem>
              {vehicles.map(v => (
                <MenuItem key={v.id} value={v.id}>{v.name}</MenuItem>
              ))}
            </Select>
          </FormControl>
          <Box mt={3} display="flex" justifyContent="flex-end" gap={2}>
            <Button onClick={() => setIsFilterBookingsOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => handleApplyFilter(filterValues)}>Apply</Button>
          </Box>
        </Box>
      </MuiDialog>
      {/* Export Data Modal is now handled by handleExportData directly */}
      {/* Send Notifications Modal */}
      <MuiDialog open={isSendNotificationsOpen} onClose={() => setIsSendNotificationsOpen(false)} maxWidth="sm" fullWidth>
        <Box p={3}>
          <Typography variant="h6">Send Notifications</Typography>
          <TextField
            fullWidth
            label="Notification Message"
            value={notificationMessage}
            onChange={e => setNotificationMessage(e.target.value)}
            multiline
            rows={3}
            sx={{ mt: 2 }}
          />
          <Box mt={3} display="flex" justifyContent="flex-end" gap={2}>
            <Button onClick={() => setIsSendNotificationsOpen(false)}>Cancel</Button>
            <Button variant="contained" onClick={handleSendNotificationSubmit}>Send</Button>
          </Box>
        </Box>
      </MuiDialog>

      {/* Speed Dial for Quick Actions */}
      <SpeedDial
        ariaLabel="Quick actions"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        icon={<SpeedDialIcon />}
      >
        <SpeedDialAction
          icon={<AddIcon />}
          tooltipTitle="New Booking"
          onClick={() => setTabValue(1)}
        />
        <SpeedDialAction
          icon={<FilterIcon />}
          tooltipTitle="Filters"
          onClick={() => setTabValue(1)}
        />
        <SpeedDialAction
          icon={<ExportIcon />}
          tooltipTitle="Export"
          onClick={() => handleExportBookings({})}
        />
        <SpeedDialAction
          icon={<NotificationIcon />}
          tooltipTitle="Send Notifications"
          onClick={() => setIsNotificationModalOpen(true)}
        />
      </SpeedDial>

      {/* Booking Details Modal */}
      <MuiDialog open={isBookingDetailsOpen} onClose={handleCloseBookingDetails} maxWidth="md" fullWidth>
        <Box p={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" fontWeight="bold">Booking Details</Typography>
            <IconButton onClick={handleCloseBookingDetails}>
              <CloseIcon />
            </IconButton>
          </Box>
          
          {selectedBookingDetails && (
            <Box>
              {/* Vehicle Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Vehicle Information</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Vehicle:</strong> {selectedBookingDetails.vehicle?.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedBookingDetails.vehicle?.brand} {selectedBookingDetails.vehicle?.model} ({selectedBookingDetails.vehicle?.year})
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <img 
                        src={selectedBookingDetails.vehicle?.image} 
                        alt={selectedBookingDetails.vehicle?.name}
                        style={{ width: '100px', height: '60px', objectFit: 'cover', borderRadius: '4px' }}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Customer Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Customer Information</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Name:</strong> {selectedBookingDetails.user?.name}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Email:</strong> {selectedBookingDetails.user?.email}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Phone:</strong> {selectedBookingDetails.user?.phone}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Booking Dates & Locations */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Booking Schedule</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Start Date:</strong> {selectedBookingDetails.startDate}
                      </Typography>
                      <Typography variant="body1">
                        <strong>End Date:</strong> {selectedBookingDetails.endDate}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Pickup Time:</strong> {selectedBookingDetails.pickupTime}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Drop-off Time:</strong> {selectedBookingDetails.dropoffTime}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Pickup Location:</strong> {selectedBookingDetails.pickupLocation}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Drop-off Location:</strong> {selectedBookingDetails.dropoffLocation}
                      </Typography>
                      {selectedBookingDetails.specialInstructions && (
                        <Typography variant="body1" sx={{ mt: 1 }}>
                          <strong>Special Instructions:</strong> {selectedBookingDetails.specialInstructions}
                        </Typography>
                      )}
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Payment Information */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Payment Information</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Payment Method:</strong> {selectedBookingDetails.paymentMethod}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Payment Status:</strong> 
                        <Chip 
                          label={selectedBookingDetails.paymentStatus} 
                          color={selectedBookingDetails.paymentStatus === 'Paid' ? 'success' : 'warning'}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body1">
                        <strong>Rental Price:</strong> Rp {selectedBookingDetails.rentalPrice?.toLocaleString()}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Total Price:</strong> Rp {selectedBookingDetails.totalPrice?.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Add-ons & Accessories */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Add-ons & Accessories</Typography>
                  {selectedBookingDetails.addons && selectedBookingDetails.addons.length > 0 ? (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom>Selected Add-ons:</Typography>
                      {selectedBookingDetails.addons.map((addon, index) => (
                        <Typography key={index} variant="body2">
                          • {addon.name} (Qty: {addon.quantity}) - Rp {addon.price?.toLocaleString()}
                        </Typography>
                      ))}
                      <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Included Accessories:</Typography>
                      {selectedBookingDetails.accessories?.map((accessory, index) => (
                        <Chip key={index} label={accessory} size="small" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">No add-ons selected</Typography>
                  )}
                </CardContent>
              </Card>

              {/* Booking Status & Actions */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">Booking Status & Actions</Typography>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Typography variant="body1" sx={{ mr: 2 }}>
                      <strong>Status:</strong>
                    </Typography>
                    <Chip 
                      label={selectedBookingDetails.status} 
                      color={
                        selectedBookingDetails.status === 'confirmed' ? 'success' : 
                        selectedBookingDetails.status === 'pending' ? 'warning' : 
                        selectedBookingDetails.status === 'cancelled' ? 'error' : 'default'
                      }
                    />
                  </Box>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Button variant="contained" color="success" size="small">
                      Approve
                    </Button>
                    <Button variant="outlined" color="error" size="small">
                      Reject
                    </Button>
                    <Button variant="outlined" size="small">
                      Edit
                    </Button>
                    <Button variant="outlined" size="small">
                      Contact Customer
                    </Button>
                  </Box>
                </CardContent>
              </Card>

              {/* Notes */}
              {selectedBookingDetails.notes && (
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom color="primary">Notes</Typography>
                    <Typography variant="body2">{selectedBookingDetails.notes}</Typography>
                  </CardContent>
                </Card>
              )}
            </Box>
          )}
        </Box>
      </MuiDialog>
    </Container>
  );
};