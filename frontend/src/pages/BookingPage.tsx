import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Container, Box, Typography, Alert, CircularProgress } from '@mui/material';
import { Vehicle } from '../types';
import BookingFlow from '../components/BookingFlow';
import * as VehicleService from '../services/VehicleService';
import { useAuth } from '../contexts/AuthContext';

const BookingPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Read booking info from location.state
  const state = location.state as {
    vehicleId?: string;
    pickupLocation?: string;
    dropOffLocation?: string;
    from?: string;
    to?: string;
  };

  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingFlowOpen, setBookingFlowOpen] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/signin');
      return;
    }
    if (!state || !state.vehicleId || typeof state.vehicleId !== 'string' || !state.vehicleId.match(/^[0-9a-fA-F-]{36}$/)) {
      navigate('/vehicles');
      return;
    }
    // Fetch vehicle by ID
    VehicleService.getVehicleById(state.vehicleId)
      .then((v) => {
        setVehicle(v);
        setLoading(false);
        setBookingFlowOpen(true); // Open modal by default
      })
      .catch(() => {
        setError('Vehicle not found.');
        setLoading(false);
      });
  }, [state, user, navigate]);

  if (loading) return <div>Loading...</div>;
  if (error) return <Alert severity="error">{error}</Alert>;
  if (!vehicle) return <Alert severity="error">Vehicle not found.</Alert>;

  return (
    <BookingFlow
      open={bookingFlowOpen}
      onClose={() => setBookingFlowOpen(false)}
      vehicle={vehicle}
      onBookingComplete={() => {}}
      pickupLocation={state.pickupLocation}
      dropOffLocation={state.dropOffLocation}
      from={state.from ? new Date(state.from) : null}
      to={state.to ? new Date(state.to) : null}
    />
  );
};

export default BookingPage;