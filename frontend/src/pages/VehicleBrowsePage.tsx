import React, { useState, useEffect } from 'react';
import { 
  <PERSON>rid, 
  Typography, 
  Card, 
  CardMedia, 
  CardContent, 
  CardActions, 
  Button,
  Alert,
  Skeleton
} from '@mui/material';
import axios from 'axios';
import VehicleFilter from '../components/VehicleFilter';
import ListedVehicleService, { ListedVehicle } from '../services/ListedVehicleService';
import EmptyListingsCTA from '../components/EmptyListingsCTA';

interface Vehicle {
  id: string;
  name: string;
  brand: string;
  model: string;
  type: string;
  dailyRate: number;
  hasInsurance: boolean;
  images: string[];
  description?: string;
  location?: {
    city: string;
  };
  rating?: number;
  reviews?: number;
  features?: string[];
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  message?: string;
}

export const VehicleBrowsePage: React.FC = () => {
  const [vehicles, setVehicles] = useState<ListedVehicle[]>([]);
  const [hasListings, setHasListings] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<{
    type?: string;
    minPrice?: number;
    maxPrice?: number;
    insurance?: boolean;
    addOns?: string[];
  }>({});

  useEffect(() => {
    const fetchVehicles = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('🔍 Fetching real listed vehicles with filters:', filters);

        // Convert filters to ListedVehicleService format
        const searchFilters = {
          category: filters.type,
          minPrice: filters.minPrice,
          maxPrice: filters.maxPrice
        };

        // Get real listed vehicles
        const response = await ListedVehicleService.searchVehicles(
          undefined, // no text query
          searchFilters,
          50 // limit
        );

        console.log('📊 Listed vehicles response:', response);

        if (response.success && response.hasListings) {
          console.log('✅ Found real listings:', response.data);
          setVehicles(response.data);
          setHasListings(true);
        } else {
          console.log('❌ No real listings found - will show CTA');
          setVehicles([]);
          setHasListings(false);
        }
      } catch (error) {
        console.error('❌ Failed to fetch vehicles', error);
        setError('Failed to fetch vehicles. Please try again later.');
        setHasListings(false);
        setVehicles([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVehicles();
  }, [filters]);

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} md={3}>
        <VehicleFilter 
          onFilterChange={(newFilters) => setFilters(newFilters)} 
        />
      </Grid>
      <Grid item xs={12} md={9}>
        <Typography variant="h4" gutterBottom>
          Available Vehicles
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {isLoading ? (
          <Grid container spacing={2}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <Grid item xs={12} sm={6} md={4} key={item}>
                <Card>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton />
                    <Skeleton width="60%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : vehicles.length === 0 && !error ? (
          <EmptyListingsCTA variant="search" />
        ) : (
          <Grid container spacing={2}>
            {vehicles.map(vehicle => (
              <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
                <Card>
                  <CardMedia
                    component="img"
                    height="200"
                    image={vehicle.images?.[0] || '/default-vehicle.jpg'}
                    alt={`${vehicle.make} ${vehicle.model}`}
                  />
                  <CardContent>
                    <Typography variant="h6">{vehicle.make} {vehicle.model}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {vehicle.year} • {vehicle.category}
                    </Typography>
                    <Typography variant="body2">
                      Daily Rate: ₹{vehicle.dailyRate}
                    </Typography>
                    <Typography variant="body2">
                      Provider: {vehicle.provider?.businessName || 'Unknown'}
                    </Typography>
                    {vehicle.features && vehicle.features.length > 0 && (
                      <Typography variant="body2">
                        Features: {vehicle.features.join(', ')}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions>
                    <Button size="small" color="primary">
                      Book Now
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Grid>
    </Grid>
  );
};

export default VehicleBrowsePage;
