import React from 'react'
import { Container, Typography, Box, Avatar, Paper, Divider } from '@mui/material'
import { mockUsers } from '../utils/mockData'
import Layout from '../components/Layout'

const user = mockUsers[0];

const Profile: React.FC = () => {
  return (
    <Layout>
      <Container maxWidth="sm" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Box display="flex" flexDirection="column" alignItems="center" mb={2}>
            <Avatar src={user.profileImage} sx={{ width: 80, height: 80, mb: 2 }}>
              {user.name.charAt(0)}
            </Avatar>
            <Typography variant="h5" gutterBottom>
              {user.name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {user.email}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {user.phone}
            </Typography>
          </Box>
          <Divider sx={{ my: 2 }} />
          <Typography variant="subtitle1" color="text.secondary">
            Profile Status: {user.verified ? 'Verified' : 'Unverified'}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Account Type: {user.type.charAt(0).toUpperCase() + user.type.slice(1)}
          </Typography>
        </Paper>
      </Container>
    </Layout>
  )
}

export default Profile