import React, { useEffect, useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Grid, 
  Card, 
  CardContent, 
  Chip, 
  Button, 
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import { supabase } from '../utils/supabaseClient';
import { useNavigate } from 'react-router-dom';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import LocationOnIcon from '@mui/icons-material/LocationOn';

interface Booking {
  id: string;
  vehicleId: string;
  userId: string;
  startDate: string;
  endDate: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  totalPrice: number;
  vehicle: {
    name: string;
    imageUrl: string;
    location: string;
    category: string;
  };
}

const MyBookingsPage = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    async function fetchBookings() {
      try {
        setLoading(true);
        
        // Get current user
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          navigate('/signin');
          return;
        }
        
        // Fetch bookings for this user
        const { data, error } = await supabase
          .from('bookings')
          .select(`
            *,
            vehicle:vehicleId (
              name,
              imageUrl,
              location,
              category
            )
          `)
          .eq('userId', session.user.id)
          .order('createdAt', { ascending: false });
          
        if (error) throw error;
        
        setBookings(data || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load bookings');
      } finally {
        setLoading(false);
      }
    }
    
    fetchBookings();
  }, [navigate]);

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ID', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'info';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Generate mock data if no real bookings exist
  const getBookingsToDisplay = () => {
    if (bookings.length > 0) return bookings;
    
    // Sample data for demonstration
    return [
      {
        id: '1',
        vehicleId: 'v1',
        userId: 'u1',
        startDate: '2024-07-15',
        endDate: '2024-07-20',
        status: 'confirmed' as 'confirmed',
        totalPrice: 1250000,
        vehicle: {
          name: 'Honda PCX 160',
          imageUrl: 'https://example.com/images/pcx.jpg',
          location: 'Jakarta Selatan',
          category: 'motorcycle'
        }
      },
      {
        id: '2',
        vehicleId: 'v2',
        userId: 'u1',
        startDate: '2024-06-01',
        endDate: '2024-06-05',
        status: 'completed' as 'completed',
        totalPrice: 2500000,
        vehicle: {
          name: 'Toyota Avanza',
          imageUrl: 'https://example.com/images/avanza.jpg',
          location: 'Jakarta Pusat',
          category: 'car'
        }
      },
      {
        id: '3',
        vehicleId: 'v3',
        userId: 'u1',
        startDate: '2024-07-25',
        endDate: '2024-07-28',
        status: 'pending' as 'pending',
        totalPrice: 1800000,
        vehicle: {
          name: 'Yamaha NMAX',
          imageUrl: 'https://example.com/images/nmax.jpg',
          location: 'Bandung',
          category: 'motorcycle'
        }
      }
    ];
  };
  
  const displayBookings = getBookingsToDisplay();

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 8 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Bookings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          View and manage your vehicle rentals
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {displayBookings.length === 0 ? (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                You don't have any bookings yet
              </Typography>
              <Button 
                variant="contained" 
                color="primary"
                onClick={() => navigate('/vehicles')}
                sx={{ mt: 2 }}
              >
                Browse Vehicles
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {displayBookings.map((booking) => (
                <Grid item xs={12} key={booking.id}>
                  <Card sx={{ 
                    display: 'flex', 
                    flexDirection: { xs: 'column', sm: 'row' },
                    borderLeft: 6,
                    borderColor: `${getStatusColor(booking.status)}.main`
                  }}>
                    <Box 
                      sx={{ 
                        width: { xs: '100%', sm: 200 }, 
                        height: { xs: 200, sm: '100%' },
                        backgroundImage: `url(${booking.vehicle.imageUrl || 'https://via.placeholder.com/200x200?text=Vehicle+Image'})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    />
                    <CardContent sx={{ flex: 1, p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2, flexDirection: { xs: 'column', md: 'row' } }}>
                        <Typography variant="h6" component="h2">
                          {booking.vehicle.name}
                        </Typography>
                        <Chip 
                          label={booking.status.toUpperCase()} 
                          color={getStatusColor(booking.status) as any}
                          size="small"
                          sx={{ mt: { xs: 1, md: 0 } }}
                        />
                      </Box>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <DirectionsCarIcon sx={{ mr: 1, color: 'text.secondary' }} fontSize="small" />
                            <Typography variant="body2">
                              {booking.vehicle.category.charAt(0).toUpperCase() + booking.vehicle.category.slice(1)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationOnIcon sx={{ mr: 1, color: 'text.secondary' }} fontSize="small" />
                            <Typography variant="body2">
                              {booking.vehicle.location}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarTodayIcon sx={{ mr: 1, color: 'text.secondary' }} fontSize="small" />
                            <Typography variant="body2">
                              {formatDate(booking.startDate)} - {formatDate(booking.endDate)}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <AttachMoneyIcon sx={{ mr: 1, color: 'text.secondary' }} fontSize="small" />
                            <Typography variant="body2">
                              Rp {booking.totalPrice.toLocaleString('id-ID')}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                      
                      <Divider sx={{ my: 2 }} />
                      
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                        <Button 
                          variant="outlined" 
                          size="small"
                          onClick={() => navigate(`/booking/${booking.id}`)}
                        >
                          View Details
                        </Button>
                        {booking.status === 'confirmed' && (
                          <Button 
                            variant="contained" 
                            color="primary"
                            size="small"
                          >
                            Extend Booking
                          </Button>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </>
      )}
    </Container>
  );
};

export default MyBookingsPage; 