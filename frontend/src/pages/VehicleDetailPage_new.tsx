import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Rating,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  ImageList,
  ImageListItem,
  Snackbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  LocationOn,
  Phone,
  WhatsApp,
  CheckCircle,
  LocalGasStation,
  Settings,
  Speed,
  CalendarToday,
  AttachMoney,
  Security,
  DeliveryDining,
  BookOnline
} from '@mui/icons-material';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { Vehicle, VehicleCategory } from '../types';
import * as VehicleService from '../services/VehicleService';
import BookingFlow from '../components/BookingFlow';
import { useAuth } from '../contexts/AuthContext';

const VehicleDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [bookingFlowOpen, setBookingFlowOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({ 
    open: false, 
    message: '', 
    severity: 'success' as 'success' | 'error' 
  });

  useEffect(() => {
    if (id) {
      fetchVehicle(id);
    }
  }, [id]);

  import { mapSimpleToDetailedVehicle } from '../utils/vehicleMapper';

  const fetchVehicle = async (vehicleId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await VehicleService.getVehicle(vehicleId);
      const detailedVehicle = mapSimpleToDetailedVehicle(result.data);
      setVehicle(detailedVehicle);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load vehicle');
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = () => {
    if (!user) {
      setSnackbar({
        open: true,
        message: 'Please sign in to book this vehicle',
        severity: 'error'
      });
      navigate('/auth');
      return;
    }
    setBookingFlowOpen(true);
  };

  const handleBookingComplete = () => {
    setBookingFlowOpen(false);
    setSnackbar({
      open: true,
      message: 'Booking confirmed successfully!',
      severity: 'success'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getCategoryName = (category: VehicleCategory) => {
    switch (category) {
      case VehicleCategory.SmallScooter: return 'Small Scooter';
      case VehicleCategory.MediumScooter: return 'Medium Scooter';
      case VehicleCategory.LargeScooter: return 'Large Scooter';
      case VehicleCategory.ElectricScooter: return 'Electric Scooter';
      case VehicleCategory.Motorcycle: return 'Motorcycle';
      case VehicleCategory.ElectricMotorcycle: return 'Electric Motorcycle';
      default: return category;
    }
  };

  const getVehicleName = (vehicle: Vehicle) => {
    return `${vehicle.make} ${vehicle.model} ${vehicle.year}`;
  };

  const isVehicleAvailable = (vehicle: Vehicle) => {
    return vehicle.availableQuantity > 0 && vehicle.active;
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading vehicle details...
        </Typography>
      </Container>
    );
  }

  if (error || !vehicle) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error || 'Vehicle not found'}</Alert>
        <Box sx={{ mt: 2 }}>
                          <Button component={Link} to="/vehicles" variant="contained">
            Browse All Vehicles
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumb */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" color="text.secondary">
          <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>Home</Link>
          {' > '}
                          <Link to="/vehicles" style={{ textDecoration: 'none', color: 'inherit' }}>Search</Link>
          {' > '}
          {getVehicleName(vehicle)}
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Image Gallery */}
        <Grid item xs={12} md={8}>
          <Card>
            <Box sx={{ position: 'relative' }}>
              <img
                src={vehicle.images[selectedImage] || vehicle.images[0] || `https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800`}
                alt={getVehicleName(vehicle)}
                style={{
                  width: '100%',
                  height: '400px',
                  objectFit: 'cover'
                }}
              />
              <Chip
                label={isVehicleAvailable(vehicle) ? 'Available' : 'Unavailable'}
                color={isVehicleAvailable(vehicle) ? 'success' : 'error'}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16,
                  fontWeight: 'bold'
                }}
              />
              <Chip
                label={getCategoryName(vehicle.category)}
                color="primary"
                sx={{
                  position: 'absolute',
                  top: 16,
                  right: 16
                }}
              />
            </Box>
            
            {/* Thumbnail Images */}
            {vehicle.images.length > 1 && (
              <Box sx={{ p: 2 }}>
                <ImageList sx={{ width: '100%', height: 120 }} cols={4} rowHeight={120}>
                  {vehicle.images.map((image, index) => (
                    <ImageListItem 
                      key={index}
                      sx={{ 
                        cursor: 'pointer',
                        border: selectedImage === index ? '2px solid #1976d2' : '2px solid transparent',
                        borderRadius: 1
                      }}
                      onClick={() => setSelectedImage(index)}
                    >
                      <img
                        src={image}
                        alt={`${getVehicleName(vehicle)} ${index + 1}`}
                        style={{ objectFit: 'cover' }}
                        loading="lazy"
                      />
                    </ImageListItem>
                  ))}
                </ImageList>
              </Box>
            )}
          </Card>

          {/* Description */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Description
              </Typography>
              <Typography variant="body1" paragraph>
                {vehicle.description}
              </Typography>
            </CardContent>
          </Card>

          {/* Features */}
          {vehicle.features && vehicle.features.length > 0 && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Features
                </Typography>
                <List>
                  {vehicle.features.map((feature, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckCircle color="success" />
                      </ListItemIcon>
                      <ListItemText primary={feature.name} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          {/* Add-ons */}
          {vehicle.addOns && vehicle.addOns.length > 0 && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Available Add-ons
                </Typography>
                <List>
                  {vehicle.addOns.map((addon, index) => (
                    <ListItem key={index}>
                      <ListItemText 
                        primary={addon.name}
                        secondary={`${formatPrice(addon.dailyRate)}/day`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Booking Panel */}
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 24 }}>
            <CardContent>
              <Typography variant="h4" component="h1" gutterBottom>
                {getVehicleName(vehicle)}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  {vehicle.location.city}, {vehicle.location.state}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Vehicle Specs */}
              <Typography variant="h6" gutterBottom>
                Specifications
              </Typography>
              <List dense>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <CalendarToday fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Year: ${vehicle.year}`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <Speed fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Engine: ${vehicle.engineSize}cc`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <Settings fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Transmission: ${vehicle.transmission}`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <LocalGasStation fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Fuel: ${vehicle.fuelType}`} />
                </ListItem>
              </List>

              <Divider sx={{ my: 2 }} />

              {/* Pricing */}
              <Typography variant="h6" gutterBottom>
                Pricing
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                  {formatPrice(vehicle.dailyRate)}
                  <Typography component="span" variant="body2" color="text.secondary">
                    /day
                  </Typography>
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Weekly: {formatPrice(vehicle.weeklyRate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monthly: {formatPrice(vehicle.monthlyRate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Security Deposit: {formatPrice(vehicle.securityDeposit)}
                </Typography>
              </Box>

              {/* Delivery Info */}
              {vehicle.deliveryAvailable && (
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DeliveryDining sx={{ mr: 1, color: 'success.main' }} />
                    <Typography variant="body2" color="success.main">
                      Delivery Available
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Fee: {formatPrice(vehicle.deliveryFee)} (within {vehicle.deliveryRadius}km)
                  </Typography>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Booking Button */}
              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={handleBookNow}
                disabled={!isVehicleAvailable(vehicle)}
                startIcon={<BookOnline />}
                sx={{ 
                  mb: 2,
                  backgroundColor: '#FF5F57', 
                  '&:hover': { backgroundColor: '#e54e47' },
                  py: 1.5
                }}
              >
                {isVehicleAvailable(vehicle) ? 'Book Now' : 'Unavailable'}
              </Button>

              {!isVehicleAvailable(vehicle) && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  This vehicle is currently unavailable. Check back later or browse similar vehicles.
                </Alert>
              )}

              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                Minimum rental: {vehicle.minimumRentalDays} day{vehicle.minimumRentalDays > 1 ? 's' : ''}
                {vehicle.maximumRentalDays && (
                  <>
                    <br />
                    Maximum rental: {vehicle.maximumRentalDays} day{vehicle.maximumRentalDays > 1 ? 's' : ''}
                  </>
                )}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Booking Flow Modal */}
      <BookingFlow
        open={bookingFlowOpen}
        onClose={() => setBookingFlowOpen(false)}
        vehicle={vehicle}
        onBookingComplete={handleBookingComplete}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        message={snackbar.message}
      />
    </Container>
  );
};

export default VehicleDetailPage;
