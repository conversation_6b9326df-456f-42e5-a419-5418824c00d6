import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Rating,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  ImageList,
  ImageListItem,
  Snackbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack
} from '@mui/material';
import {
  LocationOn,
  Phone,
  WhatsApp,
  CheckCircle,
  LocalGasStation,
  Settings,
  Speed,
  CalendarToday,
  AttachMoney,
  Security,
  DeliveryDining,
  BookOnline,
  Help,
  Schedule,
  Message,
  Support
} from '@mui/icons-material';
import { useParams, Link, useNavigate, useLocation } from 'react-router-dom';
import { Vehicle, VehicleCategory } from '../types';
import { mockVehicles } from '../utils/mockData';
import BookingFlow from '../components/BookingFlow';
import HelpSupportModal from '../components/HelpSupportModal';
import BookingExtensionModal from '../components/BookingExtensionModal';
import MessagingModal from '../components/MessagingModal';
import { useAuth } from '../contexts/AuthContext';
import { mapSimpleToDetailedVehicle } from '../utils/vehicleMapper';
import Layout from '../components/Layout';
import apiService from '../services/apiService';

const VehicleDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const fromBooking = params.get('fromBooking') === 'true';
  
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [bookingFlowOpen, setBookingFlowOpen] = useState(false);
  const [helpSupportOpen, setHelpSupportOpen] = useState(false);
  const [bookingExtensionOpen, setBookingExtensionOpen] = useState(false);
  const [messagingOpen, setMessagingOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({ 
    open: false, 
    message: '', 
    severity: 'success' as 'success' | 'error' 
  });

  useEffect(() => {
    if (!id || typeof id !== 'string' || !id.match(/^[0-9a-fA-F-]{36}$/)) {
      setError('Invalid vehicle ID');
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    apiService.getVehicleById(id)
      .then((response) => {
        // Map backend response to expected Vehicle shape
        const v = response.data;
        setVehicle({
          ...v,
          images: (v.images && Array.isArray(v.images))
            ? v.images.map(img => img.url || img) : [],
        });
        setError(null);
      })
      .catch((err) => {
        // Fall back to mock data if backend doesn't have the vehicle
        console.log('Vehicle not found in backend, trying mock data...');
        const mockVehicle = mockVehicles.find(v => v.id === id);
        if (mockVehicle) {
          setVehicle(mockVehicle);
          setError(null);
        } else {
          setVehicle(null);
          setError('Vehicle not found');
        }
      })
      .finally(() => setLoading(false));
  }, [id]);

  const handleBookNow = () => {
    if (!user) {
      setSnackbar({
        open: true,
        message: 'Please sign in to book this vehicle',
        severity: 'error'
      });
      navigate('/signin'); // Redirect to sign-in page
      return;
    }
    setBookingFlowOpen(true); // Open booking flow modal
  };

  const handleBookingComplete = () => {
    setBookingFlowOpen(false);
    setSnackbar({
      open: true,
      message: 'Booking confirmed successfully!',
      severity: 'success'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getCategoryName = (category: VehicleCategory) => {
    switch (category) {
      case VehicleCategory.SmallScooter: return 'Small Scooter';
      case VehicleCategory.MediumScooter: return 'Medium Scooter';
      case VehicleCategory.LargeScooter: return 'Large Scooter';
      case VehicleCategory.ElectricScooter: return 'Electric Scooter';
      case VehicleCategory.Motorcycle: return 'Motorcycle';
      case VehicleCategory.ElectricMotorcycle: return 'Electric Motorcycle';
      default: return category;
    }
  };

  const getVehicleName = (vehicle: Vehicle) => {
    // Handle both backend structure (brand, model, year) and frontend structure (specifications)
    if (vehicle.brand && vehicle.model && vehicle.year) {
      return `${vehicle.brand} ${vehicle.model} ${vehicle.year}`;
    }
    if (vehicle.specifications?.model && vehicle.specifications?.year) {
      return `${vehicle.specifications.model} ${vehicle.specifications.year}`;
    }
    return vehicle.name || 'Vehicle';
  };

  const isVehicleAvailable = (vehicle: Vehicle) => {
    // Handle both backend structure (availableUnits) and frontend structure (status)
    if (vehicle.availableUnits !== undefined) {
      return vehicle.availableUnits > 0;
    }
    if (vehicle.status) {
      return vehicle.status === 'available';
    }
    return true; // Default to available if we can't determine
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading vehicle details...
          </Typography>
        </Container>
      </Layout>
    );
  }

  if (error || !vehicle) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="error">{error || 'Vehicle not found'}</Alert>
          <Box sx={{ mt: 2 }}>
                            <Button component={Link} to="/vehicles" variant="contained">
              Browse All Vehicles
            </Button>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumb */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" color="text.secondary">
          <Link to="/" style={{ textDecoration: 'none', color: 'inherit' }}>Home</Link>
          {' > '}
                          <Link to="/vehicles" style={{ textDecoration: 'none', color: 'inherit' }}>Search</Link>
          {' > '}
          {getVehicleName(vehicle)}
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Image Gallery */}
        <Grid item xs={12} md={8}>
          <Card>
            <Box sx={{ position: 'relative' }}>
              <img
                src={vehicle.images[selectedImage] || vehicle.images[0] || `https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800`}
                alt={getVehicleName(vehicle)}
                style={{
                  width: '100%',
                  height: '400px',
                  objectFit: 'cover'
                }}
              />
              <Chip
                label={isVehicleAvailable(vehicle) ? 'Available' : 'Unavailable'}
                color={isVehicleAvailable(vehicle) ? 'success' : 'error'}
                sx={{
                  position: 'absolute',
                  top: 16,
                  left: 16,
                  fontWeight: 'bold'
                }}
              />
              <Chip
                label={getCategoryName(vehicle.category as VehicleCategory)}
                color="primary"
                sx={{
                  position: 'absolute',
                  top: 16,
                  right: 16
                }}
              />
            </Box>
            
            {/* Thumbnail Images */}
            {vehicle.images.length > 1 && (
              <Box sx={{ p: 2 }}>
                <ImageList sx={{ width: '100%', height: 120 }} cols={4} rowHeight={120}>
                  {vehicle.images.map((image, index) => (
                    <ImageListItem 
                      key={index}
                      sx={{ 
                        cursor: 'pointer',
                        border: selectedImage === index ? '2px solid #1976d2' : '2px solid transparent',
                        borderRadius: 1
                      }}
                      onClick={() => setSelectedImage(index)}
                    >
                      <img
                        src={image}
                        alt={`${getVehicleName(vehicle)} ${index + 1}`}
                        style={{ objectFit: 'cover' }}
                        loading="lazy"
                      />
                    </ImageListItem>
                  ))}
                </ImageList>
              </Box>
            )}
          </Card>

          {/* Description */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Description
              </Typography>
              <Typography variant="body1" paragraph>
                {vehicle.description}
              </Typography>
            </CardContent>
          </Card>

          {/* Features */}
          {vehicle.features && vehicle.features.length > 0 && (
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Features
                </Typography>
                <List>
                  {vehicle.features.map((feature, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckCircle color="success" />
                      </ListItemIcon>
                <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          {/* Add-ons */}
              {/* Add-ons section removed because addOns does not exist on Vehicle */}
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Available Add-ons
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="No add-ons available for this vehicle" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
        </Grid>

        {/* Booking Panel */}
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 24 }}>
            <CardContent>
              <Typography variant="h4" component="h1" gutterBottom>
                {getVehicleName(vehicle)}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  {vehicle.location?.city || 'Location not specified'}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Vehicle Specs */}
              <Typography variant="h6" gutterBottom>
                Specifications
              </Typography>
              <List dense>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <CalendarToday fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Year: ${vehicle.year || vehicle.specifications?.year || 'N/A'}`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <Speed fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Engine: ${vehicle.engine || vehicle.engineSize || 'N/A'}`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <Settings fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Transmission: ${vehicle.transmission}`} />
                </ListItem>
                <ListItem disablePadding>
                  <ListItemIcon>
                    <LocalGasStation fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={`Fuel: ${vehicle.fuelType}`} />
                </ListItem>
              </List>

              <Divider sx={{ my: 2 }} />

              {/* Pricing */}
              <Typography variant="h6" gutterBottom>
                Pricing
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" color="primary" gutterBottom>
                  {vehicle.dailyRate ? formatPrice(vehicle.dailyRate) : formatPrice(vehicle.pricePerDay || 0)}/day
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Weekly: {formatPrice(vehicle.weeklyRate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monthly: {formatPrice(vehicle.monthlyRate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Security Deposit: {formatPrice(vehicle.securityDeposit)}
                </Typography>
              </Box>

              {/* Delivery Info */}
              {vehicle.deliveryAvailable && (
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DeliveryDining sx={{ mr: 1, color: 'success.main' }} />
                    <Typography variant="body2" color="success.main">
                      Delivery Available
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Fee: {formatPrice(vehicle.deliveryFee)} (within {vehicle.deliveryRadius}km)
                  </Typography>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Booking Button */}
              {!fromBooking ? (
                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  onClick={handleBookNow}
                  disabled={!isVehicleAvailable(vehicle)}
                  startIcon={<BookOnline />}
                  data-testid="book-now"
                  sx={{
                    mb: 2,
                    backgroundColor: '#FF5F57',
                    '&:hover': { backgroundColor: '#e54e47' },
                    py: 1.5
                  }}
                >
                  {isVehicleAvailable(vehicle) ? 'Book Now' : 'Unavailable'}
                </Button>
              ) : (
                <>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    You have already booked this vehicle.
                  </Alert>
                  
                  {/* Booked Vehicle Actions */}
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Booking Actions
                  </Typography>
                  
                  <Stack spacing={2} sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<Help />}
                      onClick={() => setHelpSupportOpen(true)}
                      sx={{ 
                        borderColor: '#FF5F57',
                        color: '#FF5F57',
                        '&:hover': { 
                          borderColor: '#e54e47',
                          backgroundColor: 'rgba(255, 95, 87, 0.04)'
                        }
                      }}
                    >
                      Help & Support
                    </Button>
                    
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<Schedule />}
                      onClick={() => setBookingExtensionOpen(true)}
                      sx={{ 
                        borderColor: '#FF5F57',
                        color: '#FF5F57',
                        '&:hover': { 
                          borderColor: '#e54e47',
                          backgroundColor: 'rgba(255, 95, 87, 0.04)'
                        }
                      }}
                    >
                      Extend Booking
                    </Button>
                    
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<Message />}
                      onClick={() => setMessagingOpen(true)}
                      sx={{ 
                        borderColor: '#FF5F57',
                        color: '#FF5F57',
                        '&:hover': { 
                          borderColor: '#e54e47',
                          backgroundColor: 'rgba(255, 95, 87, 0.04)'
                        }
                      }}
                    >
                      Message Owner
                    </Button>
                  </Stack>
                </>
              )}

              {!isVehicleAvailable(vehicle) && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  This vehicle is currently unavailable. Check back later or browse similar vehicles.
                </Alert>
              )}

              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                Minimum rental: {vehicle.minimumRentalDays} day{vehicle.minimumRentalDays > 1 ? 's' : ''}
                {vehicle.maximumRentalDays && (
                  <>
                    <br />
                    Maximum rental: {vehicle.maximumRentalDays} day{vehicle.maximumRentalDays > 1 ? 's' : ''}
                  </>
                )}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Booking Flow Modal */}
      <BookingFlow
        open={bookingFlowOpen}
        onClose={() => setBookingFlowOpen(false)}
        vehicle={vehicle}
        onBookingComplete={handleBookingComplete}
      />

      {/* Help & Support Modal */}
      <HelpSupportModal
        open={helpSupportOpen}
        onClose={() => setHelpSupportOpen(false)}
        vehicleId={vehicle.id}
        bookingId={params.get('bookingId') || undefined}
      />

      {/* Booking Extension Modal */}
      <BookingExtensionModal
        open={bookingExtensionOpen}
        onClose={() => setBookingExtensionOpen(false)}
        currentEndDate={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)} // Example: 7 days from now
        vehicleId={vehicle.id}
        bookingId={params.get('bookingId') || undefined}
        dailyRate={vehicle.dailyRate}
      />

      {/* Messaging Modal */}
      <MessagingModal
        open={messagingOpen}
        onClose={() => setMessagingOpen(false)}
        vehicleId={vehicle.id}
        bookingId={params.get('bookingId') || undefined}
        ownerName="Vehicle Owner"
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        message={snackbar.message}
      />
    </Container>
    </Layout>
  );
};

export default VehicleDetailPage;
