import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Typo<PERSON>, <PERSON>ton, Box, Alert, Paper, CircularProgress } from '@mui/material';
import { NetworkDiagnosticsService } from '../utils/networkDiagnostics';

const NetworkTestPage = () => {
  const [diagnostics, setDiagnostics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const runTests = async () => {
    setLoading(true);
    setTestResults([]);
    
    const results: string[] = [];
    
    try {
      // Test 1: Basic fetch to Supabase
      results.push('🔍 Testing basic Supabase connectivity...');
      setTestResults([...results]);
      
      try {
        const response = await fetch('https://rocxjzukyqelvuyltrfq.supabase.co', {
          method: 'HEAD',
          mode: 'no-cors'
        });
        results.push('✅ Basic connectivity: SUCCESS');
      } catch (error: any) {
        results.push(`❌ Basic connectivity: FAILED - ${error.message}`);
      }
      setTestResults([...results]);

      // Test 2: Supabase Auth Settings
      results.push('🔍 Testing Supabase Auth API...');
      setTestResults([...results]);

      try {
        const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
        const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

        results.push(`🔍 Using URL: ${supabaseUrl}`);
        results.push(`🔍 Using Key: ${supabaseKey ? supabaseKey.substring(0, 30) + '...' : 'NOT SET'}`);
        setTestResults([...results]);

        const response = await fetch(`${supabaseUrl}/auth/v1/settings`, {
          method: 'GET',
          headers: {
            'apikey': supabaseKey,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        results.push(`🔍 Response Status: ${response.status}`);
        results.push(`🔍 Response Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
        setTestResults([...results]);

        if (response.ok) {
          const data = await response.json();
          results.push('✅ Supabase Auth API: SUCCESS');
          results.push(`📊 Auth settings received: ${Object.keys(data).join(', ')}`);
        } else {
          const errorText = await response.text();
          results.push(`❌ Supabase Auth API: FAILED - Status ${response.status}`);
          results.push(`❌ Error Response: ${errorText}`);
        }
      } catch (error: any) {
        results.push(`❌ Supabase Auth API: FAILED - ${error.message}`);
        results.push(`❌ Error Type: ${error.name}`);
        results.push(`❌ Error Stack: ${error.stack?.substring(0, 200)}...`);
      }
      setTestResults([...results]);

      // Test 3: DNS Resolution
      results.push('🔍 Testing DNS resolution...');
      setTestResults([...results]);
      
      try {
        const img = new Image();
        img.onload = () => results.push('✅ DNS Resolution: SUCCESS');
        img.onerror = () => results.push('❌ DNS Resolution: FAILED');
        img.src = 'https://rocxjzukyqelvuyltrfq.supabase.co/favicon.ico?' + Date.now();
      } catch (error: any) {
        results.push(`❌ DNS Resolution: FAILED - ${error.message}`);
      }

      // Test 4: Environment Variables
      results.push('🔍 Checking environment variables...');
      results.push(`VITE_SUPABASE_URL: ${import.meta.env.VITE_SUPABASE_URL || 'NOT SET'}`);
      results.push(`VITE_SUPABASE_ANON_KEY: ${import.meta.env.VITE_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'}`);
      setTestResults([...results]);

      // Test 5: Run full diagnostics
      results.push('🔍 Running comprehensive diagnostics...');
      setTestResults([...results]);
      
      const fullDiagnostics = await NetworkDiagnosticsService.runDiagnostics();
      setDiagnostics(fullDiagnostics);
      results.push('✅ Comprehensive diagnostics completed');
      setTestResults([...results]);

    } catch (error: any) {
      results.push(`❌ Test suite failed: ${error.message}`);
      setTestResults([...results]);
    }
    
    setLoading(false);
  };

  const testDirectAuth = async () => {
    setTestResults(['🔍 Testing direct authentication...']);
    
    try {
      const response = await fetch('https://rocxjzukyqelvuyltrfq.supabase.co/auth/v1/token?grant_type=password', {
        method: 'POST',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Password1234'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(prev => [...prev, '✅ Direct auth: SUCCESS', `Token received: ${data.access_token ? 'YES' : 'NO'}`]);
      } else {
        const errorData = await response.text();
        setTestResults(prev => [...prev, `❌ Direct auth: FAILED - ${response.status}`, `Error: ${errorData}`]);
      }
    } catch (error: any) {
      setTestResults(prev => [...prev, `❌ Direct auth: FAILED - ${error.message}`]);
    }
  };

  useEffect(() => {
    // Auto-run basic tests on component mount
    runTests();
  }, []);

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🔧 Network Connectivity Test
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <Button 
          variant="contained" 
          onClick={runTests} 
          disabled={loading}
          sx={{ mr: 2 }}
        >
          {loading ? <CircularProgress size={24} /> : 'Run Full Tests'}
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={testDirectAuth}
          disabled={loading}
        >
          Test Direct Auth
        </Button>
      </Box>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test Results
        </Typography>
        
        <Box sx={{ maxHeight: 400, overflow: 'auto', fontFamily: 'monospace', fontSize: '0.875rem' }}>
          {testResults.map((result, index) => (
            <div key={index} style={{ marginBottom: '4px' }}>
              {result}
            </div>
          ))}
        </Box>
      </Paper>

      {diagnostics && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Comprehensive Diagnostics
          </Typography>
          
          <Alert severity={diagnostics.supabaseReachable ? 'success' : 'error'} sx={{ mb: 2 }}>
            Supabase Status: {diagnostics.supabaseReachable ? 'REACHABLE' : 'UNREACHABLE'}
          </Alert>
          
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
            <div>DNS Resolution: {diagnostics.dnsResolution ? '✅' : '❌'}</div>
            <div>HTTP Connectivity: {diagnostics.httpConnectivity ? '✅' : '❌'}</div>
            <div>CORS Enabled: {diagnostics.corsEnabled ? '✅' : '❌'}</div>
            <div>Latency: {diagnostics.latency}ms</div>
            
            {diagnostics.recommendations.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2">Recommendations:</Typography>
                {diagnostics.recommendations.map((rec: string, index: number) => (
                  <div key={index}>• {rec}</div>
                ))}
              </Box>
            )}
          </Box>
        </Paper>
      )}
    </Container>
  );
};

export default NetworkTestPage;
