import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormControlLabel,
  Divider,
  Grid,
  Chip,
  RadioGroup,
  Radio,
} from '@mui/material';
import {
  Description,
  CheckCircle,
  Warning,
  ThumbUp,
  ThumbDown,
  Download,
  Print,
  Email,
} from '@mui/icons-material';

interface Agreement {
  id: string;
  title: string;
  content: string;
  status: string;
  metadata: {
    customerName: string;
    vehicleName: string;
    rentalPeriod: string;
    totalAmount: number;
  };
  created_at: string;
  sent_at: string;
}

const AgreementSigningPage: React.FC = () => {
  const { agreementId } = useParams<{ agreementId: string }>();
  const navigate = useNavigate();

  const [agreement, setAgreement] = useState<Agreement | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<'agree' | 'disagree' | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  useEffect(() => {
    if (agreementId) {
      fetchAgreement();
    }
  }, [agreementId]);

  const fetchAgreement = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/agreements/${agreementId}/view`);
      
      if (!response.ok) {
        throw new Error('Agreement not found');
      }

      const result = await response.json();
      
      if (result.success) {
        setAgreement(result.data);
      } else {
        throw new Error(result.error || 'Failed to load agreement');
      }
    } catch (error) {
      console.error('Error fetching agreement:', error);
      setError(error instanceof Error ? error.message : 'Failed to load agreement');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitResponse = () => {
    if (!response) {
      setError('Please select whether you agree or disagree with the terms');
      return;
    }
    setConfirmDialogOpen(true);
  };

  const handleConfirmResponse = async () => {
    try {
      setSubmitting(true);
      setConfirmDialogOpen(false);

      const apiResponse = await fetch(`/api/agreements/${agreementId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          response: response,
          customerInfo: {
            name: agreement?.metadata.customerName,
            timestamp: new Date().toISOString(),
            ipAddress: 'customer-ip' // This would be captured server-side
          }
        })
      });

      const result = await apiResponse.json();

      if (result.success) {
        setError(null);
        if (response === 'agree') {
          navigate('/agreement-confirmed-success');
        } else {
          navigate('/agreement-declined');
        }
      } else {
        throw new Error(result.error || 'Failed to submit response');
      }
    } catch (error) {
      console.error('Error submitting response:', error);
      setError(error instanceof Error ? error.message : 'Failed to submit response');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDownloadAgreement = () => {
    if (agreement) {
      const element = document.createElement('a');
      const file = new Blob([agreement.content], { type: 'text/plain' });
      element.href = URL.createObjectURL(file);
      element.download = `agreement-${agreement.id}.txt`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    }
  };

  const handlePrintAgreement = () => {
    window.print();
  };

  if (loading) {
    return (
      <Box sx={{ width: '100%', mt: 4 }}>
        <LinearProgress />
        <Box display="flex" justifyContent="center" mt={2}>
          <Typography>Loading agreement...</Typography>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <Alert severity="error" sx={{ maxWidth: 600 }}>
          <Typography variant="h6">Error Loading Agreement</Typography>
          <Typography>{error}</Typography>
          <Button 
            variant="outlined" 
            onClick={() => window.location.reload()} 
            sx={{ mt: 2 }}
          >
            Try Again
          </Button>
        </Alert>
      </Box>
    );
  }

  if (!agreement) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <Alert severity="warning" sx={{ maxWidth: 600 }}>
          <Typography variant="h6">Agreement Not Found</Typography>
          <Typography>The requested agreement could not be found.</Typography>
        </Alert>
      </Box>
    );
  }

  const isAlreadySigned = agreement.status === 'signed';

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center">
              <Description sx={{ mr: 2, fontSize: 30, color: 'primary.main' }} />
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  {agreement.title}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Agreement ID: {agreement.id}
                </Typography>
              </Box>
            </Box>
            <Chip
              icon={isAlreadySigned ? <CheckCircle /> : <Warning />}
              label={isAlreadySigned ? 'SIGNED' : 'PENDING SIGNATURE'}
              color={isAlreadySigned ? 'success' : 'warning'}
              variant="outlined"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Agreement Details */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Agreement Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="textSecondary">Customer</Typography>
              <Typography variant="body1" fontWeight="medium">
                {agreement.metadata.customerName}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="textSecondary">Vehicle</Typography>
              <Typography variant="body1" fontWeight="medium">
                {agreement.metadata.vehicleName}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="textSecondary">Rental Period</Typography>
              <Typography variant="body1" fontWeight="medium">
                {agreement.metadata.rentalPeriod}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="textSecondary">Total Amount</Typography>
              <Typography variant="body1" fontWeight="medium">
                ${agreement.metadata.totalAmount.toFixed(2)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Agreement Content */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
            <Typography variant="h6">
              Agreement Terms
            </Typography>
            <Box>
              <Button
                startIcon={<Download />}
                onClick={handleDownloadAgreement}
                sx={{ mr: 1 }}
              >
                Download
              </Button>
              <Button
                startIcon={<Print />}
                onClick={handlePrintAgreement}
              >
                Print
              </Button>
            </Box>
          </Box>
          
          <Paper 
            sx={{ 
              p: 3, 
              maxHeight: 500, 
              overflow: 'auto', 
              bgcolor: 'grey.50',
              fontFamily: 'monospace'
            }}
          >
            <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
              {agreement.content}
            </pre>
          </Paper>
        </CardContent>
      </Card>

      {/* Agreement Response Section */}
      {!isAlreadySigned && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Agreement Confirmation
            </Typography>

            <Typography variant="body1" sx={{ mb: 3 }}>
              Please confirm whether you agree to the terms and conditions outlined in this rental agreement:
            </Typography>

            <RadioGroup
              value={response}
              onChange={(e) => setResponse(e.target.value as 'agree' | 'disagree')}
              sx={{ mb: 3 }}
            >
              <FormControlLabel
                value="agree"
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center">
                    <ThumbUp sx={{ mr: 1, color: 'success.main' }} />
                    <Typography>
                      <strong>I AGREE</strong> to the terms and conditions of this rental agreement
                    </Typography>
                  </Box>
                }
                sx={{ mb: 1 }}
              />
              <FormControlLabel
                value="disagree"
                control={<Radio />}
                label={
                  <Box display="flex" alignItems="center">
                    <ThumbDown sx={{ mr: 1, color: 'error.main' }} />
                    <Typography>
                      <strong>I DISAGREE</strong> with the terms and conditions
                    </Typography>
                  </Box>
                }
              />
            </RadioGroup>

            <Box display="flex" gap={2}>
              <Button
                variant="contained"
                color={response === 'agree' ? 'success' : 'error'}
                onClick={handleSubmitResponse}
                disabled={!response || submitting}
                size="large"
                startIcon={response === 'agree' ? <ThumbUp /> : <ThumbDown />}
              >
                {submitting ? 'Submitting...' : `Confirm ${response === 'agree' ? 'Agreement' : 'Disagreement'}`}
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Already Confirmed Message */}
      {isAlreadySigned && (
        <Alert severity="success">
          <Typography variant="h6">Agreement Successfully Confirmed</Typography>
          <Typography>
            This agreement has been confirmed and accepted.
            You can proceed with your vehicle rental as scheduled.
            A confirmation email has been sent to your email address.
          </Typography>
        </Alert>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {response === 'agree' ? 'Confirm Agreement' : 'Confirm Disagreement'}
        </DialogTitle>
        <DialogContent>
          <Alert severity={response === 'agree' ? 'success' : 'warning'} sx={{ mb: 2 }}>
            {response === 'agree' ? (
              <Typography>
                By confirming, you agree to all terms and conditions of this rental agreement.
                This will be legally binding and you can proceed with your vehicle pickup.
              </Typography>
            ) : (
              <Typography>
                By confirming disagreement, this rental booking will be cancelled.
                You will not be able to proceed with the vehicle pickup.
              </Typography>
            )}
          </Alert>

          <Typography variant="body2" color="textSecondary">
            This action cannot be undone. Are you sure you want to proceed?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color={response === 'agree' ? 'success' : 'error'}
            onClick={handleConfirmResponse}
            disabled={submitting}
          >
            {submitting ? 'Processing...' : `Confirm ${response === 'agree' ? 'Agreement' : 'Disagreement'}`}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AgreementSigningPage;
