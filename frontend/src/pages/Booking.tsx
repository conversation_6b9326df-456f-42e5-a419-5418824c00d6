import React from 'react'
import { Container, Typography, Alert } from '@mui/material'
import { useParams } from 'react-router-dom'

const Booking: React.FC = () => {
  const { vehicleId } = useParams<{ vehicleId: string }>()

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Book Your Vehicle
      </Typography>
      <Alert severity="info">
        Booking flow for vehicle {vehicleId} - Coming soon!
      </Alert>
    </Container>
  )
}

export default Booking