import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Divider,
  Paper,
  Stack
} from '@mui/material';
import { 
  DirectionsBike as BikeIcon,
  Security as SecurityIcon,
  Public as GlobalIcon,
  EmojiPeople as PeopleIcon
} from '@mui/icons-material';

const AboutUsPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          About RentaHub
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto' }}>
          Your trusted platform for vehicle rentals across Southeast Asia, with a special focus on Indonesia
        </Typography>
      </Box>

      {/* Our Story */}
      <Paper elevation={0} sx={{ p: 4, mb: 6, bgcolor: 'grey.50', borderRadius: 2 }}>
        <Typography variant="h4" gutterBottom>
          Our Story
        </Typography>
        <Typography paragraph>
          Founded in 2023, <PERSON><PERSON>H<PERSON> was born from a simple observation: renting vehicles in Indonesia should be easier, safer, and more accessible. Our founders experienced firsthand the challenges of finding reliable rental options while traveling through Southeast Asia.
        </Typography>
        <Typography paragraph>
          What started as a small platform connecting travelers with local scooter owners in Bali has grown into Indonesia's premier vehicle rental marketplace, serving thousands of customers across the archipelago and beyond.
        </Typography>
        <Typography>
          Today, we're proud to offer a diverse selection of vehicles - from small scooters to luxury cars - all while maintaining our commitment to quality, safety, and exceptional customer service.
        </Typography>
      </Paper>

      {/* Our Mission */}
      <Box mb={6}>
        <Typography variant="h4" gutterBottom>
          Our Mission
        </Typography>
        <Typography variant="body1" paragraph>
          At RentaHub, we're on a mission to transform the vehicle rental experience in Southeast Asia by:
        </Typography>
        <Grid container spacing={3} mt={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardContent>
                <BikeIcon color="primary" sx={{ fontSize: 40, mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Accessibility
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Making quality vehicles accessible to everyone, everywhere in Indonesia
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardContent>
                <SecurityIcon color="primary" sx={{ fontSize: 40, mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Safety
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Prioritizing safety through rigorous provider verification and insurance options
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardContent>
                <GlobalIcon color="primary" sx={{ fontSize: 40, mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Community
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Building a trusted community of renters and providers across Southeast Asia
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardContent>
                <PeopleIcon color="primary" sx={{ fontSize: 40, mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Empowerment
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Empowering local vehicle owners to become successful micro-entrepreneurs
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Our Team */}
      <Box mb={6}>
        <Typography variant="h4" gutterBottom>
          Our Team
        </Typography>
        <Typography paragraph>
          RentaHub is powered by a diverse team of professionals passionate about technology, travel, and transportation. Based in Jakarta with team members across Indonesia, Singapore, and Malaysia, we combine local expertise with global perspectives.
        </Typography>
        <Typography>
          Our team includes former executives from leading transportation companies, tech innovators, and customer experience specialists - all united by our mission to revolutionize vehicle rentals in Southeast Asia.
        </Typography>
      </Box>

      {/* Indonesian Context */}
      <Paper elevation={0} sx={{ p: 4, bgcolor: 'primary.light', color: 'white', borderRadius: 2 }}>
        <Typography variant="h4" gutterBottom>
          Our Indonesian Roots
        </Typography>
        <Typography paragraph>
          As a company born in Indonesia, we understand the unique transportation needs of this diverse archipelago. From navigating Jakarta's busy streets to exploring Bali's hidden beaches, our platform is designed specifically for Indonesia's geography, culture, and transportation landscape.
        </Typography>
        <Typography>
          We're proud to support local businesses and contribute to Indonesia's growing digital economy while helping travelers and locals alike discover the beauty of our country on their own terms.
        </Typography>
      </Paper>
    </Container>
  );
};

export default AboutUsPage; 