import React, { useState, useEffect } from 'react'
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Button,
  Chip,
  Rating,
  CircularProgress,
  Alert,
} from '@mui/material'
import { LocationOn, Speed, LocalGasStation } from '@mui/icons-material'
import { useSearchParams, Link } from 'react-router-dom'
import SearchForm from '../components/SearchForm'
import { supabase } from '../utils/supabaseClient'
import apiService from '../services/apiService';
import ListedVehicleService, { ListedVehicle } from '../services/ListedVehicleService';
import EmptyListingsCTA from '../components/EmptyListingsCTA';

interface Vehicle {
  id: string
  category: string
  make: string
  model: string
  year: number
  engine_size: number
  transmission: string
  fuel_type: string
  description: string
  images: string[]
  daily_rate: number
  weekly_rate: number
  monthly_rate: number
  security_deposit: number
  delivery_available: boolean
  delivery_fee: number
  quantity: number
  available_quantity: number
  location: any
  provider: {
    business_name: string
    rating: number
    total_reviews: number
    city: string
  }
}

const Search: React.FC = () => {
  const [searchParams] = useSearchParams()
  const [vehicles, setVehicles] = useState<ListedVehicle[]>([])
  const [hasListings, setHasListings] = useState<boolean>(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchVehicles()
  }, [searchParams])

  const fetchVehicles = async () => {
    try {
      setLoading(true)
      setError('')

      // Get search parameters
      const query = searchParams.get('q') || '';
      const category = searchParams.get('category') || '';
      const city = searchParams.get('city') || '';
      const minPrice = searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined;
      const maxPrice = searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined;

      console.log('🔍 Searching for real listed vehicles with params:', { query, category, city, minPrice, maxPrice });

      // Search real listed vehicles
      const response = await ListedVehicleService.searchVehicles(
        query || undefined,
        {
          category: category || undefined,
          city: city || undefined,
          minPrice,
          maxPrice
        },
        50 // Show up to 50 results
      );

      console.log('📊 Search response:', response);

      if (response.success && response.hasListings) {
        setVehicles(response.data);
        setHasListings(true);
      } else {
        console.log('❌ No real listings found - will show CTA');
        setVehicles([]);
        setHasListings(false);
      }
    } catch (err: any) {
      console.error('Search error:', err);
      setError('Failed to search vehicles');
      setHasListings(false);
      setVehicles([]);
    } finally {
      setLoading(false);
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'small_scooter': return 'Small Scooter'
      case 'large_scooter': return 'Large Scooter'
      case 'luxury_bike': return 'Luxury Bike'
      default: return category
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getVehicleImage = (vehicle: ListedVehicle | Vehicle) => {
    if (vehicle.images && vehicle.images.length > 0) {
      return vehicle.images[0];
    }
    return 'https://via.placeholder.com/300x200?text=No+Image';
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Finding available vehicles...
        </Typography>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Search Form */}
      <Card sx={{ p: 3, mb: 4 }}>
        <SearchForm />
      </Card>

      {/* Results */}
      <Typography variant="h4" gutterBottom>
        Available Vehicles
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {vehicles.length === 0 && !loading && !error && (
        <EmptyListingsCTA variant="search" />
      )}

      <Grid container spacing={3}>
        {vehicles.map((vehicle) => (
          <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="img"
                height="200"
                image={getVehicleImage(vehicle)}
                alt={`${vehicle.make} ${vehicle.model}`}
                sx={{ objectFit: 'cover' }}
                onError={e => { (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=No+Image'; }}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Typography gutterBottom variant="h6" component="div">
                    {vehicle.make} {vehicle.model}
                  </Typography>
                  <Chip
                    label={getCategoryLabel(vehicle.category)}
                    size="small"
                    color="primary"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {vehicle.description}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {vehicle.provider?.city}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Speed sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {vehicle.year}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LocalGasStation sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {vehicle.category}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Rating
                    value={vehicle.rating || 0}
                    readOnly
                    size="small"
                    precision={0.1}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    ({vehicle.reviewCount || 0} reviews)
                  </Typography>
                </Box>

                <Typography variant="h6" color="primary" gutterBottom>
                  {formatPrice(vehicle.dailyRate)}/day
                </Typography>

                <Button
                  component={Link}
                  to={vehicle.id && typeof vehicle.id === 'string' && vehicle.id.match(/^[0-9a-fA-F-]{36}$/) ? `/vehicle/${vehicle.id}` : '#'}
                  onClick={e => {
                    if (!(vehicle.id && typeof vehicle.id === 'string' && vehicle.id.match(/^[0-9a-fA-F-]{36}$/))) {
                      e.preventDefault();
                      alert('Invalid vehicle ID');
                    }
                  }}
                  variant="contained"
                  fullWidth
                >
                  View Details
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  )
}

export default Search