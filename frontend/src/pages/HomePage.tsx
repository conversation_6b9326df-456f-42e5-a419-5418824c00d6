import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Chip,
  Stack,
  Rating,
  Avatar,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Paper,
  Slider,
  ToggleButton,
  ToggleButtonGroup,
  Divider,
  IconButton
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  DirectionsBike as BikeIcon,
  CalendarToday as CalendarIcon,
  TwoWheeler as ScooterIcon,
  ElectricScooter as ElectricIcon,
  FilterList as FilterIcon,
  Map as MapIcon,
  MyLocation as MyLocationIcon,
  AttachMoney as PriceIcon
} from '@mui/icons-material';

import { VehicleService } from '../services/VehicleService';



const HomePage: React.FC = () => {
  const navigate = useNavigate();

  // Enhanced search form state
  const [searchForm, setSearchForm] = useState({
    location: '',
    startDate: '',
    endDate: '',
    vehicleType: '',
    radius: 10, // km
    priceRange: [0, 200] as [number, number],
    showAdvanced: false
  });








  // Search handlers
  const handleSearch = async () => {
    try {
      console.log('🔍 HomePage: Starting search with form data:', searchForm);
      console.log('🔍 HomePage: Search button clicked - initiating comprehensive search flow');

      // Build search parameters
      const searchParams = {
        location: searchForm.location || undefined,
        startDate: searchForm.startDate || undefined,
        endDate: searchForm.endDate || undefined,
        vehicleType: searchForm.vehicleType || undefined,
        radius: searchForm.radius !== 10 ? searchForm.radius : undefined,
        minPrice: searchForm.priceRange[0] !== 0 ? searchForm.priceRange[0] : undefined,
        maxPrice: searchForm.priceRange[1] !== 200 ? searchForm.priceRange[1] : undefined,
        page: 1,
        size: 20
      };

      // Test the backend connection
      const result = await VehicleService.searchVehiclesAdvanced(searchParams);

      if (result.success) {
        console.log('✅ HomePage: Search successful, navigating to vehicles page');
        // Build URL params for navigation
        const params = new URLSearchParams();
        if (searchForm.location) params.append('location', searchForm.location);
        if (searchForm.startDate) params.append('startDate', searchForm.startDate);
        if (searchForm.endDate) params.append('endDate', searchForm.endDate);
        if (searchForm.vehicleType) params.append('type', searchForm.vehicleType);
        if (searchForm.radius !== 10) params.append('radius', searchForm.radius.toString());
        if (searchForm.priceRange[0] !== 0) params.append('minPrice', searchForm.priceRange[0].toString());
        if (searchForm.priceRange[1] !== 200) params.append('maxPrice', searchForm.priceRange[1].toString());

        navigate(`/vehicles?${params.toString()}`);
      } else {
        console.error('❌ HomePage: Search failed:', result.error);
        // Still navigate but show error on vehicles page
        navigate('/vehicles?error=search_failed');
      }
    } catch (error) {
      console.error('❌ HomePage: Search error:', error);
      // Navigate anyway, let vehicles page handle the error
      navigate('/vehicles?error=connection_failed');
    }
  };

  const handleSearchFormChange = (field: string, value: any) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const popularLocations = [
    { name: 'Jakarta Pusat', count: 45, image: 'https://images.unsplash.com/photo-1619771914272-e3c1ba17ba4d?w=400' },
    { name: 'Bali', count: 32, image: 'https://images.unsplash.com/photo-1591637333184-19aa84b3e01f?w=400' },
    { name: 'Yogyakarta', count: 28, image: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400' },
    { name: 'Surabaya', count: 25, image: 'https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=400' }
  ];



  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #2b5876 0%, #4e4376 100%)',
          color: 'white',
          py: 10,
          mb: 6,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7)), url(https://images.unsplash.com/photo-1622185135505-2d795003994a?w=1200)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.8,
            zIndex: 1
          }}
        />
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, py: { xs: 2, md: 6 } }}>
          <Grid container spacing={{ xs: 2, md: 4 }} alignItems="center" justifyContent="center">
            <Grid item xs={12} md={6} textAlign="center">
              <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', fontSize: { xs: '2rem', md: '3.5rem' } }}>
                Rent Vehicles
                <br />
                <span style={{ color: '#ffd700', fontSize: '2rem' }}>Across SE Asia</span>
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9, fontSize: { xs: '1rem', md: '1.25rem' } }}>
                Find and rent motorcycles, scooters, and luxury bikes from trusted providers in your area
              </Typography>

              {/* Enhanced Search Component */}
              <Paper
                elevation={8}
                sx={{
                  p: 4,
                  mb: 4,
                  borderRadius: 3,
                  background: 'rgba(255,255,255,0.95)',
                  backdropFilter: 'blur(10px)',
                  maxWidth: '800px',
                  mx: 'auto'
                }}
              >
                <Grid container spacing={3}>
                  {/* Location */}
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Location"
                      value={searchForm.location}
                      onChange={(e) => handleSearchFormChange('location', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LocationIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      placeholder="Enter city or area"
                      variant="outlined"
                      size="small"
                    />
                  </Grid>

                  {/* Start Date */}
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="Start Date"
                      type="date"
                      value={searchForm.startDate}
                      onChange={(e) => handleSearchFormChange('startDate', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      InputLabelProps={{ shrink: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>

                  {/* End Date */}
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      label="End Date"
                      type="date"
                      value={searchForm.endDate}
                      onChange={(e) => handleSearchFormChange('endDate', e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      InputLabelProps={{ shrink: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>

                  {/* Vehicle Type */}
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Vehicle Type</InputLabel>
                      <Select
                        value={searchForm.vehicleType}
                        label="Vehicle Type"
                        onChange={(e) => handleSearchFormChange('vehicleType', e.target.value)}
                        startAdornment={
                          <InputAdornment position="start">
                            <BikeIcon color="primary" />
                          </InputAdornment>
                        }
                      >
                        <MenuItem value="">All Types</MenuItem>
                        <MenuItem value="SCOOTER">
                          <Box display="flex" alignItems="center" gap={1}>
                            <ScooterIcon fontSize="small" />
                            Scooter
                          </Box>
                        </MenuItem>
                        <MenuItem value="MOTORCYCLE">
                          <Box display="flex" alignItems="center" gap={1}>
                            <BikeIcon fontSize="small" />
                            Motorcycle
                          </Box>
                        </MenuItem>
                        <MenuItem value="ELECTRIC">
                          <Box display="flex" alignItems="center" gap={1}>
                            <ElectricIcon fontSize="small" />
                            Electric
                          </Box>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Advanced Filters Toggle */}
                  <Grid item xs={12}>
                    <Box display="flex" justifyContent="center">
                      <Button
                        variant="text"
                        startIcon={<FilterIcon />}
                        onClick={() => handleSearchFormChange('showAdvanced', !searchForm.showAdvanced)}
                        sx={{ color: 'primary.main' }}
                      >
                        {searchForm.showAdvanced ? 'Hide' : 'Show'} Advanced Filters
                      </Button>
                    </Box>
                  </Grid>

                  {/* Advanced Filters */}
                  {searchForm.showAdvanced && (
                    <>
                      <Grid item xs={12}>
                        <Divider sx={{ my: 2 }} />
                      </Grid>

                      {/* Distance Radius */}
                      <Grid item xs={12} sm={6}>
                        <Box>
                          <Typography variant="body2" gutterBottom display="flex" alignItems="center" gap={1}>
                            <MyLocationIcon fontSize="small" color="primary" />
                            Search Radius: {searchForm.radius} km
                          </Typography>
                          <Slider
                            value={searchForm.radius}
                            onChange={(_, value) => handleSearchFormChange('radius', value)}
                            min={1}
                            max={50}
                            step={1}
                            marks={[
                              { value: 1, label: '1km' },
                              { value: 10, label: '10km' },
                              { value: 25, label: '25km' },
                              { value: 50, label: '50km' }
                            ]}
                            valueLabelDisplay="auto"
                            color="primary"
                          />
                        </Box>
                      </Grid>

                      {/* Price Range */}
                      <Grid item xs={12} sm={6}>
                        <Box>
                          <Typography variant="body2" gutterBottom display="flex" alignItems="center" gap={1}>
                            <PriceIcon fontSize="small" color="primary" />
                            Price Range: ${searchForm.priceRange[0]} - ${searchForm.priceRange[1]}/day
                          </Typography>
                          <Slider
                            value={searchForm.priceRange}
                            onChange={(_, value) => handleSearchFormChange('priceRange', value)}
                            min={0}
                            max={500}
                            step={10}
                            marks={[
                              { value: 0, label: '$0' },
                              { value: 50, label: '$50' },
                              { value: 100, label: '$100' },
                              { value: 200, label: '$200' },
                              { value: 500, label: '$500+' }
                            ]}
                            valueLabelDisplay="auto"
                            color="primary"
                          />
                        </Box>
                      </Grid>
                    </>
                  )}

                  {/* Search Button */}
                  <Grid item xs={12}>
                    <Box display="flex" gap={2} justifyContent="center" flexWrap="wrap">
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={<SearchIcon />}
                        onClick={handleSearch}
                        sx={{
                          bgcolor: '#2b5876',
                          color: 'white',
                          fontWeight: 'bold',
                          px: 4,
                          py: 1.5,
                          '&:hover': { bgcolor: '#4e4376' }
                        }}
                      >
                        Search Vehicles
                      </Button>
                      <Button
                        variant="outlined"
                        size="large"
                        startIcon={<MapIcon />}
                        onClick={() => navigate('/vehicles?view=map')}
                        sx={{
                          color: '#2b5876',
                          borderColor: '#2b5876',
                          fontWeight: 'bold',
                          px: 4,
                          py: 1.5,
                          '&:hover': {
                            borderColor: '#4e4376',
                            bgcolor: 'rgba(43, 88, 118, 0.1)'
                          }
                        }}
                      >
                        Map View
                      </Button>

                    </Box>
                  </Grid>
                </Grid>
              </Paper>

              <Stack
                direction={{ xs: 'column', sm: 'row' }} 
                spacing={2} 
                flexWrap="wrap" 
                useFlexGap
                justifyContent="center"
              >
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<AddIcon />}
                  sx={{
                    color: 'white',
                    borderColor: 'white',
                    fontWeight: 'bold',
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    py: 1.5,
                    px: 4,
                    '&:hover': {
                      borderColor: '#ffd700',
                      color: '#ffd700'
                    }
                  }}
                  onClick={() => navigate('/vehicle-listing')}
                >
                  List Your Vehicle
                </Button>
              </Stack>
            </Grid>
          </Grid>
          
          {/* Vehicle showcase */}
          <Box sx={{ mt: 6, display: 'flex', justifyContent: 'center' }}>
            <Grid container spacing={3} justifyContent="center" maxWidth="900px">
              <Grid item xs={6} sm={4} md={4} key="showcase-scooter">
                <Box
                  component="img"
                  src="https://images.unsplash.com/photo-1619771914272-e3c1ba17ba4d?w=600"
                  alt="Modern scooter rental"
                  sx={{
                    width: '100%',
                    height: { xs: 120, sm: 160, md: 200 },
                    objectFit: 'cover',
                    borderRadius: 3,
                    boxShadow: 6,
                    transform: 'rotate(-5deg)',
                    transition: 'transform 0.3s',
                    '&:hover': {
                      transform: 'rotate(-2deg) scale(1.05)',
                    }
                  }}
                />
              </Grid>
              <Grid item xs={6} sm={4} md={4} key="showcase-motorcycle">
                <Box
                  component="img"
                  src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=600"
                  alt="Luxury motorcycle rental"
                  sx={{
                    width: '100%',
                    height: { xs: 120, sm: 160, md: 200 },
                    objectFit: 'cover',
                    borderRadius: 3,
                    boxShadow: 6,
                    mt: { xs: 2, md: -2 },
                    transition: 'transform 0.3s',
                    '&:hover': {
                      transform: 'scale(1.05)',
                    }
                  }}
                />
              </Grid>
              <Grid item xs={6} sm={4} md={4} key="showcase-sportbike" sx={{ display: { xs: 'none', sm: 'block' }}}>
                <Box
                  component="img"
                  src="https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=600"
                  alt="Sport bike rental"
                  sx={{
                    width: '100%',
                    height: { xs: 120, sm: 160, md: 200 },
                    objectFit: 'cover',
                    borderRadius: 3,
                    boxShadow: 6,
                    transform: 'rotate(5deg)',
                    transition: 'transform 0.3s',
                    '&:hover': {
                      transform: 'rotate(2deg) scale(1.05)',
                    }
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* Quick Stats */}
      <Container maxWidth="lg" sx={{ mb: { xs: 4, md: 6 } }}>
        <Grid container spacing={{ xs: 2, md: 3 }}>
          <Grid item xs={12} sm={6} md={3} key="stats-vehicles">
            <Box sx={{ p: { xs: 2, md: 3 }, textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', md: '2rem' } }}>
                500+
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                Vehicles Available
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3} key="stats-cities">
            <Box sx={{ p: { xs: 2, md: 3 }, textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', md: '2rem' } }}>
                50+
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                Cities in SE Asia
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3} key="stats-rating">
            <Box sx={{ p: { xs: 2, md: 3 }, textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', md: '2rem' } }}>
                4.8
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                User Rating
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3} key="stats-support">
            <Box sx={{ p: { xs: 2, md: 3 }, textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', md: '2rem' } }}>
                24/7
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                Customer Support
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ mb: { xs: 4, md: 8 } }}>
        <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ fontWeight: 'bold', fontSize: { xs: '1.3rem', md: '2rem' } }}>
          Why Choose RentaHub?
        </Typography>
        <Grid container spacing={{ xs: 2, md: 4 }} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4} key="feature-secure">
            <Box sx={{ p: { xs: 2, md: 4 }, textAlign: 'center', height: '100%', borderRadius: 3 }}>
              <BikeIcon sx={{ fontSize: { xs: 40, md: 60 }, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>
                Secure & Trusted
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                All vehicles are verified and insured. Your safety is our priority.
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} key="feature-convenient">
            <Box sx={{ p: { xs: 2, md: 4 }, textAlign: 'center', height: '100%', borderRadius: 3 }}>
              <LocationIcon sx={{ fontSize: { xs: 40, md: 60 }, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>
                Local & Convenient
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                Find vehicles near you with easy pickup and drop-off locations.
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} key="feature-support">
            <Box sx={{ p: { xs: 2, md: 4 }, textAlign: 'center', height: '100%', borderRadius: 3 }}>
              <StarIcon sx={{ fontSize: { xs: 40, md: 60 }, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ fontSize: { xs: '1rem', md: '1.2rem' } }}>
                24/7 Support
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                Get help anytime with our round-the-clock customer support.
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>

      {/* Popular Locations */}
      <Container maxWidth="lg" sx={{ mb: { xs: 4, md: 8 } }}>
        <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ fontWeight: 'bold', fontSize: { xs: '1.3rem', md: '2rem' } }}>
          Popular Destinations
        </Typography>
        <Grid container spacing={{ xs: 2, md: 3 }}>
          {popularLocations.map((location, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card 
                sx={{ 
                  height: { xs: 180, md: 200 },
                  cursor: 'pointer',
                  transition: 'transform 0.2s',
                  '&:hover': { transform: { md: 'translateY(-4px)' } },
                  borderRadius: 3
                }}
                onClick={() => navigate(`/vehicles?location=${location.name}`)}
              >
                <CardMedia
                  component="img"
                  height="120"
                  image={location.image}
                  alt={location.name}
                  sx={{ objectFit: 'cover' }}
                />
                <CardContent sx={{ p: { xs: 1, md: 2 } }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ fontSize: { xs: '1rem', md: '1.1rem' } }}>
                    {location.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.95rem', md: '1rem' } }}>
                    {location.count} vehicles available
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Featured Vehicles - Coming Soon */}
      <Container maxWidth="lg" sx={{ mb: { xs: 4, md: 8 } }}>
        <Box
          sx={{
            p: 8,
            textAlign: 'center',
            background: 'linear-gradient(135deg, #2b5876 0%, #4e4376 100%)',
            color: 'white',
            borderRadius: 3,
            boxShadow: 3
          }}
        >
          <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', fontSize: { xs: '2rem', md: '3rem' } }}>
            Featured Vehicles
          </Typography>
          <Typography variant="h4" sx={{ mb: 2, opacity: 0.9, fontSize: { xs: '1.5rem', md: '2rem' } }}>
            Coming Soon!
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.8, maxWidth: '600px', mx: 'auto' }}>
            We're working hard to bring you the best selection of vehicles across SE Asia. Stay tuned for amazing deals and premium rides!
          </Typography>
          <Button
            variant="contained"
            size="large"
            onClick={() => navigate('/vehicles')}
            sx={{
              bgcolor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 'bold',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.3)',
              '&:hover': {
                bgcolor: 'rgba(255,255,255,0.3)',
                transform: 'translateY(-2px)'
              }
            }}
          >
            Notify Me When Available
          </Button>
        </Box>
      </Container>



      {/* Testimonials */}
      <Container maxWidth="lg" sx={{ mb: { xs: 4, md: 8 } }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ fontWeight: 'bold' }}>
          What They Say
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={4} key="testimonial-budi">
            <Box sx={{ p: 4, borderRadius: 3 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ mr: 2 }}>B</Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">Budi Santoso</Typography>
                  <Typography variant="body2" color="text.secondary">Jakarta</Typography>
                </Box>
              </Box>
              <Rating value={5} readOnly size="small" sx={{ mb: 2 }} />
              <Typography variant="body1">
                "Excellent service! The motorcycle was clean and the rental process was easy. Highly recommended!"
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} key="testimonial-sari">
            <Box sx={{ p: 4, borderRadius: 3 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ mr: 2 }}>S</Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">Sari Motor</Typography>
                  <Typography variant="body2" color="text.secondary">Vehicle Provider</Typography>
                </Box>
              </Box>
              <Rating value={5} readOnly size="small" sx={{ mb: 2 }} />
              <Typography variant="body1">
                "The platform is very helpful for managing our motorcycle rental business. Our income has increased!"
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} key="testimonial-ahmad">
            <Box sx={{ p: 4, borderRadius: 3 }}>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ mr: 2 }}>A</Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">Ahmad Rizki</Typography>
                  <Typography variant="body2" color="text.secondary">Bali</Typography>
                </Box>
              </Box>
              <Rating value={5} readOnly size="small" sx={{ mb: 2 }} />
              <Typography variant="body1">
                "Renting motorcycles in Bali is now much easier. Affordable prices and well-maintained vehicles."
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>


    </Box>
  );
};

export default HomePage;
