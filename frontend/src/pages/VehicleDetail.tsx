import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  <PERSON>rid,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Rating,
  ImageList,
  ImageListItem,
  Alert,
  CircularProgress,
} from '@mui/material'
import {
  LocationOn,
  Speed,
  // LocalGasStation,
  Settings,
  Security,
  Schedule,
} from '@mui/icons-material'
import { useParams, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../utils/supabaseClient'

interface Vehicle {
  id: string
  category: string
  make: string
  model: string
  year: number
  engine_size: number
  transmission: string
  fuel_type: string
  description: string
  images: string[]
  daily_rate: number
  weekly_rate: number
  monthly_rate: number
  security_deposit: number
  minimum_rental_days: number
  maximum_rental_days: number
  delivery_available: boolean
  delivery_fee: number
  delivery_radius: number
  quantity: number
  available_quantity: number
  features: any[]
  add_ons: any[]
  location: {
    address: string
    city: string
    latitude?: number
    longitude?: number
  }
  pickup_instructions: string
  provider: {
    id: string
    business_name: string
    rating: number
    total_reviews: number
    city: string
    phone: string
    response_rate: number
    offers_insurance: boolean
  }
}

const VehicleDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const { user } = useAuth()
  const navigate = useNavigate()
  const [vehicle, setVehicle] = useState<Vehicle | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (id) {
      fetchVehicle(id)
    }
  }, [id])

  const fetchVehicle = async (vehicleId: string) => {
    try {
      setLoading(true)
      setError('')

      const { data, error: fetchError } = await supabase
        .from('vehicles')
        .select(`
          *,
          provider:providers(*)
        `)
        .eq('id', vehicleId)
        .eq('active', true)
        .single()

      if (fetchError) {
throw fetchError
}

      setVehicle(data)
    } catch (err: any) {
      setError(err.message || 'Vehicle not found')
    } finally {
      setLoading(false)
    }
  }

  const handleBookNow = () => {
    if (!user) {
      // Trigger auth modal
      return
    }
    navigate(`/booking/${vehicle?.id}`)
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'small_scooter': return 'Small Scooter'
      case 'large_scooter': return 'Large Scooter'
      case 'luxury_bike': return 'Luxury Bike'
      default: return category
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
      </Container>
    )
  }

  if (error || !vehicle) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          {error || 'Vehicle not found'}
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Grid container spacing={4}>
        {/* Images */}
        <Grid item xs={12} md={8}>
          <ImageList cols={2} rowHeight={300} sx={{ mb: 3 }}>
            {vehicle.images.map((image, index) => (
              <ImageListItem key={index}>
                <img
                  src={image}
                  alt={`${vehicle.make} ${vehicle.model} - ${index + 1}`}
                  loading="lazy"
                  style={{ objectFit: 'cover', height: '100%' }}
                />
              </ImageListItem>
            ))}
          </ImageList>

          {/* Description */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Description
              </Typography>
              <Typography variant="body1" paragraph>
                {vehicle.description}
              </Typography>
            </CardContent>
          </Card>

          {/* Features */}
          {vehicle.features && vehicle.features.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Features
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {vehicle.features.map((feature, index) => (
                    <Chip key={index} label={feature} variant="outlined" />
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Pickup Instructions */}
          {vehicle.pickup_instructions && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Pickup Instructions
                </Typography>
                <Typography variant="body1">
                  {vehicle.pickup_instructions}
                </Typography>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Booking Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ position: 'sticky', top: 20 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h4" component="h1">
                  {vehicle.make} {vehicle.model}
                </Typography>
                <Chip
                  label={getCategoryLabel(vehicle.category)}
                  color="primary"
                />
              </Box>

              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                {vehicle.year} • {vehicle.engine_size}cc • {vehicle.transmission}
              </Typography>

              {/* Pricing */}
              <Box sx={{ my: 3 }}>
                <Typography variant="h5" color="primary" gutterBottom>
                  {formatPrice(vehicle.daily_rate)}/day
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Weekly: {formatPrice(vehicle.weekly_rate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monthly: {formatPrice(vehicle.monthly_rate)}
                </Typography>
              </Box>

              {/* Specs */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Speed sx={{ fontSize: 20, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {vehicle.engine_size}cc {vehicle.fuel_type}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Settings sx={{ fontSize: 20, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {vehicle.transmission} transmission
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Security sx={{ fontSize: 20, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    Security deposit: {formatPrice(vehicle.security_deposit)}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Schedule sx={{ fontSize: 20, mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {vehicle.minimum_rental_days}-{vehicle.maximum_rental_days} days rental
                  </Typography>
                </Box>
              </Box>

              {/* Provider Info */}
              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Provider
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {vehicle.provider?.business_name}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Rating
                      value={vehicle.provider?.rating || 0}
                      readOnly
                      size="small"
                      precision={0.1}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      ({vehicle.provider?.total_reviews || 0} reviews)
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <LocationOn sx={{ mt: 0.5, mr: 1, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body1">
                        {vehicle.location.address}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {vehicle.location.city}
                      </Typography>
                      {vehicle.location.latitude && vehicle.location.longitude && (
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ mt: 1 }}
                          href={`https://www.google.com/maps?q=${vehicle.location.latitude},${vehicle.location.longitude}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          View in Google Maps
                        </Button>
                      )}
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <LocationOn sx={{ mt: 0.5, mr: 1, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body1">
                        {vehicle.provider?.city}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Response rate: {vehicle.provider?.response_rate || 0}%
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* Availability */}
              <Typography variant="body2" color="success.main" gutterBottom>
                {vehicle.available_quantity} of {vehicle.quantity} available
              </Typography>

              {/* Delivery */}
              {vehicle.delivery_available && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Delivery available (+{formatPrice(vehicle.delivery_fee)})
                </Typography>
              )}

              <Button
                variant="contained"
                size="large"
                fullWidth
                onClick={handleBookNow}
                disabled={vehicle.available_quantity === 0}
              >
                {vehicle.available_quantity === 0 ? 'Not Available' : 'Book Now'}
              </Button>

              {!user && (
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mt: 1 }}>
                  Please sign in to book this vehicle
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  )
}

export default VehicleDetail