import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress
} from '@mui/material';
import { SupportTicket } from '../types/support';
import Layout from '../components/Layout';

// Mock support tickets data
const mockSupportTickets: SupportTicket[] = [
  {
    id: '1',
    category: 'PAYMENT',
    message: 'I was charged twice for my booking. Please help me get a refund.',
    status: 'OPEN',
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    category: 'VEHICLE',
    message: 'The vehicle I rented had a flat tire. I need assistance.',
    status: 'IN_PROGRESS',
    adminResponse: 'We have dispatched a technician to your location. They will arrive within 30 minutes.',
    createdAt: new Date(Date.now() - 86400000).toISOString(),
  },
  {
    id: '3',
    category: 'BOOKING',
    message: 'I need to cancel my booking for tomorrow due to an emergency.',
    status: 'RESOLVED',
    adminResponse: 'Your booking has been cancelled and a full refund has been processed.',
    createdAt: new Date(Date.now() - 172800000).toISOString(),
  }
];

const SupportTicketsPage: React.FC = () => {
  const [tickets, setTickets] = useState<SupportTicket[]>(mockSupportTickets);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    category: '',
    message: '',
    bookingId: ''
  });

  const handleSubmitTicket = (ticketData: Omit<SupportTicket, 'id' | 'createdAt'>) => {
    const newTicket: SupportTicket = {
      ...ticketData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };
    setTickets(prevTickets => [newTicket, ...prevTickets]);
    setShowForm(false);
    setFormData({ category: '', message: '', bookingId: '' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'error';
      case 'IN_PROGRESS': return 'warning';
      case 'RESOLVED': return 'success';
      case 'ESCALATED': return 'info';
      default: return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'PAYMENT': return 'error';
      case 'VEHICLE': return 'warning';
      case 'DAMAGE': return 'error';
      case 'BOOKING': return 'info';
      case 'OTHER': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box>
        <Navigation />
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
            <CircularProgress />
          </Box>
        </Container>
      </Box>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          Support Tickets
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            onClick={() => setShowForm(!showForm)}
            sx={{ mb: 2 }}
          >
            {showForm ? 'Cancel' : 'Create New Ticket'}
          </Button>

          {showForm && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Create Support Ticket
                </Typography>
                
                <Box component="form" onSubmit={(e) => {
                  e.preventDefault();
                  if (formData.message.trim().length < 10) {
                    setError('Message must be at least 10 characters long');
                    return;
                  }
                  handleSubmitTicket({
                    category: formData.category as 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'OTHER',
                    message: formData.message,
                    bookingId: formData.bookingId || undefined,
                    status: 'OPEN'
                  });
                  setError(null);
                }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      required
                    >
                      <MenuItem value="PAYMENT">Payment</MenuItem>
                      <MenuItem value="VEHICLE">Vehicle</MenuItem>
                      <MenuItem value="DAMAGE">Damage</MenuItem>
                      <MenuItem value="BOOKING">Booking</MenuItem>
                      <MenuItem value="OTHER">Other</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label="Booking ID (Optional)"
                    value={formData.bookingId}
                    onChange={(e) => setFormData({ ...formData, bookingId: e.target.value })}
                    sx={{ mb: 2 }}
                  />

                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Message"
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    required
                    helperText="Please describe your issue in detail (minimum 10 characters)"
                    sx={{ mb: 2 }}
                  />

                  <Button type="submit" variant="contained">
                    Submit Ticket
                  </Button>
                </Box>
              </CardContent>
            </Card>
          )}
        </Box>

        <Typography variant="h5" gutterBottom>
          Your Tickets ({tickets.length})
        </Typography>

        {tickets.length === 0 ? (
          <Alert severity="info">
            No support tickets found. Create your first ticket above.
          </Alert>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {tickets.map(ticket => (
              <Card key={ticket.id}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box>
                      <Chip
                        label={ticket.category}
                        color={getCategoryColor(ticket.category) as any}
                        size="small"
                        sx={{ mr: 1 }}
                      />
                      <Chip
                        label={ticket.status}
                        color={getStatusColor(ticket.status) as any}
                        size="small"
                      />
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(ticket.createdAt).toLocaleString()}
                    </Typography>
                  </Box>

                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {ticket.message}
                  </Typography>

                  {ticket.bookingId && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Booking ID: {ticket.bookingId}
                    </Typography>
                  )}

                  {ticket.adminResponse && (
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                      <Typography variant="subtitle2" color="primary" gutterBottom>
                        Admin Response:
                      </Typography>
                      <Typography variant="body2">
                        {ticket.adminResponse}
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            ))}
          </Box>
        )}
      </Container>
    </Layout>
  );
};

export default SupportTicketsPage;
