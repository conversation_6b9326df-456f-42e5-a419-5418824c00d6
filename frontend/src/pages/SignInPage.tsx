import React, { useEffect } from 'react';
import { Container, Typography, Paper, Box, Button, Link } from '@mui/material';
import { Link as RouterLink, useNavigate, useSearchParams } from 'react-router-dom';
import AuthModal from '../components/AuthModal';
import { useAuth } from '../contexts/AuthContext';

const SignInPage = () => {
  const [open, setOpen] = React.useState(true);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated } = useAuth();
  const redirectTo = searchParams.get('redirect') || '/dashboard';

  useEffect(() => {
    // Check if user is already logged in
    if (isAuthenticated) {
      // User is already logged in, redirect to intended destination
      navigate(redirectTo);
    }
  }, [isAuthenticated, navigate, redirectTo]);

  const handleClose = () => {
    setOpen(false);
    navigate('/'); // Redirect to home page when modal is closed
  };

  return (
    <Container maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Sign In to RentaHub
        </Typography>
        
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Don't have an account yet?
          </Typography>
          <Button
            component={RouterLink}
            to={`/signup${redirectTo !== '/dashboard' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`}
            variant="outlined"
            color="primary"
            sx={{ mb: 2 }}
          >
            Create an Account
          </Button>
        </Box>
      </Paper>

      {/* Use the existing AuthModal component */}
      <AuthModal
        open={open}
        onClose={handleClose}
        mode="login"
        redirectTo={redirectTo}
      />
    </Container>
  );
};

export default SignInPage; 