import React from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  // CardContent,
  // CardMedia,
  Paper,
} from '@mui/material'
import { Link } from 'react-router-dom'
import {
  DirectionsBike,
  Security,
  Schedule,
  LocationOn,
  // Star,
  // EuroSymbol,
} from '@mui/icons-material'
import VehicleCategories from '../components/VehicleCategories'
import SearchForm from '../components/SearchForm'

const Home: React.FC = () => {
  const features = [
    {
      icon: <DirectionsBike />,
      title: 'Wide Selection',
      description: 'From small scooters to luxury dirt bikes, find the perfect ride for your adventure.',
    },
    {
      icon: <Security />,
      title: 'Trusted Providers',
      description: 'All providers are verified with insurance and safety guarantees.',
    },
    {
      icon: <Schedule />,
      title: 'Flexible Booking',
      description: 'Book by the day, week, or month with easy cancellation policies.',
    },
    {
      icon: <LocationOn />,
      title: 'Convenient Locations',
      description: 'Pick up your vehicle from convenient locations across Bali.',
    },
  ]

  const stats = [
    { value: '1000+', label: 'Vehicles Available' },
    { value: '500+', label: 'Trusted Providers' },
    { value: '50+', label: 'Locations in Bali' },
    { value: '4.8★', label: 'Average Rating' },
  ]

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          background: 'linear-gradient(135deg, #1a1a1a 0%, #333 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            Explore Bali Your Way
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            Rent scooters, motorbikes, and dirt bikes from trusted local providers
          </Typography>
          
          {/* Search Form */}
          <Paper sx={{ p: 3, mt: 4, maxWidth: 800, mx: 'auto' }}>
            <SearchForm />
          </Paper>
        </Container>
      </Box>

      {/* Vehicle Categories */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Typography variant="h4" component="h2" textAlign="center" gutterBottom>
          Choose Your Adventure
        </Typography>
        <Typography variant="body1" textAlign="center" sx={{ mb: 4, color: 'text.secondary' }}>
          Whether you&apos;re exploring temples, hitting the beach, or tackling mountain trails
        </Typography>
        <VehicleCategories />
      </Container>

      {/* Features Section */}
      <Box sx={{ backgroundColor: '#f5f5f5', py: 6 }}>
        <Container maxWidth="lg">
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom>
            Why Choose RentaHub?
          </Typography>
          <Grid container spacing={4} sx={{ mt: 2 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
                  <Box sx={{ color: 'primary.main', mb: 2 }}>
                    {React.cloneElement(feature.icon, { sx: { fontSize: 48 } })}
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4} textAlign="center">
          {stats.map((stat, index) => (
            <Grid item xs={6} md={3} key={index}>
              <Typography variant="h3" component="div" fontWeight="bold" color="primary">
                {stat.value}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {stat.label}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box sx={{ backgroundColor: 'primary.main', color: 'white', py: 6, textAlign: 'center' }}>
        <Container maxWidth="md">
          <Typography variant="h4" component="h2" gutterBottom>
            Ready to Start Your Adventure?
          </Typography>
          <Typography variant="body1" sx={{ mb: 4, opacity: 0.9 }}>
            Join thousands of riders who trust RentaHub for their Bali adventures
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              component={Link}
              to="/vehicles"
              variant="contained"
              size="large"
              sx={{ backgroundColor: 'white', color: 'primary.main', '&:hover': { backgroundColor: '#f5f5f5' } }}
            >
              Find Your Ride
            </Button>
            <Button
              component={Link}
              to="/provider"
              variant="outlined"
              size="large"
              sx={{ borderColor: 'white', color: 'white', '&:hover': { borderColor: '#f5f5f5', backgroundColor: 'rgba(255,255,255,0.1)' } }}
            >
              Become a Provider
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default Home