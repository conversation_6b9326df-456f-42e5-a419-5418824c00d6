import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Stepper,
  Step,
  StepLabel,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  DirectionsCar as CarIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Payment as PaymentIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { getVehicleById } from '../services/VehicleService';
import { Vehicle } from '../types/vehicle';
import { useAuth } from '../contexts/AuthContext';

interface CheckoutState {
  vehicleId: string;
  pickupLocation: string;
  dropOffLocation: string;
  from: Date;
  to: Date;
}

const steps = ['Vehicle Details', 'Personal Information', 'Payment'];

const CheckoutPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [checkoutData, setCheckoutData] = useState<CheckoutState | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    licenseNumber: '',
    address: '',
    city: '',
    country: '',
  });

  // Read booking info from location.state
  useEffect(() => {
    if (!user) {
      navigate('/signin');
      return;
    }
    const loadCheckoutData = async () => {
      try {
        setLoading(true);
        const state = location.state as CheckoutState;
        console.log('[CheckoutPage] Received state:', state);
        if (!state || !state.vehicleId) {
          setError('Invalid checkout data. Please try booking again.');
          return;
        }
        setCheckoutData(state);
        console.log('[CheckoutPage] Fetching vehicle by ID:', state.vehicleId);
        const vehicleResponse = await getVehicleById(state.vehicleId);
        console.log('[CheckoutPage] getVehicleById response:', vehicleResponse);
        if (!vehicleResponse.success || !vehicleResponse.data) {
          setError('Vehicle not found.');
          return;
        }
        setVehicle(vehicleResponse.data);
      } catch (err) {
        setError('Failed to load checkout data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    loadCheckoutData();
  }, [location.state, user, navigate]);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      // Here you would integrate with your booking service
      console.log('Submitting booking:', {
        vehicle,
        checkoutData,
        formData
      });

      // For now, just show success and redirect
      alert('Booking submitted successfully!');
      navigate('/my-bookings');
    } catch (err) {
      console.error('Error submitting booking:', err);
      setError('Failed to submit booking. Please try again.');
    }
  };

  const calculateDays = () => {
    if (!checkoutData?.from || !checkoutData?.to) return 0;
    const diffTime = Math.abs(checkoutData.to.getTime() - checkoutData.from.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const calculateTotalPrice = () => {
    if (!vehicle || !checkoutData) return 0;
    const days = calculateDays();
    return vehicle.dailyRate * days;
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => navigate('/vehicles')}>
          Back to Vehicles
        </Button>
      </Container>
    );
  }

  if (!vehicle || !checkoutData) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Invalid checkout data. Please try booking again.
        </Alert>
        <Button variant="contained" onClick={() => navigate('/vehicles')}>
          Back to Vehicles
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Complete Your Booking
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            {activeStep === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Vehicle Details
                </Typography>
                <Card sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box display="flex" alignItems="center" mb={1}>
                          <CarIcon sx={{ mr: 1 }} />
                          <Typography variant="subtitle1">
                            {vehicle.brand} {vehicle.model}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {vehicle.year} • {vehicle.engine} • {vehicle.type}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Box display="flex" alignItems="center" mb={1}>
                          <LocationIcon sx={{ mr: 1 }} />
                          <Typography variant="subtitle1">
                            Pickup: {checkoutData.pickupLocation}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center">
                          <LocationIcon sx={{ mr: 1 }} />
                          <Typography variant="subtitle1">
                            Drop-off: {checkoutData.dropOffLocation}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12}>
                        <Box display="flex" alignItems="center">
                          <CalendarIcon sx={{ mr: 1 }} />
                          <Typography variant="subtitle1">
                            {format(checkoutData.from, 'MMM dd, yyyy')} - {format(checkoutData.to, 'MMM dd, yyyy')}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {calculateDays()} days
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Box>
            )}

            {activeStep === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Personal Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={formData.fullName}
                      onChange={(e) => handleFormChange('fullName', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleFormChange('email', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone"
                      value={formData.phone}
                      onChange={(e) => handleFormChange('phone', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="License Number"
                      value={formData.licenseNumber}
                      onChange={(e) => handleFormChange('licenseNumber', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Address"
                      value={formData.address}
                      onChange={(e) => handleFormChange('address', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="City"
                      value={formData.city}
                      onChange={(e) => handleFormChange('city', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Country"
                      value={formData.country}
                      onChange={(e) => handleFormChange('country', e.target.value)}
                      required
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {activeStep === 2 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Payment Information
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Payment integration will be implemented here. For now, this is a demo.
                </Alert>
                <Typography variant="body2" color="text.secondary">
                  Payment methods and secure checkout will be added in the next phase.
                </Typography>
              </Box>
            )}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
              >
                Back
              </Button>
              <Box>
                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleSubmit}
                  >
                    Complete Booking
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    onClick={handleNext}
                  >
                    Next
                  </Button>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>
            <Typography variant="h6" gutterBottom>
              Booking Summary
            </Typography>
            <Divider sx={{ my: 2 }} />
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Vehicle
              </Typography>
              <Typography variant="body1">
                {vehicle.brand} {vehicle.model}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Duration
              </Typography>
              <Typography variant="body1">
                {calculateDays()} days
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Daily Rate
              </Typography>
              <Typography variant="body1">
                ${vehicle.price}/day
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="h6">
                Total: ${calculateTotalPrice()}
              </Typography>
            </Box>

            <Button
              variant="contained"
              fullWidth
              size="large"
              disabled={activeStep !== steps.length - 1}
              onClick={handleSubmit}
            >
              Complete Booking
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CheckoutPage; 