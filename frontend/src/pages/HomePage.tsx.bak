import React, { useState, useEffect } from 'react'
import { 
  Container, 
  Typo<PERSON>, 
  Box, 
  Button, 
  Grid, 
  Card, 
  CardContent, 
  CircularProgress, 
  Alert, 
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  IconButton,
  AppBar,
  Toolbar,
  Avatar,
  Menu,
  ListItemIcon,
  Divider,
  TextField,
  Rating,
  Paper,
  Badge,
  CardMedia,
  CardActions,
  Fab,
  Skeleton,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  ToggleButton,
  ToggleButtonGroup,
  Backdrop,
  Stack,
  Tooltip
} from '@mui/material'
import { 
  DirectionsBike, 
  TwoWheeler, 
  ElectricScooter, 
  Refresh,
  Search,
  LocationOn,
  CalendarToday,
  Person,
  Login,
  Logout,
  AccountCircle,
  Security,
  Speed,
  Support,
  CheckCircle,
  Star,
  Phone,
  Email,
  Facebook,
  Instagram,
  Add as AddIcon,
  FilterList,
  ViewList,
  ViewModule,
  Map as MapIcon,
  Chat as ChatIcon,
  Notifications as NotificationsIcon,
  Twitter,
  YouTube,
  Favorite,
  FavoriteBorder,
  TrendingUp,
  LocalOffer,
  Verified,
  Message as MessageIcon,
  Close,
  BookOnline,
  LocationSearching,
  Timeline,
  Explore,
  LocalGasStation,
  AttachMoney,
  Schedule
} from '@mui/icons-material'
import { Link, useNavigate } from 'react-router-dom'
import ApiService from '../services/apiService'
import * as VehicleService from '../services/VehicleService'
import * as LocationService from '../services/LocationService'
import NotificationService from '../services/NotificationService'
import AuthModal from '../components/AuthModal'
import NotificationCenter from '../components/NotificationCenter'
import CommunicationPanel from '../components/CommunicationPanel'
import BookingFlow from '../components/BookingFlow'
import MapVehicleSearch from '../components/MapVehicleSearch'
import { useAuth } from '../contexts/AuthContext'
import { Vehicle, Booking, VehicleCategory as VehicleCategoryEnum, VehicleStatus, SearchFilters, GetVehiclesPayload, Location, VehicleFeature } from '../types'

interface VehicleCategory {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  type: string
  price: string
  features: string[]
}

interface VehicleCounts {
  [key: string]: number
}

interface FeaturedVehicle {
  id: number
  name: string
  type: string
  price: number
  location: string
  rating: number
  image: string
  isAvailable: boolean
}

interface Testimonial {
  id: number
  name: string
  rating: number
  comment: string
  location: string
  avatar: string
}

const HomePage: React.FC = () => {
  const [vehicleCategories, setVehicleCategories] = useState<VehicleCategory[]>([
    {
      id: 'SCOOTER',
      name: 'Scooters',
      description: 'Perfect for city exploration. Easy to ride and park anywhere.',
      icon: <TwoWheeler sx={{ fontSize: 60, color: '#2BB673' }} />,
      type: 'SCOOTER',
      price: '$25/day',
      features: ['Easy to ride', 'Great fuel economy', 'Perfect for city']
    },
    {
      id: 'LUXURY_BIKE',
      name: 'Luxury Bikes',
      description: 'Premium motorcycles for the ultimate Bali adventure.',
      icon: <DirectionsBike sx={{ fontSize: 60, color: '#2BB673' }} />,
      type: 'LUXURY_BIKE',
      price: '$80/day',
      features: ['Premium experience', 'High performance', 'Comfortable for long trips']
    },
    {
      id: 'ELECTRIC_BIKE',
      name: 'Electric Bikes',
      description: 'Eco-friendly electric vehicles for sustainable travel.',
      icon: <ElectricScooter sx={{ fontSize: 60, color: '#2BB673' }} />,
      type: 'ELECTRIC_BIKE',
      price: '$100/day',
      features: ['Zero emissions', 'Silent operation', 'Low maintenance']
    }
  ])

  const [availableCounts, setAvailableCounts] = useState<VehicleCounts>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [apiHealth, setApiHealth] = useState<any>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [featuredVehicles, setFeaturedVehicles] = useState<FeaturedVehicle[]>([])
  const [testimonials] = useState<Testimonial[]>([
    {
      id: 1,
      name: "Sarah Johnson",
      rating: 5,
      comment: "Amazing experience! The scooter was perfect for exploring Ubud. Great service and very reliable.",
      location: "Ubud, Bali",
      avatar: "https://i.pravatar.cc/150?img=1"
    },
    {
      id: 2,
      name: "Mike Chen",
      rating: 5,
      comment: "Rented a luxury bike for our honeymoon. The Harley was in perfect condition and made our trip unforgettable.",
      location: "Canggu, Bali",
      avatar: "https://i.pravatar.cc/150?img=2"
    },
    {
      id: 3,
      name: "Emma Rodriguez",
      rating: 4,
      comment: "Electric bike was fantastic! Eco-friendly and perfect for exploring the rice terraces. Highly recommend!",
      location: "Tegallalang, Bali",
      avatar: "https://i.pravatar.cc/150?img=3"
    }
  ])
  const navigate = useNavigate()
  const { user, signOut } = useAuth()

  // Auth state
  const [authDialogOpen, setAuthDialogOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'signup'>('login')

  // Communication state
  const [communicationOpen, setCommunicationOpen] = useState(false)

  // Booking flow state
  const [bookingFlowOpen, setBookingFlowOpen] = useState(false)
  const [selectedVehicleForBooking, setSelectedVehicleForBooking] = useState<Vehicle | null>(null)

  // Map search state
  const [mapSearchOpen, setMapSearchOpen] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid')

  // Enhanced search form state
  const [searchForm, setSearchForm] = useState({
    location: '',
    startDate: '',
    endDate: '',
    vehicleType: '',
    radius: 10, // km
    priceRange: [0, 200] as [number, number]
  })

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info' as 'success' | 'error' | 'warning' | 'info'
  })

  // Loading states
  const [loadingBooking, setLoadingBooking] = useState(false)

  // Initialize notifications when user is logged in
  useEffect(() => {
    if (user) {
      NotificationService.initialize(user.id);
    }
    
    return () => {
      NotificationService.disconnect();
    };
  }, [user]);

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🔄 Starting data fetch...')

      // Check API health
      console.log('🏥 Checking API health...')
      try {
        // Mock health check for now
        const healthResponse = { status: 'healthy', timestamp: new Date() }
        console.log('✅ API Health Check Success:', healthResponse)
        setApiHealth(healthResponse)
      } catch (healthError: any) {
        console.error('❌ API Health Check Failed:', healthError)
        setApiHealth({ status: 'unhealthy', error: healthError.message })
      }

      // Fetch vehicle counts
      try {
        console.log('🚗 Fetching vehicle counts...')
        // Use VehicleService instead of ApiService
        const vehicleCountsResponse = await VehicleService.getVehicleCounts()
        console.log('📊 Vehicle Counts Response:', vehicleCountsResponse)
        
        if (vehicleCountsResponse && vehicleCountsResponse.success) {
          setAvailableCounts(vehicleCountsResponse.data || {})
          console.log('✅ Vehicle counts updated:', vehicleCountsResponse.data)
        } else {
          console.warn('⚠️ No vehicle counts in response, using fallback')
          setAvailableCounts({
            'SCOOTER': 12,
            'LUXURY_BIKE': 8,
            'ELECTRIC_BIKE': 5
          })
        }
      } catch (countsError: any) {
        console.error('❌ Failed to fetch vehicle counts:', countsError)
        setError(`Failed to load vehicle data: ${countsError.message}`)
        // Set fallback counts
        setAvailableCounts({
          'SCOOTER': 12,
          'LUXURY_BIKE': 8,
          'ELECTRIC_BIKE': 5
        })
      }

      // Fetch featured vehicles
      try {
        console.log('⭐ Fetching featured vehicles...')
        const vehiclesResponse = await VehicleService.searchVehicles({
          filters: { deliveryAvailable: true },
          page: 1,
          size: 6
        })
        if (vehiclesResponse && vehiclesResponse.success && vehiclesResponse.data) {
          const featured = vehiclesResponse.data.data
            .slice(0, 6)
            .map((v: any) => ({
              id: v.id,
              name: `${v.make} ${v.model}` || v.name,
              type: v.category,
              price: v.dailyRate,
              location: v.location?.address || 'Bali',
              rating: 4.5 + Math.random() * 0.5, // Random rating between 4.5-5.0
              image: v.images?.[0] || `/images/vehicles/${v.category?.toLowerCase()}.svg`,
              isAvailable: v.status === 'available'
            }))
          setFeaturedVehicles(featured)
          console.log('✅ Featured vehicles updated:', featured)
        } else {
          // Set fallback featured vehicles
          setFeaturedVehicles([
            {
              id: 1,
              name: "Honda PCX 150",
              type: "SCOOTER",
              price: 25,
              location: "Kuta",
              rating: 4.8,
              image: "/images/vehicles/scooter.svg",
              isAvailable: true
            },
            {
              id: 2,
              name: "Harley Davidson Sportster",
              type: "LUXURY_BIKE",
              price: 80,
              location: "Ubud",
              rating: 4.9,
              image: "/images/vehicles/luxury.svg",
              isAvailable: true
            },
            {
              id: 3,
              name: "Tesla Model S",
              type: "ELECTRIC_BIKE",
              price: 100,
              location: "Nusa Dua",
              rating: 4.7,
              image: "/images/vehicles/electric.svg",
              isAvailable: true
            }
          ])
        }
      } catch (featuredError: any) {
        console.error('❌ Failed to fetch featured vehicles:', featuredError)
        // Set fallback featured vehicles
        setFeaturedVehicles([
          {
            id: 1,
            name: "Honda PCX 150",
            type: "SCOOTER",
            price: 25,
            location: "Kuta",
            rating: 4.8,
            image: "/images/vehicles/scooter.svg",
            isAvailable: true
          },
          {
            id: 2,
            name: "Harley Davidson Sportster",
            type: "LUXURY_BIKE",
            price: 80,
            location: "Ubud",
            rating: 4.9,
            image: "/images/vehicles/luxury.svg",
            isAvailable: true
          },
          {
            id: 3,
            name: "Tesla Model S",
            type: "ELECTRIC_BIKE",
            price: 100,
            location: "Nusa Dua",
            rating: 4.7,
            image: "/images/vehicles/electric.svg",
            isAvailable: true
          }
        ])
      }

      setLastUpdated(new Date())
      console.log('✅ Data fetch completed successfully')

    } catch (err: any) {
      console.error('❌ Failed to fetch data', err)
      setError(err.message || 'Failed to load information')
    } finally {
      setLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchData()
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      if (!loading) {
        fetchData()
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    setIsRefreshing(true)
    fetchData()
  }

  const handleAuthSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Auth is now handled by AuthModal component
    setAuthDialogOpen(false)
  }

  const handleAuthClick = (mode: 'login' | 'signup') => {
    setAuthMode(mode)
    setAuthDialogOpen(true)
  }

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const params = new URLSearchParams()
    if (searchForm.location) params.set('location', searchForm.location)
    if (searchForm.startDate) params.set('startDate', searchForm.startDate)
    if (searchForm.endDate) params.set('endDate', searchForm.endDate)
    if (searchForm.vehicleType) params.set('type', searchForm.vehicleType)
    if (searchForm.radius !== 10) params.set('radius', searchForm.radius.toString())
    
    navigate(`/search?${params.toString()}`)
  }

  const handleVehicleSelect = (vehicle: FeaturedVehicle) => {
    if (!user) {
      setSnackbar({
        open: true,
        message: 'Please sign in to book a vehicle',
        severity: 'warning'
      })
      setAuthDialogOpen(true)
      return
    }
    
    // Convert FeaturedVehicle to Vehicle format for booking
    const vehicleForBooking: Vehicle = {
      id: vehicle.id.toString(),
      providerId: 'unknown', // Will be fetched from backend
      category: vehicle.type as any,
      make: vehicle.name.split(' ')[0] || 'Unknown',
      model: vehicle.name.split(' ').slice(1).join(' ') || 'Model',
      year: 2023,
      engineSize: 150,
      transmission: 'automatic',
      fuelType: 'petrol',
      status: vehicle.isAvailable ? VehicleStatus.Available : VehicleStatus.Unavailable,
      dailyRate: vehicle.price,
      weeklyRate: vehicle.price * 6,
      monthlyRate: vehicle.price * 25,
      securityDeposit: vehicle.price * 2,
      minimumRentalDays: 1,
      maximumRentalDays: 30,
      quantity: 1,
      availableQuantity: vehicle.isAvailable ? 1 : 0,
      location: {
        id: '1',
        address: vehicle.location,
        city: 'Bali',
        state: 'Bali',
        country: 'Indonesia',
        postalCode: '80000',
        latitude: -8.3405,
        longitude: 115.0920
      },
      images: [vehicle.image],
      features: [
        { id: '1', name: 'Helmet included' },
        { id: '2', name: 'Insurance coverage' },
        { id: '3', name: 'GPS tracking' }
      ],
      description: `Experience the freedom of exploring Bali with this ${vehicle.name}`,
      deliveryAvailable: true,
      deliveryFee: 10,
      deliveryRadius: 15,
      addOns: [],
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    setSelectedVehicleForBooking(vehicleForBooking)
    setBookingFlowOpen(true)
  }

  const handleBookingComplete = async (booking: Booking) => {
    setBookingFlowOpen(false)
    setSelectedVehicleForBooking(null)
    setSnackbar({
      open: true,
      message: 'Booking completed successfully! Check your email for confirmation.',
      severity: 'success'
    })
    
    // Refresh vehicle data
    await fetchData()
  }

  const handleMapSearchVehicle = (vehicle: Vehicle) => {
    setMapSearchOpen(false)
    // Convert Vehicle to compatible format and book
    setSelectedVehicleForBooking(vehicle)
    setBookingFlowOpen(true)
  }

  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newMode: 'grid' | 'map' | null,
  ) => {
    if (newMode !== null) {
      setViewMode(newMode)
      if (newMode === 'map') {
        setMapSearchOpen(true)
      }
    }
  }

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'success'
      case 'degraded': return 'warning'
      case 'unhealthy': return 'error'
      default: return 'info'
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  return (
    <Box>
      {/* Header with Auth */}
      <AppBar position="static" sx={{ backgroundColor: '#2BB673' }}>
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Typography
            variant="h5"
            component={Link}
            to="/"
            sx={{
              textDecoration: 'none',
              color: 'inherit',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            🏍️ RentaHub
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            {user ? (
              <>
                <IconButton
                  color="inherit"
                  onClick={() => setCommunicationOpen(true)}
                  aria-label="messages"
                >
                  <MessageIcon />
                </IconButton>
                <NotificationCenter />
                <Button
                  color="inherit"
                  component={Link}
                  to="/profile"
                  startIcon={<AccountCircle />}
                >
                  {user.firstName || user.email}
                </Button>
                <Button
                  color="inherit"
                  onClick={handleLogout}
                  startIcon={<Logout />}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  color="inherit"
                  component={Link}
                  to="/search"
                  startIcon={<Search />}
                >
                  Browse Vehicles
                </Button>
                <Button
                  color="inherit"
                  onClick={() => setAuthDialogOpen(true)}
                  startIcon={<Person />}
                >
                  Sign In
                </Button>
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Box 
        sx={{ 
          background: 'linear-gradient(135deg, #2BB673 0%, #1e8e5a 100%)',
          color: 'white',
          py: 12,
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
            Explore Bali Your Way
          </Typography>
          <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 6, opacity: 0.9 }}>
            Rent scooters, motorbikes, and electric bikes from trusted local providers
          </Typography>
          
          {/* Enhanced Search Form */}
          <Card sx={{ p: 4, maxWidth: 900, mx: 'auto', backgroundColor: 'rgba(255,255,255,0.95)' }}>
            <Box component="form" onSubmit={handleSearchSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" color="text.primary">
                      Find Your Perfect Ride
                    </Typography>
                    <ToggleButtonGroup
                      value={viewMode}
                      exclusive
                      onChange={handleViewModeChange}
                      size="small"
                    >
                      <ToggleButton value="grid">
                        <ViewModule sx={{ mr: 1 }} />
                        Grid
                      </ToggleButton>
                      <ToggleButton value="map">
                        <MapIcon sx={{ mr: 1 }} />
                        Map
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    label="Location"
                    value={searchForm.location}
                    onChange={(e) => setSearchForm({...searchForm, location: e.target.value})}
                    placeholder="e.g. Ubud, Canggu"
                    InputProps={{
                      startAdornment: <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    label="Radius (km)"
                    type="number"
                    value={searchForm.radius}
                    onChange={(e) => setSearchForm({...searchForm, radius: parseInt(e.target.value) || 10})}
                    inputProps={{ min: 1, max: 100 }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    label="Start Date"
                    type="date"
                    value={searchForm.startDate}
                    onChange={(e) => setSearchForm({...searchForm, startDate: e.target.value})}
                    InputLabelProps={{ shrink: true }}
                    InputProps={{
                      startAdornment: <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    value={searchForm.endDate}
                    onChange={(e) => setSearchForm({...searchForm, endDate: e.target.value})}
                    InputLabelProps={{ shrink: true }}
                    InputProps={{
                      startAdornment: <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Vehicle Type</InputLabel>
                    <Select
                      value={searchForm.vehicleType}
                      onChange={(e) => setSearchForm({...searchForm, vehicleType: e.target.value})}
                      label="Vehicle Type"
                    >
                      <MenuItem value="">All Types</MenuItem>
                      <MenuItem value="SCOOTER">Scooter</MenuItem>
                      <MenuItem value="LUXURY_BIKE">Luxury Bike</MenuItem>
                      <MenuItem value="ELECTRIC_BIKE">Electric Bike</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    fullWidth
                    sx={{ 
                      backgroundColor: '#FF5F57',
                      '&:hover': { backgroundColor: '#e54e47' },
                      py: 1.5,
                      height: '56px'
                    }}
                    startIcon={<Search />}
                  >
                    Search Vehicles
                  </Button>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    variant="outlined"
                    size="large"
                    fullWidth
                    onClick={() => setMapSearchOpen(true)}
                    sx={{ 
                      py: 1.5,
                      height: '56px',
                      borderColor: '#2BB673',
                      color: '#2BB673',
                      '&:hover': { 
                        borderColor: '#1e8e5a',
                        backgroundColor: 'rgba(43, 182, 115, 0.04)'
                      }
                    }}
                    startIcon={<MapIcon />}
                  >
                    Map Search
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Card>
        </Container>
      </Box>

      {/* Featured Vehicles Section */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
            <Typography variant="h3" component="h2">
              Featured Vehicles
            </Typography>
            <Button
              component={Link}
              to="/search"
              variant="outlined"
              startIcon={<TrendingUp />}
            >
              View All
            </Button>
          </Box>
          
          <Grid container spacing={3}>
            {featuredVehicles.map((vehicle) => (
              <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
                <Card sx={{ 
                  height: '100%', 
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}>
                  <Box sx={{ position: 'relative' }}>
                    <Box
                      sx={{
                        height: 200,
                        backgroundColor: '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundImage: `url(${vehicle.image})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      {!vehicle.isAvailable && (
                        <Chip 
                          label="Not Available" 
                          color="error" 
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                        />
                      )}
                    </Box>
                    <IconButton
                      sx={{ position: 'absolute', top: 8, left: 8 }}
                      size="small"
                    >
                      <FavoriteBorder />
                    </IconButton>
                  </Box>
                  
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" component="h3">
                        {vehicle.name}
                      </Typography>
                      <Chip 
                        label={vehicle.type.replace('_', ' ')} 
                        size="small" 
                        color="primary"
                      />
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {vehicle.location}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Rating value={vehicle.rating} readOnly size="small" />
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        ({vehicle.rating})
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="h6" color="primary" fontWeight="bold">
                        {formatPrice(vehicle.price)}/day
                      </Typography>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => handleVehicleSelect(vehicle)}
                        disabled={!vehicle.isAvailable || !user}
                        sx={{ 
                          backgroundColor: '#FF5F57',
                          '&:hover': { backgroundColor: '#e54e47' }
                        }}
                        startIcon={<BookOnline />}
                      >
                        {!user ? 'Sign In to Book' : !vehicle.isAvailable ? 'Not Available' : 'Book Now'}
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Quick Access Features */}
      <Box sx={{ py: 6, backgroundColor: '#f0f9ff' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
            Quick Access
          </Typography>
          
          <Grid container spacing={4}>
            <Grid item xs={12} sm={6} md={3}>
              <Card 
                sx={{ 
                  height: '100%', 
                  textAlign: 'center', 
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
                onClick={() => setMapSearchOpen(true)}
              >
                <LocationSearching sx={{ fontSize: 60, color: '#FF5F57', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Find Nearby
                </Typography>
                <Typography color="text.secondary">
                  Discover vehicles near your location with our interactive map
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card 
                sx={{ 
                  height: '100%', 
                  textAlign: 'center', 
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
                onClick={() => navigate('/search?type=SCOOTER')}
              >
                <TwoWheeler sx={{ fontSize: 60, color: '#2BB673', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Popular Scooters
                </Typography>
                <Typography color="text.secondary">
                  Browse our most popular scooter models for city exploration
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card 
                sx={{ 
                  height: '100%', 
                  textAlign: 'center', 
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
                onClick={() => navigate('/search?delivery=true')}
              >
                <LocalOffer sx={{ fontSize: 60, color: '#9C27B0', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Delivery Available
                </Typography>
                <Typography color="text.secondary">
                  Get your vehicle delivered directly to your accommodation
                </Typography>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card 
                sx={{ 
                  height: '100%', 
                  textAlign: 'center', 
                  p: 3,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
                onClick={() => navigate('/search?duration=long-term')}
              >
                <Schedule sx={{ fontSize: 60, color: '#FF9800', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Long-term Deals
                </Typography>
                <Typography color="text.secondary">
                  Special rates for weekly and monthly rentals
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Vehicle Categories Section */}
      <Box sx={{ py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
            Choose Your Perfect Ride
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : vehicleCategories && vehicleCategories.length > 0 ? (
            <Grid container spacing={4}>
              {vehicleCategories.map((category) => (
                <Grid item xs={12} md={4} key={category.id}>
                  <Card
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: 8,
                      },
                    }}
                    onClick={() => navigate(`/search?type=${category.type}`)}
                  >
                    <Box sx={{ fontSize: 64, mb: 2 }}>{category.icon}</Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {category.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {category.description}
                    </Typography>
                    <Chip label={category.price} color="primary" sx={{ mb: 2 }} />
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
                      {category.features.map((feature, idx) => (
                        <Chip key={idx} label={feature} size="small" />
                      ))}
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Alert severity="info">No vehicle categories available</Alert>
          )}
        </Container>
      </Box>

      {/* Customer Testimonials */}
      <Box sx={{ py: 8, backgroundColor: '#f8f9fa' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
            What Our Customers Say
          </Typography>
          
          <Grid container spacing={4}>
            {testimonials.map((testimonial) => (
              <Grid item xs={12} md={4} key={testimonial.id}>
                <Card sx={{ height: '100%', p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      src={testimonial.avatar} 
                      sx={{ mr: 2, width: 56, height: 56 }}
                    />
                    <Box>
                      <Typography variant="h6">{testimonial.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.location}
                      </Typography>
                    </Box>
                  </Box>
                  <Rating value={testimonial.rating} readOnly size="small" sx={{ mb: 2 }} />
                  <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                    "{testimonial.comment}"
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Stats Section */}
      <Box sx={{ py: 8, backgroundColor: '#2BB673', color: 'white' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} textAlign="center">
            <Grid item xs={6} md={3}>
              <Typography variant="h3" component="div" fontWeight="bold">
                1000+
              </Typography>
              <Typography variant="h6">
                Vehicles Available
              </Typography>
            </Grid>
            <Grid item xs={6} md={3}>
              <Typography variant="h3" component="div" fontWeight="bold">
                500+
              </Typography>
              <Typography variant="h6">
                Trusted Providers
              </Typography>
            </Grid>
            <Grid item xs={6} md={3}>
              <Typography variant="h3" component="div" fontWeight="bold">
                50+
              </Typography>
              <Typography variant="h6">
                Locations in Bali
              </Typography>
            </Grid>
            <Grid item xs={6} md={3}>
              <Typography variant="h3" component="div" fontWeight="bold">
                4.8★
              </Typography>
              <Typography variant="h6">
                Average Rating
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Booking Flow Modal */}
      <BookingFlow
        open={bookingFlowOpen}
        onClose={() => {
          setBookingFlowOpen(false)
          setSelectedVehicleForBooking(null)
        }}
        vehicle={selectedVehicleForBooking}
        onBookingComplete={handleBookingComplete}
      />

      {/* Map Search Modal */}
      <Dialog
        open={mapSearchOpen}
        onClose={() => setMapSearchOpen(false)}
        maxWidth="xl"
        fullWidth
        fullScreen
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Find Vehicles Near You</Typography>
            <IconButton onClick={() => setMapSearchOpen(false)}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <MapVehicleSearch
            onVehicleSelect={handleMapSearchVehicle}
            searchFilters={searchForm}
          />
        </DialogContent>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Footer */}
      <Box sx={{ py: 6, backgroundColor: '#1a1a1a', color: 'white' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Typography variant="h5" gutterBottom>
                🏍️ RentaHub
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Your trusted scooter and bike rental platform in Bali. Explore the island your way with our wide selection of vehicles.
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton color="inherit" size="small">
                  <Facebook />
                </IconButton>
                <IconButton color="inherit" size="small">
                  <Instagram />
                </IconButton>
                <IconButton color="inherit" size="small">
                  <Twitter />
                </IconButton>
                <IconButton color="inherit" size="small">
                  <YouTube />
                </IconButton>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Link to="/search" style={{ color: 'inherit', textDecoration: 'none' }}>
                  Browse Vehicles
                </Link>
                <Link to="/about" style={{ color: 'inherit', textDecoration: 'none' }}>
                  About Us
                </Link>
                <Link to="/contact" style={{ color: 'inherit', textDecoration: 'none' }}>
                  Contact
                </Link>
                <Link to="/support" style={{ color: 'inherit', textDecoration: 'none' }}>
                  Support
                </Link>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Contact Info
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Phone sx={{ mr: 1, fontSize: 16 }} />
                  <Typography variant="body2">+62 812-3456-7890</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Email sx={{ mr: 1, fontSize: 16 }} />
                  <Typography variant="body2"><EMAIL></Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3, borderColor: 'rgba(255,255,255,0.2)' }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2">
              © 2024 RentaHub. All rights reserved.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Link to="/privacy" style={{ color: 'inherit', textDecoration: 'none' }}>
                Privacy Policy
              </Link>
              <Link to="/terms" style={{ color: 'inherit', textDecoration: 'none' }}>
                Terms of Service
              </Link>
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Auth Modal */}
      <AuthModal
        open={authDialogOpen}
        onClose={() => setAuthDialogOpen(false)}
        mode={authMode}
      />

      {/* Communication Panel */}
      <CommunicationPanel
        open={communicationOpen}
        onClose={() => setCommunicationOpen(false)}
      />
    </Box>
  );
};

export default HomePage; 