import { supabase } from '../utils/supabaseClient';

// This script uploads scraped vehicle data to Supabase
// Run this once to populate your Supabase database with vehicle data

const uploadScrapedDataToSupabase = async () => {
  try {
    console.log('🔄 Starting upload of scraped data to Supabase...');
    
    // Sample vehicle data to upload
    const vehiclesToUpload = [
      {
        make: 'Honda',
        model: 'Click 125i',
        category: 'scooter',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
      },
      {
        make: 'Yamaha',
        model: 'NMAX 155',
        category: 'scooter',
        image_url: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'
      },
      {
        make: 'Honda',
        model: 'PCX 160',
        category: 'scooter',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
      },
      {
        make: 'Yamaha',
        model: 'XSR 155',
        category: 'motorcycle',
        image_url: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'
      },
      {
        make: 'Kawasaki',
        model: 'Ninja 250',
        category: 'motorcycle',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
      },
      {
        make: 'Suzuki',
        model: 'GSX-R150',
        category: 'motorcycle',
        image_url: 'https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=400'
      }
    ];

    // Upload to vehicle_catalog table
    const { data, error } = await supabase
      ?.from('vehicle_catalog')
      .upsert(vehiclesToUpload, { onConflict: 'make,model' });

    if (error) {
      console.error('❌ Error uploading to vehicle_catalog:', error);
      return;
    }

    console.log('✅ Successfully uploaded vehicle data to Supabase');
    console.log('📊 Uploaded vehicles:', data?.length || 0);
    
  } catch (error) {
    console.error('❌ Upload failed:', error);
  }
};

// Run the upload
uploadScrapedDataToSupabase(); 