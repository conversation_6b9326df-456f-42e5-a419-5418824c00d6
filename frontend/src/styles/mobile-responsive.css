/* Mobile-First Responsive Design for RentaHub */

/* Base mobile styles (320px and up) */
@media (min-width: 320px) {
  .container {
    padding: 0 16px;
  }
  
  .vehicle-assistance-button {
    width: 100%;
    margin-bottom: 16px;
    font-size: 0.875rem;
    padding: 12px 16px;
  }
  
  .language-switcher {
    min-width: 120px;
  }
  
  .assistance-dialog {
    margin: 8px;
    max-height: calc(100vh - 16px);
  }
  
  .assistance-category-grid {
    gap: 8px;
  }
  
  .assistance-category-button {
    min-height: 80px;
    padding: 8px;
    font-size: 0.75rem;
  }
  
  .assistance-priority-chip {
    font-size: 0.6rem;
    height: 16px;
  }
  
  .vehicle-card {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .booking-form {
    padding: 16px;
  }
  
  .search-filters {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-chip {
    margin: 2px;
    font-size: 0.75rem;
  }
}

/* Small mobile devices (375px and up) */
@media (min-width: 375px) {
  .container {
    padding: 0 20px;
  }
  
  .vehicle-assistance-button {
    font-size: 0.9rem;
    padding: 14px 20px;
  }
  
  .assistance-category-button {
    min-height: 85px;
    font-size: 0.8rem;
  }
  
  .language-switcher {
    min-width: 140px;
  }
}

/* Large mobile devices (414px and up) */
@media (min-width: 414px) {
  .assistance-category-button {
    min-height: 90px;
    font-size: 0.85rem;
  }
  
  .vehicle-card {
    border-radius: 16px;
  }
  
  .booking-form {
    padding: 20px;
  }
}

/* Small tablets (768px and up) */
@media (min-width: 768px) {
  .container {
    padding: 0 24px;
  }
  
  .vehicle-assistance-button {
    width: auto;
    min-width: 200px;
    font-size: 1rem;
    padding: 16px 24px;
  }
  
  .assistance-dialog {
    margin: 32px;
    max-height: calc(100vh - 64px);
  }
  
  .assistance-category-grid {
    gap: 16px;
  }
  
  .assistance-category-button {
    min-height: 100px;
    font-size: 0.9rem;
    padding: 16px;
  }
  
  .language-switcher {
    min-width: 160px;
  }
  
  .search-filters {
    flex-direction: row;
    gap: 16px;
  }
  
  .vehicle-card {
    margin-bottom: 20px;
  }
  
  .booking-form {
    padding: 24px;
  }
}

/* Large tablets (1024px and up) */
@media (min-width: 1024px) {
  .container {
    padding: 0 32px;
  }
  
  .assistance-category-button {
    min-height: 110px;
    font-size: 1rem;
    padding: 20px;
  }
  
  .vehicle-card {
    margin-bottom: 24px;
    border-radius: 20px;
  }
  
  .booking-form {
    padding: 32px;
  }
}

/* Desktop (1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
  }
  
  .assistance-category-button {
    min-height: 120px;
    font-size: 1.1rem;
    padding: 24px;
  }
  
  .language-switcher {
    min-width: 180px;
  }
}

/* Large desktop (1440px and up) */
@media (min-width: 1440px) {
  .container {
    max-width: 1440px;
    padding: 0 48px;
  }
}

/* Touch-specific styles */
@media (hover: none) and (pointer: coarse) {
  .assistance-category-button {
    min-height: 88px;
    padding: 16px 12px;
  }
  
  .assistance-category-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  .vehicle-assistance-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  .language-switcher button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
  
  /* Increase touch targets */
  .MuiIconButton-root {
    min-width: 44px;
    min-height: 44px;
  }
  
  .MuiButton-root {
    min-height: 44px;
  }
  
  .MuiChip-root {
    min-height: 32px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .vehicle-card img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  .assistance-category-icon {
    transform: scale(1.1);
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .assistance-dialog {
    margin: 4px;
    max-height: calc(100vh - 8px);
  }
  
  .assistance-category-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .assistance-category-button {
    min-height: 60px;
    font-size: 0.7rem;
    padding: 8px;
  }
  
  .vehicle-card {
    margin-bottom: 12px;
  }
}

/* Portrait orientation on tablets */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .assistance-category-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .assistance-category-button {
    min-height: 120px;
  }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  /* Ensure sufficient color contrast */
  .assistance-priority-urgent {
    background-color: #d32f2f;
    color: white;
  }
  
  .assistance-priority-high {
    background-color: #f57c00;
    color: white;
  }
  
  .assistance-priority-medium {
    background-color: #1976d2;
    color: white;
  }
  
  .assistance-priority-low {
    background-color: #388e3c;
    color: white;
  }
  
  /* Focus indicators */
  .assistance-category-button:focus {
    outline: 3px solid #2196f3;
    outline-offset: 2px;
  }
  
  .vehicle-assistance-button:focus {
    outline: 3px solid #2196f3;
    outline-offset: 2px;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .assistance-category-button,
    .vehicle-assistance-button,
    .language-switcher button {
      transition: none;
    }
    
    .assistance-category-button:active,
    .vehicle-assistance-button:active,
    .language-switcher button:active {
      transform: none;
    }
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .assistance-dialog {
    background-color: #1e1e1e;
    color: #ffffff;
  }
  
  .assistance-category-button {
    background-color: #2d2d2d;
    color: #ffffff;
    border-color: #404040;
  }
  
  .assistance-category-button:hover {
    background-color: #3d3d3d;
  }
  
  .vehicle-card {
    background-color: #2d2d2d;
    color: #ffffff;
  }
}

/* Print styles */
@media print {
  .vehicle-assistance-button,
  .language-switcher,
  .assistance-dialog {
    display: none !important;
  }
  
  .vehicle-card {
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .booking-form {
    break-inside: avoid;
  }
}
