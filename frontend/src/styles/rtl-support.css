/* RTL Support for RentaHub */

/* Base RTL styles */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
}

/* Arabic font support */
[dir="rtl"][lang="ar"] {
  font-family: 'Noto Sans Arabic', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
}

/* Layout adjustments for RTL */
[dir="rtl"] .MuiDrawer-paperAnchorLeft {
  right: 0;
  left: auto;
}

[dir="rtl"] .MuiDrawer-paperAnchorRight {
  left: 0;
  right: auto;
}

/* Navigation adjustments */
[dir="rtl"] .MuiAppBar-root {
  direction: rtl;
}

[dir="rtl"] .MuiToolbar-root {
  flex-direction: row-reverse;
}

/* Button icon positioning */
[dir="rtl"] .MuiButton-startIcon {
  margin-left: 8px;
  margin-right: -4px;
}

[dir="rtl"] .MuiButton-endIcon {
  margin-right: 8px;
  margin-left: -4px;
}

/* Form field adjustments */
[dir="rtl"] .MuiFormControl-root {
  direction: rtl;
}

[dir="rtl"] .MuiInputLabel-root {
  transform-origin: top right;
  right: 0;
  left: auto;
}

[dir="rtl"] .MuiInputLabel-shrink {
  transform-origin: top right;
}

[dir="rtl"] .MuiOutlinedInput-root {
  direction: rtl;
}

[dir="rtl"] .MuiOutlinedInput-input {
  text-align: right;
}

/* Select dropdown adjustments */
[dir="rtl"] .MuiSelect-icon {
  left: 7px;
  right: auto;
}

/* Chip adjustments */
[dir="rtl"] .MuiChip-deleteIcon {
  margin-left: 5px;
  margin-right: -6px;
}

/* List item adjustments */
[dir="rtl"] .MuiListItemIcon-root {
  min-width: 40px;
  margin-left: 16px;
  margin-right: 0;
}

[dir="rtl"] .MuiListItemText-root {
  text-align: right;
}

/* Card adjustments */
[dir="rtl"] .MuiCard-root {
  direction: rtl;
}

[dir="rtl"] .MuiCardHeader-action {
  margin-left: 0;
  margin-right: auto;
}

/* Dialog adjustments */
[dir="rtl"] .MuiDialogTitle-root {
  text-align: right;
}

[dir="rtl"] .MuiDialogActions-root {
  flex-direction: row-reverse;
}

/* Table adjustments */
[dir="rtl"] .MuiTableCell-root {
  text-align: right;
}

[dir="rtl"] .MuiTableCell-head {
  text-align: right;
}

/* Stepper adjustments */
[dir="rtl"] .MuiStepper-horizontal {
  direction: rtl;
}

[dir="rtl"] .MuiStepConnector-line {
  border-left-width: 1px;
  border-right-width: 0;
}

/* Breadcrumb adjustments */
[dir="rtl"] .MuiBreadcrumbs-separator {
  transform: rotate(180deg);
}

/* Custom component adjustments */
[dir="rtl"] .language-switcher {
  direction: rtl;
}

[dir="rtl"] .vehicle-card {
  direction: rtl;
}

[dir="rtl"] .booking-form {
  direction: rtl;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .MuiAppBar-root {
    padding-left: 8px;
    padding-right: 16px;
  }

  [dir="rtl"] .MuiDrawer-paper {
    width: 280px;
  }

  [dir="rtl"] .mobile-menu-button {
    margin-left: auto;
    margin-right: 0;
  }

  /* Vehicle assistance mobile adjustments */
  [dir="rtl"] .assistance-dialog .MuiDialog-paper {
    margin: 8px;
    width: calc(100% - 16px);
    max-height: calc(100% - 16px);
  }

  [dir="rtl"] .assistance-category-grid {
    gap: 8px !important;
  }

  [dir="rtl"] .assistance-category-button {
    min-height: 60px;
    font-size: 0.75rem;
  }

  [dir="rtl"] .language-switcher-mobile {
    min-width: 40px;
  }

  [dir="rtl"] .vehicle-card-mobile {
    padding: 12px;
  }

  [dir="rtl"] .booking-form-mobile {
    padding: 16px 8px;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  [dir="rtl"] .MuiContainer-root {
    padding-left: 24px;
    padding-right: 24px;
  }
}

/* Desktop adjustments */
@media (min-width: 1025px) {
  [dir="rtl"] .MuiContainer-root {
    padding-left: 32px;
    padding-right: 32px;
  }
}

/* Animation adjustments for RTL */
[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Specific adjustments for vehicle assistance dialog */
[dir="rtl"] .assistance-dialog .MuiGrid-container {
  direction: rtl;
}

[dir="rtl"] .assistance-category-button {
  text-align: right;
}

[dir="rtl"] .assistance-priority-chip {
  margin-left: 8px;
  margin-right: 0;
}

/* Language switcher specific RTL adjustments */
[dir="rtl"] .language-menu-item {
  direction: rtl;
}

[dir="rtl"] .language-flag {
  margin-left: 8px;
  margin-right: 0;
}

/* Vehicle search and filter adjustments */
[dir="rtl"] .vehicle-search-filters {
  direction: rtl;
}

[dir="rtl"] .filter-chip {
  margin-left: 8px;
  margin-right: 0;
}

/* Booking flow adjustments */
[dir="rtl"] .booking-stepper {
  direction: rtl;
}

[dir="rtl"] .booking-summary {
  text-align: right;
}

/* Admin dashboard adjustments */
[dir="rtl"] .admin-sidebar {
  right: 0;
  left: auto;
}

[dir="rtl"] .admin-main-content {
  margin-right: 240px;
  margin-left: 0;
}

/* Print styles for RTL */
@media print {
  [dir="rtl"] body {
    direction: rtl;
    text-align: right;
  }
  
  [dir="rtl"] .print-header {
    text-align: right;
  }
}
