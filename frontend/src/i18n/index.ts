import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import Backend from 'i18next-http-backend'

// Import translation files
import enCommon from './locales/en/common.json'
import idCommon from './locales/id/common.json'
import enVehicle from './locales/en/vehicle.json'
import idVehicle from './locales/id/vehicle.json'
import enBooking from './locales/en/booking.json'
import idBooking from './locales/id/booking.json'

// Supported languages configuration
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'id', name: 'Bahasa Indonesia', flag: '🇮🇩' }
]

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    debug: import.meta.env.DEV,
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    resources: {
      en: {
        common: enCommon,
        vehicle: enVehicle,
        booking: enBooking
      },
      id: {
        common: idCommon,
        vehicle: idVehicle,
        booking: idBooking
      }
    },
    ns: ['common', 'vehicle', 'booking'], // Namespaces
    defaultNS: 'common',
    supportedLngs: ['en', 'id'],
    load: 'languageOnly', // Only load language code, not full locale
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage']
    }
  })

export default i18n 