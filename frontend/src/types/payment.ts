// Payment-related types for RentaHub

export enum PaymentMethodType {
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  PAYPAL = 'PAYPAL',
  STRIPE = 'STRIPE'
}

export interface PaymentMethod {
  id?: string;
  type: PaymentMethodType;
  lastFourDigits?: string;
  cardBrand?: string;
  isDefault?: boolean;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed';
  createdAt: Date;
}

export interface PaymentError {
  code: string;
  message: string;
}

// Payment types are now defined in index.ts to avoid conflicts 