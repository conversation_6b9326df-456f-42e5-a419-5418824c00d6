export interface SupportTicket {
  id: string;
  userId?: string;
  providerId?: string;
  bookingId?: string;
  category: 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'OTHER';
  message: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'ESCALATED';
  adminResponse?: string;
  createdAt: string;
}

export interface SOSAlert {
  id: string;
  bookingId: string;
  userId?: string;
  providerId?: string;
  reason: string;
  status: 'PENDING' | 'RESOLVED';
  createdAt: string;
}
