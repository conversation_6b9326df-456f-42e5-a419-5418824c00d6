// Booking Types

export interface CreateBookingPayload {
  vehicleId: string;
  startDate: string;
  endDate: string;
  insuranceOption?: string;
  additionalServices?: string[];
  specialRequests?: string;
  paymentMethodId?: string;
}

export interface Booking {
  id: string;
  vehicleId: string;
  userId: string;
  startDate: string;
  endDate: string;
  status: string;
  totalAmount: number;
  paymentId?: string;
  paymentStatus?: string;
  insuranceOption?: string;
  additionalServices?: string[];
  specialRequests?: string;
  checkinStatus?: {
    completed: boolean;
    completedAt?: string;
    notes?: string;
    damageReported?: boolean;
  };
  checkoutStatus?: {
    completed: boolean;
    completedAt?: string;
    notes?: string;
    damageReported?: boolean;
  };
  vehicleDetails?: {
    name: string;
    image: string;
    location: {
      address: string;
      latitude: number;
      longitude: number;
    };
  };
  refundDetails?: {
    amount: number;
    status: string;
    processedAt?: string;
  };
  cancellationDetails?: {
    reason: string;
    cancelledAt: string;
    cancelledBy: string;
    refundAmount?: number;
  };
  createdAt: string;
  updatedAt: string;
}
