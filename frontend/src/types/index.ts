// Common types aggregated for easier imports

// Booking types are defined inline to avoid conflicts
export enum VehicleCategory {
  SmallScooter = 'small_scooter',
  MediumScooter = 'medium_scooter',
  LargeScooter = 'large_scooter',
  ElectricScooter = 'electric_scooter',
  Motorcycle = 'motorcycle',
  ElectricMotorcycle = 'electric_motorcycle',
  Car = 'car',
  Truck = 'truck',
  Van = 'van'
}

// Add missing enums
export enum UserRole {
  Customer = 'customer',
  Provider = 'provider',
  Admin = 'admin'
}

export enum UserStatus {
  Active = 'active',
  Inactive = 'inactive',
  Suspended = 'suspended',
  Pending = 'pending'
}

export enum VehicleType {
  Scooter = 'scooter',
  Motorcycle = 'motorcycle',
  Car = 'car',
  Truck = 'truck',
  Van = 'van',
  ElectricScooter = 'electric_scooter',
  ElectricMotorcycle = 'electric_motorcycle'
}

export enum PaymentStatus {
  Pending = 'pending',
  Completed = 'completed',
  Failed = 'failed',
  Refunded = 'refunded'
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface GetVehiclesPayload {
  category?: VehicleCategory;
  location?: string;
  available?: boolean;
  [key: string]: any;
}

export interface CreateBookingPayload {
  vehicleId: string;
  startDate: string;
  endDate: string;
  addOns: string[];
  paymentMethod: PaymentMethod;
  deliveryRequested: boolean;
  deliveryAddress: string;
  pickupInstructions: string;
  notes: string;
  userId?: string;
  providerId?: string;
}

// User types
export enum UserType {
  Customer = 'customer',
  Provider = 'provider',
  Admin = 'admin',
  User = 'user'
}

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  profileImage?: string;
  type: UserType;
  verified: boolean;
  blacklisted: boolean;
  completedProfile: boolean;
  createdAt: string;
  updatedAt: string;
  // Legacy fields for backward compatibility
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role?: UserRole;
  status?: UserStatus;
}

// Vehicle types
export interface VehicleAddOn {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'equipment' | 'service' | 'insurance';
  available: boolean;
  dailyRate?: number;
}

export interface VehicleSpecifications {
  model: string;
  year: number;
  color: string;
  mileage: number;
  licensePlate: string;
  engineSize?: number;
  transmission?: string;
  fuelType?: string;
}

export interface Vehicle {
  id: string;
  name?: string;
  type?: VehicleType;
  category?: VehicleCategory;
  pricePerDay?: number;
  specifications?: VehicleSpecifications;
  images: string[];
  location?: Location;
  ownerId?: string;
  providerId?: string;
  status?: VehicleStatus;
  createdAt?: string;
  updatedAt?: string;
  addOns?: VehicleAddOn[];
  
  // Backend data structure properties
  brand?: string;
  model?: string;
  year?: string | number;
  engine?: string;
  dailyRate?: number;
  weeklyRate?: number;
  monthlyRate?: number;
  availableUnits?: number;
  insuranceOffered?: boolean;
  dropoffAvailable?: boolean;
  pickupAvailable?: boolean;
  helmetsIncluded?: number;
  raincoatsIncluded?: boolean;
  fullTank?: boolean;
  depositAmount?: number;
  vehicleType?: string;
  hasInsurance?: boolean;
  insurancePrice?: number;
  smartTags?: string[];
  specs?: string[];
  price?: string;
  
  // Additional properties that components expect
  make?: string;
  deliveryAvailable?: boolean;
  deliveryFee?: number;
  engineSize?: number;
  transmission?: string;
  fuelType?: string;
  
  // Properties that components are trying to access
  description?: string;
  features?: string[];
  rating?: number;
  reviews?: number;
  availableQuantity?: number;
  active?: boolean;
  securityDeposit?: number;
  deliveryRadius?: number;
  minimumRentalDays?: number;
  maximumRentalDays?: number;
  quantity?: number;
  pickupInstructions?: string;
  insuranceOptions?: {
    basic: { price: number; coverage: string };
    premium: { price: number; coverage: string };
  };
  rentalTerms?: string;
  
  // New field for single image
  image?: string;
}

export interface SearchFilters {
  location?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  startDate?: string;
  endDate?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  features?: string[];
  limit?: number;
  offset?: number;
  sort?: 'price_asc' | 'price_desc' | 'rating' | 'distance';
  vehicleType?: string;
  deliveryAvailable?: boolean;
}

export interface Review {
  id: string;
  vehicleId: string;
  userId: string;
  bookingId?: string;
  rating: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  userDetails?: {
    name: string;
    profileImage?: string;
  };
}

export interface VehicleOwner {
  id: string;
  type: UserType.Provider;
  name: string;
  email: string;
  phone?: string;
  profileImage?: string;
  averageRating?: number;
  totalReviews?: number;
  responseRate?: number;
  responseTime?: number;
}

// Payment types
export enum PaymentMethodType {
  CreditCard = 'credit_card',
  DebitCard = 'debit_card',
  BankTransfer = 'bank_transfer',
  DigitalWallet = 'digital_wallet'
}

export enum PaymentMethod {
  Card = 'card',
  PayPal = 'paypal',
  BankTransfer = 'bank_transfer',
  DigitalWallet = 'digital_wallet'
}

export interface PaymentMethodData {
  id: string;
  type: PaymentMethodType;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  createdAt: string;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed';
  paymentMethodId?: string;
  createdAt: string;
}

export interface PaymentError {
  code: string;
  message: string;
  type: 'validation' | 'payment' | 'network';
}

// Booking types
export interface Booking {
  id: string;
  vehicleId: string;
  userId: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  status: BookingStatus;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  insuranceOption?: string;
  additionalServices?: string[];
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
  vehicle?: Vehicle;
  user?: User;
  // Additional fields that components expect
  bookingStatus?: string;
  paymentMethod?: string;
  cash_confirmation_code?: string;
  totalDays?: number;
  deliveryRequested?: boolean;
  deliveryAddress?: string;
  pickupInstructions?: string;
  selectedAddOns?: any[];
  provider?: any;
  vehicleRate?: number;
  addOnsTotal?: number;
  deliveryFee?: number;
  total?: number;
  
  // New fields for comprehensive booking details
  pickupTime?: string;
  dropoffTime?: string;
  pickupLocation?: string;
  dropoffLocation?: string;
  specialInstructions?: string;
  rentalPrice?: number;
  totalPrice?: number;
  addons?: Array<{
    name: string;
    price: number;
    quantity: number;
  }>;
  accessories?: string[];
  notes?: string;
}

export interface UpdateBookingPayload {
  bookingId: string;
  startDate?: string;
  endDate?: string;
  insuranceOption?: string;
  additionalServices?: string[];
  specialRequests?: string;
}

export enum BookingStatus {
  Pending = 'pending',
  Confirmed = 'confirmed',
  Active = 'active',
  Completed = 'completed',
  Cancelled = 'cancelled'
}

export interface GetBookingsPayload {
  userId?: string;
  vehicleId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
  page?: number;
  size?: number;
}

// Pagination types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Notification types
export enum NotificationType {
  BookingConfirmation = 'booking_confirmation',
  BookingCancellation = 'booking_cancellation',
  PaymentSuccess = 'payment_success',
  PaymentFailed = 'payment_failed',
  VehicleAvailable = 'vehicle_available',
  ReviewReceived = 'review_received',
  SystemAlert = 'system_alert'
}

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  data?: any;
  createdAt: string;
}

// Location types
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  latitude: number;
  longitude: number;
  type: 'pickup' | 'dropoff' | 'both';
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

// Vehicle feature types
export interface VehicleFeature {
  id: string;
  name: string;
  description: string;
  category: string;
  icon?: string;
  active: boolean;
  createdAt: string;
}

// Add-on types
export interface AddOn {
  id: string;
  vehicleId: string;
  name: string;
  description: string;
  price: number;
  type: 'one_time' | 'per_day';
  active: boolean;
  createdAt: string;
}

// Vehicle status enum
export enum VehicleStatus {
  Available = 'available',
  Rented = 'rented',
  Maintenance = 'maintenance',
  Inactive = 'inactive',
  Unavailable = 'unavailable'
}

// Provider analytics
export interface ProviderAnalytics {
  totalEarnings: number;
  monthlyEarnings: number;
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  averageRating: number;
  totalReviews: number;
  cancellationRate: number;
  responseTime: number;
  responseRate: number;
}

export interface MessageThread {
  id: string;
  participants: User[];
  lastMessage: Message;
  unreadCount: number;
  updatedAt: string;
  booking?: Booking;
  vehicle?: Vehicle;
}

export interface Message {
  id: string;
  content: string;
  sender: User;
  timestamp: string;
  read: boolean;
  senderId?: string;
  createdAt?: string;
  isStarred?: boolean;
}

export interface NotificationCounter {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  types: NotificationType[];
}

export interface BookingDetails {
  id: string;
  vehicle: Vehicle;
  startDate: string;
  endDate: string;
  totalAmount: number;
  status: BookingStatus;
  paymentStatus: PaymentStatus;
}

export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  clientSecret?: string;
  error?: string;
}
