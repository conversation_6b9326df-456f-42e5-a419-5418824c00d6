// Database specific types

export interface Review {
  id: string
  bookingId: string
  userId: string
  providerId: string
  vehicleId: string
  rating: number
  comment?: string
  response?: string
  createdAt: string
  updatedAt: string
}

export interface DatabaseUser {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'USER' | 'PROVIDER' | 'ADMIN'
  createdAt: string
  updatedAt: string
}

export interface DatabaseVehicle {
  id: string
  providerId: string
  category: string
  make: string
  model: string
  year: number
  dailyRate: number
  securityDeposit: number
  active: boolean
  createdAt: string
  updatedAt: string
}

export interface DatabaseBooking {
  id: string
  userId: string
  providerId: string
  vehicleId: string
  startDate: string
  endDate: string
  total: number
  status: string
  createdAt: string
  updatedAt: string
}
