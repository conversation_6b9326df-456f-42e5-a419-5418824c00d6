// Vehicle Types
import { UserType } from './index';

export interface Vehicle {
  id: string;
  name: string;
  description: string;
  pricePerDay: number;
  pricePerHour?: number;
  type: string;
  category: string;
  images: string[];
  location: {
    address: string;
    latitude: number;
    longitude: number;
    city: string;
    state?: string;
    zipCode: string;
  };
  specifications: {
    model: string;
    year: number;
    mileage?: number;
    color: string;
    licensePlate?: string;
  };
  features: string[];
  availabilityCalendar?: {
    [date: string]: {
      available: boolean;
      priceOverride?: number;
    };
  };
  rating?: number;
  reviews?: number;
  ownerId: string;
  status: string;
  insuranceOptions?: {
    basic: {
      price: number;
      coverage: string;
    };
    premium?: {
      price: number;
      coverage: string;
    };
  };
  rentalTerms?: string;
  securityDeposit?: number;
  createdAt: string;
  updatedAt: string;
}

export interface SearchFilters {
  location?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  startDate?: string;
  endDate?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  features?: string[];
  limit?: number;
  offset?: number;
  sort?: 'price_asc' | 'price_desc' | 'rating' | 'distance';
  vehicleType?: string;
}

export interface Review {
  id: string;
  vehicleId: string;
  userId: string;
  bookingId?: string;
  rating: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  userDetails?: {
    name: string;
    profileImage?: string;
  };
}

export interface VehicleOwner {
  id: string;
  type: UserType.Provider;
  name: string;
  email: string;
  phone?: string;
  profileImage?: string;
  averageRating?: number;
  totalReviews?: number;
  responseRate?: number;
  responseTime?: number;
}
