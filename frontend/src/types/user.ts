// User Types

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'user' | 'provider' | 'admin';
  profileImage?: string;
  dateOfBirth?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  drivingLicense?: {
    number: string;
    expiry: string;
    state: string;
    verified: boolean;
  };
  paymentMethods?: {
    id: string;
    type: string;
    lastFour: string;
    isDefault: boolean;
  }[];
  verificationStatus?: {
    email: boolean;
    phone: boolean;
    identity: boolean;
    drivingLicense: boolean;
  };
  preferences?: {
    notifications: boolean;
    marketing: boolean;
    language: string;
    currency: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface UserAuthentication {
  token: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegistrationRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
  role?: 'user' | 'provider';
  agreeToTerms: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  error?: string;
  data?: T;
}
