// Notification-related types for RentaHub

export enum NotificationType {
  BOOKING_CONFIRMED = 'BOOKING_CONFIRMED',
  BOOKING_REJECTED = 'BOOKING_REJECTED',
  BOOKING_CREATED = 'BOOKING_CREATED',
  BOOKING_CANCELLED = 'BOOKING_CANCELLED',
  PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  VEHICLE_AVAILABLE = 'VEHICLE_AVAILABLE',
  MAINTENANCE_SCHEDULED = 'MAINTENANCE_SCHEDULED',
  MESSAGE_RECEIVED = 'MESSAGE_RECEIVED',
  DISPUTE_OPENED = 'DISPUTE_OPENED',
  REVIEW_RECEIVED = 'REVIEW_RECEIVED'
}

export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  data?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  notificationTypes: NotificationType[];
}

export interface NotificationCounter {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
}
