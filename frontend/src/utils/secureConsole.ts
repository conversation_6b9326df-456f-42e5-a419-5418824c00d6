/**
 * Secure console logging utility to prevent sensitive data exposure
 */

import { environment } from '../config/environment';

class SecureConsole {
  private static instance: SecureConsole;
  private originalConsole: Console;
  private sensitivePatterns: RegExp[];

  private constructor() {
    this.originalConsole = { ...console };
    this.sensitivePatterns = [
      /eyJ[A-Za-z0-9+/=]+/, // JWT tokens
      /sk_[a-zA-Z0-9]+/, // Stripe secret keys
      /pk_[a-zA-Z0-9]+/, // Stripe publishable keys (still sensitive in logs)
      /[A-Za-z0-9+/]{40,}/, // Long base64 strings (potential keys)
      /password/i,
      /secret/i,
      /key.*:/i,
      /token.*:/i,
      /api.*key/i
    ];
  }

  public static getInstance(): SecureConsole {
    if (!SecureConsole.instance) {
      SecureConsole.instance = new SecureConsole();
    }
    return SecureConsole.instance;
  }

  /**
   * Initialize secure console logging
   */
  public initialize(): void {
    if (environment.isProduction) {
      this.disableConsoleInProduction();
    } else {
      this.setupSecureLogging();
    }
  }

  /**
   * Disable console logging in production
   */
  private disableConsoleInProduction(): void {
    console.log = () => {};
    console.info = () => {};
    console.warn = () => {};
    console.debug = () => {};
    // Keep console.error for critical issues
  }

  /**
   * Setup secure logging for development
   */
  private setupSecureLogging(): void {
    const originalLog = console.log;
    const originalInfo = console.info;
    const originalWarn = console.warn;
    const originalDebug = console.debug;

    console.log = (...args: any[]) => {
      const sanitizedArgs = this.sanitizeLogArgs(args);
      originalLog.apply(console, sanitizedArgs);
    };

    console.info = (...args: any[]) => {
      const sanitizedArgs = this.sanitizeLogArgs(args);
      originalInfo.apply(console, sanitizedArgs);
    };

    console.warn = (...args: any[]) => {
      const sanitizedArgs = this.sanitizeLogArgs(args);
      originalWarn.apply(console, sanitizedArgs);
    };

    console.debug = (...args: any[]) => {
      const sanitizedArgs = this.sanitizeLogArgs(args);
      originalDebug.apply(console, sanitizedArgs);
    };
  }

  /**
   * Sanitize log arguments to remove sensitive data
   */
  private sanitizeLogArgs(args: any[]): any[] {
    return args.map(arg => this.sanitizeValue(arg));
  }

  /**
   * Sanitize individual values
   */
  private sanitizeValue(value: any): any {
    if (typeof value === 'string') {
      return this.sanitizeString(value);
    } else if (typeof value === 'object' && value !== null) {
      return this.sanitizeObject(value);
    }
    return value;
  }

  /**
   * Sanitize string values
   */
  private sanitizeString(str: string): string {
    let sanitized = str;
    
    this.sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, (match) => {
        if (match.length > 10) {
          return `${match.substring(0, 4)}...${match.substring(match.length - 4)}`;
        }
        return '***';
      });
    });

    return sanitized;
  }

  /**
   * Sanitize object values
   */
  private sanitizeObject(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeValue(item));
    }

    const sanitized: any = {};
    const sensitiveKeys = [
      'password', 'secret', 'key', 'token', 'apiKey', 'accessToken',
      'refreshToken', 'sessionId', 'csrf', 'salt', 'hash', 'signature'
    ];

    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      
      if (sensitiveKeys.some(sensitiveKey => lowerKey.includes(sensitiveKey))) {
        sanitized[key] = '***REDACTED***';
      } else {
        sanitized[key] = this.sanitizeValue(value);
      }
    }

    return sanitized;
  }

  /**
   * Safe logging method for development
   */
  public safelog(message: string, data?: any): void {
    if (environment.isDevelopment) {
      if (data) {
        console.log(message, this.sanitizeValue(data));
      } else {
        console.log(message);
      }
    }
  }

  /**
   * Safe error logging (always enabled)
   */
  public safeError(message: string, error?: any): void {
    if (error) {
      // Only log error message and stack, not sensitive data
      const safeError = {
        message: error.message,
        name: error.name,
        stack: environment.isDevelopment ? error.stack : undefined
      };
      console.error(message, safeError);
    } else {
      console.error(message);
    }
  }

  /**
   * Block browser extension injections
   */
  public blockExtensionInjections(): void {
    // Override common extension injection points
    const blockList = [
      'bybit',
      'metamask',
      'coinbase',
      'binance',
      'crypto',
      'wallet'
    ];

    blockList.forEach(name => {
      try {
        Object.defineProperty(window, name, {
          value: undefined,
          writable: false,
          configurable: false
        });
      } catch (e) {
        // Extension already injected, can't override
      }
    });

    // Block common injection methods
    const originalDefineProperty = Object.defineProperty;
    Object.defineProperty = function(obj: any, prop: string, descriptor: PropertyDescriptor) {
      const lowerProp = prop.toLowerCase();
      if (blockList.some(blocked => lowerProp.includes(blocked))) {
        console.warn(`🔒 Blocked potential extension injection: ${prop}`);
        return obj;
      }
      return originalDefineProperty.call(this, obj, prop, descriptor);
    };
  }

  /**
   * Clean up console noise from extensions
   */
  public filterConsoleNoise(): void {
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalInfo = console.info;

    const noisePatterns = [
      /bybit.*inject/i,
      /extension.*inject/i,
      /provider.*inject/i,
      /wallet.*inject/i,
      /metamask/i,
      /coinbase/i
    ];

    console.log = (...args: any[]) => {
      const message = args.join(' ');
      if (!noisePatterns.some(pattern => pattern.test(message))) {
        originalLog.apply(console, args);
      }
    };

    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      if (!noisePatterns.some(pattern => pattern.test(message))) {
        originalWarn.apply(console, args);
      }
    };

    console.info = (...args: any[]) => {
      const message = args.join(' ');
      if (!noisePatterns.some(pattern => pattern.test(message))) {
        originalInfo.apply(console, args);
      }
    };
  }
}

// Export singleton instance
export const secureConsole = SecureConsole.getInstance();

// Auto-initialize
secureConsole.initialize();
secureConsole.blockExtensionInjections();
secureConsole.filterConsoleNoise();
