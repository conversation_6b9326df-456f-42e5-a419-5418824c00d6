// Mock Authentication Service for testing when Supabase is not configured
export interface MockUser {
  id: string;
  email: string;
  name: string;
  role: 'CUSTOMER' | 'PROVIDER';
  companyName?: string;
}

class MockAuthService {
  private currentUser: MockUser | null = null;
  private isAuthenticated = false;

  // Mock users for testing
  private mockUsers: MockUser[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Test Customer',
      role: 'CUSTOMER'
    },
    {
      id: 'dcd4a493-ba65-4406-a65f-df364b141c27',
      email: '<EMAIL>',
      name: 'Test Provider',
      role: 'PROVIDER',
      companyName: 'Test Rental Company'
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'RentaHub Admin',
      role: 'PROVIDER',
      companyName: 'RentaHub Indonesia'
    }
  ];

  constructor() {
    // Auto-login a test provider for development
    if (import.meta.env.DEV) {
      this.currentUser = this.mockUsers[1]; // Test Provider
      this.isAuthenticated = true;
      console.log('🧪 Mock Auth: Auto-logged in test provider for development');
    }
  }

  async signUp(email: string, password: string, userData: { name: string; role: 'CUSTOMER' | 'PROVIDER' }) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    const existingUser = this.mockUsers.find(user => user.email === email);
    if (existingUser) {
      throw new Error('Email is already registered. Please log in or use a different email.');
    }

    // Create new user
    const newUser: MockUser = {
      id: Date.now().toString(),
      email,
      name: userData.name,
      role: userData.role,
      companyName: userData.role === 'PROVIDER' ? userData.name : undefined
    };

    this.mockUsers.push(newUser);
    this.currentUser = newUser;
    this.isAuthenticated = true;

    return { user: newUser, session: { user: newUser } };
  }

  async signIn(email: string, password: string) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Find user
    const user = this.mockUsers.find(u => u.email === email);
    if (!user) {
      throw new Error('Invalid email or password.');
    }

    // Simple password check (for testing, accept any password)
    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters.');
    }

    this.currentUser = user;
    this.isAuthenticated = true;

    return { user, session: { user } };
  }

  async signOut() {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    this.currentUser = null;
    this.isAuthenticated = false;
  }

  async getCurrentUser() {
    return this.currentUser;
  }

  async getSession() {
    return this.isAuthenticated ? { session: { user: this.currentUser } } : { session: null };
  }

  isUserAuthenticated() {
    return this.isAuthenticated;
  }

  // Google OAuth mock
  async signInWithOAuth() {
    // Simulate Google OAuth
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const googleUser: MockUser = {
      id: 'google-user',
      email: '<EMAIL>',
      name: 'Google Test User',
      role: 'CUSTOMER'
    };

    this.currentUser = googleUser;
    this.isAuthenticated = true;

    return { user: googleUser, session: { user: googleUser } };
  }
}

export const mockAuthService = new MockAuthService(); 