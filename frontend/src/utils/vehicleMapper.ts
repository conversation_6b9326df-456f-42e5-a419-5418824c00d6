import { Vehicle } from '../types';

export const mapSimpleToDetailedVehicle = (simpleVehicle: any): Vehicle => {
  return {
    id: simpleVehicle.id,
    name: simpleVehicle.name || `${simpleVehicle.specifications?.model || 'Vehicle'} ${simpleVehicle.specifications?.year || ''}`,
    description: simpleVehicle.description || '',
    pricePerDay: simpleVehicle.pricePerDay || simpleVehicle.dailyRate || 0,
    // pricePerHour: simpleVehicle.pricePerHour, // Removed - not in Vehicle type
    type: simpleVehicle.type || 'vehicle',
    category: simpleVehicle.category || 'car',
    images: simpleVehicle.images || [],
    location: simpleVehicle.location || {
      address: '',
      latitude: 0,
      longitude: 0,
      city: '',
      state: '',
      zipCode: ''
    },
    specifications: simpleVehicle.specifications || {
      model: '',
      year: simpleVehicle.year || 2024,
      mileage: 0,
      color: '',
      licensePlate: ''
    },
    features: simpleVehicle.features || [],
    availabilityCalendar: simpleVehicle.availabilityCalendar,
    rating: simpleVehicle.rating || 0,
    reviews: simpleVehicle.reviews || 0,
    ownerId: simpleVehicle.ownerId || '',
    status: simpleVehicle.status || 'available',
    insuranceOptions: simpleVehicle.insuranceOptions,
    rentalTerms: simpleVehicle.rentalTerms,
    securityDeposit: simpleVehicle.securityDeposit || 0,
    createdAt: simpleVehicle.createdAt || new Date().toISOString(),
    updatedAt: simpleVehicle.updatedAt || new Date().toISOString(),
    // Map additional fields
    dailyRate: simpleVehicle.dailyRate || simpleVehicle.pricePerDay || 0,
    weeklyRate: simpleVehicle.weeklyRate || (simpleVehicle.pricePerDay || 0) * 7,
    monthlyRate: simpleVehicle.monthlyRate || (simpleVehicle.pricePerDay || 0) * 30,
    year: simpleVehicle.year || simpleVehicle.specifications?.year || 2024,
    engineSize: simpleVehicle.engineSize || 0,
    transmission: simpleVehicle.transmission || 'Automatic',
    fuelType: simpleVehicle.fuelType || 'Gasoline',
    deliveryAvailable: simpleVehicle.deliveryAvailable || false,
    deliveryFee: simpleVehicle.deliveryFee || 0,
    deliveryRadius: simpleVehicle.deliveryRadius || 0,
    minimumRentalDays: simpleVehicle.minimumRentalDays || 1,
    maximumRentalDays: simpleVehicle.maximumRentalDays || 30,
    availableQuantity: simpleVehicle.availableQuantity || 1,
    active: simpleVehicle.active !== false
  };
};
