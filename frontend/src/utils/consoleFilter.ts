/**
 * Console Filter Utility
 * Filters out known noisy console messages from browser extensions and other sources
 * Only active in development mode to keep production logs clean
 */

// Known noisy messages to filter out
const NOISY_MESSAGES = [
  'bybit:page provider inject code',
  'MetaMask',
  'wallet_',
  'ethereum',
  'web3',
  'crypto wallet',
  'browser extension',
  // Add more patterns as needed
];

// Store original console methods
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info,
};

/**
 * Check if a message should be filtered out
 */
function shouldFilter(args: any[]): boolean {
  const message = args.join(' ').toLowerCase();
  return NOISY_MESSAGES.some(noise => message.includes(noise.toLowerCase()));
}

/**
 * Initialize console filtering (only in development)
 */
export function initConsoleFilter(): void {
  // Only filter in development mode
  if (import.meta.env.PROD) {
    return;
  }

  console.log('🔇 Console filter initialized - filtering out known extension noise');

  // Override console.log
  console.log = (...args: any[]) => {
    if (!shouldFilter(args)) {
      originalConsole.log(...args);
    }
  };

  // Override console.warn
  console.warn = (...args: any[]) => {
    if (!shouldFilter(args)) {
      originalConsole.warn(...args);
    }
  };

  // Override console.info
  console.info = (...args: any[]) => {
    if (!shouldFilter(args)) {
      originalConsole.info(...args);
    }
  };

  // Keep console.error unfiltered for debugging
  // console.error = (...args: any[]) => {
  //   if (!shouldFilter(args)) {
  //     originalConsole.error(...args);
  //   }
  // };
}

/**
 * Restore original console methods
 */
export function restoreConsole(): void {
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.info = originalConsole.info;
}

/**
 * Add a new noisy message pattern to filter
 */
export function addNoisyPattern(pattern: string): void {
  if (!NOISY_MESSAGES.includes(pattern)) {
    NOISY_MESSAGES.push(pattern);
  }
}

// Auto-initialize in development
if (import.meta.env.DEV) {
  initConsoleFilter();
}
