// Network Diagnostics Utility for RentaHub
// Helps diagnose and prevent connectivity issues

export interface NetworkDiagnostics {
  supabaseReachable: boolean;
  dnsResolution: boolean;
  httpConnectivity: boolean;
  corsEnabled: boolean;
  latency: number;
  error?: string;
  recommendations: string[];
}

export class NetworkDiagnosticsService {
  private static readonly SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
  private static readonly TIMEOUT_MS = 10000; // 10 seconds

  /**
   * Run comprehensive network diagnostics
   */
  static async runDiagnostics(): Promise<NetworkDiagnostics> {
    console.log('🔍 Running network diagnostics...');
    
    const diagnostics: NetworkDiagnostics = {
      supabaseReachable: false,
      dnsResolution: false,
      httpConnectivity: false,
      corsEnabled: false,
      latency: 0,
      recommendations: []
    };

    try {
      // Test 1: DNS Resolution
      diagnostics.dnsResolution = await this.testDNSResolution();
      
      // Test 2: HTTP Connectivity
      const connectivityResult = await this.testHTTPConnectivity();
      diagnostics.httpConnectivity = connectivityResult.success;
      diagnostics.latency = connectivityResult.latency;
      
      // Test 3: CORS Configuration
      diagnostics.corsEnabled = await this.testCORS();
      
      // Test 4: Supabase API Reachability
      diagnostics.supabaseReachable = await this.testSupabaseAPI();
      
      // Generate recommendations
      diagnostics.recommendations = this.generateRecommendations(diagnostics);
      
    } catch (error: any) {
      diagnostics.error = error.message;
      diagnostics.recommendations.push('Network diagnostics failed. Check console for details.');
    }

    console.log('📊 Network Diagnostics Results:', diagnostics);
    return diagnostics;
  }

  /**
   * Test DNS resolution for Supabase domain
   */
  private static async testDNSResolution(): Promise<boolean> {
    try {
      // Use a simple fetch with a very short timeout to test DNS
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);
      
      await fetch(this.SUPABASE_URL, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors' // Bypass CORS for DNS test
      });
      
      clearTimeout(timeoutId);
      return true;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return false; // Timeout = DNS issue
      }
      // Other errors might still indicate DNS resolution worked
      return !error.message.includes('ERR_NAME_NOT_RESOLVED');
    }
  }

  /**
   * Test HTTP connectivity and measure latency
   */
  private static async testHTTPConnectivity(): Promise<{ success: boolean; latency: number }> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT_MS);
      
      const response = await fetch(`${this.SUPABASE_URL}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      clearTimeout(timeoutId);
      const latency = Date.now() - startTime;
      
      return {
        success: response.status < 500, // Accept 404, but not 500+
        latency
      };
    } catch (error) {
      return {
        success: false,
        latency: Date.now() - startTime
      };
    }
  }

  /**
   * Test CORS configuration
   */
  private static async testCORS(): Promise<boolean> {
    try {
      const response = await fetch(`${this.SUPABASE_URL}/auth/v1/settings`, {
        method: 'GET',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      return response.status < 500;
    } catch (error: any) {
      // CORS errors typically show as network errors
      return !error.message.includes('CORS');
    }
  }

  /**
   * Test Supabase API accessibility
   */
  private static async testSupabaseAPI(): Promise<boolean> {
    try {
      const response = await fetch(`${this.SUPABASE_URL}/auth/v1/settings`, {
        method: 'GET',
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate recommendations based on diagnostic results
   */
  private static generateRecommendations(diagnostics: NetworkDiagnostics): string[] {
    const recommendations: string[] = [];

    if (!diagnostics.dnsResolution) {
      recommendations.push('DNS resolution failed. Try flushing DNS cache or using a different DNS server (8.8.8.8).');
    }

    if (!diagnostics.httpConnectivity) {
      recommendations.push('HTTP connectivity failed. Check firewall settings and internet connection.');
    }

    if (!diagnostics.corsEnabled) {
      recommendations.push('CORS issues detected. This might be a browser security setting.');
    }

    if (!diagnostics.supabaseReachable) {
      recommendations.push('Supabase API is not reachable. Check service status or try again later.');
    }

    if (diagnostics.latency > 5000) {
      recommendations.push('High latency detected. Network connection might be slow.');
    }

    if (recommendations.length === 0) {
      recommendations.push('All network tests passed. The issue might be temporary.');
    }

    return recommendations;
  }

  /**
   * Quick connectivity check for authentication
   */
  static async quickConnectivityCheck(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${this.SUPABASE_URL}/auth/v1/settings`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY
        }
      });
      
      clearTimeout(timeoutId);
      return response.status < 500;
    } catch (error) {
      console.warn('⚠️ Quick connectivity check failed:', error);
      return false;
    }
  }
}

/**
 * Auto-run diagnostics on app startup
 */
export const runStartupDiagnostics = async () => {
  if (import.meta.env.DEV) {
    console.log('🚀 Running startup network diagnostics...');
    const results = await NetworkDiagnosticsService.runDiagnostics();
    
    if (!results.supabaseReachable) {
      console.warn('⚠️ Supabase connectivity issues detected on startup');
      console.warn('📋 Recommendations:', results.recommendations);
    }
  }
};
