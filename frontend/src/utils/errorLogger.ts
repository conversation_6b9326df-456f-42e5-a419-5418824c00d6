import { monitoring } from '../services/MonitoringService'

export interface ErrorLogData {
  message: string
  stack?: string
  context?: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: 'user-action' | 'api' | 'component' | 'network' | 'unknown'
  timestamp: string
  userId?: string
  url: string
  userAgent: string
}

export class ErrorLogger {
  private static instance: ErrorLogger
  private logQueue: ErrorLogData[] = []
  private isProcessing = false

  private constructor() {}

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger()
    }
    return ErrorLogger.instance
  }

  logError(error: Error | string, options: Partial<ErrorLogData> = {}) {
    const errorData: ErrorLogData = {
      message: typeof error === 'string' ? error : error.message,
      stack: error instanceof Error ? error.stack : undefined,
      context: options.context || {},
      severity: options.severity || 'medium',
      source: options.source || 'unknown',
      timestamp: new Date().toISOString(),
      userId: options.userId,
      url: window.location.href,
      userAgent: navigator.userAgent,
    }

    this.logQueue.push(errorData)
    this.processQueue()

    // Also send to monitoring service
    if (error instanceof Error) {
      monitoring.captureError(error, errorData.context)
    } else {
      monitoring.captureMessage(errorData.message, 'error')
    }
  }

  logApiError(endpoint: string, status: number, response?: any, context?: Record<string, any>) {
    this.logError(`API Error: ${endpoint} (${status})`, {
      severity: status >= 500 ? 'high' : 'medium',
      source: 'api',
      context: {
        endpoint,
        status,
        response,
        ...context,
      },
    })
  }

  logUserAction(action: string, context?: Record<string, any>) {
    monitoring.addBreadcrumb(action, 'user-action', 'info')
    
    if (context) {
      monitoring.captureMessage(`User Action: ${action}`, 'info')
    }
  }

  logNetworkError(url: string, error: Error, context?: Record<string, any>) {
    this.logError(error, {
      severity: 'medium',
      source: 'network',
      context: {
        url,
        ...context,
      },
    })
  }

  logComponentError(componentName: string, error: Error, props?: Record<string, any>) {
    this.logError(error, {
      severity: 'high',
      source: 'component',
      context: {
        componentName,
        props,
      },
    })
  }

  private async processQueue() {
    if (this.isProcessing || this.logQueue.length === 0) {
      return
    }

    this.isProcessing = true
    let batch: ErrorLogData[] = []

    try {
      batch = this.logQueue.splice(0, 10) // Process up to 10 errors at a time
      
      if (import.meta.env.PROD) {
        await this.sendErrorsToBackend(batch)
      } else {
        // In development, just log to console
        batch.forEach(errorData => {
          console.group(`🚨 Error Log [${errorData.severity.toUpperCase()}]`)
          console.error('Message:', errorData.message)
          console.error('Source:', errorData.source)
          console.error('Context:', errorData.context)
          if (errorData.stack) {
            console.error('Stack:', errorData.stack)
          }
          console.groupEnd()
        })
      }
    } catch (err) {
      console.error('Failed to process error queue:', err)
      // Re-add errors to queue for retry
      if (batch.length > 0) {
        this.logQueue.unshift(...batch)
      }
    } finally {
      this.isProcessing = false
      
      // Process remaining items
      if (this.logQueue.length > 0) {
        setTimeout(() => this.processQueue(), 1000)
      }
    }
  }

  private async sendErrorsToBackend(errors: ErrorLogData[]): Promise<void> {
    try {
      const response = await fetch('/api/frontend-errors/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ errors }),
      })

      if (!response.ok) {
        throw new Error(`Failed to send errors: ${response.status}`)
      }
    } catch (error) {
      console.error('Failed to send errors to backend:', error)
      throw error
    }
  }

  // Get error statistics for debugging
  getQueueStatus() {
    return {
      queueLength: this.logQueue.length,
      isProcessing: this.isProcessing,
    }
  }

  // Clear the queue (useful for testing)
  clearQueue() {
    this.logQueue = []
  }
}

// Create singleton instance
export const errorLogger = ErrorLogger.getInstance()

// Global error handlers
window.addEventListener('error', (event) => {
  errorLogger.logError(event.error || event.message, {
    severity: 'high',
    source: 'unknown',
    context: {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    },
  })
})

window.addEventListener('unhandledrejection', (event) => {
  errorLogger.logError(event.reason, {
    severity: 'high',
    source: 'unknown',
    context: {
      type: 'unhandled_promise_rejection',
    },
  })
})