import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { mockAuthService } from './mockAuthService';
import { environment } from '../config/environment';

class SupabaseClientSingleton {
  private static instance: SupabaseClient | null = null;
  private static useMockAuth = false;

  private constructor() {}

  public static getInstance(): SupabaseClient | null {
    if (!SupabaseClientSingleton.instance) {
      const supabaseUrl = environment.supabaseUrl;
      const supabaseAnonKey = environment.supabaseAnonKey;

      console.log('🔍 Supabase Configuration Check:');
      console.log('URL:', supabaseUrl);
      console.log('Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET');

      // Check if environment variables are properly set
      if (!supabaseUrl || !supabaseAnonKey || 
          supabaseUrl === 'https://your-project.supabase.co' || 
          supabaseAnonKey === 'your_anon_key') {
        console.warn('⚠️ Supabase environment variables not properly configured. Using mock authentication for testing.');
        SupabaseClientSingleton.useMockAuth = true;
        return null;
      }

      try {
        SupabaseClientSingleton.instance = createClient(supabaseUrl, supabaseAnonKey, {
          auth: {
            storageKey: 'sb-frontend-auth-token',
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: false
          },
          global: {
            headers: {
              'X-Client-Info': 'rentahub-frontend'
            }
          }
        });
        console.log('✅ Frontend Supabase client initialized successfully');

        // Test connection immediately
        SupabaseClientSingleton.testConnection();

      } catch (error) {
        console.error('❌ Failed to initialize Supabase client:', error);
        SupabaseClientSingleton.useMockAuth = true;
        return null;
      }
    }

    return SupabaseClientSingleton.instance;
  }

  private static async testConnection() {
    try {
      if (SupabaseClientSingleton.instance) {
        console.log('🔍 Testing Supabase connection...');
        const { data, error } = await SupabaseClientSingleton.instance.auth.getSession();
        if (error) {
          console.warn('⚠️ Supabase connection test warning:', error.message);
        } else {
          console.log('✅ Supabase connection test successful');
        }
      }
    } catch (error) {
      console.error('❌ Supabase connection test failed:', error);
    }
  }

  public static isUsingMockAuth(): boolean {
    return SupabaseClientSingleton.useMockAuth;
  }
}

export const supabase = SupabaseClientSingleton.getInstance();

// Create a mock Supabase-like interface for testing
export const mockSupabase = {
  auth: {
    signUp: async (credentials: any) => {
      const { data, error } = await mockAuthService.signUp(
        credentials.email,
        credentials.password,
        credentials.options?.data || {}
      );
      return { data, error };
    },
    signInWithPassword: async (credentials: any) => {
      const { data, error } = await mockAuthService.signIn(
        credentials.email,
        credentials.password
      );
      return { data, error };
    },
    signInWithOAuth: async (options: any) => {
      const { data, error } = await mockAuthService.signInWithOAuth();
      return { data, error };
    },
    signOut: async () => {
      const { error } = await mockAuthService.signOut();
      return { error };
    },
    getSession: async () => {
      const { data, error } = await mockAuthService.getSession();
      return { data, error };
    },
    getCurrentUser: async () => {
      const { data, error } = await mockAuthService.getCurrentUser();
      return { data, error };
    },
    onAuthStateChange: (callback: any) => {
      return mockAuthService.onAuthStateChange(callback);
    }
  }
};

// Export the appropriate auth service
export const authService = SupabaseClientSingleton.isUsingMockAuth() ? mockSupabase.auth : supabase?.auth;

// Test connection function
export const testConnection = async () => {
  if (SupabaseClientSingleton.isUsingMockAuth()) {
    console.log('🔍 Testing mock auth connection...');
    try {
      const session = await mockAuthService.getSession();
      console.log('✅ Mock auth connection successful');
      return true;
    } catch (error) {
      console.error('❌ Mock auth connection failed:', error);
      return false;
    }
  }

  if (supabase) {
    try {
      console.log('🔍 Testing Supabase connection...');
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        console.error('❌ Supabase connection failed:', error);
        return false;
      }
      console.log('✅ Supabase connection successful');
      return true;
    } catch (error) {
      console.error('❌ Supabase connection error:', error);
      return false;
    }
  }

  return false;
};