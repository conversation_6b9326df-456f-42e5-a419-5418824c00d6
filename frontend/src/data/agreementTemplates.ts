export const defaultAgreementTemplates = [
  {
    id: 'rental-standard',
    name: 'Standard Rental Agreement',
    type: 'rental' as const,
    isActive: true,
    lastModified: new Date().toISOString(),
    sections: [
      {
        id: 'parties',
        title: 'Parties to the Agreement',
        content: `This Vehicle Rental Agreement ("Agreement") is entered into on {{AGREEMENT_DATE}} between:

**PROVIDER:** {{PROVIDER_NAME}}
Company: {{PROVIDER_COMPANY}}
Phone: {{PROVIDER_PHONE}}

**CUSTOMER:** {{CUSTOMER_NAME}}
Email: {{CUSTOMER_EMAIL}}
Phone: {{CUSTOMER_PHONE}}`,
        isRequired: true,
        variables: ['PROVIDER_NAME', 'PROVIDER_COMPANY', 'PROVIDER_PHONE', 'CUSTOMER_NAME', 'CUSTOMER_EMAIL', 'CUSTOMER_PHONE', 'AGREEMENT_DATE'],
        order: 1,
      },
      {
        id: 'vehicle-details',
        title: 'Vehicle Information',
        content: `The following vehicle is being rented under this agreement:

**Vehicle:** {{VEHICLE_NAME}}
**Year:** {{VEHICLE_YEAR}}
**License Plate:** {{VEHICLE_LICENSE}}
**Daily Rate:** {{DAILY_RATE}}
**Security Deposit:** {{DEPOSIT_AMOUNT}}`,
        isRequired: true,
        variables: ['VEHICLE_NAME', 'VEHICLE_YEAR', 'VEHICLE_LICENSE', 'DAILY_RATE', 'DEPOSIT_AMOUNT'],
        order: 2,
      },
      {
        id: 'rental-period',
        title: 'Rental Period and Payment',
        content: `**Rental Period:** {{RENTAL_START}} to {{RENTAL_END}} ({{RENTAL_DAYS}} days)
**Total Amount:** {{TOTAL_AMOUNT}}
**Payment Method:** {{PAYMENT_METHOD}}
**Booking ID:** {{BOOKING_ID}}

The customer agrees to pay the total rental amount as specified above. Payment must be completed before vehicle pickup.`,
        isRequired: true,
        variables: ['RENTAL_START', 'RENTAL_END', 'RENTAL_DAYS', 'TOTAL_AMOUNT', 'PAYMENT_METHOD', 'BOOKING_ID'],
        order: 3,
      },
      {
        id: 'pickup-return',
        title: 'Pickup and Return',
        content: `**Pickup Location:** {{PICKUP_LOCATION}}

The customer must pick up the vehicle at the specified location during agreed hours. The vehicle must be returned to the same location unless otherwise arranged. 

The customer is responsible for:
- Returning the vehicle with the same fuel level as provided
- Returning the vehicle in the same condition as received
- Reporting any damage immediately upon discovery`,
        isRequired: true,
        variables: ['PICKUP_LOCATION'],
        order: 4,
      },
      {
        id: 'responsibilities',
        title: 'Customer Responsibilities',
        content: `The customer agrees to:

1. Use the vehicle only for lawful purposes
2. Not allow anyone else to drive the vehicle without prior written consent
3. Maintain the vehicle in good condition during the rental period
4. Follow all traffic laws and regulations
5. Not use the vehicle for commercial purposes without authorization
6. Report any accidents or damage immediately
7. Return the vehicle on time and in the same condition as received`,
        isRequired: true,
        variables: [],
        order: 5,
      },
      {
        id: 'insurance',
        title: 'Insurance Coverage',
        content: `**Insurance Type:** {{INSURANCE_TYPE}}

The customer acknowledges that they have selected the above insurance coverage. The customer is responsible for any damages not covered by the selected insurance plan.

The customer must have a valid driver's license and meet all legal requirements for operating the vehicle.`,
        isRequired: false,
        variables: ['INSURANCE_TYPE'],
        order: 6,
      },
      {
        id: 'liability',
        title: 'Liability and Damages',
        content: `The customer is liable for:
- Any damage to the vehicle during the rental period
- Traffic violations and fines incurred during the rental
- Theft or loss of the vehicle due to negligence
- Additional charges for late return or excessive mileage

The security deposit may be used to cover any damages or additional charges. Any costs exceeding the deposit amount will be charged to the customer.`,
        isRequired: true,
        variables: [],
        order: 7,
      },
      {
        id: 'termination',
        title: 'Termination',
        content: `This agreement may be terminated by either party with proper notice. The provider reserves the right to terminate this agreement immediately if:

- The customer violates any terms of this agreement
- The customer uses the vehicle for illegal activities
- The customer fails to make required payments
- The vehicle is damaged due to negligence or misuse

Upon termination, the customer must return the vehicle immediately.`,
        isRequired: true,
        variables: [],
        order: 8,
      },
      {
        id: 'governing-law',
        title: 'Governing Law',
        content: `This agreement shall be governed by and construed in accordance with the laws of the jurisdiction where the rental takes place. Any disputes arising from this agreement shall be resolved through binding arbitration.

Both parties acknowledge that they have read, understood, and agree to be bound by all terms and conditions of this agreement.`,
        isRequired: true,
        variables: [],
        order: 9,
      },
    ],
  },
  {
    id: 'insurance-waiver',
    name: 'Insurance Waiver Agreement',
    type: 'insurance' as const,
    isActive: true,
    lastModified: new Date().toISOString(),
    sections: [
      {
        id: 'waiver-acknowledgment',
        title: 'Insurance Waiver Acknowledgment',
        content: `**CUSTOMER:** {{CUSTOMER_NAME}}
**VEHICLE:** {{VEHICLE_NAME}}
**RENTAL PERIOD:** {{RENTAL_START}} to {{RENTAL_END}}

By signing this waiver, the customer acknowledges and agrees that:

1. They have been offered insurance coverage for the rental vehicle
2. They have chosen to DECLINE the offered insurance coverage
3. They understand the risks associated with declining insurance coverage
4. They accept full financial responsibility for any damage, theft, or loss of the vehicle`,
        isRequired: true,
        variables: ['CUSTOMER_NAME', 'VEHICLE_NAME', 'RENTAL_START', 'RENTAL_END'],
        order: 1,
      },
      {
        id: 'financial-responsibility',
        title: 'Financial Responsibility',
        content: `The customer understands that by declining insurance coverage, they are personally responsible for:

- The full replacement value of the vehicle in case of total loss
- All repair costs for any damage to the vehicle
- Towing and storage fees
- Loss of rental income to the provider during repair period
- Administrative fees related to damage claims

**Vehicle Value:** Estimated at ${{VEHICLE_VALUE}} (for reference)
**Security Deposit:** {{DEPOSIT_AMOUNT}} (held but may not cover full damages)`,
        isRequired: true,
        variables: ['VEHICLE_VALUE', 'DEPOSIT_AMOUNT'],
        order: 2,
      },
    ],
  },
  {
    id: 'damage-waiver',
    name: 'Damage Waiver Agreement',
    type: 'damage_waiver' as const,
    isActive: true,
    lastModified: new Date().toISOString(),
    sections: [
      {
        id: 'damage-waiver-terms',
        title: 'Damage Waiver Coverage',
        content: `**CUSTOMER:** {{CUSTOMER_NAME}}
**VEHICLE:** {{VEHICLE_NAME}}
**COVERAGE AMOUNT:** Up to ${{COVERAGE_LIMIT}}

This Damage Waiver Agreement provides coverage for accidental damage to the rental vehicle, subject to the following terms:

**COVERED:**
- Accidental damage during normal use
- Minor scratches and dents
- Windshield damage
- Tire damage from road hazards

**NOT COVERED:**
- Intentional damage or misuse
- Damage from illegal activities
- Damage due to negligence
- Interior damage from smoking or pets
- Damage exceeding the coverage limit`,
        isRequired: true,
        variables: ['CUSTOMER_NAME', 'VEHICLE_NAME', 'COVERAGE_LIMIT'],
        order: 1,
      },
    ],
  },
];

export const agreementVariables = {
  // Customer Information
  CUSTOMER_NAME: 'Customer full name',
  CUSTOMER_EMAIL: 'Customer email address',
  CUSTOMER_PHONE: 'Customer phone number',
  
  // Provider Information
  PROVIDER_NAME: 'Provider name',
  PROVIDER_COMPANY: 'Provider company name',
  PROVIDER_PHONE: 'Provider phone number',
  
  // Vehicle Information
  VEHICLE_NAME: 'Vehicle name/model',
  VEHICLE_YEAR: 'Vehicle year',
  VEHICLE_LICENSE: 'Vehicle license plate',
  VEHICLE_VALUE: 'Estimated vehicle value',
  
  // Rental Details
  RENTAL_START: 'Rental start date',
  RENTAL_END: 'Rental end date',
  RENTAL_DAYS: 'Number of rental days',
  PICKUP_LOCATION: 'Vehicle pickup location',
  
  // Financial Information
  TOTAL_AMOUNT: 'Total rental amount',
  DAILY_RATE: 'Daily rental rate',
  DEPOSIT_AMOUNT: 'Security deposit amount',
  PAYMENT_METHOD: 'Payment method used',
  COVERAGE_LIMIT: 'Insurance coverage limit',
  
  // Other
  INSURANCE_TYPE: 'Selected insurance type',
  BOOKING_ID: 'Booking reference ID',
  AGREEMENT_DATE: 'Agreement creation date',
};
