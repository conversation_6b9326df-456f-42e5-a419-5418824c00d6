import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp,
  AttachMoney,
  DirectionsBike,
  BookOnline,
  Visibility,
  Star,
  Add as AddIcon,
  Refresh,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface EmptyAnalyticsCTAProps {
  onRefresh?: () => void;
}

const EmptyAnalyticsCTA: React.FC<EmptyAnalyticsCTAProps> = ({ onRefresh }) => {
  const navigate = useNavigate();

  const handleAddVehicle = () => {
    // Navigate to add vehicle or trigger add vehicle modal
    navigate('/provider-dashboard?tab=4'); // Vehicles tab
  };

  const features = [
    {
      icon: TrendingUp,
      title: 'Revenue Tracking',
      description: 'Monitor your earnings with detailed charts and insights',
      color: '#4caf50',
    },
    {
      icon: BookOnline,
      title: 'Booking Analytics',
      description: 'Track booking patterns and customer behavior',
      color: '#2196f3',
    },
    {
      icon: Visibility,
      title: 'Performance Metrics',
      description: 'View conversion rates and listing performance',
      color: '#ff9800',
    },
    {
      icon: Star,
      title: 'Rating Insights',
      description: 'Analyze customer feedback and improve service',
      color: '#9c27b0',
    },
  ];

  return (
    <Box>
      {/* Main CTA Section */}
      <Paper
        elevation={2}
        sx={{
          background: 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)',
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          mb: 4,
        }}
      >
        {/* Icon */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              width: 100,
              height: 100,
              bgcolor: 'primary.main',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              boxShadow: 3,
            }}
          >
            <AnalyticsIcon sx={{ fontSize: 50, color: 'white' }} />
          </Box>
        </Box>

        {/* Content */}
        <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          Start Tracking Your Success
        </Typography>

        <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
          Get detailed analytics once you have active bookings
        </Typography>

        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: '600px', mx: 'auto' }}>
          Your analytics dashboard will show comprehensive insights about your earnings, booking patterns, 
          customer ratings, and vehicle performance once you start receiving bookings.
        </Typography>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            onClick={handleAddVehicle}
            variant="contained"
            size="large"
            startIcon={<AddIcon />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
            }}
          >
            Add Your First Vehicle
          </Button>

          <Button
            onClick={onRefresh}
            variant="outlined"
            size="large"
            startIcon={<Refresh />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
              borderWidth: 2,
            }}
          >
            Refresh Data
          </Button>
        </Box>

        {/* Progress Indicator */}
        <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            📊 <strong>Coming Soon:</strong> Rich analytics and insights will appear here once you receive your first booking
          </Typography>
        </Box>
      </Paper>

      {/* Features Preview */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
          What You'll Get With Analytics
        </Typography>
        
        <Grid container spacing={3}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: feature.color,
                      width: 60,
                      height: 60,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <feature.icon sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Quick Stats Preview */}
      <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          Analytics Preview - What You'll Track
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Box textAlign="center">
              <AttachMoney sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h6" fontWeight="bold">Total Earnings</Typography>
              <Typography variant="body2" color="text.secondary">
                Track daily, weekly, and monthly revenue
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box textAlign="center">
              <BookOnline sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" fontWeight="bold">Booking Trends</Typography>
              <Typography variant="body2" color="text.secondary">
                Monitor booking patterns and peak times
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box textAlign="center">
              <DirectionsBike sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h6" fontWeight="bold">Vehicle Performance</Typography>
              <Typography variant="body2" color="text.secondary">
                See which vehicles perform best
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Box textAlign="center">
              <Star sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h6" fontWeight="bold">Customer Ratings</Typography>
              <Typography variant="body2" color="text.secondary">
                Monitor feedback and service quality
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default EmptyAnalyticsCTA;
