import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Paper,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  DirectionsBike,
  BookOnline,
  Star,
  Visibility,
  Share,
  Download,
  Refresh,
  CalendarToday,
  LocationOn,
  Speed,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { AdminService } from '../../services/AdminService';
import { ProviderService } from '../../services/ProviderService';
import EmptyAnalyticsCTA from './EmptyAnalyticsCTA';

interface AnalyticsData {
  totalEarnings: number;
  monthlyEarnings: number;
  totalBookings: number;
  completedBookings: number;
  averageRating: number;
  totalViews: number;
  conversionRate: number;
  topVehicles: Array<{
    id: string;
    name: string;
    earnings: number;
    bookings: number;
    rating: number;
  }>;
  earningsChart: Array<{
    date: string;
    earnings: number;
    bookings: number;
  }>;
  bookingsByCategory: Array<{
    category: string;
    count: number;
    earnings: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: 'booking' | 'payment' | 'review';
    description: string;
    amount?: number;
    timestamp: string;
  }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const ProviderAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timePeriod, setTimePeriod] = useState(30);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch live analytics data from ProviderService
      const [statsResponse, vehiclesResponse] = await Promise.all([
        ProviderService.getDashboardStats(),
        ProviderService.getProviderVehicles()
      ]);

      if (statsResponse.success && statsResponse.data) {
        const stats = statsResponse.data;
        const vehicles = vehiclesResponse.success ? vehiclesResponse.data : [];

        // Transform the live data to match our interface
        const transformedData: AnalyticsData = {
          totalEarnings: stats.totalRevenue || 0,
          monthlyEarnings: stats.monthlyRevenue || 0,
          totalBookings: stats.totalBookings || 0,
          completedBookings: stats.completedBookings || 0,
          averageRating: stats.averageRating || 0,
          totalViews: stats.totalViews || 0,
          conversionRate: stats.conversionRate || 0,
          topVehicles: stats.topVehicles?.map(vehicle => ({
            id: vehicle.id,
            name: vehicle.name,
            earnings: vehicle.revenue || 0,
            bookings: vehicle.bookings || 0,
            rating: vehicle.rating || 0
          })) || [],
          earningsChart: stats.earningsChart || [],
          bookingsByCategory: stats.bookingsByCategory || [],
          recentActivity: stats.recentActivity || [],
        };

        // If we have bookings or vehicles, show analytics
        if (transformedData.totalBookings > 0 || vehicles.length > 0) {
          setAnalyticsData(transformedData);
        } else {
          // No data available - show empty state
          setAnalyticsData(null);
        }
      } else {
        // No data available - show empty state
        setAnalyticsData(null);
      }
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
      setAnalyticsData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timePeriod]);

  const handleExportData = () => {
    // TODO: Implement data export functionality
    console.log('Exporting analytics data...');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <Button color="inherit" size="small" onClick={fetchAnalytics}>
            Retry
          </Button>
        }
      >
        {error}
      </Alert>
    );
  }

  // Show empty state CTA if no data
  if (!analyticsData || analyticsData.totalBookings === 0) {
    return <EmptyAnalyticsCTA onRefresh={fetchAnalytics} />;
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" fontWeight="bold">
          Analytics Dashboard
        </Typography>
        <Box display="flex" gap={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Period</InputLabel>
            <Select
              value={timePeriod}
              label="Period"
              onChange={(e) => setTimePeriod(e.target.value as number)}
            >
              <MenuItem value={7}>Last 7 days</MenuItem>
              <MenuItem value={30}>Last 30 days</MenuItem>
              <MenuItem value={90}>Last 3 months</MenuItem>
              <MenuItem value={365}>Last year</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title="Export Data">
            <IconButton onClick={handleExportData}>
              <Download />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh">
            <IconButton onClick={fetchAnalytics}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Earnings
                  </Typography>
                  <Typography variant="h4" component="div">
                    {formatCurrency(analyticsData.totalEarnings)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUp color="success" fontSize="small" />
                    <Typography variant="body2" color="success.main" ml={0.5}>
                      +12.5% from last month
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Bookings
                  </Typography>
                  <Typography variant="h4" component="div">
                    {analyticsData.totalBookings}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUp color="success" fontSize="small" />
                    <Typography variant="body2" color="success.main" ml={0.5}>
                      +8.2% from last month
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <BookOnline />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Average Rating
                  </Typography>
                  <Typography variant="h4" component="div">
                    {analyticsData.averageRating.toFixed(1)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Star color="warning" fontSize="small" />
                    <Typography variant="body2" color="text.secondary" ml={0.5}>
                      Based on {analyticsData.completedBookings} reviews
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <Star />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Conversion Rate
                  </Typography>
                  <Typography variant="h4" component="div">
                    {formatPercentage(analyticsData.conversionRate)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Visibility color="info" fontSize="small" />
                    <Typography variant="body2" color="text.secondary" ml={0.5}>
                      {analyticsData.totalViews} total views
                    </Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <Visibility />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Earnings Overview
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analyticsData.earningsChart}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip 
                    formatter={(value: number, name: string) => [
                      name === 'earnings' ? formatCurrency(value) : value,
                      name === 'earnings' ? 'Earnings' : 'Bookings'
                    ]}
                  />
                  <Area
                    type="monotone"
                    dataKey="earnings"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bookings by Category
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={analyticsData.bookingsByCategory}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                  >
                    {analyticsData.bookingsByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProviderAnalytics;
