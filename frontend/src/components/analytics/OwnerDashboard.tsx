import React, { useState, useEffect } from 'react'
import { 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Box, 
  Select, 
  MenuItem 
} from '@mui/material'
import { 
  <PERSON>Chart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer 
} from 'recharts'
import AnalyticsService, { 
  OwnerPerformance 
} from '../../services/AnalyticsService'

const OwnerDashboard: React.FC = () => {
  const [performance, setPerformance] = useState<OwnerPerformance | null>(null)
  const [timePeriod, setTimePeriod] = useState(30)

  useEffect(() => {
    const fetchOwnerAnalytics = async () => {
      try {
        const performanceData = await AnalyticsService.getOwnerPerformance(undefined, timePeriod)
        setPerformance(performanceData)
      } catch (error) {
        console.error('Failed to fetch owner analytics', error)
      }
    }

    fetchOwnerAnalytics()
  }, [timePeriod])

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Owner Performance Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Time Period Selector */}
        <Grid item xs={12}>
          <Select
            value={timePeriod}
            onChange={(e) => setTimePeriod(e.target.value as number)}
          >
            <MenuItem value={30}>Last 30 Days</MenuItem>
            <MenuItem value={90}>Last 90 Days</MenuItem>
            <MenuItem value={365}>Last Year</MenuItem>
          </Select>
        </Grid>

        {/* Key Performance Indicators */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6">Total Bookings</Typography>
              <Typography variant="h4">
                {performance?.totalBookings || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6">Total Earnings</Typography>
              <Typography variant="h4">
                ${performance?.totalEarnings.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Vehicles Performance */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6">Top Performing Vehicles</Typography>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart 
                  data={performance?.topVehicles || []}
                  layout="vertical"
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" />
                  <Tooltip />
                  <Bar dataKey="bookingCount" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default OwnerDashboard 