import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  Chip,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Language as LanguageIcon,
  Check as CheckIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { SUPPORTED_LANGUAGES } from '../i18n';

interface LanguageSwitcherProps {
  variant?: 'button' | 'icon' | 'compact';
  showFlag?: boolean;
  showName?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'icon',
  showFlag = true,
  showName = true,
  size = 'medium'
}) => {
  const { i18n, t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isRTL, setIsRTL] = useState(false);

  const currentLanguage = SUPPORTED_LANGUAGES.find(lang => lang.code === i18n.language) || SUPPORTED_LANGUAGES[0];

  useEffect(() => {
    // Update RTL direction when language changes
    const currentLang = SUPPORTED_LANGUAGES.find(lang => lang.code === i18n.language);
    if (currentLang?.rtl) {
      document.dir = 'rtl';
      setIsRTL(true);
    } else {
      document.dir = 'ltr';
      setIsRTL(false);
    }
  }, [i18n.language]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await i18n.changeLanguage(languageCode);
      handleClose();

      // Store preference
      localStorage.setItem('rentahub-language', languageCode);

      // Update page title if needed
      document.title = t('common:app.title', 'RentaHub - Vehicle Rental Platform');

      // Trigger a custom event for other components to react to language change
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: languageCode } }));
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const getButtonSize = () => {
    if (isMobile) return 'small';
    return size;
  };

  const renderButton = () => {
    if (variant === 'icon') {
      return (
        <Tooltip title={t('common:language.select', 'Select Language')}>
          <IconButton
            onClick={handleClick}
            size={getButtonSize()}
            sx={{
              color: 'inherit',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)'
              }
            }}
          >
            <LanguageIcon />
          </IconButton>
        </Tooltip>
      );
    }

    if (variant === 'compact') {
      return (
        <Button
          variant="outlined"
          size={getButtonSize()}
          onClick={handleClick}
          startIcon={showFlag ? null : <TranslateIcon />}
          sx={{
            minWidth: isMobile ? 'auto' : 120,
            textTransform: 'none',
            borderRadius: 2,
            px: isMobile ? 1 : 2
          }}
        >
          {showFlag && (
            <Typography component="span" sx={{ fontSize: '1.2em', mr: showName ? 1 : 0 }}>
              {currentLanguage.flag}
            </Typography>
          )}
          {showName && !isMobile && (
            <Typography variant="body2" component="span">
              {currentLanguage.name}
            </Typography>
          )}
          {isMobile && (
            <Typography variant="body2" component="span">
              {currentLanguage.code.toUpperCase()}
            </Typography>
          )}
        </Button>
      );
    }

    return (
      <Button
        variant="outlined"
        size={getButtonSize()}
        onClick={handleClick}
        startIcon={<LanguageIcon />}
        sx={{
          textTransform: 'none',
          borderRadius: 2,
          px: 2,
          minWidth: 140
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          {showFlag && (
            <Typography component="span" sx={{ fontSize: '1.2em' }}>
              {currentLanguage.flag}
            </Typography>
          )}
          {showName && (
            <Typography variant="body2" component="span">
              {isMobile ? currentLanguage.code.toUpperCase() : currentLanguage.name}
            </Typography>
          )}
        </Box>
      </Button>
    );
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      {renderButton()}
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            maxHeight: 400,
            width: isMobile ? 280 : 320,
            mt: 1,
            borderRadius: 2,
            boxShadow: theme.shadows[8]
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box px={2} py={1}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            {t('common:language.select', 'Select Language')}
          </Typography>
        </Box>
        <Divider />

        {SUPPORTED_LANGUAGES.map((language, index) => (
          <MenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            selected={language.code === i18n.language}
            sx={{
              py: 1.5,
              px: 2,
              '&.Mui-selected': {
                backgroundColor: theme.palette.primary.light + '20',
                '&:hover': {
                  backgroundColor: theme.palette.primary.light + '30',
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <Typography component="span" sx={{ fontSize: '1.5em' }}>
                {language.flag}
              </Typography>
            </ListItemIcon>

            <ListItemText
              primary={
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="body1" fontWeight={language.code === i18n.language ? 600 : 400}>
                    {language.name}
                  </Typography>
                  {language.code === i18n.language && (
                    <CheckIcon color="primary" fontSize="small" />
                  )}
                </Box>
              }
              secondary={
                <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                  <Chip
                    label={language.code.toUpperCase()}
                    size="small"
                    variant="outlined"
                    sx={{ height: 20, fontSize: '0.7em' }}
                  />
                  {language.rtl && (
                    <Chip
                      label="RTL"
                      size="small"
                      color="info"
                      variant="outlined"
                      sx={{ height: 20, fontSize: '0.7em' }}
                    />
                  )}
                  {index < 2 && (
                    <Chip
                      label="Primary"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ height: 20, fontSize: '0.7em' }}
                    />
                  )}
                </Box>
              }
            />
          </MenuItem>
        ))}

        <Divider sx={{ mt: 1 }} />
        <Box px={2} py={1}>
          <Typography variant="caption" color="text.secondary">
            {t('common:language.help', 'Missing your language? Contact support!')}
          </Typography>
        </Box>
      </Menu>
    </Box>
  );
};



export default LanguageSwitcher; 