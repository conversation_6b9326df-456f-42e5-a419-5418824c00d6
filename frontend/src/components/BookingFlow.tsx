import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stepper,
  Step,
  StepLabel,
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Divider,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Switch,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Rating,
  IconButton
} from '@mui/material';
import {
  CalendarToday,
  LocationOn,
  DirectionsBike,
  Payment,
  CheckCircle,
  ExpandMore,
  Add,
  Remove,
  Security,
  LocalOffer,
  Schedule,
  Phone,
  Email,
  Message,
  Star,
  Info
} from '@mui/icons-material';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Vehicle, Booking, AddOn, CreateBookingPayload, PaymentMethod } from '../types';
import * as BookingService from '../services/BookingService';
import * as PaymentService from '../services/PaymentService';
import { useAuth } from '../contexts/AuthContext';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '');

interface BookingFlowProps {
  open: boolean;
  onClose: () => void;
  vehicle: Vehicle | null;
  onBookingComplete: (booking: Booking) => void;
}

interface BookingFormData {
  startDate: Date | null;
  endDate: Date | null;
  startTime: Date | null;
  endTime: Date | null;
  deliveryRequested: boolean;
  deliveryAddress: string;
  selectedAddOns: string[];
  paymentMethod: PaymentMethod;
  specialRequests: string;
  emergencyContact: {
    name: string;
    phone: string;
  };
  agreesToTerms: boolean;
  agreesToInsurance: boolean;
}

const steps = [
  'Select Dates',
  'Add-ons & Options',
  'Personal Details',
  'Payment',
  'Confirmation'
];

const BookingFlow: React.FC<BookingFlowProps> = ({
  open,
  onClose,
  vehicle,
  onBookingComplete
}) => {
  const { user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [bookingData, setBookingData] = useState<BookingFormData>({
    startDate: null,
    endDate: null,
    startTime: null,
    endTime: null,
    deliveryRequested: false,
    deliveryAddress: '',
    selectedAddOns: [],
    paymentMethod: PaymentMethod.Card,
    specialRequests: '',
    emergencyContact: {
      name: '',
      phone: ''
    },
    agreesToTerms: false,
    agreesToInsurance: false
  });
  const [pricing, setPricing] = useState({
    dailyRate: 0,
    totalDays: 0,
    subtotal: 0,
    addOnsTotal: 0,
    deliveryFee: 0,
    insuranceFee: 0,
    serviceFee: 0,
    total: 0
  });

  useEffect(() => {
    if (vehicle && bookingData.startDate && bookingData.endDate) {
      calculatePricing();
    }
  }, [vehicle, bookingData.startDate, bookingData.endDate, bookingData.selectedAddOns, bookingData.deliveryRequested, bookingData.agreesToInsurance]);

  const calculatePricing = () => {
    if (!vehicle || !bookingData.startDate || !bookingData.endDate) return;

    const startDate = new Date(bookingData.startDate);
    const endDate = new Date(bookingData.endDate);
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    let dailyRate = vehicle.dailyRate;
    
    // Apply weekly/monthly discounts
    if (totalDays >= 30) {
      dailyRate = vehicle.monthlyRate / 30;
    } else if (totalDays >= 7) {
      dailyRate = vehicle.weeklyRate / 7;
    }

    const subtotal = dailyRate * totalDays;
    
    // Calculate add-ons
    const addOnsTotal = bookingData.selectedAddOns.reduce((total, addonId) => {
      const addon = vehicle.addOns.find(a => a.id === addonId);
      return total + (addon ? addon.dailyRate * totalDays : 0);
    }, 0);

    // Calculate delivery fee
    const deliveryFee = bookingData.deliveryRequested ? vehicle.deliveryFee : 0;

    // Calculate insurance (10% of subtotal)
    const insuranceFee = bookingData.agreesToInsurance ? subtotal * 0.1 : 0;

    // Service fee (5% of subtotal + addons)
    const serviceFee = (subtotal + addOnsTotal) * 0.05;

    const total = subtotal + addOnsTotal + deliveryFee + insuranceFee + serviceFee;

    setPricing({
      dailyRate,
      totalDays,
      subtotal,
      addOnsTotal,
      deliveryFee,
      insuranceFee,
      serviceFee,
      total
    });
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0:
        return !!(bookingData.startDate && bookingData.endDate && bookingData.startTime && bookingData.endTime);
      case 1:
        return true; // Optional step
      case 2:
        return !!(bookingData.emergencyContact.name && bookingData.emergencyContact.phone);
      case 3:
        return bookingData.agreesToTerms;
      default:
        return true;
    }
  };

  const handleBookingSubmit = async () => {
    if (!vehicle || !user) return;

    setLoading(true);
    setError(null);

    try {
      const bookingPayload: CreateBookingPayload = {
        vehicleId: vehicle.id,
        userId: user.id,
        providerId: vehicle.providerId,
        startDate: bookingData.startDate!.toISOString(),
        endDate: bookingData.endDate!.toISOString(),
        addOns: bookingData.selectedAddOns,
        paymentMethod: bookingData.paymentMethod,
        deliveryRequested: bookingData.deliveryRequested,
        deliveryAddress: bookingData.deliveryAddress,
        pickupInstructions: bookingData.specialRequests,
        notes: bookingData.specialRequests
      };

      const result = await BookingService.createBooking(bookingPayload);
      
      if (result.success && result.data) {
        onBookingComplete(result.data.booking);
        handleNext(); // Move to confirmation step
      } else {
        setError('Failed to create booking. Please try again.');
      }
    } catch (error) {
      setError('An error occurred while creating your booking.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Select Your Rental Period
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Start Date"
                  value={bookingData.startDate}
                  onChange={(date) => setBookingData({ ...bookingData, startDate: date })}
                  minDate={new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="End Date"
                  value={bookingData.endDate}
                  onChange={(date) => setBookingData({ ...bookingData, endDate: date })}
                  minDate={bookingData.startDate || new Date()}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TimePicker
                  label="Pickup Time"
                  value={bookingData.startTime}
                  onChange={(time) => setBookingData({ ...bookingData, startTime: time })}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TimePicker
                  label="Return Time"
                  value={bookingData.endTime}
                  onChange={(time) => setBookingData({ ...bookingData, endTime: time })}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
              
              {vehicle?.deliveryAvailable && (
                <>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={bookingData.deliveryRequested}
                          onChange={(e) => setBookingData({ ...bookingData, deliveryRequested: e.target.checked })}
                        />
                      }
                      label={`Request Delivery (+$${vehicle.deliveryFee})`}
                    />
                  </Grid>
                  
                  {bookingData.deliveryRequested && (
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Delivery Address"
                        value={bookingData.deliveryAddress}
                        onChange={(e) => setBookingData({ ...bookingData, deliveryAddress: e.target.value })}
                        multiline
                        rows={2}
                      />
                    </Grid>
                  )}
                </>
              )}
            </Grid>
          </LocalizationProvider>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Add-ons & Options
              </Typography>
            </Grid>
            
            {vehicle?.addOns.map((addon) => (
              <Grid item xs={12} key={addon.id}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={bookingData.selectedAddOns.includes(addon.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setBookingData({
                            ...bookingData,
                            selectedAddOns: [...bookingData.selectedAddOns, addon.id]
                          });
                        } else {
                          setBookingData({
                            ...bookingData,
                            selectedAddOns: bookingData.selectedAddOns.filter(id => id !== addon.id)
                          });
                        }
                      }}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body1">{addon.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {addon.description} - ${addon.dailyRate}/day
                      </Typography>
                    </Box>
                  }
                />
              </Grid>
            ))}
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={bookingData.agreesToInsurance}
                    onChange={(e) => setBookingData({ ...bookingData, agreesToInsurance: e.target.checked })}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body1">Additional Insurance Coverage</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Comprehensive coverage for damage, theft, and accidents (+10% of rental cost)
                    </Typography>
                  </Box>
                }
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Special Requests"
                value={bookingData.specialRequests}
                onChange={(e) => setBookingData({ ...bookingData, specialRequests: e.target.value })}
                multiline
                rows={3}
                placeholder="Any special requests or requirements..."
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Personal Details
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={bookingData.emergencyContact.name}
                onChange={(e) => setBookingData({
                  ...bookingData,
                  emergencyContact: {
                    ...bookingData.emergencyContact,
                    name: e.target.value
                  }
                })}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={bookingData.emergencyContact.phone}
                onChange={(e) => setBookingData({
                  ...bookingData,
                  emergencyContact: {
                    ...bookingData.emergencyContact,
                    phone: e.target.value
                  }
                })}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <Alert severity="info">
                <Typography variant="body2">
                  Your emergency contact will only be used in case of an emergency during your rental period.
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Payment Method
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  value={bookingData.paymentMethod}
                  onChange={(e) => setBookingData({ ...bookingData, paymentMethod: e.target.value as PaymentMethod })}
                >
                  <MenuItem value={PaymentMethod.Card}>Credit/Debit Card</MenuItem>
                  <MenuItem value={PaymentMethod.Cash}>Cash on Pickup</MenuItem>
                  <MenuItem value={PaymentMethod.PayPal}>PayPal</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {bookingData.paymentMethod === PaymentMethod.Card && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Card Details
                  </Typography>
                  <CardElement
                    options={{
                      style: {
                        base: {
                          fontSize: '16px',
                          color: '#424770',
                          '::placeholder': {
                            color: '#aab7c4',
                          },
                        },
                      },
                    }}
                  />
                </Paper>
              </Grid>
            )}
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={bookingData.agreesToTerms}
                    onChange={(e) => setBookingData({ ...bookingData, agreesToTerms: e.target.checked })}
                  />
                }
                label="I agree to the Terms of Service and Privacy Policy"
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <Alert severity="warning">
                <Typography variant="body2">
                  Please ensure you have a valid driver's license and meet the minimum age requirement.
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} display="flex" justifyContent="center">
              <CheckCircle color="success" sx={{ fontSize: 64 }} />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h5" align="center" gutterBottom>
                Booking Confirmed!
              </Typography>
              <Typography variant="body1" align="center" color="text.secondary">
                Your booking has been successfully created. You will receive a confirmation email shortly.
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Booking Summary
                  </Typography>
                  <Typography variant="body2">
                    Vehicle: {vehicle?.make} {vehicle?.model}
                  </Typography>
                  <Typography variant="body2">
                    Dates: {bookingData.startDate?.toLocaleDateString()} - {bookingData.endDate?.toLocaleDateString()}
                  </Typography>
                  <Typography variant="body2">
                    Total: ${pricing.total.toFixed(2)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  const renderPricingSummary = () => (
    <Card sx={{ position: 'sticky', top: 20 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Pricing Summary
        </Typography>
        
        {pricing.totalDays > 0 && (
          <>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">
                ${pricing.dailyRate.toFixed(2)} × {pricing.totalDays} days
              </Typography>
              <Typography variant="body2">
                ${pricing.subtotal.toFixed(2)}
              </Typography>
            </Box>
            
            {pricing.addOnsTotal > 0 && (
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Add-ons</Typography>
                <Typography variant="body2">
                  ${pricing.addOnsTotal.toFixed(2)}
                </Typography>
              </Box>
            )}
            
            {pricing.deliveryFee > 0 && (
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Delivery Fee</Typography>
                <Typography variant="body2">
                  ${pricing.deliveryFee.toFixed(2)}
                </Typography>
              </Box>
            )}
            
            {pricing.insuranceFee > 0 && (
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Insurance</Typography>
                <Typography variant="body2">
                  ${pricing.insuranceFee.toFixed(2)}
                </Typography>
              </Box>
            )}
            
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Service Fee</Typography>
              <Typography variant="body2">
                ${pricing.serviceFee.toFixed(2)}
              </Typography>
            </Box>
            
            <Divider sx={{ my: 1 }} />
            
            <Box display="flex" justifyContent="space-between">
              <Typography variant="h6">Total</Typography>
              <Typography variant="h6" color="primary">
                ${pricing.total.toFixed(2)}
              </Typography>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      scroll="paper"
    >
      <DialogTitle>
        Book {vehicle?.make} {vehicle?.model}
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}
            
            {renderStepContent(activeStep)}
          </Grid>
          
          <Grid item xs={12} md={4}>
            {renderPricingSummary()}
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        
        {activeStep > 0 && activeStep < steps.length - 1 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        
        {activeStep < steps.length - 2 && (
          <Button
            onClick={handleNext}
            variant="contained"
            disabled={!validateStep(activeStep)}
          >
            Next
          </Button>
        )}
        
        {activeStep === steps.length - 2 && (
          <Button
            onClick={handleBookingSubmit}
            variant="contained"
            disabled={loading || !validateStep(activeStep)}
          >
            {loading ? <CircularProgress size={24} /> : 'Complete Booking'}
          </Button>
        )}
        
        {activeStep === steps.length - 1 && (
          <Button
            onClick={onClose}
            variant="contained"
            color="success"
          >
            Done
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

// Payment Component using Stripe
const PaymentForm: React.FC<{ onPaymentComplete: (result: any) => void }> = ({ onPaymentComplete }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) return;

    setLoading(true);

    const card = elements.getElement(CardElement);
    if (!card) return;

    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: 'card',
      card,
    });

    if (error) {
      console.error(error);
    } else {
      onPaymentComplete(paymentMethod);
    }

    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      <CardElement />
      <Button
        type="submit"
        disabled={!stripe || loading}
        variant="contained"
        fullWidth
        sx={{ mt: 2 }}
      >
        {loading ? 'Processing...' : 'Complete Payment'}
      </Button>
    </form>
  );
};

// Main export wrapped with Stripe Elements
const BookingFlowWrapper: React.FC<BookingFlowProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <BookingFlow {...props} />
    </Elements>
  );
};

export default BookingFlowWrapper;
