import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Avatar,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  Badge,
  Tab,
  Tabs,
  TabPanel,
} from '@mui/material';
import {
  Message as MessageIcon,
  Send,
  Person,
  Schedule,
  DirectionsBike,
  AttachFile,
  Star,
  Reply,
  MoreVert,
  Notifications,
  Extension,
  Assignment,
  CheckCircle,
  Cancel,
} from '@mui/icons-material';
import { ProviderService } from '../../services/ProviderService';
import AgreementGenerator from '../agreements/AgreementGenerator';
import AgreementManager from '../agreements/AgreementManager';
import CommunicationPanel from '../CommunicationPanel';

interface MessageThread {
  id: string;
  customerId: string;
  customerName: string;
  customerAvatar?: string;
  bookingId?: string;
  vehicleName?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  status: 'active' | 'archived';
  priority: 'low' | 'normal' | 'high';
}

interface ExtensionRequest {
  id: string;
  bookingId: string;
  customerId: string;
  customerName: string;
  vehicleName: string;
  currentEndDate: string;
  requestedEndDate: string;
  additionalDays: number;
  additionalCost: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  requestedAt: string;
}

interface Agreement {
  id: string;
  bookingId: string;
  customerId: string;
  customerName: string;
  type: 'rental_agreement' | 'damage_waiver' | 'extension_agreement';
  title: string;
  content: string;
  status: 'sent' | 'viewed' | 'signed' | 'rejected';
  sentAt: string;
  signedAt?: string;
}

const ProviderMessaging: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [messageThreads, setMessageThreads] = useState<MessageThread[]>([]);
  const [extensionRequests, setExtensionRequests] = useState<ExtensionRequest[]>([]);
  const [agreements, setAgreements] = useState<Agreement[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedThread, setSelectedThread] = useState<MessageThread | null>(null);
  const [communicationPanelOpen, setCommunicationPanelOpen] = useState(false);
  const [extensionDialogOpen, setExtensionDialogOpen] = useState(false);
  const [agreementDialogOpen, setAgreementDialogOpen] = useState(false);
  const [selectedExtension, setSelectedExtension] = useState<ExtensionRequest | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const [newAgreement, setNewAgreement] = useState({
    customerId: '',
    bookingId: '',
    type: 'rental_agreement' as const,
    title: '',
    content: '',
  });

  useEffect(() => {
    fetchMessagingData();
  }, []);

  const fetchMessagingData = async () => {
    try {
      setLoading(true);
      
      const [threadsResponse, extensionsResponse, agreementsResponse] = await Promise.all([
        ProviderService.getMessageThreads(),
        ProviderService.getExtensionRequests(),
        ProviderService.getAgreements(),
      ]);

      if (threadsResponse.success) {
        setMessageThreads(threadsResponse.data || []);
      }

      if (extensionsResponse.success) {
        setExtensionRequests(extensionsResponse.data || []);
      }

      if (agreementsResponse.success) {
        setAgreements(agreementsResponse.data || []);
      }
    } catch (error) {
      console.error('Error fetching messaging data:', error);
      setSnackbar({ open: true, message: 'Failed to load messaging data', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleExtensionResponse = async (extensionId: string, approved: boolean) => {
    try {
      const response = await ProviderService.respondToExtensionRequest(extensionId, approved);
      
      if (response.success) {
        setExtensionRequests(extensionRequests.map(ext => 
          ext.id === extensionId 
            ? { ...ext, status: approved ? 'approved' : 'rejected' }
            : ext
        ));
        setSnackbar({ 
          open: true, 
          message: `Extension request ${approved ? 'approved' : 'rejected'} successfully`, 
          severity: 'success' 
        });
      } else {
        setSnackbar({ 
          open: true, 
          message: response.error || 'Failed to respond to extension request', 
          severity: 'error' 
        });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to respond to extension request', severity: 'error' });
    }
  };

  const handleSendAgreement = async () => {
    try {
      const response = await ProviderService.sendAgreement(newAgreement);
      
      if (response.success) {
        setAgreements([...agreements, response.data]);
        setAgreementDialogOpen(false);
        setNewAgreement({
          customerId: '',
          bookingId: '',
          type: 'rental_agreement',
          title: '',
          content: '',
        });
        setSnackbar({ open: true, message: 'Agreement sent successfully', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to send agreement', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to send agreement', severity: 'error' });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': case 'signed': return 'success';
      case 'rejected': return 'error';
      case 'sent': case 'viewed': return 'info';
      default: return 'default';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const TabPanel = ({ children, value, index }: { children: React.ReactNode; value: number; index: number }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <MessageIcon sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Customer Communication
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab 
            label={
              <Badge badgeContent={messageThreads.reduce((sum, thread) => sum + thread.unreadCount, 0)} color="error">
                Messages
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={extensionRequests.filter(req => req.status === 'pending').length} color="warning">
                Extension Requests
              </Badge>
            } 
          />
          <Tab
            label={
              <Badge badgeContent={agreements.filter(agr => agr.status === 'sent').length} color="info">
                Agreements
              </Badge>
            }
          />
          <Tab label="Generate Agreement" />
        </Tabs>
      </Box>

      {/* Messages Tab */}
      <TabPanel value={activeTab} index={0}>
        {messageThreads.length === 0 ? (
          <Alert severity="info">
            No message threads yet. Messages will appear here when customers contact you.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {messageThreads.map((thread) => (
              <Grid item xs={12} md={6} key={thread.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Avatar src={thread.customerAvatar} sx={{ mr: 2 }}>
                        {thread.customerName.charAt(0)}
                      </Avatar>
                      <Box flex={1}>
                        <Typography variant="h6">
                          {thread.customerName}
                        </Typography>
                        {thread.vehicleName && (
                          <Typography variant="body2" color="textSecondary">
                            {thread.vehicleName}
                          </Typography>
                        )}
                      </Box>
                      {thread.unreadCount > 0 && (
                        <Badge badgeContent={thread.unreadCount} color="error" />
                      )}
                    </Box>

                    <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                      {thread.lastMessage}
                    </Typography>

                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="textSecondary">
                        {new Date(thread.lastMessageTime).toLocaleString()}
                      </Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<Reply />}
                        onClick={() => {
                          setSelectedThread(thread);
                          setCommunicationPanelOpen(true);
                        }}
                      >
                        Reply
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Extension Requests Tab */}
      <TabPanel value={activeTab} index={1}>
        {extensionRequests.length === 0 ? (
          <Alert severity="info">
            No extension requests yet. Requests will appear here when customers want to extend their rentals.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {extensionRequests.map((request) => (
              <Grid item xs={12} md={6} key={request.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                      <Box flex={1}>
                        <Typography variant="h6" gutterBottom>
                          {request.customerName}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {request.vehicleName}
                        </Typography>
                      </Box>
                      <Chip
                        label={request.status.toUpperCase()}
                        color={getStatusColor(request.status) as any}
                        size="small"
                      />
                    </Box>

                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <Schedule />
                        </ListItemIcon>
                        <ListItemText
                          primary="Extension Period"
                          secondary={`${new Date(request.currentEndDate).toLocaleDateString()} → ${new Date(request.requestedEndDate).toLocaleDateString()}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Extension />
                        </ListItemIcon>
                        <ListItemText
                          primary="Additional Days"
                          secondary={`${request.additionalDays} days (+${formatCurrency(request.additionalCost)})`}
                        />
                      </ListItem>
                    </List>

                    {request.reason && (
                      <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
                        <Typography variant="body2">
                          <strong>Reason:</strong> {request.reason}
                        </Typography>
                      </Alert>
                    )}

                    {request.status === 'pending' && (
                      <Box display="flex" gap={1} mt={2}>
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          startIcon={<CheckCircle />}
                          onClick={() => handleExtensionResponse(request.id, true)}
                        >
                          Approve
                        </Button>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          startIcon={<Cancel />}
                          onClick={() => handleExtensionResponse(request.id, false)}
                        >
                          Reject
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Agreements Tab */}
      <TabPanel value={activeTab} index={2}>
        <AgreementManager />
      </TabPanel>

      {/* Generate Agreement Tab */}
      <TabPanel value={activeTab} index={3}>
        {selectedThread?.booking && (
          <AgreementGenerator
            booking={selectedThread.booking}
            onAgreementSent={(agreement) => {
              setSnackbar({
                open: true,
                message: 'Agreement sent successfully!',
                severity: 'success'
              });
            }}
          />
        )}

        {agreements.length === 0 ? (
          <Alert severity="info">
            No agreements sent yet. Use the "Send Agreement" button to create and send agreements to customers.
          </Alert>
        ) : (
          <Grid container spacing={3}>
            {agreements.map((agreement) => (
              <Grid item xs={12} md={6} key={agreement.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                      <Box flex={1}>
                        <Typography variant="h6" gutterBottom>
                          {agreement.title}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Version: {agreement.version || 'N/A'}
                        </Typography>
                      </Box>
                      <Chip
                        label={agreement.status.toUpperCase()}
                        color={getStatusColor(agreement.status) as any}
                        size="small"
                      />
                    </Box>

                    <Typography variant="body2" sx={{ mb: 2 }}>
                      Type: {agreement.type ? agreement.type.replace('_', ' ').toUpperCase() : 'Agreement'}
                    </Typography>

                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="textSecondary">
                        Sent: {agreement.sentAt ? new Date(agreement.sentAt).toLocaleString() : 'N/A'}
                      </Typography>
                      {agreement.signedAt && (
                        <Typography variant="caption" color="success.main">
                          Signed: {new Date(agreement.signedAt).toLocaleString()}
                        </Typography>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Communication Panel */}
      <CommunicationPanel
        open={communicationPanelOpen}
        onClose={() => setCommunicationPanelOpen(false)}
        bookingId={selectedThread?.bookingId}
        recipientUser={selectedThread ? {
          id: selectedThread.customerId,
          name: selectedThread.customerName,
          email: '', // Would be fetched from API
          avatar: selectedThread.customerAvatar,
        } : undefined}
      />

      {/* Send Agreement Dialog */}
      <Dialog open={agreementDialogOpen} onClose={() => setAgreementDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Send Agreement</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Agreement Type</InputLabel>
                <Select
                  value={newAgreement.type}
                  label="Agreement Type"
                  onChange={(e) => setNewAgreement({
                    ...newAgreement,
                    type: e.target.value as any
                  })}
                >
                  <MenuItem value="rental_agreement">Rental Agreement</MenuItem>
                  <MenuItem value="damage_waiver">Damage Waiver</MenuItem>
                  <MenuItem value="extension_agreement">Extension Agreement</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer ID"
                value={newAgreement.customerId}
                onChange={(e) => setNewAgreement({
                  ...newAgreement,
                  customerId: e.target.value
                })}
                placeholder="Enter customer ID"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Agreement Title"
                value={newAgreement.title}
                onChange={(e) => setNewAgreement({
                  ...newAgreement,
                  title: e.target.value
                })}
                placeholder="e.g., Vehicle Rental Agreement - Honda Activa"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={8}
                label="Agreement Content"
                value={newAgreement.content}
                onChange={(e) => setNewAgreement({
                  ...newAgreement,
                  content: e.target.value
                })}
                placeholder="Enter the agreement terms and conditions..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAgreementDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSendAgreement} variant="contained">
            Send Agreement
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ProviderMessaging;
