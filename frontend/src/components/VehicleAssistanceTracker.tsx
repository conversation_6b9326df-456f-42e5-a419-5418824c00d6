import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Schedule as PendingIcon,
  Assignment as AssignedIcon,
  Build as InProgressIcon,
  Cancel as CancelledIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { apiService } from '../services/apiService';

interface AssistanceRequest {
  id: string;
  category: string;
  priority: string;
  status: string;
  reason: string;
  message?: string;
  location?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}

interface VehicleAssistanceTrackerProps {
  userId?: string;
  bookingId?: string;
  refreshInterval?: number;
}

const VehicleAssistanceTracker: React.FC<VehicleAssistanceTrackerProps> = ({
  userId,
  bookingId,
  refreshInterval = 30000 // 30 seconds
}) => {
  const { t } = useTranslation();
  const [requests, setRequests] = useState<AssistanceRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<AssistanceRequest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  useEffect(() => {
    fetchAssistanceRequests();
    
    // Set up auto-refresh
    const interval = setInterval(fetchAssistanceRequests, refreshInterval);
    return () => clearInterval(interval);
  }, [userId, bookingId, refreshInterval]);

  const fetchAssistanceRequests = async () => {
    try {
      setError(null);
      const response = await apiService.get('/vehicle-assistance/user');
      
      if (response.success) {
        setRequests(response.data || []);
      } else {
        setError(response.error || 'Failed to fetch assistance requests');
      }
    } catch (error) {
      console.error('Error fetching assistance requests:', error);
      setError('Failed to fetch assistance requests');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <PendingIcon color="warning" />;
      case 'ASSIGNED':
        return <AssignedIcon color="info" />;
      case 'IN_PROGRESS':
        return <InProgressIcon color="primary" />;
      case 'RESOLVED':
        return <CheckIcon color="success" />;
      case 'CANCELLED':
        return <CancelledIcon color="error" />;
      default:
        return <PendingIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'warning';
      case 'ASSIGNED':
        return 'info';
      case 'IN_PROGRESS':
        return 'primary';
      case 'RESOLVED':
        return 'success';
      case 'CANCELLED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'error';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      case 'LOW':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getEstimatedResponseTime = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return t('assistance:responseTime.urgent', '15-30 minutes');
      case 'HIGH':
        return t('assistance:responseTime.high', '30-60 minutes');
      case 'MEDIUM':
        return t('assistance:responseTime.medium', '1-2 hours');
      case 'LOW':
        return t('assistance:responseTime.low', '2-4 hours');
      default:
        return t('assistance:responseTime.medium', '1-2 hours');
    }
  };

  const handleViewDetails = (request: AssistanceRequest) => {
    setSelectedRequest(request);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedRequest(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (requests.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('assistance:title', 'Vehicle Assistance')}
          </Typography>
          <Typography color="text.secondary">
            {t('assistance:noRequests', 'No assistance requests found.')}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('assistance:title', 'Vehicle Assistance')} ({requests.length})
          </Typography>
          
          <Timeline>
            {requests.map((request, index) => (
              <TimelineItem key={request.id}>
                <TimelineSeparator>
                  <TimelineDot color={getStatusColor(request.status) as any}>
                    {getStatusIcon(request.status)}
                  </TimelineDot>
                  {index < requests.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                
                <TimelineContent>
                  <Card variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {t(`assistance:categories.${request.category.toLowerCase()}`, request.category)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {formatDateTime(request.createdAt)}
                          </Typography>
                        </Box>
                        <Box display="flex" gap={1}>
                          <Chip
                            label={t(`assistance:priority.${request.priority.toLowerCase()}`, request.priority)}
                            color={getPriorityColor(request.priority) as any}
                            size="small"
                          />
                          <Chip
                            label={t(`assistance:status.${request.status.toLowerCase()}`, request.status)}
                            color={getStatusColor(request.status) as any}
                            size="small"
                          />
                        </Box>
                      </Box>
                      
                      <Typography variant="body2" paragraph>
                        {request.reason}
                      </Typography>
                      
                      {request.location && (
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <LocationIcon fontSize="small" color="action" />
                          <Typography variant="body2" color="text.secondary">
                            {request.location}
                          </Typography>
                        </Box>
                      )}
                      
                      {request.status === 'PENDING' && (
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <TimeIcon fontSize="small" color="action" />
                          <Typography variant="body2" color="text.secondary">
                            {t('assistance:estimatedResponse', 'Estimated response: {{time}}', {
                              time: getEstimatedResponseTime(request.priority)
                            })}
                          </Typography>
                        </Box>
                      )}
                      
                      {request.adminNotes && (
                        <Alert severity="info" sx={{ mt: 1, mb: 1 }}>
                          <Typography variant="body2">
                            <strong>{t('assistance:adminNotes', 'Admin Notes')}:</strong> {request.adminNotes}
                          </Typography>
                        </Alert>
                      )}
                      
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                        <Button
                          size="small"
                          onClick={() => handleViewDetails(request)}
                        >
                          {t('common:buttons.view', 'View Details')}
                        </Button>
                        
                        {(request.status === 'ASSIGNED' || request.status === 'IN_PROGRESS') && (
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<PhoneIcon />}
                            color="primary"
                          >
                            {t('assistance:contactSupport', 'Contact Support')}
                          </Button>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={handleCloseDetails}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {t('assistance:requestDetails', 'Assistance Request Details')}
        </DialogTitle>
        
        <DialogContent>
          {selectedRequest && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:category', 'Category')}
                </Typography>
                <Typography variant="body1" paragraph>
                  {t(`assistance:categories.${selectedRequest.category.toLowerCase()}`, selectedRequest.category)}
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:priority.title', 'Priority')}
                </Typography>
                <Chip
                  label={t(`assistance:priority.${selectedRequest.priority.toLowerCase()}`, selectedRequest.priority)}
                  color={getPriorityColor(selectedRequest.priority) as any}
                  sx={{ mb: 2 }}
                />
                
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:status.title', 'Status')}
                </Typography>
                <Chip
                  label={t(`assistance:status.${selectedRequest.status.toLowerCase()}`, selectedRequest.status)}
                  color={getStatusColor(selectedRequest.status) as any}
                  sx={{ mb: 2 }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:createdAt', 'Created At')}
                </Typography>
                <Typography variant="body1" paragraph>
                  {formatDateTime(selectedRequest.createdAt)}
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:lastUpdated', 'Last Updated')}
                </Typography>
                <Typography variant="body1" paragraph>
                  {formatDateTime(selectedRequest.updatedAt)}
                </Typography>
                
                {selectedRequest.resolvedAt && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('assistance:resolvedAt', 'Resolved At')}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {formatDateTime(selectedRequest.resolvedAt)}
                    </Typography>
                  </>
                )}
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  {t('assistance:description', 'Description')}
                </Typography>
                <Typography variant="body1" paragraph>
                  {selectedRequest.reason}
                </Typography>
                
                {selectedRequest.message && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('assistance:additionalInfo', 'Additional Information')}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {selectedRequest.message}
                    </Typography>
                  </>
                )}
                
                {selectedRequest.location && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('assistance:location', 'Location')}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {selectedRequest.location}
                    </Typography>
                  </>
                )}
                
                {selectedRequest.adminNotes && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('assistance:adminNotes', 'Admin Notes')}
                    </Typography>
                    <Alert severity="info">
                      <Typography variant="body2">
                        {selectedRequest.adminNotes}
                      </Typography>
                    </Alert>
                  </>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDetails}>
            {t('common:buttons.close', 'Close')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default VehicleAssistanceTracker;
