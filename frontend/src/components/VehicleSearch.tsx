import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Paper,
  Alert,
  CircularProgress,
  Stack,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  DirectionsBike as BikeIcon
} from '@mui/icons-material';
import { VehicleService, Vehicle } from '../services/VehicleService';

interface ScrapedVehicle {
  id: string;
  brand: string;
  model: string;
  year: string;
  engine: string;
  type: string;
  category: string;
  images: string[];
  specs: string[];
  displayName: string;
  searchTerms: string;
}

const VehicleSearch = ({ onVehicleSelect, onManualEntry, selectedVehicle }: any) => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [search, setSearch] = useState('');
  const [suggestions, setSuggestions] = useState<Vehicle[]>([]);
  const [manualEntryMode, setManualEntryMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showManualDialog, setShowManualDialog] = useState(false);
  const [showVehicleGrid, setShowVehicleGrid] = useState(false);
  const [manualVehicle, setManualVehicle] = useState({
    brand: '',
    model: '',
    year: '2023',
    engine: '',
    type: 'scooter',
    category: 'medium'
  });

  useEffect(() => {
    fetchVehicles();
  }, []);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔍 VehicleSearch: Starting to fetch vehicles...');
      const vehicles = await VehicleService.getVehicles();
      
      console.log('🔍 VehicleSearch: Received vehicles from service:', vehicles.length);
      
      if (vehicles.length > 0) {
        setVehicles(vehicles);
        console.log(`✅ VehicleSearch: Loaded ${vehicles.length} vehicles from Supabase`);
        console.log('✅ VehicleSearch: Sample vehicle:', vehicles[0]);
      } else {
        setError('No vehicles found in database');
        console.log('⚠️ VehicleSearch: No vehicles found in Supabase');
      }
    } catch (err) {
      console.error('❌ VehicleSearch: Error fetching vehicles:', err);
      setError('Failed to load vehicle data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (searchTerm: string) => {
    setSearch(searchTerm);
    if (searchTerm.trim() === '') {
      setSuggestions([]);
      return;
    }

    try {
      console.log(`🔍 Searching for: "${searchTerm}"`);
      const searchResults = await VehicleService.searchVehiclesByText(searchTerm);
      setSuggestions(searchResults.slice(0, 10)); // Limit to 10 suggestions
      console.log(`✅ Found ${searchResults.length} matching vehicles`);
    } catch (err) {
      console.error('❌ Search error:', err);
      // Fallback to local filtering
      const searchLower = searchTerm.toLowerCase();
      const filtered = vehicles.filter(vehicle => {
        return (
          (vehicle.displayName && vehicle.displayName.toLowerCase().includes(searchLower)) ||
          (vehicle.brand && vehicle.brand.toLowerCase().includes(searchLower)) ||
          (vehicle.model && vehicle.model.toLowerCase().includes(searchLower)) ||
          (vehicle.category && vehicle.category.toLowerCase().includes(searchLower)) ||
          (vehicle.searchTerms && vehicle.searchTerms.includes(searchLower))
        );
      });
      setSuggestions(filtered.slice(0, 10));
    }
  };

  const handleVehicleSelect = (vehicle: Vehicle) => {
    if (onVehicleSelect) {
      onVehicleSelect(vehicle);
    }
    setSearch(vehicle.displayName);
    setSuggestions([]);
    setShowVehicleGrid(false);
  };

  const handleManualEntry = () => {
    setShowManualDialog(true);
  };

  const handleManualSubmit = async () => {
    if (!manualVehicle.brand || !manualVehicle.model) {
      setError('Brand and model are required');
      return;
    }

    const newVehicle: Vehicle = {
      id: Date.now().toString(),
      brand: manualVehicle.brand,
      model: manualVehicle.model,
      year: manualVehicle.year,
      engine: manualVehicle.engine,
      type: manualVehicle.type,
      category: manualVehicle.category,
      images: [],
      displayName: `${manualVehicle.brand} ${manualVehicle.model}`,
      searchTerms: `${manualVehicle.brand} ${manualVehicle.model} ${manualVehicle.engine} ${manualVehicle.type}`.toLowerCase()
    };

    try {
      // Add to local list
      setVehicles(prev => [...prev, newVehicle]);
      
      // Select the new vehicle
      if (onVehicleSelect) {
        onVehicleSelect(newVehicle);
      }
      
      setShowManualDialog(false);
      setManualVehicle({
        brand: '',
        model: '',
        year: '2023',
        engine: '',
        type: 'scooter',
        category: 'medium'
      });
      setError(null);
    } catch (err) {
      setError('Failed to add vehicle');
    }
  };

  const getVehicleImage = (vehicle: Vehicle) => {
    if (vehicle.images && vehicle.images.length > 0) {
      return vehicle.images[0];
    }
    return 'https://placehold.co/300x200/cccccc/666666?text=No+Image';
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'small': return 'success';
      case 'medium': return 'primary';
      case 'large': return 'warning';
      case 'luxury': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Search for your vehicle (brand or model)
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ position: 'relative', mb: 2 }}>
        <TextField
          fullWidth
          value={search}
          onChange={(e) => handleSearch(e.target.value)}
          placeholder="Search vehicles..."
          InputProps={{
            startAdornment: (
              <SearchIcon />
            ),
          }}
        />

        {suggestions.length > 0 && (
          <Paper
            sx={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              zIndex: 1000,
              maxHeight: 400,
              overflow: 'auto',
              mt: 1,
            }}
          >
            <List>
              {suggestions.map((vehicle, index) => (
                <React.Fragment key={`${vehicle.id}-${index}`}>
                  <ListItem
                    button
                    onClick={() => handleVehicleSelect(vehicle)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <ListItemAvatar>
                      <Avatar src={getVehicleImage(vehicle)} variant="rounded">
                        <BikeIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={vehicle.displayName}
                      secondary={`${vehicle.year} • ${vehicle.engine} • ${vehicle.category} ${vehicle.type}`}
                    />
                  </ListItem>
                  {index < suggestions.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        )}
      </Box>

      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleManualEntry}
        >
          Enter vehicle details manually
        </Button>
        
        <Button
          variant="outlined"
          onClick={() => setShowVehicleGrid(!showVehicleGrid)}
        >
          {showVehicleGrid ? 'Hide' : 'Show'} All Available Models
        </Button>
      </Stack>

      {loading && (
        <Box display="flex" justifyContent="center" py={2}>
          <CircularProgress />
        </Box>
      )}

      {/* Vehicle Grid */}
      {showVehicleGrid && vehicles.length > 0 && (
        <Paper sx={{ p: 2, mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            Available Models ({vehicles.length})
          </Typography>
          <Grid container spacing={2}>
            {vehicles.slice(0, 20).map((vehicle) => (
              <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
                <Paper
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => handleVehicleSelect(vehicle)}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar
                      src={getVehicleImage(vehicle)}
                      variant="rounded"
                      sx={{ width: 60, height: 60 }}
                    >
                      <BikeIcon />
                    </Avatar>
                    <Box flex={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {vehicle.displayName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {vehicle.year} • {vehicle.category}
                      </Typography>
                      <Chip
                        label={vehicle.type}
                        size="small"
                        color={getCategoryColor(vehicle.category) as any}
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {/* Manual Entry Dialog */}
      <Dialog open={showManualDialog} onClose={() => setShowManualDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Enter Vehicle Details Manually</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Brand"
                value={manualVehicle.brand}
                onChange={(e) => setManualVehicle({ ...manualVehicle, brand: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Model"
                value={manualVehicle.model}
                onChange={(e) => setManualVehicle({ ...manualVehicle, model: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Year"
                value={manualVehicle.year}
                onChange={(e) => setManualVehicle({ ...manualVehicle, year: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Engine"
                value={manualVehicle.engine}
                onChange={(e) => setManualVehicle({ ...manualVehicle, engine: e.target.value })}
                placeholder="e.g., 125cc"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={manualVehicle.type}
                  onChange={(e) => setManualVehicle({ ...manualVehicle, type: e.target.value })}
                >
                  <MenuItem value="scooter">Scooter</MenuItem>
                  <MenuItem value="motorcycle">Motorcycle</MenuItem>
                  <MenuItem value="electric">Electric</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={manualVehicle.category}
                  onChange={(e) => setManualVehicle({ ...manualVehicle, category: e.target.value })}
                >
                  <MenuItem value="small">Small</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="large">Large</MenuItem>
                  <MenuItem value="luxury">Luxury</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowManualDialog(false)}>Cancel</Button>
          <Button onClick={handleManualSubmit} variant="contained">
            Add Vehicle
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VehicleSearch; 