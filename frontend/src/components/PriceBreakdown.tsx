import React from 'react';
import { Typography, Box, Divider } from '@mui/material';

interface PriceBreakdownProps {
    baseRate: number;
    bookingFee: number;
    addOns?: number;
    deliveryFee?: number;
    discountDetails?: {
        discountCode: string;
        discountAmount: number;
        discountType: 'percentage' | 'fixed';
    };
    total: number;
    currency?: string;
}

const PriceBreakdown: React.FC<PriceBreakdownProps> = ({
    baseRate,
    bookingFee,
    addOns = 0,
    deliveryFee = 0,
    discountDetails,
    total,
    currency = '$'
}) => {
    const formatCurrency = (amount: number) => `${currency}${amount.toFixed(2)}`;

    return (
        <Box sx={{ 
            border: '1px solid #e0e0e0', 
            borderRadius: 2, 
            p: 2 
        }}>
            <Typography variant="h6" gutterBottom>
                Price Breakdown
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Base Rate</Typography>
                <Typography>{formatCurrency(baseRate)}</Typography>
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Booking Fee (15%)</Typography>
                <Typography>{formatCurrency(bookingFee)}</Typography>
            </Box>
            
            {addOns > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Add-ons</Typography>
                    <Typography>{formatCurrency(addOns)}</Typography>
                </Box>
            )}
            
            {deliveryFee > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Delivery Fee</Typography>
                    <Typography>{formatCurrency(deliveryFee)}</Typography>
                </Box>
            )}
            
            {discountDetails && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1, color: 'green' }}>
                    <Typography>
                        Discount ({discountDetails.discountCode})
                    </Typography>
                    <Typography>
                        -{formatCurrency(discountDetails.discountAmount)}
                    </Typography>
                </Box>
            )}
            
            <Divider sx={{ my: 1 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
                <Typography variant="h6">Total</Typography>
                <Typography variant="h6">{formatCurrency(total)}</Typography>
            </Box>
        </Box>
    );
};

export default PriceBreakdown; 