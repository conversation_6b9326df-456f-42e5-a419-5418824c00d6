import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Avatar,
  Menu,
  MenuItem,
  IconButton,
  Chip,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  Person as PersonIcon,
  Dashboard as DashboardIcon,
  DirectionsCar as CarIcon,
  BookOnline as BookingIcon,
  Favorite as FavoriteIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  SwitchAccount as SwitchIcon,
  Home as HomeIcon,
  Search as SearchIcon,
  Support as SupportIcon,
  Add as AddIcon
} from '@mui/icons-material';
import LanguageSwitcher from './LanguageSwitcher';
import { useNavigate } from 'react-router-dom';
import { mockAuthService } from '../utils/mockAuthService';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN';
}

export default function Navigation() {
  const [user, setUser] = useState<User | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const checkAuth = async () => {
      const currentUser = await mockAuthService.getCurrentUser();
      setUser(currentUser);
    };
    
    checkAuth();
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    await mockAuthService.signOut();
    setUser(null);
    handleMenuClose();
    navigate('/');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileOpen(false);
    handleMenuClose();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'error';
      case 'PROVIDER': return 'warning';
      case 'CUSTOMER': return 'success';
      default: return 'default';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'Admin';
      case 'PROVIDER': return 'Service Provider';
      case 'CUSTOMER': return 'Customer';
      default: return 'User';
    }
  };

  const menuItems = [
    { text: 'Home', icon: <HomeIcon />, path: '/' },
    { text: 'Find Vehicles', icon: <SearchIcon />, path: '/vehicles' },
    { text: 'List Vehicle', icon: <AddIcon />, path: '/vehicle-listing' },
    { text: 'Support', icon: <SupportIcon />, path: '/help' },
  ];

  const userMenuItems = user?.role === 'PROVIDER' ? [
    { text: 'Provider Dashboard', icon: <DashboardIcon />, path: '/provider-dashboard' },
    { text: 'My Vehicles', icon: <CarIcon />, path: '/provider/vehicles' },
    { text: 'My Bookings', icon: <BookingIcon />, path: '/provider/bookings' },
    { text: 'Switch to User View', icon: <SwitchIcon />, path: '/' },
  ] : [
    { text: 'User Dashboard', icon: <DashboardIcon />, path: '/user-dashboard' },
    { text: 'My Bookings', icon: <BookingIcon />, path: '/my-bookings' },
    { text: 'Saved Vehicles', icon: <FavoriteIcon />, path: '/user/saved' },
  ];

  const commonUserItems = [
    { text: 'Profile', icon: <PersonIcon />, path: '/profile' },
    { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
  ];

  return (
    <>
      <AppBar position="static" sx={{ px: { xs: 1, sm: 2 }, py: { xs: 0.5, sm: 1 } }}>
        <Toolbar sx={{ minHeight: { xs: 48, sm: 64 }, px: 0 }}>
          {/* Mobile menu button */}
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={() => setMobileOpen(true)}
            sx={{ mr: 1, display: { sm: 'none' }, p: 1.5 }}
          >
            <MenuIcon sx={{ fontSize: 28 }} />
          </IconButton>

          {/* Logo/Brand */}
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, cursor: 'pointer', fontSize: { xs: '1.1rem', sm: '1.25rem' }, fontWeight: 'bold', letterSpacing: 1 }}
            onClick={() => navigate('/')}
          >
            RentaHub
          </Typography>

          {/* Desktop Navigation */}
          <Box sx={{ display: { xs: 'none', sm: 'flex' }, gap: 2 }}>
            {menuItems.map((item) => (
              <Button
                key={item.text}
                color="inherit"
                onClick={() => navigate(item.path)}
                sx={{ fontSize: { sm: '1rem', md: '1.1rem' }, py: 1.5 }}
              >
                {item.text}
              </Button>
            ))}
          </Box>

          {/* Language Switcher */}
          <LanguageSwitcher />
          
          {/* User Menu */}
          {user ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip
                label={getRoleText(user.role)}
                color={getRoleColor(user.role) as any}
                size="small"
              />
              <IconButton
                onClick={handleMenuOpen}
                sx={{ color: 'inherit' }}
              >
                <Avatar sx={{ width: 32, height: 32 }}>
                  {user.name?.charAt(0) || user.email.charAt(0)}
                </Avatar>
              </IconButton>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button color="inherit" onClick={() => navigate('/signin')}>
                Login
              </Button>
              <Button 
                variant="contained" 
                color="secondary"
                onClick={() => navigate('/signup')}
              >
                Sign Up
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      {/* User Menu Dropdown */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {userMenuItems.map((item) => (
          <MenuItem key={item.text} onClick={() => handleNavigation(item.path)}>
            <ListItemIcon>{item.icon}</ListItemIcon>
            {item.text}
          </MenuItem>
        ))}
        <Divider />
        {commonUserItems.map((item) => (
          <MenuItem key={item.text} onClick={() => handleNavigation(item.path)}>
            <ListItemIcon>{item.icon}</ListItemIcon>
            {item.text}
          </MenuItem>
        ))}
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon><LogoutIcon /></ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Mobile Drawer */}
      <Drawer
        anchor="left"
        open={mobileOpen}
        onClose={() => setMobileOpen(false)}
        PaperProps={{ sx: { width: 260, pt: 2 } }}
      >
        <Box sx={{ width: 1 }} role="presentation">
          <List>
            {menuItems.map((item) => (
              <ListItem 
                key={item.text} 
                button 
                onClick={() => handleNavigation(item.path)}
                sx={{ py: 1.5 }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>{item.icon}</ListItemIcon>
                <ListItemText primary={item.text} primaryTypographyProps={{ fontSize: '1rem' }} />
              </ListItem>
            ))}
            
            {user && (
              <>
                <Divider />
                {userMenuItems.map((item) => (
                  <ListItem 
                    key={item.text} 
                    button 
                    onClick={() => handleNavigation(item.path)}
                  >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <ListItemText primary={item.text} />
                  </ListItem>
                ))}
                <Divider />
                {commonUserItems.map((item) => (
                  <ListItem 
                    key={item.text} 
                    button 
                    onClick={() => handleNavigation(item.path)}
                  >
                    <ListItemIcon>{item.icon}</ListItemIcon>
                    <ListItemText primary={item.text} />
                  </ListItem>
                ))}
                <Divider />
                <ListItem button onClick={handleLogout}>
                  <ListItemIcon><LogoutIcon /></ListItemIcon>
                  <ListItemText primary="Logout" />
                </ListItem>
              </>
            )}
          </List>
        </Box>
      </Drawer>
    </>
  );
} 