import React, { useState } from 'react';
import { Button } from '@mui/material';
import { Build as AssistanceIcon } from '@mui/icons-material';
import VehicleAssistanceRequest from './VehicleAssistanceRequest';

interface VehicleAssistanceButtonProps {
  bookingId?: string;
  providerId?: string;
  vehicleId?: string;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const VehicleAssistanceButton: React.FC<VehicleAssistanceButtonProps> = ({
  bookingId,
  providerId,
  vehicleId,
  variant = 'contained',
  size = 'medium',
  fullWidth = false
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleRequestCreated = (request: any) => {
    console.log('Vehicle assistance request created:', request);
    // You can add additional logic here, like showing a notification
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        startIcon={<AssistanceIcon />}
        onClick={() => setDialogOpen(true)}
        color="warning"
        sx={{
          fontWeight: 'bold',
          textTransform: 'none'
        }}
      >
        🚗 Request Vehicle Assistance
      </Button>

      <VehicleAssistanceRequest
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        bookingId={bookingId}
        providerId={providerId}
        vehicleId={vehicleId}
        onRequestCreated={handleRequestCreated}
      />
    </>
  );
};

export default VehicleAssistanceButton;
