import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';

interface VehicleAssistanceRequest {
  id: string;
  vehicleId: string;
  customerId: string;
  customerName: string;
  vehicleName: string;
  issue: string;
  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
  status: 'PENDING' | 'IN_PROGRESS' | 'RESOLVED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  description?: string;
  location?: string;
  contactPhone?: string;
}

interface AssistanceStats {
  total: number;
  pending: number;
  inProgress: number;
  resolved: number;
  urgent: number;
}

const VehicleAssistanceManagement: React.FC = () => {
  const [requests, setRequests] = useState<VehicleAssistanceRequest[]>([]);
  const [stats, setStats] = useState<AssistanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<VehicleAssistanceRequest | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [statusFilters, setStatusFilters] = useState<string[]>(['PENDING', 'IN_PROGRESS', 'RESOLVED']);
  const [priorityFilters, setPriorityFilters] = useState<string[]>(['ALL', 'URGENT', 'HIGH', 'MEDIUM', 'LOW']);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [requestsResponse, statsResponse] = await Promise.all([
        apiService.get('/vehicle-assistance/admin/all'),
        apiService.get('/vehicle-assistance/admin/stats')
      ]);

      setRequests(requestsResponse.data || []);
      setStats(statsResponse.data || null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch assistance requests');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (requestId: string, newStatus: string) => {
    try {
      const response = await apiService.put(
        `/vehicle-assistance/admin/${requestId}/status`,
        { status: newStatus }
      );

      if (response.success) {
        setRequests(prev => 
          prev.map(req => 
            req.id === requestId 
              ? { ...req, status: newStatus as any, updatedAt: new Date().toISOString() }
              : req
          )
        );
        fetchData(); // Refresh stats
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update status');
    }
  };

  const handlePriorityUpdate = async (requestId: string, newPriority: string) => {
    try {
      const response = await apiService.put(
        `/vehicle-assistance/admin/${requestId}/priority`,
        { priority: newPriority }
      );

      if (response.success) {
        setRequests(prev => 
          prev.map(req => 
            req.id === requestId 
              ? { ...req, priority: newPriority as any, updatedAt: new Date().toISOString() }
              : req
          )
        );
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update priority');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'warning';
      case 'IN_PROGRESS': return 'info';
      case 'RESOLVED': return 'success';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      case 'LOW': return 'success';
      default: return 'default';
    }
  };

  const filteredRequests = requests.filter(request => 
    statusFilters.includes(request.status)
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Vehicle Assistance Management
      </Typography>

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Requests
                </Typography>
                <Typography variant="h4">
                  {stats.total}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Pending
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {stats.pending}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  In Progress
                </Typography>
                <Typography variant="h4" color="info.main">
                  {stats.inProgress}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Resolved
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.resolved}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Status Filters */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Status
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CANCELLED'].map((status) => (
            <Chip
              key={status}
              label={status}
              color={statusFilters.includes(status) ? 'primary' : 'default'}
              onClick={() => {
                if (statusFilters.includes(status)) {
                  setStatusFilters(prev => prev.filter(s => s !== status));
                } else {
                  setStatusFilters(prev => [...prev, status]);
                }
              }}
              clickable
            />
          ))}
        </Box>
      </Box>

      {/* Requests Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Customer</TableCell>
              <TableCell>Vehicle</TableCell>
              <TableCell>Issue</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRequests.map((request) => (
              <TableRow key={request.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {request.customerName}
                  </Typography>
                  {request.contactPhone && (
                    <Typography variant="caption" color="textSecondary">
                      {request.contactPhone}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {request.vehicleName}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ maxWidth: 200 }}>
                    {request.issue}
                  </Typography>
                  {request.description && (
                    <Typography variant="caption" color="textSecondary">
                      {request.description}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    label={request.priority}
                    color={getPriorityColor(request.priority) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={request.status}
                    color={getStatusColor(request.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="caption">
                    {new Date(request.createdAt).toLocaleDateString()}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedRequest(request);
                          setDialogOpen(true);
                        }}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Update Status">
                      <IconButton
                        size="small"
                        onClick={() => handleStatusUpdate(request.id, 'IN_PROGRESS')}
                        disabled={request.status === 'RESOLVED'}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Detail Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Request Details
        </DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Customer
                  </Typography>
                  <Typography variant="body1">
                    {selectedRequest.customerName}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Vehicle
                  </Typography>
                  <Typography variant="body1">
                    {selectedRequest.vehicleName}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Issue
                  </Typography>
                  <Typography variant="body1">
                    {selectedRequest.issue}
                  </Typography>
                </Grid>
                {selectedRequest.description && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Description
                    </Typography>
                    <Typography variant="body1">
                      {selectedRequest.description}
                    </Typography>
                  </Grid>
                )}
                {selectedRequest.location && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Location
                    </Typography>
                    <Typography variant="body1">
                      {selectedRequest.location}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VehicleAssistanceManagement;
