import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Autocomplete,
  Checkbox,
  ListItemText,
  OutlinedInput,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Slider,
  InputAdornment,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  DirectionsBike as VehicleIcon,
  Person as CustomerIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Email as EmailIcon,
  Repeat as RecurringIcon,
  ViewList as ListIcon,
  ViewModule as GridIcon,
  DragIndicator as DragIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as NotificationIcon,
  ExpandMore as ExpandMoreIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isSameMonth, addMonths, subMonths, startOfWeek, endOfWeek, addDays, isToday, isBefore, isAfter, parseISO, differenceInDays } from 'date-fns';
import { Booking, Vehicle, User, BookingStatus } from '../types';

interface BookingCalendarProps {
  bookings: Booking[];
  vehicles: Vehicle[];
  onBookingUpdate?: (bookingId: string, status: BookingStatus) => void;
  onBookingCreate?: (booking: Partial<Booking>) => void;
  onBookingDelete?: (bookingId: string) => void;
  onBookingReschedule?: (bookingId: string, newStartDate: string, newEndDate: string) => void;
  onExportBookings?: (filters: BookingFilters) => void;
  onImportBookings?: (file: File) => void;
  onSendNotification?: (bookingId: string, type: 'email' | 'sms') => void;
  loading?: boolean;
}

interface CalendarDay {
  date: Date;
  bookings: Booking[];
  isToday: boolean;
  isCurrentMonth: boolean;
}

interface BookingFilters {
  status: BookingStatus[];
  vehicles: string[];
  customers: string[];
  dateRange: [Date | null, Date | null];
  minAmount: number;
  maxAmount: number;
  recurring: boolean;
}

interface RecurringBooking {
  pattern: 'daily' | 'weekly' | 'monthly';
  interval: number;
  endDate: Date;
  daysOfWeek?: number[];
  dayOfMonth?: number;
}

export const BookingCalendar: React.FC<BookingCalendarProps> = ({
  bookings,
  vehicles,
  onBookingUpdate,
  onBookingCreate,
  onBookingDelete,
  onBookingReschedule,
  onExportBookings,
  onImportBookings,
  onSendNotification,
  loading = false
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isCreateBookingModalOpen, setIsCreateBookingModalOpen] = useState(false);
  const [isRecurringBookingModalOpen, setIsRecurringBookingModalOpen] = useState(false);
  const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [view, setView] = useState<'month' | 'week' | 'day' | 'resource'>('month');
  const [showResourceView, setShowResourceView] = useState(false);
  const [draggedBooking, setDraggedBooking] = useState<Booking | null>(null);
  const [filters, setFilters] = useState<BookingFilters>({
    status: [],
    vehicles: [],
    customers: [],
    dateRange: [null, null],
    minAmount: 0,
    maxAmount: 10000,
    recurring: false
  });

  // Filtered bookings based on current filters
  const filteredBookings = useMemo(() => {
    return bookings.filter(booking => {
      if (filters.status.length > 0 && !filters.status.includes(booking.status as BookingStatus)) {
        return false;
      }
      if (filters.vehicles.length > 0 && !filters.vehicles.includes(booking.vehicleId)) {
        return false;
      }
      if (filters.customers.length > 0 && !filters.customers.includes(booking.userId)) {
        return false;
      }
      if (filters.dateRange[0] && isBefore(new Date(booking.startDate), filters.dateRange[0])) {
        return false;
      }
      if (filters.dateRange[1] && isAfter(new Date(booking.endDate), filters.dateRange[1])) {
        return false;
      }
      if (booking.totalAmount < filters.minAmount || booking.totalAmount > filters.maxAmount) {
        return false;
      }
      return true;
    });
  }, [bookings, filters]);

  // Calendar navigation
  const goToPreviousMonth = () => setCurrentDate(subMonths(currentDate, 1));
  const goToNextMonth = () => setCurrentDate(addMonths(currentDate, 1));
  const goToToday = () => setCurrentDate(new Date());

  // Generate calendar days
  const generateCalendarDays = useCallback((): CalendarDay[] => {
    const start = startOfWeek(startOfMonth(currentDate));
    const end = endOfWeek(endOfMonth(currentDate));
    const days = eachDayOfInterval({ start, end });

    return days.map(day => ({
      date: day,
      bookings: filteredBookings.filter(booking => {
        const bookingStart = new Date(booking.startDate);
        const bookingEnd = new Date(booking.endDate);
        return isSameDay(day, bookingStart) || 
               isSameDay(day, bookingEnd) || 
               (isAfter(day, bookingStart) && isBefore(day, bookingEnd));
      }),
      isToday: isToday(day),
      isCurrentMonth: isSameMonth(day, currentDate)
    }));
  }, [currentDate, filteredBookings]);

  const calendarDays = generateCalendarDays();

  // Get status color
  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, booking: Booking) => {
    setDraggedBooking(booking);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetDate: Date) => {
    e.preventDefault();
    if (draggedBooking && onBookingReschedule) {
      const daysDiff = differenceInDays(targetDate, new Date(draggedBooking.startDate));
      const newStartDate = addDays(new Date(draggedBooking.startDate), daysDiff);
      const newEndDate = addDays(new Date(draggedBooking.endDate), daysDiff);
      
      onBookingReschedule(
        draggedBooking.id,
        newStartDate.toISOString(),
        newEndDate.toISOString()
      );
    }
    setDraggedBooking(null);
  };

  // Handle booking selection
  const handleBookingClick = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsBookingModalOpen(true);
  };

  // Handle date selection
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    setIsCreateBookingModalOpen(true);
  };

  // Handle booking actions
  const handleApproveBooking = () => {
    if (selectedBooking && onBookingUpdate) {
      onBookingUpdate(selectedBooking.id, 'confirmed');
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleRejectBooking = () => {
    if (selectedBooking && onBookingUpdate) {
      onBookingUpdate(selectedBooking.id, 'cancelled');
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleDeleteBooking = () => {
    if (selectedBooking && onBookingDelete) {
      onBookingDelete(selectedBooking.id);
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleSendNotification = (type: 'email' | 'sms') => {
    if (selectedBooking && onSendNotification) {
      onSendNotification(selectedBooking.id, type);
    }
  };

  // Export/Import handlers
  const handleExport = () => {
    if (onExportBookings) {
      onExportBookings(filters);
    }
    setIsExportModalOpen(false);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onImportBookings) {
      onImportBookings(file);
    }
  };

  return (
    <Card>
      <CardContent>
        {/* Calendar Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h5" component="h2">
              Booking Calendar
            </Typography>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setIsCreateBookingModalOpen(true)}
            >
              New Booking
            </Button>
            <Button
              variant="outlined"
              startIcon={<RecurringIcon />}
              onClick={() => setIsRecurringBookingModalOpen(true)}
            >
              Recurring
            </Button>
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton onClick={goToPreviousMonth}>
              <CalendarIcon />
            </IconButton>
            <Typography variant="h6">
              {format(currentDate, 'MMMM yyyy')}
            </Typography>
            <IconButton onClick={goToNextMonth}>
              <CalendarIcon />
            </IconButton>
            <Button variant="outlined" size="small" onClick={goToToday}>
              Today
            </Button>
          </Box>
        </Box>

        {/* View Controls */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={view} onChange={(_, newValue) => setView(newValue)}>
            <Tab value="month" label="Month" />
            <Tab value="week" label="Week" />
            <Tab value="day" label="Day" />
            <Tab value="resource" label="Resource" />
          </Tabs>
          
          <Box display="flex" alignItems="center" gap={1}>
            <FormControlLabel
              control={
                <Switch
                  checked={showResourceView}
                  onChange={(e) => setShowResourceView(e.target.checked)}
                />
              }
              label="Resource View"
            />
            <IconButton onClick={() => setIsFiltersModalOpen(true)}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={() => setIsExportModalOpen(true)}>
              <ExportIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Calendar Grid */}
        {loading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {/* Weekday Headers */}
            <Grid container sx={{ mb: 1 }}>
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <Grid item xs key={day}>
                  <Typography variant="subtitle2" align="center" sx={{ fontWeight: 'bold' }}>
                    {day}
                  </Typography>
                </Grid>
              ))}
            </Grid>

            {/* Calendar Days */}
            <Grid container spacing={1}>
              {calendarDays.map((day, index) => (
                <Grid item xs key={index}>
                  <Paper
                    sx={{
                      p: 1,
                      minHeight: 100,
                      cursor: 'pointer',
                      backgroundColor: day.isToday ? 'primary.light' : 'background.paper',
                      opacity: day.isCurrentMonth ? 1 : 0.5,
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                    onClick={() => handleDateClick(day.date)}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, day.date)}
                  >
                    <Typography
                      variant="body2"
                      align="center"
                      sx={{
                        fontWeight: day.isToday ? 'bold' : 'normal',
                        color: day.isToday ? 'white' : 'text.primary'
                      }}
                    >
                      {format(day.date, 'd')}
                    </Typography>
                    
                    {/* Bookings for this day */}
                    <Box mt={1}>
                      {day.bookings.slice(0, 3).map((booking) => (
                        <Tooltip
                          key={booking.id}
                          title={`${booking.vehicle?.name} - ${booking.user?.name}`}
                        >
                          <Chip
                            label={`${booking.vehicle?.name}`}
                            size="small"
                            color={getStatusColor(booking.status) as any}
                            sx={{ 
                              mb: 0.5, 
                              cursor: 'pointer', 
                              fontSize: '0.7rem',
                              opacity: draggedBooking?.id === booking.id ? 0.5 : 1
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleBookingClick(booking);
                            }}
                            draggable
                            onDragStart={(e) => handleDragStart(e, booking)}
                            icon={<DragIcon />}
                          />
                        </Tooltip>
                      ))}
                      {day.bookings.length > 3 && (
                        <Typography variant="caption" color="text.secondary">
                          +{day.bookings.length - 3} more
                        </Typography>
                      )}
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Booking Details Modal */}
        <Dialog open={isBookingModalOpen} onClose={() => setIsBookingModalOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            <Box display="flex" alignItems="center" gap={1}>
              <VehicleIcon />
              Booking Details
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedBooking && (
              <Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      {selectedBooking.vehicle?.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" mb={2}>
                      Customer: {selectedBooking.user?.name}
                    </Typography>
                    <Typography variant="body2" mb={1}>
                      Start: {format(new Date(selectedBooking.startDate), 'PPP')}
                    </Typography>
                    <Typography variant="body2" mb={1}>
                      End: {format(new Date(selectedBooking.endDate), 'PPP')}
                    </Typography>
                    <Typography variant="h6" color="primary" mb={2}>
                      Total: ${selectedBooking.totalAmount}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Chip
                        label={selectedBooking.status}
                        color={getStatusColor(selectedBooking.status) as any}
                        size="medium"
                      />
                    </Box>
                    {selectedBooking.status === 'pending' && (
                      <Alert severity="warning">
                        This booking requires your approval
                      </Alert>
                    )}
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsBookingModalOpen(false)}>Close</Button>
            <Button onClick={() => handleSendNotification('email')} startIcon={<EmailIcon />}>
              Send Email
            </Button>
            {selectedBooking?.status === 'pending' && (
              <>
                <Button onClick={handleRejectBooking} color="error" startIcon={<RejectIcon />}>
                  Reject
                </Button>
                <Button onClick={handleApproveBooking} variant="contained" startIcon={<ApproveIcon />}>
                  Approve
                </Button>
              </>
            )}
            {selectedBooking && (
              <Button onClick={handleDeleteBooking} color="error" startIcon={<DeleteIcon />}>
                Delete
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Create Booking Modal */}
        <Dialog open={isCreateBookingModalOpen} onClose={() => setIsCreateBookingModalOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Create New Booking</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Vehicle</InputLabel>
                  <Select label="Vehicle">
                    {vehicles.map(vehicle => (
                      <MenuItem key={vehicle.id} value={vehicle.id}>
                        {vehicle.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedDate ? format(selectedDate, 'yyyy-MM-dd') : ''}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Customer Name"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsCreateBookingModalOpen(false)}>Cancel</Button>
            <Button variant="contained">Create Booking</Button>
          </DialogActions>
        </Dialog>

        {/* Filters Modal */}
        <Dialog open={isFiltersModalOpen} onClose={() => setIsFiltersModalOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Booking Filters</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    multiple
                    value={filters.status}
                    onChange={(e) => setFilters({...filters, status: e.target.value as BookingStatus[]})}
                    input={<OutlinedInput label="Status" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {Object.values(BookingStatus).map((status) => (
                      <MenuItem key={status} value={status}>
                        <Checkbox checked={filters.status.indexOf(status) > -1} />
                        <ListItemText primary={status} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Vehicles</InputLabel>
                  <Select
                    multiple
                    value={filters.vehicles}
                    onChange={(e) => setFilters({...filters, vehicles: e.target.value as string[]})}
                    input={<OutlinedInput label="Vehicles" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={vehicles.find(v => v.id === value)?.name || value} />
                        ))}
                      </Box>
                    )}
                  >
                    {vehicles.map((vehicle) => (
                      <MenuItem key={vehicle.id} value={vehicle.id}>
                        <Checkbox checked={filters.vehicles.indexOf(vehicle.id) > -1} />
                        <ListItemText primary={vehicle.name} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography gutterBottom>Price Range</Typography>
                <Slider
                  value={[filters.minAmount, filters.maxAmount]}
                  onChange={(_, value) => setFilters({...filters, minAmount: value[0], maxAmount: value[1]})}
                  valueLabelDisplay="auto"
                  min={0}
                  max={10000}
                  step={100}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setFilters({
              status: [],
              vehicles: [],
              customers: [],
              dateRange: [null, null],
              minAmount: 0,
              maxAmount: 10000,
              recurring: false
            })}>
              Clear Filters
            </Button>
            <Button onClick={() => setIsFiltersModalOpen(false)}>Apply</Button>
          </DialogActions>
        </Dialog>

        {/* Export Modal */}
        <Dialog open={isExportModalOpen} onClose={() => setIsExportModalOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Export Bookings</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Export filtered bookings to CSV or Excel format
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  component="label"
                >
                  Import Bookings
                  <input
                    type="file"
                    hidden
                    accept=".csv,.xlsx,.xls"
                    onChange={handleImport}
                  />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsExportModalOpen(false)}>Cancel</Button>
            <Button onClick={handleExport} variant="contained">Export</Button>
          </DialogActions>
        </Dialog>

        {/* Speed Dial for Quick Actions */}
        <SpeedDial
          ariaLabel="Quick actions"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          icon={<SpeedDialIcon />}
        >
          <SpeedDialAction
            icon={<AddIcon />}
            tooltipTitle="New Booking"
            onClick={() => setIsCreateBookingModalOpen(true)}
          />
          <SpeedDialAction
            icon={<FilterIcon />}
            tooltipTitle="Filters"
            onClick={() => setIsFiltersModalOpen(true)}
          />
          <SpeedDialAction
            icon={<ExportIcon />}
            tooltipTitle="Export"
            onClick={() => setIsExportModalOpen(true)}
          />
          <SpeedDialAction
            icon={<NotificationIcon />}
            tooltipTitle="Send Notifications"
            onClick={() => handleSendNotification('email')}
          />
        </SpeedDial>
      </CardContent>
    </Card>
  );
}; 

export default BookingCalendar;