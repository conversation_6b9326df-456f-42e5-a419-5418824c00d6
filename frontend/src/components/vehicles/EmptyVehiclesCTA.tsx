import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  DirectionsBike,
  Add as AddIcon,
  PhotoCamera,
  AttachMoney,
  Star,
  TrendingUp,
  Security,
  Speed,
  Refresh,
} from '@mui/icons-material';

interface EmptyVehiclesCTAProps {
  onAddVehicle?: () => void;
  onRefresh?: () => void;
}

const EmptyVehiclesCTA: React.FC<EmptyVehiclesCTAProps> = ({ 
  onAddVehicle,
  onRefresh 
}) => {
  const vehicleTypes = [
    {
      name: 'Small Scooter',
      description: '50-125cc • Perfect for city commuting',
      earning: '$15-25/day',
      color: '#4caf50',
    },
    {
      name: 'Large Scooter',
      description: '125-300cc • Great for longer rides',
      earning: '$25-40/day',
      color: '#2196f3',
    },
    {
      name: 'Luxury Bike',
      description: '300cc+ • Premium experience',
      earning: '$40-80/day',
      color: '#9c27b0',
    },
  ];

  const listingSteps = [
    {
      icon: PhotoCamera,
      title: 'Upload Photos',
      description: 'Add high-quality photos of your vehicle',
    },
    {
      icon: DirectionsBike,
      title: 'Vehicle Details',
      description: 'Provide make, model, and specifications',
    },
    {
      icon: AttachMoney,
      title: 'Set Pricing',
      description: 'Choose competitive daily and weekly rates',
    },
    {
      icon: Security,
      title: 'Safety Features',
      description: 'Highlight safety and security features',
    },
  ];

  const benefits = [
    'Earn passive income from your unused vehicle',
    'Set your own availability and pricing',
    'Full insurance coverage included',
    'Secure payment processing',
    'Customer screening and verification',
    '24/7 customer support',
  ];

  return (
    <Box>
      {/* Main CTA Section */}
      <Paper
        elevation={2}
        sx={{
          background: 'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)',
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          mb: 4,
        }}
      >
        {/* Icon */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              width: 100,
              height: 100,
              bgcolor: 'primary.main',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              boxShadow: 3,
            }}
          >
            <DirectionsBike sx={{ fontSize: 50, color: 'white' }} />
          </Box>
        </Box>

        {/* Content */}
        <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          List Your First Vehicle
        </Typography>

        <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
          Start earning from your motorcycle or scooter today
        </Typography>

        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: '600px', mx: 'auto' }}>
          Turn your idle vehicle into a source of income. List your motorcycle or scooter on RentaHub 
          and start earning money while helping others explore the city.
        </Typography>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            onClick={onAddVehicle}
            variant="contained"
            size="large"
            startIcon={<AddIcon />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
            }}
          >
            Add Your Vehicle
          </Button>

          <Button
            onClick={onRefresh}
            variant="outlined"
            size="large"
            startIcon={<Refresh />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
              borderWidth: 2,
            }}
          >
            Refresh List
          </Button>
        </Box>

        {/* Earning Potential */}
        <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            💰 <strong>Earning Potential:</strong> Providers earn an average of $500-1,500 per month
          </Typography>
        </Box>
      </Paper>

      {/* Vehicle Types */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
          What Can You List?
        </Typography>
        
        <Grid container spacing={3}>
          {vehicleTypes.map((type, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: type.color,
                      width: 60,
                      height: 60,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <DirectionsBike sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {type.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {type.description}
                  </Typography>
                  <Chip 
                    label={type.earning}
                    color="primary"
                    variant="outlined"
                    icon={<TrendingUp />}
                  />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Listing Process */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
          Easy 4-Step Listing Process
        </Typography>
        
        <Grid container spacing={3}>
          {listingSteps.map((step, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ height: '100%', textAlign: 'center' }}>
                <CardContent sx={{ p: 3 }}>
                  <Box
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      bgcolor: 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <step.icon />
                  </Box>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {step.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {step.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Benefits */}
      <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          Why List on RentaHub?
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <List dense>
              {benefits.slice(0, Math.ceil(benefits.length / 2)).map((benefit, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'success.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px',
                      }}
                    >
                      ✓
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={benefit} />
                </ListItem>
              ))}
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <List dense>
              {benefits.slice(Math.ceil(benefits.length / 2)).map((benefit, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'success.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px',
                      }}
                    >
                      ✓
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={benefit} />
                </ListItem>
              ))}
            </List>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body2" color="text.secondary">
            🚀 <strong>Get Started:</strong> Most providers complete their first listing in under 10 minutes
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default EmptyVehiclesCTA;
