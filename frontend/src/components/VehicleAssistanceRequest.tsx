import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Grid
} from '@mui/material';
import {
  Build as BuildIcon,
  CarCrash as AccidentIcon,
  LocalGasStation as FuelIcon,
  BatteryAlert as BatteryIcon,
  Key as KeyIcon,
  Engineering as MechanicalIcon,
  ElectricalServices as ElectricalIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { apiService } from '../services/apiService';

interface VehicleAssistanceRequestProps {
  open: boolean;
  onClose: () => void;
  bookingId?: string;
  providerId?: string;
  vehicleId?: string;
  onRequestCreated?: (request: any) => void;
}

const getAssistanceCategories = (t: any) => [
  { value: 'BREAKDOWN', label: t('assistance:categories.breakdown'), icon: <BuildIcon />, priority: 'HIGH' },
  { value: 'ACCIDENT', label: t('assistance:categories.accident'), icon: <AccidentIcon />, priority: 'URGENT' },
  { value: 'FLAT_TIRE', label: t('assistance:categories.flatTire'), icon: <BuildIcon />, priority: 'MEDIUM' },
  { value: 'FUEL_EMPTY', label: t('assistance:categories.fuelEmpty'), icon: <FuelIcon />, priority: 'MEDIUM' },
  { value: 'BATTERY_DEAD', label: t('assistance:categories.batteryDead'), icon: <BatteryIcon />, priority: 'MEDIUM' },
  { value: 'KEY_LOCKED', label: t('assistance:categories.keyLocked'), icon: <KeyIcon />, priority: 'LOW' },
  { value: 'MECHANICAL_ISSUE', label: t('assistance:categories.mechanicalIssue'), icon: <MechanicalIcon />, priority: 'HIGH' },
  { value: 'ELECTRICAL_ISSUE', label: t('assistance:categories.electricalIssue'), icon: <ElectricalIcon />, priority: 'HIGH' },
  { value: 'OTHER', label: t('assistance:categories.other'), icon: <HelpIcon />, priority: 'LOW' }
];

const PRIORITY_COLORS = {
  URGENT: 'error',
  HIGH: 'warning',
  MEDIUM: 'info',
  LOW: 'default'
} as const;

const VehicleAssistanceRequest: React.FC<VehicleAssistanceRequestProps> = ({
  open,
  onClose,
  bookingId,
  providerId,
  vehicleId,
  onRequestCreated
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    category: '',
    reason: '',
    message: '',
    location: '',
    priority: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const ASSISTANCE_CATEGORIES = getAssistanceCategories(t);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-set priority based on category
    if (field === 'category') {
      const category = ASSISTANCE_CATEGORIES.find(cat => cat.value === value);
      if (category) {
        setFormData(prev => ({
          ...prev,
          priority: category.priority
        }));
      }
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.category || !formData.reason) {
        setError('Please select a category and provide a reason for assistance');
        return;
      }

      const requestData = {
        bookingId,
        providerId,
        vehicleId,
        category: formData.category,
        reason: formData.reason,
        message: formData.message,
        location: formData.location,
        priority: formData.priority
      };

      const response = await apiService.post('/vehicle-assistance', requestData);

      if (response.success) {
        setSuccess(true);
        onRequestCreated?.(response.data);
        
        // Close dialog after 2 seconds
        setTimeout(() => {
          onClose();
          setSuccess(false);
          setFormData({
            category: '',
            reason: '',
            message: '',
            location: '',
            priority: ''
          });
        }, 2000);
      } else {
        setError(response.error || 'Failed to create assistance request');
      }
    } catch (error) {
      console.error('Error creating assistance request:', error);
      setError('Failed to create assistance request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const selectedCategory = ASSISTANCE_CATEGORIES.find(cat => cat.value === formData.category);

  if (success) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Box textAlign="center" py={3}>
            <Typography variant="h5" color="success.main" gutterBottom>
              ✅ Assistance Request Sent!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Our team has been notified and will contact you shortly.
            </Typography>
            {selectedCategory && (
              <Box mt={2}>
                <Chip
                  icon={selectedCategory.icon}
                  label={`${selectedCategory.label} - ${formData.priority} Priority`}
                  color={PRIORITY_COLORS[formData.priority as keyof typeof PRIORITY_COLORS]}
                  variant="outlined"
                />
              </Box>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h5" component="div">
          🚗 Request Vehicle Assistance
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Get help with your vehicle issue. Our team will respond based on priority level.
        </Typography>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Category Selection */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              What type of assistance do you need?
            </Typography>
            <Grid container spacing={2}>
              {ASSISTANCE_CATEGORIES.map((category) => (
                <Grid item xs={12} sm={6} md={4} key={category.value}>
                  <Button
                    variant={formData.category === category.value ? 'contained' : 'outlined'}
                    fullWidth
                    startIcon={category.icon}
                    onClick={() => handleInputChange('category', category.value)}
                    sx={{ 
                      height: 80, 
                      flexDirection: 'column',
                      gap: 1,
                      textTransform: 'none'
                    }}
                  >
                    <Typography variant="body2" fontWeight="bold">
                      {category.label}
                    </Typography>
                    <Chip
                      label={category.priority}
                      size="small"
                      color={PRIORITY_COLORS[category.priority as keyof typeof PRIORITY_COLORS]}
                    />
                  </Button>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* Reason */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Describe the issue *"
              multiline
              rows={3}
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              placeholder="Please provide details about what happened..."
              required
            />
          </Grid>

          {/* Additional Message */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Additional Information"
              multiline
              rows={2}
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Any additional details that might help..."
            />
          </Grid>

          {/* Location */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Current Location"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="Street address, landmark, or GPS coordinates..."
              helperText="Help us find you faster by providing your exact location"
            />
          </Grid>

          {/* Priority Override */}
          {formData.category && (
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Priority Level</InputLabel>
                <Select
                  value={formData.priority}
                  label="Priority Level"
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                >
                  <MenuItem value="LOW">
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label="LOW" size="small" color="default" />
                      <Typography>2-4 hours response</Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="MEDIUM">
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label="MEDIUM" size="small" color="info" />
                      <Typography>1-2 hours response</Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="HIGH">
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label="HIGH" size="small" color="warning" />
                      <Typography>30-60 minutes response</Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="URGENT">
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label="URGENT" size="small" color="error" />
                      <Typography>15-30 minutes response</Typography>
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.category || !formData.reason}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Sending Request...' : 'Request Assistance'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VehicleAssistanceRequest;
