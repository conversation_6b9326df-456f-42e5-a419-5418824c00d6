import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Checkbox, 
  FormControlLabel, 
  Slider, 
  Typography, 
  Select, 
  MenuItem, 
  FormGroup 
} from '@mui/material';
import axios from 'axios';

interface VehicleFilterProps {
  onFilterChange: (filters: {
    type?: string;
    minPrice?: number;
    maxPrice?: number;
    insurance?: boolean;
    addOns?: string[];
  }) => void;
}

export const VehicleFilter: React.FC<VehicleFilterProps> = ({ onFilterChange }) => {
  const [vehicleTypes, setVehicleTypes] = useState<string[]>([]);
  const [addOns, setAddOns] = useState<string[]>([]);
  
  const [selectedType, setSelectedType] = useState<string>('');
  const [priceRange, setPriceRange] = useState<number[]>([0, 500]);
  const [insuranceIncluded, setInsuranceIncluded] = useState<boolean>(false);
  const [selectedAddOns, setSelectedAddOns] = useState<string[]>([]);

  useEffect(() => {
    // Fetch vehicle types and add-ons
    const fetchFilterOptions = async () => {
      try {
        const [typesResponse, addOnsResponse] = await Promise.all([
          axios.get('/api/vehicle-listing/categories'),
          axios.get('/api/vehicle-listing/types')
        ]);
        
        setVehicleTypes(typesResponse.data.data || []);
        setAddOns(addOnsResponse.data.data || []);
      } catch (error) {
        console.error('Failed to fetch filter options', error);
      }
    };

    fetchFilterOptions();
  }, []);

  useEffect(() => {
    // Trigger filter change when any filter changes
    onFilterChange({
      type: selectedType || undefined,
      minPrice: priceRange[0],
      maxPrice: priceRange[1],
      insurance: insuranceIncluded,
      addOns: selectedAddOns.length > 0 ? selectedAddOns : undefined
    });
  }, [selectedType, priceRange, insuranceIncluded, selectedAddOns]);

  const handleAddOnChange = (addOn: string) => {
    setSelectedAddOns(prev => 
      prev.includes(addOn) 
        ? prev.filter(a => a !== addOn)
        : [...prev, addOn]
    );
  };

  return (
    <Box sx={{ p: 2, border: '1px solid #ddd', borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Filter Vehicles
      </Typography>

      {/* Vehicle Type */}
      <Typography variant="subtitle1">Vehicle Type</Typography>
      <Select
        fullWidth
        value={selectedType}
        onChange={(e) => setSelectedType(e.target.value as string)}
        displayEmpty
      >
        <MenuItem value="">All Types</MenuItem>
        {vehicleTypes.map(type => (
          <MenuItem key={type} value={type}>{type}</MenuItem>
        ))}
      </Select>

      {/* Price Range */}
      <Typography variant="subtitle1" sx={{ mt: 2 }}>Price Range</Typography>
      <Slider
        value={priceRange}
        onChange={(_, newValue) => setPriceRange(newValue as number[])}
        valueLabelDisplay="auto"
        min={0}
        max={500}
        step={10}
      />

      {/* Insurance */}
      <FormControlLabel
        control={
          <Checkbox
            checked={insuranceIncluded}
            onChange={(e) => setInsuranceIncluded(e.target.checked)}
          />
        }
        label="Insurance Included"
      />

      {/* Add-ons */}
      <Typography variant="subtitle1" sx={{ mt: 2 }}>Add-ons</Typography>
      <FormGroup>
        {addOns.map(addOn => (
          <FormControlLabel
            key={addOn}
            control={
              <Checkbox
                checked={selectedAddOns.includes(addOn)}
                onChange={() => handleAddOnChange(addOn)}
              />
            }
            label={addOn}
          />
        ))}
      </FormGroup>
    </Box>
  );
};

export default VehicleFilter;
