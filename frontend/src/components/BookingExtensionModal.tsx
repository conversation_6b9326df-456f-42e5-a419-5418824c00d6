import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  CalendarToday,
  AccessTime,
  AttachMoney,
  Close,
  Schedule,
  CheckCircle
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { addDays, differenceInDays, format } from 'date-fns';

interface BookingExtensionModalProps {
  open: boolean;
  onClose: () => void;
  currentEndDate: Date;
  vehicleId?: string;
  bookingId?: string;
  dailyRate?: number;
}

const BookingExtensionModal: React.FC<BookingExtensionModalProps> = ({
  open,
  onClose,
  currentEndDate,
  vehicleId,
  bookingId,
  dailyRate = 0
}) => {
  const [newEndDate, setNewEndDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (!newEndDate || newEndDate <= currentEndDate) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API call to extend booking
      // const response = await BookingService.extendBooking({
      //   bookingId,
      //   newEndDate,
      //   vehicleId
      // });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubmitted(true);
      setTimeout(() => {
        onClose();
        setSubmitted(false);
        setNewEndDate(null);
      }, 2000);
    } catch (error) {
      console.error('Error extending booking:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      setNewEndDate(null);
      setSubmitted(false);
    }
  };

  const getExtensionDays = () => {
    if (!newEndDate || newEndDate <= currentEndDate) return 0;
    return differenceInDays(newEndDate, currentEndDate);
  };

  const getExtensionCost = () => {
    const days = getExtensionDays();
    return days * dailyRate;
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Schedule sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">Extend Booking</Typography>
          </Box>
          <Button onClick={handleClose} disabled={loading}>
            <Close />
          </Button>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {submitted ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            Your booking has been extended successfully!
          </Alert>
        ) : (
          <>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Extend your rental period. Select a new end date below.
            </Typography>

            {/* Current Booking Info */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Current Booking
                </Typography>
                <Box display="flex" alignItems="center" sx={{ mb: 1 }}>
                  <CalendarToday sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    Ends: {format(currentEndDate, 'PPP')}
                  </Typography>
                </Box>
                <Box display="flex" alignItems="center">
                  <AttachMoney sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    Daily Rate: ${dailyRate}
                  </Typography>
                </Box>
              </CardContent>
            </Card>

            <Divider sx={{ my: 2 }} />

            {/* Extension Form */}
            <Typography variant="h6" gutterBottom>
              New End Date
            </Typography>

            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Select New End Date"
                value={newEndDate}
                onChange={(date) => setNewEndDate(date)}
                minDate={addDays(currentEndDate, 1)}
                maxDate={addDays(currentEndDate, 30)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    sx: { mb: 2 }
                  }
                }}
              />
            </LocalizationProvider>

            {/* Extension Summary */}
            {newEndDate && newEndDate > currentEndDate && (
              <Card sx={{ mt: 2, bgcolor: 'primary.50' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Extension Summary
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Additional Days
                      </Typography>
                      <Typography variant="h6">
                        {getExtensionDays()} days
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Additional Cost
                      </Typography>
                      <Typography variant="h6" color="primary">
                        ${getExtensionCost().toFixed(2)}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        {!submitted && (
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !newEndDate || newEndDate <= currentEndDate}
            startIcon={loading ? <CircularProgress size={20} /> : <CheckCircle />}
          >
            {loading ? 'Processing...' : 'Extend Booking'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default BookingExtensionModal; 