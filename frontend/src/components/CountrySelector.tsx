import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Tooltip
} from '@mui/material';
import {
  Public as PublicIcon
} from '@mui/icons-material';

interface Country {
  code: string;
  name: string;
  flag: string;
}

// List of countries supported by RentaHub
const countries: Country[] = [
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬' },
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭' },
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' }
];

// Country storage key
const COUNTRY_STORAGE_KEY = 'rentahub-country';

export const CountryContext = React.createContext<{
  country: Country;
  setCountry: (country: Country) => void;
}>({
  country: countries[0], // Default to Indonesia
  setCountry: () => {},
});

// Hook for components to access the current country
export const useCountry = () => React.useContext(CountryContext);

export const CountryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [country, setCountry] = useState<Country>(() => {
    // Try to get country from localStorage
    const savedCountry = localStorage.getItem(COUNTRY_STORAGE_KEY);
    if (savedCountry) {
      try {
        return JSON.parse(savedCountry);
      } catch (e) {
        console.error('Failed to parse saved country', e);
      }
    }
    return countries[0]; // Default to Indonesia
  });

  // Save country to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(COUNTRY_STORAGE_KEY, JSON.stringify(country));
    
    // Dispatch event so other components can update without context
    window.dispatchEvent(new CustomEvent('country-changed', { 
      detail: country 
    }));
  }, [country]);

  return (
    <CountryContext.Provider value={{ country, setCountry }}>
      {children}
    </CountryContext.Provider>
  );
};

const CountrySelector: React.FC<{
  iconColor?: string;
  displayType?: 'icon' | 'text' | 'flag' | 'flag-text';
}> = ({ 
  iconColor = 'inherit',
  displayType = 'flag-text'
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { country, setCountry } = useCountry();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCountryChange = (newCountry: Country) => {
    setCountry(newCountry);
    handleClose();
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Tooltip title="Change Country">
        <IconButton
          onClick={handleClick}
          sx={{
            color: iconColor,
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}
        >
          {displayType === 'icon' && <PublicIcon />}
          {displayType === 'text' && (
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {country.code}
            </Typography>
          )}
          {displayType === 'flag' && (
            <Typography variant="body1">{country.flag}</Typography>
          )}
          {displayType === 'flag-text' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography variant="body1">{country.flag}</Typography>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                {country.name}
              </Typography>
            </Box>
          )}
        </IconButton>
      </Tooltip>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 180,
            '& .MuiMenuItem-root': {
              py: 1
            }
          }
        }}
      >
        <MenuItem disabled>
          <ListItemIcon>
            <PublicIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            <Typography variant="subtitle2" color="text.secondary">
              Select Country
            </Typography>
          </ListItemText>
        </MenuItem>
        
        {countries.map((ctry) => (
          <MenuItem
            key={ctry.code}
            onClick={() => handleCountryChange(ctry)}
            selected={country.code === ctry.code}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'primary.light',
                '&:hover': {
                  backgroundColor: 'primary.light'
                }
              }
            }}
          >
            <ListItemIcon>
              <Typography variant="h6">{ctry.flag}</Typography>
            </ListItemIcon>
            <ListItemText>
              <Typography variant="body2">
                {ctry.name}
              </Typography>
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default CountrySelector; 