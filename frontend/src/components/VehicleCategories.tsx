import React, { useState, useEffect } from 'react'
import { Box, Grid, Card, CardContent, Typography, Chip, CircularProgress, Alert } from '@mui/material'
import { styled } from '@mui/material/styles'
import { getPriceRangesByCategory, formatPriceRange, getCategoryStats, PriceRangesResponse } from '../services/PriceRangeService'

const CategoryCard = styled(Card)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  borderRadius: '16px',
  border: '2px solid transparent',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    borderColor: '#2BB673',
  },
  '&.selected': {
    borderColor: '#2BB673',
    backgroundColor: '#2BB673',
    color: 'white',
    '& .category-icon': {
      filter: 'brightness(0) invert(1)',
    },
  },
}))

const CategoryIcon = styled('div')({
  fontSize: '48px',
  marginBottom: '16px',
  textAlign: 'center',
})

interface VehicleCategoriesProps {
  selectedCategory?: string
  onCategorySelect?: (category: string) => void
}

const staticCategories = [
  {
    id: 'small_scooter',
    name: 'Small Scooters',
    description: '125cc and under',
    icon: '🛴',
    features: ['Perfect for city rides', 'Fuel efficient', 'Easy to park'],
  },
  {
    id: 'large_scooter',
    name: 'Large Scooters',
    description: '150cc and above',
    icon: '🛵',
    features: ['Highway capable', 'Comfortable for two', 'Storage space'],
  },
  {
    id: 'luxury_bike',
    name: 'Luxury Bikes',
    description: 'Premium motorcycles',
    icon: '🏍️',
    features: ['High performance', 'Premium features', 'Sport & touring'],
  },
]

export const VehicleCategories: React.FC<VehicleCategoriesProps> = ({
  selectedCategory,
  onCategorySelect = () => {},
}) => {
  const [priceRanges, setPriceRanges] = useState<PriceRangesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dynamic price ranges on component mount and refresh periodically
  useEffect(() => {
    const fetchPriceRanges = async () => {
      try {
        setLoading(true);
        setError(null);
        const ranges = await getPriceRangesByCategory();
        setPriceRanges(ranges);
        console.log('✅ Dynamic price ranges loaded:', ranges);
      } catch (err) {
        console.error('❌ Failed to load price ranges:', err);
        setError('Failed to load current pricing');
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchPriceRanges();

    // Refresh every 5 minutes to get updated pricing
    const interval = setInterval(fetchPriceRanges, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  // Combine static category data with dynamic pricing
  const categories = staticCategories.map(category => ({
    ...category,
    priceRange: priceRanges
      ? formatPriceRange(priceRanges[category.id as keyof PriceRangesResponse])
      : 'Loading...',
    stats: priceRanges
      ? getCategoryStats(priceRanges[category.id as keyof PriceRangesResponse])
      : ''
  }));

  return (
    <Box sx={{ py: 4 }}>
      <Typography
        variant="h4"
        component="h2"
        sx={{
          textAlign: 'center',
          mb: 4,
          fontWeight: 'bold',
          color: '#2C2C2C',
        }}
      >
        Choose Your Ride
      </Typography>

      {error && (
        <Alert severity="warning" sx={{ mb: 3, maxWidth: 600, mx: 'auto' }}>
          {error} - Showing estimated ranges
        </Alert>
      )}
      
      <Grid container spacing={3} justifyContent="center">
        {categories.map((category) => (
          <Grid item xs={12} sm={6} md={4} key={category.id}>
            <CategoryCard
              className={selectedCategory === category.id ? 'selected' : ''}
              onClick={() => onCategorySelect(category.id)}
            >
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : (
                  <>
                    <Box
                      className="category-icon"
                      sx={{
                        fontSize: '48px',
                        marginBottom: '16px',
                        textAlign: 'center',
                        filter: selectedCategory === category.id ? 'brightness(0) invert(1)' : 'none',
                      }}
                    >
                      {category.icon}
                    </Box>

                    <Typography
                      variant="h5"
                      component="h3"
                      sx={{ fontWeight: 'bold', mb: 1 }}
                    >
                      {category.name}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{ mb: 2, opacity: 0.8 }}
                    >
                      {category.description}
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      {category.features.map((feature, index) => (
                        <Chip
                          key={index}
                          label={feature}
                          size="small"
                          sx={{
                            m: 0.25,
                            backgroundColor: selectedCategory === category.id
                              ? 'rgba(255,255,255,0.2)'
                              : '#F6D6A7',
                            color: selectedCategory === category.id
                              ? 'white'
                              : '#2C2C2C',
                          }}
                        />
                      ))}
                    </Box>

                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 'bold',
                        color: selectedCategory === category.id ? 'white' : '#2BB673',
                        mb: 1
                      }}
                    >
                      {category.priceRange}
                    </Typography>

                    {category.stats && (
                      <Typography
                        variant="caption"
                        sx={{
                          color: selectedCategory === category.id ? 'rgba(255,255,255,0.8)' : 'text.secondary',
                          display: 'block'
                        }}
                      >
                        {category.stats}
                      </Typography>
                    )}
                  </>
                )}
              </CardContent>
            </CategoryCard>
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default VehicleCategories