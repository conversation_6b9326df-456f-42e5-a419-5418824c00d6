import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Alert,
  Tabs,
  Tab,
  Fab,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  LinearProgress,
  Switch,
  FormControlLabel,
  Autocomplete,
  Rating,
  Pagination
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PhotoCamera as PhotoIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  VisibilityOff as HideIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as AnalyticsIcon,
  FilterList as FilterIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import * as VehicleService from '../services/VehicleService';
import { Vehicle, VehicleCategory, VehicleStatus, VehicleFeature, AddOn, Location } from '../types';

interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  category: VehicleCategory | '';
  engineSize: number;
  transmission: 'automatic' | 'manual';
  fuelType: 'petrol' | 'electric';
  description: string;
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  securityDeposit: number;
  minimumRentalDays: number;
  maximumRentalDays: number;
  deliveryAvailable: boolean;
  deliveryFee: number;
  deliveryRadius: number;
  quantity: number;
  features: string[];
  addOns: string[];
  location: string;
  pickupInstructions: string;
  status: VehicleStatus;
}

interface VehicleFilters {
  category?: VehicleCategory;
  status?: VehicleStatus;
  minRate?: number;
  maxRate?: number;
  search?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vehicle-tabpanel-${index}`}
      aria-labelledby={`vehicle-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export const VehicleManagement: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);

  // Form state
  const [formData, setFormData] = useState<VehicleFormData>({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    category: '',
    engineSize: 150,
    transmission: 'automatic',
    fuelType: 'petrol',
    description: '',
    dailyRate: 0,
    weeklyRate: 0,
    monthlyRate: 0,
    securityDeposit: 0,
    minimumRentalDays: 1,
    maximumRentalDays: 30,
    deliveryAvailable: false,
    deliveryFee: 0,
    deliveryRadius: 0,
    quantity: 1,
    features: [],
    addOns: [],
    location: '',
    pickupInstructions: '',
    status: VehicleStatus.Available
  });

  // Filter state
  const [filters, setFilters] = useState<VehicleFilters>({});
  const [page, setPage] = useState(1);
  const [pageSize] = useState(12);

  // Image upload state
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  useEffect(() => {
    fetchVehicles();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [vehicles, filters]);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      const data = await VehicleService.getProviderVehicles(user?.id || '', 1, 100);
      setVehicles(data);
      setError(null);
    } catch (error) {
      setError('Failed to fetch vehicles. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = vehicles;

    if (filters.category) {
      filtered = filtered.filter(v => v.category === filters.category);
    }

    if (filters.status) {
      filtered = filtered.filter(v => v.status === filters.status);
    }

    if (filters.minRate) {
      filtered = filtered.filter(v => v.dailyRate >= filters.minRate!);
    }

    if (filters.maxRate) {
      filtered = filtered.filter(v => v.dailyRate <= filters.maxRate!);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(v => 
        v.make.toLowerCase().includes(searchTerm) ||
        v.model.toLowerCase().includes(searchTerm) ||
        v.description.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredVehicles(filtered);
  };

  const handleCreateVehicle = async () => {
    try {
      setLoading(true);
      
      // Upload images first
      const imageUrls = await uploadImages();
      
      const vehicleData = {
        ...formData,
        providerId: user?.id || '',
        images: imageUrls,
        category: formData.category as VehicleCategory,
        location: {
          id: '',
          name: formData.location,
          address: formData.location,
          city: '',
          state: '',
          country: '',
          postalCode: '',
          latitude: 0,
          longitude: 0
        },
        features: [],
        addOns: [],
        active: true,
        availableQuantity: formData.quantity,
        yearlyRate: formData.monthlyRate * 10
      };

      await VehicleService.createVehicle(vehicleData);
      
      setIsCreateModalOpen(false);
      resetForm();
      fetchVehicles();
    } catch (error) {
      setError('Failed to create vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateVehicle = async () => {
    if (!selectedVehicle) return;

    try {
      setLoading(true);
      
      const { features, addOns, location, ...updateData } = formData;
      const vehicleData = {
        ...updateData,
        category: formData.category as VehicleCategory,
        images: [...selectedVehicle.images, ...(await uploadImages())],
        location: {
          ...selectedVehicle.location,
          address: formData.location
        }
      };

      await VehicleService.updateVehicle(selectedVehicle.id, vehicleData);
      
      setIsEditModalOpen(false);
      setSelectedVehicle(null);
      resetForm();
      fetchVehicles();
    } catch (error) {
      setError('Failed to update vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVehicle = async (vehicleId: string) => {
    if (!window.confirm('Are you sure you want to delete this vehicle?')) {
      return;
    }

    try {
      setLoading(true);
      await VehicleService.deleteVehicle(vehicleId);
      fetchVehicles();
    } catch (error) {
      setError('Failed to delete vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (vehicleId: string, status: VehicleStatus) => {
    try {
      await VehicleService.updateVehicleStatus(vehicleId, status);
      fetchVehicles();
    } catch (error) {
      setError('Failed to update vehicle status.');
    }
  };

  const uploadImages = async (): Promise<string[]> => {
    if (uploadedImages.length === 0) return [];

    // Mock upload implementation
    const imageUrls: string[] = [];
    
    for (let i = 0; i < uploadedImages.length; i++) {
      const file = uploadedImages[i];
      
      // Simulate upload progress
      setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
      
      for (let progress = 0; progress <= 100; progress += 10) {
        setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Mock URL generation
      imageUrls.push(URL.createObjectURL(file));
    }
    
    setUploadedImages([]);
    setUploadProgress({});
    
    return imageUrls;
  };

  const resetForm = () => {
    setFormData({
      make: '',
      model: '',
      year: new Date().getFullYear(),
      category: '',
      engineSize: 150,
      transmission: 'automatic',
      fuelType: 'petrol',
      description: '',
      dailyRate: 0,
      weeklyRate: 0,
      monthlyRate: 0,
      securityDeposit: 0,
      minimumRentalDays: 1,
      maximumRentalDays: 30,
      deliveryAvailable: false,
      deliveryFee: 0,
      deliveryRadius: 0,
      quantity: 1,
      features: [],
      addOns: [],
      location: '',
      pickupInstructions: '',
      status: VehicleStatus.Available
    });
    setUploadedImages([]);
  };

  const openEditModal = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setFormData({
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      category: vehicle.category,
      engineSize: vehicle.engineSize,
      transmission: vehicle.transmission,
      fuelType: vehicle.fuelType,
      description: vehicle.description,
      dailyRate: vehicle.dailyRate,
      weeklyRate: vehicle.weeklyRate,
      monthlyRate: vehicle.monthlyRate,
      securityDeposit: vehicle.securityDeposit,
      minimumRentalDays: vehicle.minimumRentalDays,
      maximumRentalDays: vehicle.maximumRentalDays,
      deliveryAvailable: vehicle.deliveryAvailable,
      deliveryFee: vehicle.deliveryFee,
      deliveryRadius: vehicle.deliveryRadius,
      quantity: vehicle.quantity,
      features: [],
      addOns: [],
      location: vehicle.location.address,
      pickupInstructions: vehicle.pickupInstructions || '',
      status: vehicle.status
    });
    setIsEditModalOpen(true);
  };

  const getStatusColor = (status: VehicleStatus) => {
    switch (status) {
      case VehicleStatus.Available:
        return 'success';
      case VehicleStatus.Rented:
        return 'info';
      case VehicleStatus.Maintenance:
        return 'warning';
      case VehicleStatus.Unavailable:
        return 'error';
      default:
        return 'default';
    }
  };

  const renderVehicleCard = (vehicle: Vehicle) => (
    <Card key={vehicle.id} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardMedia
        component="img"
        height="200"
        image={vehicle.images[0] || '/placeholder-vehicle.jpg'}
        alt={`${vehicle.make} ${vehicle.model}`}
        sx={{ objectFit: 'cover' }}
      />
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="start" mb={1}>
          <Typography variant="h6" component="h3">
            {vehicle.make} {vehicle.model}
          </Typography>
          <Chip
            label={vehicle.status}
            color={getStatusColor(vehicle.status)}
            size="small"
          />
        </Box>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {vehicle.year} • {vehicle.category} • {vehicle.engineSize}cc
        </Typography>
        
        <Typography variant="body2" sx={{ mb: 2 }}>
          {vehicle.description.length > 100 
            ? `${vehicle.description.substring(0, 100)}...` 
            : vehicle.description
          }
        </Typography>
        
        <Box display="flex" alignItems="center" mb={1}>
          <LocationIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
          <Typography variant="body2" color="text.secondary">
            {vehicle.location.address}
          </Typography>
        </Box>
        
        <Typography variant="h6" color="primary">
          ${vehicle.dailyRate}/day
        </Typography>
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
        <Box>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedVehicle(vehicle);
              setIsViewModalOpen(true);
            }}
          >
            <ViewIcon />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => openEditModal(vehicle)}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDeleteVehicle(vehicle.id)}
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Box>
        
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <Select
            value={vehicle.status}
            onChange={(e) => handleStatusChange(vehicle.id, e.target.value as VehicleStatus)}
            displayEmpty
          >
            <MenuItem value={VehicleStatus.Available}>Available</MenuItem>
            <MenuItem value={VehicleStatus.Rented}>Rented</MenuItem>
            <MenuItem value={VehicleStatus.Maintenance}>Maintenance</MenuItem>
            <MenuItem value={VehicleStatus.Unavailable}>Unavailable</MenuItem>
          </Select>
        </FormControl>
      </CardActions>
    </Card>
  );

  const renderVehicleForm = () => (
    <Grid container spacing={3}>
      {/* Basic Information */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Basic Information
        </Typography>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Make *"
          value={formData.make}
          onChange={(e) => setFormData({ ...formData, make: e.target.value })}
          required
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Model *"
          value={formData.model}
          onChange={(e) => setFormData({ ...formData, model: e.target.value })}
          required
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Year *"
          type="number"
          value={formData.year}
          onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
          required
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth required>
          <InputLabel>Category</InputLabel>
          <Select
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value as VehicleCategory })}
          >
            {Object.values(VehicleCategory).map((category) => (
              <MenuItem key={category} value={category}>
                {category.replace('_', ' ').toUpperCase()}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Engine Size (cc)"
          type="number"
          value={formData.engineSize}
          onChange={(e) => setFormData({ ...formData, engineSize: parseInt(e.target.value) })}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Transmission</InputLabel>
          <Select
            value={formData.transmission}
            onChange={(e) => setFormData({ ...formData, transmission: e.target.value as 'automatic' | 'manual' })}
          >
            <MenuItem value="automatic">Automatic</MenuItem>
            <MenuItem value="manual">Manual</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Fuel Type</InputLabel>
          <Select
            value={formData.fuelType}
            onChange={(e) => setFormData({ ...formData, fuelType: e.target.value as 'petrol' | 'electric' })}
          >
            <MenuItem value="petrol">Petrol</MenuItem>
            <MenuItem value="electric">Electric</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Quantity"
          type="number"
          value={formData.quantity}
          onChange={(e) => setFormData({ ...formData, quantity: parseInt(e.target.value) })}
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Describe your vehicle, its condition, features..."
        />
      </Grid>
      
      {/* Pricing */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          Pricing
        </Typography>
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          fullWidth
          label="Daily Rate *"
          type="number"
          value={formData.dailyRate}
          onChange={(e) => {
            const daily = parseFloat(e.target.value);
            setFormData({ 
              ...formData, 
              dailyRate: daily,
              weeklyRate: daily * 6.5,
              monthlyRate: daily * 25,
              securityDeposit: daily * 3
            });
          }}
          required
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          fullWidth
          label="Weekly Rate"
          type="number"
          value={formData.weeklyRate}
          onChange={(e) => setFormData({ ...formData, weeklyRate: parseFloat(e.target.value) })}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          fullWidth
          label="Monthly Rate"
          type="number"
          value={formData.monthlyRate}
          onChange={(e) => setFormData({ ...formData, monthlyRate: parseFloat(e.target.value) })}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          fullWidth
          label="Security Deposit"
          type="number"
          value={formData.securityDeposit}
          onChange={(e) => setFormData({ ...formData, securityDeposit: parseFloat(e.target.value) })}
        />
      </Grid>
      
      {/* Rental Terms */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          Rental Terms
        </Typography>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Minimum Rental Days"
          type="number"
          value={formData.minimumRentalDays}
          onChange={(e) => setFormData({ ...formData, minimumRentalDays: parseInt(e.target.value) })}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Maximum Rental Days"
          type="number"
          value={formData.maximumRentalDays}
          onChange={(e) => setFormData({ ...formData, maximumRentalDays: parseInt(e.target.value) })}
        />
      </Grid>
      
      {/* Delivery Options */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          Delivery Options
        </Typography>
      </Grid>
      
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={formData.deliveryAvailable}
              onChange={(e) => setFormData({ ...formData, deliveryAvailable: e.target.checked })}
            />
          }
          label="Offer delivery service"
        />
      </Grid>
      
      {formData.deliveryAvailable && (
        <>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Delivery Fee"
              type="number"
              value={formData.deliveryFee}
              onChange={(e) => setFormData({ ...formData, deliveryFee: parseFloat(e.target.value) })}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Delivery Radius (km)"
              type="number"
              value={formData.deliveryRadius}
              onChange={(e) => setFormData({ ...formData, deliveryRadius: parseFloat(e.target.value) })}
            />
          </Grid>
        </>
      )}
      
      {/* Location */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          Location & Pickup
        </Typography>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Pickup Location *"
          value={formData.location}
          onChange={(e) => setFormData({ ...formData, location: e.target.value })}
          required
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Pickup Instructions"
          multiline
          rows={2}
          value={formData.pickupInstructions}
          onChange={(e) => setFormData({ ...formData, pickupInstructions: e.target.value })}
          placeholder="Instructions for customers when picking up the vehicle..."
        />
      </Grid>
      
      {/* Image Upload */}
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          Photos
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Upload clear photos of your vehicle (maximum 10 images)
        </Typography>
        
        <input
          accept="image/*"
          style={{ display: 'none' }}
          id="vehicle-images-upload"
          multiple
          type="file"
          onChange={(e) => {
            const files = Array.from(e.target.files || []);
            setUploadedImages(prev => [...prev, ...files].slice(0, 10));
          }}
        />
        <label htmlFor="vehicle-images-upload">
          <Button
            variant="outlined"
            component="span"
            startIcon={<PhotoIcon />}
            sx={{ mb: 2 }}
          >
            Upload Photos
          </Button>
        </label>
        
        {uploadedImages.length > 0 && (
          <ImageList cols={4} rowHeight={120}>
            {uploadedImages.map((file, index) => (
              <ImageListItem key={index}>
                <img
                  src={URL.createObjectURL(file)}
                  alt={`Upload ${index + 1}`}
                  loading="lazy"
                  style={{ height: 120, objectFit: 'cover' }}
                />
                <ImageListItemBar
                  title={file.name}
                  actionIcon={
                    <IconButton
                      size="small"
                      onClick={() => {
                        setUploadedImages(prev => prev.filter((_, i) => i !== index));
                      }}
                      sx={{ color: 'white' }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  }
                />
                {uploadProgress[file.name] > 0 && (
                  <LinearProgress
                    variant="determinate"
                    value={uploadProgress[file.name]}
                    sx={{ position: 'absolute', bottom: 0, left: 0, right: 0 }}
                  />
                )}
              </ImageListItem>
            ))}
          </ImageList>
        )}
      </Grid>
    </Grid>
  );

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1">
          Vehicle Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Add Vehicle
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                value={filters.search || ''}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category || ''}
                  onChange={(e) => setFilters({ ...filters, category: e.target.value as VehicleCategory })}
                >
                  <MenuItem value="">All</MenuItem>
                  {Object.values(VehicleCategory).map((category) => (
                    <MenuItem key={category} value={category}>
                      {category.replace('_', ' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status || ''}
                  onChange={(e) => setFilters({ ...filters, status: e.target.value as VehicleStatus })}
                >
                  <MenuItem value="">All</MenuItem>
                  {Object.values(VehicleStatus).map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                size="small"
                label="Min Rate"
                type="number"
                value={filters.minRate || ''}
                onChange={(e) => setFilters({ ...filters, minRate: parseFloat(e.target.value) || undefined })}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                size="small"
                label="Max Rate"
                type="number"
                value={filters.maxRate || ''}
                onChange={(e) => setFilters({ ...filters, maxRate: parseFloat(e.target.value) || undefined })}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={1}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => setFilters({})}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Vehicle Grid */}
      {loading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <LinearProgress sx={{ width: '50%' }} />
        </Box>
      ) : (
        <>
          <Grid container spacing={3}>
            {filteredVehicles.map((vehicle) => (
              <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
                {renderVehicleCard(vehicle)}
              </Grid>
            ))}
          </Grid>

          {filteredVehicles.length === 0 && (
            <Box textAlign="center" py={8}>
              <Typography variant="h6" color="text.secondary">
                No vehicles found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {vehicles.length === 0 
                  ? "You haven't added any vehicles yet." 
                  : "Try adjusting your filters."
                }
              </Typography>
              {vehicles.length === 0 && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setIsCreateModalOpen(true)}
                >
                  Add Your First Vehicle
                </Button>
              )}
            </Box>
          )}

          {filteredVehicles.length > 0 && (
            <Box display="flex" justifyContent="center" mt={4}>
              <Pagination
                count={Math.ceil(filteredVehicles.length / pageSize)}
                page={page}
                onChange={(e, newPage) => setPage(newPage)}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* Create Vehicle Modal */}
      <Dialog
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        maxWidth="lg"
        fullWidth
        scroll="paper"
      >
        <DialogTitle>Add New Vehicle</DialogTitle>
        <DialogContent>
          {renderVehicleForm()}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsCreateModalOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateVehicle}
            variant="contained"
            disabled={loading || !formData.make || !formData.model || !formData.category}
          >
            Create Vehicle
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Vehicle Modal */}
      <Dialog
        open={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        maxWidth="lg"
        fullWidth
        scroll="paper"
      >
        <DialogTitle>Edit Vehicle</DialogTitle>
        <DialogContent>
          {renderVehicleForm()}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsEditModalOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateVehicle}
            variant="contained"
            disabled={loading}
          >
            Update Vehicle
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Vehicle Modal */}
      <Dialog
        open={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedVehicle?.make} {selectedVehicle?.model}
        </DialogTitle>
        <DialogContent>
          {selectedVehicle && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <ImageList cols={2} rowHeight={200}>
                  {selectedVehicle.images.map((image, index) => (
                    <ImageListItem key={index}>
                      <img
                        src={image}
                        alt={`${selectedVehicle.make} ${selectedVehicle.model} ${index + 1}`}
                        loading="lazy"
                        style={{ height: 200, objectFit: 'cover' }}
                      />
                    </ImageListItem>
                  ))}
                </ImageList>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Category
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedVehicle.category}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Year
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedVehicle.year}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Description
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedVehicle.description}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  Daily Rate
                </Typography>
                <Typography variant="h6" color="primary">
                  ${selectedVehicle.dailyRate}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  Weekly Rate
                </Typography>
                <Typography variant="h6" color="primary">
                  ${selectedVehicle.weeklyRate}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  Monthly Rate
                </Typography>
                <Typography variant="h6" color="primary">
                  ${selectedVehicle.monthlyRate}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsViewModalOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add vehicle"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setIsCreateModalOpen(true)}
      >
        <AddIcon />
      </Fab>
    </Container>
  );
};

export default VehicleManagement;
