import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  AddCircle as PlusCircle,
  People as Users,
  TrendingUp,
  Security as Shield
} from '@mui/icons-material';
import {
  Box,
  Typography,
  Button,
  Grid,
  Paper
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';

interface EmptyListingsCTAProps {
  variant?: 'home' | 'search' | 'featured';
  className?: string;
}

const EmptyListingsCTA: React.FC<EmptyListingsCTAProps> = ({
  variant = 'home',
  className = ''
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Handle "List Your Vehicle" button click
  const handleListVehicleClick = () => {
    if (user) {
      // User is authenticated, go to list vehicle page
      navigate('/list-vehicle');
    } else {
      // User is not authenticated, go to sign up with redirect
      navigate('/signup?redirect=/list-vehicle');
    }
  };

  const getContent = () => {
    switch (variant) {
      case 'home':
        return {
          title: 'Be the First to List Your Vehicle!',
          subtitle: 'Join <PERSON> and start earning from your motorcycle or scooter today',
          description: 'No vehicles are currently listed on our platform. This is your opportunity to be among the first providers and capture the entire market.',
          buttonText: 'List Your Vehicle',
          features: [
            { icon: TrendingUp, text: 'Earn up to ₹3,000/day' },
            { icon: Shield, text: 'Full insurance coverage' },
            { icon: Users, text: 'Growing customer base' }
          ]
        };
      
      case 'search':
        return {
          title: 'No Vehicles Found',
          subtitle: 'Be the first to list vehicles in this category',
          description: 'We couldn\'t find any vehicles matching your search. Help us grow by listing your vehicle and be the first in this category.',
          buttonText: 'List Your Vehicle',
          features: [
            { icon: PlusCircle, text: 'Easy listing process' },
            { icon: TrendingUp, text: 'High demand area' },
            { icon: Shield, text: 'Secure platform' }
          ]
        };
      
      case 'featured':
        return {
          title: 'Featured Vehicles Coming Soon',
          subtitle: 'Your vehicle could be featured here',
          description: 'We\'re building an amazing marketplace. List your vehicle now and get featured when we launch.',
          buttonText: 'Get Featured',
          features: [
            { icon: TrendingUp, text: 'Premium visibility' },
            { icon: Users, text: 'More bookings' },
            { icon: Shield, text: 'Trusted platform' }
          ]
        };
      
      default:
        return getContent();
    }
  };

  const content = getContent();

  return (
    <Paper
      elevation={2}
      sx={{
        background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
        borderRadius: 4,
        p: 6,
        textAlign: 'center',
        maxWidth: '800px',
        mx: 'auto',
        ...className
      }}
    >
      {/* Icon */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            width: 80,
            height: 80,
            bgcolor: 'primary.main',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 2
          }}
        >
          <PlusCircle sx={{ fontSize: 40, color: 'white' }} />
        </Box>
      </Box>

      {/* Content */}
      <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
        {content.title}
      </Typography>

      <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
        {content.subtitle}
      </Typography>

      <Typography variant="body1" sx={{ color: 'text.secondary', mb: 6, lineHeight: 1.6 }}>
        {content.description}
      </Typography>

        {/* Features */}
        <Grid container spacing={2} sx={{ mb: 6 }}>
          {content.features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                <feature.icon sx={{ fontSize: 20, color: 'primary.main' }} />
                <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary' }}>
                  {feature.text}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            onClick={handleListVehicleClick}
            variant="contained"
            size="large"
            startIcon={<PlusCircle />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2
            }}
          >
            {content.buttonText}
          </Button>

          <Button
            component={Link}
            to="/how-it-works"
            variant="outlined"
            size="large"
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
              borderWidth: 2
            }}
          >
            Learn How It Works
          </Button>
        </Box>

        {/* Additional Info */}
        <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            🚀 <strong>Early Bird Advantage:</strong> List now and get priority placement when customers start searching
          </Typography>
        </Box>
    </Paper>
  );
};

export default EmptyListingsCTA;
