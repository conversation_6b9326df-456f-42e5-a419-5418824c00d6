import React, { useState, useEffect } from 'react'
import { <PERSON>, Typography, Chip, Stack, Theme } from '@mui/material'
import { SxProps } from '@mui/system'
import { supabase } from '../services/supabaseClient'
import apiService from '../services/apiService'

interface HealthStatus {
  supabase: 'checking' | 'healthy' | 'unhealthy'
  api: 'checking' | 'healthy' | 'unhealthy'
  environment: string
}

interface HealthCheckProps {
  apiUrl?: string
}

export const HealthCheck: React.FC<HealthCheckProps> = ({ apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001' }) => {
  const [status, setStatus] = useState<HealthStatus>({
    supabase: 'checking',
    api: 'checking',
    environment: import.meta.env.MODE || 'unknown',
  })

  const [healthDetails, setHealthDetails] = useState<{
    status: 'checking' | 'healthy' | 'unhealthy'
    message?: string
    data?: any
  }>({ status: 'checking' })

  useEffect(() => {
    const checkHealth = async () => {
      // Check Supabase connection by testing auth (safer approach)
      try {
        // Simple connectivity test - just get current session without requiring login
        const { data, error } = await supabase.auth.getSession()
        // Consider it healthy if we can connect, regardless of session status
        setStatus(prev => ({
          ...prev,
          supabase: !error || error.message.includes('session') ? 'healthy' : 'unhealthy'
        }))
      } catch (error: any) {
        console.warn('Supabase health check failed:', error)
        setStatus(prev => ({
          ...prev,
          supabase: 'unhealthy'
        }))
      }

      // Check API connection using apiService
      try {
        const healthResult = await apiService.healthCheck()
        setStatus(prev => ({
          ...prev,
          api: healthResult.status === 'healthy' ? 'healthy' : 'unhealthy'
        }))
        setHealthDetails({
          status: healthResult.status === 'healthy' ? 'healthy' : 'unhealthy',
          data: { message: healthResult.message }
        })
      } catch (error) {
        setStatus(prev => ({
          ...prev,
          api: 'unhealthy'
        }))
        setHealthDetails({
          status: 'unhealthy',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    checkHealth()
  }, [apiUrl])

  const getStatusColor = (status: string): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
    switch (status) {
      case 'healthy':
        return 'success'
      case 'unhealthy':
        return 'error'
      default:
        return 'warning'
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'healthy':
        return 'Healthy'
      case 'unhealthy':
        return 'Unhealthy'
      default:
        return 'Checking...'
    }
  }

  if (!apiUrl || !status) {
    return null
  }

  const boxStyles: SxProps<Theme> = {
    position: 'fixed',
    bottom: 16,
    right: 16,
    bgcolor: 'background.paper',
    p: 2,
    border: 1,
    borderColor: 'grey.200',
    borderRadius: 1,
    boxShadow: 2,
    fontSize: '0.875rem',
    zIndex: 9999,
  }

  return (
    <Box sx={boxStyles}>
      <Stack spacing={1}>
        <Typography variant="subtitle1" fontWeight="bold">
          System Health
        </Typography>
        
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2">Environment:</Typography>
          <Chip
            label={status.environment}
            color={status.environment === 'production' ? 'success' : 'primary'}
            size="small"
          />
        </Stack>
        
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2">Supabase:</Typography>
          <Chip 
            label={getStatusText(status.supabase)}
            color={getStatusColor(status.supabase)}
            size="small"
          />
        </Stack>
        
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2">API:</Typography>
          <Chip 
            label={getStatusText(status.api)}
            color={getStatusColor(status.api)}
            size="small"
          />
        </Stack>
        
        <Typography variant="caption" color="text.secondary">
          API URL: {apiUrl || 'Not set'}
        </Typography>
      </Stack>
    </Box>
  )
}