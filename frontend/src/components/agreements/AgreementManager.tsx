import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Divider,
} from '@mui/material';
import {
  Description,
  Send,
  CheckCircle,
  Schedule,
  Warning,
  Visibility,
  Download,
  MoreVert,
  Refresh,
  Add,
  Email,
  Create,
} from '@mui/icons-material';
import { ProviderService } from '../../services/ProviderService';

interface GeneratedAgreement {
  id: string;
  bookingId: string;
  templateId: string;
  content: string;
  status: 'draft' | 'sent' | 'signed' | 'expired';
  sentAt?: string;
  signedAt?: string;
  customerSignature?: string;
  providerSignature?: string;
  metadata: {
    customerName: string;
    vehicleName: string;
    rentalPeriod: string;
    totalAmount: number;
  };
}

const AgreementManager: React.FC = () => {
  const [agreements, setAgreements] = useState<GeneratedAgreement[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgreement, setSelectedAgreement] = useState<GeneratedAgreement | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedAgreementId, setSelectedAgreementId] = useState<string | null>(null);

  useEffect(() => {
    fetchAgreements();
  }, []);

  const fetchAgreements = async () => {
    try {
      setLoading(true);
      const response = await ProviderService.getSentAgreements();
      
      if (response.success) {
        setAgreements(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching agreements:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'default';
      case 'sent': return 'warning';
      case 'signed': return 'success';
      case 'expired': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <Description />;
      case 'sent': return <Send />;
      case 'signed': return <CheckCircle />;
      case 'expired': return <Warning />;
      default: return <Schedule />;
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, agreementId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedAgreementId(agreementId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedAgreementId(null);
  };

  const handleViewAgreement = (agreement: GeneratedAgreement) => {
    setSelectedAgreement(agreement);
    setViewDialogOpen(true);
    handleMenuClose();
  };

  const handleResendAgreement = async (agreementId: string) => {
    try {
      // Implementation for resending agreement
      console.log('Resending agreement:', agreementId);
      handleMenuClose();
    } catch (error) {
      console.error('Error resending agreement:', error);
    }
  };

  const handleDownloadAgreement = (agreement: GeneratedAgreement) => {
    // Create a downloadable PDF or text file
    const element = document.createElement('a');
    const file = new Blob([agreement.content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `agreement-${agreement.bookingId}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    handleMenuClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading) {
    return (
      <Box>
        <LinearProgress />
        <Box p={3}>
          <Typography>Loading agreements...</Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box display="flex" alignItems="center">
          <Description sx={{ mr: 2, fontSize: 30 }} />
          <Typography variant="h5" fontWeight="bold">
            Agreement Management
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchAgreements}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {/* Navigate to create new agreement */}}
          >
            New Agreement
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Description sx={{ mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {agreements.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Agreements
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Send sx={{ mr: 2, color: 'warning.main' }} />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {agreements.filter(a => a.status === 'sent').length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Pending Signature
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <CheckCircle sx={{ mr: 2, color: 'success.main' }} />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {agreements.filter(a => a.status === 'signed').length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Signed
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <Warning sx={{ mr: 2, color: 'error.main' }} />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {agreements.filter(a => a.status === 'expired').length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Expired
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Agreements Table */}
      {agreements.length === 0 ? (
        <Alert severity="info">
          No agreements found. Create your first agreement to get started.
        </Alert>
      ) : (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Agreements
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Customer</TableCell>
                    <TableCell>Vehicle</TableCell>
                    <TableCell>Rental Period</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Sent Date</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {agreements.map((agreement) => (
                    <TableRow key={agreement.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {agreement.metadata.customerName}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agreement.metadata.vehicleName}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agreement.metadata.rentalPeriod}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(agreement.metadata.totalAmount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(agreement.status)}
                          label={agreement.status.toUpperCase()}
                          color={getStatusColor(agreement.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {agreement.sentAt ? new Date(agreement.sentAt).toLocaleDateString() : '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={(e) => handleMenuClick(e, agreement.id)}
                          size="small"
                        >
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          const agreement = agreements.find(a => a.id === selectedAgreementId);
          if (agreement) handleViewAgreement(agreement);
        }}>
          <Visibility sx={{ mr: 1 }} />
          View Agreement
        </MenuItem>
        <MenuItem onClick={() => {
          const agreement = agreements.find(a => a.id === selectedAgreementId);
          if (agreement) handleDownloadAgreement(agreement);
        }}>
          <Download sx={{ mr: 1 }} />
          Download
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => selectedAgreementId && handleResendAgreement(selectedAgreementId)}>
          <Email sx={{ mr: 1 }} />
          Resend
        </MenuItem>
      </Menu>

      {/* View Agreement Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Agreement Details
          {selectedAgreement && (
            <Chip
              icon={getStatusIcon(selectedAgreement.status)}
              label={selectedAgreement.status.toUpperCase()}
              color={getStatusColor(selectedAgreement.status) as any}
              size="small"
              sx={{ ml: 2 }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {selectedAgreement && (
            <Box>
              {/* Agreement Metadata */}
              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Customer</Typography>
                  <Typography variant="body1">{selectedAgreement.metadata.customerName}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Vehicle</Typography>
                  <Typography variant="body1">{selectedAgreement.metadata.vehicleName}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Rental Period</Typography>
                  <Typography variant="body1">{selectedAgreement.metadata.rentalPeriod}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">Total Amount</Typography>
                  <Typography variant="body1">{formatCurrency(selectedAgreement.metadata.totalAmount)}</Typography>
                </Grid>
              </Grid>

              <Divider sx={{ mb: 3 }} />

              {/* Agreement Content */}
              <Paper sx={{ p: 3, maxHeight: 400, overflow: 'auto', bgcolor: 'grey.50' }}>
                <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit', fontSize: '14px' }}>
                  {selectedAgreement.content}
                </pre>
              </Paper>

              {/* Signature Status */}
              {selectedAgreement.status === 'signed' && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  <Box display="flex" alignItems="center">
                    <Create sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      Agreement signed on {selectedAgreement.signedAt ? new Date(selectedAgreement.signedAt).toLocaleString() : 'Unknown'}
                    </Typography>
                  </Box>
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          {selectedAgreement && selectedAgreement.status === 'sent' && (
            <Button
              variant="contained"
              onClick={() => selectedAgreement && handleResendAgreement(selectedAgreement.id)}
            >
              Resend Agreement
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AgreementManager;
