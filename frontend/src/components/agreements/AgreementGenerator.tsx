import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Alert,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Description,
  Send,
  Preview,
  Edit,
  Save,
  ExpandMore,
  CheckCircle,
  Warning,
  Info,
  Gavel,
  Security,
  AttachMoney,
  DirectionsCar,
  Person,
  Schedule,
  LocationOn,
} from '@mui/icons-material';
import { Booking, Vehicle, User } from '../../types';
import { ProviderService } from '../../services/ProviderService';

interface AgreementTemplate {
  id: string;
  name: string;
  type: 'rental' | 'insurance' | 'damage_waiver' | 'custom';
  sections: AgreementSection[];
  isActive: boolean;
  lastModified: string;
}

interface AgreementSection {
  id: string;
  title: string;
  content: string;
  isRequired: boolean;
  variables: string[]; // Variables that can be replaced with booking data
  order: number;
}

interface GeneratedAgreement {
  id: string;
  bookingId: string;
  templateId: string;
  content: string;
  status: 'draft' | 'sent' | 'signed' | 'expired';
  sentAt?: string;
  signedAt?: string;
  customerSignature?: string;
  providerSignature?: string;
  ipAddress?: string;
  metadata: {
    customerName: string;
    vehicleName: string;
    rentalPeriod: string;
    totalAmount: number;
  };
}

interface AgreementGeneratorProps {
  booking: Booking;
  onAgreementSent?: (agreement: GeneratedAgreement) => void;
}

const AgreementGenerator: React.FC<AgreementGeneratorProps> = ({
  booking,
  onAgreementSent,
}) => {
  const [templates, setTemplates] = useState<AgreementTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<AgreementTemplate | null>(null);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [customClauses, setCustomClauses] = useState<string[]>([]);
  const [agreementSettings, setAgreementSettings] = useState({
    requireSignature: true,
    sendCopy: true,
    expiryDays: 7,
    includeInsurance: booking.insuranceOption ? true : false,
    includeDamageWaiver: true,
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await ProviderService.getAgreementTemplates();
      if (response.success) {
        setTemplates(response.data || []);
        // Auto-select rental template if available
        const rentalTemplate = response.data?.find((t: AgreementTemplate) => t.type === 'rental');
        if (rentalTemplate) {
          setSelectedTemplate(rentalTemplate);
          generateAgreement(rentalTemplate);
        }
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const generateAgreement = (template: AgreementTemplate) => {
    if (!template || !booking) return;

    let content = '';
    
    // Sort sections by order
    const sortedSections = [...template.sections].sort((a, b) => a.order - b.order);
    
    sortedSections.forEach((section) => {
      content += `\n\n## ${section.title}\n\n`;
      
      // Replace variables with actual booking data
      let sectionContent = section.content;
      
      // Replace common variables
      const variables = {
        '{{CUSTOMER_NAME}}': booking.user?.name || 'Customer',
        '{{CUSTOMER_EMAIL}}': booking.user?.email || '',
        '{{CUSTOMER_PHONE}}': booking.user?.phone || '',
        '{{PROVIDER_NAME}}': booking.provider?.name || 'Provider',
        '{{PROVIDER_COMPANY}}': booking.provider?.companyName || '',
        '{{PROVIDER_PHONE}}': booking.provider?.phone || '',
        '{{VEHICLE_NAME}}': booking.vehicle?.name || `${booking.vehicle?.brand} ${booking.vehicle?.model}`,
        '{{VEHICLE_YEAR}}': booking.vehicle?.year || '',
        '{{VEHICLE_LICENSE}}': booking.vehicle?.specifications?.licensePlate || '',
        '{{RENTAL_START}}': new Date(booking.startDate).toLocaleDateString(),
        '{{RENTAL_END}}': new Date(booking.endDate).toLocaleDateString(),
        '{{RENTAL_DAYS}}': booking.totalDays?.toString() || '1',
        '{{TOTAL_AMOUNT}}': `$${booking.totalAmount?.toFixed(2) || '0.00'}`,
        '{{DAILY_RATE}}': `$${booking.vehicleRate?.toFixed(2) || '0.00'}`,
        '{{DEPOSIT_AMOUNT}}': `$${booking.vehicle?.depositAmount?.toFixed(2) || '0.00'}`,
        '{{PICKUP_LOCATION}}': booking.vehicle?.location?.address || '',
        '{{INSURANCE_TYPE}}': booking.insuranceOption || 'Basic',
        '{{BOOKING_ID}}': booking.id,
        '{{AGREEMENT_DATE}}': new Date().toLocaleDateString(),
        '{{PAYMENT_METHOD}}': booking.paymentMethod || 'Card',
      };

      // Replace all variables
      Object.entries(variables).forEach(([variable, value]) => {
        sectionContent = sectionContent.replace(new RegExp(variable, 'g'), value);
      });

      content += sectionContent;
    });

    // Add custom clauses if any
    if (customClauses.length > 0) {
      content += '\n\n## Additional Terms\n\n';
      customClauses.forEach((clause, index) => {
        content += `${index + 1}. ${clause}\n\n`;
      });
    }

    // Add signature section
    content += '\n\n## Signatures\n\n';
    content += 'By signing below, both parties agree to the terms and conditions outlined in this agreement.\n\n';
    content += `**Customer Signature:** ___________________________ Date: ___________\n`;
    content += `**Provider Signature:** ___________________________ Date: ___________\n\n`;
    content += `Agreement ID: ${booking.id}-${Date.now()}\n`;
    content += `Generated on: ${new Date().toLocaleString()}`;

    setGeneratedContent(content);
  };

  const sendAgreement = async () => {
    if (!selectedTemplate || !generatedContent) return;

    setLoading(true);
    try {
      const agreementData = {
        bookingId: booking.id,
        templateId: selectedTemplate.id,
        content: generatedContent,
        settings: agreementSettings,
        metadata: {
          customerName: booking.user?.name || 'Customer',
          vehicleName: booking.vehicle?.name || 'Vehicle',
          rentalPeriod: `${new Date(booking.startDate).toLocaleDateString()} - ${new Date(booking.endDate).toLocaleDateString()}`,
          totalAmount: booking.totalAmount || 0,
        },
      };

      const response = await ProviderService.sendAgreement(agreementData);
      
      if (response.success) {
        onAgreementSent?.(response.data);
        // Show success message
      }
    } catch (error) {
      console.error('Error sending agreement:', error);
    } finally {
      setLoading(false);
    }
  };

  const addCustomClause = () => {
    setCustomClauses([...customClauses, '']);
  };

  const updateCustomClause = (index: number, value: string) => {
    const updated = [...customClauses];
    updated[index] = value;
    setCustomClauses(updated);
    
    // Regenerate agreement with updated clauses
    if (selectedTemplate) {
      generateAgreement(selectedTemplate);
    }
  };

  const removeCustomClause = (index: number) => {
    const updated = customClauses.filter((_, i) => i !== index);
    setCustomClauses(updated);
    
    // Regenerate agreement
    if (selectedTemplate) {
      generateAgreement(selectedTemplate);
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <Description sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Generate Rental Agreement
        </Typography>
      </Box>

      {/* Booking Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Booking Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box display="flex" alignItems="center" mb={1}>
                <Person sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body2">
                  <strong>Customer:</strong> {booking.user?.name || 'N/A'}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <DirectionsCar sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body2">
                  <strong>Vehicle:</strong> {booking.vehicle?.name || `${booking.vehicle?.brand} ${booking.vehicle?.model}`}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box display="flex" alignItems="center" mb={1}>
                <Schedule sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body2">
                  <strong>Period:</strong> {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={1}>
                <AttachMoney sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body2">
                  <strong>Total:</strong> ${booking.totalAmount?.toFixed(2) || '0.00'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Template Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Agreement Template
          </Typography>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Select Template</InputLabel>
            <Select
              value={selectedTemplate?.id || ''}
              onChange={(e) => {
                const template = templates.find(t => t.id === e.target.value);
                setSelectedTemplate(template || null);
                if (template) generateAgreement(template);
              }}
            >
              {templates.map((template) => (
                <MenuItem key={template.id} value={template.id}>
                  <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                    <Typography>{template.name}</Typography>
                    <Chip 
                      label={template.type.replace('_', ' ').toUpperCase()} 
                      size="small" 
                      color="primary" 
                      variant="outlined" 
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {selectedTemplate && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>{selectedTemplate.name}</strong> - {selectedTemplate.sections.length} sections
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Agreement Settings */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="h6">Agreement Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={agreementSettings.requireSignature}
                    onChange={(e) => setAgreementSettings({
                      ...agreementSettings,
                      requireSignature: e.target.checked
                    })}
                  />
                }
                label="Require Digital Signature"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={agreementSettings.sendCopy}
                    onChange={(e) => setAgreementSettings({
                      ...agreementSettings,
                      sendCopy: e.target.checked
                    })}
                  />
                }
                label="Send Copy to Customer"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Agreement Expiry (Days)"
                type="number"
                value={agreementSettings.expiryDays}
                onChange={(e) => setAgreementSettings({
                  ...agreementSettings,
                  expiryDays: parseInt(e.target.value) || 7
                })}
                fullWidth
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Custom Clauses */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="h6">Custom Clauses</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {customClauses.map((clause, index) => (
            <Box key={index} display="flex" alignItems="center" mb={2}>
              <TextField
                label={`Custom Clause ${index + 1}`}
                value={clause}
                onChange={(e) => updateCustomClause(index, e.target.value)}
                fullWidth
                multiline
                rows={2}
                sx={{ mr: 1 }}
              />
              <Button
                color="error"
                onClick={() => removeCustomClause(index)}
              >
                Remove
              </Button>
            </Box>
          ))}
          <Button
            variant="outlined"
            onClick={addCustomClause}
            startIcon={<Edit />}
          >
            Add Custom Clause
          </Button>
        </AccordionDetails>
      </Accordion>

      {/* Actions */}
      <Box display="flex" gap={2} mb={3}>
        <Button
          variant="outlined"
          startIcon={<Preview />}
          onClick={() => setPreviewOpen(true)}
          disabled={!generatedContent}
        >
          Preview Agreement
        </Button>
        <Button
          variant="contained"
          startIcon={<Send />}
          onClick={sendAgreement}
          disabled={!generatedContent || loading}
        >
          {loading ? 'Sending...' : 'Send Agreement'}
        </Button>
      </Box>

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Agreement Preview</DialogTitle>
        <DialogContent>
          <Paper sx={{ p: 3, maxHeight: 500, overflow: 'auto' }}>
            <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
              {generatedContent}
            </pre>
          </Paper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>Close</Button>
          <Button variant="contained" onClick={sendAgreement} disabled={loading}>
            Send Agreement
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AgreementGenerator;
