import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  IconButton,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Drawer,
  useTheme,
  useMediaQuery,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  Slider,
  FormControlLabel,
  Switch,
  Autocomplete
} from '@mui/material';
import {
  MyLocation,
  Search,
  DirectionsBike,
  FilterList,
  Close,
  Navigation,
  ZoomIn,
  ZoomOut,
  Layers
} from '@mui/icons-material';
import { GoogleMap, LoadScript, Marker, InfoWindow, Circle } from '@react-google-maps/api';
import { Vehicle, Location, VehicleCategory } from '../types';
import * as VehicleService from '../services/VehicleService';
import * as LocationService from '../services/LocationService';

const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

interface MapVehicleSearchProps {
  onVehicleSelect: (vehicle: Vehicle) => void;
  searchFilters?: any;
  className?: string;
}

interface MapFilters {
  category: VehicleCategory | '';
  maxDistance: number;
  priceRange: [number, number];
  showOnlyAvailable: boolean;
  deliveryOnly: boolean;
}

const mapContainerStyle = {
  width: '100%',
  height: '600px'
};

const defaultCenter = {
  lat: 37.7749,
  lng: -122.4194 // San Francisco
};

const mapOptions = {
  disableDefaultUI: false,
  zoomControl: true,
  streetViewControl: false,
  mapTypeControl: true,
  fullscreenControl: true,
};

export const MapVehicleSearch: React.FC<MapVehicleSearchProps> = ({
  onVehicleSelect,
  searchFilters,
  className
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [userLocation, setUserLocation] = useState<google.maps.LatLngLiteral | null>(null);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<Vehicle[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [searchLocation, setSearchLocation] = useState('');
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const [filters, setFilters] = useState<MapFilters>({
    category: '',
    maxDistance: 25, // km
    priceRange: [0, 200],
    showOnlyAvailable: true,
    deliveryOnly: false
  });

  const searchBoxRef = useRef<google.maps.places.SearchBox | null>(null);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  // Initialize user location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setUserLocation(location);
        },
        (error) => {
          console.error('Error getting user location:', error);
        }
      );
    }
  }, []);

  // Fetch vehicles when filters change
  useEffect(() => {
    fetchVehicles();
  }, [filters, userLocation]);

  // Apply filters to vehicles
  useEffect(() => {
    applyFilters();
  }, [vehicles, filters, userLocation]);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      
      // Get vehicles based on search criteria
      const searchParams = {
        category: filters.category || undefined,
        location: userLocation ? `${userLocation.lat},${userLocation.lng}` : undefined,
        radius: filters.maxDistance,
        minPrice: filters.priceRange[0],
        maxPrice: filters.priceRange[1],
        available: filters.showOnlyAvailable,
        delivery: filters.deliveryOnly,
        ...searchFilters
      };

      const response = await VehicleService.searchVehicles({
        filters: searchParams,
        page: 1,
        size: 100
      });

      if (response.success && response.data) {
        setVehicles(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching vehicles:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = vehicles;

    // Distance filter
    if (userLocation && filters.maxDistance > 0) {
      filtered = filtered.filter(vehicle => {
        const distance = calculateDistance(
          userLocation.lat,
          userLocation.lng,
          vehicle.location.latitude,
          vehicle.location.longitude
        );
        return distance <= filters.maxDistance;
      });
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(vehicle => vehicle.category === filters.category);
    }

    // Price range filter
    filtered = filtered.filter(vehicle => 
      vehicle.dailyRate >= filters.priceRange[0] && 
      vehicle.dailyRate <= filters.priceRange[1]
    );

    // Availability filter
    if (filters.showOnlyAvailable) {
      filtered = filtered.filter(vehicle => vehicle.status === 'available');
    }

    // Delivery filter
    if (filters.deliveryOnly) {
      filtered = filtered.filter(vehicle => vehicle.deliveryAvailable);
    }

    setFilteredVehicles(filtered);
  };

  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const handleLocationSearch = async (location: string) => {
    if (!placesService.current || !map) return;

    const request = {
      query: location,
      fields: ['name', 'geometry'],
    };

    placesService.current.textSearch(request, (results, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && results && results[0]) {
        const place = results[0];
        if (place.geometry && place.geometry.location) {
          const location = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
          };
          
          map.panTo(location);
          map.setZoom(13);
          setUserLocation(location);
        }
      }
    });
  };

  const handleMapLoad = useCallback((map: google.maps.Map) => {
    setMap(map);
    placesService.current = new google.maps.places.PlacesService(map);
    autocompleteService.current = new google.maps.places.AutocompleteService();
  }, []);

  const handleMarkerClick = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    
    // Pan to vehicle location
    if (map) {
      map.panTo({
        lat: vehicle.location.latitude,
        lng: vehicle.location.longitude
      });
    }
  };

  const getMarkerIcon = (vehicle: Vehicle) => {
    const isSelected = selectedVehicle?.id === vehicle.id;
    
    return {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="18" fill="${isSelected ? '#f50057' : '#1976d2'}" stroke="white" stroke-width="2"/>
          <text x="20" y="26" text-anchor="middle" font-family="Arial" font-size="12" fill="white">$${vehicle.dailyRate}</text>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(40, 40),
      anchor: new google.maps.Point(20, 20),
    };
  };

  const renderVehicleCard = (vehicle: Vehicle) => (
    <Card sx={{ mb: 2, cursor: 'pointer' }} onClick={() => handleMarkerClick(vehicle)}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="start">
          <Box>
            <Typography variant="h6">
              {vehicle.make} {vehicle.model}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {vehicle.year} • {vehicle.category}
            </Typography>
            <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
              ${vehicle.dailyRate}/day
            </Typography>
          </Box>
          <Avatar>
            <DirectionsBike />
          </Avatar>
        </Box>
        
        <Box mt={2}>
          <Button
            variant="contained"
            size="small"
            fullWidth
            onClick={(e) => {
              e.stopPropagation();
              onVehicleSelect(vehicle);
            }}
          >
            Book Now
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  const renderFilters = () => (
    <Box sx={{ p: 3, minWidth: 300 }}>
      <Typography variant="h6" gutterBottom>
        Search Filters
      </Typography>
      
      <Box mb={3}>
        <Typography gutterBottom>Category</Typography>
        <Autocomplete
          value={filters.category}
          onChange={(_, value) => setFilters({ ...filters, category: (value as VehicleCategory) || '' })}
          options={['', ...Object.values(VehicleCategory)]}
          getOptionLabel={(option) => option ? option.replace('_', ' ').toUpperCase() : 'All Categories'}
          renderInput={(params) => <TextField {...params} size="small" />}
        />
      </Box>
      
      <Box mb={3}>
        <Typography gutterBottom>
          Max Distance: {filters.maxDistance}km
        </Typography>
        <Slider
          value={filters.maxDistance}
          onChange={(_, value) => setFilters({ ...filters, maxDistance: value as number })}
          min={1}
          max={50}
          valueLabelDisplay="auto"
        />
      </Box>
      
      <Box mb={3}>
        <Typography gutterBottom>
          Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
        </Typography>
        <Slider
          value={filters.priceRange}
          onChange={(_, value) => setFilters({ ...filters, priceRange: value as [number, number] })}
          min={0}
          max={500}
          valueLabelDisplay="auto"
        />
      </Box>
      
      <Box mb={2}>
        <FormControlLabel
          control={
            <Switch
              checked={filters.showOnlyAvailable}
              onChange={(e) => setFilters({ ...filters, showOnlyAvailable: e.target.checked })}
            />
          }
          label="Available only"
        />
      </Box>
      
      <Box mb={2}>
        <FormControlLabel
          control={
            <Switch
              checked={filters.deliveryOnly}
              onChange={(e) => setFilters({ ...filters, deliveryOnly: e.target.checked })}
            />
          }
          label="Delivery available"
        />
      </Box>
      
      <Button
        variant="outlined"
        fullWidth
        onClick={() => {
          setFilters({
            category: '',
            maxDistance: 25,
            priceRange: [0, 200],
            showOnlyAvailable: true,
            deliveryOnly: false
          });
        }}
      >
        Reset Filters
      </Button>
    </Box>
  );

  return (
    <Box className={className} sx={{ position: 'relative', height: '100%' }}>
      <LoadScript googleMapsApiKey={GOOGLE_MAPS_API_KEY} libraries={['places']}>
        <Box sx={{ position: 'relative', height: '100%' }}>
          {/* Search Bar */}
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              left: 16,
              right: isMobile ? 16 : 400,
              zIndex: 1000,
              display: 'flex',
              gap: 1
            }}
          >
            <TextField
              fullWidth
              placeholder="Search location..."
              value={searchLocation}
              onChange={(e) => setSearchLocation(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleLocationSearch(searchLocation);
                }
              }}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                sx: { bgcolor: 'background.paper' }
              }}
            />
            
            <IconButton
              onClick={() => setIsFiltersOpen(true)}
              sx={{ bgcolor: 'background.paper' }}
            >
              <FilterList />
            </IconButton>
          </Box>

          {/* Map */}
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={userLocation || defaultCenter}
            zoom={userLocation ? 13 : 10}
            options={mapOptions}
            onLoad={handleMapLoad}
          >
            {/* User location marker */}
            {userLocation && (
              <Marker
                position={userLocation}
                icon={{
                  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="10" cy="10" r="8" fill="#4285f4" stroke="white" stroke-width="2"/>
                      <circle cx="10" cy="10" r="3" fill="white"/>
                    </svg>
                  `),
                  scaledSize: new google.maps.Size(20, 20),
                }}
              />
            )}

            {/* Vehicle markers */}
            {filteredVehicles.map((vehicle) => (
              <Marker
                key={vehicle.id}
                position={{
                  lat: vehicle.location.latitude,
                  lng: vehicle.location.longitude,
                }}
                icon={getMarkerIcon(vehicle)}
                onClick={() => handleMarkerClick(vehicle)}
              />
            ))}

            {/* Search radius circle */}
            {userLocation && (
              <Circle
                center={userLocation}
                radius={filters.maxDistance * 1000} // Convert km to meters
                options={{
                  strokeColor: '#1976d2',
                  strokeOpacity: 0.3,
                  strokeWeight: 2,
                  fillColor: '#1976d2',
                  fillOpacity: 0.1,
                }}
              />
            )}

            {/* Selected vehicle info window */}
            {selectedVehicle && (
              <InfoWindow
                position={{
                  lat: selectedVehicle.location.latitude,
                  lng: selectedVehicle.location.longitude,
                }}
                onCloseClick={() => setSelectedVehicle(null)}
              >
                <Box sx={{ maxWidth: 250 }}>
                  <Typography variant="h6">
                    {selectedVehicle.make} {selectedVehicle.model}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedVehicle.year} • {selectedVehicle.category}
                  </Typography>
                  <Typography variant="body2" sx={{ my: 1 }}>
                    {selectedVehicle.description.substring(0, 100)}...
                  </Typography>
                  <Typography variant="h6" color="primary" gutterBottom>
                    ${selectedVehicle.dailyRate}/day
                  </Typography>
                  <Button
                    variant="contained"
                    size="small"
                    fullWidth
                    onClick={() => onVehicleSelect(selectedVehicle)}
                  >
                    Book Now
                  </Button>
                </Box>
              </InfoWindow>
            )}
          </GoogleMap>

          {/* Vehicle List Sidebar (Desktop) */}
          {!isMobile && (
            <Box
              sx={{
                position: 'absolute',
                top: 80,
                right: 16,
                bottom: 16,
                width: 350,
                bgcolor: 'background.paper',
                borderRadius: 1,
                boxShadow: 2,
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">
                  {filteredVehicles.length} vehicles found
                </Typography>
              </Box>
              
              <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
                {filteredVehicles.map((vehicle) => renderVehicleCard(vehicle))}
              </Box>
            </Box>
          )}

          {/* Floating Action Buttons */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: 16,
              display: 'flex',
              flexDirection: 'column',
              gap: 1
            }}
          >
            <Fab
              size="small"
              onClick={() => {
                if (navigator.geolocation) {
                  navigator.geolocation.getCurrentPosition((position) => {
                    const location = {
                      lat: position.coords.latitude,
                      lng: position.coords.longitude,
                    };
                    setUserLocation(location);
                    map?.panTo(location);
                    map?.setZoom(13);
                  });
                }
              }}
            >
              <MyLocation />
            </Fab>
          </Box>

          {/* Mobile Vehicle List */}
          {isMobile && (
            <Drawer
              anchor="bottom"
              open={selectedVehicle !== null}
              onClose={() => setSelectedVehicle(null)}
              PaperProps={{
                sx: { height: '50%', borderTopLeftRadius: 16, borderTopRightRadius: 16 }
              }}
            >
              <Box sx={{ p: 2 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    {filteredVehicles.length} vehicles found
                  </Typography>
                  <IconButton onClick={() => setSelectedVehicle(null)}>
                    <Close />
                  </IconButton>
                </Box>
                
                <Box sx={{ height: 'calc(50vh - 100px)', overflow: 'auto' }}>
                  {filteredVehicles.map((vehicle) => renderVehicleCard(vehicle))}
                </Box>
              </Box>
            </Drawer>
          )}
        </Box>
      </LoadScript>

      {/* Filters Dialog */}
      <Dialog
        open={isFiltersOpen}
        onClose={() => setIsFiltersOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Search Filters
          <IconButton
            onClick={() => setIsFiltersOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {renderFilters()}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default MapVehicleSearch;
