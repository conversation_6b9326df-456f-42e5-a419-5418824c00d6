import React, { useState } from 'react';
import axios from 'axios';

const SUPPORT_CATEGORIES = [
  'PAYMENT',
  'VEHICLE',
  'DAMAGE', 
  'BOOKING', 
  'OTHER'
];

interface SupportTicketFormProps {
  bookingId?: string;
}

const SupportTicketForm: React.FC<SupportTicketFormProps> = ({ bookingId }) => {
  const [category, setCategory] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      await axios.post('/api/support/tickets', {
        category,
        message,
        bookingId
      });

      setSubmitStatus('success');
      setCategory('');
      setMessage('');
    } catch (error) {
      console.error('Support ticket submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="support-ticket-form">
      <h2>Submit Support Ticket</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="category">Category</label>
          <select 
            id="category"
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            required
          >
            <option value="">Select Category</option>
            {SUPPORT_CATEGORIES.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="message">Message</label>
          <textarea 
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            required
            placeholder="Describe your issue in detail"
          />
        </div>

        <button 
          type="submit" 
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Ticket'}
        </button>

        {submitStatus === 'success' && (
          <p className="success-message">
            Support ticket submitted successfully!
          </p>
        )}

        {submitStatus === 'error' && (
          <p className="error-message">
            Failed to submit support ticket. Please try again.
          </p>
        )}
      </form>
    </div>
  );
};

export default SupportTicketForm;
