import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Person,
  Notifications,
  Business,
  Security,
  Payment,
  Verified,
  Refresh,
  GetApp,
} from '@mui/icons-material';

interface EmptySettingsCTAProps {
  onRefresh?: () => void;
}

const EmptySettingsCTA: React.FC<EmptySettingsCTAProps> = ({ onRefresh }) => {
  const settingsFeatures = [
    {
      icon: Person,
      title: 'Profile Management',
      description: 'Update your personal and business information',
      color: '#2196f3',
    },
    {
      icon: Notifications,
      title: 'Notification Preferences',
      description: 'Control how and when you receive notifications',
      color: '#ff9800',
    },
    {
      icon: Business,
      title: 'Business Settings',
      description: 'Configure booking rules and policies',
      color: '#4caf50',
    },
    {
      icon: Security,
      title: 'Security & Privacy',
      description: 'Manage account security and privacy settings',
      color: '#f44336',
    },
    {
      icon: Payment,
      title: 'Payment Settings',
      description: 'Set up payment methods and payout preferences',
      color: '#9c27b0',
    },
    {
      icon: Verified,
      title: 'Account Verification',
      description: 'Get verified to build trust with customers',
      color: '#00bcd4',
    },
  ];

  const quickActions = [
    'Complete your profile information',
    'Set up notification preferences',
    'Configure business policies',
    'Upload verification documents',
    'Set payment preferences',
    'Review security settings',
  ];

  return (
    <Box>
      {/* Main CTA Section */}
      <Paper
        elevation={2}
        sx={{
          background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          mb: 4,
        }}
      >
        {/* Icon */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              width: 100,
              height: 100,
              bgcolor: 'primary.main',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              boxShadow: 3,
            }}
          >
            <SettingsIcon sx={{ fontSize: 50, color: 'white' }} />
          </Box>
        </Box>

        {/* Content */}
        <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          Customize Your Experience
        </Typography>

        <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
          Configure your account settings and preferences
        </Typography>

        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: '600px', mx: 'auto' }}>
          Set up your profile, notification preferences, business policies, and security settings to 
          optimize your RentaHub experience and build trust with customers.
        </Typography>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            onClick={onRefresh}
            variant="contained"
            size="large"
            startIcon={<Refresh />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
            }}
          >
            Load Settings
          </Button>

          <Button
            variant="outlined"
            size="large"
            startIcon={<GetApp />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
              borderWidth: 2,
            }}
          >
            Download App
          </Button>
        </Box>

        {/* Progress Indicator */}
        <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            ⚙️ <strong>Pro Tip:</strong> Complete your profile setup to increase booking rates by up to 40%
          </Typography>
        </Box>
      </Paper>

      {/* Settings Features */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
          Available Settings & Features
        </Typography>
        
        <Grid container spacing={3}>
          {settingsFeatures.map((feature, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: feature.color,
                      width: 60,
                      height: 60,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <feature.icon sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Quick Setup Checklist */}
      <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          Quick Setup Checklist
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
              Essential Setup Steps:
            </Typography>
            <List dense>
              {quickActions.slice(0, 3).map((action, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 1}
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={action} />
                </ListItem>
              ))}
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
              Advanced Configuration:
            </Typography>
            <List dense>
              {quickActions.slice(3).map((action, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'secondary.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 4}
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={action} />
                </ListItem>
              ))}
            </List>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body2" color="text.secondary">
            💡 <strong>Completion Bonus:</strong> Providers with complete profiles earn 40% more than incomplete ones
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default EmptySettingsCTA;
