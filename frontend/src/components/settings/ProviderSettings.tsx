import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Alert,
  Snackbar,
  Avatar,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Tooltip,
  IconButton as MuiIconButton,
} from '@mui/material';
import {
  Notifications,
  Security,
  Payment,
  Person,
  Business,
  LocationOn,
  Phone,
  Email,
  Edit,
  Save,
  Cancel,
  ExpandMore,
  Verified,
  Warning,
  Info,
  CheckCircle,
  Settings as SettingsIcon,
  ContentCopy,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { ProviderService } from '../../services/ProviderService';
import { NotificationService } from '../../services/NotificationService';
import EmptySettingsCTA from './EmptySettingsCTA';

interface ProviderProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  businessName: string;
  businessAddress: string;
  city: string;
  profileImage?: string;
  isVerified: boolean;
  verificationStatus: 'pending' | 'verified' | 'rejected';
}

interface NotificationSettings {
  emailBookings: boolean;
  smsBookings: boolean;
  emailPayments: boolean;
  smsPayments: boolean;
  emailMarketing: boolean;
  emailReminders: boolean;
  pushNotifications: boolean;
}

interface BusinessSettings {
  autoAcceptBookings: boolean;
  minimumBookingHours: number;
  maximumBookingDays: number;
  cancellationPolicy: 'flexible' | 'moderate' | 'strict';
  instantBooking: boolean;
  requireDeposit: boolean;
  depositAmount: number;
}

const ProviderSettings: React.FC = () => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<ProviderProfile | null>(null);
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailBookings: true,
    smsBookings: true,
    emailPayments: true,
    smsPayments: false,
    emailMarketing: false,
    emailReminders: true,
    pushNotifications: true,
  });
  const [business, setBusiness] = useState<BusinessSettings>({
    autoAcceptBookings: false,
    minimumBookingHours: 4,
    maximumBookingDays: 30,
    cancellationPolicy: 'moderate',
    instantBooking: false,
    requireDeposit: true,
    depositAmount: 500,
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingProfile, setEditingProfile] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [verificationDialog, setVerificationDialog] = useState(false);

  useEffect(() => {
    fetchProviderSettings();
  }, []);

  const fetchProviderSettings = async () => {
    try {
      setLoading(true);

      // Fetch live provider data
      const response = await ProviderService.getProviderProfile();

      if (response.success && response.data) {
        const providerData = response.data;

        const profileData: ProviderProfile = {
          id: providerData.id || user?.id || '1',
          name: providerData.name || user?.name || 'Provider',
          email: providerData.email || user?.email || '',
          phone: providerData.phone || '',
          businessName: providerData.businessName || '',
          businessAddress: providerData.businessAddress || '',
          city: providerData.city || '',
          profileImage: providerData.profileImage,
          isVerified: providerData.isVerified || false,
          verificationStatus: providerData.verificationStatus || 'pending',
        };

        setProfile(profileData);
      } else {
        // Fallback to user data if provider profile not found
        const fallbackProfile: ProviderProfile = {
          id: user?.id || '1',
          name: user?.name || 'Provider',
          email: user?.email || '',
          phone: '',
          businessName: '',
          businessAddress: '',
          city: '',
          isVerified: false,
          verificationStatus: 'pending',
        };

        setProfile(fallbackProfile);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setSnackbar({ open: true, message: 'Failed to load settings', severity: 'error' });

      // Fallback to basic user data on error
      if (user) {
        const fallbackProfile: ProviderProfile = {
          id: user.id,
          name: user.name || 'Provider',
          email: user.email || '',
          phone: '',
          businessName: '',
          businessAddress: '',
          city: '',
          isVerified: false,
          verificationStatus: 'pending',
        };
        setProfile(fallbackProfile);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!profile) return;

    try {
      setSaving(true);

      // Save profile using ProviderService
      const response = await ProviderService.updateProviderProfile({
        name: profile.name,
        email: profile.email,
        phone: profile.phone,
        businessName: profile.businessName,
        businessAddress: profile.businessAddress,
        city: profile.city,
      });

      if (response.success) {
        setEditingProfile(false);
        setSnackbar({ open: true, message: 'Profile updated successfully', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to update profile', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to update profile', severity: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleNotificationChange = (key: keyof NotificationSettings) => {
    setNotifications(prev => ({ ...prev, [key]: !prev[key] }));
    // Auto-save notification settings
    saveNotificationSettings({ ...notifications, [key]: !notifications[key] });
  };

  const handleBusinessChange = (key: keyof BusinessSettings, value: any) => {
    setBusiness(prev => ({ ...prev, [key]: value }));
  };

  const saveNotificationSettings = async (settings: NotificationSettings) => {
    try {
      // TODO: Implement API call
      console.log('Saving notification settings:', settings);
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to save notification settings', severity: 'error' });
    }
  };

  const saveBusinessSettings = async () => {
    try {
      setSaving(true);
      // TODO: Implement API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Mock delay
      
      setSnackbar({ open: true, message: 'Business settings updated successfully', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to update business settings', severity: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleVerificationRequest = () => {
    setVerificationDialog(true);
  };

  const handleVerificationAcknowledged = async () => {
    try {
      // Create notification for admin
      await NotificationService.createNotification({
        type: 'info',
        title: 'Provider Verification Request',
        message: `${profile?.businessName || profile?.name || 'A provider'} has been instructed to send verification <NAME_EMAIL>`,
        userId: 'admin', // This will be handled by the admin system
        metadata: {
          providerEmail: profile?.email,
          businessName: profile?.businessName,
          providerName: profile?.name,
          requestType: 'verification'
        }
      });

      setVerificationDialog(false);
      setSnackbar({
        open: true,
        message: 'Verification instructions sent! Please check your email for next steps.',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error creating admin notification:', error);
      // Still close dialog even if notification fails
      setVerificationDialog(false);
      setSnackbar({
        open: true,
        message: 'Instructions received! Please send your <NAME_EMAIL>',
        severity: 'info'
      });
    }
  };



  if (loading) {
    return (
      <Box>
        <LinearProgress />
        <Box p={3}>
          <Typography>Loading settings...</Typography>
        </Box>
      </Box>
    );
  }

  if (!profile) {
    return <EmptySettingsCTA onRefresh={fetchProviderSettings} />;
  }

  const getVerificationChip = () => {
    switch (profile.verificationStatus) {
      case 'verified':
        return <Chip icon={<Verified />} label="Verified" color="success" size="small" />;
      case 'pending':
        return <Chip icon={<Warning />} label="Pending Verification" color="warning" size="small" />;
      case 'rejected':
        return <Chip icon={<Warning />} label="Verification Rejected" color="error" size="small" />;
      default:
        return <Chip icon={<Info />} label="Not Verified" color="default" size="small" />;
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <SettingsIcon sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Provider Settings
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" display="flex" alignItems="center">
                  <Person sx={{ mr: 1 }} />
                  Profile Information
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  {getVerificationChip()}
                  <IconButton 
                    size="small" 
                    onClick={() => setEditingProfile(!editingProfile)}
                    color={editingProfile ? "secondary" : "primary"}
                  >
                    {editingProfile ? <Cancel /> : <Edit />}
                  </IconButton>
                </Box>
              </Box>

              <Box display="flex" alignItems="center" mb={3}>
                <Avatar
                  src={profile.profileImage}
                  sx={{ width: 80, height: 80, mr: 2 }}
                >
                  {profile.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="h6">{profile.name}</Typography>
                  <Typography color="textSecondary">{profile.email}</Typography>
                  {!profile.isVerified && (
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={handleVerificationRequest}
                      sx={{ mt: 1 }}
                    >
                      Get Verified
                    </Button>
                  )}
                </Box>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={profile.name}
                    disabled={!editingProfile}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    value={profile.email}
                    disabled={!editingProfile}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Phone"
                    value={profile.phone}
                    disabled={!editingProfile}
                    onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Business Name"
                    value={profile.businessName}
                    disabled={!editingProfile}
                    onChange={(e) => setProfile({ ...profile, businessName: e.target.value })}
                  />
                </Grid>
              </Grid>

              {editingProfile && (
                <Box mt={2} display="flex" gap={2}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSaveProfile}
                    disabled={saving}
                  >
                    Save Changes
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setEditingProfile(false)}
                  >
                    Cancel
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" display="flex" alignItems="center" mb={2}>
                <Notifications sx={{ mr: 1 }} />
                Notification Preferences
              </Typography>

              <List>
                <ListItem>
                  <ListItemIcon>
                    <Email />
                  </ListItemIcon>
                  <ListItemText primary="Email Notifications" secondary="Booking confirmations and updates" />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={notifications.emailBookings}
                      onChange={() => handleNotificationChange('emailBookings')}
                    />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <Phone />
                  </ListItemIcon>
                  <ListItemText primary="SMS Notifications" secondary="Urgent booking alerts" />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={notifications.smsBookings}
                      onChange={() => handleNotificationChange('smsBookings')}
                    />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <Payment />
                  </ListItemIcon>
                  <ListItemText primary="Payment Notifications" secondary="Payment confirmations and receipts" />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={notifications.emailPayments}
                      onChange={() => handleNotificationChange('emailPayments')}
                    />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <Info />
                  </ListItemIcon>
                  <ListItemText primary="Marketing Emails" secondary="Promotions and platform updates" />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={notifications.emailMarketing}
                      onChange={() => handleNotificationChange('emailMarketing')}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Business Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" display="flex" alignItems="center" mb={2}>
                <Business sx={{ mr: 1 }} />
                Business Settings
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={business.autoAcceptBookings}
                        onChange={(e) => handleBusinessChange('autoAcceptBookings', e.target.checked)}
                      />
                    }
                    label="Auto-accept bookings"
                  />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 4 }}>
                    Automatically approve booking requests
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={business.instantBooking}
                        onChange={(e) => handleBusinessChange('instantBooking', e.target.checked)}
                      />
                    }
                    label="Instant booking"
                  />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 4 }}>
                    Allow customers to book immediately
                  </Typography>
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Minimum Booking Hours"
                    type="number"
                    value={business.minimumBookingHours}
                    onChange={(e) => handleBusinessChange('minimumBookingHours', parseInt(e.target.value))}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Maximum Booking Days"
                    type="number"
                    value={business.maximumBookingDays}
                    onChange={(e) => handleBusinessChange('maximumBookingDays', parseInt(e.target.value))}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Cancellation Policy</InputLabel>
                    <Select
                      value={business.cancellationPolicy}
                      label="Cancellation Policy"
                      onChange={(e) => handleBusinessChange('cancellationPolicy', e.target.value)}
                    >
                      <MenuItem value="flexible">Flexible</MenuItem>
                      <MenuItem value="moderate">Moderate</MenuItem>
                      <MenuItem value="strict">Strict</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Box mt={3}>
                <Button
                  variant="contained"
                  onClick={saveBusinessSettings}
                  disabled={saving}
                  startIcon={<Save />}
                >
                  Save Business Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Verification Dialog */}
      <Dialog open={verificationDialog} onClose={() => setVerificationDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Request Account Verification</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            Account verification helps build trust with customers and may increase your booking rates.
          </Typography>
          <Typography paragraph>
            Please prepare and email the following documents:
          </Typography>
          <List>
            <ListItem>
              <ListItemText primary="Government-issued ID (KTP/Passport)" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Business registration documents (SIUP/NIB)" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Proof of address (utility bill/bank statement)" />
            </ListItem>
            <ListItem>
              <ListItemText primary="Bank account details for payouts" />
            </ListItem>
          </List>

          <Divider sx={{ my: 2 }} />

          <Typography variant="h6" gutterBottom>
            Submit via Email
          </Typography>
          <Typography paragraph color="text.secondary">
            Please send all verification documents to:
          </Typography>

          <Paper sx={{ p: 2, bgcolor: 'grey.50', border: 1, borderColor: 'grey.200' }}>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box display="flex" alignItems="center" gap={1}>
                <Email color="primary" />
                <Typography variant="h6" color="primary">
                  <EMAIL>
                </Typography>
              </Box>
              <Tooltip title="Copy email address">
                <MuiIconButton
                  size="small"
                  onClick={() => {
                    navigator.clipboard.writeText('<EMAIL>');
                    setSnackbar({ open: true, message: 'Email address copied!', severity: 'success' });
                  }}
                >
                  <ContentCopy />
                </MuiIconButton>
              </Tooltip>
            </Box>
          </Paper>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Email Subject:</strong> Provider Verification Request - [Your Business Name]<br/>
              <strong>Include:</strong> Your account email, business name, and all required documents as attachments.
            </Typography>
          </Alert>

          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Our admin team will review your submission and notify you within 2-3 business days.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleVerificationAcknowledged} variant="contained">
            Got it
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ProviderSettings;
