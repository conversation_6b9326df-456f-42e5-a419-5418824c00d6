import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { 
  Elements, 
  CardElement, 
  useStripe, 
  useElements 
} from '@stripe/react-stripe-js';

import { 
  Button, 
  Typography, 
  Box, 
  CircularProgress, 
  Alert 
} from '@mui/material';

import { 
  BookingDetails, 
  PaymentMethod, 
  PaymentResult 
} from '../types';

import { 
  createPaymentIntent
} from '../services/PaymentService';

// Stripe configuration
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
if (!stripePublishableKey) {
  console.warn('⚠️ Stripe not configured. Please set VITE_STRIPE_PUBLISHABLE_KEY in your environment variables.');
}
const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

interface PaymentCheckoutProps {
  booking: BookingDetails;
  onPaymentSuccess: (payment: PaymentResult) => void;
  onPaymentError: (error: string) => void;
}

const PaymentCheckoutForm: React.FC<PaymentCheckoutProps> = ({ 
  booking, 
  onPaymentSuccess, 
  onPaymentError 
}) => {
  const stripe = useStripe();
  const elements = useElements();
  
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.Card);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Initialize payment
      const initResult = await createPaymentIntent(booking.id, paymentMethod);

      if (!initResult.success) {
        throw new Error(initResult.error || 'Payment initialization failed');
      }

      // Confirm payment with Stripe
      const cardElement = elements.getElement(CardElement);
      if (!cardElement) {
        throw new Error('Card element not found');
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(
        initResult.data?.clientSecret || '', 
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${booking.customer.firstName} ${booking.customer.lastName}`,
              email: booking.customer.email
            }
          }
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      // Payment successful
      onPaymentSuccess({
        success: true,
        paymentIntentId: paymentIntent?.id || '',
        paymentId: paymentIntent?.id || ''
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      onPaymentError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Box 
      component="form" 
      onSubmit={handleSubmit} 
      sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 2 
      }}
    >
      <Typography variant="h6">
        Payment Details
      </Typography>

      {/* Payment Method Selection */}
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button 
          variant={paymentMethod === PaymentMethod.Card ? 'contained' : 'outlined'}
          onClick={() => setPaymentMethod(PaymentMethod.Card)}
        >
          Credit Card
        </Button>
        <Button 
          variant={paymentMethod === 'paypal' ? 'contained' : 'outlined'}
          onClick={() => setPaymentMethod(PaymentMethod.PayPal)}
          disabled
        >
          PayPal
        </Button>
      </Box>

      {/* Card Input */}
      <CardElement 
        options={{
          style: {
            base: {
              fontSize: '16px',
              color: '#424770',
              '::placeholder': {
                color: '#aab7c4',
              },
            },
            invalid: {
              color: '#9e2146',
            },
          },
        }}
      />

      {/* Price Breakdown */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        mt: 2 
      }}>
        <Typography>Base Rental Cost:</Typography>
        <Typography>${booking.totalCost.toFixed(2)}</Typography>
      </Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between' 
      }}>
        <Typography>RentaHub Fee (15%):</Typography>
        <Typography>${(booking.totalCost * 0.15).toFixed(2)}</Typography>
      </Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        fontWeight: 'bold' 
      }}>
        <Typography variant="h6">Total:</Typography>
        <Typography variant="h6">
          ${(booking.totalCost * 1.15).toFixed(2)}
        </Typography>
      </Box>

      {/* Error Handling */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {/* Submit Button */}
      <Button 
        type="submit" 
        variant="contained" 
        color="primary" 
        disabled={isProcessing || !stripe}
        sx={{ mt: 2 }}
      >
        {isProcessing ? (
          <CircularProgress size={24} />
        ) : (
          'Pay Now'
        )}
      </Button>
    </Box>
  );
};

const PaymentCheckout: React.FC<PaymentCheckoutProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentCheckoutForm {...props} />
    </Elements>
  );
};

export default PaymentCheckout;
