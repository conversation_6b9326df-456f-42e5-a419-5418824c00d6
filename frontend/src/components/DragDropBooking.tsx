import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
  Paper,
  Grid,
  Tooltip,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  DragIndicator as DragIcon,
  Schedule as ScheduleIcon,
  CheckCircle as ConfirmIcon,
  Cancel as CancelIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  DirectionsBike as VehicleIcon,
  Person as CustomerIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { format, parseISO, differenceInDays, addDays, isSameDay, isBefore, isAfter } from 'date-fns';
import { Booking, Vehicle, User, BookingStatus } from '../types';

interface DragDropBookingProps {
  bookings: Booking[];
  vehicles: Vehicle[];
  onBookingReschedule: (bookingId: string, newStartDate: string, newEndDate: string) => void;
  onBookingUpdate: (bookingId: string, status: BookingStatus) => void;
  onBookingDelete: (bookingId: string) => void;
  loading?: boolean;
}

interface DraggedBooking {
  booking: Booking;
  originalPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
}

interface DropZone {
  id: string;
  date: Date;
  vehicleId?: string;
  bookings: Booking[];
  maxBookings: number;
}

export const DragDropBooking: React.FC<DragDropBookingProps> = ({
  bookings,
  vehicles,
  onBookingReschedule,
  onBookingUpdate,
  onBookingDelete,
  loading = false
}) => {
  const [draggedBooking, setDraggedBooking] = useState<DraggedBooking | null>(null);
  const [dropZones, setDropZones] = useState<DropZone[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false);
  const [isBookingDetailsModalOpen, setIsBookingDetailsModalOpen] = useState(false);
  const [rescheduleData, setRescheduleData] = useState({
    startDate: '',
    endDate: '',
    vehicleId: ''
  });
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  const dragRef = useRef<HTMLDivElement>(null);

  // Generate drop zones for the next 30 days
  useEffect(() => {
    const zones: DropZone[] = [];
    const today = new Date();

    for (let i = 0; i < 30; i++) {
      const date = addDays(today, i);
      const dayBookings = bookings.filter(booking => 
        isSameDay(parseISO(booking.startDate), date) ||
        isSameDay(parseISO(booking.endDate), date) ||
        (isAfter(parseISO(booking.startDate), date) && isBefore(parseISO(booking.endDate), date))
      );

      zones.push({
        id: `zone-${i}`,
        date,
        bookings: dayBookings,
        maxBookings: 5 // Maximum bookings per day
      });
    }

    setDropZones(zones);
  }, [bookings]);

  const handleDragStart = useCallback((event: React.DragEvent, booking: Booking) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setDraggedBooking({
      booking,
      originalPosition: { x: rect.left, y: rect.top },
      currentPosition: { x: event.clientX, y: event.clientY }
    });

    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', booking.id);
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDrop = useCallback((event: React.DragEvent, dropZone: DropZone) => {
    event.preventDefault();
    
    if (!draggedBooking) return;

    const booking = draggedBooking.booking;
    const newStartDate = format(dropZone.date, 'yyyy-MM-dd');
    const days = differenceInDays(parseISO(booking.endDate), parseISO(booking.startDate));
    const newEndDate = format(addDays(dropZone.date, days), 'yyyy-MM-dd');

    // Check for conflicts
    const conflicts = bookings.filter(b => 
      b.id !== booking.id &&
      b.vehicleId === booking.vehicleId &&
      ((isAfter(parseISO(newStartDate), parseISO(b.startDate)) && isBefore(parseISO(newStartDate), parseISO(b.endDate))) ||
       (isAfter(parseISO(newEndDate), parseISO(b.startDate)) && isBefore(parseISO(newEndDate), parseISO(b.endDate))) ||
       (isBefore(parseISO(newStartDate), parseISO(b.startDate)) && isAfter(parseISO(newEndDate), parseISO(b.endDate))))
    );

    if (conflicts.length > 0) {
      setSnackbar({
        open: true,
        message: 'Cannot reschedule: Vehicle is already booked for these dates',
        severity: 'error'
      });
      return;
    }

    // Confirm reschedule
    setRescheduleData({
      startDate: newStartDate,
      endDate: newEndDate,
      vehicleId: booking.vehicleId
    });
    setSelectedBooking(booking);
    setIsRescheduleModalOpen(true);
  }, [draggedBooking, bookings]);

  const handleDragEnd = useCallback(() => {
    setDraggedBooking(null);
  }, []);

  const handleRescheduleConfirm = async () => {
    if (!selectedBooking) return;

    try {
      await onBookingReschedule(
        selectedBooking.id,
        rescheduleData.startDate,
        rescheduleData.endDate
      );

      setSnackbar({
        open: true,
        message: 'Booking rescheduled successfully',
        severity: 'success'
      });

      setIsRescheduleModalOpen(false);
      setSelectedBooking(null);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to reschedule booking',
        severity: 'error'
      });
    }
  };

  const handleBookingAction = async (bookingId: string, action: 'approve' | 'reject' | 'delete') => {
    try {
      switch (action) {
        case 'approve':
          await onBookingUpdate(bookingId, 'confirmed');
          setSnackbar({
            open: true,
            message: 'Booking approved',
            severity: 'success'
          });
          break;
        case 'reject':
          await onBookingUpdate(bookingId, 'cancelled');
          setSnackbar({
            open: true,
            message: 'Booking rejected',
            severity: 'warning'
          });
          break;
        case 'delete':
          await onBookingDelete(bookingId);
          setSnackbar({
            open: true,
            message: 'Booking deleted',
            severity: 'success'
          });
          break;
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Action failed',
        severity: 'error'
      });
    }
  };

  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getVehicleName = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    return vehicle ? vehicle.name : 'Unknown Vehicle';
  };

  const getCustomerName = (userId: string) => {
    const booking = bookings.find(b => b.userId === userId);
    return booking?.user?.name || 'Unknown Customer';
  };

  const BookingCard: React.FC<{ booking: Booking; isDragging?: boolean }> = ({ 
    booking, 
    isDragging = false 
  }) => (
    <Card
      sx={{
        mb: 1,
        cursor: 'grab',
        opacity: isDragging ? 0.5 : 1,
        transform: isDragging ? 'rotate(5deg)' : 'none',
        transition: 'all 0.2s ease',
        '&:hover': {
          boxShadow: 3,
          transform: 'translateY(-2px)'
        }
      }}
      draggable
      onDragStart={(e) => handleDragStart(e, booking)}
      onDragEnd={handleDragEnd}
    >
      <CardContent sx={{ p: 2 }}>
        <Box display="flex" alignItems="center" gap={1} mb={1}>
          <DragIcon color="action" fontSize="small" />
          <Typography variant="subtitle2" noWrap>
            {getVehicleName(booking.vehicleId)}
          </Typography>
        </Box>
        
        <Typography variant="body2" color="text.secondary" mb={1}>
          {getCustomerName(booking.userId)}
        </Typography>
        
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Chip
            label={booking.status}
            color={getStatusColor(booking.status) as any}
            size="small"
          />
          <Typography variant="body2" color="primary">
            Rp {booking.totalAmount.toLocaleString()}
          </Typography>
        </Box>
        
        <Typography variant="caption" color="text.secondary">
          {format(parseISO(booking.startDate), 'MMM dd')} - {format(parseISO(booking.endDate), 'MMM dd')}
        </Typography>
      </CardContent>
    </Card>
  );

  const DropZoneCard: React.FC<{ dropZone: DropZone }> = ({ dropZone }) => (
    <Paper
      sx={{
        p: 2,
        minHeight: 120,
        border: '2px dashed',
        borderColor: 'divider',
        backgroundColor: 'background.paper',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: 'primary.main',
          backgroundColor: 'action.hover'
        }
      }}
      onDragOver={handleDragOver}
      onDrop={(e) => handleDrop(e, dropZone)}
    >
      <Typography variant="subtitle2" gutterBottom>
        {format(dropZone.date, 'MMM dd, yyyy')}
      </Typography>
      
      <Typography variant="caption" color="text.secondary" display="block" mb={1}>
        {dropZone.bookings.length} / {dropZone.maxBookings} bookings
      </Typography>
      
      <LinearProgress
        variant="determinate"
        value={(dropZone.bookings.length / dropZone.maxBookings) * 100}
        sx={{ mb: 1 }}
      />
      
      <Box>
        {dropZone.bookings.map((booking) => (
          <BookingCard key={booking.id} booking={booking} />
        ))}
      </Box>
    </Paper>
  );

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Drag & Drop Booking Management
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        Drag bookings to reschedule them. Drop them on any date to move the booking.
      </Alert>

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      <Grid container spacing={2}>
        {dropZones.map((dropZone) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={dropZone.id}>
            <DropZoneCard dropZone={dropZone} />
          </Grid>
        ))}
      </Grid>

      {/* Reschedule Confirmation Modal */}
      <Dialog open={isRescheduleModalOpen} onClose={() => setIsRescheduleModalOpen(false)}>
        <DialogTitle>Confirm Reschedule</DialogTitle>
        <DialogContent>
          {selectedBooking && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Reschedule booking for {getVehicleName(selectedBooking.vehicleId)}?
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                From: {format(parseISO(selectedBooking.startDate), 'MMM dd, yyyy')} - {format(parseISO(selectedBooking.endDate), 'MMM dd, yyyy')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                To: {format(parseISO(rescheduleData.startDate), 'MMM dd, yyyy')} - {format(parseISO(rescheduleData.endDate), 'MMM dd, yyyy')}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsRescheduleModalOpen(false)}>Cancel</Button>
          <Button onClick={handleRescheduleConfirm} variant="contained">
            Confirm Reschedule
          </Button>
        </DialogActions>
      </Dialog>

      {/* Booking Details Modal */}
      <Dialog 
        open={isBookingDetailsModalOpen} 
        onClose={() => setIsBookingDetailsModalOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Booking Details</DialogTitle>
        <DialogContent>
          {selectedBooking && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Vehicle Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><VehicleIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Vehicle"
                        secondary={getVehicleName(selectedBooking.vehicleId)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><MoneyIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Daily Rate"
                        secondary={`Rp ${selectedBooking.vehicle?.dailyRate?.toLocaleString()}`}
                      />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Customer Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><CustomerIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Customer"
                        secondary={selectedBooking.user?.name}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><EmailIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Email"
                        secondary={selectedBooking.user?.email}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><PhoneIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Phone"
                        secondary={selectedBooking.user?.phone || 'Not provided'}
                      />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Booking Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><CalendarIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Start Date"
                        secondary={format(parseISO(selectedBooking.startDate), 'MMM dd, yyyy')}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CalendarIcon /></ListItemIcon>
                      <ListItemText 
                        primary="End Date"
                        secondary={format(parseISO(selectedBooking.endDate), 'MMM dd, yyyy')}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><TimeIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Duration"
                        secondary={`${differenceInDays(parseISO(selectedBooking.endDate), parseISO(selectedBooking.startDate)) + 1} days`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><MoneyIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Total Amount"
                        secondary={`Rp ${selectedBooking.totalAmount.toLocaleString()}`}
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsBookingDetailsModalOpen(false)}>Close</Button>
          <Button 
            onClick={() => selectedBooking && handleBookingAction(selectedBooking.id, 'approve')}
            startIcon={<ConfirmIcon />}
            color="success"
          >
            Approve
          </Button>
          <Button 
            onClick={() => selectedBooking && handleBookingAction(selectedBooking.id, 'reject')}
            startIcon={<CancelIcon />}
            color="error"
          >
            Reject
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DragDropBooking; 