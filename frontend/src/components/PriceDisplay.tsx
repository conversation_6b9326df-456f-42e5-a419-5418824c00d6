/**
 * PriceDisplay Component - Automatically converts and formats prices
 * Shows prices in user's preferred currency with proper formatting
 */

import React, { useState, useEffect } from 'react';
import { Typography, Box, Chip, Skeleton } from '@mui/material';
import DynamicCurrencyService from '../services/DynamicCurrencyService';
import AutoTranslate from './AutoTranslate';

interface PriceDisplayProps {
  amount: number;
  currency?: string;
  period?: 'day' | 'week' | 'month' | 'hour' | 'total';
  showOriginal?: boolean;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit';
  fontWeight?: 'normal' | 'bold' | number;
  className?: string;
  style?: React.CSSProperties;
  showComparison?: boolean;
  originalCurrency?: string;
}

/**
 * PriceDisplay component that automatically converts and formats prices
 */
const PriceDisplay: React.FC<PriceDisplayProps> = ({
  amount,
  currency = 'USD',
  period,
  showOriginal = false,
  variant = 'body1',
  color = 'inherit',
  fontWeight = 'normal',
  className,
  style,
  showComparison = false,
  originalCurrency
}) => {
  const [formattedPrice, setFormattedPrice] = useState<string>('');
  const [originalPrice, setOriginalPrice] = useState<string>('');
  const [userCurrency, setUserCurrency] = useState<string>('USD');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPriceData = async () => {
      setIsLoading(true);

      try {
        // Get user's preferred currency
        const preferredCurrency = await DynamicCurrencyService.getUserCurrency();
        setUserCurrency(preferredCurrency);

        // Format price in user's currency
        const formatted = await DynamicCurrencyService.formatCurrency(
          amount,
          currency,
          preferredCurrency
        );
        setFormattedPrice(formatted);

        // Format original price if needed
        if (showOriginal || showComparison) {
          const original = await DynamicCurrencyService.formatCurrency(
            amount,
            currency,
            originalCurrency || currency
          );
          setOriginalPrice(original);
        }
      } catch (error) {
        console.warn('Price formatting failed:', error);
        // Fallback formatting
        setFormattedPrice(`${currency} ${amount.toFixed(2)}`);
        setOriginalPrice(`${currency} ${amount.toFixed(2)}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadPriceData();

    // Listen for currency changes
    const handleCurrencyChange = () => {
      loadPriceData();
    };

    window.addEventListener('currencyChanged', handleCurrencyChange);
    return () => {
      window.removeEventListener('currencyChanged', handleCurrencyChange);
    };
  }, [amount, currency, showOriginal, showComparison, originalCurrency]);

  if (isLoading) {
    return (
      <Skeleton
        variant="text"
        width={100}
        height={variant.startsWith('h') ? 40 : 24}
        className={className}
        style={style}
      />
    );
  }

  const periodText = period ? getPeriodText(period) : '';

  return (
    <Box className={className} style={style}>
      <Typography
        variant={variant}
        color={color}
        fontWeight={fontWeight}
        component="span"
      >
        {formattedPrice}
        {period && (
          <Typography
            component="span"
            variant="body2"
            color="text.secondary"
            sx={{ ml: 0.5 }}
          >
            <AutoTranslate>/{periodText}</AutoTranslate>
          </Typography>
        )}
      </Typography>

      {showComparison && userCurrency !== currency && (
        <Box sx={{ mt: 0.5 }}>
          <Chip
            label={`≈ ${originalPrice}`}
            size="small"
            variant="outlined"
            color="info"
            sx={{ fontSize: '0.75rem', height: 20 }}
          />
        </Box>
      )}

      {showOriginal && userCurrency !== currency && (
        <Typography
          variant="caption"
          color="text.secondary"
          display="block"
          sx={{ mt: 0.5 }}
        >
          <AutoTranslate>Original:</AutoTranslate> {originalPrice}
        </Typography>
      )}
    </Box>
  );
};

/**
 * Get localized period text
 */
function getPeriodText(period: string): string {
  const periodMap: { [key: string]: string } = {
    'hour': 'hour',
    'day': 'day',
    'week': 'week',
    'month': 'month',
    'total': 'total'
  };

  return periodMap[period] || period;
}

export default PriceDisplay; 