import React from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip
} from '@mui/material';

interface VehicleSpecificationsProps {
  make: string;
  model: string;
  year: number;
  category: string;
  engineSize: string;
  fuelType: string;
  transmission: string;
  onMakeChange: (make: string) => void;
  onModelChange: (model: string) => void;
  onYearChange: (year: number) => void;
  onCategoryChange: (category: string) => void;
  onEngineSizeChange: (engineSize: string) => void;
  onFuelTypeChange: (fuelType: string) => void;
  onTransmissionChange: (transmission: string) => void;
}

const VehicleSpecifications: React.FC<VehicleSpecificationsProps> = ({
  make,
  model,
  year,
  category,
  engineSize,
  fuelType,
  transmission,
  onMakeChange,
  onModelChange,
  onYearChange,
  onCategoryChange,
  onEngineSizeChange,
  onFuelTypeChange,
  onTransmissionChange
}) => {
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 30 }, (_, i) => currentYear - i);

  const categories = [
    'small_scooter',
    'large_scooter',
    'motorcycle',
    'car',
    'van',
    'truck'
  ];

  const fuelTypes = [
    'gasoline',
    'diesel',
    'electric',
    'hybrid'
  ];

  const transmissions = [
    'manual',
    'automatic',
    'cvt'
  ];

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Vehicle Specifications
      </Typography>

      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Make"
              value={make}
              onChange={(e) => onMakeChange(e.target.value)}
              placeholder="e.g., Honda"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Model"
              value={model}
              onChange={(e) => onModelChange(e.target.value)}
              placeholder="e.g., Scoopy"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Year</InputLabel>
              <Select
                value={year || ''}
                onChange={(e) => onYearChange(Number(e.target.value))}
                label="Year"
              >
                {yearOptions.map((yearOption) => (
                  <MenuItem key={yearOption} value={yearOption}>
                    {yearOption}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={category}
                onChange={(e) => onCategoryChange(e.target.value)}
                label="Category"
              >
                {categories.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    {cat.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Engine Size"
              value={engineSize}
              onChange={(e) => onEngineSizeChange(e.target.value)}
              placeholder="e.g., 110cc"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Fuel Type</InputLabel>
              <Select
                value={fuelType}
                onChange={(e) => onFuelTypeChange(e.target.value)}
                label="Fuel Type"
              >
                {fuelTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Transmission</InputLabel>
              <Select
                value={transmission}
                onChange={(e) => onTransmissionChange(e.target.value)}
                label="Transmission"
              >
                {transmissions.map((trans) => (
                  <MenuItem key={trans} value={trans}>
                    {trans.charAt(0).toUpperCase() + trans.slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            <strong>Specification Tips:</strong>
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            <Chip label="Accurate specs help customers" size="small" />
            <Chip label="Include all relevant details" size="small" />
            <Chip label="Verify information before listing" size="small" />
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default VehicleSpecifications; 