import React, { useState } from 'react'
import {
  Box,
  TextField,
  Button,
  Grid,
  MenuItem,
  Typography,
} from '@mui/material'
// Date picker imports temporarily removed to fix build issue
// import { DatePicker } from '@mui/x-date-pickers/DatePicker'
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { Search, LocationOn } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const SearchForm: React.FC = () => {
  const navigate = useNavigate()
  const [location, setLocation] = useState('')
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [endDate, setEndDate] = useState<string>(new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0])
  const [vehicleType, setVehicleType] = useState('')

  const locations = [
    'Canggu',
    'Seminyak',
    'Kuta',
    'Sanur',
    'Ubud',
    'Denpasar',
    'Jimbaran',
    'Nusa Dua',
    'Uluwatu',
    'Amed',
  ]

  const vehicleTypes = [
    { value: '', label: 'Any Vehicle Type' },
    { value: 'small_scooter', label: 'Small Scooter (50-125cc)' },
    { value: 'large_scooter', label: 'Large Scooter (150cc+)' },
    { value: 'luxury_bike', label: 'Luxury Motorbike' },
  ]

  const handleSearch = () => {
    const searchParams = new URLSearchParams()
    
    if (location) {
searchParams.set('location', location)
}
    if (startDate) {
searchParams.set('startDate', startDate)
}
    if (endDate) {
searchParams.set('endDate', endDate)
}
    if (vehicleType) {
searchParams.set('type', vehicleType)
}

    navigate(`/vehicles?${searchParams.toString()}`)
  }

  return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Find Your Perfect Ride
        </Typography>
        
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              InputProps={{
                startAdornment: <LocationOn sx={{ mr: 1, color: 'action.active' }} />,
              }}
            >
              <MenuItem value="">Any Location</MenuItem>
              {locations.map((loc) => (
                <MenuItem key={loc} value={loc}>
                  {loc}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              type="date"
              label="Start Date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <TextField
              fullWidth
              type="date"
              label="End Date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              select
              label="Vehicle Type"
              value={vehicleType}
              onChange={(e) => setVehicleType(e.target.value)}
            >
              {vehicleTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              size="large"
              startIcon={<Search />}
              onClick={handleSearch}
              sx={{ height: 56 }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Box>
  )
}

export default SearchForm