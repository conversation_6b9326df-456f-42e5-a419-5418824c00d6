import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardContent
} from '@mui/material'
import MapIcon from '@mui/icons-material/Map'
import { useTranslation } from 'react-i18next'
import AnalyticsService, {
  GeospatialVehicle
} from '../../services/AnalyticsService'

const GeospatialSearch: React.FC = () => {
  const { t } = useTranslation()
  const [latitude, setLatitude] = useState<string>('')
  const [longitude, setLongitude] = useState<string>('')
  const [radius, setRadius] = useState<string>('10')
  const [vehicles, setVehicles] = useState<GeospatialVehicle[]>([])
  const [error, setError] = useState<string | null>(null)

  // Attempt to get user's current location
  useEffect(() => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLatitude(position.coords.latitude.toString())
          setLongitude(position.coords.longitude.toString())
        },
        (error) => {
          console.error('Geolocation error:', error)
          setError(t('search.errors.location'))
        }
      )
    }
  }, [])

  const handleSearch = async () => {
    try {
      // Validate inputs
      const lat = parseFloat(latitude)
      const lon = parseFloat(longitude)
      const rad = parseFloat(radius)

      if (isNaN(lat) || isNaN(lon)) {
        setError(t('search.errors.invalidCoords'))
        return
      }

      // Perform geospatial search
      const nearbyVehicles = await AnalyticsService.findNearbyVehicles(
        lat, 
        lon, 
        rad
      )

      // Log the search
      await AnalyticsService.logGeospatialSearch(lat, lon, rad)

      setVehicles(nearbyVehicles)
      setError(null)
    } catch (err) {
      console.error('Search error:', err)
      setError(t('search.errors.searchFailed'))
    }
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <MapIcon sx={{ mr: 1 }} /> {t('search.nearby')}
      </Typography>

      {/* Search Inputs */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label={t('search.latitude')}
            value={latitude}
            onChange={(e) => setLatitude(e.target.value)}
            error={!!error}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            label={t('search.longitude')}
            value={longitude}
            onChange={(e) => setLongitude(e.target.value)}
            error={!!error}
          />
        </Grid>
        <Grid item xs={12} md={2}>
          <TextField
            fullWidth
            label={t('search.radius')}
            value={radius}
            onChange={(e) => setRadius(e.target.value)}
            type="number"
          />
        </Grid>
        <Grid item xs={12} md={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            fullWidth
            sx={{ height: '100%' }}
          >
            {t('search.searchButton')}
          </Button>
        </Grid>
      </Grid>

      {/* Error Handling */}
      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      {/* Search Results */}
      <Grid container spacing={2}>
        {vehicles.map((vehicle) => (
          <Grid item xs={12} md={4} key={vehicle.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{vehicle.name}</Typography>
                <Typography>
                  Distance: {vehicle.distance.toFixed(2)} km
                </Typography>
                <Typography>
                  Location: {vehicle.latitude}, {vehicle.longitude}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default GeospatialSearch 