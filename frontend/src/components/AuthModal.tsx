import React, { useState } from 'react';
import {
  Modal,
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import GoogleIcon from '@mui/icons-material/Google';
import GoogleAuthService from '../services/GoogleAuthService';
import { useNavigate } from 'react-router-dom';

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  mode: 'login' | 'register';
  redirectTo?: string;
}

export default function AuthModal({ open, onClose, mode, redirectTo = '/dashboard' }: AuthModalProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState<'CUSTOMER' | 'PROVIDER'>('CUSTOMER');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const { login, register } = useAuth();
  const navigate = useNavigate();

  const handleGoogleAuth = async () => {
    setLoading(true);
    setError('');
    try {
      // Use backend Google OAuth instead of Supabase
      GoogleAuthService.initiateGoogleAuth();
      // The page will redirect, so we don't need to handle the response here
    } catch (error: any) {
      setError(error.message || 'Google authentication failed');
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');
    setEmailError('');
    setPasswordError('');

    // Validate required fields
    let hasErrors = false;
    if (!email) {
      setEmailError('Email is required');
      hasErrors = true;
    }
    if (!password) {
      setPasswordError('Password is required');
      hasErrors = true;
    }

    if (hasErrors) {
      setLoading(false);
      return;
    }

    try {
      if (mode === 'register') {
        await register(email, password, {
          name,
          role,
          companyName: role === 'PROVIDER' ? name : null
        });
        setSuccess('Registration successful! Redirecting...');

        // Determine redirect URL based on role
        const redirectUrl = role === 'PROVIDER' ? '/provider-dashboard' : '/dashboard';
        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 1500);
      } else {
        await login(email, password);
        setSuccess('Login successful! Redirecting...');
        setTimeout(() => {
          window.location.href = redirectTo;
        }, 1500);
      }
    } catch (error: any) {
      if (error.message?.includes('already registered')) {
        setError('Email is already registered. Please log in or use a different email.');
      } else if (error.message?.toLowerCase().includes('password')) {
        setError('Password must be at least 6 characters.');
      } else {
        setError(error.message || `${mode === 'register' ? 'Registration' : 'Login'} failed`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 400,
        bgcolor: 'background.paper',
        boxShadow: 24,
        p: 4,
        borderRadius: 2
      }}>
        <Typography variant="h4" component="h2" gutterBottom align="center">
          {mode === 'login' ? 'Login' : 'Sign Up'}
        </Typography>

        {error && <Alert severity="error" sx={{ mb: 2 }} data-testid="error-message">{error}</Alert>}
        {success && <Alert severity="success" sx={{ mb: 2 }} data-testid="success-message">{success}</Alert>}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<GoogleIcon />}
                onClick={handleGoogleAuth}
                disabled={loading}
                sx={{ mb: 2 }}
              >
                {loading ? 'Processing...' : (mode === 'login' ? 'Login with Google' : 'Sign up with Google')}
              </Button>
            </Grid>
            {mode === 'register' && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Full Name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  inputProps={{ 'data-testid': 'full-name-input' }}
                />
              </Grid>
            )}
            
            {mode === 'register' && (
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Account Type</InputLabel>
                  <Select
                    value={role}
                    label="Account Type"
                    onChange={(e) => setRole(e.target.value as 'CUSTOMER' | 'PROVIDER')}
                    data-testid="role-select"
                  >
                    <MenuItem value="CUSTOMER">Customer (Rent Vehicles)</MenuItem>
                    <MenuItem value="PROVIDER">Service Provider (List Vehicles)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                error={!!emailError}
                helperText={emailError}
                inputProps={{ 'data-testid': 'email-input' }}
                FormHelperTextProps={{ 'data-testid': 'email-error' }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                error={!!passwordError}
                helperText={passwordError}
                inputProps={{ 'data-testid': 'password-input' }}
                FormHelperTextProps={{ 'data-testid': 'password-error' }}
              />
            </Grid>

            <Grid item xs={12}>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                disabled={loading}
                sx={{ mt: 2 }}
                data-testid="login-button"
              >
                {loading ? <CircularProgress size={24} /> : mode === 'login' ? 'Login' : 'Sign Up'}
              </Button>
            </Grid>

            {mode === 'login' && (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Button
                    variant="text"
                    size="small"
                    data-testid="forgot-password-link"
                    onClick={() => {
                      onClose();
                      navigate('/forgot-password');
                    }}
                  >
                    Forgot Password?
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </form>
      </Box>
    </Modal>
  );
}