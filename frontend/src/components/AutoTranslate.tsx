/**
 * AutoTranslate Component - Automatically translates any text content
 * Works like Google Translate - just wrap any text and it gets translated
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import DynamicTranslationService from '../services/DynamicTranslationService';

interface AutoTranslateProps {
  children: React.ReactNode;
  text?: string;
  fallback?: string;
  className?: string;
  style?: React.CSSProperties;
  component?: keyof JSX.IntrinsicElements;
  [key: string]: any; // Allow any other props to pass through
}

/**
 * AutoTranslate component that automatically translates text content
 * 
 * Usage examples:
 * <AutoTranslate>Hello World</AutoTranslate>
 * <AutoTranslate text="Search Vehicles" />
 * <AutoTranslate component="h1">Welcome to RentaHub</AutoTranslate>
 */
const AutoTranslate: React.FC<AutoTranslateProps> = ({
  children,
  text,
  fallback,
  className,
  style,
  component: Component = 'span',
  ...props
}) => {
  const { i18n } = useTranslation();
  const [translatedText, setTranslatedText] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Extract text content from children or use provided text
  const sourceText = text || (typeof children === 'string' ? children : extractTextFromChildren(children));

  useEffect(() => {
    if (!sourceText) {
      setTranslatedText('');
      return;
    }

    // If current language is English, no translation needed
    if (i18n.language === 'en') {
      setTranslatedText(sourceText);
      return;
    }

    setIsLoading(true);
    
    DynamicTranslationService.translate(sourceText, i18n.language)
      .then(translated => {
        setTranslatedText(translated);
      })
      .catch(error => {
        console.warn('Translation failed:', error);
        setTranslatedText(fallback || sourceText);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [sourceText, i18n.language, fallback]);

  // Show loading state or translated text
  const displayText = isLoading ? (sourceText || '...') : (translatedText || sourceText || fallback || '');

  return (
    <Component 
      className={className} 
      style={style}
      {...props}
    >
      {displayText}
    </Component>
  );
};

/**
 * Hook version for programmatic use
 */
export const useAutoTranslate = () => {
  const { i18n } = useTranslation();
  
  const translate = async (text: string): Promise<string> => {
    if (!text || i18n.language === 'en') return text;
    
    try {
      return await DynamicTranslationService.translate(text, i18n.language);
    } catch (error) {
      console.warn('Translation failed:', error);
      return text;
    }
  };

  const translateBatch = async (texts: string[]): Promise<string[]> => {
    if (i18n.language === 'en') return texts;
    
    try {
      return await DynamicTranslationService.translateBatch(texts, i18n.language);
    } catch (error) {
      console.warn('Batch translation failed:', error);
      return texts;
    }
  };

  return { translate, translateBatch, currentLang: i18n.language };
};

/**
 * Higher-order component for automatic translation
 */
export const withAutoTranslate = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const { translate } = useAutoTranslate();
    
    return (
      <WrappedComponent
        {...props}
        ref={ref}
        translate={translate}
      />
    );
  });
};

/**
 * Batch translation component for multiple texts
 */
interface AutoTranslateBatchProps {
  texts: string[];
  render: (translatedTexts: string[]) => React.ReactNode;
  fallback?: React.ReactNode;
}

export const AutoTranslateBatch: React.FC<AutoTranslateBatchProps> = ({
  texts,
  render,
  fallback
}) => {
  const { i18n } = useTranslation();
  const [translatedTexts, setTranslatedTexts] = useState<string[]>(texts);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (i18n.language === 'en') {
      setTranslatedTexts(texts);
      return;
    }

    setIsLoading(true);
    
    DynamicTranslationService.translateBatch(texts, i18n.language)
      .then(translated => {
        setTranslatedTexts(translated);
      })
      .catch(error => {
        console.warn('Batch translation failed:', error);
        setTranslatedTexts(texts);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [texts, i18n.language]);

  if (isLoading && fallback) {
    return <>{fallback}</>;
  }

  return <>{render(translatedTexts)}</>;
};

/**
 * Utility function to extract text from React children
 */
function extractTextFromChildren(children: React.ReactNode): string {
  if (typeof children === 'string') {
    return children;
  }
  
  if (typeof children === 'number') {
    return children.toString();
  }
  
  if (React.isValidElement(children)) {
    return extractTextFromChildren(children.props.children);
  }
  
  if (Array.isArray(children)) {
    return children.map(child => extractTextFromChildren(child)).join(' ');
  }
  
  return '';
}

/**
 * Smart translation component that preserves React elements
 */
interface SmartTranslateProps {
  children: React.ReactNode;
  preserveElements?: boolean;
}

export const SmartTranslate: React.FC<SmartTranslateProps> = ({
  children,
  preserveElements = true
}) => {
  const { i18n } = useTranslation();
  const [translatedContent, setTranslatedContent] = useState<React.ReactNode>(children);

  useEffect(() => {
    if (i18n.language === 'en') {
      setTranslatedContent(children);
      return;
    }

    if (preserveElements && React.isValidElement(children)) {
      // For complex React elements, only translate text nodes
      setTranslatedContent(children); // TODO: Implement deep translation
    } else {
      // For simple text, translate directly
      const text = extractTextFromChildren(children);
      if (text) {
        DynamicTranslationService.translate(text, i18n.language)
          .then(translated => {
            setTranslatedContent(translated);
          })
          .catch(() => {
            setTranslatedContent(children);
          });
      }
    }
  }, [children, i18n.language, preserveElements]);

  return <>{translatedContent}</>;
};

export default AutoTranslate;
