import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  IconButton,
  Badge,
  Chip,
  Paper,
  InputAdornment,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Send as SendIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  AttachFile as AttachFileIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import NotificationService from '../services/NotificationService';
import { MessageThread, Message, User } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface CommunicationPanelProps {
  open: boolean;
  onClose: () => void;
  bookingId?: string;
  vehicleId?: string;
  recipientUser?: User;
}

const CommunicationPanel: React.FC<CommunicationPanelProps> = ({
  open,
  onClose,
  bookingId,
  vehicleId,
  recipientUser
}) => {
  const { user } = useAuth();
  const [threads, setThreads] = useState<MessageThread[]>([]);
  const [selectedThread, setSelectedThread] = useState<MessageThread | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load message threads on component mount
  useEffect(() => {
    if (open && user) {
      loadThreads();
    }
  }, [open, user]);

  // Load messages when thread is selected
  useEffect(() => {
    if (selectedThread) {
      loadMessages(selectedThread.id);
    }
  }, [selectedThread]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Listen for real-time message updates
  useEffect(() => {
    const handleNewMessage = (event: CustomEvent) => {
      const notification = event.detail;
      if (notification.type === 'MESSAGE_RECEIVED') {
        loadThreads(); // Refresh threads
        if (selectedThread && notification.data?.threadId === selectedThread.id) {
          loadMessages(selectedThread.id); // Refresh current thread messages
        }
      }
    };

    window.addEventListener('notification-received', handleNewMessage as EventListener);
    return () => {
      window.removeEventListener('notification-received', handleNewMessage as EventListener);
    };
  }, [selectedThread]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadThreads = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const threadsData = await NotificationService.getMessageThreads(user.id);
      setThreads(threadsData);
      
      // Auto-select thread if booking or vehicle context
      if (bookingId || vehicleId) {
        const contextThread = threadsData.find(thread => 
          thread.booking?.id === bookingId || 
          thread.vehicle?.id === vehicleId
        );
        if (contextThread) {
          setSelectedThread(contextThread);
        }
      }
    } catch (error) {
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (threadId: string) => {
    setLoading(true);
    try {
      const messagesData = await NotificationService.getMessages(threadId);
      setMessages(messagesData);
      
      // Mark messages as read
      const unreadMessageIds = messagesData
        .filter(msg => !msg.isRead && msg.senderId !== user?.id)
        .map(msg => msg.id);
      
      if (unreadMessageIds.length > 0) {
        await Promise.all(
          unreadMessageIds.map(id => NotificationService.markMessageAsRead(id))
        );
      }
    } catch (error) {
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !user) return;

    setSending(true);
    try {
      let threadId = selectedThread?.id;
      
      // Create new thread if needed
      if (!selectedThread && recipientUser) {
        const messageData = await NotificationService.sendMessage({
          toUserId: recipientUser.id,
          content: newMessage,
          bookingId,
          vehicleId
        });
        
        if (messageData) {
          setNewMessage('');
          loadThreads(); // Refresh to show new thread
        }
      } else if (threadId) {
        const messageData = await NotificationService.sendMessage({
          threadId,
          toUserId: '', // Will be determined by thread
          content: newMessage
        });
        
        if (messageData) {
          setNewMessage('');
          loadMessages(threadId); // Refresh current thread
        }
      }
    } catch (error) {
      setError('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const toggleStar = async (messageId: string, starred: boolean) => {
    try {
      await NotificationService.toggleMessageStar(messageId, !starred);
      if (selectedThread) {
        loadMessages(selectedThread.id);
      }
    } catch (error) {
      setError('Failed to update message');
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      await NotificationService.deleteMessage(messageId);
      if (selectedThread) {
        loadMessages(selectedThread.id);
      }
    } catch (error) {
      setError('Failed to delete message');
    }
  };

  const filteredThreads = threads.filter(thread =>
    searchQuery === '' || 
    thread.participants.some(p => 
      p.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      p.lastName.toLowerCase().includes(searchQuery.toLowerCase())
    ) ||
    thread.lastMessage?.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh', maxHeight: 600 }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Messages</Typography>
          <Box>
            <IconButton onClick={loadThreads} disabled={loading}>
              <RefreshIcon />
            </IconButton>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0, height: '100%' }}>
        <Box display="flex" height="100%">
          {/* Thread List */}
          <Box width="40%" borderRight={1} borderColor="divider">
            <Box p={2}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            
            <List sx={{ height: 'calc(100% - 80px)', overflow: 'auto' }}>
              {loading && threads.length === 0 ? (
                <Box display="flex" justifyContent="center" p={2}>
                  <CircularProgress size={24} />
                </Box>
              ) : filteredThreads.length === 0 ? (
                <ListItem>
                  <ListItemText 
                    primary="No conversations"
                    secondary="Start a new conversation"
                  />
                </ListItem>
              ) : (
                filteredThreads.map((thread) => (
                  <ListItem
                    key={thread.id}
                    button
                    selected={selectedThread?.id === thread.id}
                    onClick={() => setSelectedThread(thread)}
                  >
                    <ListItemAvatar>
                      <Badge
                        badgeContent={thread.unreadCount}
                        color="primary"
                        invisible={thread.unreadCount === 0}
                      >
                        <Avatar>
                          {thread.participants
                            .find(p => p.id !== user?.id)
                            ?.firstName?.[0] || '?'}
                        </Avatar>
                      </Badge>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" justifyContent="space-between">
                          <Typography variant="subtitle2">
                            {thread.participants
                              .find(p => p.id !== user?.id)
                              ?.firstName} {thread.participants
                              .find(p => p.id !== user?.id)
                              ?.lastName}
                          </Typography>
                          {thread.booking && (
                            <Chip
                              label="Booking"
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Typography
                          variant="body2"
                          color="textSecondary"
                          noWrap
                        >
                          {thread.lastMessage?.content || 'No messages yet'}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))
              )}
            </List>
          </Box>

          {/* Message View */}
          <Box flex={1} display="flex" flexDirection="column">
            {selectedThread ? (
              <>
                {/* Thread Header */}
                <Box
                  p={2}
                  borderBottom={1}
                  borderColor="divider"
                  bgcolor="grey.50"
                >
                  <Typography variant="h6">
                    {selectedThread.participants
                      .find(p => p.id !== user?.id)
                      ?.firstName} {selectedThread.participants
                      .find(p => p.id !== user?.id)
                      ?.lastName}
                  </Typography>
                  {selectedThread.booking && (
                    <Typography variant="body2" color="textSecondary">
                      Booking: {selectedThread.booking.id}
                    </Typography>
                  )}
                  {selectedThread.vehicle && (
                    <Typography variant="body2" color="textSecondary">
                      Vehicle: {selectedThread.vehicle.make} {selectedThread.vehicle.model}
                    </Typography>
                  )}
                </Box>

                {/* Messages */}
                <Box
                  flex={1}
                  overflow="auto"
                  p={1}
                  sx={{ maxHeight: 'calc(100% - 160px)' }}
                >
                  {messages.map((message) => (
                    <Box
                      key={message.id}
                      display="flex"
                      justifyContent={
                        message.senderId === user?.id ? 'flex-end' : 'flex-start'
                      }
                      mb={1}
                    >
                      <Paper
                        elevation={1}
                        sx={{
                          p: 2,
                          maxWidth: '70%',
                          bgcolor: message.senderId === user?.id 
                            ? 'primary.main' 
                            : 'grey.100',
                          color: message.senderId === user?.id 
                            ? 'white' 
                            : 'text.primary',
                        }}
                      >
                        <Typography variant="body2">
                          {message.content}
                        </Typography>
                        <Box
                          display="flex"
                          justifyContent="space-between"
                          alignItems="center"
                          mt={1}
                        >
                          <Typography
                            variant="caption"
                            sx={{
                              opacity: 0.7,
                              color: message.senderId === user?.id 
                                ? 'inherit' 
                                : 'text.secondary'
                            }}
                          >
                            {formatDistanceToNow(new Date(message.createdAt), {
                              addSuffix: true
                            })}
                          </Typography>
                          <Box>
                            <IconButton
                              size="small"
                              onClick={() => toggleStar(message.id, message.isStarred)}
                              sx={{ 
                                color: message.senderId === user?.id 
                                  ? 'inherit' 
                                  : 'text.secondary',
                                p: 0.5
                              }}
                            >
                              {message.isStarred ? (
                                <StarIcon fontSize="small" />
                              ) : (
                                <StarBorderIcon fontSize="small" />
                              )}
                            </IconButton>
                            {message.senderId === user?.id && (
                              <IconButton
                                size="small"
                                onClick={() => deleteMessage(message.id)}
                                sx={{ 
                                  color: 'inherit',
                                  p: 0.5
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            )}
                          </Box>
                        </Box>
                      </Paper>
                    </Box>
                  ))}
                  <div ref={messagesEndRef} />
                </Box>

                {/* Message Input */}
                <Box p={2} borderTop={1} borderColor="divider">
                  <Box display="flex" gap={1}>
                    <TextField
                      fullWidth
                      multiline
                      maxRows={3}
                      placeholder="Type your message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendMessage();
                        }
                      }}
                      disabled={sending}
                    />
                    <IconButton
                      color="primary"
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || sending}
                    >
                      {sending ? (
                        <CircularProgress size={24} />
                      ) : (
                        <SendIcon />
                      )}
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                height="100%"
              >
                <Typography variant="body1" color="textSecondary">
                  Select a conversation to start messaging
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>

      {error && (
        <DialogActions>
          <Alert severity="error" sx={{ width: '100%' }}>
            {error}
          </Alert>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default CommunicationPanel;
