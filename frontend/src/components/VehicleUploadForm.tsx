import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  IconButton,
  Chip,
  InputAdornment,
  FormControlLabel,
  Switch,
  Divider,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Autocomplete,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  PhotoCamera,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CloudUpload as UploadIcon,
  LocationOn as LocationIcon,
  AttachMoney as MoneyIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  DirectionsBike as BikeIcon,
} from '@mui/icons-material';
// import { LoadingButton } from '@mui/lab';
import { toast } from 'react-toastify';
import { supabase } from '../utils/supabaseClient';
import { VehicleService, Vehicle } from '../services/VehicleService';

interface VehicleFormData {
  // Basic Information
  category: 'small_scooter' | 'large_scooter' | 'luxury_bike';
  make: string;
  model: string;
  year: number;
  engine_size: number;
  transmission: 'automatic' | 'manual';
  fuel_type: 'petrol' | 'electric';
  description: string;
  
  // Pricing
  daily_rate: number;
  weekly_rate: number;
  monthly_rate: number;
  yearly_rate?: number;
  security_deposit: number;
  
  // Availability
  minimum_rental_days: number;
  maximum_rental_days: number;
  quantity: number;
  
  // Delivery
  delivery_available: boolean;
  delivery_fee: number;
  delivery_radius: number;
  
  // Location
  location: {
    address: string;
    city: string;
  };
  
  // Instructions
  pickup_instructions?: string;
  rental_agreement?: string;
  
  // Features & Add-ons
  features: string[];
  add_ons: string[];
  
  // Status
  active: boolean;
  
  // Images
  images: string[];
}

const vehicleCategories = [
  { value: 'small_scooter', label: 'Small Scooter (50-125cc)' },
  { value: 'large_scooter', label: 'Large Scooter (125-300cc)' },
  { value: 'luxury_bike', label: 'Luxury Bike (300cc+)' },
];

// Vehicle database for auto-suggestions
const vehicleDatabase = {
  scooter: {
    Honda: ['PCX 150', 'Forza 300', 'SH150i', 'SH300i', 'ADV150', 'Vario 150'],
    Yamaha: ['NMAX 155', 'XMAX 300', 'Aerox 155', 'Lexi 125', 'Mio Gear', 'Freego'],
    Suzuki: ['Burgman 200', 'Address 110', 'Nex II', 'Skydrive', 'Spin 125'],
    Kawasaki: ['J300', 'J125', 'J300 ABS'],
    Piaggio: ['Vespa Primavera', 'Vespa Sprint', 'Vespa GTS', 'Beverly 300', 'Liberty 150'],
    Kymco: ['Like 150i', 'X-Town 300i', 'Agility 125', 'People S 300i'],
    SYM: ['Jet 14', 'Citycom 300i', 'Maxsym 400i', 'Fiddle III'],
    Benelli: ['Zafferano 250', 'Caffenero 150'],
  },
  motorcycle: {
    Honda: ['CB150R', 'CBR150R', 'CRF150L', 'CB650R', 'CBR650R', 'Africa Twin', 'Gold Wing'],
    Yamaha: ['YZF-R15', 'MT-15', 'YZF-R25', 'MT-25', 'YZF-R3', 'MT-03', 'YZF-R6', 'MT-07', 'MT-09'],
    Suzuki: ['GSX-R150', 'GSX-S150', 'V-Strom 250', 'GSX-R600', 'GSX-R750', 'Hayabusa'],
    Kawasaki: ['Ninja 250', 'Z250', 'Ninja 400', 'Z400', 'Ninja 650', 'Z650', 'Ninja ZX-6R'],
    KTM: ['Duke 200', 'RC 200', 'Duke 390', 'RC 390', 'Duke 790', '1290 Super Duke R'],
    BMW: ['G310R', 'G310GS', 'S1000RR', 'R1250GS', 'F850GS'],
    Ducati: ['Panigale V2', 'Monster 821', 'Scrambler Icon', 'Multistrada V4'],
    Harley: ['Iron 883', 'Forty-Eight', 'Street 750', 'Fat Boy', 'Road King'],
  }
};

const availableFeatures = [
  'GPS Tracking',
  'Anti-theft System',
  'Bluetooth Speaker',
  'USB Charging',
  'Storage Compartment',
  'Windshield',
  'Heated Grips',
  'LED Lighting',
];

const availableAddOns = [
  'Helmet',
  'Rain Gear',
  'Phone Mount',
  'Child Seat',
  'Cargo Box',
  'Insurance',
  'Roadside Assistance',
  'Fuel Card',
];

interface VehicleUploadFormProps {
  onClose: () => void;
  onSuccess?: (vehicle: any) => void;
}

const STORAGE_KEY = 'vehicle-upload-form-data';

const VehicleUploadForm: React.FC<VehicleUploadFormProps> = ({ onClose, onSuccess }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  
  const [formData, setFormData] = useState<VehicleFormData>({
    category: 'small_scooter',
    make: '',
    model: '',
    year: new Date().getFullYear(),
    engine_size: 125,
    transmission: 'automatic',
    fuel_type: 'petrol',
    description: '',
    daily_rate: 0,
    weekly_rate: 0,
    monthly_rate: 0,
    security_deposit: 0,
    minimum_rental_days: 1,
    maximum_rental_days: 30,
    quantity: 1,
    delivery_available: false,
    delivery_fee: 0,
    delivery_radius: 0,
    location: {
      address: '',
      city: '',
    },
    pickup_instructions: '',
    rental_agreement: '',
    features: [],
    add_ons: [],
    active: true,
    images: [],
  });

  // Auto-complete state
  const [makeOptions, setMakeOptions] = useState<string[]>([]);
  const [modelOptions, setModelOptions] = useState<string[]>([]);
  const [existingVehicles, setExistingVehicles] = useState<Vehicle[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [makeInputValue, setMakeInputValue] = useState('');
  const [modelInputValue, setModelInputValue] = useState('');

  // Validation and manual entry state
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [manualEntryMode, setManualEntryMode] = useState<Record<string, boolean>>({});

  // Load saved form data from sessionStorage if present
  useEffect(() => {
    const saved = sessionStorage.getItem(STORAGE_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setFormData((prev) => ({ ...prev, ...parsed }));
      } catch {}
    }
  }, []);

  // Save form data to sessionStorage on every change
  useEffect(() => {
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
  }, [formData]);

  // Clear storage on submit or close
  const handleClose = () => {
    sessionStorage.removeItem(STORAGE_KEY);
    onClose();
  };

  const steps = [
    'Basic Information',
    'Pricing & Availability',
    'Location & Delivery',
    'Features & Add-ons',
    'Images & Final Details'
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Real-time validation
    handleFieldValidation(field, value);
  };

  const handleLocationChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value
      }
    }));

    // Real-time validation for location fields
    handleFieldValidation(`location.${field}`, value);
  };

  // Auto-complete logic
  useEffect(() => {
    const loadExistingVehicles = async () => {
      try {
        setSearchLoading(true);
        const vehicles = await VehicleService.getAllVehicles();
        setExistingVehicles(vehicles);
      } catch (error) {
        console.error('Error loading existing vehicles:', error);
      } finally {
        setSearchLoading(false);
      }
    };

    loadExistingVehicles();
  }, []);

  // Update make options based on category
  useEffect(() => {
    const categoryType = formData.category === 'luxury_bike' ? 'motorcycle' : 'scooter';
    const makes = Object.keys(vehicleDatabase[categoryType]);

    // Add makes from existing vehicles
    const existingMakes = [...new Set(existingVehicles.map(v => v.make))];
    const allMakes = [...new Set([...makes, ...existingMakes])].sort();

    setMakeOptions(allMakes);

    // Reset model if make changes
    if (formData.make && !allMakes.includes(formData.make)) {
      handleInputChange('make', '');
      handleInputChange('model', '');
    }
  }, [formData.category, existingVehicles]);

  // Update model options based on selected make
  useEffect(() => {
    if (!formData.make) {
      setModelOptions([]);
      return;
    }

    const categoryType = formData.category === 'luxury_bike' ? 'motorcycle' : 'scooter';
    const predefinedModels = vehicleDatabase[categoryType][formData.make] || [];

    // Add models from existing vehicles with same make
    const existingModels = existingVehicles
      .filter(v => v.make === formData.make)
      .map(v => v.model);

    const allModels = [...new Set([...predefinedModels, ...existingModels])].sort();
    setModelOptions(allModels);
  }, [formData.make, existingVehicles]);

  // Handle make selection
  const handleMakeChange = (event: any, newValue: string | null) => {
    handleInputChange('make', newValue || '');
    handleInputChange('model', ''); // Reset model when make changes
    setMakeInputValue(newValue || '');
  };

  // Handle model selection
  const handleModelChange = (event: any, newValue: string | null) => {
    handleInputChange('model', newValue || '');
    setModelInputValue(newValue || '');

    // Auto-fill some details if we have existing data
    if (newValue && formData.make) {
      const existingVehicle = existingVehicles.find(
        v => v.make === formData.make && v.model === newValue
      );

      if (existingVehicle) {
        // Pre-fill some common fields
        if (!formData.engine_size || formData.engine_size === 125) {
          handleInputChange('engine_size', existingVehicle.engine_size);
        }
        if (!formData.transmission || formData.transmission === 'automatic') {
          handleInputChange('transmission', existingVehicle.transmission);
        }
        if (!formData.fuel_type || formData.fuel_type === 'petrol') {
          handleInputChange('fuel_type', existingVehicle.fuel_type);
        }
      }
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length + imageFiles.length > 10) {
      toast.error('Maximum 10 images allowed');
      return;
    }

    const newFiles = [...imageFiles, ...files];
    setImageFiles(newFiles);

    // Create previews
    const newPreviews = [...imagePreviews];
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviews.push(e.target?.result as string);
        setImagePreviews([...newPreviews]);
      }
      reader.readAsDataURL(file);
    });

    // Update form
    const imageUrls = newFiles.map((_, index) => `preview_${index}`);
    handleInputChange('images', imageUrls);
  };

  const removeImage = (index: number) => {
    const newFiles = imageFiles.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setImageFiles(newFiles);
    setImagePreviews(newPreviews);
    
    const imageUrls = newFiles.map((_, i) => `preview_${i}`);
    handleInputChange('images', imageUrls);
  };

  const handleFeatureToggle = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  const handleAddOnToggle = (addOn: string) => {
    setFormData(prev => ({
      ...prev,
      add_ons: prev.add_ons.includes(addOn)
        ? prev.add_ons.filter(a => a !== addOn)
        : [...prev.add_ons, addOn]
    }));
  };

  // Field validation functions
  const validateField = (fieldName: string, value: any): string => {
    switch (fieldName) {
      case 'make':
        return !value ? 'Vehicle make is required' : '';
      case 'model':
        return !value ? 'Vehicle model is required' : '';
      case 'year':
        const currentYear = new Date().getFullYear();
        if (!value) return 'Year is required';
        if (value < 1990 || value > currentYear + 1) return `Year must be between 1990 and ${currentYear + 1}`;
        return '';
      case 'engine_size':
        if (!value) return 'Engine size is required';
        if (value < 50 || value > 2000) return 'Engine size must be between 50cc and 2000cc';
        return '';
      case 'description':
        if (!value) return 'Description is required';
        if (value.length < 20) return 'Description must be at least 20 characters';
        return '';
      case 'daily_rate':
        if (!value || value <= 0) return 'Daily rate must be greater than 0';
        return '';
      case 'weekly_rate':
        if (!value || value <= 0) return 'Weekly rate must be greater than 0';
        return '';
      case 'monthly_rate':
        if (!value || value <= 0) return 'Monthly rate must be greater than 0';
        return '';
      case 'security_deposit':
        if (!value || value < 0) return 'Security deposit is required';
        return '';
      case 'minimum_rental_days':
        if (!value || value < 1) return 'Minimum rental days must be at least 1';
        return '';
      case 'maximum_rental_days':
        if (!value || value < 1) return 'Maximum rental days must be at least 1';
        if (value < formData.minimum_rental_days) return 'Maximum must be greater than minimum';
        return '';
      case 'quantity':
        if (!value || value < 1) return 'Quantity must be at least 1';
        return '';
      case 'location.address':
        return !value ? 'Address is required' : '';
      case 'location.city':
        return !value ? 'City is required' : '';
      default:
        return '';
    }
  };

  // Real-time field validation
  const handleFieldValidation = (fieldName: string, value: any) => {
    const error = validateField(fieldName, value);
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  };

  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {};
    let isValid = true;

    switch (step) {
      case 0: // Basic Information
        ['make', 'model', 'year', 'engine_size', 'description'].forEach(field => {
          const error = validateField(field, formData[field as keyof VehicleFormData]);
          if (error) {
            errors[field] = error;
            isValid = false;
          }
        });
        break;
      case 1: // Pricing & Availability
        ['daily_rate', 'weekly_rate', 'monthly_rate', 'security_deposit', 'minimum_rental_days', 'maximum_rental_days', 'quantity'].forEach(field => {
          const error = validateField(field, formData[field as keyof VehicleFormData]);
          if (error) {
            errors[field] = error;
            isValid = false;
          }
        });
        break;
      case 2: // Location & Delivery
        ['location.address', 'location.city'].forEach(field => {
          const value = field === 'location.address' ? formData.location.address : formData.location.city;
          const error = validateField(field, value);
          if (error) {
            errors[field] = error;
            isValid = false;
          }
        });
        break;
      case 3: // Features & Add-ons
        return true; // Optional step
      case 4: // Images & Final Details
        if (imageFiles.length === 0) {
          errors['images'] = 'At least one image is required';
          isValid = false;
        }
        break;
      default:
        return false;
    }

    setFieldErrors(errors);
    return isValid;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Add robust null/undefined checks and error handling for Supabase upload and getPublicUrl calls
  // Show user-friendly error if response is undefined or missing expected structure
  const uploadImages = async (vehicleId: string): Promise<string[]> => {
    const uploadedUrls: string[] = [];
    for (let i = 0; i < imageFiles.length; i++) {
      const file = imageFiles[i];
      const fileName = `${Date.now()}_${file.name}`;
      const filePath = `vehicles/${vehicleId}/${fileName}`;
      let uploadError, publicUrl;
      try {
        const uploadRes = await supabase?.storage.from('vehicle-images').upload(filePath, file);
        if (!uploadRes || uploadRes.error) {
          throw new Error(uploadRes?.error?.message || 'Image upload failed');
        }
        const publicRes = await supabase?.storage.from('vehicle-images').getPublicUrl(filePath);
        if (!publicRes || !publicRes.data || !publicRes.data.publicUrl) {
          throw new Error('Failed to get public URL for uploaded image');
        }
        publicUrl = publicRes.data.publicUrl;
        uploadedUrls.push(publicUrl);
      } catch (err: any) {
        if (process.env.NODE_ENV === 'production') {
          toast.error('Image upload failed. Please try again or contact support.');
        } else {
          toast.error(`Image upload error: ${err.message}`);
          console.error('Image upload error:', err);
        }
        break;
      }
    }
    return uploadedUrls;
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      // Get current user
      const { data: { user } } = await supabase?.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create vehicle first
      const { data: vehicle, error: vehicleError } = await supabase
        .from('vehicles')
        .insert({
          ...formData,
          provider_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (vehicleError) {
        throw new Error(vehicleError.message);
      }

      // Upload images
      let imageUrls: string[] = [];
      if (imageFiles.length > 0) {
        imageUrls = await uploadImages(vehicle.id);
        
        // Update vehicle with image URLs
        await supabase
          .from('vehicles')
          .update({ images: imageUrls })
          .eq('id', vehicle.id);
      }

      toast.success('Vehicle created successfully!');
      onSuccess?.(vehicle);
      sessionStorage.removeItem(STORAGE_KEY);
      setLoading(false);
      onClose();
    } catch (error: any) {
      console.error('Error creating vehicle:', error);
      toast.error(error.message || 'Failed to create vehicle');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Add New Vehicle
      </Typography>

      <Stepper activeStep={activeStep} orientation="vertical" sx={{ mb: 3 }}>
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                {index === 0 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Vehicle Category</InputLabel>
                        <Select
                          value={formData.category}
                          onChange={(e) => handleInputChange('category', e.target.value)}
                          label="Vehicle Category"
                        >
                          {vehicleCategories.map(cat => (
                            <MenuItem key={cat.value} value={cat.value}>
                              {cat.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Box>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="body2">Vehicle Make</Typography>
                          <Chip
                            label={manualEntryMode.make ? "Manual Entry" : "Suggestions"}
                            size="small"
                            color={manualEntryMode.make ? "secondary" : "primary"}
                            onClick={() => setManualEntryMode(prev => ({ ...prev, make: !prev.make }))}
                            clickable
                          />
                        </Box>
                        {manualEntryMode.make ? (
                          <TextField
                            fullWidth
                            label="Make"
                            value={formData.make}
                            onChange={(e) => handleInputChange('make', e.target.value)}
                            required
                            error={!!fieldErrors.make}
                            helperText={fieldErrors.make || "Enter vehicle make manually"}
                          />
                        ) : (
                          <Autocomplete
                            fullWidth
                            options={makeOptions}
                            value={formData.make}
                            onChange={handleMakeChange}
                            inputValue={makeInputValue}
                            onInputChange={(event, newInputValue) => {
                              setMakeInputValue(newInputValue);
                            }}
                            freeSolo
                            loading={searchLoading}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Make"
                                required
                                error={!!fieldErrors.make}
                                helperText={fieldErrors.make || "Start typing to see suggestions or switch to manual entry"}
                                InputProps={{
                                  ...params.InputProps,
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <SearchIcon />
                                    </InputAdornment>
                                  ),
                                  endAdornment: (
                                    <>
                                      {searchLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                      {params.InputProps.endAdornment}
                                    </>
                                  ),
                                }}
                              />
                            )}
                            renderOption={(props, option) => (
                              <Box component="li" {...props}>
                                <ListItemAvatar>
                                  <Avatar>
                                    <BikeIcon />
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText
                                  primary={option}
                                  secondary={`${vehicleDatabase[formData.category === 'luxury_bike' ? 'motorcycle' : 'scooter'][option]?.length || 0} models available`}
                                />
                              </Box>
                            )}
                          />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Box>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="body2">Vehicle Model</Typography>
                          <Chip
                            label={manualEntryMode.model ? "Manual Entry" : "Suggestions"}
                            size="small"
                            color={manualEntryMode.model ? "secondary" : "primary"}
                            onClick={() => setManualEntryMode(prev => ({ ...prev, model: !prev.model }))}
                            clickable
                            disabled={!formData.make}
                          />
                        </Box>
                        {manualEntryMode.model ? (
                          <TextField
                            fullWidth
                            label="Model"
                            value={formData.model}
                            onChange={(e) => handleInputChange('model', e.target.value)}
                            required
                            disabled={!formData.make}
                            error={!!fieldErrors.model}
                            helperText={fieldErrors.model || (formData.make ? "Enter vehicle model manually" : "Select a make first")}
                          />
                        ) : (
                          <Autocomplete
                            fullWidth
                            options={modelOptions}
                            value={formData.model}
                            onChange={handleModelChange}
                            inputValue={modelInputValue}
                            onInputChange={(event, newInputValue) => {
                              setModelInputValue(newInputValue);
                            }}
                            freeSolo
                            disabled={!formData.make}
                            loading={searchLoading}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Model"
                                required
                                error={!!fieldErrors.model}
                                helperText={fieldErrors.model || (formData.make ? "Select or type a model, or switch to manual entry" : "Select a make first")}
                                InputProps={{
                                  ...params.InputProps,
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <SearchIcon />
                                    </InputAdornment>
                                  ),
                                  endAdornment: (
                                    <>
                                      {searchLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                      {params.InputProps.endAdornment}
                                    </>
                                  ),
                                }}
                              />
                            )}
                            renderOption={(props, option) => {
                              const existingVehicle = existingVehicles.find(
                                v => v.make === formData.make && v.model === option
                              );
                              return (
                                <Box component="li" {...props}>
                                  <ListItemAvatar>
                                    <Avatar>
                                      <BikeIcon />
                                    </Avatar>
                                  </ListItemAvatar>
                                  <ListItemText
                                    primary={option}
                                    secondary={existingVehicle ?
                                      `${existingVehicle.engine_size}cc • ${existingVehicle.fuel_type} • ${existingVehicle.transmission}` :
                                      'New model'
                                    }
                                  />
                                </Box>
                              );
                            }}
                          />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Year"
                        type="number"
                        value={formData.year}
                        onChange={(e) => handleInputChange('year', parseInt(e.target.value))}
                        required
                        error={!!fieldErrors.year}
                        helperText={fieldErrors.year || "Enter the manufacturing year"}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Engine Size"
                        type="number"
                        value={formData.engine_size}
                        onChange={(e) => handleInputChange('engine_size', parseInt(e.target.value))}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">cc</InputAdornment>,
                        }}
                        required
                        error={!!fieldErrors.engine_size}
                        helperText={fieldErrors.engine_size || "Engine displacement in cubic centimeters"}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Transmission</InputLabel>
                        <Select
                          value={formData.transmission}
                          onChange={(e) => handleInputChange('transmission', e.target.value)}
                          label="Transmission"
                        >
                          <MenuItem value="automatic">Automatic</MenuItem>
                          <MenuItem value="manual">Manual</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Fuel Type</InputLabel>
                        <Select
                          value={formData.fuel_type}
                          onChange={(e) => handleInputChange('fuel_type', e.target.value)}
                          label="Fuel Type"
                        >
                          <MenuItem value="petrol">Petrol</MenuItem>
                          <MenuItem value="electric">Electric</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        multiline
                        rows={4}
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        required
                        error={!!fieldErrors.description}
                        helperText={fieldErrors.description || "Describe your vehicle's features, condition, and any special notes (minimum 20 characters)"}
                      />
                    </Grid>
                  </Grid>
                )}

                {index === 1 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Daily Rate"
                        type="number"
                        value={formData.daily_rate}
                        onChange={(e) => handleInputChange('daily_rate', parseFloat(e.target.value))}
                        required
                        error={!!fieldErrors.daily_rate}
                        helperText={fieldErrors.daily_rate || "Price per day in USD"}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Weekly Rate"
                        type="number"
                        value={formData.weekly_rate}
                        onChange={(e) => handleInputChange('weekly_rate', parseFloat(e.target.value))}
                        required
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Monthly Rate"
                        type="number"
                        value={formData.monthly_rate}
                        onChange={(e) => handleInputChange('monthly_rate', parseFloat(e.target.value))}
                        required
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Security Deposit"
                        type="number"
                        value={formData.security_deposit}
                        onChange={(e) => handleInputChange('security_deposit', parseFloat(e.target.value))}
                        required
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Minimum Rental Days"
                        type="number"
                        value={formData.minimum_rental_days}
                        onChange={(e) => handleInputChange('minimum_rental_days', parseInt(e.target.value))}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Maximum Rental Days"
                        type="number"
                        value={formData.maximum_rental_days}
                        onChange={(e) => handleInputChange('maximum_rental_days', parseInt(e.target.value))}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Quantity Available"
                        type="number"
                        value={formData.quantity}
                        onChange={(e) => handleInputChange('quantity', parseInt(e.target.value))}
                        required
                      />
                    </Grid>
                  </Grid>
                )}

                {index === 2 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Address *"
                        value={formData.location.address}
                        onChange={e => handleLocationChange('address', e.target.value)}
                        required
                        error={!!fieldErrors['location.address']}
                        helperText={fieldErrors['location.address'] || "Full street address where vehicle can be picked up"}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="City *"
                        value={formData.location.city}
                        onChange={e => handleLocationChange('city', e.target.value)}
                        required
                        error={!!fieldErrors['location.city']}
                        helperText={fieldErrors['location.city'] || "City where vehicle is located"}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.delivery_available}
                            onChange={(e) => handleInputChange('delivery_available', e.target.checked)}
                          />
                        }
                        label="Delivery Available"
                      />
                    </Grid>
                    {formData.delivery_available && (
                      <>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Delivery Fee"
                            type="number"
                            value={formData.delivery_fee}
                            onChange={(e) => handleInputChange('delivery_fee', parseFloat(e.target.value))}
                            InputProps={{
                              startAdornment: <InputAdornment position="start">$</InputAdornment>,
                            }}
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Delivery Radius (km)"
                            type="number"
                            value={formData.delivery_radius}
                            onChange={(e) => handleInputChange('delivery_radius', parseFloat(e.target.value))}
                          />
                        </Grid>
                      </>
                    )}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Pickup Instructions"
                        multiline
                        rows={3}
                        value={formData.pickup_instructions}
                        onChange={(e) => handleInputChange('pickup_instructions', e.target.value)}
                        helperText="Instructions for customers picking up the vehicle"
                      />
                    </Grid>
                  </Grid>
                )}

                {index === 3 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom>
                        Vehicle Features
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {availableFeatures.map(feature => (
                          <Chip
                            key={feature}
                            label={feature}
                            onClick={() => handleFeatureToggle(feature)}
                            color={formData.features.includes(feature) ? 'primary' : 'default'}
                            variant={formData.features.includes(feature) ? 'filled' : 'outlined'}
                          />
                        ))}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom>
                        Available Add-ons
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {availableAddOns.map(addOn => (
                          <Chip
                            key={addOn}
                            label={addOn}
                            onClick={() => handleAddOnToggle(addOn)}
                            color={formData.add_ons.includes(addOn) ? 'primary' : 'default'}
                            variant={formData.add_ons.includes(addOn) ? 'filled' : 'outlined'}
                          />
                        ))}
                      </Box>
                    </Grid>
                  </Grid>
                )}

                {index === 4 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Vehicle Images
                      </Typography>
                      <Box mb={2}>
                        <input
                          accept="image/*"
                          style={{ display: 'none' }}
                          id="image-upload"
                          multiple
                          type="file"
                          onChange={handleImageChange}
                        />
                        <label htmlFor="image-upload">
                          <Button
                            variant="outlined"
                            component="span"
                            startIcon={<PhotoCamera />}
                          >
                            Upload Images
                          </Button>
                        </label>
                        <Typography variant="caption" display="block" mt={1} color={fieldErrors.images ? 'error' : 'textSecondary'}>
                          {fieldErrors.images || "Upload up to 10 images. First image will be the main photo."}
                        </Typography>
                      </Box>

                      {imagePreviews.length > 0 && (
                        <Grid container spacing={2}>
                          {imagePreviews.map((preview, index) => (
                            <Grid item xs={6} sm={4} md={3} key={index}>
                              <Paper
                                sx={{
                                  position: 'relative',
                                  p: 1,
                                  textAlign: 'center',
                                }}
                              >
                                <img
                                  src={preview}
                                  alt={`Preview ${index + 1}`}
                                  style={{
                                    width: '100%',
                                    height: 100,
                                    objectFit: 'cover',
                                    borderRadius: 4,
                                  }}
                                />
                                <IconButton
                                  size="small"
                                  color="error"
                                  sx={{
                                    position: 'absolute',
                                    top: 4,
                                    right: 4,
                                    bgcolor: 'rgba(255,255,255,0.8)',
                                  }}
                                  onClick={() => removeImage(index)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Paper>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                )}

                <Box sx={{ mt: 2 }}>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>
                  {activeStep === steps.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleSubmit}
                      startIcon={<SaveIcon />}
                      disabled={loading}
                    >
                      {loading ? 'Creating...' : 'Create Vehicle'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                    >
                      Next
                    </Button>
                  )}
                </Box>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>

      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          startIcon={<CancelIcon />}
        >
          Cancel
        </Button>
      </Box>
    </Box>
  );
};

export default VehicleUploadForm; 