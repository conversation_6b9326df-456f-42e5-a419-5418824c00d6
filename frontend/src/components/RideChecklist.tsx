import React, { useState, useCallback } from 'react';
import { 
  Box,
  Button,
  Typography,
  Stack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  FormLabel,
  Alert,
  IconButton,
  Paper,
  SelectChangeEvent,
} from '@mui/material';
import { useDropzone } from 'react-dropzone';
import CloseIcon from '@mui/icons-material/Close';
import { useSnackbar } from 'notistack';
import axios from 'axios';

interface RideChecklistProps {
  bookingId: string;
  vehicleId: string;
  stage: 'START' | 'END';
  onChecklistComplete?: () => void;
}

const RideChecklist: React.FC<RideChecklistProps> = ({
  bookingId,
  vehicleId,
  stage,
  onChecklistComplete
}) => {
  const [images, setImages] = useState<File[]>([]);
  const [conditionNotes, setConditionNotes] = useState('');
  const [damageSeverity, setDamageSeverity] = useState<string>('MINOR');
  const { enqueueSnackbar } = useSnackbar();

  const handleDrop = useCallback((acceptedFiles: File[]) => {
    setImages(prev => [...prev, ...acceptedFiles].slice(0, 6));
  }, []);

  const handleRemoveImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    maxSize: 5242880, // 5MB
  });

  const handleDamageSeverityChange = (event: SelectChangeEvent) => {
    setDamageSeverity(event.target.value);
  };

  const handleSubmit = async () => {
    try {
      const formData = new FormData();
      formData.append('bookingId', bookingId);
      formData.append('vehicleId', vehicleId);
      formData.append('stage', stage);
      formData.append('conditionNotes', conditionNotes);
      formData.append('damageSeverity', damageSeverity);
      
      images.forEach((image, index) => {
        formData.append(`images${index}`, image);
      });

      await axios.post('/api/ride-checklist', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      enqueueSnackbar('Checklist submitted successfully', { 
        variant: 'success'
      });
      
      if (onChecklistComplete) {
        onChecklistComplete();
      }
    } catch (error) {
      console.error('Failed to submit checklist', error);
      enqueueSnackbar('Failed to submit checklist', { 
        variant: 'error'
      });
    }
  };

  return (
    <Stack spacing={3}>
      {/* Image Upload */}
      <Paper
        variant="outlined"
        sx={{
          p: 3,
          bgcolor: isDragActive ? 'action.hover' : 'background.paper',
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          textAlign: 'center',
          cursor: 'pointer'
        }}
        {...getRootProps()}
      >
        <input {...getInputProps()} />
        <Typography>
          {isDragActive 
            ? 'Drop images here' 
            : 'Drag & drop images, or click to select (max 6)'}
        </Typography>
      </Paper>

      {/* Image Preview */}
      <Box sx={{ 
        display: 'flex', 
        gap: 1, 
        overflowX: 'auto', 
        width: '100%',
        py: 1
      }}>
        {images.map((image, index) => (
          <Box key={index} sx={{ position: 'relative' }}>
            <Box
              component="img"
              src={URL.createObjectURL(image)}
              alt={`Preview ${index + 1}`}
              sx={{
                width: 100,
                height: 100,
                objectFit: 'cover',
                borderRadius: 1
              }}
            />
            <IconButton
              size="small"
              sx={{
                position: 'absolute',
                top: 4,
                right: 4,
                bgcolor: 'error.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'error.dark'
                },
                padding: '4px'
              }}
              onClick={() => handleRemoveImage(index)}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        ))}
      </Box>

      {/* Condition Notes */}
      <FormControl fullWidth>
        <FormLabel>Condition Notes</FormLabel>
        <TextField
          multiline
          rows={4}
          value={conditionNotes}
          onChange={(e) => setConditionNotes(e.target.value)}
          placeholder="Describe the current condition of the vehicle..."
          fullWidth
        />
      </FormControl>

      {/* Damage Severity (for END stage) */}
      {stage === 'END' && (
        <FormControl fullWidth>
          <FormLabel>Damage Severity</FormLabel>
          <Select
            value={damageSeverity}
            onChange={handleDamageSeverityChange}
          >
            <MenuItem value="MINOR">Minor</MenuItem>
            <MenuItem value="MODERATE">Moderate</MenuItem>
            <MenuItem value="SEVERE">Severe</MenuItem>
          </Select>
        </FormControl>
      )}

      {/* Help Text */}
      <Alert severity="info">
        Please take clear photos of the vehicle from all angles and note any existing damage or issues.
      </Alert>

      {/* Submit Button */}
      <Button
        variant="contained"
        color="primary"
        onClick={handleSubmit}
        fullWidth
        size="large"
      >
        Submit {stage === 'START' ? 'Start' : 'End'} Checklist
      </Button>
    </Stack>
  );
};

export default RideChecklist;