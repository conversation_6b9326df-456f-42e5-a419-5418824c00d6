import React, { useState } from 'react';
import {
  Box,
  TextField,
  Typography,
  Grid,
  Paper,
  Alert
} from '@mui/material';
import {
  LocationOn as LocationIcon
} from '@mui/icons-material';

interface LocationPickerProps {
  address: string;
  city: string;
  latitude: number;
  longitude: number;
  onAddressChange: (address: string) => void;
  onCityChange: (city: string) => void;
  onLatitudeChange: (latitude: number) => void;
  onLongitudeChange: (longitude: number) => void;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  address,
  city,
  latitude,
  longitude,
  onAddressChange,
  onCityChange,
  onLatitudeChange,
  onLongitudeChange
}) => {
  const [error, setError] = useState<string>('');

  const handleAddressChange = (value: string) => {
    onAddressChange(value);
    setError('');
  };

  const handleCityChange = (value: string) => {
    onCityChange(value);
    setError('');
  };

  const handleLatitudeChange = (value: string) => {
    const lat = parseFloat(value);
    if (isNaN(lat) || lat < -90 || lat > 90) {
      setError('Latitude must be between -90 and 90');
      return;
    }
    onLatitudeChange(lat);
    setError('');
  };

  const handleLongitudeChange = (value: string) => {
    const lng = parseFloat(value);
    if (isNaN(lng) || lng < -180 || lng > 180) {
      setError('Longitude must be between -180 and 180');
      return;
    }
    onLongitudeChange(lng);
    setError('');
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Vehicle Location
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Street Address"
              value={address}
              onChange={(e) => handleAddressChange(e.target.value)}
              placeholder="Enter street address"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="City"
              value={city}
              onChange={(e) => handleCityChange(e.target.value)}
              placeholder="Enter city name"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Latitude"
              type="number"
              value={latitude || ''}
              onChange={(e) => handleLatitudeChange(e.target.value)}
              placeholder="e.g., -6.2088"
              inputProps={{
                step: 0.0001,
                min: -90,
                max: 90
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Longitude"
              type="number"
              value={longitude || ''}
              onChange={(e) => handleLongitudeChange(e.target.value)}
              placeholder="e.g., 106.8456"
              inputProps={{
                step: 0.0001,
                min: -180,
                max: 180
              }}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Location Tips:</strong>
          </Typography>
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            • Provide accurate coordinates for better customer experience
          </Typography>
          <Typography variant="caption" display="block">
            • Include nearby landmarks or landmarks for easy pickup
          </Typography>
          <Typography variant="caption" display="block">
            • Ensure the location is accessible and safe for vehicle pickup
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default LocationPicker; 