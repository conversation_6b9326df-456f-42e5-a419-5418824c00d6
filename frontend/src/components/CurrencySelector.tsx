import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Tooltip
} from '@mui/material';
import { 
  CurrencyExchange as CurrencyIcon 
} from '@mui/icons-material';

interface Currency {
  code: string;
  symbol: string;
  name: string;
}

// List of commonly used currencies
const currencies: Currency[] = [
  { code: 'IDR', symbol: 'Rp', name: 'Indonesian Rupiah' },
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'SGD', symbol: 'S$', name: 'Singapore Dollar' },
  { code: 'MYR', symbol: 'RM', name: 'Malaysian Ringgit' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' }
];

// Currency storage key
const CURRENCY_STORAGE_KEY = 'rentahub-currency';

export const CurrencyContext = React.createContext<{
  currency: Currency;
  setCurrency: (currency: Currency) => void;
}>({
  currency: currencies[0], // Default to Indonesian Rupiah
  setCurrency: () => {},
});

// Hook for components to access the current currency
export const useCurrency = () => React.useContext(CurrencyContext);

export const CurrencyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currency, setCurrency] = useState<Currency>(() => {
    // Try to get currency from localStorage
    const savedCurrency = localStorage.getItem(CURRENCY_STORAGE_KEY);
    if (savedCurrency) {
      try {
        return JSON.parse(savedCurrency);
      } catch (e) {
        console.error('Failed to parse saved currency', e);
      }
    }
    return currencies[0]; // Default to Indonesian Rupiah
  });

  // Save currency to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(CURRENCY_STORAGE_KEY, JSON.stringify(currency));
    
    // Dispatch event so other components can update without context
    window.dispatchEvent(new CustomEvent('currency-changed', { 
      detail: currency 
    }));
  }, [currency]);

  return (
    <CurrencyContext.Provider value={{ currency, setCurrency }}>
      {children}
    </CurrencyContext.Provider>
  );
};

const CurrencySelector: React.FC<{
  iconColor?: string;
  displayType?: 'icon' | 'text' | 'both';
}> = ({ 
  iconColor = 'inherit',
  displayType = 'both'
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { currency, setCurrency } = useCurrency();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCurrencyChange = (newCurrency: Currency) => {
    setCurrency(newCurrency);
    handleClose();
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Tooltip title="Change Currency">
        <IconButton
          onClick={handleClick}
          sx={{
            color: iconColor,
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}
        >
          {displayType === 'icon' && <CurrencyIcon />}
          {displayType === 'text' && (
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {currency.code}
            </Typography>
          )}
          {displayType === 'both' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <CurrencyIcon fontSize="small" />
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                {currency.code}
              </Typography>
            </Box>
          )}
        </IconButton>
      </Tooltip>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 180,
            '& .MuiMenuItem-root': {
              py: 1
            }
          }
        }}
      >
        <MenuItem disabled>
          <ListItemIcon>
            <CurrencyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            <Typography variant="subtitle2" color="text.secondary">
              Select Currency
            </Typography>
          </ListItemText>
        </MenuItem>
        
        {currencies.map((curr) => (
          <MenuItem
            key={curr.code}
            onClick={() => handleCurrencyChange(curr)}
            selected={currency.code === curr.code}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'primary.light',
                '&:hover': {
                  backgroundColor: 'primary.light'
                }
              }
            }}
          >
            <ListItemIcon>
              <Typography variant="body1">{curr.symbol}</Typography>
            </ListItemIcon>
            <ListItemText>
              <Typography variant="body2">
                {curr.code} - {curr.name}
              </Typography>
            </ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default CurrencySelector; 