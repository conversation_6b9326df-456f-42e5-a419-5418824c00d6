import React from 'react';
import {
  Box,
  Typography,
  TextField,
  Paper,
  Alert,
  Chip,
  Grid,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  TrendingUp as TrendingIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

interface InventoryManagementProps {
  availableUnits: number;
  onAvailableUnitsChange: (value: number) => void;
  disabled?: boolean;
}

const InventoryManagement: React.FC<InventoryManagementProps> = ({
  availableUnits,
  onAvailableUnitsChange,
  disabled = false
}) => {
  const handleAvailableUnitsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value) || 0;
    onAvailableUnitsChange(Math.max(0, value));
  };

  const getInventoryStatus = () => {
    if (availableUnits === 0) {
      return {
        status: 'out-of-stock',
        color: 'error' as const,
        message: 'Out of stock',
        icon: <WarningIcon />
      };
    } else if (availableUnits <= 2) {
      return {
        status: 'low-stock',
        color: 'warning' as const,
        message: 'Low stock',
        icon: <WarningIcon />
      };
    } else {
      return {
        status: 'in-stock',
        color: 'success' as const,
        message: 'In stock',
        icon: <CheckIcon />
      };
    }
  };

  const inventoryStatus = getInventoryStatus();

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        Inventory Management
      </Typography>
      
      <Paper elevation={1} sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <InventoryIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="subtitle1" fontWeight="medium">
            Available Units
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Set the number of vehicles available for rent. This affects calendar availability and booking capacity.
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              required
              type="number"
              value={availableUnits}
              onChange={handleAvailableUnitsChange}
              label="Number of vehicles available"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                inputProps: { min: 0, max: 100 }
              }}
              helperText="How many vehicles of this type are available for rent"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              {inventoryStatus.icon}
              <Chip
                label={inventoryStatus.message}
                color={inventoryStatus.color}
                variant="filled"
              />
            </Box>
            
            <Typography variant="body2" color="text.secondary">
              Current stock level: {availableUnits} unit{availableUnits !== 1 ? 's' : ''}
            </Typography>
          </Grid>
        </Grid>

        {/* Status Messages */}
        {availableUnits === 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            <Typography variant="body2" fontWeight="medium">
              Out of Stock
            </Typography>
            <Typography variant="caption">
              This vehicle will not appear in search results until stock is added.
            </Typography>
          </Alert>
        )}

        {availableUnits > 0 && availableUnits <= 2 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2" fontWeight="medium">
              Low Stock Warning
            </Typography>
            <Typography variant="caption">
              Consider adding more units to meet demand. Low stock may limit booking availability.
            </Typography>
          </Alert>
        )}

        {availableUnits > 2 && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="body2" fontWeight="medium">
              Good Stock Level
            </Typography>
            <Typography variant="caption">
              Sufficient inventory for normal rental demand.
            </Typography>
          </Alert>
        )}

        {/* Inventory Tips */}
        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Inventory Management Tips:
          </Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
              Set realistic numbers based on your actual fleet size
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
              Update inventory when vehicles are sold or added
            </Typography>
            <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
              Monitor booking patterns to optimize stock levels
            </Typography>
            <Typography component="li" variant="body2">
              Consider seasonal demand when setting inventory
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default InventoryManagement; 