import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Autocomplete,
  Checkbox,
  ListItemText,
  OutlinedInput,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Slider,
  InputAdornment,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemSecondaryAction
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Schedule as ScheduleIcon,
  DirectionsBike as VehicleIcon,
  Person as CustomerIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Email as EmailIcon,
  Repeat as RecurringIcon,
  ViewList as ListIcon,
  ViewModule as GridIcon,
  DragIndicator as DragIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as NotificationIcon,
  ExpandMore as ExpandMoreIcon,
  Timeline as TimelineIcon,
  Group as GroupIcon,
  Sort as SortIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, addDays, isToday, isBefore, isAfter, parseISO, differenceInDays, addWeeks, subWeeks, startOfDay, endOfDay } from 'date-fns';
import { Booking, Vehicle, User, BookingStatus } from '../types';

interface BookingSchedulerProps {
  bookings: Booking[];
  vehicles: Vehicle[];
  onBookingUpdate?: (bookingId: string, status: BookingStatus) => void;
  onBookingCreate?: (booking: Partial<Booking>) => void;
  onBookingDelete?: (bookingId: string) => void;
  onBookingReschedule?: (bookingId: string, newStartDate: string, newEndDate: string) => void;
  onExportBookings?: (filters: BookingFilters) => void;
  onImportBookings?: (file: File) => void;
  onSendNotification?: (bookingId: string, type: 'email' | 'sms') => void;
  loading?: boolean;
}

interface BookingFilters {
  status: BookingStatus[];
  vehicles: string[];
  customers: string[];
  dateRange: [Date | null, Date | null];
  minAmount: number;
  maxAmount: number;
  recurring: boolean;
  resourceView: boolean;
}

interface ResourceView {
  vehicle: Vehicle;
  bookings: Booking[];
  utilization: number;
}

export const BookingScheduler: React.FC<BookingSchedulerProps> = ({
  bookings,
  vehicles,
  onBookingUpdate,
  onBookingCreate,
  onBookingDelete,
  onBookingReschedule,
  onExportBookings,
  onImportBookings,
  onSendNotification,
  loading = false
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isCreateBookingModalOpen, setIsCreateBookingModalOpen] = useState(false);
  const [isRecurringBookingModalOpen, setIsRecurringBookingModalOpen] = useState(false);
  const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [view, setView] = useState<'timeline' | 'resource' | 'list'>('timeline');
  const [filters, setFilters] = useState<BookingFilters>({
    status: [],
    vehicles: [],
    customers: [],
    dateRange: [null, null],
    minAmount: 0,
    maxAmount: 10000,
    recurring: false,
    resourceView: false
  });

  // Filtered bookings based on current filters
  const filteredBookings = useMemo(() => {
    return bookings.filter(booking => {
      if (filters.status.length > 0 && !filters.status.includes(booking.status as BookingStatus)) {
        return false;
      }
      if (filters.vehicles.length > 0 && !filters.vehicles.includes(booking.vehicleId)) {
        return false;
      }
      if (filters.customers.length > 0 && !filters.customers.includes(booking.userId)) {
        return false;
      }
      if (filters.dateRange[0] && isBefore(new Date(booking.startDate), filters.dateRange[0])) {
        return false;
      }
      if (filters.dateRange[1] && isAfter(new Date(booking.endDate), filters.dateRange[1])) {
        return false;
      }
      if (booking.totalAmount < filters.minAmount || booking.totalAmount > filters.maxAmount) {
        return false;
      }
      return true;
    });
  }, [bookings, filters]);

  // Generate resource view data
  const resourceViewData = useMemo(() => {
    return vehicles.map(vehicle => {
      const vehicleBookings = filteredBookings.filter(booking => booking.vehicleId === vehicle.id);
      const totalDays = 7; // Current week
      const bookedDays = vehicleBookings.reduce((total, booking) => {
        const start = new Date(booking.startDate);
        const end = new Date(booking.endDate);
        const days = differenceInDays(end, start) + 1;
        return total + days;
      }, 0);
      
      return {
        vehicle,
        bookings: vehicleBookings,
        utilization: Math.min((bookedDays / totalDays) * 100, 100)
      };
    });
  }, [vehicles, filteredBookings]);

  // Generate timeline data
  const timelineData = useMemo(() => {
    const start = startOfWeek(currentWeek);
    const end = endOfWeek(currentWeek);
    const days = eachDayOfInterval({ start, end });

    return days.map(day => ({
      date: day,
      bookings: filteredBookings.filter(booking => {
        const bookingStart = new Date(booking.startDate);
        const bookingEnd = new Date(booking.endDate);
        return isSameDay(day, bookingStart) || 
               isSameDay(day, bookingEnd) || 
               (isAfter(day, bookingStart) && isBefore(day, bookingEnd));
      })
    }));
  }, [currentWeek, filteredBookings]);

  // Navigation
  const goToPreviousWeek = () => setCurrentWeek(subWeeks(currentWeek, 1));
  const goToNextWeek = () => setCurrentWeek(addWeeks(currentWeek, 1));
  const goToToday = () => setCurrentWeek(new Date());

  // Get status color
  const getStatusColor = (status: BookingStatus) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Handle booking selection
  const handleBookingClick = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsBookingModalOpen(true);
  };

  // Handle booking actions
  const handleApproveBooking = () => {
    if (selectedBooking && onBookingUpdate) {
      onBookingUpdate(selectedBooking.id, 'confirmed');
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleRejectBooking = () => {
    if (selectedBooking && onBookingUpdate) {
      onBookingUpdate(selectedBooking.id, 'cancelled');
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleDeleteBooking = () => {
    if (selectedBooking && onBookingDelete) {
      onBookingDelete(selectedBooking.id);
      setIsBookingModalOpen(false);
      setSelectedBooking(null);
    }
  };

  const handleSendNotification = (type: 'email' | 'sms') => {
    if (selectedBooking && onSendNotification) {
      onSendNotification(selectedBooking.id, type);
    }
  };

  // Export/Import handlers
  const handleExport = () => {
    if (onExportBookings) {
      onExportBookings(filters);
    }
    setIsExportModalOpen(false);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onImportBookings) {
      onImportBookings(file);
    }
  };

  return (
    <Card>
      <CardContent>
        {/* Scheduler Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h5" component="h2">
              Booking Scheduler
            </Typography>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setIsCreateBookingModalOpen(true)}
            >
              New Booking
            </Button>
            <Button
              variant="outlined"
              startIcon={<RecurringIcon />}
              onClick={() => setIsRecurringBookingModalOpen(true)}
            >
              Recurring
            </Button>
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton onClick={goToPreviousWeek}>
              <CalendarIcon />
            </IconButton>
            <Typography variant="h6">
              {format(currentWeek, 'MMM dd')} - {format(addDays(currentWeek, 6), 'MMM dd, yyyy')}
            </Typography>
            <IconButton onClick={goToNextWeek}>
              <CalendarIcon />
            </IconButton>
            <Button variant="outlined" size="small" onClick={goToToday}>
              Today
            </Button>
          </Box>
        </Box>

        {/* View Controls */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={view} onChange={(_, newValue) => setView(newValue)}>
            <Tab value="timeline" label="Timeline" icon={<TimelineIcon />} />
            <Tab value="resource" label="Resource" icon={<GroupIcon />} />
            <Tab value="list" label="List" icon={<ListIcon />} />
          </Tabs>
          
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton onClick={() => setIsFiltersModalOpen(true)}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={() => setIsExportModalOpen(true)}>
              <ExportIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Scheduler Content */}
        {loading ? (
          <Box display="flex" justifyContent="center" p={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Box>
            {view === 'timeline' && (
              <Box>
                {/* Timeline Header */}
                <Grid container sx={{ mb: 1 }}>
                  <Grid item xs={2}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                      Vehicle
                    </Typography>
                  </Grid>
                  {timelineData.map((day, index) => (
                    <Grid item xs key={index}>
                      <Typography variant="subtitle2" align="center" sx={{ fontWeight: 'bold' }}>
                        {format(day.date, 'EEE dd')}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>

                {/* Timeline Rows */}
                {vehicles.map(vehicle => (
                  <Grid container key={vehicle.id} sx={{ mb: 1 }}>
                    <Grid item xs={2}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <VehicleIcon />
                        <Typography variant="body2">{vehicle.name}</Typography>
                      </Box>
                    </Grid>
                    {timelineData.map((day, index) => {
                      const dayBookings = day.bookings.filter(booking => booking.vehicleId === vehicle.id);
                      return (
                        <Grid item xs key={index}>
                          <Paper
                            sx={{
                              p: 1,
                              minHeight: 60,
                              backgroundColor: dayBookings.length > 0 ? 'primary.light' : 'background.paper',
                              cursor: 'pointer'
                            }}
                          >
                            {dayBookings.map(booking => (
                              <Tooltip
                                key={booking.id}
                                title={`${booking.user?.name} - ${booking.status}`}
                              >
                                <Chip
                                  label={booking.user?.name}
                                  size="small"
                                  color={getStatusColor(booking.status) as any}
                                  sx={{ mb: 0.5, cursor: 'pointer', fontSize: '0.7rem' }}
                                  onClick={() => handleBookingClick(booking)}
                                />
                              </Tooltip>
                            ))}
                          </Paper>
                        </Grid>
                      );
                    })}
                  </Grid>
                ))}
              </Box>
            )}

            {view === 'resource' && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Resource Utilization
                </Typography>
                <Grid container spacing={2}>
                  {resourceViewData.map((resource) => (
                    <Grid item xs={12} md={6} lg={4} key={resource.vehicle.id}>
                      <Card>
                        <CardContent>
                          <Box display="flex" alignItems="center" gap={2} mb={2}>
                            <Avatar>
                              <VehicleIcon />
                            </Avatar>
                            <Box>
                              <Typography variant="h6">{resource.vehicle.name}</Typography>
                              <Typography variant="body2" color="text.secondary">
                                {resource.bookings.length} bookings
                              </Typography>
                            </Box>
                          </Box>
                          
                          <Box mb={2}>
                            <Typography variant="body2" gutterBottom>
                              Utilization: {resource.utilization.toFixed(1)}%
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={resource.utilization}
                              color={resource.utilization > 80 ? 'error' : resource.utilization > 60 ? 'warning' : 'success'}
                            />
                          </Box>

                          <List dense>
                            {resource.bookings.slice(0, 3).map(booking => (
                              <ListItem key={booking.id} button onClick={() => handleBookingClick(booking)}>
                                <ListItemText
                                  primary={booking.user?.name}
                                  secondary={`${format(new Date(booking.startDate), 'MMM dd')} - ${format(new Date(booking.endDate), 'MMM dd')}`}
                                />
                                <Chip
                                  label={booking.status}
                                  size="small"
                                  color={getStatusColor(booking.status) as any}
                                />
                              </ListItem>
                            ))}
                            {resource.bookings.length > 3 && (
                              <ListItem>
                                <ListItemText
                                  primary={`+${resource.bookings.length - 3} more bookings`}
                                  color="text.secondary"
                                />
                              </ListItem>
                            )}
                          </List>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {view === 'list' && (
              <Box>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Vehicle</TableCell>
                        <TableCell>Customer</TableCell>
                        <TableCell>Start Date</TableCell>
                        <TableCell>End Date</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredBookings.map(booking => (
                        <TableRow key={booking.id} hover>
                          <TableCell>{booking.vehicle?.name}</TableCell>
                          <TableCell>{booking.user?.name}</TableCell>
                          <TableCell>{format(new Date(booking.startDate), 'MMM dd, yyyy')}</TableCell>
                          <TableCell>{format(new Date(booking.endDate), 'MMM dd, yyyy')}</TableCell>
                          <TableCell>
                            <Chip
                              label={booking.status}
                              size="small"
                              color={getStatusColor(booking.status) as any}
                            />
                          </TableCell>
                          <TableCell>${booking.totalAmount}</TableCell>
                          <TableCell>
                            <IconButton size="small" onClick={() => handleBookingClick(booking)}>
                              <ViewIcon />
                            </IconButton>
                            <IconButton size="small" onClick={() => handleSendNotification('email')}>
                              <EmailIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Box>
        )}

        {/* Booking Details Modal */}
        <Dialog open={isBookingModalOpen} onClose={() => setIsBookingModalOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            <Box display="flex" alignItems="center" gap={1}>
              <VehicleIcon />
              Booking Details
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedBooking && (
              <Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      {selectedBooking.vehicle?.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" mb={2}>
                      Customer: {selectedBooking.user?.name}
                    </Typography>
                    <Typography variant="body2" mb={1}>
                      Start: {format(new Date(selectedBooking.startDate), 'PPP')}
                    </Typography>
                    <Typography variant="body2" mb={1}>
                      End: {format(new Date(selectedBooking.endDate), 'PPP')}
                    </Typography>
                    <Typography variant="h6" color="primary" mb={2}>
                      Total: ${selectedBooking.totalAmount}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Chip
                        label={selectedBooking.status}
                        color={getStatusColor(selectedBooking.status) as any}
                        size="medium"
                      />
                    </Box>
                    {selectedBooking.status === 'pending' && (
                      <Alert severity="warning">
                        This booking requires your approval
                      </Alert>
                    )}
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsBookingModalOpen(false)}>Close</Button>
            <Button onClick={() => handleSendNotification('email')} startIcon={<EmailIcon />}>
              Send Email
            </Button>
            {selectedBooking?.status === 'pending' && (
              <>
                <Button onClick={handleRejectBooking} color="error" startIcon={<RejectIcon />}>
                  Reject
                </Button>
                <Button onClick={handleApproveBooking} variant="contained" startIcon={<ApproveIcon />}>
                  Approve
                </Button>
              </>
            )}
            {selectedBooking && (
              <Button onClick={handleDeleteBooking} color="error" startIcon={<DeleteIcon />}>
                Delete
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Create Booking Modal */}
        <Dialog open={isCreateBookingModalOpen} onClose={() => setIsCreateBookingModalOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Create New Booking</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Vehicle</InputLabel>
                  <Select label="Vehicle">
                    {vehicles.map(vehicle => (
                      <MenuItem key={vehicle.id} value={vehicle.id}>
                        {vehicle.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Customer Name"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsCreateBookingModalOpen(false)}>Cancel</Button>
            <Button variant="contained">Create Booking</Button>
          </DialogActions>
        </Dialog>

        {/* Filters Modal */}
        <Dialog open={isFiltersModalOpen} onClose={() => setIsFiltersModalOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Booking Filters</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    multiple
                    value={filters.status}
                    onChange={(e) => setFilters({...filters, status: e.target.value as BookingStatus[]})}
                    input={<OutlinedInput label="Status" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {Object.values(BookingStatus).map((status) => (
                      <MenuItem key={status} value={status}>
                        <Checkbox checked={filters.status.indexOf(status) > -1} />
                        <ListItemText primary={status} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Vehicles</InputLabel>
                  <Select
                    multiple
                    value={filters.vehicles}
                    onChange={(e) => setFilters({...filters, vehicles: e.target.value as string[]})}
                    input={<OutlinedInput label="Vehicles" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={vehicles.find(v => v.id === value)?.name || value} />
                        ))}
                      </Box>
                    )}
                  >
                    {vehicles.map((vehicle) => (
                      <MenuItem key={vehicle.id} value={vehicle.id}>
                        <Checkbox checked={filters.vehicles.indexOf(vehicle.id) > -1} />
                        <ListItemText primary={vehicle.name} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography gutterBottom>Price Range</Typography>
                <Slider
                  value={[filters.minAmount, filters.maxAmount]}
                  onChange={(_, value) => setFilters({...filters, minAmount: value[0], maxAmount: value[1]})}
                  valueLabelDisplay="auto"
                  min={0}
                  max={10000}
                  step={100}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setFilters({
              status: [],
              vehicles: [],
              customers: [],
              dateRange: [null, null],
              minAmount: 0,
              maxAmount: 10000,
              recurring: false,
              resourceView: false
            })}>
              Clear Filters
            </Button>
            <Button onClick={() => setIsFiltersModalOpen(false)}>Apply</Button>
          </DialogActions>
        </Dialog>

        {/* Export Modal */}
        <Dialog open={isExportModalOpen} onClose={() => setIsExportModalOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Export Bookings</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Export filtered bookings to CSV or Excel format
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  component="label"
                >
                  Import Bookings
                  <input
                    type="file"
                    hidden
                    accept=".csv,.xlsx,.xls"
                    onChange={handleImport}
                  />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsExportModalOpen(false)}>Cancel</Button>
            <Button onClick={handleExport} variant="contained">Export</Button>
          </DialogActions>
        </Dialog>

        {/* Speed Dial for Quick Actions */}
        <SpeedDial
          ariaLabel="Quick actions"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          icon={<SpeedDialIcon />}
        >
          <SpeedDialAction
            icon={<AddIcon />}
            tooltipTitle="New Booking"
            onClick={() => setIsCreateBookingModalOpen(true)}
          />
          <SpeedDialAction
            icon={<FilterIcon />}
            tooltipTitle="Filters"
            onClick={() => setIsFiltersModalOpen(true)}
          />
          <SpeedDialAction
            icon={<ExportIcon />}
            tooltipTitle="Export"
            onClick={() => setIsExportModalOpen(true)}
          />
          <SpeedDialAction
            icon={<NotificationIcon />}
            tooltipTitle="Send Notifications"
            onClick={() => handleSendNotification('email')}
          />
        </SpeedDial>
      </CardContent>
    </Card>
  );
};

export default BookingScheduler; 