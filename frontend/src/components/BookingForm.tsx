import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Divider,
  Alert,
  CircularProgress,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import * as BookingService from '../services/BookingService'
import * as PaymentService from '../services/PaymentService'
import { CreateBookingPayload, PaymentMethod } from '../types'
import { useForm, Controller } from 'react-hook-form'
import { Stack } from '@mui/material'

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '')

// Placeholder functions for missing functionality
const isDateAvailable = (_date: Date): boolean => {
  // TODO: Implement date availability check
  return true
}

const unavailableDates: any[] = []

interface Vehicle {
  id: string
  make: string
  model: string
  year: number
  category: string
  daily_rate: number
  weekly_rate: number
  monthly_rate: number
  security_deposit: number
  features: string[]
  add_ons: Array<{
    name: string
    price: number
    description: string
  }>
  delivery_available: boolean
  delivery_fee: number
  provider_id: string
}

interface BookingFormProps {
  vehicle: Vehicle
  onBookingComplete: (bookingId: string) => void
  onCancel: () => void
}

interface BookingFormData {
  startDate: Date | null
  endDate: Date | null
  deliveryRequested: boolean
  deliveryAddress: string
  pickupInstructions: string
  selectedAddOns: string[]
  paymentMethod: 'card' | 'cash'
  insuranceEnabled: boolean
  depositEnabled: boolean
}

const BookingForm: React.FC<BookingFormProps> = ({ vehicle, onBookingComplete, onCancel }) => {
  const [formData, setFormData] = useState<BookingFormData>({
    startDate: null,
    endDate: null,
    deliveryRequested: false,
    deliveryAddress: '',
    pickupInstructions: '',
    selectedAddOns: [],
    paymentMethod: 'card',
    insuranceEnabled: false,
    depositEnabled: false,
  })

  const [totalDays, setTotalDays] = useState(0)
  const [pricing, setPricing] = useState({
    vehicleRate: 0,
    addOnsTotal: 0,
    deliveryFee: 0,
    subtotal: 0,
    total: 0,
  })

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPayment, setShowPayment] = useState(false)
  const [paymentIntent, setPaymentIntent] = useState<any>(null)

  const { 
    control, 
    watch, 
    formState: { errors } 
  } = useForm<BookingFormData>({
    defaultValues: {
      paymentMethod: 'card'
    }
  })

  const startDate = watch('startDate')

  // Calculate total days when dates change
  useEffect(() => {
    if (formData.startDate && formData.endDate) {
      const diffTime = formData.endDate.getTime() - formData.startDate.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      setTotalDays(diffDays > 0 ? diffDays : 0)
    } else {
      setTotalDays(0)
    }
  }, [formData.startDate, formData.endDate])

  // Calculate pricing when form data changes
  useEffect(() => {
    if (totalDays > 0) {
      // Determine rate based on rental duration
      let vehicleRate = vehicle.daily_rate
      if (totalDays >= 30) {
        vehicleRate = vehicle.monthly_rate
      } else if (totalDays >= 7) {
        vehicleRate = vehicle.weekly_rate
      }

      // Calculate add-ons total
      const addOnsTotal = formData.selectedAddOns.reduce((total, addonName) => {
        const addon = vehicle.add_ons.find(a => a.name === addonName)
        return total + (addon ? addon.price * totalDays : 0)
      }, 0)

      // Calculate delivery fee
      const deliveryFee = formData.deliveryRequested ? vehicle.delivery_fee : 0

      // Calculate totals
      const subtotal = (vehicleRate * totalDays) + addOnsTotal + deliveryFee
      const total = subtotal

      setPricing({
        vehicleRate,
        addOnsTotal,
        deliveryFee,
        subtotal,
        total,
      })
    } else {
      setPricing({
        vehicleRate: 0,
        addOnsTotal: 0,
        deliveryFee: 0,
        subtotal: 0,
        total: 0,
      })
    }
  }, [totalDays, formData.selectedAddOns, formData.deliveryRequested, vehicle])

  const handleInputChange = (field: keyof BookingFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
    setError('')
  }

  const handleAddOnToggle = (addonName: string) => {
    setFormData(prev => ({
      ...prev,
      selectedAddOns: prev.selectedAddOns.includes(addonName)
        ? prev.selectedAddOns.filter(name => name !== addonName)
        : [...prev.selectedAddOns, addonName],
    }))
  }

  const validateForm = (): boolean => {
    if (!formData.startDate || !formData.endDate) {
      setError('Please select start and end dates')
      return false
    }

    if (formData.startDate >= formData.endDate) {
      setError('End date must be after start date')
      return false
    }

    if (formData.startDate < new Date()) {
      setError('Start date cannot be in the past')
      return false
    }

    if (formData.deliveryRequested && !formData.deliveryAddress.trim()) {
      setError('Please provide delivery address')
      return false
    }

    return true
  }

  const handleBookingSubmit = async () => {
    if (!validateForm()) return

    setLoading(true)
    setError('')

    try {
      // Check vehicle availability
      const availabilityResult = await BookingService.checkAvailability({
        vehicleId: vehicle.id,
        startDate: formData.startDate!.toISOString().split('T')[0],
        endDate: formData.endDate!.toISOString().split('T')[0]
      })

      if (!availabilityResult.success || !availabilityResult.data?.available) {
        setError('Vehicle is not available for the selected dates')
        setLoading(false)
        return
      }

      // Create booking
      const bookingPayload: CreateBookingPayload = {
        userId: 'current-user-id', // TODO: Get from auth context
        providerId: vehicle.provider_id,
        vehicleId: vehicle.id,
        startDate: formData.startDate!.toISOString().split('T')[0],
        endDate: formData.endDate!.toISOString().split('T')[0],
        addOns: formData.selectedAddOns,
        paymentMethod: formData.paymentMethod as PaymentMethod,
        deliveryRequested: formData.deliveryRequested,
        deliveryAddress: formData.deliveryAddress,
        notes: formData.pickupInstructions
      }

      const result = await BookingService.createBooking(bookingPayload)

      if (result.success) {
        if (formData.paymentMethod === 'card' && result.data?.paymentIntent) {
          setPaymentIntent(result.data.paymentIntent)
          setShowPayment(true)
        } else {
          // Cash payment or booking created successfully
          onBookingComplete(result.data?.booking.id!)
        }
      } else {
        setError(result.error || 'Failed to create booking')
      }
    } catch (error) {
      console.error('Error creating booking:', error)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card sx={{ maxWidth: 600, mx: 'auto', mt: 2 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Book {vehicle.make} {vehicle.model}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={2}>
            {/* Date Selection */}
            <Grid item xs={12} sm={6}>
              <FormControl>
                <FormLabel>Start Date</FormLabel>
                <Controller
                  name="startDate"
                  control={control}
                  rules={{ 
                    required: 'Start date is required',
                    validate: {
                      available: (value) => 
                        value ? isDateAvailable(value) || 'Selected date is not available' : true
                    }
                  }}
                  render={({ field }) => (
                    <DatePicker
                      {...field}
                      minDate={new Date()}
                      // excludeDates not supported in MUI DatePicker, using shouldDisableDate instead
                      shouldDisableDate={(date) => {
                        return unavailableDates.some(range => 
                          date >= range.startDate && date <= range.endDate
                        )
                      }}
                    />
                  )}
                />
                {errors.startDate && <Typography color="error">{errors.startDate.message}</Typography>}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl>
                <FormLabel>End Date</FormLabel>
                <Controller
                  name="endDate"
                  control={control}
                  rules={{ 
                    required: 'End date is required',
                    validate: {
                      afterStart: (value) => 
                        value && startDate ? value > startDate || 'End date must be after start date' : true,
                      available: (value) => 
                        value ? isDateAvailable(value) || 'Selected date is not available' : true
                    }
                  }}
                  render={({ field }) => (
                    <DatePicker
                      {...field}
                      minDate={startDate || new Date()}
                      // excludeDates not supported in MUI DatePicker, using shouldDisableDate instead
                      shouldDisableDate={(date) => {
                        return unavailableDates.some(range => 
                          date >= range.startDate && date <= range.endDate
                        )
                      }}
                    />
                  )}
                />
                {errors.endDate && <Typography color="error">{errors.endDate.message}</Typography>}
              </FormControl>
            </Grid>

            {/* Duration Display */}
            {totalDays > 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Duration: {totalDays} day{totalDays !== 1 ? 's' : ''}
                </Typography>
              </Grid>
            )}

            {/* Add-ons */}
            {vehicle.add_ons && vehicle.add_ons.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Add-ons
                </Typography>
                {vehicle.add_ons.map((addon) => (
                  <FormControlLabel
                    key={addon.name}
                    control={
                      <Checkbox
                        checked={formData.selectedAddOns.includes(addon.name)}
                        onChange={() => handleAddOnToggle(addon.name)}
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2">
                          {addon.name} - {PaymentService.formatCurrency(addon.price, 'IDR')}/day
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {addon.description}
                        </Typography>
                      </Box>
                    }
                  />
                ))}
              </Grid>
            )}

            {/* Delivery Options */}
            {vehicle.delivery_available && (
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.deliveryRequested}
                      onChange={(e) => handleInputChange('deliveryRequested', e.target.checked)}
                    />
                  }
                  label={`Delivery Service (+${PaymentService.formatCurrency(vehicle.delivery_fee, 'IDR')})`}
                />
                {formData.deliveryRequested && (
                  <TextField
                    fullWidth
                    label="Delivery Address"
                    value={formData.deliveryAddress}
                    onChange={(e) => handleInputChange('deliveryAddress', e.target.value)}
                    required
                    sx={{ mt: 1 }}
                  />
                )}
              </Grid>
            )}

            {/* Pickup Instructions */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Pickup Instructions (Optional)"
                value={formData.pickupInstructions}
                onChange={(e) => handleInputChange('pickupInstructions', e.target.value)}
                multiline
                rows={3}
              />
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12}>
              <FormControl>
                <FormLabel>Payment Method</FormLabel>
                <Controller
                  name="paymentMethod"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field}>
                      <Stack direction="row" spacing={2}>
                        <FormControlLabel value="card" control={<Radio />} label="Card Payment" />
                        <FormControlLabel value="cash" control={<Radio />} label="Cash on Pickup" />
                      </Stack>
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Grid>

            {/* Insurance and Deposit Options */}
            <Box sx={{ mt: 2 }}>
              <FormControlLabel
                control={<Checkbox checked={formData.insuranceEnabled} onChange={(e) => setFormData({...formData, insuranceEnabled: e.target.checked})} />}
                label="Enable Insurance"
                sx={{ color: 'purple' }}
              />
              <FormControlLabel
                control={<Checkbox checked={formData.depositEnabled} onChange={(e) => setFormData({...formData, depositEnabled: e.target.checked})} />}
                label="Enable Deposit"
                sx={{ color: 'purple' }}
              />
            </Box>

            {/* Pricing Summary */}
            {totalDays > 0 && (
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Pricing Summary
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Vehicle Rate ({totalDays} days):</Typography>
                  <Typography>{PaymentService.formatCurrency(pricing.vehicleRate * totalDays, 'IDR')}</Typography>
                </Box>
                {pricing.addOnsTotal > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Add-ons:</Typography>
                    <Typography>{PaymentService.formatCurrency(pricing.addOnsTotal, 'IDR')}</Typography>
                  </Box>
                )}
                {pricing.deliveryFee > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Delivery Fee:</Typography>
                    <Typography>{PaymentService.formatCurrency(pricing.deliveryFee, 'IDR')}</Typography>
                  </Box>
                )}
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6">{PaymentService.formatCurrency(pricing.total, 'IDR')}</Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  Security deposit: {PaymentService.formatCurrency(vehicle.security_deposit, 'IDR')} (refundable)
                </Typography>
              </Grid>
            )}
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            <Button variant="outlined" onClick={onCancel} disabled={loading}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleBookingSubmit}
              disabled={loading || totalDays === 0}
              sx={{ flex: 1 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Book Now'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={showPayment} onClose={() => setShowPayment(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Complete Payment</DialogTitle>
        <DialogContent>
          {paymentIntent && (
            <Elements stripe={stripePromise}>
              <PaymentForm
                paymentIntent={paymentIntent}
                amount={pricing.total}
                onSuccess={(bookingId) => {
                  setShowPayment(false)
                  onBookingComplete(bookingId)
                }}
                onError={(error) => setError(error)}
              />
            </Elements>
          )}
        </DialogContent>
      </Dialog>
    </LocalizationProvider>
  )
}

// Payment Form Component
interface PaymentFormProps {
  paymentIntent: any
  amount: number
  onSuccess: (bookingId: string) => void
  onError: (error: string) => void
}

const PaymentForm: React.FC<PaymentFormProps> = ({ paymentIntent, amount, onSuccess, onError }) => {
  const stripe = useStripe()
  const elements = useElements()
  const [processing, setProcessing] = useState(false)

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) return

    setProcessing(true)

    const cardElement = elements.getElement(CardElement)
    if (!cardElement) return

    const { error, paymentIntent: confirmedPayment } = await stripe.confirmCardPayment(
      paymentIntent.clientSecret,
      {
        payment_method: {
          card: cardElement,
        },
      }
    )

    if (error) {
      onError(error.message || 'Payment failed')
    } else if (confirmedPayment?.status === 'succeeded') {
      // Payment succeeded - call success handler directly
      onSuccess(paymentIntent.bookingId)
    }

    setProcessing(false)
  }

  return (
    <form onSubmit={handleSubmit} data-testid="payment-form">
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Total: {PaymentService.formatCurrency(amount, 'IDR')}
        </Typography>
      </Box>

      <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, mb: 2 }}>
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
            },
          }}
        />
      </Box>

      <Button
        type="submit"
        variant="contained"
        fullWidth
        disabled={!stripe || processing}
        data-testid="pay-now-button"
      >
        {processing ? <CircularProgress size={24} /> : 'Pay Now'}
      </Button>
    </form>
  )
}

export default BookingForm