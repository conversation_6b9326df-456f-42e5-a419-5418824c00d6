import React from 'react';
import {
  Box,
  Typography,
  FormControlLabel,
  Switch,
  TextField,
  Grid,
  Paper,
  Divider,
  Chip,
  Alert
} from '@mui/material';
import {
  Security as SecurityIcon,
  LocalShipping as DeliveryIcon,
  DirectionsCar as PickupIcon,
  SportsMotorsports as HelmetIcon,
  Umbrella as RaincoatIcon,
  LocalGasStation as GasIcon,
  AccountBalance as DepositIcon
} from '@mui/icons-material';

interface RentalOptionsProps {
  insuranceOffered: boolean;
  dropoffAvailable: boolean;
  pickupAvailable: boolean;
  helmetsIncluded: number;
  raincoatsIncluded: boolean;
  fullTank: boolean;
  depositAmount: number;
  onInsuranceChange: (value: boolean) => void;
  onDropoffChange: (value: boolean) => void;
  onPickupChange: (value: boolean) => void;
  onHelmetsChange: (value: number) => void;
  onRaincoatsChange: (value: boolean) => void;
  onFullTankChange: (value: boolean) => void;
  onDepositChange: (value: number) => void;
  disabled?: boolean;
}

const RentalOptions: React.FC<RentalOptionsProps> = ({
  insuranceOffered,
  dropoffAvailable,
  pickupAvailable,
  helmetsIncluded,
  raincoatsIncluded,
  fullTank,
  depositAmount,
  onInsuranceChange,
  onDropoffChange,
  onPickupChange,
  onHelmetsChange,
  onRaincoatsChange,
  onFullTankChange,
  onDepositChange,
  disabled = false
}) => {
  const handleDepositChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value) || 0;
    onDepositChange(value);
  };

  const handleHelmetsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value) || 0;
    onHelmetsChange(Math.max(0, value));
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        Rental Options
      </Typography>
      
      <Paper elevation={1} sx={{ p: 3, mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
          Service Options
        </Typography>
        
        <Grid container spacing={3}>
          {/* Insurance */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SecurityIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Insurance Offered
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={insuranceOffered}
                  onChange={(e) => onInsuranceChange(e.target.checked)}
                  disabled={disabled}
                />
              }
              label="Include insurance coverage"
            />
            {insuranceOffered && (
              <Alert severity="info" sx={{ mt: 1 }}>
                Insurance coverage will be available to renters
              </Alert>
            )}
          </Grid>

          {/* Delivery/Dropoff */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <DeliveryIcon sx={{ mr: 1, color: 'success.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Drop-off Service
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={dropoffAvailable}
                  onChange={(e) => onDropoffChange(e.target.checked)}
                  disabled={disabled}
                />
              }
              label="Offer drop-off service"
            />
            {dropoffAvailable && (
              <Alert severity="success" sx={{ mt: 1 }}>
                Renters can request drop-off service
              </Alert>
            )}
          </Grid>

          {/* Pickup */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PickupIcon sx={{ mr: 1, color: 'info.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Pickup Service
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={pickupAvailable}
                  onChange={(e) => onPickupChange(e.target.checked)}
                  disabled={disabled}
                />
              }
              label="Offer pickup service"
            />
            {pickupAvailable && (
              <Alert severity="info" sx={{ mt: 1 }}>
                Renters can request pickup service
              </Alert>
            )}
          </Grid>

          {/* Full Tank */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <GasIcon sx={{ mr: 1, color: 'warning.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Full Tank
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={fullTank}
                  onChange={(e) => onFullTankChange(e.target.checked)}
                  disabled={disabled}
                />
              }
              label="Provide full tank of gas"
            />
            {fullTank && (
              <Alert severity="warning" sx={{ mt: 1 }}>
                Vehicle will be provided with full tank
              </Alert>
            )}
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={1} sx={{ p: 3, mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
          Included Items
        </Typography>
        
        <Grid container spacing={3}>
          {/* Helmets */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <HelmetIcon sx={{ mr: 1, color: 'secondary.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Helmets Included
              </Typography>
            </Box>
            <TextField
              type="number"
              value={helmetsIncluded}
              onChange={handleHelmetsChange}
              label="Number of helmets"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                inputProps: { min: 0, max: 10 }
              }}
              helperText="Number of helmets provided with rental"
            />
            {helmetsIncluded > 0 && (
              <Chip
                label={`${helmetsIncluded} helmet${helmetsIncluded > 1 ? 's' : ''} included`}
                color="secondary"
                size="small"
                sx={{ mt: 1 }}
              />
            )}
          </Grid>

          {/* Raincoats */}
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <RaincoatIcon sx={{ mr: 1, color: 'info.main' }} />
              <Typography variant="body1" fontWeight="medium">
                Raincoats Included
              </Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={raincoatsIncluded}
                  onChange={(e) => onRaincoatsChange(e.target.checked)}
                  disabled={disabled}
                />
              }
              label="Include raincoats"
            />
            {raincoatsIncluded && (
              <Alert severity="info" sx={{ mt: 1 }}>
                Raincoats will be provided with rental
              </Alert>
            )}
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={1} sx={{ p: 3 }}>
        <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
          Deposit Requirements
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <DepositIcon sx={{ mr: 1, color: 'error.main' }} />
          <Typography variant="body1" fontWeight="medium">
            Security Deposit
          </Typography>
        </Box>
        
        <TextField
          type="number"
          value={depositAmount}
          onChange={handleDepositChange}
          label="Deposit amount (IDR)"
          variant="outlined"
          fullWidth
          disabled={disabled}
          InputProps={{
            startAdornment: <Typography variant="body2" sx={{ mr: 1 }}>Rp</Typography>,
            inputProps: { min: 0, step: 10000 }
          }}
          helperText="Security deposit required for rental (0 = no deposit)"
        />
        
        {depositAmount > 0 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Renters will be required to pay a {depositAmount.toLocaleString('id-ID')} IDR deposit
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default RentalOptions; 