import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Help,
  Support,
  Phone,
  Email,
  WhatsApp,
  Close,
  Send
} from '@mui/icons-material';

interface HelpSupportModalProps {
  open: boolean;
  onClose: () => void;
  vehicleId?: string;
  bookingId?: string;
}

const HelpSupportModal: React.FC<HelpSupportModalProps> = ({
  open,
  onClose,
  vehicleId,
  bookingId
}) => {
  const [issueType, setIssueType] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (!issueType || !subject || !message) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API call to submit support ticket
      // const response = await SupportService.createTicket({
      //   issueType,
      //   subject,
      //   message,
      //   vehicleId,
      //   bookingId
      // });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubmitted(true);
      setTimeout(() => {
        onClose();
        setSubmitted(false);
        setIssueType('');
        setSubject('');
        setMessage('');
      }, 2000);
    } catch (error) {
      console.error('Error submitting support ticket:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      setIssueType('');
      setSubject('');
      setMessage('');
      setSubmitted(false);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Help sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">Help & Support</Typography>
          </Box>
          <Button onClick={handleClose} disabled={loading}>
            <Close />
          </Button>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {submitted ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            Your support ticket has been submitted successfully! We'll get back to you soon.
          </Alert>
        ) : (
          <>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Need help with your booking? We're here to assist you 24/7.
            </Typography>

            {/* Quick Contact Options */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Contact
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Phone color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Call Support" 
                    secondary="+****************"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Email color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Email Support" 
                    secondary="<EMAIL>"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <WhatsApp color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="WhatsApp Support" 
                    secondary="Available 24/7"
                  />
                </ListItem>
              </List>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Support Ticket Form */}
            <Typography variant="h6" gutterBottom>
              Submit Support Ticket
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Issue Type</InputLabel>
              <Select
                value={issueType}
                onChange={(e) => setIssueType(e.target.value)}
                label="Issue Type"
              >
                <MenuItem value="technical">Technical Issue</MenuItem>
                <MenuItem value="billing">Billing & Payment</MenuItem>
                <MenuItem value="vehicle">Vehicle Problem</MenuItem>
                <MenuItem value="booking">Booking Modification</MenuItem>
                <MenuItem value="delivery">Delivery Issue</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              multiline
              rows={4}
              placeholder="Please describe your issue in detail..."
            />
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        {!submitted && (
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !issueType || !subject || !message}
            startIcon={loading ? <CircularProgress size={20} /> : <Send />}
          >
            {loading ? 'Submitting...' : 'Submit Ticket'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default HelpSupportModal; 