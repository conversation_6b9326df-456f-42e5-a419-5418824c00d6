import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  Chip,
  Box,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment,
  CircularProgress
} from '@mui/material';
import {
  DirectionsCar,
  CalendarToday,
  AttachMoney,
  LocationOn,
  Search,
  FilterList
} from '@mui/icons-material';
import { Vehicle } from '../types';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import ListedVehicleService, { ListedVehicle } from '../services/ListedVehicleService';
import EmptyListingsCTA from './EmptyListingsCTA';

const VehicleList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [brandFilter, setBrandFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [sortBy, setSortBy] = useState('brand');
  const [vehicles, setVehicles] = useState<ListedVehicle[]>([]);
  const [hasListings, setHasListings] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchParams] = useSearchParams();

  // Handle search parameters from homepage
  useEffect(() => {
    const location = searchParams.get('location');
    const vehicleType = searchParams.get('type');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const radius = searchParams.get('radius');
    const errorParam = searchParams.get('error');

    console.log('🔍 VehicleList: Received search parameters:', {
      location, vehicleType, startDate, endDate, minPrice, maxPrice, radius, errorParam
    });

    // Set initial filters based on URL parameters
    if (location) setSearchTerm(location);
    if (vehicleType) setCategoryFilter(vehicleType);

    // Show error messages if search failed
    if (errorParam === 'search_failed') {
      setError('Search failed. Showing all available vehicles.');
    } else if (errorParam === 'connection_failed') {
      setError('Connection failed. Showing all available vehicles.');
    }

    // Show search summary if parameters exist
    const hasSearchParams = location || vehicleType || startDate || endDate || minPrice || maxPrice;
    if (hasSearchParams) {
      const searchSummary = [];
      if (location) searchSummary.push(`📍 ${location}`);
      if (vehicleType) searchSummary.push(`🏍️ ${vehicleType}`);
      if (minPrice || maxPrice) searchSummary.push(`💰 $${minPrice || 0}-${maxPrice || '∞'}`);
      if (radius) searchSummary.push(`📏 ${radius}km radius`);

      console.log('🔍 Search Summary:', searchSummary.join(' • '));
    }
  }, [searchParams]);
  
  // Fetch real listed vehicles
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        console.log('🔍 Fetching real listed vehicles...');

        // Apply search and filters
        const searchFilters = {
          category: categoryFilter || undefined,
          make: brandFilter || undefined
        };

        const response = await ListedVehicleService.searchVehicles(
          searchTerm || undefined,
          searchFilters,
          100 // Show up to 100 vehicles
        );

        console.log('📊 Listed vehicles response:', response);

        if (response.success && response.hasListings) {
          console.log('✅ Found real listings:', response.data);
          setVehicles(response.data);
          setHasListings(true);
        } else {
          console.log('❌ No real listings found - will show CTA');
          setVehicles([]);
          setHasListings(false);
        }
      } catch (err) {
        console.error('❌ Fetch error:', err);
        setError('Failed to load vehicles');
        setHasListings(false);
        setVehicles([]);
      } finally {
        setLoading(false);
      }
    };

    fetchVehicles();
  }, [searchTerm, brandFilter, categoryFilter]);

  // Handle booking button click
  const handleBookNow = (vehicle: ListedVehicle) => {
    if (!user) {
      navigate('/signin');
      return;
    }
    navigate('/checkout', {
      state: {
        vehicleId: vehicle.id,
        pickupLocation: 'Jakarta Airport',
        dropOffLocation: 'Jakarta Airport',
        from: new Date().toISOString(),
        to: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      }
    });
  };

  // Filter and sort vehicles
  const filteredVehicles = vehicles
    .filter(vehicle => {
      if (!vehicle || !vehicle.make || !vehicle.model) {
        return false;
      }

      const matchesSearch = vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (vehicle.description && vehicle.description.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesBrand = !brandFilter || vehicle.make === brandFilter;
      const matchesCategory = !categoryFilter || vehicle.category === categoryFilter;

      return matchesSearch && matchesBrand && matchesCategory;
    })
    .sort((a, b) => {
      if (!a || !b) return 0;

      switch (sortBy) {
        case 'brand':
          return (a.make || '').localeCompare(b.make || '');
        case 'price':
          return (a.dailyRate || 0) - (b.dailyRate || 0);
        case 'year':
          return (b.year || 0) - (a.year || 0);
        default:
          return 0;
      }
    });

  // Get unique brands and categories for filters
  const brands = [...new Set(vehicles.map(v => v?.make).filter(Boolean))].sort();
  const categories = [...new Set(vehicles.map(v => v?.category).filter(Boolean))].sort();

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Available Vehicles ({filteredVehicles.length})
      </Typography>

      {/* Search Summary */}
      {(() => {
        const location = searchParams.get('location');
        const vehicleType = searchParams.get('type');
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const minPrice = searchParams.get('minPrice');
        const maxPrice = searchParams.get('maxPrice');
        const radius = searchParams.get('radius');

        const hasSearchParams = location || vehicleType || startDate || endDate || minPrice || maxPrice;

        if (hasSearchParams) {
          return (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                🔍 Search Results for:
              </Typography>
              <Box component="span" sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {location && <Chip size="small" label={`📍 ${location}`} color="primary" variant="outlined" />}
                {vehicleType && <Chip size="small" label={`🏍️ ${vehicleType}`} color="primary" variant="outlined" />}
                {(minPrice || maxPrice) && <Chip size="small" label={`💰 $${minPrice || 0}-${maxPrice || '∞'}/day`} color="primary" variant="outlined" />}
                {radius && <Chip size="small" label={`📏 ${radius}km radius`} color="primary" variant="outlined" />}
                {(startDate || endDate) && <Chip size="small" label={`📅 ${startDate || '?'} to ${endDate || '?'}`} color="primary" variant="outlined" />}
              </Box>
            </Alert>
          );
        }
        return null;
      })()}

      {/* Search and Filter Section */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search vehicles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Brand</InputLabel>
              <Select
                value={brandFilter}
                onChange={(e) => setBrandFilter(e.target.value)}
                label="Brand"
              >
                <MenuItem value="">All Brands</MenuItem>
                {brands.map(brand => (
                  <MenuItem key={brand} value={brand}>{brand}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
                data-testid="category-filter"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category} value={category}>{category}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                label="Sort By"
              >
                <MenuItem value="brand">Brand</MenuItem>
                <MenuItem value="price">Price</MenuItem>
                <MenuItem value="year">Year</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {/* Vehicle Grid */}
      <Grid container spacing={3} data-testid="vehicle-list">
        {filteredVehicles.map((vehicle) => (
          <Grid item xs={12} sm={6} md={4} key={vehicle.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="img"
                height="200"
                image={vehicle.images?.[0] || 'https://via.placeholder.com/300x200?text=Vehicle'}
                alt={`${vehicle.make} ${vehicle.model}`}
                sx={{ objectFit: 'cover' }}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="h2" gutterBottom>
                  {vehicle.make} {vehicle.model}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {vehicle.year} • {vehicle.category}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Provider: {vehicle.provider?.businessName || 'Unknown'}
                </Typography>

                <Box sx={{ mt: 2, mb: 2 }}>
                  <Typography variant="h6" color="primary">
                    ₹{vehicle.dailyRate}/day
                  </Typography>
                  {vehicle.weeklyRate && vehicle.monthlyRate && (
                    <Typography variant="body2" color="text.secondary">
                      ₹{vehicle.weeklyRate}/week • ₹{vehicle.monthlyRate}/month
                    </Typography>
                  )}
                </Box>

                <Box sx={{ mb: 2 }}>
                  {vehicle.features?.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </Box>

                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => handleBookNow(vehicle)}
                  startIcon={<CalendarToday />}
                >
                  Book Now
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredVehicles.length === 0 && !loading && !error && (
        <EmptyListingsCTA variant="search" />
      )}
    </Container>
  );
};

export default VehicleList; 