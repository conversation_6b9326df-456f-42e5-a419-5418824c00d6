import React, { useState, useEffect, ReactElement } from 'react';
import {
  Box,
  Typography,
  Stack,
  Tabs,
  Tab,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  ImageList,
  ImageListItem,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import axios from 'axios';

interface RideChecklistViewProps {
  bookingId: string;
}

interface RideChecklist {
  id: string;
  stage: 'START' | 'END';
  conditionNotes: string;
  imageUrls: string[];
  createdAt: string;
}

interface DamageReport {
  id: string;
  note: string;
  severity: 'MINOR' | 'MODERATE' | 'SEVERE';
  status: 'PENDING' | 'UNDER_REVIEW' | 'RESOLVED' | 'DISPUTED';
  images: string[];
  createdAt: string;
}

type SeverityStatusColor = "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning";

const ProviderRideChecklistView: React.FC<RideChecklistViewProps> = ({ bookingId }): ReactElement => {
  const [checklists, setChecklists] = useState<RideChecklist[]>([]);
  const [damageReports, setDamageReports] = useState<DamageReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    const fetchChecklistData = async () => {
      try {
        const [checklistResponse, damageResponse] = await Promise.all([
          axios.get(`/api/ride-checklist/${bookingId}`),
          axios.get(`/api/ride-checklist/${bookingId}/damage`)
        ]);

        setChecklists(checklistResponse.data);
        setDamageReports(damageResponse.data);
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch checklist data', error);
        setLoading(false);
      }
    };

    fetchChecklistData();
  }, [bookingId]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getSeverityColor = (severity: string): SeverityStatusColor => {
    switch (severity) {
      case 'MINOR':
        return 'success';
      case 'MODERATE':
        return 'warning';
      case 'SEVERE':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string): SeverityStatusColor => {
    switch (status) {
      case 'PENDING':
        return 'warning';
      case 'UNDER_REVIEW':
        return 'info';
      case 'RESOLVED':
        return 'success';
      case 'DISPUTED':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  const renderChecklistSection = (stage: 'START' | 'END') => {
    const filteredChecklists = checklists.filter(c => c.stage === stage);
    
    if (filteredChecklists.length === 0) {
      return (
        <Typography color="text.secondary">
          No {stage.toLowerCase()} checklist available
        </Typography>
      );
    }

    return filteredChecklists.map(checklist => (
      <Accordion key={checklist.id}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>
            {stage} Checklist - {new Date(checklist.createdAt).toLocaleString()}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Stack spacing={2}>
            {checklist.conditionNotes && (
              <Typography>
                <strong>Notes:</strong> {checklist.conditionNotes}
              </Typography>
            )}
            <ImageList cols={3} gap={8}>
              {checklist.imageUrls.map((url, index) => (
                <ImageListItem key={index}>
                  <Box
                    component="img"
                    src={url}
                    alt={`Checklist image ${index + 1}`}
                    loading="lazy"
                    sx={{
                      height: 150,
                      width: '100%',
                      objectFit: 'cover',
                      borderRadius: 1
                    }}
                  />
                </ImageListItem>
              ))}
            </ImageList>
          </Stack>
        </AccordionDetails>
      </Accordion>
    ));
  };

  const renderDamageReports = () => {
    if (damageReports.length === 0) {
      return (
        <Typography color="text.secondary">
          No damage reports
        </Typography>
      );
    }

    return damageReports.map(report => (
      <Accordion key={report.id}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Chip
              label={report.severity}
              color={getSeverityColor(report.severity)}
              size="small"
            />
            <Chip
              label={report.status.replace('_', ' ')}
              color={getStatusColor(report.status)}
              size="small"
            />
            <Typography>
              {new Date(report.createdAt).toLocaleString()}
            </Typography>
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <Stack spacing={2}>
            <Typography>
              <strong>Damage Description:</strong> {report.note}
            </Typography>
            <ImageList cols={3} gap={8}>
              {report.images.map((url, index) => (
                <ImageListItem key={index}>
                  <Box
                    component="img"
                    src={url}
                    alt={`Damage report image ${index + 1}`}
                    loading="lazy"
                    sx={{
                      height: 150,
                      width: '100%',
                      objectFit: 'cover',
                      borderRadius: 1
                    }}
                  />
                </ImageListItem>
              ))}
            </ImageList>
          </Stack>
        </AccordionDetails>
      </Accordion>
    ));
  };

  return (
    <Box width="100%">
      <Tabs value={tabValue} onChange={handleTabChange}>
        <Tab label="Start Checklist" />
        <Tab label="End Checklist" />
        <Tab label="Damage Reports" />
      </Tabs>

      <Box sx={{ mt: 2 }}>
        {tabValue === 0 && renderChecklistSection('START')}
        {tabValue === 1 && renderChecklistSection('END')}
        {tabValue === 2 && renderDamageReports()}
      </Box>
    </Box>
  );
};

export default ProviderRideChecklistView;