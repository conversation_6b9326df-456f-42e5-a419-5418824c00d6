import React from 'react';
import { 
    Box, 
    Typography, 
    Alert, 
    AlertTitle 
} from '@mui/material';
import { 
    Shield, 
    CreditCard, 
    Info 
} from 'react-feather';

interface BookingConfirmationInsuranceDepositProps {
    bookingData: {
        vehicleId: number;
        insuranceProvided: boolean;
        depositRequired: boolean;
        depositAmount?: number;
        insuranceNotes?: string;
        depositNotes?: string;
    };
}

export const BookingConfirmationInsuranceDeposit: React.FC<BookingConfirmationInsuranceDepositProps> = ({ 
    bookingData 
}) => {
    return (
        <Box sx={{ 
            p: 2, 
            border: '1px solid #e0e0e0', 
            borderRadius: 2,
            backgroundColor: '#f9f9f9'
        }}>
            <Typography variant="h6" gutterBottom>
                Insurance & Deposit Information
            </Typography>

            {/* Insurance Alert */}
            <Alert 
                severity={bookingData.insuranceProvided ? 'success' : 'warning'}
                icon={<Shield />}
                sx={{ mb: 2 }}
            >
                <AlertTitle>
                    {bookingData.insuranceProvided 
                        ? 'Insurance Provided by Vehicle Owner' 
                        : 'Insurance Not Provided'}
                </AlertTitle>
                {bookingData.insuranceNotes && (
                    <Typography variant="body2">
                        {bookingData.insuranceNotes}
                    </Typography>
                )}
            </Alert>

            {/* Deposit Alert */}
            {bookingData.depositRequired && (
                <Alert 
                    severity="info" 
                    icon={<CreditCard />}
                    sx={{ mb: 2 }}
                >
                    <AlertTitle>
                        Deposit Required: ${bookingData.depositAmount?.toFixed(2)}
                    </AlertTitle>
                    {bookingData.depositNotes && (
                        <Typography variant="body2">
                            {bookingData.depositNotes}
                        </Typography>
                    )}
                </Alert>
            )}

            {/* Pickup Instructions */}
            <Alert 
                severity="info" 
                icon={<Info />}
                sx={{ mb: 2 }}
            >
                <AlertTitle>Important Pickup Information</AlertTitle>
                <Typography variant="body2">
                    <strong>At Pickup Location:</strong>
                    <ul>
                        {!bookingData.insuranceProvided && (
                            <li>You must provide your own insurance coverage</li>
                        )}
                        {bookingData.depositRequired && (
                            <li>Security deposit of ${bookingData.depositAmount?.toFixed(2)} will be collected</li>
                        )}
                        <li>All requirements will be verified by the vehicle owner</li>
                    </ul>
                </Typography>
            </Alert>
        </Box>
    );
}; 