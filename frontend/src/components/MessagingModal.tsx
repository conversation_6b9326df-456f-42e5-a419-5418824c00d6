import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Paper,
  IconButton
} from '@mui/material';
import {
  Message,
  Send,
  Close,
  Person,
  AttachFile,
  EmojiEmotions
} from '@mui/icons-material';

interface Message {
  id: string;
  sender: 'user' | 'owner';
  content: string;
  timestamp: Date;
  senderName: string;
}

interface MessagingModalProps {
  open: boolean;
  onClose: () => void;
  vehicleId?: string;
  bookingId?: string;
  ownerName?: string;
  ownerAvatar?: string;
}

const MessagingModal: React.FC<MessagingModalProps> = ({
  open,
  onClose,
  vehicleId,
  bookingId,
  ownerName = 'Vehicle Owner',
  ownerAvatar
}) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'owner',
      content: 'Hi! I\'m the owner of this vehicle. How can I help you with your booking?',
      timestamp: new Date(Date.now() - 3600000),
      senderName: ownerName
    },
    {
      id: '2',
      sender: 'user',
      content: 'Hi! I have a question about the pickup location.',
      timestamp: new Date(Date.now() - 1800000),
      senderName: 'You'
    },
    {
      id: '3',
      sender: 'owner',
      content: 'Of course! The pickup location is at the address listed in your booking. I\'ll be there to hand over the keys.',
      timestamp: new Date(Date.now() - 900000),
      senderName: ownerName
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setSending(true);
    try {
      // TODO: Implement API call to send message
      // const response = await MessagingService.sendMessage({
      //   bookingId,
      //   vehicleId,
      //   content: message
      // });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newMessage: Message = {
        id: Date.now().toString(),
        sender: 'user',
        content: message,
        timestamp: new Date(),
        senderName: 'You'
      };
      
      setMessages(prev => [...prev, newMessage]);
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleClose = () => {
    if (!sending) {
      onClose();
      setMessage('');
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Message sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">Message {ownerName}</Typography>
          </Box>
          <Button onClick={handleClose} disabled={sending}>
            <Close />
          </Button>
        </Box>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ height: '400px', display: 'flex', flexDirection: 'column' }}>
          {/* Messages List */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
            <List>
              {messages.map((msg) => (
                <ListItem
                  key={msg.id}
                  sx={{
                    flexDirection: 'column',
                    alignItems: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                    mb: 1
                  }}
                >
                  <Paper
                    sx={{
                      p: 2,
                      maxWidth: '70%',
                      bgcolor: msg.sender === 'user' ? 'primary.main' : 'grey.100',
                      color: msg.sender === 'user' ? 'white' : 'text.primary'
                    }}
                  >
                    <Box display="flex" alignItems="center" sx={{ mb: 1 }}>
                      <Avatar
                        src={msg.sender === 'owner' ? ownerAvatar : undefined}
                        sx={{ width: 24, height: 24, mr: 1 }}
                      >
                        {msg.sender === 'owner' ? <Person /> : 'U'}
                      </Avatar>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                        {msg.senderName}
                      </Typography>
                      <Typography variant="caption" sx={{ ml: 'auto', opacity: 0.7 }}>
                        {formatTime(msg.timestamp)}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      {msg.content}
                    </Typography>
                  </Paper>
                </ListItem>
              ))}
            </List>
          </Box>

          <Divider />

          {/* Message Input */}
          <Box sx={{ p: 2 }}>
            <Box display="flex" alignItems="center" gap={1}>
              <TextField
                fullWidth
                placeholder="Type your message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                multiline
                maxRows={3}
                disabled={sending}
              />
              <IconButton
                onClick={handleSendMessage}
                disabled={sending || !message.trim()}
                color="primary"
              >
                {sending ? <CircularProgress size={20} /> : <Send />}
              </IconButton>
            </Box>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default MessagingModal; 