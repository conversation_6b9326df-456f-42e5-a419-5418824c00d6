import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  SvgIconProps,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh,
  Search,
  Info,
} from '@mui/icons-material';

interface EmptyStateAction {
  label: string;
  onClick: () => void;
  variant?: 'contained' | 'outlined' | 'text';
  icon?: React.ComponentType<SvgIconProps>;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
}

interface EmptyStateFeature {
  icon: React.ComponentType<SvgIconProps>;
  title: string;
  description: string;
  color?: string;
}

interface EmptyStateCTAProps {
  title: string;
  subtitle: string;
  description: string;
  icon?: React.ComponentType<SvgIconProps>;
  iconColor?: string;
  actions?: EmptyStateAction[];
  features?: EmptyStateFeature[];
  footerText?: string;
  variant?: 'default' | 'search' | 'error' | 'info';
  className?: string;
}

const EmptyStateCTA: React.FC<EmptyStateCTAProps> = ({
  title,
  subtitle,
  description,
  icon: IconComponent = AddIcon,
  iconColor = '#2196f3',
  actions = [],
  features = [],
  footerText,
  variant = 'default',
  className = '',
}) => {
  const getBackgroundGradient = () => {
    switch (variant) {
      case 'search':
        return 'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)';
      case 'error':
        return 'linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)';
      case 'info':
        return 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)';
      default:
        return 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)';
    }
  };

  const getDefaultActions = (): EmptyStateAction[] => {
    if (actions.length > 0) return actions;
    
    return [
      {
        label: 'Get Started',
        onClick: () => console.log('Get started clicked'),
        variant: 'contained',
        icon: AddIcon,
      },
      {
        label: 'Refresh',
        onClick: () => window.location.reload(),
        variant: 'outlined',
        icon: Refresh,
      },
    ];
  };

  return (
    <Box className={className}>
      {/* Main CTA Section */}
      <Paper
        elevation={2}
        sx={{
          background: getBackgroundGradient(),
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          mb: features.length > 0 ? 4 : 0,
        }}
      >
        {/* Icon */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              width: 100,
              height: 100,
              bgcolor: iconColor,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              boxShadow: 3,
            }}
          >
            <IconComponent sx={{ fontSize: 50, color: 'white' }} />
          </Box>
        </Box>

        {/* Content */}
        <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          {title}
        </Typography>

        <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
          {subtitle}
        </Typography>

        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: '600px', mx: 'auto' }}>
          {description}
        </Typography>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          {getDefaultActions().map((action, index) => (
            <Button
              key={index}
              onClick={action.onClick}
              variant={action.variant || 'contained'}
              color={action.color || 'primary'}
              size="large"
              startIcon={action.icon ? <action.icon /> : undefined}
              sx={{
                px: 4,
                py: 1.5,
                fontWeight: 'bold',
                borderRadius: 2,
                ...(action.variant === 'outlined' && { borderWidth: 2 }),
              }}
            >
              {action.label}
            </Button>
          ))}
        </Box>

        {/* Footer Text */}
        {footerText && (
          <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {footerText}
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Features Section */}
      {features.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
            What You Can Do
          </Typography>
          
          <Grid container spacing={3}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={features.length > 4 ? 4 : 6} key={index}>
                <Card 
                  sx={{ 
                    height: '100%',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 3 }}>
                    <Avatar
                      sx={{
                        bgcolor: feature.color || '#2196f3',
                        width: 60,
                        height: 60,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <feature.icon sx={{ fontSize: 30 }} />
                    </Avatar>
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default EmptyStateCTA;
