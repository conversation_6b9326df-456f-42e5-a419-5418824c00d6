import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Stepper,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  <PERSON>ton,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Paper,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  Radio,
  RadioGroup,
  FormLabel,
  Checkbox,
  FormGroup,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  DirectionsBike as VehicleIcon,
  Verified as VerifiedIcon,
  Upload as UploadIcon,
  PhotoCamera as PhotoIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Edit as EditIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import * as UserService from '../services/UserService';
import { User, UserType, VehicleCategory } from '../types';

interface OnboardingData {
  // Personal Information
  firstName: string;
  lastName: string;
  phone: string;
  birthDate: string;
  language: string;
  
  // Role Selection
  userType: UserType;
  
  // Provider Information (if applicable)
  businessName: string;
  businessType: 'individual' | 'shop';
  businessRegistration: string;
  address: string;
  city: string;
  description: string;
  
  // Document Upload
  driverLicense: File | null;
  idCard: File | null;
  businessLicense: File | null;
  profileImage: File | null;
  
  // Preferences
  enableEmailNotifications: boolean;
  enableSmsNotifications: boolean;
  acceptedTerms: boolean;
  acceptedPrivacy: boolean;
  
  // Driving Lessons (if provider)
  offersDrivingLessons: boolean;
  lessonCategories: VehicleCategory[];
  lessonPricing: Record<VehicleCategory, number>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`onboarding-tabpanel-${index}`}
      aria-labelledby={`onboarding-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const steps = [
  'Personal Information',
  'Role Selection',
  'Profile Setup',
  'Document Upload',
  'Verification',
  'Complete'
];

export const UserOnboarding: React.FC = () => {
  const { user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const [formData, setFormData] = useState<OnboardingData>({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: user?.phone || '',
    birthDate: '',
    language: 'en',
    userType: UserType.User,
    businessName: '',
    businessType: 'individual',
    businessRegistration: '',
    address: '',
    city: '',
    description: '',
    driverLicense: null,
    idCard: null,
    businessLicense: null,
    profileImage: null,
    enableEmailNotifications: true,
    enableSmsNotifications: true,
    acceptedTerms: false,
    acceptedPrivacy: false,
    offersDrivingLessons: false,
    lessonCategories: [],
    lessonPricing: {} as Record<VehicleCategory, number>
  });

  const [verification, setVerification] = useState({
    emailVerified: user?.verified || false,
    phoneVerified: false,
    documentsVerified: false,
    profileComplete: false
  });

  const handleInputChange = (field: keyof OnboardingData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = async (field: keyof OnboardingData, file: File) => {
    try {
      setLoading(true);
      setUploadProgress(prev => ({ ...prev, [field]: 0 }));
      
      // Simulate upload progress
      const interval = setInterval(() => {
        setUploadProgress(prev => ({
          ...prev,
          [field]: Math.min((prev[field] || 0) + 10, 90)
        }));
      }, 200);

      // Upload file (mock implementation)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(interval);
      setUploadProgress(prev => ({ ...prev, [field]: 100 }));
      
      handleInputChange(field, file);
      
      setTimeout(() => {
        setUploadProgress(prev => ({ ...prev, [field]: 0 }));
      }, 1000);
    } catch (error) {
      setError('Failed to upload file. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = async () => {
    if (activeStep === steps.length - 1) {
      // Complete onboarding
      await handleComplete();
      return;
    }

    // Validate current step
    if (!validateStep(activeStep)) {
      return;
    }

    // Save progress for current step
    await saveStepProgress(activeStep);
    
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0: // Personal Information
        return !!(formData.firstName && formData.lastName && formData.phone);
      case 1: // Role Selection
        return !!formData.userType;
      case 2: // Profile Setup
        if (formData.userType === UserType.Provider) {
          return !!(formData.businessName && formData.address && formData.city);
        }
        return true;
      case 3: // Document Upload
        return !!(formData.driverLicense && formData.idCard);
      case 4: // Verification
        return formData.acceptedTerms && formData.acceptedPrivacy;
      default:
        return true;
    }
  };

  const saveStepProgress = async (step: number) => {
    try {
      setLoading(true);
      // Save current progress to backend
      await UserService.updateProfile(user?.id || '', {
        ...formData,
        onboardingStep: step + 1
      } as any);
    } catch (error) {
      console.error('Failed to save progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = async () => {
    try {
      setLoading(true);
      
      // Complete user onboarding
      const updatedUser = await UserService.completeOnboarding(formData);
      
      if (updatedUser) {
        // Redirect to appropriate dashboard
        if (formData.userType === UserType.Provider) {
          window.location.href = '/provider-dashboard';
        } else {
          window.location.href = '/dashboard';
        }
      }
    } catch (error) {
      setError('Failed to complete onboarding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationEmail = async () => {
    try {
      await UserService.sendVerificationEmail();
      setError(null);
    } catch (error) {
      setError('Failed to send verification email.');
    }
  };

  const sendVerificationSMS = async () => {
    try {
      await UserService.sendVerificationSMS(formData.phone);
      setError(null);
    } catch (error) {
      setError('Failed to send verification SMS.');
    }
  };

  const renderPersonalInformation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="First Name *"
          value={formData.firstName}
          onChange={(e) => handleInputChange('firstName', e.target.value)}
          required
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Last Name *"
          value={formData.lastName}
          onChange={(e) => handleInputChange('lastName', e.target.value)}
          required
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Phone Number *"
          value={formData.phone}
          onChange={(e) => handleInputChange('phone', e.target.value)}
          required
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Date of Birth"
          type="date"
          value={formData.birthDate}
          onChange={(e) => handleInputChange('birthDate', e.target.value)}
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Language</InputLabel>
          <Select
            value={formData.language}
            onChange={(e) => handleInputChange('language', e.target.value)}
          >
            <MenuItem value="en">English</MenuItem>
            <MenuItem value="es">Spanish</MenuItem>
            <MenuItem value="fr">French</MenuItem>
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );

  const renderRoleSelection = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Choose Your Role
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Select how you plan to use RENTAHUB
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              border: formData.userType === UserType.User ? 2 : 1,
              borderColor: formData.userType === UserType.User ? 'primary.main' : 'divider'
            }}
            onClick={() => handleInputChange('userType', UserType.User)}
          >
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <PersonIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Renter
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Rent vehicles for personal use
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              border: formData.userType === UserType.Provider ? 2 : 1,
              borderColor: formData.userType === UserType.Provider ? 'primary.main' : 'divider'
            }}
            onClick={() => handleInputChange('userType', UserType.Provider)}
          >
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <BusinessIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Provider
              </Typography>
              <Typography variant="body2" color="text.secondary">
                List and rent out your vehicles
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card 
            sx={{ 
              cursor: 'pointer',
              border: formData.userType === UserType.Admin ? 2 : 1,
              borderColor: formData.userType === UserType.Admin ? 'primary.main' : 'divider'
            }}
            onClick={() => handleInputChange('userType', UserType.Admin)}
          >
            <CardContent sx={{ textAlign: 'center', p: 3 }}>
              <VehicleIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Administrator
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Manage platform operations
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderProfileSetup = () => (
    <Box>
      {formData.userType === UserType.Provider ? (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Business Information
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Business Name *"
              value={formData.businessName}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Business Type *</InputLabel>
              <Select
                value={formData.businessType}
                onChange={(e) => handleInputChange('businessType', e.target.value)}
              >
                <MenuItem value="individual">Individual</MenuItem>
                <MenuItem value="shop">Shop/Company</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          {formData.businessType === 'shop' && (
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Business Registration Number"
                value={formData.businessRegistration}
                onChange={(e) => handleInputChange('businessRegistration', e.target.value)}
              />
            </Grid>
          )}
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Address *"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="City *"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              required
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Business Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Tell customers about your business..."
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.offersDrivingLessons}
                  onChange={(e) => handleInputChange('offersDrivingLessons', e.target.checked)}
                />
              }
              label="I offer driving lessons"
            />
          </Grid>
          
          {formData.offersDrivingLessons && (
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Lesson Categories
              </Typography>
              <FormGroup>
                {Object.values(VehicleCategory).map((category) => (
                  <FormControlLabel
                    key={category}
                    control={
                      <Checkbox
                        checked={formData.lessonCategories.includes(category)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('lessonCategories', [...formData.lessonCategories, category]);
                          } else {
                            handleInputChange('lessonCategories', formData.lessonCategories.filter(c => c !== category));
                          }
                        }}
                      />
                    }
                    label={category.replace('_', ' ').toUpperCase()}
                  />
                ))}
              </FormGroup>
            </Grid>
          )}
        </Grid>
      ) : (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Profile Setup Complete
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Your basic profile is ready. Continue to document upload.
          </Typography>
        </Box>
      )}
    </Box>
  );

  const renderDocumentUpload = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Upload Required Documents
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Upload clear photos of your documents for verification
        </Typography>
      </Grid>
      
      {/* Profile Image */}
      <Grid item xs={12} sm={6}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}>
              <PhotoIcon />
            </Avatar>
            <Typography variant="h6" gutterBottom>
              Profile Photo
            </Typography>
            {uploadProgress.profileImage > 0 && (
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress.profileImage} 
                sx={{ mb: 2 }}
              />
            )}
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="profile-image-upload"
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload('profileImage', file);
              }}
            />
            <label htmlFor="profile-image-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                disabled={loading}
              >
                Upload Photo
              </Button>
            </label>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Driver's License */}
      <Grid item xs={12} sm={6}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <VerifiedIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Driver's License *
            </Typography>
            {uploadProgress.driverLicense > 0 && (
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress.driverLicense} 
                sx={{ mb: 2 }}
              />
            )}
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="license-upload"
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload('driverLicense', file);
              }}
            />
            <label htmlFor="license-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                disabled={loading}
              >
                Upload License
              </Button>
            </label>
            {formData.driverLicense && (
              <Chip
                label="Uploaded"
                color="success"
                size="small"
                sx={{ mt: 1, display: 'block' }}
              />
            )}
          </CardContent>
        </Card>
      </Grid>
      
      {/* ID Card */}
      <Grid item xs={12} sm={6}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <VerifiedIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              ID Card *
            </Typography>
            {uploadProgress.idCard > 0 && (
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress.idCard} 
                sx={{ mb: 2 }}
              />
            )}
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="id-upload"
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload('idCard', file);
              }}
            />
            <label htmlFor="id-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                disabled={loading}
              >
                Upload ID
              </Button>
            </label>
            {formData.idCard && (
              <Chip
                label="Uploaded"
                color="success"
                size="small"
                sx={{ mt: 1, display: 'block' }}
              />
            )}
          </CardContent>
        </Card>
      </Grid>
      
      {/* Business License (if provider) */}
      {formData.userType === UserType.Provider && formData.businessType === 'shop' && (
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <BusinessIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Business License
              </Typography>
              {uploadProgress.businessLicense > 0 && (
                <LinearProgress 
                  variant="determinate" 
                  value={uploadProgress.businessLicense} 
                  sx={{ mb: 2 }}
                />
              )}
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="business-license-upload"
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileUpload('businessLicense', file);
                }}
              />
              <label htmlFor="business-license-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  disabled={loading}
                >
                  Upload License
                </Button>
              </label>
              {formData.businessLicense && (
                <Chip
                  label="Uploaded"
                  color="success"
                  size="small"
                  sx={{ mt: 1, display: 'block' }}
                />
              )}
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  );

  const renderVerification = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Account Verification
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Complete the verification steps to activate your account
        </Typography>
      </Grid>
      
      {/* Email Verification */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box display="flex" alignItems="center">
                <Box sx={{ mr: 2 }}>
                  {verification.emailVerified ? (
                    <CheckIcon color="success" />
                  ) : (
                    <WarningIcon color="warning" />
                  )}
                </Box>
                <Box>
                  <Typography variant="subtitle1">
                    Email Verification
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {user?.email}
                  </Typography>
                </Box>
              </Box>
              {!verification.emailVerified && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={sendVerificationEmail}
                >
                  Send Email
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Phone Verification */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box display="flex" alignItems="center">
                <Box sx={{ mr: 2 }}>
                  {verification.phoneVerified ? (
                    <CheckIcon color="success" />
                  ) : (
                    <WarningIcon color="warning" />
                  )}
                </Box>
                <Box>
                  <Typography variant="subtitle1">
                    Phone Verification
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formData.phone}
                  </Typography>
                </Box>
              </Box>
              {!verification.phoneVerified && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={sendVerificationSMS}
                >
                  Send SMS
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Terms and Privacy */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.acceptedTerms}
                    onChange={(e) => handleInputChange('acceptedTerms', e.target.checked)}
                  />
                }
                label={
                  <Typography variant="body2">
                    I agree to the{' '}
                    <Button variant="text" size="small">
                      Terms of Service
                    </Button>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.acceptedPrivacy}
                    onChange={(e) => handleInputChange('acceptedPrivacy', e.target.checked)}
                  />
                }
                label={
                  <Typography variant="body2">
                    I agree to the{' '}
                    <Button variant="text" size="small">
                      Privacy Policy
                    </Button>
                  </Typography>
                }
              />
            </FormGroup>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Notification Preferences */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Notification Preferences
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.enableEmailNotifications}
                    onChange={(e) => handleInputChange('enableEmailNotifications', e.target.checked)}
                  />
                }
                label="Email notifications"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.enableSmsNotifications}
                    onChange={(e) => handleInputChange('enableSmsNotifications', e.target.checked)}
                  />
                }
                label="SMS notifications"
              />
            </FormGroup>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderComplete = () => (
    <Box sx={{ textAlign: 'center', py: 4 }}>
      <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 3 }} />
      <Typography variant="h4" gutterBottom>
        Welcome to RENTAHUB!
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Your account has been set up successfully. You can now start using the platform.
      </Typography>
      
      <Grid container spacing={3} justifyContent="center">
        <Grid item>
          <Button
            variant="contained"
            size="large"
            onClick={() => {
              if (formData.userType === UserType.Provider) {
                window.location.href = '/provider-dashboard';
              } else {
                window.location.href = '/dashboard';
              }
            }}
          >
            Go to Dashboard
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            size="large"
            onClick={() => window.location.href = '/'}
          >
            Browse Vehicles
          </Button>
        </Grid>
      </Grid>
    </Box>
  );

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return renderPersonalInformation();
      case 1:
        return renderRoleSelection();
      case 2:
        return renderProfileSetup();
      case 3:
        return renderDocumentUpload();
      case 4:
        return renderVerification();
      case 5:
        return renderComplete();
      default:
        return 'Unknown step';
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          Complete Your Profile
        </Typography>
        
        <Stepper activeStep={activeStep} orientation="horizontal" sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 4 }}>
          {getStepContent(activeStep)}
        </Box>

        {activeStep < steps.length - 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
            >
              Back
            </Button>
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={loading || !validateStep(activeStep)}
            >
              {activeStep === steps.length - 2 ? 'Complete' : 'Next'}
            </Button>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default UserOnboarding;
