import React from 'react';
import {
  Box,
  Typography,
  TextField,
  Grid,
  Paper,
  Alert,
  Chip,
  Divider,
  InputAdornment
} from '@mui/material';
import {
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingIcon,
  Discount as DiscountIcon
} from '@mui/icons-material';

interface PricingSectionProps {
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  yearlyRate?: number;
  onDailyRateChange: (value: number) => void;
  onWeeklyRateChange: (value: number) => void;
  onMonthlyRateChange: (value: number) => void;
  onYearlyRateChange: (value: number) => void;
  disabled?: boolean;
}

const PricingSection: React.FC<PricingSectionProps> = ({
  dailyRate,
  weeklyRate,
  monthlyRate,
  yearlyRate,
  onDailyRateChange,
  onWeeklyRateChange,
  onMonthlyRateChange,
  onYearlyRateChange,
  disabled = false
}) => {
  const handleDailyRateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value) || 0;
    onDailyRateChange(Math.max(0, value));
  };

  const handleWeeklyRateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value) || 0;
    onWeeklyRateChange(Math.max(0, value));
  };

  const handleMonthlyRateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value) || 0;
    onMonthlyRateChange(Math.max(0, value));
  };

  const handleYearlyRateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(event.target.value) || 0;
    onYearlyRateChange(Math.max(0, value));
  };

  const calculateWeeklyDiscount = () => {
    if (!weeklyRate || !dailyRate) return 0;
    const weeklyTotal = dailyRate * 7;
    return ((weeklyTotal - weeklyRate) / weeklyTotal) * 100;
  };

  const calculateMonthlyDiscount = () => {
    if (!monthlyRate || !dailyRate) return 0;
    const monthlyTotal = dailyRate * 30;
    return ((monthlyTotal - monthlyRate) / monthlyTotal) * 100;
  };

  const calculateYearlyDiscount = () => {
    if (!yearlyRate || !dailyRate) return 0;
    const yearlyTotal = dailyRate * 365;
    return ((yearlyTotal - yearlyRate) / yearlyTotal) * 100;
  };

  const weeklyDiscount = calculateWeeklyDiscount();
  const monthlyDiscount = calculateMonthlyDiscount();
  const yearlyDiscount = calculateYearlyDiscount();

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        Pricing
      </Typography>
      
      <Paper elevation={1} sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MoneyIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="subtitle1" fontWeight="medium">
            Set Your Rates
          </Typography>
        </Box>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          Set competitive rates for different rental periods. Daily rate is required, others are optional.
        </Alert>

        <Grid container spacing={3}>
          {/* Daily Rate - Required */}
          <Grid item xs={12} sm={6}>
            <TextField
              required
              type="number"
              value={dailyRate}
              onChange={handleDailyRateChange}
              label="Daily Rate (IDR)"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                startAdornment: <InputAdornment position="start">Rp</InputAdornment>,
                inputProps: { min: 0, step: 1000 }
              }}
              helperText="Required - Price per day"
            />
            {dailyRate > 0 && (
              <Chip
                label={`${dailyRate.toLocaleString('id-ID')} IDR/day`}
                color="primary"
                size="small"
                sx={{ mt: 1 }}
              />
            )}
          </Grid>

          {/* Weekly Rate - Optional */}
          <Grid item xs={12} sm={6}>
            <TextField
              type="number"
              value={weeklyRate || ''}
              onChange={handleWeeklyRateChange}
              label="Weekly Rate (IDR) - Optional"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                startAdornment: <InputAdornment position="start">Rp</InputAdornment>,
                inputProps: { min: 0, step: 1000 }
              }}
              helperText="Optional - Price for 7 days"
            />
            {weeklyRate && weeklyRate > 0 && (
              <Box sx={{ mt: 1 }}>
                <Chip
                  label={`${weeklyRate.toLocaleString('id-ID')} IDR/week`}
                  color="secondary"
                  size="small"
                  sx={{ mr: 1 }}
                />
                {weeklyDiscount > 0 && (
                  <Chip
                    label={`${weeklyDiscount.toFixed(0)}% off`}
                    color="success"
                    size="small"
                    icon={<DiscountIcon />}
                  />
                )}
              </Box>
            )}
          </Grid>

          {/* Monthly Rate - Optional */}
          <Grid item xs={12} sm={6}>
            <TextField
              type="number"
              value={monthlyRate || ''}
              onChange={handleMonthlyRateChange}
              label="Monthly Rate (IDR) - Optional"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                startAdornment: <InputAdornment position="start">Rp</InputAdornment>,
                inputProps: { min: 0, step: 1000 }
              }}
              helperText="Optional - Price for 30 days"
            />
            {monthlyRate && monthlyRate > 0 && (
              <Box sx={{ mt: 1 }}>
                <Chip
                  label={`${monthlyRate.toLocaleString('id-ID')} IDR/month`}
                  color="secondary"
                  size="small"
                  sx={{ mr: 1 }}
                />
                {monthlyDiscount > 0 && (
                  <Chip
                    label={`${monthlyDiscount.toFixed(0)}% off`}
                    color="success"
                    size="small"
                    icon={<DiscountIcon />}
                  />
                )}
              </Box>
            )}
          </Grid>

          {/* Yearly Rate - Optional */}
          <Grid item xs={12} sm={6}>
            <TextField
              type="number"
              value={yearlyRate || ''}
              onChange={handleYearlyRateChange}
              label="Yearly Rate (IDR) - Optional"
              variant="outlined"
              fullWidth
              disabled={disabled}
              InputProps={{
                startAdornment: <InputAdornment position="start">Rp</InputAdornment>,
                inputProps: { min: 0, step: 1000 }
              }}
              helperText="Optional - Price for 365 days"
            />
            {yearlyRate && yearlyRate > 0 && (
              <Box sx={{ mt: 1 }}>
                <Chip
                  label={`${yearlyRate.toLocaleString('id-ID')} IDR/year`}
                  color="secondary"
                  size="small"
                  sx={{ mr: 1 }}
                />
                {yearlyDiscount > 0 && (
                  <Chip
                    label={`${yearlyDiscount.toFixed(0)}% off`}
                    color="success"
                    size="small"
                    icon={<DiscountIcon />}
                  />
                )}
              </Box>
            )}
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        {/* Pricing Summary */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Pricing Summary
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper elevation={0} sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
                <Typography variant="h6" align="center">
                  Daily
                </Typography>
                <Typography variant="h5" align="center" fontWeight="bold">
                  Rp {dailyRate.toLocaleString('id-ID')}
                </Typography>
                <Typography variant="caption" align="center" display="block">
                  per day
                </Typography>
              </Paper>
            </Grid>
            
            {weeklyRate && weeklyRate > 0 && (
              <Grid item xs={12} sm={6} md={3}>
                <Paper elevation={0} sx={{ p: 2, bgcolor: 'secondary.light', color: 'secondary.contrastText' }}>
                  <Typography variant="h6" align="center">
                    Weekly
                  </Typography>
                  <Typography variant="h5" align="center" fontWeight="bold">
                    Rp {weeklyRate.toLocaleString('id-ID')}
                  </Typography>
                  <Typography variant="caption" align="center" display="block">
                    per week
                  </Typography>
                  {weeklyDiscount > 0 && (
                    <Chip
                      label={`${weeklyDiscount.toFixed(0)}% off`}
                      size="small"
                      sx={{ mt: 1, width: '100%' }}
                    />
                  )}
                </Paper>
              </Grid>
            )}
            
            {monthlyRate && monthlyRate > 0 && (
              <Grid item xs={12} sm={6} md={3}>
                <Paper elevation={0} sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
                  <Typography variant="h6" align="center">
                    Monthly
                  </Typography>
                  <Typography variant="h5" align="center" fontWeight="bold">
                    Rp {monthlyRate.toLocaleString('id-ID')}
                  </Typography>
                  <Typography variant="caption" align="center" display="block">
                    per month
                  </Typography>
                  {monthlyDiscount > 0 && (
                    <Chip
                      label={`${monthlyDiscount.toFixed(0)}% off`}
                      size="small"
                      sx={{ mt: 1, width: '100%' }}
                    />
                  )}
                </Paper>
              </Grid>
            )}
            
            {yearlyRate && yearlyRate > 0 && (
              <Grid item xs={12} sm={6} md={3}>
                <Paper elevation={0} sx={{ p: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
                  <Typography variant="h6" align="center">
                    Yearly
                  </Typography>
                  <Typography variant="h5" align="center" fontWeight="bold">
                    Rp {yearlyRate.toLocaleString('id-ID')}
                  </Typography>
                  <Typography variant="caption" align="center" display="block">
                    per year
                  </Typography>
                  {yearlyDiscount > 0 && (
                    <Chip
                      label={`${yearlyDiscount.toFixed(0)}% off`}
                      size="small"
                      sx={{ mt: 1, width: '100%' }}
                    />
                  )}
                </Paper>
              </Grid>
            )}
          </Grid>
        </Box>

        {/* Validation Messages */}
        {dailyRate <= 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Daily rate is required and must be greater than 0
          </Alert>
        )}

        {weeklyRate && weeklyRate > 0 && weeklyRate >= dailyRate * 7 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Weekly rate should be less than 7 times the daily rate to offer a discount
          </Alert>
        )}

        {monthlyRate && monthlyRate > 0 && monthlyRate >= dailyRate * 30 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Monthly rate should be less than 30 times the daily rate to offer a discount
          </Alert>
        )}

        {yearlyRate && yearlyRate > 0 && yearlyRate >= dailyRate * 365 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            Yearly rate should be less than 365 times the daily rate to offer a discount
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default PricingSection; 