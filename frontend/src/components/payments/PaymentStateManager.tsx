import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Button,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Paper,

} from '@mui/material';
// Timeline components temporarily removed - need @mui/lab package
// import {
//   Timeline,
//   TimelineItem,
//   TimelineSeparator,
//   TimelineConnector,
//   TimelineContent,
//   TimelineDot,
//   TimelineOppositeContent,
// } from '@mui/lab';
import {
  Payment,
  Schedule,
  CheckCircle,
  Warning,
  HourglassEmpty,
  AccountBalance,
  DirectionsBike,
  Person,
  AttachMoney,
  Security,
  Verified,
} from '@mui/icons-material';
import { ProviderService } from '../../services/ProviderService';

interface PaymentState {
  id: string;
  bookingId: string;
  customerId: string;
  customerName: string;
  vehicleName: string;
  totalAmount: number;
  providerAmount: number;
  platformFee: number;
  status: 'pending_pickup' | 'pickup_confirmed' | 'in_progress' | 'pending_return' | 'return_confirmed' | 'payment_released' | 'completed';
  paymentMethod: 'stripe' | 'bank_transfer' | 'cash';
  pickupConfirmedAt?: string;
  returnConfirmedAt?: string;
  paymentReleasedAt?: string;
  holdPeriodEnd?: string;
  timeline: PaymentTimelineEvent[];
}

interface PaymentTimelineEvent {
  id: string;
  type: 'booking_created' | 'payment_received' | 'pickup_confirmed' | 'return_confirmed' | 'payment_released' | 'dispute_raised';
  title: string;
  description: string;
  timestamp: string;
  status: 'completed' | 'pending' | 'failed';
  actor: 'customer' | 'provider' | 'system' | 'admin';
}

const PaymentStateManager: React.FC = () => {
  const [paymentStates, setPaymentStates] = useState<PaymentState[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<PaymentState | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  useEffect(() => {
    fetchPaymentStates();
  }, []);

  const fetchPaymentStates = async () => {
    try {
      setLoading(true);
      const response = await ProviderService.getPaymentStates();
      
      if (response.success) {
        setPaymentStates(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching payment states:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_pickup': return 'warning';
      case 'pickup_confirmed': return 'info';
      case 'in_progress': return 'primary';
      case 'pending_return': return 'secondary';
      case 'return_confirmed': return 'success';
      case 'payment_released': return 'success';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending_pickup': return <HourglassEmpty />;
      case 'pickup_confirmed': return <DirectionsBike />;
      case 'in_progress': return <Schedule />;
      case 'pending_return': return <Warning />;
      case 'return_confirmed': return <CheckCircle />;
      case 'payment_released': return <AttachMoney />;
      case 'completed': return <Verified />;
      default: return <Schedule />;
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'pending_pickup': return 'Waiting for customer to confirm vehicle pickup';
      case 'pickup_confirmed': return 'Customer confirmed pickup - rental in progress';
      case 'in_progress': return 'Vehicle is currently rented out';
      case 'pending_return': return 'Waiting for customer to return vehicle';
      case 'return_confirmed': return 'Vehicle returned - payment processing';
      case 'payment_released': return 'Payment released to provider';
      case 'completed': return 'Transaction completed successfully';
      default: return 'Processing...';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const calculateHoldPeriod = (returnConfirmedAt: string) => {
    const returnDate = new Date(returnConfirmedAt);
    const holdEndDate = new Date(returnDate.getTime() + (3 * 24 * 60 * 60 * 1000)); // 3 days hold
    return holdEndDate;
  };

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case 'booking_created': return <Schedule />;
      case 'payment_received': return <Payment />;
      case 'pickup_confirmed': return <DirectionsBike />;
      case 'return_confirmed': return <CheckCircle />;
      case 'payment_released': return <AttachMoney />;
      case 'dispute_raised': return <Warning />;
      default: return <Schedule />;
    }
  };

  if (loading) {
    return (
      <Box>
        <LinearProgress />
        <Box p={3}>
          <Typography>Loading payment states...</Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <Payment sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Payment State Management
        </Typography>
      </Box>

      {/* Payment Flow Explanation */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Payment Protection:</strong> Payments are held securely until customers confirm vehicle pickup and return. 
          This protects both providers and customers from disputes.
        </Typography>
      </Alert>

      {/* Payment States Grid */}
      {paymentStates.length === 0 ? (
        <Alert severity="info">
          No payment states to display. Payment tracking will appear here once you have active bookings.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {paymentStates.map((payment) => (
            <Grid item xs={12} md={6} lg={4} key={payment.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                    <Box flex={1}>
                      <Typography variant="h6" gutterBottom>
                        {payment.vehicleName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Customer: {payment.customerName}
                      </Typography>
                    </Box>
                    <Box textAlign="right">
                      <Typography variant="h6" color="primary">
                        {formatCurrency(payment.providerAmount)}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        of {formatCurrency(payment.totalAmount)}
                      </Typography>
                    </Box>
                  </Box>

                  <Box mb={2}>
                    <Chip
                      icon={getStatusIcon(payment.status)}
                      label={payment.status.replace('_', ' ').toUpperCase()}
                      color={getStatusColor(payment.status) as any}
                      size="small"
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="body2" color="textSecondary">
                      {getStatusDescription(payment.status)}
                    </Typography>
                  </Box>

                  {/* Payment Method */}
                  <Box display="flex" alignItems="center" mb={2}>
                    <Payment sx={{ mr: 1, fontSize: 16 }} />
                    <Typography variant="body2">
                      {payment.paymentMethod.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </Box>

                  {/* Hold Period Info */}
                  {payment.returnConfirmedAt && payment.status !== 'payment_released' && (
                    <Alert severity="warning" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>Hold Period:</strong> Payment will be released on{' '}
                        {calculateHoldPeriod(payment.returnConfirmedAt).toLocaleDateString()}
                      </Typography>
                    </Alert>
                  )}

                  {/* Action Button */}
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => {
                      setSelectedPayment(payment);
                      setDetailsDialogOpen(true);
                    }}
                  >
                    View Details
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Payment Details Dialog */}
      <Dialog 
        open={detailsDialogOpen} 
        onClose={() => setDetailsDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Payment Details - {selectedPayment?.vehicleName}
        </DialogTitle>
        <DialogContent>
          {selectedPayment && (
            <Box>
              {/* Payment Summary */}
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Payment Summary
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Total Amount
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(selectedPayment.totalAmount)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Your Earnings
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {formatCurrency(selectedPayment.providerAmount)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Platform Fee
                    </Typography>
                    <Typography variant="body1">
                      {formatCurrency(selectedPayment.platformFee)}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Payment Method
                    </Typography>
                    <Typography variant="body1">
                      {selectedPayment.paymentMethod.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* Payment Timeline */}
              <Typography variant="h6" gutterBottom>
                Payment Timeline
              </Typography>
              <List>
                {selectedPayment.timeline.map((event, index) => (
                  <ListItem key={event.id}>
                    <ListItemIcon>
                      {getTimelineIcon(event.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={event.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="textSecondary">
                            {event.description}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {new Date(event.timestamp).toLocaleString()}
                          </Typography>
                          <Chip
                            label={event.actor.toUpperCase()}
                            size="small"
                            variant="outlined"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>

              {/* Security Information */}
              <Alert severity="success" sx={{ mt: 3 }}>
                <Typography variant="body2">
                  <Security sx={{ mr: 1, verticalAlign: 'middle' }} />
                  <strong>Secure Payment Processing:</strong> All payments are processed securely through our payment partners. 
                  Funds are held in escrow until pickup and return confirmations are completed.
                </Typography>
              </Alert>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentStateManager;
