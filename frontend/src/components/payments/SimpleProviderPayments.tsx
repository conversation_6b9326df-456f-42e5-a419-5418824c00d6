import React from 'react';
import {
  Box,
  Typography,
  Alert,
} from '@mui/material';
import {
  AttachMoney,
} from '@mui/icons-material';
import PaymentStateManager from './PaymentStateManager';

const SimpleProviderPayments: React.FC = () => {
  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <AttachMoney sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Payment Management
        </Typography>
      </Box>

      {/* Payment State Manager */}
      <PaymentStateManager />

      {/* Additional Info */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Payment Protection:</strong> All payments are processed securely and held until pickup/return confirmations are completed.
          This protects both providers and customers from disputes.
        </Typography>
      </Alert>
    </Box>
  );
};

export default SimpleProviderPayments;
