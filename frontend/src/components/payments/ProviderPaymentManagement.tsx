import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  LinearProgress,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Box as MuiBox,
} from '@mui/material';
import {
  AccountBalance,
  CreditCard,
  AttachMoney,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Verified,
  Warning,
  Info,
  Receipt,
  TrendingUp,
  Schedule,
  CheckCircle,
} from '@mui/icons-material';
import { ProviderService } from '../../services/ProviderService';
import PaymentStateManager from './PaymentStateManager';

interface BankAccount {
  id: string;
  accountHolder: string;
  bankName: string;
  accountNumber: string;
  routingNumber: string;
  iban?: string;
  swiftCode?: string;
  isDefault: boolean;
  isVerified: boolean;
  country: string;
}

interface PaymentSettings {
  acceptStripe: boolean;
  acceptBankTransfer: boolean;
  acceptCash: boolean;
  minimumPayout: number;
  payoutSchedule: 'daily' | 'weekly' | 'monthly';
  autoPayoutEnabled: boolean;
}

interface EarningsData {
  pendingEarnings: number;
  availableEarnings: number;
  totalEarnings: number;
  lastPayout: {
    amount: number;
    date: string;
    method: string;
  } | null;
  upcomingPayout: {
    amount: number;
    date: string;
  } | null;
}

const ProviderPaymentManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    acceptStripe: true,
    acceptBankTransfer: true,
    acceptCash: false,
    minimumPayout: 50,
    payoutSchedule: 'weekly',
    autoPayoutEnabled: true,
  });
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [addBankDialogOpen, setAddBankDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const [newBankAccount, setNewBankAccount] = useState({
    accountHolder: '',
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    iban: '',
    swiftCode: '',
    country: 'US',
  });

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      
      const [accountsResponse, settingsResponse, earningsResponse] = await Promise.all([
        ProviderService.getBankAccounts(),
        ProviderService.getPaymentSettings(),
        ProviderService.getEarningsData(),
      ]);

      if (accountsResponse.success) {
        setBankAccounts(accountsResponse.data || []);
      }

      if (settingsResponse.success) {
        setPaymentSettings(settingsResponse.data || paymentSettings);
      }

      if (earningsResponse.success) {
        setEarningsData(earningsResponse.data);
      }
    } catch (error) {
      console.error('Error fetching payment data:', error);
      setSnackbar({ open: true, message: 'Failed to load payment data', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleAddBankAccount = async () => {
    try {
      setSaving(true);
      
      const response = await ProviderService.addBankAccount(newBankAccount);
      
      if (response.success) {
        setBankAccounts([...bankAccounts, response.data]);
        setAddBankDialogOpen(false);
        setNewBankAccount({
          accountHolder: '',
          bankName: '',
          accountNumber: '',
          routingNumber: '',
          iban: '',
          swiftCode: '',
          country: 'US',
        });
        setSnackbar({ open: true, message: 'Bank account added successfully', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to add bank account', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to add bank account', severity: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteBankAccount = async (accountId: string) => {
    try {
      const response = await ProviderService.deleteBankAccount(accountId);
      
      if (response.success) {
        setBankAccounts(bankAccounts.filter(account => account.id !== accountId));
        setSnackbar({ open: true, message: 'Bank account deleted successfully', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to delete bank account', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to delete bank account', severity: 'error' });
    }
  };

  const handleSetDefaultAccount = async (accountId: string) => {
    try {
      const response = await ProviderService.setDefaultBankAccount(accountId);
      
      if (response.success) {
        setBankAccounts(bankAccounts.map(account => ({
          ...account,
          isDefault: account.id === accountId
        })));
        setSnackbar({ open: true, message: 'Default account updated', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to update default account', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to update default account', severity: 'error' });
    }
  };

  const handleSavePaymentSettings = async () => {
    try {
      setSaving(true);
      
      const response = await ProviderService.updatePaymentSettings(paymentSettings);
      
      if (response.success) {
        setSnackbar({ open: true, message: 'Payment settings updated successfully', severity: 'success' });
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to update settings', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to update settings', severity: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const handleRequestPayout = async () => {
    try {
      setSaving(true);
      
      const response = await ProviderService.requestPayout();
      
      if (response.success) {
        setSnackbar({ open: true, message: 'Payout requested successfully', severity: 'success' });
        fetchPaymentData(); // Refresh data
      } else {
        setSnackbar({ open: true, message: response.error || 'Failed to request payout', severity: 'error' });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to request payout', severity: 'error' });
    } finally {
      setSaving(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading) {
    return (
      <Box>
        <LinearProgress />
        <Box p={3}>
          <Typography>Loading payment information...</Typography>
        </Box>
      </Box>
    );
  }

  const TabPanel = ({ children, value, index }: { children: React.ReactNode; value: number; index: number }) => (
    <div hidden={value !== index}>
      {value === index && <MuiBox sx={{ pt: 3 }}>{children}</MuiBox>}
    </div>
  );

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <AttachMoney sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Payment Management
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Payment Tracking" />
          <Tab label="Earnings & Payouts" />
          <Tab label="Bank Accounts" />
          <Tab label="Settings" />
        </Tabs>
      </Box>

      {/* Payment Tracking Tab */}
      <TabPanel value={activeTab} index={0}>
        <PaymentStateManager />
      </TabPanel>

      {/* Earnings & Payouts Tab */}
      <TabPanel value={activeTab} index={1}>
        {/* Earnings Overview */}
        {earningsData && (
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Available Earnings
                    </Typography>
                    <Typography variant="h4" component="div" color="success.main">
                      {formatCurrency(earningsData.availableEarnings)}
                    </Typography>
                  </Box>
                  <CheckCircle color="success" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Pending Earnings
                    </Typography>
                    <Typography variant="h4" component="div" color="warning.main">
                      {formatCurrency(earningsData.pendingEarnings)}
                    </Typography>
                  </Box>
                  <Schedule color="warning" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      Total Earnings
                    </Typography>
                    <Typography variant="h4" component="div">
                      {formatCurrency(earningsData.totalEarnings)}
                    </Typography>
                  </Box>
                  <TrendingUp color="primary" />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box textAlign="center">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleRequestPayout}
                    disabled={earningsData.availableEarnings < paymentSettings.minimumPayout || saving}
                    startIcon={<Receipt />}
                    fullWidth
                  >
                    Request Payout
                  </Button>
                  <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                    Min: {formatCurrency(paymentSettings.minimumPayout)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        )}
      </TabPanel>

      {/* Bank Accounts Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          {/* Bank Accounts */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" display="flex" alignItems="center">
                  <AccountBalance sx={{ mr: 1 }} />
                  Bank Accounts
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={() => setAddBankDialogOpen(true)}
                >
                  Add Account
                </Button>
              </Box>

              {bankAccounts.length === 0 ? (
                <Alert severity="info">
                  No bank accounts added. Add a bank account to receive payouts.
                </Alert>
              ) : (
                <List>
                  {bankAccounts.map((account, index) => (
                    <React.Fragment key={account.id}>
                      <ListItem>
                        <ListItemIcon>
                          <AccountBalance />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="subtitle1">
                                {account.bankName}
                              </Typography>
                              {account.isDefault && (
                                <Chip label="Default" size="small" color="primary" />
                              )}
                              {account.isVerified ? (
                                <Verified color="success" fontSize="small" />
                              ) : (
                                <Warning color="warning" fontSize="small" />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2">
                                {account.accountHolder}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                ****{account.accountNumber.slice(-4)}
                              </Typography>
                            </Box>
                          }
                        />
                        <Box>
                          {!account.isDefault && (
                            <Tooltip title="Set as default">
                              <IconButton
                                size="small"
                                onClick={() => handleSetDefaultAccount(account.id)}
                              >
                                <CheckCircle />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="Delete account">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteBankAccount(account.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </ListItem>
                      {index < bankAccounts.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
        </Grid>
      </TabPanel>

      {/* Settings Tab */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          {/* Payment Settings */}
          <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" display="flex" alignItems="center" mb={2}>
                <CreditCard sx={{ mr: 1 }} />
                Payment Settings
              </Typography>

              <Box mb={3}>
                <Typography variant="subtitle2" gutterBottom>
                  Accepted Payment Methods
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.acceptStripe}
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        acceptStripe: e.target.checked
                      })}
                    />
                  }
                  label="Stripe (Credit/Debit Cards)"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.acceptBankTransfer}
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        acceptBankTransfer: e.target.checked
                      })}
                    />
                  }
                  label="Bank Transfer"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.acceptCash}
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        acceptCash: e.target.checked
                      })}
                    />
                  }
                  label="Cash on Pickup"
                />
              </Box>

              <Grid container spacing={2} mb={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Minimum Payout"
                    type="number"
                    value={paymentSettings.minimumPayout}
                    onChange={(e) => setPaymentSettings({
                      ...paymentSettings,
                      minimumPayout: parseFloat(e.target.value) || 0
                    })}
                    InputProps={{
                      startAdornment: '$',
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Payout Schedule</InputLabel>
                    <Select
                      value={paymentSettings.payoutSchedule}
                      label="Payout Schedule"
                      onChange={(e) => setPaymentSettings({
                        ...paymentSettings,
                        payoutSchedule: e.target.value as 'daily' | 'weekly' | 'monthly'
                      })}
                    >
                      <MenuItem value="daily">Daily</MenuItem>
                      <MenuItem value="weekly">Weekly</MenuItem>
                      <MenuItem value="monthly">Monthly</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <FormControlLabel
                control={
                  <Switch
                    checked={paymentSettings.autoPayoutEnabled}
                    onChange={(e) => setPaymentSettings({
                      ...paymentSettings,
                      autoPayoutEnabled: e.target.checked
                    })}
                  />
                }
                label="Enable automatic payouts"
              />

              <Box mt={3}>
                <Button
                  variant="contained"
                  onClick={handleSavePaymentSettings}
                  disabled={saving}
                  fullWidth
                >
                  Save Payment Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        </Grid>
      </TabPanel>

      {/* Add Bank Account Dialog */}
      <Dialog open={addBankDialogOpen} onClose={() => setAddBankDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Bank Account</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Account Holder Name"
                value={newBankAccount.accountHolder}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  accountHolder: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Bank Name"
                value={newBankAccount.bankName}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  bankName: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Account Number"
                value={newBankAccount.accountNumber}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  accountNumber: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Routing Number"
                value={newBankAccount.routingNumber}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  routingNumber: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="IBAN (Optional)"
                value={newBankAccount.iban}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  iban: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="SWIFT Code (Optional)"
                value={newBankAccount.swiftCode}
                onChange={(e) => setNewBankAccount({
                  ...newBankAccount,
                  swiftCode: e.target.value
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Country</InputLabel>
                <Select
                  value={newBankAccount.country}
                  label="Country"
                  onChange={(e) => setNewBankAccount({
                    ...newBankAccount,
                    country: e.target.value
                  })}
                >
                  <MenuItem value="US">United States</MenuItem>
                  <MenuItem value="CA">Canada</MenuItem>
                  <MenuItem value="GB">United Kingdom</MenuItem>
                  <MenuItem value="AU">Australia</MenuItem>
                  <MenuItem value="DE">Germany</MenuItem>
                  <MenuItem value="FR">France</MenuItem>
                  <MenuItem value="IN">India</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddBankDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddBankAccount} variant="contained" disabled={saving}>
            Add Account
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ProviderPaymentManagement;
