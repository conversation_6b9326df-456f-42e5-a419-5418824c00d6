import React from 'react';
import { SupportTicket } from '../../types/support';

interface SupportTicketListProps {
  tickets: SupportTicket[];
}

const SupportTicketList: React.FC<SupportTicketListProps> = ({ tickets }) => {
  if (tickets.length === 0) {
    return <div>No support tickets found.</div>;
  }

  return (
    <div className="support-ticket-list">
      <h2>Your Tickets</h2>
      {tickets.map(ticket => (
        <div key={ticket.id} className="support-ticket-item">
          <div>
            <strong>Category:</strong> {ticket.category}
          </div>
          <div>
            <strong>Status:</strong> {ticket.status}
          </div>
          <div>
            <strong>Message:</strong> {ticket.message}
          </div>
          {ticket.adminResponse && (
            <div>
              <strong>Admin Response:</strong> {ticket.adminResponse}
            </div>
          )}
          <div className="ticket-timestamp">
            Created: {new Date(ticket.createdAt).toLocaleString()}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SupportTicketList;
