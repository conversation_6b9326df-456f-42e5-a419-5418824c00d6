import React, { useState } from 'react';
import { SupportTicket } from '../../types/support';

interface SupportTicketFormProps {
  onSubmit: (ticketData: Omit<SupportTicket, 'id' | 'createdAt'>) => void;
}

const SupportTicketForm: React.FC<SupportTicketFormProps> = ({ onSubmit }) => {
  const [category, setCategory] = useState('');
  const [message, setMessage] = useState('');
  const [bookingId, setBookingId] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (message.trim().length < 10) {
      alert('Message must be at least 10 characters long');
      return;
    }

    onSubmit({
      category: category as 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'OTHER',
      message,
      bookingId: bookingId || undefined,
      status: 'OPEN'
    });

    // Reset form
    setCategory('');
    setMessage('');
    setBookingId('');
  };

  return (
    <form onSubmit={handleSubmit} className="support-ticket-form">
      <div>
        <label>Category</label>
        <select 
          value={category} 
          onChange={(e) => setCategory(e.target.value)}
          required
        >
          <option value="">Select Category</option>
          <option value="PAYMENT">Payment</option>
          <option value="VEHICLE">Vehicle</option>
          <option value="DAMAGE">Damage</option>
          <option value="BOOKING">Booking</option>
          <option value="OTHER">Other</option>
        </select>
      </div>

      <div>
        <label>Booking ID (Optional)</label>
        <input 
          type="text" 
          value={bookingId}
          onChange={(e) => setBookingId(e.target.value)}
          placeholder="Enter Booking ID if applicable"
        />
      </div>

      <div>
        <label>Message</label>
        <textarea 
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Describe your issue in detail"
          required
          minLength={10}
        />
      </div>

      <button type="submit">Submit Support Ticket</button>
    </form>
  );
};

export default SupportTicketForm;
