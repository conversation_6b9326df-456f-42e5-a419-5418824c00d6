import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Divide<PERSON>,
  But<PERSON>,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  Tooltip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  Build as BuildIcon,
  CheckCircle as ResolvedIcon,
  Update as UpdateIcon,
  Clear as ClearIcon,
  MarkEmailRead as MarkReadIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Mock hook for now - will be replaced with actual implementation
const useRealTimeNotifications = () => ({
  notifications: [],
  unreadCount: 0,
  isConnected: true,
  connectionStatus: 'connected',
  markAsRead: () => {},
  markAllAsRead: () => {},
  clearNotifications: () => {}
});

interface NotificationCenterProps {
  maxHeight?: number;
  showConnectionStatus?: boolean;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  maxHeight = 400,
  showConnectionStatus = true
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const {
    notifications,
    unreadCount,
    isConnected,
    connectionStatus,
    markAsRead,
    markAllAsRead,
    clearNotifications
  } = useRealTimeNotifications();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'success';
      case 'connecting':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return t('notifications:status.connected', 'Connected');
      case 'connecting':
        return t('notifications:status.connecting', 'Connecting...');
      case 'error':
        return t('notifications:status.error', 'Connection Error');
      default:
        return t('notifications:status.disconnected', 'Disconnected');
    }
  };

  return (
    <>
      <Tooltip title={t('notifications:title', 'Notifications')}>
        <IconButton
          color="inherit"
          onClick={handleClick}
          sx={{
            color: unreadCount > 0 ? theme.palette.warning.main : 'inherit'
          }}
        >
          <Badge badgeContent={unreadCount} color="error" max={99}>
            {unreadCount > 0 ? <NotificationsActiveIcon /> : <NotificationsIcon />}
          </Badge>
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: isMobile ? '90vw' : 400,
            maxWidth: 400,
            maxHeight: maxHeight + 100,
            mt: 1
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Header */}
        <Box px={2} py={1}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {t('notifications:title', 'Notifications')}
            </Typography>
            {showConnectionStatus && (
              <Chip
                label={getConnectionStatusText()}
                color={getConnectionStatusColor() as any}
                size="small"
                variant="outlined"
              />
            )}
          </Box>

          {unreadCount > 0 && (
            <Box display="flex" gap={1} mt={1}>
              <Button
                size="small"
                startIcon={<MarkReadIcon />}
                onClick={markAllAsRead}
              >
                {t('notifications:markAllRead', 'Mark All Read')}
              </Button>
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={clearNotifications}
                color="error"
              >
                {t('notifications:clear', 'Clear')}
              </Button>
            </Box>
          )}
        </Box>

        <Divider />

        {/* Connection Status Alert */}
        {!isConnected && (
          <Alert severity="warning" sx={{ m: 1 }}>
            <Typography variant="body2">
              {t('notifications:connectionIssue', 'Real-time notifications may be delayed')}
            </Typography>
          </Alert>
        )}

        {/* Notifications List */}
        <Box sx={{ maxHeight: maxHeight, overflow: 'auto' }}>
          {notifications.length === 0 ? (
            <Box p={3} textAlign="center">
              <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                {t('notifications:empty', 'No notifications yet')}
              </Typography>
            </Box>
          ) : (
            <List disablePadding>
              {/* Notifications will be rendered here when real-time hook is implemented */}
            </List>
          )}
        </Box>
      </Menu>
    </>
  );
};

export default NotificationCenter;