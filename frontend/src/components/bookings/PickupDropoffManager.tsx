import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  DirectionsBike,
  LocationOn,
  Schedule,
  CheckCircle,
  Warning,
  PhotoCamera,
  Person,
  Phone,
  Message,
  Star,
  Upload,
  Download,
  Visibility,
} from '@mui/icons-material';
import { ProviderService } from '../../services/ProviderService';

interface BookingDetails {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  vehicleName: string;
  vehicleId: string;
  startDate: string;
  endDate: string;
  pickupLocation: string;
  dropoffLocation: string;
  status: 'pending' | 'confirmed' | 'picked_up' | 'returned' | 'completed';
  totalAmount: number;
  paymentMethod: string;
  specialInstructions?: string;
}

interface PickupDropoffEvent {
  id: string;
  bookingId: string;
  type: 'pickup' | 'dropoff';
  status: 'pending' | 'in_progress' | 'completed';
  scheduledTime: string;
  actualTime?: string;
  location: string;
  photos: string[];
  notes: string;
  vehicleCondition: {
    fuelLevel: number;
    mileage: number;
    damages: string[];
    cleanliness: number;
  };
  customerSignature?: string;
  providerSignature?: string;
}

const PickupDropoffManager: React.FC = () => {
  const [bookings, setBookings] = useState<BookingDetails[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<BookingDetails | null>(null);
  const [pickupDropoffEvents, setPickupDropoffEvents] = useState<PickupDropoffEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [eventType, setEventType] = useState<'pickup' | 'dropoff'>('pickup');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const [eventData, setEventData] = useState({
    fuelLevel: 100,
    mileage: 0,
    damages: [] as string[],
    cleanliness: 5,
    notes: '',
    photos: [] as File[],
  });

  useEffect(() => {
    fetchBookingsData();
  }, []);

  const fetchBookingsData = async () => {
    try {
      setLoading(true);
      
      const [bookingsResponse, eventsResponse] = await Promise.all([
        ProviderService.getActiveBookings(),
        ProviderService.getPickupDropoffEvents(),
      ]);

      if (bookingsResponse.success) {
        setBookings(bookingsResponse.data || []);
      }

      if (eventsResponse.success) {
        setPickupDropoffEvents(eventsResponse.data || []);
      }
    } catch (error) {
      console.error('Error fetching bookings data:', error);
      setSnackbar({ open: true, message: 'Failed to load bookings data', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmPickupDropoff = async () => {
    if (!selectedBooking) return;

    try {
      setLoading(true);

      const eventPayload = {
        bookingId: selectedBooking.id,
        type: eventType,
        location: eventType === 'pickup' ? selectedBooking.pickupLocation : selectedBooking.dropoffLocation,
        vehicleCondition: {
          fuelLevel: eventData.fuelLevel,
          mileage: eventData.mileage,
          damages: eventData.damages,
          cleanliness: eventData.cleanliness,
        },
        notes: eventData.notes,
        photos: eventData.photos,
      };

      const response = await ProviderService.confirmPickupDropoff(eventPayload);

      if (response.success) {
        setConfirmationDialogOpen(false);
        setSelectedBooking(null);
        setEventData({
          fuelLevel: 100,
          mileage: 0,
          damages: [],
          cleanliness: 5,
          notes: '',
          photos: [],
        });
        
        fetchBookingsData(); // Refresh data
        setSnackbar({ 
          open: true, 
          message: `${eventType === 'pickup' ? 'Pickup' : 'Dropoff'} confirmed successfully`, 
          severity: 'success' 
        });
      } else {
        setSnackbar({ 
          open: true, 
          message: response.error || 'Failed to confirm event', 
          severity: 'error' 
        });
      }
    } catch (error) {
      setSnackbar({ open: true, message: 'Failed to confirm event', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setEventData({
      ...eventData,
      photos: [...eventData.photos, ...files].slice(0, 5), // Max 5 photos
    });
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'confirmed': return 'info';
      case 'picked_up': return 'primary';
      case 'returned': return 'secondary';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  const getNextAction = (booking: BookingDetails) => {
    switch (booking.status) {
      case 'confirmed':
        return { action: 'Confirm Pickup', type: 'pickup' as const };
      case 'picked_up':
        return { action: 'Confirm Return', type: 'dropoff' as const };
      default:
        return null;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading && bookings.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading bookings...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" mb={3}>
        <DirectionsBike sx={{ mr: 2, fontSize: 30 }} />
        <Typography variant="h5" fontWeight="bold">
          Pickup & Dropoff Management
        </Typography>
      </Box>

      {bookings.length === 0 ? (
        <Alert severity="info">
          No active bookings requiring pickup or dropoff confirmation.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {bookings.map((booking) => {
            const nextAction = getNextAction(booking);
            const relatedEvents = pickupDropoffEvents.filter(event => event.bookingId === booking.id);

            return (
              <Grid item xs={12} md={6} key={booking.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="between" alignItems="flex-start" mb={2}>
                      <Box flex={1}>
                        <Typography variant="h6" gutterBottom>
                          {booking.vehicleName}
                        </Typography>
                        <Chip
                          label={booking.status.replace('_', ' ').toUpperCase()}
                          color={getBookingStatusColor(booking.status) as any}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                      </Box>
                      <Typography variant="h6" color="primary">
                        {formatCurrency(booking.totalAmount)}
                      </Typography>
                    </Box>

                    {/* Customer Info */}
                    <Box display="flex" alignItems="center" mb={2}>
                      <Person sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {booking.customerName}
                      </Typography>
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <Phone fontSize="small" />
                      </IconButton>
                      <IconButton size="small">
                        <Message fontSize="small" />
                      </IconButton>
                    </Box>

                    {/* Booking Details */}
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <Schedule />
                        </ListItemIcon>
                        <ListItemText
                          primary="Rental Period"
                          secondary={`${new Date(booking.startDate).toLocaleDateString()} - ${new Date(booking.endDate).toLocaleDateString()}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <LocationOn />
                        </ListItemIcon>
                        <ListItemText
                          primary="Pickup Location"
                          secondary={booking.pickupLocation}
                        />
                      </ListItem>
                      {booking.dropoffLocation !== booking.pickupLocation && (
                        <ListItem>
                          <ListItemIcon>
                            <LocationOn />
                          </ListItemIcon>
                          <ListItemText
                            primary="Dropoff Location"
                            secondary={booking.dropoffLocation}
                          />
                        </ListItem>
                      )}
                    </List>

                    {/* Event History */}
                    {relatedEvents.length > 0 && (
                      <Box mt={2}>
                        <Typography variant="subtitle2" gutterBottom>
                          Event History
                        </Typography>
                        <Stepper orientation="vertical" sx={{ pl: 2 }}>
                          {relatedEvents.map((event, index) => (
                            <Step key={event.id} active={true} completed={event.status === 'completed'}>
                              <StepLabel
                                icon={event.status === 'completed' ? <CheckCircle color="success" /> : <Warning color="warning" />}
                              >
                                <Typography variant="body2">
                                  {event.type === 'pickup' ? 'Vehicle Picked Up' : 'Vehicle Returned'}
                                </Typography>
                              </StepLabel>
                              <StepContent>
                                <Typography variant="caption" color="textSecondary">
                                  {event.actualTime ? new Date(event.actualTime).toLocaleString() : 'Pending'}
                                </Typography>
                                {event.notes && (
                                  <Typography variant="body2" sx={{ mt: 1 }}>
                                    {event.notes}
                                  </Typography>
                                )}
                              </StepContent>
                            </Step>
                          ))}
                        </Stepper>
                      </Box>
                    )}

                    {/* Action Button */}
                    {nextAction && (
                      <Box mt={2}>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={() => {
                            setSelectedBooking(booking);
                            setEventType(nextAction.type);
                            setConfirmationDialogOpen(true);
                          }}
                          startIcon={nextAction.type === 'pickup' ? <DirectionsBike /> : <CheckCircle />}
                        >
                          {nextAction.action}
                        </Button>
                      </Box>
                    )}

                    {/* Special Instructions */}
                    {booking.specialInstructions && (
                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          <strong>Special Instructions:</strong> {booking.specialInstructions}
                        </Typography>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Confirmation Dialog */}
      <Dialog 
        open={confirmationDialogOpen} 
        onClose={() => setConfirmationDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Confirm {eventType === 'pickup' ? 'Vehicle Pickup' : 'Vehicle Return'}
        </DialogTitle>
        <DialogContent>
          {selectedBooking && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Please document the vehicle condition and take photos before confirming the {eventType}.
                </Typography>
              </Alert>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Fuel Level (%)"
                    type="number"
                    value={eventData.fuelLevel}
                    onChange={(e) => setEventData({
                      ...eventData,
                      fuelLevel: parseInt(e.target.value) || 0
                    })}
                    inputProps={{ min: 0, max: 100 }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Mileage"
                    type="number"
                    value={eventData.mileage}
                    onChange={(e) => setEventData({
                      ...eventData,
                      mileage: parseInt(e.target.value) || 0
                    })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Vehicle Cleanliness
                  </Typography>
                  <Rating
                    value={eventData.cleanliness}
                    onChange={(event, newValue) => {
                      setEventData({
                        ...eventData,
                        cleanliness: newValue || 1
                      });
                    }}
                    max={5}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Notes & Observations"
                    value={eventData.notes}
                    onChange={(e) => setEventData({
                      ...eventData,
                      notes: e.target.value
                    })}
                    placeholder="Document any damages, issues, or special observations..."
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Vehicle Photos ({eventData.photos.length}/5)
                    </Typography>
                    <input
                      accept="image/*"
                      style={{ display: 'none' }}
                      id="photo-upload"
                      multiple
                      type="file"
                      onChange={handlePhotoUpload}
                    />
                    <label htmlFor="photo-upload">
                      <Button
                        variant="outlined"
                        component="span"
                        startIcon={<PhotoCamera />}
                        disabled={eventData.photos.length >= 5}
                      >
                        Add Photos
                      </Button>
                    </label>
                    {eventData.photos.length > 0 && (
                      <Box display="flex" gap={1} mt={2} flexWrap="wrap">
                        {eventData.photos.map((photo, index) => (
                          <Paper key={index} sx={{ p: 1, display: 'flex', alignItems: 'center' }}>
                            <Typography variant="caption">
                              {photo.name}
                            </Typography>
                          </Paper>
                        ))}
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmationDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirmPickupDropoff} 
            variant="contained"
            disabled={loading}
          >
            Confirm {eventType === 'pickup' ? 'Pickup' : 'Return'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PickupDropoffManager;
