import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  BookOnline,
  DirectionsBike,
  TrendingUp,
  Star,
  Notifications,
  Share,
  Add as AddIcon,
  Refresh,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface EmptyBookingsCTAProps {
  variant?: 'provider' | 'customer';
  onRefresh?: () => void;
}

const EmptyBookingsCTA: React.FC<EmptyBookingsCTAProps> = ({ 
  variant = 'provider',
  onRefresh 
}) => {
  const navigate = useNavigate();

  const handleAddVehicle = () => {
    navigate('/provider-dashboard?tab=4'); // Vehicles tab
  };

  const handleBrowseVehicles = () => {
    navigate('/');
  };

  const providerContent = {
    title: 'Ready to Start Earning?',
    subtitle: 'Add your first vehicle to receive bookings',
    description: 'Your booking dashboard will show all customer requests, confirmed bookings, and rental history once you list your vehicles and start receiving bookings.',
    primaryAction: {
      label: 'Add Your First Vehicle',
      onClick: handleAddVehicle,
      icon: AddIcon,
    },
    secondaryAction: {
      label: 'Refresh Bookings',
      onClick: onRefresh || (() => window.location.reload()),
      icon: Refresh,
    },
    features: [
      {
        icon: BookOnline,
        title: 'Booking Management',
        description: 'Accept, decline, and manage all booking requests',
        color: '#2196f3',
      },
      {
        icon: Notifications,
        title: 'Real-time Notifications',
        description: 'Get instant alerts for new bookings and updates',
        color: '#ff9800',
      },
      {
        icon: TrendingUp,
        title: 'Earnings Tracking',
        description: 'Monitor your income and booking performance',
        color: '#4caf50',
      },
      {
        icon: Star,
        title: 'Customer Reviews',
        description: 'Build your reputation with customer feedback',
        color: '#9c27b0',
      },
    ],
    tips: [
      'List high-quality photos to attract more bookings',
      'Set competitive pricing for your area',
      'Respond quickly to booking requests',
      'Maintain your vehicles in excellent condition',
      'Provide clear pickup instructions',
    ],
  };

  const customerContent = {
    title: 'No Bookings Yet?',
    subtitle: 'Discover amazing vehicles to rent',
    description: 'Your booking history will appear here once you make your first reservation. Browse our selection of motorcycles and scooters to get started.',
    primaryAction: {
      label: 'Browse Vehicles',
      onClick: handleBrowseVehicles,
      icon: DirectionsBike,
    },
    secondaryAction: {
      label: 'Refresh',
      onClick: onRefresh || (() => window.location.reload()),
      icon: Refresh,
    },
    features: [
      {
        icon: DirectionsBike,
        title: 'Wide Selection',
        description: 'Choose from scooters, motorcycles, and luxury bikes',
        color: '#2196f3',
      },
      {
        icon: BookOnline,
        title: 'Easy Booking',
        description: 'Book instantly or request approval from owners',
        color: '#4caf50',
      },
      {
        icon: Star,
        title: 'Verified Owners',
        description: 'Rent from trusted, verified vehicle owners',
        color: '#ff9800',
      },
      {
        icon: Share,
        title: 'Share Experience',
        description: 'Rate and review your rental experience',
        color: '#9c27b0',
      },
    ],
    tips: [
      'Check vehicle ratings and reviews',
      'Read pickup instructions carefully',
      'Inspect the vehicle before riding',
      'Follow traffic rules and safety guidelines',
      'Return the vehicle on time and in good condition',
    ],
  };

  const content = variant === 'provider' ? providerContent : customerContent;

  return (
    <Box>
      {/* Main CTA Section */}
      <Paper
        elevation={2}
        sx={{
          background: variant === 'provider' 
            ? 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)'
            : 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          mb: 4,
        }}
      >
        {/* Icon */}
        <Box sx={{ mb: 4 }}>
          <Box
            sx={{
              width: 100,
              height: 100,
              bgcolor: 'primary.main',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              boxShadow: 3,
            }}
          >
            <BookOnline sx={{ fontSize: 50, color: 'white' }} />
          </Box>
        </Box>

        {/* Content */}
        <Typography variant="h3" component="h2" gutterBottom sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          {content.title}
        </Typography>

        <Typography variant="h5" sx={{ color: 'primary.main', mb: 3, fontWeight: 'medium' }}>
          {content.subtitle}
        </Typography>

        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: '600px', mx: 'auto' }}>
          {content.description}
        </Typography>

        {/* CTA Buttons */}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            onClick={content.primaryAction.onClick}
            variant="contained"
            size="large"
            startIcon={<content.primaryAction.icon />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
            }}
          >
            {content.primaryAction.label}
          </Button>

          <Button
            onClick={content.secondaryAction.onClick}
            variant="outlined"
            size="large"
            startIcon={<content.secondaryAction.icon />}
            sx={{
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              borderRadius: 2,
              borderWidth: 2,
            }}
          >
            {content.secondaryAction.label}
          </Button>
        </Box>

        {/* Progress Indicator */}
        <Box sx={{ pt: 3, borderTop: 1, borderColor: 'primary.light' }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {variant === 'provider' 
              ? '🚀 Pro Tip: Providers with complete profiles get 3x more bookings'
              : '🎯 Tip: Book early for better availability and pricing'
            }
          </Typography>
        </Box>
      </Paper>

      {/* Features */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3, fontWeight: 'bold' }}>
          {variant === 'provider' ? 'Booking Management Features' : 'Why Choose RentaHub?'}
        </Typography>
        
        <Grid container spacing={3}>
          {content.features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  }
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Avatar
                    sx={{
                      bgcolor: feature.color,
                      width: 60,
                      height: 60,
                      mx: 'auto',
                      mb: 2,
                    }}
                  >
                    <feature.icon sx={{ fontSize: 30 }} />
                  </Avatar>
                  <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Tips */}
      <Paper sx={{ p: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
          {variant === 'provider' ? 'Tips for Success' : 'Booking Tips'}
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <List dense>
              {content.tips.slice(0, Math.ceil(content.tips.length / 2)).map((tip, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 1}
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={tip} />
                </ListItem>
              ))}
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <List dense>
              {content.tips.slice(Math.ceil(content.tips.length / 2)).map((tip, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        bgcolor: 'secondary.main',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      {Math.ceil(content.tips.length / 2) + index + 1}
                    </Box>
                  </ListItemIcon>
                  <ListItemText primary={tip} />
                </ListItem>
              ))}
            </List>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default EmptyBookingsCTA;
