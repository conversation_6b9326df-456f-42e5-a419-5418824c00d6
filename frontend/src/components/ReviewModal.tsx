import React, { useState } from 'react';
import { 
  Modal, 
  Box, 
  Typography, 
  Rating, 
  TextField, 
  Button, 
  Stack 
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { submitReview } from '../services/ReviewService';

interface ReviewModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  vehicleId: string;
  providerId: string;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  open, 
  onClose, 
  bookingId, 
  vehicleId, 
  providerId 
}) => {
  const [vehicleRating, setVehicleRating] = useState<number | null>(0);
  const [providerRating, setProviderRating] = useState<number | null>(0);
  const [comment, setComment] = useState('');
  const { enqueueSnackbar } = useSnackbar();

  const handleSubmitReview = async () => {
    try {
      if (!vehicleRating || !providerRating) {
        enqueueSnackbar('Please rate both the vehicle and provider', { variant: 'warning' });
        return;
      }

      await submitReview({
        bookingId: bookingId,
        rating: (vehicleRating + providerRating) / 2,
        comment,
        reviewType: 'vehicle_review'
      });

      enqueueSnackbar('Review submitted successfully! +10 Loyalty Points', { variant: 'success' });
      onClose();
    } catch (error) {
      enqueueSnackbar('Failed to submit review', { variant: 'error' });
    }
  };

  const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
    borderRadius: 2
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="review-modal-title"
      aria-describedby="review-modal-description"
    >
      <Box sx={style}>
        <Typography id="review-modal-title" variant="h6" component="h2" gutterBottom>
          Rate Your Experience
        </Typography>

        <Stack spacing={2}>
          <Box>
            <Typography variant="subtitle1">Vehicle Rating</Typography>
            <Rating
              name="vehicle-rating"
              value={vehicleRating}
              onChange={(_, newValue) => setVehicleRating(newValue)}
              precision={1}
              size="large"
            />
          </Box>

          <Box>
            <Typography variant="subtitle1">Provider Rating</Typography>
            <Rating
              name="provider-rating"
              value={providerRating}
              onChange={(_, newValue) => setProviderRating(newValue)}
              precision={1}
              size="large"
            />
          </Box>

          <TextField
            fullWidth
            multiline
            rows={4}
            label="Additional Comments (Optional)"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            variant="outlined"
          />

          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleSubmitReview}
            fullWidth
          >
            Submit Review
          </Button>
        </Stack>
      </Box>
    </Modal>
  );
};

export default ReviewModal;
