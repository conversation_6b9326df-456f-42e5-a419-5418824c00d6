import React, { useState, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  IconButton,
  Chip,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Tooltip,
  Badge
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Schedule as ScheduleIcon,
  DirectionsBike as VehicleIcon,
  Person as CustomerIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
  ViewList as ListIcon,
  ViewModule as GridIcon
} from '@mui/icons-material';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, addDays, isToday } from 'date-fns';
import { Booking, Vehicle, BookingStatus } from '../types';

interface SimpleBookingSchedulerProps {
  bookings: Booking[];
  vehicles: Vehicle[];
  onBookingUpdate?: (bookingId: string, status: BookingStatus) => void;
  onBookingCreate?: (booking: Partial<Booking>) => void;
  onBookingDelete?: (bookingId: string) => void;
  loading?: boolean;
}

const SimpleBookingScheduler: React.FC<SimpleBookingSchedulerProps> = ({
  bookings = [],
  vehicles = [],
  onBookingUpdate,
  onBookingCreate,
  onBookingDelete,
  loading = false
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [view, setView] = useState<'week' | 'list'>('week');
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);

  // Generate week days
  const weekDays = useMemo(() => {
    try {
      const start = startOfWeek(currentWeek);
      const end = endOfWeek(currentWeek);
      return eachDayOfInterval({ start, end });
    } catch (error) {
      console.error('Error generating week days:', error);
      return [];
    }
  }, [currentWeek]);

  // Get bookings for a specific day
  const getBookingsForDay = (day: Date) => {
    try {
      return bookings.filter(booking => {
        const startDate = new Date(booking.startDate);
        const endDate = new Date(booking.endDate);
        return day >= startDate && day <= endDate;
      });
    } catch (error) {
      console.error('Error filtering bookings for day:', error);
      return [];
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      case 'completed':
        return 'info';
      default:
        return 'default';
    }
  };

  // Handle booking action
  const handleBookingAction = (bookingId: string, action: 'approve' | 'reject' | 'view') => {
    try {
      const booking = bookings.find(b => b.id === bookingId);
      if (!booking) return;

      switch (action) {
        case 'approve':
          onBookingUpdate?.(bookingId, 'CONFIRMED' as BookingStatus);
          break;
        case 'reject':
          onBookingUpdate?.(bookingId, 'CANCELLED' as BookingStatus);
          break;
        case 'view':
          setSelectedBooking(booking);
          break;
      }
    } catch (error) {
      console.error('Error handling booking action:', error);
    }
  };

  // Navigate week
  const navigateWeek = (direction: 'prev' | 'next') => {
    try {
      setCurrentWeek(prev => {
        const newDate = new Date(prev);
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        return newDate;
      });
    } catch (error) {
      console.error('Error navigating week:', error);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2">
          Booking Scheduler
        </Typography>
        
        <Box display="flex" gap={1}>
          <Tabs value={view} onChange={(_, newValue) => setView(newValue)}>
            <Tab icon={<GridIcon />} label="Week View" value="week" />
            <Tab icon={<ListIcon />} label="List View" value="list" />
          </Tabs>
        </Box>
      </Box>

      {view === 'week' && (
        <>
          {/* Week Navigation */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Button onClick={() => navigateWeek('prev')}>Previous Week</Button>
            <Typography variant="h6">
              {format(weekDays[0] || new Date(), 'MMM dd')} - {format(weekDays[6] || new Date(), 'MMM dd, yyyy')}
            </Typography>
            <Button onClick={() => navigateWeek('next')}>Next Week</Button>
          </Box>

          {/* Week Grid */}
          <Grid container spacing={1}>
            {weekDays.map((day, index) => {
              const dayBookings = getBookingsForDay(day);
              const isCurrentDay = isToday(day);
              
              return (
                <Grid item xs={12/7} key={index}>
                  <Paper 
                    elevation={isCurrentDay ? 3 : 1}
                    sx={{ 
                      minHeight: 200, 
                      p: 1,
                      backgroundColor: isCurrentDay ? 'primary.light' : 'background.paper',
                      color: isCurrentDay ? 'primary.contrastText' : 'text.primary'
                    }}
                  >
                    <Typography variant="subtitle2" align="center" gutterBottom>
                      {format(day, 'EEE dd')}
                    </Typography>
                    
                    <Box>
                      {dayBookings.map((booking) => {
                        const vehicle = vehicles.find(v => v.id === booking.vehicleId);
                        return (
                          <Card 
                            key={booking.id} 
                            sx={{ 
                              mb: 1, 
                              cursor: 'pointer',
                              '&:hover': { elevation: 2 }
                            }}
                            onClick={() => handleBookingAction(booking.id, 'view')}
                          >
                            <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
                              <Typography variant="caption" display="block">
                                {vehicle?.brand} {vehicle?.model}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {booking.customerName || 'Customer'}
                              </Typography>
                              <Box mt={0.5}>
                                <Chip 
                                  label={booking.status} 
                                  size="small" 
                                  color={getStatusColor(booking.status) as any}
                                />
                              </Box>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </Box>
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </>
      )}

      {view === 'list' && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Booking ID</TableCell>
                <TableCell>Vehicle</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>End Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {bookings.map((booking) => {
                const vehicle = vehicles.find(v => v.id === booking.vehicleId);
                return (
                  <TableRow key={booking.id}>
                    <TableCell>{booking.id.slice(-8)}</TableCell>
                    <TableCell>
                      {vehicle?.brand} {vehicle?.model}
                    </TableCell>
                    <TableCell>{booking.customerName || 'Customer'}</TableCell>
                    <TableCell>{format(new Date(booking.startDate), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>{format(new Date(booking.endDate), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>
                      <Chip 
                        label={booking.status} 
                        size="small" 
                        color={getStatusColor(booking.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="View Details">
                          <IconButton 
                            size="small" 
                            onClick={() => handleBookingAction(booking.id, 'view')}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        {booking.status === 'PENDING' && (
                          <>
                            <Tooltip title="Approve">
                              <IconButton 
                                size="small" 
                                color="success"
                                onClick={() => handleBookingAction(booking.id, 'approve')}
                              >
                                <ApproveIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Reject">
                              <IconButton 
                                size="small" 
                                color="error"
                                onClick={() => handleBookingAction(booking.id, 'reject')}
                              >
                                <RejectIcon />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Stats */}
      <Box mt={3}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {bookings.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Bookings
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {bookings.filter(b => b.status === 'PENDING').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {bookings.filter(b => b.status === 'CONFIRMED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Confirmed
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {bookings.filter(b => b.status === 'COMPLETED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completed
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default SimpleBookingScheduler;
