import React, { useState, useEffect } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Box,
  Button,
  Container,
  IconButton,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  Menu,
  MenuItem,
  Avatar
} from '@mui/material';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import {
  Menu as MenuIcon,
  AccountCircle as AccountIcon,
  Close as CloseIcon,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import LanguageSwitcher from './LanguageSwitcher';
import { useAuth } from '../contexts/AuthContext';

const Header: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuOpen(false);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setProfileMenuAnchor(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      handleProfileMenuClose();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Menu items based on authentication state
  const getMenuItems = () => {
    const baseItems = [
      { label: 'Home', path: '/' },
      { label: 'Find Vehicles', path: '/vehicles' },
      { label: 'List Your Vehicle', path: '/list-vehicle' },
      { label: 'About', path: '/about' },
      { label: 'Contact', path: '/contact' }
    ];

    if (isAuthenticated) {
      // Authenticated user - no sign in/up buttons
      return baseItems;
    } else {
      // Non-authenticated user - show sign in/up
      return [
        ...baseItems,
        { label: 'Sign In', path: '/login' },
        { label: 'Sign Up', path: '/signup' }
      ];
    }
  };

  const menuItems = getMenuItems();

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          {/* Logo - Always visible and links to home */}
          <Box
            component={RouterLink}
            to="/"
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              color: 'inherit',
              mr: 3,
              '&:hover': {
                opacity: 0.8
              }
            }}
          >
            <Typography
              variant="h5"
              component="div"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                letterSpacing: '0.5px'
              }}
            >
              RentaHub
            </Typography>
          </Box>

          {/* Desktop Navigation */}
          {!isMobile && (
            <Box sx={{ flexGrow: 1, display: 'flex', gap: 2 }}>
              <Button
                component={RouterLink}
                to="/"
                color="inherit"
                sx={{
                  textTransform: 'none',
                  fontWeight: location.pathname === '/' ? 'bold' : 'normal'
                }}
              >
                Home
              </Button>
              <Button
                component={RouterLink}
                to="/vehicles"
                color="inherit"
                sx={{
                  textTransform: 'none',
                  fontWeight: location.pathname === '/vehicles' ? 'bold' : 'normal'
                }}
              >
                Find Vehicles
              </Button>
              <Button
                component={RouterLink}
                to="/list-vehicle"
                color="inherit"
                sx={{
                  textTransform: 'none',
                  fontWeight: location.pathname === '/list-vehicle' ? 'bold' : 'normal'
                }}
              >
                List Your Vehicle
              </Button>
            </Box>
          )}

          {/* Right side items */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LanguageSwitcher />
            
            {!isMobile && (
              <>
                {isAuthenticated ? (
                  // Authenticated user - show profile menu
                  <IconButton
                    onClick={handleProfileMenuOpen}
                    sx={{ ml: 1 }}
                    data-testid="user-menu"
                  >
                    {user?.avatar ? (
                      <Avatar src={user.avatar} sx={{ width: 32, height: 32 }} />
                    ) : (
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                        {user?.name?.charAt(0).toUpperCase()}
                      </Avatar>
                    )}
                  </IconButton>
                ) : (
                  // Non-authenticated user - show sign in/up buttons
                  <>
                    <Button
                      component={RouterLink}
                      to="/login"
                      color="inherit"
                      startIcon={<AccountIcon />}
                      sx={{ textTransform: 'none' }}
                    >
                      Sign In
                    </Button>
                    <Button
                      component={RouterLink}
                      to="/signup"
                      variant="contained"
                      sx={{
                        textTransform: 'none',
                        ml: 1,
                        bgcolor: 'primary.main',
                        '&:hover': {
                          bgcolor: 'primary.dark'
                        }
                      }}
                    >
                      Sign Up
                    </Button>
                  </>
                )}
              </>
            )}

            {isMobile && (
              <IconButton
                color="inherit"
                aria-label="menu"
                onClick={handleMobileMenuToggle}
                sx={{ ml: 1 }}
                data-testid="mobile-menu-button"
              >
                <MenuIcon />
              </IconButton>
            )}
          </Box>
        </Toolbar>
      </Container>

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={handleMobileMenuClose}
        PaperProps={{
          sx: {
            width: 280,
            backgroundColor: 'background.paper'
          }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
            RentaHub
          </Typography>
          <IconButton onClick={handleMobileMenuClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />

        <List sx={{ pt: 0 }}>
          {menuItems.map((item) => (
            <ListItem key={item.path} disablePadding>
              <ListItemButton
                component={RouterLink}
                to={item.path}
                onClick={handleMobileMenuClose}
                sx={{
                  py: 1.5,
                  '&:hover': {
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText'
                  },
                  ...(location.pathname === item.path && {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark'
                    }
                  })
                }}
              >
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontWeight: location.pathname === item.path ? 'bold' : 'normal'
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        {/* User Profile Section in Mobile Menu */}
        {isAuthenticated && (
          <>
            <Divider />
            <List>
              <ListItem disablePadding>
                <ListItemButton
                  component={RouterLink}
                  to="/dashboard"
                  onClick={handleMobileMenuClose}
                  sx={{ py: 1.5 }}
                >
                  <DashboardIcon sx={{ mr: 2 }} />
                  <ListItemText primary="Dashboard" />
                </ListItemButton>
              </ListItem>
              <ListItem disablePadding>
                <ListItemButton onClick={handleLogout} sx={{ py: 1.5 }}>
                  <ListItemText primary="Logout" />
                </ListItemButton>
              </ListItem>
            </List>
          </>
        )}

        <Box sx={{ mt: 'auto', p: 2 }}>
          <Divider sx={{ mb: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <LanguageSwitcher />
          </Box>
        </Box>
      </Drawer>

      {/* Profile Menu */}
      <Menu
        anchorEl={profileMenuAnchor}
        open={Boolean(profileMenuAnchor)}
        onClose={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileMenuClose} component={RouterLink} to="/dashboard" data-testid="profile-link">
          <DashboardIcon sx={{ mr: 1 }} />
          Dashboard
        </MenuItem>
        {isAuthenticated && (
          <MenuItem onClick={handleLogout} data-testid="logout-button">
            <ListItemText primary="Logout" />
          </MenuItem>
        )}
      </Menu>
    </AppBar>
  );
};

export default Header;