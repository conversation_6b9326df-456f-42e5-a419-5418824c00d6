import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Box, Button, Stack, Typography } from '@mui/material'
import { monitoring } from '../services/MonitoringService'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo,
    })

    monitoring.captureError(error, {
      errorBoundary: true,
      componentStack: errorInfo.componentStack,
    })

    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    if (window.location.hostname !== 'localhost') {
      this.logErrorToService(error, errorInfo)
    }
  }

  private logErrorToService = (error: Error, errorInfo: React.ErrorInfo) => {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    }

    fetch('/api/frontend-errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorData),
    }).catch(console.error)
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Box sx={{ p: 6, maxWidth: 'md', mx: 'auto', mt: 8 }}>
          <Stack spacing={4}>
            <Alert severity="error">
              <AlertTitle>Error</AlertTitle>
              Something went wrong. Please try refreshing the page.
            </Alert>
            
            {import.meta.env.DEV && this.state.error && (
              <Box sx={{ 
                p: 4, 
                bgcolor: 'grey.100', 
                borderRadius: 1,
                fontSize: '0.875rem'
              }}>
                <Typography fontWeight="bold">Error Details:</Typography>
                <Typography color="error" sx={{ mt: 1 }}>
                  {this.state.error.message}
                </Typography>
                <Typography 
                  sx={{ 
                    mt: 2, 
                    fontSize: '0.75rem',
                    fontFamily: 'monospace',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {this.state.error.stack}
                </Typography>
              </Box>
            )}
            
            <Stack spacing={2} alignItems="center">
              <Button 
                variant="contained" 
                color="primary" 
                onClick={this.handleRetry}
              >
                Try Again
              </Button>
              <Button 
                variant="text"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            </Stack>
          </Stack>
        </Box>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary;