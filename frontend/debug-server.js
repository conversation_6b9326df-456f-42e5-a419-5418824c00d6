const express = require('express');
const path = require('path');
const app = express();
const port = 3000;

// Serve static files from dist
app.use(express.static(path.join(__dirname, 'dist')));

// Serve the index.html for all routes (SPA routing)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, () => {
  console.log(`Debug server running at http://localhost:${port}`);
});
