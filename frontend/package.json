{"name": "rentahub-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host 0.0.0.0 --port 4173", "build:production": "NODE_ENV=production vite build", "serve": "vite preview --port 4173", "serve:simple": "node simple-serve.js", "start": "node deploy.js", "start:prod": "serve -s dist -l ${PORT:-4173}", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "validate-env": "node ../scripts/validate-env.js", "rotate-credentials": "bash ../scripts/rotate-credentials.sh"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.4", "@mui/x-date-pickers": "^6.12.0", "@react-google-maps/api": "^2.20.7", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^2.1.0", "@supabase/supabase-js": "^2.39.0", "@types/multer": "^2.0.0", "@types/validator": "^13.15.2", "axios": "^1.7.7", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "date-fns": "^2.30.0", "escape-string-regexp": "^5.0.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "isomorphic-dompurify": "^2.26.0", "multer": "^2.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-router-dom": "^6.8.0", "react-toastify": "^9.1.3", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "stream-browserify": "^3.0.0", "validator": "^13.15.15"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/i18next": "^13.0.0", "@types/jest": "^29.5.14", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.6.0", "esbuild": "^0.17.19", "eslint": "^8.45.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "serve": "^14.2.4", "terser": "^5.19.2", "typescript": "^5.1.6", "vite": "^4.4.9", "vitest": "^0.34.6"}, "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}