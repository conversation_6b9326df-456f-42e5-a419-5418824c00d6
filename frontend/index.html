<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="RentaHub - Scooter & Motorbike Rental Service" />
  <title>RentaHub - Scooter & Motorbike Rental Service</title>

  <!-- Block unwanted browser extension injections and suppress dev warnings -->
  <script>
    // Block Bybit and other crypto extension injections
    if (typeof window !== 'undefined') {
      // Block extension objects
      Object.defineProperty(window, 'bybit', {
        value: undefined,
        writable: false,
        configurable: false
      });

      // Block common crypto extension properties
      ['ethereum', 'web3', 'coinbase', 'metamask'].forEach(prop => {
        try {
          Object.defineProperty(window, prop, {
            get: () => undefined,
            set: () => {},
            configurable: false
          });
        } catch (e) {
          // Ignore if property already exists
        }
      });

      // Suppress development warnings and extension messages
      const originalWarn = console.warn;
      const originalLog = console.log;

      console.warn = function(...args) {
        const message = args.join(' ');
        if (message.includes('You may test your Stripe.js integration over HTTP') ||
            message.includes('unsafe-eval') ||
            message.includes('Content Security Policy') ||
            message.includes('WebAssembly') ||
            message.includes('Auth session missing')) {
          return; // Suppress development warnings
        }
        originalWarn.apply(console, args);
      };

      console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('bybit') ||
            message.includes('inject code') ||
            message.includes('provider inject') ||
            message.includes('bybit:page provider inject code')) {
          return; // Suppress crypto extension injection messages
        }
        originalLog.apply(console, args);
      };

      // Also suppress console errors for development noise
      const originalError = console.error;
      console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('unsafe-eval') ||
            message.includes('Content Security Policy') ||
            message.includes('WebAssembly') ||
            message.includes('Refused to compile') ||
            message.includes('Refused to evaluate')) {
          return; // Suppress CSP development errors
        }
        originalError.apply(console, args);
      };

      // Also suppress console.info and console.debug
      const originalInfo = console.info;
      const originalDebug = console.debug;
      // originalError already declared above

      console.info = function(...args) {
        const message = args.join(' ');
        if (message.includes('bybit') || message.includes('inject')) {
          return;
        }
        originalInfo.apply(console, args);
      };

      console.debug = function(...args) {
        const message = args.join(' ');
        if (message.includes('bybit') || message.includes('inject')) {
          return;
        }
        originalDebug.apply(console, args);
      };

      console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('bybit:page provider inject code') ||
            (message.includes('bybit') && message.includes('inject'))) {
          return;
        }
        originalError.apply(console, args);
      };

      // Override all console methods more aggressively
      ['log', 'info', 'warn', 'error', 'debug'].forEach(method => {
        const original = console[method];
        console[method] = function(...args) {
          const message = args.join(' ');
          if (message.includes('bybit:page provider inject code') ||
              message.includes('bybit') && message.includes('inject') ||
              message.includes('bybit') && message.includes('provider') ||
              message.includes('bybit:page') ||
              args.some(arg => typeof arg === 'string' && arg.includes('bybit'))) {
            return; // Completely suppress these messages
          }
          original.apply(console, args);
        };
      });

      // Also intercept at the window level
      const originalWindowConsole = window.console;
      Object.keys(originalWindowConsole).forEach(method => {
        if (typeof originalWindowConsole[method] === 'function') {
          const original = originalWindowConsole[method];
          window.console[method] = function(...args) {
            const message = args.join(' ');
            if (message.includes('bybit') || args.some(arg => typeof arg === 'string' && arg.includes('bybit'))) {
              return;
            }
            original.apply(console, args);
          };
        }
      });

      // Block extension script injections more aggressively
      const originalAppendChild = Element.prototype.appendChild;
      Element.prototype.appendChild = function(child) {
        if (child.tagName === 'SCRIPT' && child.textContent &&
            child.textContent.includes('bybit')) {
          return child; // Block the script but return it to avoid errors
        }
        return originalAppendChild.call(this, child);
      };
    }
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>