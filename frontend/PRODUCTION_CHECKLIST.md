# MVP Production Checklist

## 🚀 Pre-Deployment Steps

### 1. Environment Configuration
- [ ] Copy `.env.example` to `.env.production`
- [ ] Set production API URL: `VITE_API_URL=https://your-backend.railway.app/api`
- [ ] Configure Supabase production credentials
- [ ] Set production Stripe keys
- [ ] Disable development features: `VITE_ENABLE_SOURCE_MAPS=false`

### 2. Database Setup (Supabase)
Create `frontend_errors` table for error logging:
```sql
CREATE TABLE frontend_errors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  message TEXT NOT NULL,
  stack TEXT,
  context JSONB,
  url TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policy
ALTER TABLE frontend_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow insert for authenticated users" ON frontend_errors
FOR INSERT TO authenticated
WITH CHECK (true);
```

### 3. Build & Deploy
```bash
# Install dependencies (when disk space available)
npm install

# Run tests
npm run test

# Build for production
npm run build:production

# Deploy to Railway
# (Configure Railway to build from /frontend directory)
```

### 4. Railway Deployment Settings
- Build Command: `npm run build:production`
- Start Command: `npm run serve`
- Root Directory: `/frontend`
- Node Version: 18+

## 🔧 Production Features Active

### ✅ Error Handling
- Global error boundaries
- Automatic error logging to Supabase
- User-friendly error messages
- Retry mechanisms

### ✅ Testing Framework
- Vitest + Testing Library setup
- Component and integration tests
- Test coverage reporting

### ✅ Performance
- Code splitting by vendor/ui/utils
- Lazy loading for routes
- Source maps in development only

## 🏗️ Post-MVP Considerations

### Consider adding later:
- [ ] Sentry for advanced error monitoring
- [ ] Analytics (Google Analytics)
- [ ] PWA features
- [ ] Push notifications
- [ ] Advanced caching strategies

## 🚨 Critical Production Issues to Monitor

1. **Payment failures** - Check Stripe dashboard
2. **API errors** - Monitor backend logs
3. **User authentication issues** - Check Supabase auth
4. **Frontend errors** - Check `frontend_errors` table

## 📊 Key Metrics to Track

- Error rate in `frontend_errors` table
- User sign-up/login success rates
- Booking completion rates
- Payment success rates

## 🔧 Quick Debugging

### Check frontend errors:
```sql
SELECT * FROM frontend_errors 
ORDER BY created_at DESC 
LIMIT 20;
```

### Most common errors:
```sql
SELECT message, COUNT(*) as count 
FROM frontend_errors 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY message 
ORDER BY count DESC;
```