const { spawn } = require('child_process');
const path = require('path');

console.log('Starting RentaHub Frontend Development Server...');
console.log('Current directory:', process.cwd());

// Start the Vite development server
const viteProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.join(__dirname)
});

viteProcess.on('error', (error) => {
  console.error('Failed to start server:', error);
});

viteProcess.on('exit', (code) => {
  console.log(`Server process exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  viteProcess.kill('SIGINT');
  process.exit(0);
});

console.log('Development server starting...');
console.log('Server should be available at http://localhost:3000');
console.log('Press Ctrl+C to stop the server');
