# RENTAHUB Frontend Environment Variables
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# API Configuration
VITE_API_URL=http://localhost:3001

# Supabase Configuration (Public keys only - safe for client)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Stripe Configuration (Publishable key only - safe for client)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Google Configuration (Client ID only - safe for client)
VITE_GOOGLE_CLIENT_ID=your_google_oauth_client_id
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Application Configuration
VITE_APP_NAME=RentaHub
VITE_APP_DESCRIPTION=Premium Vehicle Rental Platform
VITE_APP_VERSION=1.0.0

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_CONTACT_EMAIL=<EMAIL>

# Feature Flags
VITE_ENABLE_GOOGLE_AUTH=true
VITE_ENABLE_STRIPE_PAYMENTS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true

# Security Note:
# - Only VITE_ prefixed variables are exposed to the client
# - Never put secret keys, passwords, or sensitive data in VITE_ variables
# - Backend secrets should only be in backend .env files
