#!/bin/bash

echo "🧪 RentaHub Frontend Test"
echo "========================="

cd "$(dirname "$0")"

echo "1. ✅ Checking if frontend dependencies are installed..."
if [ ! -d "node_modules" ]; then
    echo "❌ node_modules not found. Run: npm install"
    exit 1
fi

echo "2. ✅ Testing TypeScript compilation..."
npx tsc --noEmit --pretty || {
    echo "❌ TypeScript errors found"
    exit 1
}

echo "3. ✅ Checking environment variables..."
if [ ! -f ".env" ]; then
    echo "❌ .env file not found"
    exit 1
fi

echo "4. ✅ All checks passed!"
echo ""
echo "🚀 Ready to start development server:"
echo "   npm run dev"
echo ""
echo "🔧 To start backend server:"
echo "   cd ../backend && ./start-backend.sh"
