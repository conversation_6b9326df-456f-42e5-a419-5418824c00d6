// Quick test file to verify environment variable fixes
console.log('=== Environment Variable Test ===');
console.log('NODE_ENV check:', typeof process !== 'undefined' ? process.env.NODE_ENV : 'process not available (good for browser)');
console.log('Testing Vite env vars (these should work in browser):');
console.log('VITE_API_URL:', import.meta?.env?.VITE_API_URL || 'not set');
console.log('VITE_STRIPE_PUBLISHABLE_KEY:', import.meta?.env?.VITE_STRIPE_PUBLISHABLE_KEY ? '***configured***' : 'not set');
console.log('DEV mode:', import.meta?.env?.DEV || false);
console.log('PROD mode:', import.meta?.env?.PROD || false);
console.log('=== Test Complete ===');
