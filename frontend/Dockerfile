# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with legacy peer deps to avoid conflicts
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Set environment variables for build
ENV VITE_API_URL=https://api.rentahub.info/api
ENV VITE_SUPABASE_URL=https://rocxjzukyqelvuyltrfq.supabase.co
ENV VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8
ENV VITE_GOOGLE_CLIENT_ID=177790503193-5g624cighvbma0n0nrrg4oqljagdegi5.apps.googleusercontent.com

# Build the application
RUN npm run build

# Production stage - use nginx to serve static files
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create a startup script that handles the PORT variable
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'PORT=${PORT:-80}' >> /start.sh && \
    echo 'sed -i "s/listen 80;/listen $PORT;/" /etc/nginx/conf.d/default.conf' >> /start.sh && \
    echo 'nginx -g "daemon off;"' >> /start.sh && \
    chmod +x /start.sh

# Expose port (Railway will set the PORT environment variable)
EXPOSE $PORT

# Start nginx using our startup script
CMD ["/start.sh"]
