import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode`
  const env = loadEnv(mode, process.cwd(), '')

  // Security: Only expose VITE_ prefixed variables and validate them
  const secureEnvVars = {
    'import.meta.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL || (mode === 'development' ? 'http://localhost:3001' : '/api')),
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL || ''),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY || ''),
    'import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY': JSON.stringify(env.VITE_STRIPE_PUBLISHABLE_KEY || ''),
    'import.meta.env.VITE_GOOGLE_CLIENT_ID': JSON.stringify(env.VITE_GOOGLE_CLIENT_ID || ''),
    'import.meta.env.VITE_GOOGLE_MAPS_API_KEY': JSON.stringify(env.VITE_GOOGLE_MAPS_API_KEY || ''),
  };

  // Security: Validate that no secret keys are accidentally exposed
  Object.entries(secureEnvVars).forEach(([key, value]) => {
    const stringValue = JSON.parse(value);
    if (stringValue && (
      stringValue.includes('sk_') || // Stripe secret key
      stringValue.includes('secret') ||
      stringValue.includes('private') ||
      key.toLowerCase().includes('secret')
    )) {
      console.error(`🚨 SECURITY WARNING: Potential secret key detected in ${key}`);
      process.exit(1);
    }
  });

  // Security: Log environment validation
  if (mode === 'development') {
    console.log('🔒 Security: Environment variables validated - no secrets exposed');
  }

  return {
    plugins: [react()],
    define: secureEnvVars,
    optimizeDeps: {
      include: [
        'react', 
        'react-dom', 
        'react-router-dom', 
        'axios',
        '@mui/material',
        '@mui/icons-material'
      ],
      force: true
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@/components': path.resolve(__dirname, './src/components'),
        '@/pages': path.resolve(__dirname, './src/pages'),
        '@/services': path.resolve(__dirname, './src/services'),
        '@/types': path.resolve(__dirname, './src/types'),
        '@/utils': path.resolve(__dirname, './src/utils'),
        '@/styles': path.resolve(__dirname, './src/styles'),
      },
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.mjs']
    },
    server: {
      port: 5173,
      host: true,
      strictPort: false,
      open: true,
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false,
          // Keep the /api prefix - don't rewrite
        }
      }
    },
    preview: {
      port: 4173,
      host: true,
      strictPort: false,
    },
    build: {
      outDir: 'dist',
      sourcemap: mode === 'development',
      minify: 'terser',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            ui: ['@mui/material', '@mui/icons-material'],
            utils: ['axios', 'date-fns']
          }
        }
      }
    }
  }
})