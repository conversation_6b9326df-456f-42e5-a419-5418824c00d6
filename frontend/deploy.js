#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Change to the frontend directory
process.chdir(__dirname);

console.log('Starting frontend deployment...');
console.log('Current directory:', process.cwd());

// Check if dist directory exists
if (!fs.existsSync('dist')) {
  console.log('Dist directory not found, building frontend...');
  const { execSync } = require('child_process');
  execSync('npm run build:production', { stdio: 'inherit' });
} else {
  console.log('Using pre-built dist directory...');
}

const port = process.env.PORT || 4173;
const distPath = path.join(__dirname, 'dist');

// MIME type mapping
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.webp': 'image/webp',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;
  
  // Default to index.html for root
  if (pathname === '/') {
    pathname = '/index.html';
  }
  
  const filePath = path.join(distPath, pathname);
  
  // Check if file exists
  fs.stat(filePath, (err, stat) => {
    if (err || !stat.isFile()) {
      // File not found, serve index.html for SPA routing
      const indexPath = path.join(distPath, 'index.html');
      fs.readFile(indexPath, (err, data) => {
        if (err) {
          res.writeHead(404);
          res.end('Not Found');
          return;
        }
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(data);
      });
      return;
    }
    
    // Serve the file
    const ext = path.extname(filePath);
    const mimeType = mimeTypes[ext] || 'application/octet-stream';
    
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(500);
        res.end('Internal Server Error');
        return;
      }
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(data);
    });
  });
});

server.listen(port, '0.0.0.0', () => {
  console.log(`Frontend server running on port ${port}`);
});
