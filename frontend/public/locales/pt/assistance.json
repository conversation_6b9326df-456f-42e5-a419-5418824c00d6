{"title": "Assistência Veicular", "subtitle": "Obtenha ajuda com seu problema veicular. Nossa equipe responderá baseada no nível de prioridade.", "categories": {"breakdown": "Pane do Veículo", "accident": "<PERSON><PERSON><PERSON>", "flatTire": "Pneu Furado", "fuelEmpty": "Se<PERSON>ust<PERSON>", "batteryDead": "Bateria Descarregada", "keyLocked": "<PERSON><PERSON>", "mechanicalIssue": "Problema Mecânico", "electricalIssue": "Problema Elétrico", "other": "Outro Problema"}, "priority": {"urgent": "Urgente", "high": "Alto", "medium": "Médio", "low": "Baixo"}, "responseTime": {"urgent": "15-30 minutos", "high": "30-60 minutos", "medium": "1-2 horas", "low": "2-4 horas"}, "form": {"selectCategory": "Que tipo de assistência você precisa?", "describeIssue": "Descreva o <PERSON>a", "describeIssuePlaceholder": "Por favor forneça detalhes sobre o que aconteceu...", "additionalInfo": "Informações Adicionais", "additionalInfoPlaceholder": "Qualquer detalhe adicional que possa ajudar...", "currentLocation": "Localização Atual", "locationPlaceholder": "Endereço, ponto de referência ou coordenadas GPS...", "locationHelper": "Nos ajude a encontrá-lo mais rápido fornecendo sua localização exata", "priorityLevel": "Nível de Prioridade"}, "status": {"pending": "Pendente", "assigned": "Atribu<PERSON><PERSON>", "inProgress": "Em Andamento", "resolved": "Resolvido", "cancelled": "Cancelado"}, "messages": {"requestSent": "Solicitação de Assistência Enviada!", "teamNotified": "Nossa equipe foi notificada e entrará em contato em breve.", "sendingRequest": "Enviando Solicitação...", "requestAssistance": "Solicitar Assistência Veicular"}, "errors": {"categoryRequired": "Por favor selecione uma categoria e forneça um motivo para assistência", "failed": "Falha ao criar solicitação de assistência. Por favor tente novamente."}}