{"title": "Помощь с Транспортом", "subtitle": "Получите помощь с проблемой вашего транспорта. Наша команда ответит в зависимости от уровня приоритета.", "categories": {"breakdown": "Поломка Транспорта", "accident": "Авария", "flatTire": "Спущенная Шина", "fuelEmpty": "Закончилось Топливо", "batteryDead": "Разряженная Батарея", "keyLocked": "Ключи Заперты", "mechanicalIssue": "Механическая Проблема", "electricalIssue": "Электрическая Проблема", "other": "Другая Проблема"}, "priority": {"urgent": "Срочно", "high": "Высокий", "medium": "Средний", "low": "Низкий"}, "responseTime": {"urgent": "15-30 минут", "high": "30-60 минут", "medium": "1-2 часа", "low": "2-4 часа"}, "form": {"selectCategory": "Какой тип помощи вам нужен?", "describeIssue": "Опишите проблему", "describeIssuePlaceholder": "Пожалуйста, предоставьте детали о том, что произошло...", "additionalInfo": "Дополнительная Информация", "additionalInfoPlaceholder": "Любые дополнительные детали, которые могут помочь...", "currentLocation": "Текущее Местоположение", "locationPlaceholder": "Адрес улицы, ориентир или GPS координаты...", "locationHelper": "Помогите нам найти вас быстрее, предоставив точное местоположение", "priorityLevel": "Уровень Приоритета"}, "status": {"pending": "Ожидание", "assigned": "Назначено", "inProgress": "В Процессе", "resolved": "Решено", "cancelled": "Отменено"}, "messages": {"requestSent": "Запрос на Помощь Отправлен!", "teamNotified": "Наша команда уведомлена и скоро свяжется с вами.", "sendingRequest": "Отправка Запроса...", "requestAssistance": "Запросить Помощь с Транспортом"}, "errors": {"categoryRequired": "Пожалуйста, выберите категорию и укажите причину для помощи", "failed": "Не удалось создать запрос на помощь. Пожалуйста, попробуйте снова."}}