{"title": "Search Vehicles", "subtitle": "Find the perfect vehicle for your journey", "location": {"label": "Location", "placeholder": "Enter city or area", "current": "Use current location", "popular": "Popular locations"}, "dates": {"startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration"}, "filters": {"vehicleType": "Vehicle Type", "priceRange": "Price Range", "features": "Features", "rating": "Minimum Rating", "availability": "Availability"}, "vehicleTypes": {"all": "All Types", "scooter": "<PERSON>ooter", "motorcycle": "Motorcycle", "electric": "Electric", "manual": "Manual", "automatic": "Automatic"}, "features": {"helmet": "Helmet Included", "insurance": "Insurance Included", "gps": "GPS Navigation", "delivery": "Delivery Available", "support": "24/7 Support"}, "buttons": {"search": "Search Vehicles", "clear": "Clear Filters", "apply": "Apply Filters", "reset": "Reset"}, "results": {"found": "vehicles found", "noResults": "No vehicles found", "tryDifferent": "Try different search criteria", "loading": "Searching vehicles...", "error": "Error loading vehicles"}, "sorting": {"label": "Sort by", "price_low": "Price: Low to High", "price_high": "Price: High to Low", "rating": "Highest Rated", "distance": "Nearest First", "newest": "Newest First"}, "map": {"showMap": "Show Map", "hideMap": "Hide Map", "viewOnMap": "View on Map", "directions": "Get Directions"}}