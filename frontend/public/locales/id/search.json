{"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON> kendaraan yang sempurna untuk perjalanan <PERSON>a", "location": {"label": "<PERSON><PERSON>", "placeholder": "Masukkan kota atau area", "current": "Gunakan lokasi saat ini", "popular": "Lokasi populer"}, "dates": {"startDate": "<PERSON><PERSON>", "endDate": "<PERSON><PERSON>", "startTime": "<PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>"}, "filters": {"vehicleType": "<PERSON><PERSON>", "priceRange": "<PERSON><PERSON><PERSON>", "features": "<PERSON><PERSON>", "rating": "Rating Minimum", "availability": "<PERSON><PERSON><PERSON><PERSON>"}, "vehicleTypes": {"all": "<PERSON><PERSON><PERSON>", "scooter": "<PERSON><PERSON><PERSON>", "motorcycle": "Motor", "electric": "<PERSON><PERSON>", "manual": "Manual", "automatic": "<PERSON><PERSON><PERSON><PERSON>"}, "features": {"helmet": "<PERSON><PERSON>", "insurance": "Asuransi Disertakan", "gps": "Navigasi GPS", "delivery": "<PERSON>tar <PERSON>", "support": "Dukungan 24/7"}, "buttons": {"search": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>", "apply": "Terap<PERSON> Filter", "reset": "Reset"}, "results": {"found": "k<PERSON><PERSON><PERSON> di<PERSON>n", "noResults": "Tidak ada kendaraan di<PERSON>n", "tryDifferent": "Coba kriteria pencarian yang berbeda", "loading": "<PERSON><PERSON><PERSON> k<PERSON>...", "error": "<PERSON><PERSON>r memuat kend<PERSON>an"}, "sorting": {"label": "<PERSON>ru<PERSON><PERSON> be<PERSON>", "price_low": "Harga: <PERSON><PERSON> ke Tinggi", "price_high": "Harga: <PERSON><PERSON><PERSON> ke <PERSON>dah", "rating": "Rating <PERSON>gi", "distance": "Terdekat Dulu", "newest": "Terbaru Dulu"}, "map": {"showMap": "<PERSON><PERSON><PERSON><PERSON>", "hideMap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewOnMap": "<PERSON><PERSON>", "directions": "Dapatkan Arah"}}