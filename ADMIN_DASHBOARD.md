# RentaHub Admin Dashboard

## Overview
The RentaHub Admin Dashboard provides comprehensive platform oversight, enabling administrators to monitor, moderate, and analyze platform activities.

## Key Features

### 1. Listings Moderation
- View all vehicle listings
- Filter by status, vehicle type, and provider region
- Actions:
  - Approve listings
  - Disable problematic listings
  - Add approval/disable notes

### 2. SOS Alerts Panel
- Real-time tracking of emergency alerts
- Detailed alert information
- Resolution workflow
- Status tracking (Pending/Resolved)

### 3. Support Ticket System
- Comprehensive ticket management
- Filter by category and status
- Actions:
  - Update ticket status
  - Add resolution notes
  - Escalate tickets

### 4. Analytics Dashboard
#### Overview Cards
- Total Bookings
- Total Revenue
- Platform Commission
- User/Provider Statistics

#### Charts
- Booking Status Distribution
- User Role Distribution
- Revenue Metrics

## Authorization
- Strict role-based access control
- Only users with `ADMIN` role can access
- Middleware protection on both frontend and backend

## Backend Routes
- `/api/admin/listings`
- `/api/admin/sos-alerts`
- `/api/admin/support-tickets`
- `/api/admin/analytics`

## Security Considerations
- JWT authentication
- Role-based middleware
- Comprehensive logging
- Input validation

## Future Enhancements
- Real-time notifications
- Advanced filtering
- Exportable reports
- Integrated communication tools

## Technical Stack
- Frontend: React, Material-UI, Recharts
- Backend: Node.js, Express, Prisma
- Authentication: JWT
- State Management: React Hooks

## Performance Optimization
- Efficient database queries
- Pagination
- Caching mechanisms
- Lazy loading of components

## Monitoring & Logging
- Capture all administrative actions
- Detailed error tracking
- Performance metrics

## Compliance
- GDPR considerations
- Data privacy protection
- Audit trail maintenance

## Installation & Setup
1. Ensure all dependencies are installed
2. Configure environment variables
3. Run database migrations
4. Set up admin user accounts

## Troubleshooting
- Check network connectivity
- Verify authentication tokens
- Review server logs
- Validate database connections

## Contributing
Please read our contribution guidelines before submitting pull requests.

## License
[Your License Information]
