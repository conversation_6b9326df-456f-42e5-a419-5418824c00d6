#!/bin/bash

# RENTAHUB Railway Environment Setup Script
# This script sets up environment variables in Railway for all services

set -e  # Exit immediately if a command exits with a non-zero status

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
  exit 1
}

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
  log_error "Railway CLI not found. Please install it with: npm i -g @railway/cli"
fi

# Check if logged in
if ! railway whoami &> /dev/null; then
  log_error "Not logged in to Railway. Please run 'railway login' first."
fi

# Function to set environment variables for a service
setup_env_vars() {
  local service=$1
  local env_file=$2
  
  log_info "Setting up environment variables for $service..."
  
  # Check if the environment file exists
  if [ ! -f "$env_file" ]; then
    log_error "Environment file $env_file not found"
  fi
  
  # Read environment file and set variables
  while IFS= read -r line || [[ -n "$line" ]]; do
    # Skip empty lines and comments
    if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
      continue
    fi
    
    # Extract variable name and value
    if [[ "$line" =~ ^([^=]+)=(.*)$ ]]; then
      var_name="${BASH_REMATCH[1]}"
      var_value="${BASH_REMATCH[2]}"
      
      # Remove quotes if present
      var_value="${var_value%\"}"
      var_value="${var_value#\"}"
      var_value="${var_value%\'}"
      var_value="${var_value#\'}"
      
      # Set the variable in Railway
      if [ -n "$var_name" ] && [ -n "$var_value" ] && [ "$var_value" != "your_value_here" ]; then
        log_info "Setting $var_name for $service"
        railway variables set "$var_name=$var_value" --service "$service"
      fi
    fi
  done < "$env_file"
}

# Main function
main() {
  log_info "Starting Railway environment setup for RentaHub..."
  
  # Ask which service to configure
  echo "Which service would you like to configure?"
  echo "1. Backend"
  echo "2. Frontend"
  echo "3. Admin"
  echo "4. All services"
  read -p "Enter option (1-4): " service_option
  
  # Convert env.example to temporary files for each service
  cp __config/env.example backend.env
  cp __config/env.example frontend.env
  cp __config/env.example admin.env
  
  # Add frontend-specific vars
  echo "VITE_API_URL=https://rentahub-backend-production.up.railway.app" >> frontend.env
  
  # Add admin-specific vars
  echo "VITE_API_URL=https://rentahub-backend-production.up.railway.app" >> admin.env
  
  case $service_option in
    1)
      setup_env_vars "backend" "backend.env"
      ;;
    2)
      setup_env_vars "frontend" "frontend.env"
      ;;
    3)
      setup_env_vars "admin" "admin.env"
      ;;
    4)
      setup_env_vars "backend" "backend.env"
      setup_env_vars "frontend" "frontend.env"
      setup_env_vars "admin" "admin.env"
      ;;
    *)
      log_error "Invalid option. Please enter 1, 2, 3, or 4."
      ;;
  esac
  
  # Clean up temporary files
  rm -f backend.env frontend.env admin.env
  
  log_info "Railway environment setup completed!"
}

# Run the main function
main
