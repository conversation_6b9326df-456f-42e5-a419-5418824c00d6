#!/bin/bash

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to validate input
validate_input() {
    local input=$1
    if [ -z "$input" ]; then
        echo -e "${RED}Error: Input cannot be empty.${NC}"
        return 1
    fi
    return 0
}

# Function to set a secret with validation
set_secret() {
    local secret_name=$1
    local attempts=0
    local secret_value=""

    while [ $attempts -lt 3 ]; do
        echo -e "${YELLOW}Enter value for $secret_name:${NC}"
        read -sp "> " secret_value
        echo

        validate_input "$secret_value"
        if [ $? -eq 0 ]; then
            # Confirm the secret
            echo -e "${YELLOW}Confirm the secret for $secret_name (type it again):${NC}"
            read -sp "> " confirm_value
            echo

            if [ "$secret_value" = "$confirm_value" ]; then
                # Set the secret
                gh secret set "$secret_name" --body "$secret_value"
                echo -e "${GREEN}✓ Secret $secret_name set successfully!${NC}"
                return 0
            else
                echo -e "${RED}Error: Secrets do not match. Please try again.${NC}"
            fi
        fi

        ((attempts++))
    done

    echo -e "${RED}❌ Failed to set secret $secret_name after 3 attempts.${NC}"
    return 1
}

# Main script
main() {
    # Check if GitHub CLI is installed
    if ! command -v gh &> /dev/null; then
        echo -e "${RED}GitHub CLI (gh) is not installed. Please install it first.${NC}"
        exit 1
    fi

    # Ensure we're in the correct repository context
    gh repo set-default 2020visiontrader/RENTAHUB

    # List of secrets to set up with descriptions
    declare -A secret_descriptions=(
        ["SLACK_WEBHOOK"]="Slack webhook URL for notifications"
        ["STRIPE_TEST_SECRET_KEY"]="Stripe test secret key for payment testing"
        ["STRIPE_TEST_WEBHOOK_SECRET"]="Stripe test webhook secret for webhook verification"
        ["SNYK_TOKEN"]="Snyk security scanning token"
        ["SERVER_SSH_KEY"]="SSH private key for server deployment"
    )

    echo -e "${GREEN}🔐 Backend GitHub Secrets Setup 🔐${NC}"
    echo -e "${YELLOW}Please have your secret values ready. Each secret will be confirmed twice.${NC}"
    echo

    # Interactively set secrets
    for secret in "${!secret_descriptions[@]}"; do
        echo -e "${YELLOW}Secret: $secret${NC}"
        echo -e "Description: ${secret_descriptions[$secret]}"
        set_secret "$secret"
        echo
    done

    echo -e "${GREEN}✨ All backend secrets have been set up successfully! ✨${NC}"
}

# Run the main function
main 