#!/bin/bash

# RENTAHUB Deployment Script
# Version: 1.1.0
# Supports: Railway

set -e  # Exit immediately if a command exits with a non-zero status

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Validate environment variables
validate_env_vars() {
    local required_vars=(
        "DATABASE_URL"
        "JWT_SECRET"
        "STRIPE_SECRET_KEY"
        "GOOGLE_CLIENT_ID"
        "GOOGLE_CLIENT_SECRET"
        "EMAIL_SERVICE_API_KEY"
        "FRONTEND_URL"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "Missing required environment variable: $var"
        fi
    done
}

# Pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check Node.js version
    if ! node --version | grep -q "v18\|v20"; then
        log_error "Node.js version must be 18.x or 20.x"
    fi

    # Check npm/yarn
    if command -v npm >/dev/null; then
        npm install -g npm@latest
    elif command -v yarn >/dev/null; then
        yarn set version latest
    else
        log_error "Neither npm nor yarn found. Please install a package manager."
    fi
}

# Database migrations
run_migrations() {
    log_info "Running database migrations..."
    npx prisma migrate deploy || log_error "Database migration failed"
}

# Build frontend
build_frontend() {
    log_info "Building frontend..."
    cd frontend
    npm install || yarn install
    npm run build || yarn build
    cd ..
}

# Build backend
build_backend() {
    log_info "Building backend..."
    cd backend
    npm install || yarn install
    npm run build || yarn build
    cd ..
}

# Run tests
run_tests() {
    log_info "Running tests..."
    npm run test || yarn test
}

# Deployment to specific platforms
deploy_railway() {
    log_info "Deploying to Railway..."
    # Check if railway CLI is installed
    if ! command -v railway &> /dev/null; then
        log_error "Railway CLI not found. Please install it with: npm i -g @railway/cli"
    fi
    
    # Check if logged in
    if ! railway whoami &> /dev/null; then
        log_error "Not logged in to Railway. Please run 'railway login' first."
    fi
    
    # Deploy backend
    cd backend
    log_info "Deploying backend to Railway..."
    railway deploy || log_error "Railway backend deployment failed"
    cd ..
    
    # Deploy frontend
    cd frontend
    log_info "Deploying frontend to Railway..."
    railway deploy || log_error "Railway frontend deployment failed"
    cd ..
    
    # Deploy admin
    cd admin
    log_info "Deploying admin dashboard to Railway..."
    railway deploy || log_error "Railway admin deployment failed"
    cd ..
}

# Main deployment function
main() {
    log_info "Starting RENTAHUB deployment process..."

    # Validate environment
    validate_env_vars

    # Pre-deployment checks
    pre_deployment_checks

    # Run migrations
    run_migrations

    # Build projects
    build_frontend
    build_backend

    # Run tests
    run_tests

    # Deploy based on environment
    case "$DEPLOY_PLATFORM" in
        "railway")
            deploy_railway
            ;;
        *)
            log_error "Unsupported deployment platform: $DEPLOY_PLATFORM. Only 'railway' is supported."
            ;;
    esac

    log_info "Deployment completed successfully! 🚀"
}

# Execute main deployment function
main 