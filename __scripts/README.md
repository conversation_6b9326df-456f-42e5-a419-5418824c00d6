# GitHub Secrets Setup

## Prerequisites

1. Install GitHub CLI
   ```bash
   brew install gh  # For macOS
   # Or use appropriate package manager for your OS
   ```

2. Authenticate with GitHub CLI
   ```bash
   gh auth login
   ```

## Setting Up Secrets

Run the `setup-github-secrets.sh` script to interactively set up GitHub secrets:

```bash
chmod +x setup-github-secrets.sh
./setup-github-secrets.sh
```

### Secrets to Prepare

You'll need to have the following information ready:

- `DB_URI`: Database connection string
- `COOKIE_SECRET`: Secret for cookie encryption
- `JWT_SECRET`: Secret for JWT token generation
- `SMTP_HOST`: SMTP server hostname
- `SMTP_PORT`: SMTP server port
- `SMTP_USER`: SMTP username
- `SMTP_PASS`: SMTP password
- `SMTP_FROM`: Email address to send from
- `EXPO_ACCESS_TOKEN`: Expo access token (if using Expo)
- `STRIPE_SECRET_KEY`: Stripe secret key
- `PAYPAL_CLIENT_ID`: PayPal client ID
- `PAYPAL_CLIENT_SECRET`: PayPal client secret
- `ADMIN_EMAIL`: Administrator email address
- `CODECOV_TOKEN`: Codecov reporting token

## Troubleshooting

- Ensure you have the latest version of GitHub CLI
- Verify you have the necessary permissions to set secrets in the repository
- Check that you're authenticated with the correct GitHub account 