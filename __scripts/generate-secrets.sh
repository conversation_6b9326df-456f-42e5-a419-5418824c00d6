#!/bin/bash

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to generate a secure random string
generate_secret() {
    local length=${1:-32}
    # Use openssl to generate a cryptographically secure random hex string
    openssl rand -hex "$length"
}

# Function to generate a secure database URI
generate_db_uri() {
    local username=$(openssl rand -hex 8)
    local password=$(generate_secret 16)
    local host="localhost"
    local port="5432"
    local dbname="rentahub_$(openssl rand -hex 4)"

    echo "postgresql://${username}:${password}@${host}:${port}/${dbname}"
}

# Function to generate SMTP details (mock for demonstration)
generate_smtp_details() {
    echo "smtp.gmail.com"  # SMTP Host
    echo "587"             # SMTP Port
    echo "<EMAIL>"  # SMTP User
    generate_secret 16     # SMTP Pass
    echo "<EMAIL>"  # SMTP From
}

# Main script
main() {
    echo -e "${GREEN}🔐 Secret Generation Utility for RENTAHUB 🔐${NC}"
    echo

    # Generate and display secrets
    echo -e "${YELLOW}1. Database URI:${NC}"
    DB_URI=$(generate_db_uri)
    echo "$DB_URI"
    echo

    echo -e "${YELLOW}2. Cookie Secret:${NC}"
    COOKIE_SECRET=$(generate_secret)
    echo "$COOKIE_SECRET"
    echo

    echo -e "${YELLOW}3. JWT Secret:${NC}"
    JWT_SECRET=$(generate_secret 64)
    echo "$JWT_SECRET"
    echo

    echo -e "${YELLOW}4. SMTP Details:${NC}"
    SMTP_HOST=$(generate_smtp_details | sed -n '1p')
    SMTP_PORT=$(generate_smtp_details | sed -n '2p')
    SMTP_USER=$(generate_smtp_details | sed -n '3p')
    SMTP_PASS=$(generate_smtp_details | sed -n '4p')
    SMTP_FROM=$(generate_smtp_details | sed -n '5p')
    
    echo "Host: $SMTP_HOST"
    echo "Port: $SMTP_PORT"
    echo "User: $SMTP_USER"
    echo "Pass: $SMTP_PASS"
    echo "From: $SMTP_FROM"
    echo

    echo -e "${YELLOW}5. Third-Party Service Placeholders:${NC}"
    echo "EXPO_ACCESS_TOKEN: $(generate_secret)"
    echo "STRIPE_SECRET_KEY: sk_test_$(generate_secret 24)"
    echo "STRIPE_PUBLISHABLE_KEY: pk_test_$(generate_secret 24)"
    echo "STRIPE_TEST_WEBHOOK_SECRET: $(generate_secret 32)"
    echo "PAYPAL_CLIENT_ID: $(generate_secret 20)"
    echo "PAYPAL_CLIENT_SECRET: $(generate_secret 32)"
    echo "SNYK_TOKEN: $(generate_secret 32)"
    echo

    echo -e "${YELLOW}6. Admin and Monitoring:${NC}"
    echo "ADMIN_EMAIL: <EMAIL>"
    echo "CODECOV_TOKEN: $(generate_secret 32)"
    echo "SLACK_WEBHOOK: $(generate_secret 32)"
    echo "SERVER_SSH_KEY: $(generate_secret 64)"
    echo

    echo -e "${GREEN}✨ Secret Generation Complete! ✨${NC}"
    echo -e "${RED}IMPORTANT: These are SAMPLE/GENERATED secrets. Replace with your actual service credentials.${NC}"
}

# Run the main function
main 