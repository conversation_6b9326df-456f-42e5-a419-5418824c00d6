#!/bin/bash

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to validate input
validate_input() {
    local input=$1
    if [ -z "$input" ]; then
        echo -e "${RED}Error: Input cannot be empty.${NC}"
        return 1
    fi
    return 0
}

# Function to set a secret with validation
set_secret() {
    local secret_name=$1
    local default_value=$2
    local attempts=0
    local secret_value=""

    while [ $attempts -lt 3 ]; do
        echo -e "${YELLOW}Enter value for $secret_name:${NC}"
        if [ -n "$default_value" ]; then
            echo -e "${GREEN}Suggested value (press Enter to use):${NC}"
            echo -e "${YELLOW}$default_value${NC}"
        fi
        read -sp "> " secret_value
        echo

        # Use default if no input provided
        if [ -z "$secret_value" ] && [ -n "$default_value" ]; then
            secret_value="$default_value"
        fi

        validate_input "$secret_value"
        if [ $? -eq 0 ]; then
            # Confirm the secret
            echo -e "${YELLOW}Confirm the secret for $secret_name (type it again):${NC}"
            read -sp "> " confirm_value
            echo

            if [ "$secret_value" = "$confirm_value" ]; then
                # Set the secret
                gh secret set "$secret_name" --body "$secret_value"
                echo -e "${GREEN}✓ Secret $secret_name set successfully!${NC}"
                return 0
            else
                echo -e "${RED}Error: Secrets do not match. Please try again.${NC}"
            fi
        fi

        ((attempts++))
    done

    echo -e "${RED}❌ Failed to set secret $secret_name after 3 attempts.${NC}"
    return 1
}

# Main script
main() {
    # Check if GitHub CLI is installed
    if ! command -v gh &> /dev/null; then
        echo -e "${RED}GitHub CLI (gh) is not installed. Please install it first.${NC}"
        exit 1
    fi

    # Suggest running generate-secrets.sh
    echo -e "${YELLOW}💡 Tip: Run __scripts/generate-secrets.sh to get suggested secret values.${NC}"
    echo

    # Ensure we're in the correct repository context
    gh repo set-default 2020visiontrader/RENTAHUB

    # List of secrets to set up with descriptions and optional default generation
    declare -A secret_descriptions=(
        ["DB_URI"]="Full database connection string (e.g., postgresql://user:pass@host:port/dbname)"
        ["COOKIE_SECRET"]="Random long string for cookie encryption"
        ["JWT_SECRET"]="Random long string for JWT token generation"
        ["SMTP_HOST"]="SMTP server hostname (e.g., smtp.gmail.com)"
        ["SMTP_PORT"]="SMTP server port (e.g., 587)"
        ["SMTP_USER"]="SMTP username/email"
        ["SMTP_PASS"]="SMTP password or app-specific password"
        ["SMTP_FROM"]="Email address to send emails from"
        ["EXPO_ACCESS_TOKEN"]="Expo access token (if using Expo)"
        ["STRIPE_SECRET_KEY"]="Stripe secret key for payment processing"
        ["PAYPAL_CLIENT_ID"]="PayPal client ID"
        ["PAYPAL_CLIENT_SECRET"]="PayPal client secret"
        ["ADMIN_EMAIL"]="Primary administrator email address"
        ["CODECOV_TOKEN"]="Codecov reporting token for code coverage"
    )

    echo -e "${GREEN}🔐 GitHub Secrets Setup for RENTAHUB 🔐${NC}"
    echo -e "${YELLOW}Please have your secret values ready. Each secret will be confirmed twice.${NC}"
    echo

    # Interactively set secrets
    for secret in "${!secret_descriptions[@]}"; do
        echo -e "${YELLOW}Secret: $secret${NC}"
        echo -e "Description: ${secret_descriptions[$secret]}"
        
        # Optional: Add logic to generate or suggest default values
        case "$secret" in
            "DB_URI")
                default_value=$(__scripts/generate-secrets.sh | grep "Database URI:" | cut -d: -f2 | xargs)
                ;;
            "COOKIE_SECRET"|"JWT_SECRET")
                default_value=$(__scripts/generate-secrets.sh | grep "$secret:" | cut -d: -f2 | xargs)
                ;;
            "SMTP_HOST")
                default_value="smtp.gmail.com"
                ;;
            "SMTP_PORT")
                default_value="587"
                ;;
            "ADMIN_EMAIL")
                default_value="<EMAIL>"
                ;;
            *)
                default_value=""
                ;;
        esac

        set_secret "$secret" "$default_value"
        echo
    done

    echo -e "${GREEN}✨ All secrets have been set up successfully! ✨${NC}"
}

# Run the main function
main 