# RENTAHUB 🚗 Car Rental Platform

## 🌐 Overview
RENTAHUB is a modern, scalable car rental platform built with cutting-edge technologies.

## 🚀 Deployment Platforms
- Railway

## 📋 Prerequisites
- Node.js (v18.x or v20.x)
- npm or yarn
- PostgreSQL database
- Stripe account
- Google OAuth credentials

## 🔧 Environment Setup

### Required Environment Variables
Create a `.env` file with the following variables:

```bash
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Authentication
JWT_SECRET=your_jwt_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payment
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Email Service
EMAIL_SERVICE_API_KEY=your_email_service_api_key

# Frontend URL
FRONTEND_URL=https://your-frontend-url.com
```

## 🛠 Local Development

### Backend Setup
```bash
cd backend
npm install
npx prisma generate
npx prisma migrate dev
npm run dev
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## 🌈 Deployment Options

### 1. Render Deployment
```bash
# Set deployment platform
export DEPLOY_PLATFORM=render

# Run deployment script
bash __scripts/deploy.sh
```

### 2. Supabase Deployment
```bash
# Set deployment platform
export DEPLOY_PLATFORM=supabase

# Run deployment script
bash __scripts/deploy.sh
```

### 3. Railway Deployment
```bash
# Set deployment platform
export DEPLOY_PLATFORM=railway

# Run deployment script
bash __scripts/deploy.sh
```

### 4. Railway Environment Setup
```bash
# Run the Railway environment setup script
bash __scripts/setup-railway-env.sh
```

This script will help you configure all the necessary environment variables for your Railway services.

## 🔒 Security Considerations
- Always use strong, unique secrets
- Enable two-factor authentication
- Regularly update dependencies
- Use environment-specific configuration

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License
[Your License Here]

## 🛟 Support
For issues or questions, please open a GitHub issue <NAME_EMAIL>