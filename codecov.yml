coverage:
  status:
    project:
      default:
        # Basic configuration
        target: auto
        threshold: 1%  # Allow 1% fluctuation
        base: auto
        flags:
          - unit
          - integration
    
    patch:
      default:
        target: 80%  # Require 80% coverage for changes
        base: auto
        branches:
          - main

comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: true

flags:
  unit:
    paths:
      - src/
    carryforward: true
  
  integration:
    paths:
      - __tests__/
    carryforward: true

ignore:
  - "**/*.config.js"
  - "scripts/**"
  - "__tests__/**"
  - "src/utils/testSupabaseConnection.ts"

fixes:
  - "::src/" 