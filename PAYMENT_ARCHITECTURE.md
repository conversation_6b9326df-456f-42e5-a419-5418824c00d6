# RentaHub Payment Architecture

## Overview
RentaHub supports multiple payment flows with a robust, flexible architecture that handles various payment methods, commission tracking, and provider payouts.

## Payment Methods
1. **Stripe Card Payments**
   - Instant payment at booking
   - Full card payment processing
   - Secure PaymentIntent creation
   - Automatic booking confirmation

2. **Cash-on-Pickup**
   - Provider manually confirms payment
   - No upfront card charge
   - Platform tracks payment status
   - Requires provider confirmation

3. **Bank Transfer Payouts**
   - Supports manual and Stripe Connect transfers
   - 3-day payout buffer
   - Detailed earnings tracking

## Commission Model
- **Rate**: 15% per booking
- Calculated on total booking price
- Deducted before provider payout
- Tracked in `payments` table

### Commission Calculation
```typescript
const baseAmount = totalPrice;
const commission = baseAmount * 0.15;
const providerEarnings = baseAmount - commission;
```

## Database Schema
### Payments Table
- `id`: Unique payment identifier
- `bookingId`: Associated booking
- `userId`: Customer ID
- `providerId`: Provider ID
- `paymentMethod`: Stripe/Cash/Bank Transfer
- `amount`: Total booking price
- `commission`: Platform's share
- `providerEarnings`: Amount due to provider
- `stripeChargeId`: Optional Stripe transaction ID
- `payoutStatus`: Pending/Ready/Paid
- `payoutDate`: Timestamp of payout

### Provider Bank Details Table
- `providerId`: Unique provider identifier
- `bankName`: Bank institution
- `accountNumber`: Bank account number
- `accountHolderName`: Account owner
- `branchCode`: Optional branch identifier
- `isVerified`: Bank details verification status

## Payment Flow
### Stripe Card Payment
1. Create PaymentIntent
2. Collect card details
3. Confirm payment
4. Create payment record
5. Mark booking as confirmed

### Cash Payment
1. Create booking
2. Provider confirms cash receipt
3. Create payment record
4. Mark booking as confirmed

## Payout Process
- 3-day buffer after pickup
- Automatic payout processing
- Supports Stripe Connect
- Manual transfer logging
- Detailed earnings dashboard

## Security Considerations
- Bank-grade encryption
- Secure Stripe integration
- Role-based access control
- Comprehensive error tracking
- Monitoring and alerting

## Configuration
Managed via `.env` configuration:
- Stripe keys
- Commission rate
- Payout buffer days
- Monitoring integrations

## Error Handling
- Comprehensive error logging
- Sentry integration
- Slack alerts for critical issues
- Graceful failure modes

## Future Roadmap
- Multi-currency support
- Advanced fraud detection
- International bank transfer support
- Real-time payout notifications

## Best Practices
- Always use environment variables
- Implement strong type checking
- Maintain comprehensive logs
- Regular security audits
- Continuous monitoring

## Troubleshooting
1. Verify Stripe webhook configuration
2. Check environment variables
3. Monitor Sentry for error tracking
4. Review payment logs
5. Validate bank detail completeness 