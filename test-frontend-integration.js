#!/usr/bin/env node

// =============================================================================
// FRONTEND-BACKEND INTEGRATION TEST
// =============================================================================
// Test script to verify frontend can communicate with production backend

const http = require('http');

const BACKEND_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:3000';

async function testEndpoint(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonBody
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testIntegration() {
  console.log('🔗 Testing Frontend-Backend Integration');
  console.log('======================================');

  const tests = [
    {
      name: 'Backend Health Check',
      url: `${BACKEND_URL}/health`,
      expectedStatus: 200
    },
    {
      name: 'API Status',
      url: `${BACKEND_URL}/api`,
      expectedStatus: 200
    },
    {
      name: 'Vehicle Search API',
      url: `${BACKEND_URL}/api/vehicles`,
      expectedStatus: 200
    },
    {
      name: 'Vehicle Categories API',
      url: `${BACKEND_URL}/api/vehicles/categories`,
      expectedStatus: 200
    },
    {
      name: 'Price Ranges API',
      url: `${BACKEND_URL}/api/vehicles/price-ranges`,
      expectedStatus: 200
    },
    {
      name: 'CORS Headers Check',
      url: `${BACKEND_URL}/api/vehicles`,
      expectedStatus: 200,
      checkCors: true
    }
  ];

  let passed = 0;
  let failed = 0;

  console.log('\n🔍 Testing Backend Endpoints...');
  console.log('--------------------------------');

  for (const test of tests) {
    try {
      console.log(`\n📡 Testing: ${test.name}`);
      console.log(`   URL: ${test.url}`);
      
      const result = await testEndpoint(test.url);
      
      if (result.status === test.expectedStatus) {
        console.log(`   ✅ PASS - Status: ${result.status}`);
        
        if (test.checkCors) {
          const corsHeaders = {
            'access-control-allow-origin': result.headers['access-control-allow-origin'],
            'access-control-allow-methods': result.headers['access-control-allow-methods'],
            'access-control-allow-headers': result.headers['access-control-allow-headers']
          };
          console.log(`   🌐 CORS Headers:`, corsHeaders);
        }
        
        if (result.data && typeof result.data === 'object') {
          if (result.data.success !== undefined) {
            console.log(`   📊 API Success: ${result.data.success}`);
          }
          if (result.data.data && Array.isArray(result.data.data)) {
            console.log(`   📊 Data Count: ${result.data.data.length}`);
          }
        }
        passed++;
      } else {
        console.log(`   ❌ FAIL - Expected: ${test.expectedStatus}, Got: ${result.status}`);
        console.log(`   📊 Response: ${JSON.stringify(result.data).substring(0, 100)}...`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ ERROR - ${error.message}`);
      failed++;
    }
  }

  // Test frontend availability
  console.log('\n🌐 Testing Frontend Availability...');
  console.log('-----------------------------------');

  try {
    console.log(`\n📡 Testing: Frontend Server`);
    console.log(`   URL: ${FRONTEND_URL}`);
    
    const frontendResult = await testEndpoint(FRONTEND_URL);
    
    if (frontendResult.status === 200) {
      console.log(`   ✅ PASS - Frontend is running`);
      passed++;
    } else {
      console.log(`   ⚠️ Frontend not running on ${FRONTEND_URL}`);
      console.log(`   💡 Start frontend with: cd frontend && npm run dev`);
      failed++;
    }
  } catch (error) {
    console.log(`   ⚠️ Frontend not running - ${error.message}`);
    console.log(`   💡 Start frontend with: cd frontend && npm run dev`);
    failed++;
  }

  // Summary
  console.log('\n📊 Integration Test Results');
  console.log('============================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All integration tests passed!');
    console.log('✅ Backend is running and responding correctly');
    console.log('✅ API endpoints are working');
    console.log('✅ CORS is configured properly');
    console.log('✅ Frontend can communicate with backend');
  } else {
    console.log('\n⚠️ Some integration tests failed.');
    console.log('\n💡 Troubleshooting:');
    console.log('   1. Make sure backend is running: cd backend && npm run dev:production');
    console.log('   2. Make sure frontend is running: cd frontend && npm run dev');
    console.log('   3. Check environment variables in .env files');
    console.log('   4. Verify API URLs match between frontend and backend');
  }

  // Configuration check
  console.log('\n⚙️ Configuration Check');
  console.log('======================');
  console.log('Backend URL:', BACKEND_URL);
  console.log('Frontend URL:', FRONTEND_URL);
  console.log('Expected API Base:', `${BACKEND_URL}/api`);
  
  console.log('\n📝 Frontend Environment Variables to Check:');
  console.log('   VITE_API_URL should be:', `${BACKEND_URL}/api`);
  console.log('   VITE_SUPABASE_URL should be set');
  console.log('   VITE_STRIPE_PUBLISHABLE_KEY should be set (for payments)');
  
  console.log('\n📝 Backend Environment Variables to Check:');
  console.log('   SUPABASE_URL should be set');
  console.log('   SUPABASE_SERVICE_ROLE_KEY should be set');
  console.log('   JWT_SECRET should be set');
  console.log('   STRIPE_SECRET_KEY should be set (for payments)');
}

// Run the integration test
if (require.main === module) {
  console.log('🚀 Starting Frontend-Backend Integration Test...\n');
  
  testIntegration()
    .then(() => {
      console.log('\n✅ Integration test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Integration test failed:', error);
      process.exit(1);
    });
}
