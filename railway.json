{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run build:all", "watchPatterns": ["backend/**", "frontend/**"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "environments": {"production": {"variables": {"NODE_ENV": "production", "PORT": "${{ PORT }}", "RAILWAY_ENVIRONMENT": "production"}, "plugins": [{"name": "postgresql", "config": {"version": "15"}}, {"name": "redis", "config": {"version": "7"}}]}, "staging": {"variables": {"NODE_ENV": "staging", "PORT": "${{ PORT }}", "RAILWAY_ENVIRONMENT": "staging"}, "plugins": [{"name": "postgresql", "config": {"version": "15"}}]}}, "services": {"backend": {"source": "./backend", "build": {"buildCommand": "npm ci && npm run build", "watchPatterns": ["**/*.ts", "**/*.js", "package*.json"]}, "deploy": {"startCommand": "npm start", "healthcheckPath": "/health", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE"}, "variables": {"NODE_ENV": "${{ RAILWAY_ENVIRONMENT }}", "PORT": "${{ PORT }}"}}, "frontend": {"source": "./frontend", "build": {"buildCommand": "npm ci && npm run build", "watchPatterns": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "package*.json"]}, "deploy": {"startCommand": "npm run preview", "healthcheckPath": "/", "healthcheckTimeout": 300}, "variables": {"NODE_ENV": "${{ RAILWAY_ENVIRONMENT }}", "VITE_API_URL": "${{ BACKEND_URL }}"}}}, "regions": ["us-west1"]}