#!/usr/bin/env node

/**
 * Test frontend Supabase connection with corrected URL
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Use the corrected Supabase URL
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

console.log('🔍 Testing Frontend Supabase Connection (Corrected URL)');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('\n🔗 Testing basic connection...');
    
    // Test basic connection by trying to get auth settings
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Basic auth connection successful');
    
    // Test a simple query that doesn't require authentication
    console.log('🗄️ Testing public access...');
    
    try {
      // Try to access auth settings (public endpoint)
      const response = await fetch(`${supabaseUrl}/auth/v1/settings`, {
        headers: {
          'apikey': supabaseAnonKey,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const settings = await response.json();
        console.log('✅ Auth settings accessible');
        console.log('   External providers:', Object.keys(settings.external || {}));
      } else {
        console.log('⚠️ Auth settings not accessible (this might be expected)');
      }
    } catch (fetchError) {
      console.log('⚠️ Fetch test failed:', fetchError.message);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

testConnection().then(success => {
  if (success) {
    console.log('\n🎉 Frontend Supabase connection test completed successfully!');
    console.log('✅ The corrected URL is working properly');
    console.log('🚀 Your frontend should now be able to connect to Supabase');
  } else {
    console.log('\n💥 Frontend Supabase connection test failed');
    process.exit(1);
  }
});
