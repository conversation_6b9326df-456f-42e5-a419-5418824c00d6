// Read data from the actual tables that exist with data
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Try with RLS bypass
const supabase = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  global: {
    headers: {
      'X-Client-Info': 'admin-panel'
    }
  }
});

async function readActualData() {
  console.log('📊 READING DATA FROM TABLES WITH ACTUAL RECORDS...\n');
  
  // Tables that have data according to your screenshot
  const tablesWithData = [
    { name: 'User', count: 6 },
    { name: 'Vehicle', count: 3 },
    { name: 'Booking', count: 2 },
    { name: 'listed_vehicles', count: 2 },
    { name: 'vehicle_catalog', count: 72 }
  ];
  
  for (const table of tablesWithData) {
    try {
      console.log(`🔍 Reading ${table.name} (expected ${table.count} records)...`);
      
      // Try different approaches to bypass RLS
      
      // Approach 1: Direct query
      const { data: directData, error: directError } = await supabase
        .from(table.name)
        .select('*')
        .limit(2);
      
      if (!directError && directData) {
        console.log(`   ✅ Direct access successful: ${directData.length} records`);
        if (directData.length > 0) {
          console.log(`   📋 Columns: ${Object.keys(directData[0]).join(', ')}`);
          console.log(`   📄 Sample:`, JSON.stringify(directData[0], null, 4));
        }
        console.log('');
        continue;
      }
      
      console.log(`   ❌ Direct access failed: ${directError?.message}`);
      
      // Approach 2: Try with RPC if available
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_table_data', { table_name: table.name, limit_count: 2 });
        
        if (!rpcError && rpcData) {
          console.log(`   ✅ RPC access successful: ${rpcData.length} records`);
          console.log(`   📄 Sample:`, JSON.stringify(rpcData[0], null, 4));
        } else {
          console.log(`   ❌ RPC access failed: ${rpcError?.message}`);
        }
      } catch (rpcErr) {
        console.log(`   ❌ RPC not available: ${rpcErr.message}`);
      }
      
      console.log('');
      
    } catch (error) {
      console.log(`   ❌ ${table.name}: ${error.message}`);
    }
  }
  
  // Also try to get some basic stats
  console.log('📈 TRYING TO GET TABLE COUNTS...\n');
  
  for (const table of tablesWithData) {
    try {
      const { count, error } = await supabase
        .from(table.name)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table.name} count: ${error.message}`);
      } else {
        console.log(`✅ ${table.name}: ${count} records (expected ${table.count})`);
      }
    } catch (e) {
      console.log(`❌ ${table.name} count: ${e.message}`);
    }
  }
  
  console.log('\n🏁 FINISHED READING ATTEMPT');
}

readActualData().catch(console.error);
