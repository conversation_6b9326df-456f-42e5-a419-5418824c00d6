// Execute admin policies creation directly via Supabase
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function createAdminPolicies() {
  console.log('🔧 CREATING ADMIN POLICIES FOR SERVICE ROLE ACCESS...\n');
  
  try {
    // Step 1: Create helper function
    console.log('📝 Creating helper function...');
    const helperFunction = `
      CREATE OR REPLACE FUNCTION is_service_role()
      RETURNS BOOLEAN AS $$
      BEGIN
        RETURN current_setting('request.jwt.claims', true)::json->>'role' = 'service_role';
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;
    
    const { error: funcError } = await supabase.rpc('exec', { sql: helperFunction });
    if (funcError) {
      console.log('⚠️ Helper function error (might already exist):', funcError.message);
    } else {
      console.log('✅ Helper function created');
    }

    // Step 2: Create policies for key tables
    const keyTables = [
      'User', 'Vehicle', 'Booking', 'vehicle_catalog', 'listed_vehicles', 
      'Payment', 'Review', 'Notification', 'support_tickets', 'Refund'
    ];
    
    console.log('\n📋 Creating policies for key tables...');
    
    for (const table of keyTables) {
      try {
        const policySQL = `
          CREATE POLICY "Service role can do everything on ${table}" ON "${table}"
          FOR ALL USING (is_service_role());
        `;
        
        const { error: policyError } = await supabase.rpc('exec', { sql: policySQL });
        if (policyError) {
          console.log(`⚠️ ${table} policy error:`, policyError.message);
        } else {
          console.log(`✅ ${table} policy created`);
        }
      } catch (e) {
        console.log(`❌ ${table} policy failed:`, e.message);
      }
    }

    // Step 3: Test access to verify policies work
    console.log('\n🧪 Testing access after policy creation...');
    
    const testTables = ['User', 'Vehicle', 'Booking', 'vehicle_catalog'];
    
    for (const table of testTables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table} test: ${error.message}`);
        } else {
          console.log(`✅ ${table} test: ${count} records accessible`);
        }
      } catch (e) {
        console.log(`❌ ${table} test failed:`, e.message);
      }
    }

  } catch (error) {
    console.error('❌ Policy creation failed:', error);
    
    // Fallback: Try to disable RLS temporarily
    console.log('\n🔄 Trying fallback approach - disabling RLS...');
    
    const keyTables = ['User', 'Vehicle', 'Booking', 'vehicle_catalog', 'listed_vehicles'];
    
    for (const table of keyTables) {
      try {
        const disableRLS = `ALTER TABLE "${table}" DISABLE ROW LEVEL SECURITY;`;
        const { error: rlsError } = await supabase.rpc('exec', { sql: disableRLS });
        
        if (rlsError) {
          console.log(`⚠️ ${table} RLS disable error:`, rlsError.message);
        } else {
          console.log(`✅ ${table} RLS disabled`);
        }
      } catch (e) {
        console.log(`❌ ${table} RLS disable failed:`, e.message);
      }
    }
  }
  
  console.log('\n🏁 ADMIN POLICY SETUP COMPLETE');
}

createAdminPolicies().catch(console.error);
