-- Create RLS policies to allow service role full access to all tables
-- Run this in your Supabase SQL Editor

-- Helper function to check if user is service role
CREATE OR REPLACE FUNCTION is_service_role()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN current_setting('request.jwt.claims', true)::json->>'role' = 'service_role';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create admin policies for all tables
-- These policies allow service role full access (SELECT, INSERT, UPDATE, DELETE)

-- User table policies
CREATE POLICY "Service role can do everything on User" ON "User"
  FOR ALL USING (is_service_role());

-- Vehicle table policies  
CREATE POLICY "Service role can do everything on Vehicle" ON "Vehicle"
  FOR ALL USING (is_service_role());

-- Booking table policies
CREATE POLICY "Service role can do everything on Booking" ON "Booking"
  FOR ALL USING (is_service_role());

-- vehicle_catalog table policies
CREATE POLICY "Service role can do everything on vehicle_catalog" ON "vehicle_catalog"
  FOR ALL USING (is_service_role());

-- listed_vehicles table policies
CREATE POLICY "Service role can do everything on listed_vehicles" ON "listed_vehicles"
  FOR ALL USING (is_service_role());

-- Payment table policies
CREATE POLICY "Service role can do everything on Payment" ON "Payment"
  FOR ALL USING (is_service_role());

-- Review table policies
CREATE POLICY "Service role can do everything on Review" ON "Review"
  FOR ALL USING (is_service_role());

-- Notification table policies
CREATE POLICY "Service role can do everything on Notification" ON "Notification"
  FOR ALL USING (is_service_role());

-- support_tickets table policies
CREATE POLICY "Service role can do everything on support_tickets" ON "support_tickets"
  FOR ALL USING (is_service_role());

-- Refund table policies
CREATE POLICY "Service role can do everything on Refund" ON "Refund"
  FOR ALL USING (is_service_role());

-- Report table policies
CREATE POLICY "Service role can do everything on Report" ON "Report"
  FOR ALL USING (is_service_role());

-- DamageAssessment table policies
CREATE POLICY "Service role can do everything on DamageAssessment" ON "DamageAssessment"
  FOR ALL USING (is_service_role());

-- DamageReport table policies
CREATE POLICY "Service role can do everything on DamageReport" ON "DamageReport"
  FOR ALL USING (is_service_role());

-- DepositTransaction table policies
CREATE POLICY "Service role can do everything on DepositTransaction" ON "DepositTransaction"
  FOR ALL USING (is_service_role());

-- DiscountCode table policies
CREATE POLICY "Service role can do everything on DiscountCode" ON "DiscountCode"
  FOR ALL USING (is_service_role());

-- Document table policies
CREATE POLICY "Service role can do everything on Document" ON "Document"
  FOR ALL USING (is_service_role());

-- GeospatialSearchLog table policies
CREATE POLICY "Service role can do everything on GeospatialSearchLog" ON "GeospatialSearchLog"
  FOR ALL USING (is_service_role());

-- InsuranceClaim table policies
CREATE POLICY "Service role can do everything on InsuranceClaim" ON "InsuranceClaim"
  FOR ALL USING (is_service_role());

-- InsurancePolicy table policies
CREATE POLICY "Service role can do everything on InsurancePolicy" ON "InsurancePolicy"
  FOR ALL USING (is_service_role());

-- Language table policies
CREATE POLICY "Service role can do everything on Language" ON "Language"
  FOR ALL USING (is_service_role());

-- Translation table policies
CREATE POLICY "Service role can do everything on Translation" ON "Translation"
  FOR ALL USING (is_service_role());

-- user_profiles table policies
CREATE POLICY "Service role can do everything on user_profiles" ON "user_profiles"
  FOR ALL USING (is_service_role());

-- vehicle_images table policies
CREATE POLICY "Service role can do everything on vehicle_images" ON "vehicle_images"
  FOR ALL USING (is_service_role());

-- api_keys table policies
CREATE POLICY "Service role can do everything on api_keys" ON "api_keys"
  FOR ALL USING (is_service_role());

-- payout_requests table policies
CREATE POLICY "Service role can do everything on payout_requests" ON "payout_requests"
  FOR ALL USING (is_service_role());

-- sos_alerts table policies
CREATE POLICY "Service role can do everything on sos_alerts" ON "sos_alerts"
  FOR ALL USING (is_service_role());

-- SystemAlert table policies
CREATE POLICY "Service role can do everything on SystemAlert" ON "SystemAlert"
  FOR ALL USING (is_service_role());

-- All other tables
CREATE POLICY "Service role can do everything on HistoricalRiskDataset" ON "HistoricalRiskDataset"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on InsuranceConfiguration" ON "InsuranceConfiguration"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on IntegrationLog" ON "IntegrationLog"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on OAuthState" ON "OAuthState"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on PricingConfig" ON "PricingConfig"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on RefundRule" ON "RefundRule"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on RefundTransaction" ON "RefundTransaction"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on RideChecklist" ON "RideChecklist"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on SavedVehicle" ON "SavedVehicle"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on ScheduledReport" ON "ScheduledReport"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on UserCommunicationPreferences" ON "UserCommunicationPreferences"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on vehicle_availability" ON "vehicle_availability"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on VehiclePerformanceMetric" ON "VehiclePerformanceMetric"
  FOR ALL USING (is_service_role());

CREATE POLICY "Service role can do everything on WebhookLog" ON "WebhookLog"
  FOR ALL USING (is_service_role());

-- Verify policies were created
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public' 
ORDER BY tablename, policyname;
