// Generate the service role key based on the anon key pattern
import jwt from 'jsonwebtoken';

// Your project details from the anon key
const payload = {
  "iss": "supabase",
  "ref": "rocxjzukyqelvuyltrfq", 
  "role": "service_role",  // Changed from "anon" to "service_role"
  "iat": 1751078471,
  "exp": 2066654471
};

// Note: We don't have the actual JWT secret, so this won't work
// But we can show what the service role key should look like

console.log('Expected service role key payload:');
console.log(JSON.stringify(payload, null, 2));
console.log('');
console.log('❌ Cannot generate the actual key without the JWT secret');
console.log('✅ You need to get it from Supabase Dashboard');
console.log('');
console.log('Go to: https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq/settings/api');
console.log('Copy the service_role key and provide it to me.');
console.log('');
console.log('It should look like:');
console.log('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.[DIFFERENT_SIGNATURE]');
console.log('');
console.log('Once you provide it, I will hardcode it and this issue will never happen again.');
