// Decode and verify the service role key
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

try {
  // Decode the JWT payload
  const parts = serviceKey.split('.');
  const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
  const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
  
  console.log('🔍 JWT Header:', JSON.stringify(header, null, 2));
  console.log('🔍 JWT Payload:', JSON.stringify(payload, null, 2));
  
  // Check if it's actually a service role
  if (payload.role === 'service_role') {
    console.log('✅ Key has service_role permission');
  } else {
    console.log('❌ Key does NOT have service_role permission, has:', payload.role);
  }
  
  // Check expiration
  const now = Date.now() / 1000;
  if (payload.exp > now) {
    console.log('✅ Key is not expired');
    console.log('⏰ Expires:', new Date(payload.exp * 1000).toISOString());
  } else {
    console.log('❌ Key is EXPIRED');
  }
  
  // Check project reference
  if (payload.ref === 'rocxjzukyqelvuyltrfq') {
    console.log('✅ Key is for correct project');
  } else {
    console.log('❌ Key is for wrong project:', payload.ref);
  }
  
} catch (error) {
  console.error('❌ Failed to decode service key:', error);
  console.log('🔍 Key format appears invalid');
}

console.log('\n📋 Key Analysis Complete');
console.log('If the key is valid but still getting permission errors,');
console.log('the issue might be with Supabase RLS policies or database setup.');
