// Create the required database tables for the admin system
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function createTables() {
  console.log('🏗️ Creating database tables...');
  
  try {
    // Create users table (extends auth.users)
    console.log('📊 Creating users table...');
    const usersSQL = `
      CREATE TABLE IF NOT EXISTS public.users (
        id UUID REFERENCES auth.users(id) PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        first_name TEXT,
        last_name TEXT,
        phone TEXT,
        role TEXT DEFAULT 'CUSTOMER',
        status TEXT DEFAULT 'ACTIVE',
        email_verified BOOLEAN DEFAULT FALSE,
        phone_verified BOOLEAN DEFAULT FALSE,
        is_provider BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: usersError } = await supabase.rpc('exec_sql', { sql: usersSQL });
    if (usersError) {
      console.error('❌ Users table error:', usersError);
    } else {
      console.log('✅ Users table created');
    }

    // Create vehicles table
    console.log('📊 Creating vehicles table...');
    const vehiclesSQL = `
      CREATE TABLE IF NOT EXISTS public.vehicles (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        provider_id UUID REFERENCES public.users(id),
        make TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER,
        category TEXT,
        license_plate TEXT,
        color TEXT,
        fuel_type TEXT,
        transmission TEXT,
        seats INTEGER,
        doors INTEGER,
        air_conditioning BOOLEAN DEFAULT FALSE,
        daily_rate DECIMAL(10,2),
        security_deposit DECIMAL(10,2),
        available_quantity INTEGER DEFAULT 1,
        total_quantity INTEGER DEFAULT 1,
        active BOOLEAN DEFAULT TRUE,
        description TEXT,
        features TEXT[],
        images TEXT[],
        location_city TEXT,
        location_country TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: vehiclesError } = await supabase.rpc('exec_sql', { sql: vehiclesSQL });
    if (vehiclesError) {
      console.error('❌ Vehicles table error:', vehiclesError);
    } else {
      console.log('✅ Vehicles table created');
    }

    // Create bookings table
    console.log('📊 Creating bookings table...');
    const bookingsSQL = `
      CREATE TABLE IF NOT EXISTS public.bookings (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id),
        vehicle_id UUID REFERENCES public.vehicles(id),
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        total_amount DECIMAL(10,2),
        status TEXT DEFAULT 'PENDING',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: bookingsError } = await supabase.rpc('exec_sql', { sql: bookingsSQL });
    if (bookingsError) {
      console.error('❌ Bookings table error:', bookingsError);
    } else {
      console.log('✅ Bookings table created');
    }

    // Create payments table
    console.log('📊 Creating payments table...');
    const paymentsSQL = `
      CREATE TABLE IF NOT EXISTS public.payments (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        booking_id UUID REFERENCES public.bookings(id),
        total_amount DECIMAL(10,2),
        status TEXT DEFAULT 'pending',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: paymentsError } = await supabase.rpc('exec_sql', { sql: paymentsSQL });
    if (paymentsError) {
      console.error('❌ Payments table error:', paymentsError);
    } else {
      console.log('✅ Payments table created');
    }

    // Create providers table (view of users where is_provider = true)
    console.log('📊 Creating providers view...');
    const providersSQL = `
      CREATE OR REPLACE VIEW public.providers AS
      SELECT * FROM public.users WHERE is_provider = TRUE;
    `;
    
    const { error: providersError } = await supabase.rpc('exec_sql', { sql: providersSQL });
    if (providersError) {
      console.error('❌ Providers view error:', providersError);
    } else {
      console.log('✅ Providers view created');
    }

    console.log('\n🎉 Database setup complete!');
    console.log('Now the admin dashboard should be able to access live data.');

  } catch (error) {
    console.error('❌ Database creation failed:', error);
    console.log('\n⚠️ If RPC exec_sql is not available, you need to run these SQL commands');
    console.log('manually in the Supabase SQL editor.');
  }
}

createTables();
