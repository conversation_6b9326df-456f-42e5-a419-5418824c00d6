// <PERSON>ript to create auto-confirmed admin using service role
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

// Create client with service role for admin operations
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAutoConfirmedAdmin() {
  try {
    console.log('Creating auto-confirmed admin account...');
    
    // Create user with admin client
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Password1234',
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        full_name: 'Super Admin',
        first_name: '<PERSON>',
        last_name: 'Admin',
        role: 'ADMIN'
      }
    });

    if (error) {
      console.error('Error creating admin:', error);
      
      // If user exists, try to update them to confirmed
      if (error.message.includes('already registered') || error.message.includes('already exists')) {
        console.log('User exists, attempting to confirm email...');
        
        // Try to find and update the user
        const { data: users, error: listError } = await supabase.auth.admin.listUsers();
        
        if (!listError && users) {
          const existingUser = users.users.find(u => u.email === '<EMAIL>');
          
          if (existingUser) {
            const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
              existingUser.id,
              { email_confirm: true }
            );
            
            if (updateError) {
              console.error('Error confirming existing user:', updateError);
            } else {
              console.log('✅ Existing user email confirmed!');
            }
          }
        }
      }
      return;
    }

    if (data.user) {
      console.log('✅ Auto-confirmed admin created successfully!');
      console.log('Email:', data.user.email);
      console.log('Email confirmed:', data.user.email_confirmed_at ? 'Yes' : 'No');
      
      // Also create user profile
      const { error: profileError } = await supabase
        .from('users')
        .upsert({
          id: data.user.id,
          email: '<EMAIL>',
          first_name: 'Super',
          last_name: 'Admin',
          role: 'ADMIN',
          status: 'ACTIVE',
          email_verified: true,
          phone_verified: false,
          is_provider: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.warn('Could not create user profile:', profileError);
      } else {
        console.log('✅ User profile created!');
      }
    }

    console.log('\n🎉 Ready to login!');
    console.log('Email: <EMAIL>');
    console.log('Password: Password1234');
    console.log('\nEmail confirmation is bypassed - you can login immediately!');

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

createAutoConfirmedAdmin();
