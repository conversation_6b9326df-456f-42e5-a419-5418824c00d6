// Test the service role key
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testServiceKey() {
  console.log('🔑 Testing service role key...');
  
  try {
    // Test 1: Try to read users table
    console.log('📊 Testing users table access...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Users table error:', usersError);
    } else {
      console.log('✅ Users table accessible:', users?.length || 0, 'records');
    }

    // Test 2: Try to read vehicles table
    console.log('📊 Testing vehicles table access...');
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('vehicles')
      .select('*')
      .limit(1);
    
    if (vehiclesError) {
      console.error('❌ Vehicles table error:', vehiclesError);
    } else {
      console.log('✅ Vehicles table accessible:', vehicles?.length || 0, 'records');
    }

    // Test 3: Try to read bookings table
    console.log('📊 Testing bookings table access...');
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('*')
      .limit(1);
    
    if (bookingsError) {
      console.error('❌ Bookings table error:', bookingsError);
    } else {
      console.log('✅ Bookings table accessible:', bookings?.length || 0, 'records');
    }

    // Test 4: List all tables
    console.log('📊 Testing table listing...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (tablesError) {
      console.error('❌ Tables listing error:', tablesError);
    } else {
      console.log('✅ Available tables:', tables?.map(t => t.table_name).join(', '));
    }

  } catch (error) {
    console.error('❌ Service key test failed:', error);
  }
}

testServiceKey();
