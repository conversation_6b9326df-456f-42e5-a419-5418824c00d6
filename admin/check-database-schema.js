// Check what's actually in the Supabase database
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function checkDatabase() {
  console.log('🔍 Checking Supabase database schema...');
  
  try {
    // Try different approaches to see what exists
    
    // Approach 1: Try auth.users (this should always exist)
    console.log('\n📊 Testing auth.users table...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Auth users error:', authError);
    } else {
      console.log('✅ Auth users accessible:', authUsers.users?.length || 0, 'users');
    }

    // Approach 2: Try to query pg_tables to see what exists
    console.log('\n📊 Checking available tables...');
    const { data: pgTables, error: pgError } = await supabase
      .rpc('get_schema_tables'); // This might not exist
    
    if (pgError) {
      console.log('⚠️ Custom RPC not available:', pgError.message);
    } else {
      console.log('✅ Tables found via RPC:', pgTables);
    }

    // Approach 3: Try some common table names that might exist
    const tablesToTest = [
      'profiles', 
      'user_profiles', 
      'public.users',
      'auth.users',
      'vehicle_catalog',
      'vehicles',
      'bookings',
      'payments',
      'providers'
    ];

    console.log('\n📊 Testing individual tables...');
    for (const tableName of tablesToTest) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ ${tableName}:`, error.message);
        } else {
          console.log(`✅ ${tableName}: accessible (${data?.length || 0} records)`);
        }
      } catch (e) {
        console.log(`❌ ${tableName}: exception -`, e.message);
      }
    }

    // Approach 4: Try to create a simple table to test permissions
    console.log('\n📊 Testing table creation permissions...');
    const { data: createResult, error: createError } = await supabase
      .from('test_admin_permissions')
      .select('*')
      .limit(1);
    
    if (createError) {
      console.log('⚠️ Cannot access test table (expected):', createError.message);
    } else {
      console.log('✅ Test table accessible');
    }

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

checkDatabase();
