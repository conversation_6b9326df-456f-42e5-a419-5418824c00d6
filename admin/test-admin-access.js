// Test admin dashboard access to real data
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  global: {
    headers: {
      'X-Client-Info': 'admin-panel',
      'Authorization': `Bearer ${serviceKey}`,
      'apikey': serviceKey
    }
  }
});

async function testAdminAccess() {
  console.log('🧪 TESTING ADMIN DASHBOARD ACCESS TO REAL DATA...\n');
  
  try {
    // Test Users
    console.log('👥 Testing Users access...');
    const { data: users, error: usersError } = await supabase
      .from('User')
      .select('*')
      .limit(3);
    
    if (usersError) {
      console.log('❌ Users error:', usersError.message);
    } else {
      console.log(`✅ Users: ${users.length} records`);
      console.log('   Sample user:', {
        id: users[0]?.id,
        email: users[0]?.email,
        name: users[0]?.name,
        role: users[0]?.role
      });
    }

    // Test Vehicles
    console.log('\n🚗 Testing Vehicles access...');
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('Vehicle')
      .select('*')
      .limit(3);
    
    if (vehiclesError) {
      console.log('❌ Vehicles error:', vehiclesError.message);
    } else {
      console.log(`✅ Vehicles: ${vehicles.length} records`);
      console.log('   Sample vehicle:', {
        id: vehicles[0]?.id,
        brand: vehicles[0]?.brand,
        model: vehicles[0]?.model,
        dailyRate: vehicles[0]?.dailyRate,
        location_city: vehicles[0]?.location_city
      });
    }

    // Test Bookings
    console.log('\n📅 Testing Bookings access...');
    const { data: bookings, error: bookingsError } = await supabase
      .from('Booking')
      .select('*')
      .limit(3);
    
    if (bookingsError) {
      console.log('❌ Bookings error:', bookingsError.message);
    } else {
      console.log(`✅ Bookings: ${bookings.length} records`);
      if (bookings.length > 0) {
        console.log('   Sample booking:', {
          id: bookings[0]?.id,
          // Add relevant booking fields here
        });
      }
    }

    // Test Vehicle Catalog
    console.log('\n📋 Testing Vehicle Catalog access...');
    const { data: catalog, error: catalogError } = await supabase
      .from('vehicle_catalog')
      .select('*')
      .limit(3);
    
    if (catalogError) {
      console.log('❌ Vehicle Catalog error:', catalogError.message);
    } else {
      console.log(`✅ Vehicle Catalog: ${catalog.length} records`);
      if (catalog.length > 0) {
        console.log('   Sample catalog item:', {
          id: catalog[0]?.id,
          // Add relevant catalog fields here
        });
      }
    }

    // Test dashboard stats calculation
    console.log('\n📊 Testing Dashboard Stats...');
    
    const { count: userCount } = await supabase
      .from('User')
      .select('*', { count: 'exact', head: true });
    
    const { count: vehicleCount } = await supabase
      .from('Vehicle')
      .select('*', { count: 'exact', head: true });
    
    const { count: bookingCount } = await supabase
      .from('Booking')
      .select('*', { count: 'exact', head: true });

    console.log('✅ Dashboard Stats:');
    console.log(`   Total Users: ${userCount}`);
    console.log(`   Total Vehicles: ${vehicleCount}`);
    console.log(`   Total Bookings: ${bookingCount}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  console.log('\n🏁 ADMIN ACCESS TEST COMPLETE');
  console.log('If all tests passed, the admin dashboard should now work with live data!');
}

testAdminAccess().catch(console.error);
