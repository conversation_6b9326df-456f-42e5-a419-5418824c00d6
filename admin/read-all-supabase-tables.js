// Connect to Supabase and read ALL actual tables
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function readAllTables() {
  console.log('🔍 CONNECTING TO SUPABASE AND READING ALL TABLES...\n');
  
  // From the screenshot, I can see these tables exist:
  const visibleTables = [
    'api_keys',
    'Booking', 
    'DamageAssessment',
    'DamageReport',
    'DepositTransaction', 
    'DiscountCode',
    'Document',
    'GeospatialSearchLog'
    // There are more tables below in the screenshot
  ];
  
  // Let's also try some common table names that might exist
  const possibleTables = [
    ...visibleTables,
    'User',
    'Users', 
    'users',
    'Vehicle',
    'Vehicles',
    'vehicles',
    'Payment',
    'Payments', 
    'payments',
    'Provider',
    'Providers',
    'providers',
    'Rental',
    'Rentals',
    'rentals',
    'VehicleCategory',
    'Location',
    'Review',
    'Reviews',
    'Notification',
    'Notifications',
    'Profile',
    'Profiles',
    'UserProfile',
    'VehicleImage',
    'BookingStatus',
    'PaymentMethod',
    'Insurance',
    'Maintenance',
    'Fleet',
    'Company',
    'Driver',
    'License',
    'Verification'
  ];
  
  console.log('📊 CHECKING EACH TABLE FOR EXISTENCE AND DATA...\n');
  
  const existingTables = [];
  
  for (const tableName of possibleTables) {
    try {
      console.log(`🔍 Checking table: ${tableName}`);
      
      // Try to get count and sample data
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        console.log(`   ❌ ${tableName}: ${countError.message}`);
        continue;
      }
      
      console.log(`   ✅ ${tableName}: ${count} records`);
      existingTables.push({ name: tableName, count });
      
      // Get sample data to see structure
      if (count > 0) {
        const { data: sample, error: sampleError } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (!sampleError && sample && sample[0]) {
          const columns = Object.keys(sample[0]);
          console.log(`   📋 Columns: ${columns.join(', ')}`);
          console.log(`   📄 Sample:`, JSON.stringify(sample[0], null, 4));
        }
      }
      
      console.log(''); // Empty line for readability
      
    } catch (error) {
      console.log(`   ❌ ${tableName}: ${error.message}`);
    }
  }
  
  console.log('\n🎯 SUMMARY OF EXISTING TABLES:');
  console.log('================================');
  
  if (existingTables.length === 0) {
    console.log('❌ NO TABLES FOUND - Database appears empty');
  } else {
    existingTables.forEach(table => {
      console.log(`✅ ${table.name}: ${table.count} records`);
    });
    
    console.log(`\n📊 Total tables found: ${existingTables.length}`);
    console.log(`📊 Total records across all tables: ${existingTables.reduce((sum, t) => sum + t.count, 0)}`);
  }
  
  // Also check auth users
  console.log('\n👥 CHECKING AUTH USERS...');
  try {
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    if (authError) {
      console.log('❌ Auth users error:', authError);
    } else {
      console.log(`✅ Auth users: ${authUsers.users.length} users`);
      authUsers.users.forEach(user => {
        console.log(`   - ${user.email} (ID: ${user.id})`);
      });
    }
  } catch (error) {
    console.log('❌ Auth check failed:', error);
  }
  
  console.log('\n🏁 COMPLETE DATABASE SCAN FINISHED');
}

readAllTables().catch(console.error);
