// <PERSON>ript to help get the service role key
console.log('🔑 To get your Service Role Key:');
console.log('');
console.log('1. Go to: https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq/settings/api');
console.log('2. Look for "Project API keys" section');
console.log('3. Copy the "service_role" key (NOT the anon key)');
console.log('4. It should start with: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
console.log('5. It should contain "service_role" in the decoded payload');
console.log('');
console.log('Your anon key for reference:');
console.log('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8');
console.log('');
console.log('The service_role key will be similar but longer and with different signature.');
console.log('');
console.log('Once you have it, I will hardcode it into the system so this never happens again.');

// Try to decode the anon key to show the pattern
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

try {
  const payload = JSON.parse(Buffer.from(anonKey.split('.')[1], 'base64').toString());
  console.log('');
  console.log('Decoded anon key payload:');
  console.log(JSON.stringify(payload, null, 2));
  console.log('');
  console.log('The service_role key will have the same structure but with "role": "service_role"');
} catch (e) {
  console.log('Could not decode key for reference');
}
