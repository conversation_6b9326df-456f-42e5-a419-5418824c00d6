// Script to confirm the superuser email
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function confirmSuperuser() {
  try {
    console.log('Confirming superuser email...');
    
    // First, let's try to sign in to get the user ID
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Password1234'
    });

    if (signInError && signInError.message !== 'Email not confirmed') {
      console.error('Unexpected error:', signInError);
      return;
    }

    console.log('User exists but email not confirmed. This is expected.');
    
    // The user needs to be confirmed via Supabase dashboard
    console.log('\n⚠️  Email confirmation required!');
    console.log('\nPlease follow these steps:');
    console.log('1. Go to: https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq');
    console.log('2. Navigate to: Authentication → Users');
    console.log('3. Find user: <EMAIL>');
    console.log('4. Click the three dots (...) next to the user');
    console.log('5. Select "Confirm email"');
    console.log('6. Try logging in again');
    
    console.log('\nAlternatively, I can create a new user with auto-confirmation...');

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

confirmSuperuser();
