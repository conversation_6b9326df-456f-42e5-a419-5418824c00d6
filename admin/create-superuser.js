// <PERSON>ript to create the superuser account
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createSuperuser() {
  try {
    console.log('Creating superuser account...');
    
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'Password1234',
      options: {
        data: {
          full_name: 'Super Admin',
          first_name: 'Super',
          last_name: 'Admin',
          role: 'ADMIN'
        }
      }
    });

    if (error) {
      console.error('Error creating superuser:', error);
      return;
    }

    console.log('Superuser created successfully:', data.user?.email);
    
    // Also create user profile in users table
    if (data.user) {
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          email: '<EMAIL>',
          first_name: 'Super',
          last_name: 'Admin',
          role: 'ADMIN',
          status: 'ACTIVE',
          email_verified: true,
          phone_verified: false,
          is_provider: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.warn('Could not create user profile:', profileError);
      } else {
        console.log('User profile created successfully');
      }
    }

    console.log('\n✅ Superuser account ready!');
    console.log('Email: <EMAIL>');
    console.log('Password: Password1234');
    console.log('\nYou can now login to the admin dashboard.');

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

createSuperuser();
