<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RentaHub Admin</title>

    <!-- Force cache refresh -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- Suppress development warnings -->
    <script>
      // Suppress development warnings and extension messages
      if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
        const originalError = console.error;
        const originalLog = console.log;

        console.error = function(...args) {
          const message = args.join(' ');
          if (message.includes('validateDOMNesting') ||
              message.includes('cannot appear as a descendant of')) {
            return; // Suppress DOM nesting warnings in development
          }
          originalError.apply(console, args);
        };

        console.log = function(...args) {
          const message = args.join(' ');
          if (message.includes('bybit:page provider inject code') ||
              message.includes('bybit') && message.includes('inject')) {
            return; // Suppress Bybit injection messages
          }
          originalLog.apply(console, args);
        };
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>