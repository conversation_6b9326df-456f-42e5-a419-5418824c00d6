# =============================================================================
# RENTAHUB ADMIN - RAILWAY CONFIGURATION
# =============================================================================
# Admin Panel: admin.rentahub.info

[build]
builder = "DOCKERFILE"

[deploy]
healthcheckDisabled = true
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[project]
name = "rentahub-admin"

# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================

[env]
NODE_ENV = "production"
VITE_NODE_ENV = "production"

# API Configuration
VITE_API_URL = "https://api.rentahub.info"

# Application URLs
VITE_APP_URL = "https://rentahub.info"
VITE_ADMIN_URL = "https://admin.rentahub.info"
VITE_FRONTEND_URL = "https://rentahub.info"

# Google OAuth
VITE_GOOGLE_CALLBACK_URL = "https://admin.rentahub.info/auth/google/callback"

# Admin Configuration
VITE_APP_NAME = "RentaHub Admin"
VITE_APP_DESCRIPTION = "RentaHub Administrative Dashboard"
VITE_APP_VERSION = "1.0.0"

# Contact Information
VITE_SUPPORT_EMAIL = "<EMAIL>"
VITE_ADMIN_EMAIL = "<EMAIL>"

# Feature Flags
VITE_ENABLE_GOOGLE_AUTH = "true"
VITE_ENABLE_ADVANCED_ANALYTICS = "true"
VITE_ENABLE_SYSTEM_MONITORING = "true"
VITE_ENABLE_USER_MANAGEMENT = "true"
VITE_ENABLE_PROVIDER_MANAGEMENT = "true"

# Security Configuration
VITE_ADMIN_ACCESS_LEVEL = "super_admin"
VITE_ENABLE_AUDIT_LOGGING = "true"

# SEO Configuration
VITE_SITE_NAME = "RentaHub Admin"
VITE_SITE_DESCRIPTION = "Administrative dashboard for RentaHub platform management"
VITE_SITE_URL = "https://admin.rentahub.info"