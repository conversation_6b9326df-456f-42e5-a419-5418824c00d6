{"name": "rentahub-admin", "description": "Admin panel for RentaHub - <PERSON>ooter, Motorbike & Dirt Bike Rental Platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:type-check": "tsc && vite build", "preview": "vite preview --host 0.0.0.0 --port 4173", "start:prod": "serve -s dist", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@chakra-ui/react": "^3.22.0", "@date-io/date-fns": "2.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^5.14.19", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.14.20", "@mui/system": "^5.14.20", "@mui/x-data-grid": "^6.18.4", "@mui/x-date-pickers": "^6.18.4", "@react-google-maps/api": "^2.20.7", "@stripe/stripe-js": "^7.5.0", "@supabase/supabase-js": "^2.45.4", "antd": "^5.26.4", "axios": "^1.7.7", "date-fns": "^2.29.3", "lucide-react": "^0.525.0", "notistack": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-router-dom": "^6.26.2", "react-toastify": "^10.0.5", "recharts": "^3.1.0", "stripe": "^18.3.0", "zod": "^3.23.8"}, "devDependencies": {"@types/google.maps": "^3.58.1", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "@vitejs/plugin-react": "^4.3.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.12", "serve": "^14.2.1", "typescript": "^5.6.2", "vite": "^5.4.8"}}