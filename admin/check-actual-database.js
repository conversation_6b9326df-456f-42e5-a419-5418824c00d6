// Connect to Supabase and check what's actually there
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function checkRealDatabase() {
  console.log('🔍 CONNECTING TO SUPABASE DATABASE RIGHT NOW...');
  console.log('URL:', supabaseUrl);
  console.log('Using service role key for full access\n');
  
  try {
    // Check auth users first
    console.log('👥 CHECKING AUTH USERS...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Auth error:', authError);
    } else {
      console.log(`✅ Found ${authUsers.users.length} auth users:`);
      authUsers.users.forEach(user => {
        console.log(`   - ${user.email} (${user.id}) - Created: ${user.created_at}`);
      });
    }

    // Try to get database info using SQL
    console.log('\n📊 CHECKING DATABASE SCHEMA...');
    
    // Use raw SQL to check what tables exist
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_schema')
      .eq('table_schema', 'public');
    
    if (tablesError) {
      console.log('⚠️ Cannot query information_schema:', tablesError.message);
      
      // Try alternative approach - check specific tables we know might exist
      console.log('\n🔍 TRYING DIRECT TABLE ACCESS...');
      
      const knownTables = [
        'users', 'profiles', 'vehicles', 'vehicle_catalog', 
        'bookings', 'payments', 'providers', 'rentals'
      ];
      
      for (const tableName of knownTables) {
        try {
          const { count, error } = await supabase
            .from(tableName)
            .select('*', { count: 'exact', head: true });
          
          if (error) {
            console.log(`❌ ${tableName}: ${error.message}`);
          } else {
            console.log(`✅ ${tableName}: ${count} records`);
            
            // If table exists, get a sample
            if (count > 0) {
              const { data: sample } = await supabase
                .from(tableName)
                .select('*')
                .limit(1);
              console.log(`   Sample:`, JSON.stringify(sample?.[0], null, 2));
            }
          }
        } catch (e) {
          console.log(`❌ ${tableName}: ${e.message}`);
        }
      }
    } else {
      console.log(`✅ Found ${tables.length} public tables:`);
      tables.forEach(table => {
        console.log(`   - ${table.table_name}`);
      });
      
      // Check each table for data
      console.log('\n📈 CHECKING TABLE DATA...');
      for (const table of tables) {
        try {
          const { count, error } = await supabase
            .from(table.table_name)
            .select('*', { count: 'exact', head: true });
          
          if (error) {
            console.log(`❌ ${table.table_name}: ${error.message}`);
          } else {
            console.log(`✅ ${table.table_name}: ${count} records`);
          }
        } catch (e) {
          console.log(`❌ ${table.table_name}: ${e.message}`);
        }
      }
    }

    // Check if RLS is enabled
    console.log('\n🔒 CHECKING ROW LEVEL SECURITY...');
    try {
      const { data: rlsInfo, error: rlsError } = await supabase
        .from('pg_class')
        .select('relname, relrowsecurity')
        .eq('relkind', 'r')
        .like('relname', '%user%');
      
      if (!rlsError && rlsInfo) {
        console.log('RLS Status:', rlsInfo);
      }
    } catch (e) {
      console.log('Cannot check RLS status');
    }

  } catch (error) {
    console.error('❌ CRITICAL ERROR:', error);
  }
  
  console.log('\n🏁 DATABASE CHECK COMPLETE');
}

checkRealDatabase();
