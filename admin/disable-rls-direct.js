// Disable R<PERSON> directly using HTTP requests

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

async function disableRLSDirectly() {
  console.log('🔧 DISABLING RLS FOR KEY TABLES DIRECTLY...\n');
  
  const keyTables = [
    'User', 'Vehicle', 'Booking', 'vehicle_catalog', 'listed_vehicles', 
    'Payment', 'Review', 'Notification', 'support_tickets', 'Refund'
  ];
  
  for (const table of keyTables) {
    try {
      console.log(`🔓 Disabling RLS for ${table}...`);
      
      const sql = `ALTER TABLE "${table}" DISABLE ROW LEVEL SECURITY;`;
      
      // Try using the PostgREST admin endpoint
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': serviceKey,
          'Authorization': `Bearer ${serviceKey}`,
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({ sql })
      });
      
      if (response.ok) {
        console.log(`✅ ${table} RLS disabled successfully`);
      } else {
        const error = await response.text();
        console.log(`⚠️ ${table} RLS disable failed:`, error);
      }
      
    } catch (error) {
      console.log(`❌ ${table} error:`, error.message);
    }
  }
  
  // Test access after disabling RLS
  console.log('\n🧪 Testing table access after RLS disable...');
  
  const { createClient } = await import('@supabase/supabase-js');
  const supabase = createClient(supabaseUrl, serviceKey);
  
  for (const table of ['User', 'Vehicle', 'Booking', 'vehicle_catalog']) {
    try {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: ${count} records accessible`);
        
        // Get sample data if accessible
        if (count > 0) {
          const { data, error: dataError } = await supabase
            .from(table)
            .select('*')
            .limit(1);
          
          if (!dataError && data && data[0]) {
            console.log(`   📋 Columns: ${Object.keys(data[0]).join(', ')}`);
          }
        }
      }
    } catch (e) {
      console.log(`❌ ${table} test failed:`, e.message);
    }
  }
  
  console.log('\n🏁 RLS DISABLE ATTEMPT COMPLETE');
}

disableRLSDirectly().catch(console.error);
