// Read data from the actual tables that exist
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, serviceKey);

async function readRealTables() {
  console.log('📊 READING DATA FROM YOUR ACTUAL TABLES...\n');
  
  // Tables that exist based on the API endpoints
  const actualTables = [
    'GeospatialSearchLog',
    'Vehicle', 
    'vehicle_catalog',
    'RefundRule',
    'support_tickets',
    'ScheduledReport', 
    'Translation',
    'Refund',
    'Report',
    'Booking',
    'DamageAssessment',
    'DamageReport',
    'DepositTransaction',
    'DiscountCode',
    'Document',
    'User',
    'Payment',
    'Provider',
    'Rental'
  ];
  
  for (const tableName of actualTables) {
    try {
      console.log(`🔍 Reading table: ${tableName}`);
      
      // Get count
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        console.log(`   ❌ ${tableName}: ${countError.message}`);
        continue;
      }
      
      console.log(`   ✅ ${tableName}: ${count} records`);
      
      // Get sample data
      if (count > 0) {
        const { data: sample, error: sampleError } = await supabase
          .from(tableName)
          .select('*')
          .limit(2);
        
        if (!sampleError && sample && sample.length > 0) {
          console.log(`   📋 Columns: ${Object.keys(sample[0]).join(', ')}`);
          console.log(`   📄 Sample record:`);
          console.log(JSON.stringify(sample[0], null, 4));
        }
      }
      
      console.log(''); // Empty line
      
    } catch (error) {
      console.log(`   ❌ ${tableName}: ${error.message}`);
    }
  }
  
  console.log('🏁 FINISHED READING ALL TABLES');
}

readRealTables().catch(console.error);
