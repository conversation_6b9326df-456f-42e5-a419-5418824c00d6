// Verify I'm connecting to the correct Supabase project
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

console.log('🔍 VERIFYING PROJECT CONNECTION...');
console.log('URL:', supabaseUrl);
console.log('Project ID from URL:', 'rocxjzukyqelvuyltrfq');

// Decode the service key to verify project
try {
  const payload = JSON.parse(Buffer.from(serviceKey.split('.')[1], 'base64').toString());
  console.log('Project ID from service key:', payload.ref);
  console.log('Role from service key:', payload.role);
  
  if (payload.ref === 'rocxjzukyqelvuyltrfq') {
    console.log('✅ Service key matches project ID');
  } else {
    console.log('❌ SERVICE KEY MISMATCH!');
    console.log('Expected: rocxjzukyqelvuyltrfq');
    console.log('Got:', payload.ref);
  }
} catch (e) {
  console.log('❌ Could not decode service key');
}

const supabase = createClient(supabaseUrl, serviceKey);

async function verifyConnection() {
  try {
    // Test basic connection
    console.log('\n🔗 Testing connection...');
    
    // Check auth users to verify we're connected
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.log('❌ Connection failed:', authError);
      return;
    }
    
    console.log(`✅ Connected successfully - Found ${authUsers.users.length} auth users`);
    
    // List the users to verify this is your project
    console.log('\n👥 Auth users in this project:');
    authUsers.users.forEach(user => {
      console.log(`   - ${user.email} (Created: ${user.created_at})`);
    });
    
    // Try to access the REST API directly to see what's available
    console.log('\n🌐 Testing REST API access...');
    
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': serviceKey,
        'Authorization': `Bearer ${serviceKey}`
      }
    });
    
    console.log('REST API Status:', response.status);
    
    if (response.status === 200) {
      const apiInfo = await response.text();
      console.log('API Response:', apiInfo.substring(0, 200) + '...');
    }
    
    // Try to get OpenAPI spec to see available tables
    console.log('\n📋 Checking available endpoints...');
    
    const specResponse = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': serviceKey,
        'Authorization': `Bearer ${serviceKey}`,
        'Accept': 'application/openapi+json'
      }
    });
    
    if (specResponse.ok) {
      const spec = await specResponse.json();
      if (spec.paths) {
        const endpoints = Object.keys(spec.paths);
        console.log('Available endpoints:', endpoints.slice(0, 10));
      }
    }
    
  } catch (error) {
    console.log('❌ Verification failed:', error);
  }
}

verifyConnection();
