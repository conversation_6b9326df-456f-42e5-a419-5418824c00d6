// User Types
export enum UserRole {
  ADMIN = 'admin',
  PROVIDER = 'provider',
  CUSTOMER = 'customer',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  DELETED = 'deleted',
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole | string;
  status: UserStatus | string;
  phoneNumber?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  avatar?: string;
  bio?: string;
  location?: string;
  createdAt: string;
  updatedAt?: string;
  lastLogin?: string;
  // Provider-specific fields
  isProvider?: boolean;
  providerStatus?: string;
  businessName?: string;
  businessAddress?: string;
  businessPhone?: string;
  taxId?: string;
  // Customer-specific fields
  dateOfBirth?: string;
  driverLicenseNumber?: string;
  driverLicenseExpiry?: string;
}

// Vehicle Types
export enum VehicleType {
  CAR = 'car',
  MOTORCYCLE = 'motorcycle',
  SCOOTER = 'scooter',
  BICYCLE = 'bicycle',
  VAN = 'van',
  TRUCK = 'truck',
  BUS = 'bus',
}

export enum VehicleStatus {
  AVAILABLE = 'available',
  BOOKED = 'booked',
  MAINTENANCE = 'maintenance',
  INACTIVE = 'inactive',
}

export enum FuelType {
  GASOLINE = 'gasoline',
  DIESEL = 'diesel',
  ELECTRIC = 'electric',
  HYBRID = 'hybrid',
  PLUGIN_HYBRID = 'plugInHybrid',
}

export enum GearboxType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
}

export interface Vehicle {
  id: string;
  providerId: string;
  provider?: User;
  make: string;
  model: string;
  year: number;
  type: VehicleType | string;
  category?: string;
  status: VehicleStatus | string;
  licensePlate: string;
  vin?: string;
  color?: string;
  fuelType: FuelType | string;
  gearbox: GearboxType | string;
  seats: number;
  doors: number;
  airConditioning: boolean;
  // Pricing
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  deposit: number;
  // Features
  features: string[];
  images: string[];
  description?: string;
  // Location
  locationId?: string;
  location?: Location;
  // Availability
  availableFrom?: string;
  availableTo?: string;
  minimumRentalDays: number;
  maximumRentalDays?: number;
  // Metrics
  totalBookings: number;
  rating?: number;
  reviewCount: number;
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

// Booking Types
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

export interface Booking {
  id: string;
  userId: string;
  user?: User;
  vehicleId: string;
  vehicle?: Vehicle;
  providerId: string;
  provider?: User;
  // Dates and times
  startDate: string;
  endDate: string;
  pickupTime?: string;
  returnTime?: string;
  // Location
  pickupLocationId?: string;
  pickupLocation?: Location;
  returnLocationId?: string;
  returnLocation?: Location;
  // Status and details
  status: BookingStatus | string;
  totalDays: number;
  // Pricing
  dailyRate: number;
  subtotal: number;
  taxes: number;
  fees: number;
  total: number;
  deposit: number;
  // Payment
  paymentStatus: string;
  paymentMethod?: string;
  paymentIntentId?: string;
  // Additional services
  additionalServices: string[];
  specialRequests?: string;
  // Timestamps
  createdAt: string;
  updatedAt?: string;
  confirmedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
}

// Location Types
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  latitude?: number;
  longitude?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt?: string;
}

// Payment Types
export interface Payment {
  id: string;
  bookingId: string;
  booking?: Booking;
  userId: string;
  user?: User;
  providerId: string;
  provider?: User;
  amount: number;
  currency: string;
  status: string;
  method: string;
  transactionId?: string;
  paymentIntentId?: string;
  refundId?: string;
  createdAt: string;
  updatedAt?: string;
}

// API Payload Types
export interface SignInPayload {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignUpPayload {
  email: string;
  password: string;
  name: string;
  role?: UserRole | string;
  phoneNumber?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface CreateUserPayload {
  email: string;
  name: string;
  role: UserRole | string;
  phoneNumber?: string;
  password?: string;
  isProvider?: boolean;
  businessName?: string;
}

export interface UpdateUserPayload {
  name?: string;
  phoneNumber?: string;
  status?: UserStatus | string;
  role?: UserRole | string;
  bio?: string;
  location?: string;
  businessName?: string;
  businessAddress?: string;
}

export interface CreateVehiclePayload {
  providerId: string;
  make: string;
  model: string;
  year: number;
  type: VehicleType | string;
  licensePlate: string;
  fuelType: FuelType | string;
  gearbox: GearboxType | string;
  seats: number;
  doors: number;
  airConditioning: boolean;
  dailyRate: number;
  deposit: number;
  features: string[];
  description?: string;
  locationId?: string;
  minimumRentalDays: number;
}

export interface UpdateVehiclePayload {
  make?: string;
  model?: string;
  year?: number;
  type?: VehicleType | string;
  status?: VehicleStatus | string;
  dailyRate?: number;
  deposit?: number;
  features?: string[];
  description?: string;
  airConditioning?: boolean;
  minimumRentalDays?: number;
}

export interface CreateBookingPayload {
  userId: string;
  vehicleId: string;
  startDate: string;
  endDate: string;
  pickupLocationId?: string;
  returnLocationId?: string;
  additionalServices?: string[];
  specialRequests?: string;
}

export interface UpdateBookingPayload {
  status?: BookingStatus | string;
  startDate?: string;
  endDate?: string;
  specialRequests?: string;
}

// Filter and Search Types
export interface FilterOptions {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

export interface VehicleFilters {
  type?: VehicleType[];
  fuelType?: FuelType[];
  gearbox?: GearboxType[];
  minPrice?: number;
  maxPrice?: number;
  seats?: number;
  features?: string[];
  location?: string;
  availability?: {
    startDate: string;
    endDate: string;
  };
}

export interface BookingFilters {
  status?: BookingStatus[];
  dateRange?: {
    start: string;
    end: string;
  };
  providerId?: string;
  userId?: string;
}

// Dashboard and Analytics Types
export interface DashboardStats {
  users: {
    total: number;
    recent: User[];
    growth: { total: number; period: string };
  };
  vehicles: {
    total: number;
    available: number;
    recent: Vehicle[];
  };
  bookings: {
    total: number;
    active: number;
    recent: Booking[];
  };
  financials: {
    totalRevenue: number;
    monthlyRevenue: number;
    pendingPayments: number;
  };
}

// Event Types
export interface DataEvent<T> {
  data?: T[];
  rowCount: number;
}

// Common Response Types
export interface Result<T> {
  pageInfo: Array<{ totalRecords: number }>;
  resultData: T[];
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}
