import { z } from 'zod'

export const VehicleFormSchema = z.object({
  // Basic Information
  make: z.string().min(1, 'Make is required'),
  model: z.string().min(1, 'Model is required'),
  year: z.number().min(2000, 'Year must be 2000 or later').max(new Date().getFullYear() + 1, 'Year cannot be in the future'),
  category: z.enum(['small_scooter', 'large_scooter', 'luxury_bike'], {
    required_error: 'Category is required',
  }),
  
  // Technical Specifications
  engine_size: z.number().min(50, 'Engine size must be at least 50cc').max(2000, 'Engine size cannot exceed 2000cc'),
  transmission: z.enum(['automatic', 'manual']),
  fuel_type: z.enum(['petrol', 'electric']),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  
  // Pricing
  daily_rate: z.number().min(10000, 'Daily rate must be at least IDR 10,000'),
  weekly_rate: z.number().min(50000, 'Weekly rate must be at least IDR 50,000'),
  monthly_rate: z.number().min(200000, 'Monthly rate must be at least IDR 200,000'),
  yearly_rate: z.number().optional().nullable(),
  security_deposit: z.number().min(0, 'Security deposit cannot be negative'),
  
  // Rental Terms
  minimum_rental_days: z.number().min(1, 'Minimum rental days must be at least 1').default(1),
  maximum_rental_days: z.number().min(1, 'Maximum rental days must be at least 1').default(30),
  
  // Delivery Options
  delivery_available: z.boolean().default(false),
  delivery_fee: z.number().min(0, 'Delivery fee cannot be negative').default(0),
  delivery_radius: z.number().min(0, 'Delivery radius cannot be negative').default(0),
  
  // Inventory
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  available_quantity: z.number().min(0, 'Available quantity cannot be negative'),
  
  // Features and Add-ons
  features: z.array(z.string()).default([]),
  add_ons: z.array(z.object({
    name: z.string(),
    price: z.number().min(0),
    description: z.string().optional(),
  })).default([]),
  
  // Location (simplified for now)
  location: z.object({
    address: z.string().min(1, 'Address is required'),
    city: z.string().min(1, 'City is required'),
    latitude: z.number({ 
      required_error: 'Latitude is required', 
      invalid_type_error: 'Latitude must be a number' 
    }),
    longitude: z.number({ 
      required_error: 'Longitude is required', 
      invalid_type_error: 'Longitude must be a number' 
    }),
  }),
  
  // Instructions
  pickup_instructions: z.string().optional().nullable(),
  rental_agreement: z.string().optional().nullable(),
  
  // Status
  active: z.boolean().default(true),
  
  // Images (will be handled separately in the form)
  images: z.array(z.string()).min(1, 'At least one image is required'),
  
  // Smart Tags
  smartTags: z.array(z.string()).optional(),
})

export type VehicleFormData = z.infer<typeof VehicleFormSchema>

// Validation function for additional business rules
export const validateVehicleForm = (data: VehicleFormData): string[] => {
  const errors: string[] = []
  
  // Weekly rate should be less than 7x daily rate (discount expected)
  if (data.weekly_rate >= data.daily_rate * 7) {
    errors.push('Weekly rate should offer a discount compared to daily rate')
  }
  
  // Monthly rate should be less than 30x daily rate (bigger discount expected)
  if (data.monthly_rate >= data.daily_rate * 30) {
    errors.push('Monthly rate should offer a discount compared to daily rate')
  }
  
  // Available quantity cannot exceed total quantity
  if (data.available_quantity > data.quantity) {
    errors.push('Available quantity cannot exceed total quantity')
  }
  
  // Minimum rental days cannot exceed maximum rental days
  if (data.minimum_rental_days > data.maximum_rental_days) {
    errors.push('Minimum rental days cannot exceed maximum rental days')
  }
  
  // If delivery is available, delivery fee should be specified
  if (data.delivery_available && data.delivery_fee === 0 && data.delivery_radius > 0) {
    errors.push('Delivery fee should be specified when delivery is available with radius > 0')
  }
  
  return errors
}

// Default form values
export const getDefaultVehicleFormValues = (): Partial<VehicleFormData> => ({
  category: 'small_scooter',
  transmission: 'automatic',
  fuel_type: 'petrol',
  minimum_rental_days: 1,
  maximum_rental_days: 30,
  delivery_available: false,
  delivery_fee: 0,
  delivery_radius: 0,
  features: [],
  add_ons: [],
  active: true,
  images: [],
  year: new Date().getFullYear(),
})

// Categories for the dropdown
export const vehicleCategories = [
  { value: 'small_scooter', label: 'Small Scooter (50-125cc)' },
  { value: 'large_scooter', label: 'Large Scooter (125-250cc)' },
  { value: 'luxury_bike', label: 'Luxury Bike (250cc+)' },
] as const

// Common features that can be selected
export const commonFeatures = [
  'GPS Navigation',
  'Bluetooth Connectivity',
  'USB Charging Port',
  'Under-seat Storage',
  'Top Box',
  'Phone Holder',
  'Rain Cover',
  'Anti-theft Alarm',
  'LED Lights',
  'Digital Dashboard',
  'Keyless Start',
  'ABS Brakes',
] as const

// Common add-ons with suggested prices (in IDR)
export const commonAddOns = [
  { name: 'Extra Helmet', price: 10000, description: 'Additional helmet for passenger' },
  { name: 'Rain Gear', price: 15000, description: 'Raincoat and pants' },
  { name: 'Phone Mount', price: 5000, description: 'Secure phone mounting bracket' },
  { name: 'Top Box', price: 20000, description: 'Large storage box' },
  { name: 'Full Insurance', price: 50000, description: 'Comprehensive insurance coverage' },
  { name: 'GPS Device', price: 25000, description: 'Standalone GPS navigation device' },
] as const

export interface VehicleForm {
  smartTags?: string[];
  // ... existing fields
}

export const PREDEFINED_SMART_TAGS = [
  'Beginner-Friendly',
  'Great for Couples',
  'Adventure-Ready',
  'Lightweight',
  'Long-Term Friendly',
  'Luxury Experience',
  'Fuel Efficient',
  'Easy City Navigation',
  'Good for Bali Roads',
  'Pet Friendly',
  'Family Friendly',
  'Offroad Ready'
];
