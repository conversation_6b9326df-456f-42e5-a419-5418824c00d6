import { FC } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Box } from '@mui/material'
import { useAuth } from './contexts/AuthContext'
import Layout from './components/Layout'
import SignIn from './pages/SignIn'
import Dashboard from './pages/Dashboard'
import AdminDashboard from './pages/AdminDashboard'
import ProviderDashboard from './pages/ProviderDashboard'
import Vehicles from './pages/Vehicles'
import CreateVehicle from './pages/CreateVehicle'
import UpdateVehicle from './pages/UpdateVehicle'
import Bookings from './pages/Bookings'
import Providers from './pages/Providers'
import Users from './pages/Users'
import LoadingScreen from './components/LoadingScreen'

// New admin pages
import ProviderPayouts from './pages/ProviderPayouts'
import BankDetails from './pages/BankDetails'
import UserVerification from './pages/UserVerification'
import SupportTickets from './pages/SupportTickets'
import Complaints from './pages/Complaints'
import RefundsDisputes from './pages/RefundsDisputes'
import CashflowDashboard from './pages/CashflowDashboard'
import TransactionHistory from './pages/TransactionHistory'
import FinancialReports from './pages/FinancialReports'
import SystemAnalytics from './pages/SystemAnalytics'
import PlatformHealth from './pages/PlatformHealth'
import SystemSettings from './pages/SystemSettings'

// Protected Route component
const ProtectedRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth()
  
  if (state.isLoading) {
    return <LoadingScreen />
  }
  
  if (!state.isAuthenticated) {
    return <Navigate to="/signin" replace />
  }
  
  return <>{children}</>
}

// Admin Route component (only for admin users)
const AdminRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth()
  const isAdmin = state.user?.role === 'ADMIN'
  const isProvider = state.user?.role === 'PROVIDER'
  
  // Allow both admin and providers to access admin panel
  if (!isAdmin && !isProvider) {
    return <Navigate to="/dashboard" replace />
  }
  
  return <>{children}</>
}

// Provider Route component (only for provider users)
const ProviderRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAuth()
  const isAdmin = state.user?.role === 'ADMIN'
  const isProvider = state.user?.role === 'PROVIDER'
  
  if (!isProvider && !isAdmin) {
    return <Navigate to="/dashboard" replace />
  }
  
  return <>{children}</>
}

const App: FC = () => {
  const { state } = useAuth()

  if (state.isLoading) {
    return <LoadingScreen />
  }

  return (
    <Router>
      <Routes>
        <Route 
          path="/signin" 
          element={
            state.isAuthenticated ? 
            <Navigate to="/dashboard" replace /> : 
            <SignIn />
          } 
        />
        
        <Route 
          path="/*" 
          element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/dashboard" element={<Dashboard />} />
                  
                  {/* Admin Dashboard Routes */}
                  <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
                  <Route path="/users" element={<AdminRoute><Users /></AdminRoute>} />
                  <Route path="/providers" element={<AdminRoute><Providers /></AdminRoute>} />
                  <Route path="/user-verification" element={<AdminRoute><UserVerification /></AdminRoute>} />
                  <Route path="/support-tickets" element={<AdminRoute><SupportTickets /></AdminRoute>} />
                  <Route path="/complaints" element={<AdminRoute><Complaints /></AdminRoute>} />
                  <Route path="/refunds-disputes" element={<AdminRoute><RefundsDisputes /></AdminRoute>} />
                  <Route path="/cashflow" element={<AdminRoute><CashflowDashboard /></AdminRoute>} />
                  <Route path="/transactions" element={<AdminRoute><TransactionHistory /></AdminRoute>} />
                  <Route path="/financial-reports" element={<AdminRoute><FinancialReports /></AdminRoute>} />
                  <Route path="/analytics" element={<AdminRoute><SystemAnalytics /></AdminRoute>} />
                  <Route path="/platform-health" element={<AdminRoute><PlatformHealth /></AdminRoute>} />
                  <Route path="/settings" element={<AdminRoute><SystemSettings /></AdminRoute>} />
                  
                  {/* Provider Dashboard Routes */}
                  <Route path="/provider-dashboard" element={<ProviderRoute><ProviderDashboard /></ProviderRoute>} />
                  <Route path="/vehicles" element={<ProviderRoute><Vehicles /></ProviderRoute>} />
                  <Route path="/create-vehicle" element={<ProviderRoute><CreateVehicle /></ProviderRoute>} />
                  <Route path="/update-vehicle/:id" element={<ProviderRoute><UpdateVehicle /></ProviderRoute>} />
                  <Route path="/bookings" element={<ProviderRoute><Bookings /></ProviderRoute>} />
                  <Route path="/payouts" element={<ProviderRoute><ProviderPayouts /></ProviderRoute>} />
                  <Route path="/bank-details" element={<ProviderRoute><BankDetails /></ProviderRoute>} />
                  
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } 
        />
      </Routes>
    </Router>
  )
}

export default App
