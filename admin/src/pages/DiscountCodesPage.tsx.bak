import React, { useState, useEffect } from 'react';
import { 
    <PERSON>, 
    <PERSON>po<PERSON>, 
    <PERSON>ton, 
    Dialog, 
    DialogTitle, 
    DialogContent, 
    DialogActions, 
    TextField, 
    Select, 
    MenuItem, 
    FormControl, 
    InputLabel,
    Switch,
    FormControlLabel,
    Grid,
    Paper
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import { DiscountService } from '../services/DiscountService';

interface DiscountCode {
    id?: string;
    code: string;
    description?: string;
    discountType: 'percentage' | 'fixed';
    discountValue: number;
    minPurchaseAmount?: number;
    maxDiscountAmount?: number;
    startDate?: Date;
    expirationDate?: Date;
    usageLimit?: number;
    appliesTo?: string[];
    isActive: boolean;
}

const DiscountCodesPage: React.FC = () => {
    const [discountCodes, setDiscountCodes] = useState<DiscountCode[]>([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [currentDiscountCode, setCurrentDiscountCode] = useState<DiscountCode>({
        code: '',
        discountType: 'percentage',
        discountValue: 0,
        isActive: true
    });

    useEffect(() => {
        fetchDiscountCodes();
    }, []);

    const fetchDiscountCodes = async () => {
        try {
            const codes = await DiscountService.listDiscountCodes();
            setDiscountCodes(codes);
        } catch (error) {
            console.error('Failed to fetch discount codes', error);
        }
    };

    const handleCreateOrUpdateDiscountCode = async () => {
        try {
            if (currentDiscountCode.id) {
                // Update existing discount code
                await DiscountService.updateDiscountCode(
                    currentDiscountCode.id, 
                    currentDiscountCode
                );
            } else {
                // Create new discount code
                await DiscountService.createDiscountCode(
                    currentDiscountCode, 
                    'admin-user-id' // Replace with actual admin user ID
                );
            }
            
            // Refresh the list and close dialog
            fetchDiscountCodes();
            setOpenDialog(false);
        } catch (error) {
            console.error('Failed to save discount code', error);
        }
    };

    const handleDeactivateDiscountCode = async (id: string) => {
        try {
            await DiscountService.deactivateDiscountCode(id);
            fetchDiscountCodes();
        } catch (error) {
            console.error('Failed to deactivate discount code', error);
        }
    };

    const renderDiscountCodeForm = () => (
        <Dialog 
            open={openDialog} 
            onClose={() => setOpenDialog(false)}
            maxWidth="md"
            fullWidth
        >
            <DialogTitle>
                {currentDiscountCode.id ? 'Edit' : 'Create'} Discount Code
            </DialogTitle>
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Discount Code"
                            value={currentDiscountCode.code}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                code: e.target.value.toUpperCase()
                            }))}
                            required
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Description"
                            value={currentDiscountCode.description || ''}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                description: e.target.value
                            }))}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                            <InputLabel>Discount Type</InputLabel>
                            <Select
                                value={currentDiscountCode.discountType}
                                label="Discount Type"
                                onChange={(e) => setCurrentDiscountCode(prev => ({
                                    ...prev, 
                                    discountType: e.target.value as 'percentage' | 'fixed'
                                }))}
                            >
                                <MenuItem value="percentage">Percentage</MenuItem>
                                <MenuItem value="fixed">Fixed Amount</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Discount Value"
                            type="number"
                            value={currentDiscountCode.discountValue}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                discountValue: Number(e.target.value)
                            }))}
                            InputProps={{
                                inputProps: { 
                                    min: 0, 
                                    max: currentDiscountCode.discountType === 'percentage' ? 100 : undefined 
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Minimum Purchase Amount"
                            type="number"
                            value={currentDiscountCode.minPurchaseAmount || 0}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                minPurchaseAmount: Number(e.target.value)
                            }))}
                            InputProps={{ inputProps: { min: 0 } }}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Maximum Discount Amount"
                            type="number"
                            value={currentDiscountCode.maxDiscountAmount || ''}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                maxDiscountAmount: e.target.value ? Number(e.target.value) : undefined
                            }))}
                            InputProps={{ inputProps: { min: 0 } }}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <DateTimePicker
                            label="Start Date"
                            value={currentDiscountCode.startDate || null}
                            onChange={(date) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                startDate: date || undefined
                            }))}
                            renderInput={(params) => <TextField {...params} fullWidth />}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <DateTimePicker
                            label="Expiration Date"
                            value={currentDiscountCode.expirationDate || null}
                            onChange={(date) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                expirationDate: date || undefined
                            }))}
                            renderInput={(params) => <TextField {...params} fullWidth />}
                        />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <TextField
                            fullWidth
                            label="Usage Limit"
                            type="number"
                            value={currentDiscountCode.usageLimit || ''}
                            onChange={(e) => setCurrentDiscountCode(prev => ({
                                ...prev, 
                                usageLimit: e.target.value ? Number(e.target.value) : undefined
                            }))}
                            InputProps={{ inputProps: { min: 0 } }}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={currentDiscountCode.isActive}
                                    onChange={(e) => setCurrentDiscountCode(prev => ({
                                        ...prev, 
                                        isActive: e.target.checked
                                    }))}
                                />
                            }
                            label="Active"
                        />
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
                <Button 
                    onClick={handleCreateOrUpdateDiscountCode}
                    color="primary"
                    variant="contained"
                >
                    Save Discount Code
                </Button>
            </DialogActions>
        </Dialog>
    );

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                Discount Codes Management
            </Typography>
            
            <Button 
                variant="contained" 
                color="primary" 
                onClick={() => {
                    setCurrentDiscountCode({
                        code: '',
                        discountType: 'percentage',
                        discountValue: 0,
                        isActive: true
                    });
                    setOpenDialog(true);
                }}
                sx={{ mb: 2 }}
            >
                Create New Discount Code
            </Button>

            <Grid container spacing={2}>
                {discountCodes.map((code) => (
                    <Grid item xs={12} md={6} key={code.id}>
                        <Paper elevation={3} sx={{ p: 2 }}>
                            <Typography variant="h6">{code.code}</Typography>
                            <Typography variant="body2">
                                {code.discountType === 'percentage' 
                                    ? `${code.discountValue}% Off` 
                                    : `$${code.discountValue} Off`
                                }
                            </Typography>
                            <Typography variant="body2">
                                Status: {code.isActive ? 'Active' : 'Inactive'}
                            </Typography>
                            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                                <Button 
                                    variant="outlined" 
                                    color="primary"
                                    onClick={() => {
                                        setCurrentDiscountCode(code);
                                        setOpenDialog(true);
                                    }}
                                >
                                    Edit
                                </Button>
                                {code.isActive && (
                                    <Button 
                                        variant="outlined" 
                                        color="secondary"
                                        onClick={() => handleDeactivateDiscountCode(code.id!)}
                                    >
                                        Deactivate
                                    </Button>
                                )}
                            </Box>
                        </Paper>
                    </Grid>
                ))}
            </Grid>

            {renderDiscountCodeForm()}
        </Box>
    );
};

export default DiscountCodesPage; 