import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Grid, 
  Chip, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Select, 
  MenuItem, 
  FormControl, 
  InputLabel 
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { AdminApiService } from '../services/AdminApiService';

const categoryColors = {
  PAYMENT: 'primary',
  VEHICLE: 'secondary',
  DAMAGE: 'error',
  BOOKING: 'info',
  OTHER: 'default'
};

const statusColors = {
  OPEN: 'primary',
  IN_PROGRESS: 'warning',
  RESOLVED: 'success',
  ESCALATED: 'error'
};

const SupportDashboard: React.FC = () => {
  const [tickets, setTickets] = useState([]);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [openResponseDialog, setOpenResponseDialog] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    const fetchTickets = async () => {
      try {
        const response = await AdminApiService.getSupportTickets(1, 50);
        setTickets(response.tickets);
      } catch (error) {
        console.error('Failed to load support tickets:', error);
        enqueueSnackbar('Failed to load support tickets', { variant: 'error' });
      }
    };

    fetchTickets();
  }, []);

  const handleUpdateTicketStatus = async (ticketId, status) => {
    try {
      await AdminApiService.updateTicketStatus(ticketId, status);

      // Update local state
      setTickets(tickets.map(ticket =>
        ticket.id === ticketId ? { ...ticket, status } : ticket
      ));

      enqueueSnackbar('Ticket status updated successfully', { variant: 'success' });
    } catch (error) {
      console.error('Failed to update ticket status:', error);
      enqueueSnackbar('Failed to update ticket status', { variant: 'error' });
    }
  };

  const handleOpenResponseDialog = (ticket) => {
    setSelectedTicket(ticket);
    setOpenResponseDialog(true);
  };

  const handleCloseResponseDialog = () => {
    setSelectedTicket(null);
    setOpenResponseDialog(false);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Support Ticket Management
      </Typography>

      <Grid container spacing={3}>
        {tickets.map((ticket) => (
          <Grid item xs={12} key={ticket.id}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h6">
                    {ticket.bookingId ? `Booking ${ticket.bookingId}` : 'General Inquiry'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {ticket.message}
                  </Typography>
                </Box>
                <Box display="flex" gap={2} alignItems="center">
                  <Chip 
                    label={ticket.category} 
                    color={categoryColors[ticket.category] || 'default'} 
                    size="small" 
                  />
                  <Chip 
                    label={ticket.status} 
                    color={statusColors[ticket.status] || 'default'} 
                    size="small" 
                  />
                  <Button 
                    variant="outlined" 
                    color="primary" 
                    size="small"
                    onClick={() => handleOpenResponseDialog(ticket)}
                  >
                    Respond
                  </Button>
                </Box>
              </Box>
              <Box display="flex" justifyContent="space-between" mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Created: {new Date(ticket.createdAt).toLocaleString()}
                </Typography>
                <Select
                  value={ticket.status}
                  onChange={(e) => handleUpdateTicketStatus(ticket.id, e.target.value)}
                  size="small"
                  variant="outlined"
                >
                  <MenuItem value="OPEN">Open</MenuItem>
                  <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                  <MenuItem value="RESOLVED">Resolved</MenuItem>
                  <MenuItem value="ESCALATED">Escalated</MenuItem>
                </Select>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Dialog 
        open={openResponseDialog} 
        onClose={handleCloseResponseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Respond to Support Ticket</DialogTitle>
        <DialogContent>
          {selectedTicket && (
            <Box>
              <Typography variant="h6">
                {selectedTicket.bookingId ? `Booking ${selectedTicket.bookingId}` : 'General Inquiry'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1, mb: 2 }}>
                {selectedTicket.message}
              </Typography>
              <TextField
                label="Your Response"
                multiline
                rows={4}
                fullWidth
                variant="outlined"
                placeholder="Type your response here..."
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseResponseDialog} color="secondary">
            Cancel
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            onClick={handleCloseResponseDialog}
          >
            Send Response
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SupportDashboard;
