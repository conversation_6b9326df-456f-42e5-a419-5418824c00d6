import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Stack,
  Divider,
  LinearProgress,
  Chip,
  Alert,
  AlertTitle
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  AccountBalance as BankIcon,
  Receipt as ReceiptIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { CashflowData, CashflowBreakdown, SupabaseDashboardService } from '../services/SupabaseDashboardService';

const CashflowDashboard: React.FC = () => {
  const [cashflowData, setCashflowData] = useState<CashflowData | null>(null);
  const [revenueBreakdown, setRevenueBreakdown] = useState<CashflowBreakdown[]>([]);
  const [expenseBreakdown, setExpenseBreakdown] = useState<CashflowBreakdown[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timePeriod, setTimePeriod] = useState<string>('month');

  useEffect(() => {
    fetchCashflowData();
  }, [timePeriod]);

  const fetchCashflowData = async () => {
    try {
      setLoading(true);

      // Fetch live data from Supabase
      const [liveData, liveBreakdown] = await Promise.all([
        SupabaseDashboardService.getCashflowData(timePeriod),
        SupabaseDashboardService.getCashflowBreakdown(timePeriod)
      ]);

      setCashflowData(liveData);
      setRevenueBreakdown(liveBreakdown.filter(item =>
        item.category.includes('Revenue') || item.category.includes('Fees')
      ));
      setExpenseBreakdown(liveBreakdown.filter(item =>
        item.category.includes('Payouts') || item.category.includes('Processing') || item.category.includes('Operational')
      ));

      // If no data is found, show a message
      if (!liveData.revenue.total && !liveData.expenses.total) {
        setError('No financial data available for the selected period. Please add some transactions or try a different time period.');
      } else {
        setError(null);
      }
    } catch (error) {
      console.error('Error fetching cashflow data:', error);
      setError('Failed to load cashflow data. Please check your database connection.');
      // Set empty data as fallback
      setCashflowData({
        period: timePeriod,
        revenue: { total: 0, bookings: 0, fees: 0, growth: 0 },
        expenses: { total: 0, payouts: 0, processing: 0, operational: 0, growth: 0 },
        netCashflow: { amount: 0, growth: 0 },
        metrics: { grossMargin: 0, burnRate: 0, runway: 0, conversionRate: 0 }
      });
      setRevenueBreakdown([]);
      setExpenseBreakdown([]);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getTrendIcon = (growth: number) => {
    return growth >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />;
  };

  const getTrendColor = (growth: number) => {
    return growth >= 0 ? 'success.main' : 'error.main';
  };

  if (loading || !cashflowData) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          💰 Cashflow Dashboard
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          💰 Cashflow Dashboard
        </Typography>
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Period</InputLabel>
            <Select
              value={timePeriod}
              onChange={(e) => setTimePeriod(e.target.value)}
              label="Period"
            >
              <MenuItem value="week">This Week</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="quarter">This Quarter</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchCashflowData}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
          >
            Export
          </Button>
        </Stack>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Data Loading Error</AlertTitle>
          {error}
        </Alert>
      )}

      {!error && cashflowData.revenue.total === 0 && cashflowData.expenses.total === 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>No Financial Data</AlertTitle>
          No financial transactions found for the selected period. Try selecting a different time period or add some payment data to see your cashflow analytics.
        </Alert>
      )}

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Revenue
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {formatCurrency(cashflowData.revenue.total)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getTrendIcon(cashflowData.revenue.growth)}
                    <Typography 
                      variant="body2" 
                      color={getTrendColor(cashflowData.revenue.growth)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(cashflowData.revenue.growth)}
                    </Typography>
                  </Box>
                </Box>
                <MoneyIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Expenses
                  </Typography>
                  <Typography variant="h5" color="error.main">
                    {formatCurrency(cashflowData.expenses.total)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getTrendIcon(cashflowData.expenses.growth)}
                    <Typography 
                      variant="body2" 
                      color={getTrendColor(cashflowData.expenses.growth)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(cashflowData.expenses.growth)}
                    </Typography>
                  </Box>
                </Box>
                <ReceiptIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Net Cashflow
                  </Typography>
                  <Typography variant="h5" color="primary.main">
                    {formatCurrency(cashflowData.netCashflow.amount)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getTrendIcon(cashflowData.netCashflow.growth)}
                    <Typography 
                      variant="body2" 
                      color={getTrendColor(cashflowData.netCashflow.growth)}
                      sx={{ ml: 0.5 }}
                    >
                      {formatPercentage(cashflowData.netCashflow.growth)}
                    </Typography>
                  </Box>
                </Box>
                <BankIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Gross Margin
                  </Typography>
                  <Typography variant="h5">
                    {cashflowData.metrics.grossMargin.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Industry Avg: 25%
                  </Typography>
                </Box>
                <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Revenue and Expense Breakdown */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Revenue Breakdown
            </Typography>
            <Stack spacing={2}>
              {revenueBreakdown.map((item, index) => (
                <Box key={index}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2">
                      {item.category}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(item.amount)}
                      </Typography>
                      <Chip
                        label={`${item.percentage.toFixed(1)}%`}
                        size="small"
                        sx={{ ml: 1, bgcolor: item.color, color: 'white' }}
                      />
                    </Box>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={item.percentage}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: item.color,
                        borderRadius: 4
                      }
                    }}
                  />
                </Box>
              ))}
            </Stack>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Expense Breakdown
            </Typography>
            <Stack spacing={2}>
              {expenseBreakdown.map((item, index) => (
                <Box key={index}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2">
                      {item.category}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(item.amount)}
                      </Typography>
                      <Chip
                        label={`${item.percentage.toFixed(1)}%`}
                        size="small"
                        sx={{ ml: 1, bgcolor: item.color, color: 'white' }}
                      />
                    </Box>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={item.percentage}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: item.color,
                        borderRadius: 4
                      }
                    }}
                  />
                </Box>
              ))}
            </Stack>
          </Paper>
        </Grid>
      </Grid>

      {/* Financial Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Key Financial Metrics
            </Typography>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Gross Margin:</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {cashflowData.metrics.grossMargin.toFixed(1)}%
                </Typography>
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Monthly Burn Rate:</Typography>
                <Typography variant="body2" fontWeight="bold" color="error.main">
                  {formatCurrency(cashflowData.metrics.burnRate)}
                </Typography>
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Cash Runway:</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {cashflowData.metrics.runway.toFixed(1)} months
                </Typography>
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Conversion Rate:</Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  {cashflowData.metrics.conversionRate.toFixed(1)}%
                </Typography>
              </Box>
            </Stack>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Cashflow Summary
            </Typography>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Booking Revenue:</Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  +{formatCurrency(cashflowData.revenue.bookings)}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Platform Fees:</Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  +{formatCurrency(cashflowData.revenue.fees)}
                </Typography>
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Provider Payouts:</Typography>
                <Typography variant="body2" fontWeight="bold" color="error.main">
                  -{formatCurrency(cashflowData.expenses.payouts)}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Processing Costs:</Typography>
                <Typography variant="body2" fontWeight="bold" color="error.main">
                  -{formatCurrency(cashflowData.expenses.processing)}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Operational Costs:</Typography>
                <Typography variant="body2" fontWeight="bold" color="error.main">
                  -{formatCurrency(cashflowData.expenses.operational)}
                </Typography>
              </Box>
              <Divider sx={{ borderStyle: 'double', borderWidth: 3 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6">Net Cashflow:</Typography>
                <Typography variant="h6" fontWeight="bold" color="primary.main">
                  {formatCurrency(cashflowData.netCashflow.amount)}
                </Typography>
              </Box>
            </Stack>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CashflowDashboard;
