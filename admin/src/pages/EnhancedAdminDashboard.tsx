import React from 'react'
import { Box, Typography, Container, Card, CardContent, Grid } from '@mui/material'
import { useAuth } from '../contexts/AuthContext'

const EnhancedAdminDashboard: React.FC = () => {
  const { state } = useAuth()

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        🎛️ Admin Dashboard
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Welcome back, {state.user?.name}! You have full admin access.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                👤 User Management
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Manage all users, providers, and customers
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🏢 Provider Management
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Oversee provider fleets and performance
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          🎉 Authentication Integration Complete!
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Your super user account (<EMAIL>) now has full access to:
        </Typography>
        <Box component="ul" sx={{ mt: 1 }}>
          <li>Admin Dashboard - Full administrative privileges</li>
          <li>Provider Dashboard - Vehicle and booking management</li>
          <li>Customer Dashboard - Booking and profile management</li>
        </Box>
      </Box>
    </Container>
  )
}

export default EnhancedAdminDashboard
