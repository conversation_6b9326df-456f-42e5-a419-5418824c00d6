import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Chip
} from '@mui/material';
import {
  People as PeopleIcon,
  DirectionsCar as CarIcon,
  BookOnline as BookingIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import { AdminApiService, DashboardStats } from '../services/AdminApiService';

interface DashboardStats {
  users: {
    total: number;
    recent: any[];
    growth: { total: number; period: string };
  };
  vehicles: {
    total: number;
    available: number;
    recent: any[];
  };
  bookings: {
    total: number;
    active: number;
    recent: any[];
  };
  financials: {
    totalRevenue: number;
    monthlyRevenue: number;
    pendingPayments: number;
  };
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        // For now, use mock data since the API might not be ready
        const mockStats: DashboardStats = {
          users: {
            total: 1250,
            recent: [
              { id: 1, name: 'John Doe', email: '<EMAIL>', date: '2024-01-15' },
              { id: 2, name: 'Jane Smith', email: '<EMAIL>', date: '2024-01-14' }
            ],
            growth: { total: 12, period: 'month' }
          },
          vehicles: {
            total: 450,
            available: 380,
            recent: [
              { id: 1, type: 'Scooter', provider: 'City Rentals', status: 'Available' },
              { id: 2, type: 'Motorcycle', provider: 'Speed Bikes', status: 'Booked' }
            ]
          },
          bookings: {
            total: 2340,
            active: 156,
            recent: [
              { id: 1, user: 'John Doe', vehicle: 'Scooter', status: 'Active' },
              { id: 2, user: 'Jane Smith', vehicle: 'Motorcycle', status: 'Completed' }
            ]
          },
          financials: {
            totalRevenue: 125000,
            monthlyRevenue: 15000,
            pendingPayments: 8500
          }
        };
        
        setStats(mockStats);
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch dashboard stats', error);
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  const StatCard = ({ title, value, icon, subtitle, color = 'primary' }: any) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box color={color}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* Users Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={stats?.users.total || 0}
            icon={<PeopleIcon fontSize="large" />}
            subtitle={`+${stats?.users.growth.total || 0} this ${stats?.users.growth.period || 'month'}`}
            color="primary"
          />
        </Grid>

        {/* Vehicles Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Vehicles"
            value={stats?.vehicles.total || 0}
            icon={<CarIcon fontSize="large" />}
            subtitle={`${stats?.vehicles.available || 0} available`}
            color="success"
          />
        </Grid>

        {/* Bookings Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Bookings"
            value={stats?.bookings.total || 0}
            icon={<BookingIcon fontSize="large" />}
            subtitle={`${stats?.bookings.active || 0} active`}
            color="info"
          />
        </Grid>

        {/* Financial Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${(stats?.financials.totalRevenue || 0).toLocaleString()}`}
            icon={<MoneyIcon fontSize="large" />}
            subtitle={`$${(stats?.financials.monthlyRevenue || 0).toLocaleString()} this month`}
            color="success"
          />
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Users
              </Typography>
              <List>
                {stats?.users.recent.map((user, index) => (
                  <React.Fragment key={user.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar>{user.name.charAt(0)}</Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={user.name}
                        secondary={user.email}
                      />
                      <Chip label="New" size="small" color="primary" />
                    </ListItem>
                    {index < stats.users.recent.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Bookings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Bookings
              </Typography>
              <List>
                {stats?.bookings.recent.map((booking, index) => (
                  <React.Fragment key={booking.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: booking.status === 'Active' ? 'success.main' : 'grey.500' }}>
                          <BookingIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={`${booking.user} - ${booking.vehicle}`}
                        secondary={booking.status}
                      />
                      <Chip 
                        label={booking.status} 
                        size="small" 
                        color={booking.status === 'Active' ? 'success' : 'default'}
                      />
                    </ListItem>
                    {index < stats.bookings.recent.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;