import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  LinearProgress,
  Badge,
  Tooltip,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  ListItemButton,
  ListItemIcon,
  Snackbar
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  DirectionsCar as CarIcon,
  BookOnline as BookingIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as AnalyticsIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Business as BusinessIcon,
  AccountBalance as AccountBalanceIcon,
  Timeline as TimelineIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon,
  ExpandMore as ExpandMoreIcon,
  AdminPanelSettings as AdminIcon,
  VerifiedUser as VerifiedIcon,
  Report as ReportIcon,
  SystemUpdate as SystemUpdateIcon,
  Backup as BackupIcon,
  Warning as EmergencyIcon
} from '@mui/icons-material';
import { supabase } from '../utils/supabaseClient';
import Search from '../components/Search';
import { SupabaseDashboardService, DashboardStats } from '../services/SupabaseDashboardService';
import StatusFilter from '../components/StatusFilter';
import ErrorBoundary from '../components/ErrorBoundary';
import NotificationSystem from '../components/NotificationSystem';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  createdAt: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  lastLogin?: string;
  totalBookings?: number;
  totalSpent?: number;
}

interface Vehicle {
  id: string;
  vehicleType: string;
  providerId: string;
  dailyRate: number;
  status: 'AVAILABLE' | 'UNAVAILABLE' | 'MAINTENANCE' | 'RESERVED';
  location_city: string;
  provider: {
    name: string;
    email: string;
  };
  totalBookings?: number;
  totalRevenue?: number;
  rating?: number;
}

interface Booking {
  id: string;
  userId: string;
  providerId: string;
  vehicleId: string;
  totalPrice: number;
  status: string;
  startDate: string;
  endDate: string;
  user: {
    name: string;
    email: string;
  };
  provider: {
    name: string;
    email: string;
  };
  vehicle: {
    vehicleType: string;
  };
  createdAt: string;
  paymentStatus?: string;
}

interface SystemStats {
  totalUsers: number;
  totalProviders: number;
  totalVehicles: number;
  totalBookings: number;
  totalRevenue: number;
  activeBookings: number;
  pendingApprovals: number;
  systemAlerts: number;
  monthlyRevenue: number;
  weeklyGrowth: number;
  platformFee: number;
  pendingPayouts: number;
}

interface SystemAlert {
  id: string;
  type: 'WARNING' | 'ERROR' | 'INFO';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export default function AdminDashboard() {
  console.log('AdminDashboard component rendering...');

  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const liveStats = await SupabaseDashboardService.getDashboardStats();
        setStats(liveStats);
      } catch (error) {
        console.error('Failed to fetch dashboard stats from Supabase:', error);
        setError('Failed to load dashboard data. Please check your database connection.');
        // Set empty stats on error
        setStats({
          users: { total: 0, recent: [], growth: { total: 0, period: 'month' }, activeUsers: 0, newUsersThisMonth: 0 },
          vehicles: { total: 0, available: 0, recent: [], rented: 0, maintenance: 0 },
          bookings: { total: 0, active: 0, recent: [], completed: 0, cancelled: 0, pending: 0 },
          financials: { totalRevenue: 0, monthlyRevenue: 0, pendingPayments: 0, completedPayments: 0, averageBookingValue: 0 },
          providers: { total: 0, active: 0, pending: 0, recent: [] }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🏍️ RentaHub Admin Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" mb={3}>
        Welcome to the RentaHub administration panel. Manage users, vehicles, bookings, and system settings.
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Users
              </Typography>
              <Typography variant="h4">
                {stats?.users.total || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Vehicles
              </Typography>
              <Typography variant="h4">
                {stats?.vehicles.total || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Bookings
              </Typography>
              <Typography variant="h4">
                {stats?.bookings.total || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Revenue
              </Typography>
              <Typography variant="h4">
                ${(stats?.financials.totalRevenue || 0).toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item>
            <Button variant="contained" startIcon={<PeopleIcon />}>
              Manage Users
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" startIcon={<CarIcon />}>
              Manage Vehicles
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" startIcon={<BookingIcon />}>
              View Bookings
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" startIcon={<MoneyIcon />}>
              Financial Reports
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" startIcon={<BuildIcon />} color="warning">
              Vehicle Assistance
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}
