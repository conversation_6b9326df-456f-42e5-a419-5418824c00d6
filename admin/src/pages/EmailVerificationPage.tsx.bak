import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { 
  Box, 
  Typography, 
  Container, 
  CircularProgress, 
  Alert 
} from '@mui/material';

const EmailVerificationPage: React.FC = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Extract token from URL query parameters
        const searchParams = new URLSearchParams(location.search);
        const token = searchParams.get('token');

        if (!token) {
          setStatus('error');
          setMessage('Invalid verification link');
          return;
        }

        // Call backend verification endpoint
        const response = await axios.post('/api/auth/verify-email', { token });

        // Handle successful verification
        setStatus('success');
        setMessage('Email verified successfully!');

        // Redirect to login after a short delay
        setTimeout(() => {
          navigate('/login');
        }, 3000);

      } catch (error) {
        // Handle verification errors
        setStatus('error');
        if (axios.isAxiosError(error)) {
          setMessage(
            error.response?.data?.message || 
            'Email verification failed. Please try again.'
          );
        } else {
          setMessage('An unexpected error occurred.');
        }
      }
    };

    verifyEmail();
  }, [location, navigate]);

  return (
    <Container maxWidth="xs">
      <Box 
        sx={{ 
          marginTop: 8, 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center' 
        }}
      >
        {status === 'loading' && (
          <CircularProgress />
        )}

        {status === 'success' && (
          <Alert severity="success" sx={{ width: '100%' }}>
            {message}
          </Alert>
        )}

        {status === 'error' && (
          <Alert severity="error" sx={{ width: '100%' }}>
            {message}
          </Alert>
        )}
      </Box>
    </Container>
  );
};

export default EmailVerificationPage;
