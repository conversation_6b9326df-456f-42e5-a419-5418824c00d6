import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  Badge,
  LinearProgress,
  Avatar,
  Stack,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Visibility as ViewIcon,
  CheckCircle as VerifiedIcon,
  Cancel as UnverifiedIcon,
  Email as EmailIcon,
  Google as GoogleIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Block as BlockIcon,
  Refresh as RefreshIcon,
  Send as SendIcon,
  Download as ExportIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { adminSupabase as supabase } from '../services/adminSupabaseClient';

interface UserVerification {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN';
    createdAt: string;
    lastLogin?: string;
  };
  emailVerification: {
    isVerified: boolean;
    verifiedAt?: string;
    verificationSentAt?: string;
    attempts: number;
  };
  googleAuth: {
    isConnected: boolean;
    connectedAt?: string;
    googleId?: string;
    email?: string;
  };
  accountStatus: 'ACTIVE' | 'PENDING' | 'SUSPENDED' | 'BLOCKED';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  verificationScore: number;
  flags: string[];
  lastActivity: string;
}

interface VerificationStats {
  totalUsers: number;
  emailVerified: number;
  googleConnected: number;
  pendingVerification: number;
  suspendedAccounts: number;
  verificationRate: number;
  avgVerificationTime: number;
}

const UserVerification: React.FC = () => {
  const [users, setUsers] = useState<UserVerification[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    totalUsers: 0,
    emailVerified: 0,
    googleConnected: 0,
    pendingVerification: 0,
    suspendedAccounts: 0,
    verificationRate: 0,
    avgVerificationTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<UserVerification | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [filterVerification, setFilterVerification] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, [filterStatus, filterVerification, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // Get live user data from Supabase
      const { data: users, error } = await supabase
        .from('User')
        .select('*')
        .order('createdAt', { ascending: false });

      if (error) {
        console.error('Error fetching users:', error);
        setUsers([]);
        return;
      }

      // Convert User records to UserVerification format
      const liveUsers: UserVerification[] = (users || []).map(user => ({
        id: user.id,
        user: {
          id: user.id,
          name: user.name || 'Unknown User',
          email: user.email,
          role: user.role || 'CUSTOMER',
          createdAt: user.createdAt || new Date().toISOString(),
          lastLogin: user.lastLoginAt || user.createdAt || new Date().toISOString()
        },
        emailVerification: {
          isVerified: user.emailVerified || false,
          verifiedAt: user.emailVerified ? user.createdAt : null,
          verificationSentAt: user.createdAt,
          attempts: 1
        },
        googleAuth: {
          isConnected: !!user.googleId,
          connectedAt: user.googleId ? user.createdAt : null,
          googleId: user.googleId || null,
          email: user.email
        },
        accountStatus: user.status || 'ACTIVE',
        riskLevel: 'LOW' as const,
        verificationScore: user.emailVerified ? 95 : 50,
        flags: user.emailVerified ? [] : ['EMAIL_NOT_VERIFIED'],
        lastActivity: user.lastLoginAt || user.createdAt || new Date().toISOString()
      }));

      setUsers(liveUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Get live user verification statistics from Supabase
      const { data: users, error } = await supabase
        .from('User')
        .select('emailVerified, googleId, status');

      if (error) {
        console.error('Error fetching user stats:', error);
        setStats({
          totalUsers: 0,
          emailVerified: 0,
          googleConnected: 0,
          pendingVerification: 0,
          suspendedAccounts: 0,
          verificationRate: 0,
          avgVerificationTime: 0
        });
        return;
      }

      const totalUsers = users?.length || 0;
      const emailVerified = users?.filter(u => u.emailVerified).length || 0;
      const googleConnected = users?.filter(u => u.googleId).length || 0;
      const pendingVerification = totalUsers - emailVerified;
      const suspendedAccounts = users?.filter(u => u.status === 'SUSPENDED').length || 0;
      const verificationRate = totalUsers > 0 ? (emailVerified / totalUsers) * 100 : 0;

      setStats({
        totalUsers,
        emailVerified,
        googleConnected,
        pendingVerification,
        suspendedAccounts,
        verificationRate,
        avgVerificationTime: 0 // Would need timestamp analysis for real calculation
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'PENDING': return 'warning';
      case 'SUSPENDED': return 'error';
      case 'BLOCKED': return 'error';
      default: return 'default';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW': return 'success';
      case 'MEDIUM': return 'warning';
      case 'HIGH': return 'error';
      default: return 'default';
    }
  };

  const getVerificationScoreColor = (score: number) => {
    if (score >= 80) return 'success.main';
    if (score >= 60) return 'warning.main';
    return 'error.main';
  };

  const handleViewUser = (user: UserVerification) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  const handleSendVerificationEmail = async (userId: string) => {
    try {
      // API call to resend verification email
      // await AdminApiService.resendVerificationEmail(userId);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { 
              ...user, 
              emailVerification: {
                ...user.emailVerification,
                verificationSentAt: new Date().toISOString(),
                attempts: user.emailVerification.attempts + 1
              }
            }
          : user
      ));
      
      alert('Verification email sent successfully!');
    } catch (error) {
      console.error('Error sending verification email:', error);
      alert('Failed to send verification email');
    }
  };

  const handleUpdateAccountStatus = async (userId: string, newStatus: string) => {
    try {
      // API call to update account status
      // await AdminApiService.updateUserAccountStatus(userId, newStatus);
      
      // Update local state
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { ...user, accountStatus: newStatus as any }
          : user
      ));
      
      setDialogOpen(false);
      fetchStats();
    } catch (error) {
      console.error('Error updating account status:', error);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesStatus = filterStatus === 'ALL' || user.accountStatus === filterStatus;
    const matchesVerification = filterVerification === 'ALL' || 
      (filterVerification === 'EMAIL_VERIFIED' && user.emailVerification.isVerified) ||
      (filterVerification === 'EMAIL_UNVERIFIED' && !user.emailVerification.isVerified) ||
      (filterVerification === 'GOOGLE_CONNECTED' && user.googleAuth.isConnected) ||
      (filterVerification === 'GOOGLE_DISCONNECTED' && !user.googleAuth.isConnected);
    const matchesSearch = searchTerm === '' || 
      user.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesVerification && matchesSearch;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🔐 User Verification
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchUsers(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
          >
            Export Report
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Users
              </Typography>
              <Typography variant="h4">
                {stats.totalUsers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Email Verified
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.emailVerified}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {((stats.emailVerified / stats.totalUsers) * 100).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Google Connected
              </Typography>
              <Typography variant="h4" color="primary.main">
                {stats.googleConnected}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {((stats.googleConnected / stats.totalUsers) * 100).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pendingVerification}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Verification Rate
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.verificationRate}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Time
              </Typography>
              <Typography variant="h4">
                {stats.avgVerificationTime}h
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Account Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Account Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="ACTIVE">Active</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
                <MenuItem value="SUSPENDED">Suspended</MenuItem>
                <MenuItem value="BLOCKED">Blocked</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Verification</InputLabel>
              <Select
                value={filterVerification}
                onChange={(e) => setFilterVerification(e.target.value)}
                label="Verification"
              >
                <MenuItem value="ALL">All Users</MenuItem>
                <MenuItem value="EMAIL_VERIFIED">Email Verified</MenuItem>
                <MenuItem value="EMAIL_UNVERIFIED">Email Unverified</MenuItem>
                <MenuItem value="GOOGLE_CONNECTED">Google Connected</MenuItem>
                <MenuItem value="GOOGLE_DISCONNECTED">Google Disconnected</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Users Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Email Verification</TableCell>
                  <TableCell>Google Auth</TableCell>
                  <TableCell>Account Status</TableCell>
                  <TableCell>Risk Level</TableCell>
                  <TableCell>Score</TableCell>
                  <TableCell>Last Activity</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((userVerification) => (
                  <TableRow key={userVerification.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 40, height: 40, mr: 2 }}>
                          {userVerification.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {userVerification.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {userVerification.user.email}
                          </Typography>
                          <br />
                          <Chip
                            label={userVerification.user.role}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {userVerification.emailVerification.isVerified ? (
                          <VerifiedIcon color="success" sx={{ mr: 1 }} />
                        ) : (
                          <UnverifiedIcon color="error" sx={{ mr: 1 }} />
                        )}
                        <Box>
                          <Typography variant="body2">
                            {userVerification.emailVerification.isVerified ? 'Verified' : 'Unverified'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Attempts: {userVerification.emailVerification.attempts}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {userVerification.googleAuth.isConnected ? (
                          <GoogleIcon color="primary" sx={{ mr: 1 }} />
                        ) : (
                          <GoogleIcon color="disabled" sx={{ mr: 1 }} />
                        )}
                        <Typography variant="body2">
                          {userVerification.googleAuth.isConnected ? 'Connected' : 'Not Connected'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={userVerification.accountStatus}
                        size="small"
                        color={getStatusColor(userVerification.accountStatus) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={userVerification.riskLevel}
                        size="small"
                        color={getRiskColor(userVerification.riskLevel) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        fontWeight="bold"
                        color={getVerificationScoreColor(userVerification.verificationScore)}
                      >
                        {userVerification.verificationScore}%
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {userVerification.lastActivity ? 
                          new Date(userVerification.lastActivity).toLocaleDateString() : 
                          'Never'
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewUser(userVerification)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        {!userVerification.emailVerification.isVerified && (
                          <Tooltip title="Resend Verification Email">
                            <IconButton
                              size="small"
                              onClick={() => handleSendVerificationEmail(userVerification.id)}
                            >
                              <SendIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* User Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedUser && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  User Verification Details
                </Typography>
                <Chip
                  label={selectedUser.accountStatus}
                  color={getStatusColor(selectedUser.accountStatus) as any}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
                <Tab label="Overview" />
                <Tab label="Verification History" />
                <Tab label="Security Flags" />
              </Tabs>

              {activeTab === 0 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        User Information
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar sx={{ width: 60, height: 60, mr: 2 }}>
                          {selectedUser.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="h6">
                            {selectedUser.user.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedUser.user.email}
                          </Typography>
                          <Chip
                            label={selectedUser.user.role}
                            size="small"
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      </Box>
                      
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption">Account Created:</Typography>
                          <Typography variant="body2">
                            {new Date(selectedUser.user.createdAt).toLocaleString()}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption">Last Login:</Typography>
                          <Typography variant="body2">
                            {selectedUser.user.lastLogin ? 
                              new Date(selectedUser.user.lastLogin).toLocaleString() : 
                              'Never'
                            }
                          </Typography>
                        </Box>
                      </Stack>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Verification Status
                      </Typography>
                      <Stack spacing={2}>
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <EmailIcon sx={{ mr: 1 }} />
                            <Typography variant="subtitle2">Email Verification</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Box>
                              <Typography variant="body2">
                                Status: {selectedUser.emailVerification.isVerified ? 'Verified' : 'Unverified'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Attempts: {selectedUser.emailVerification.attempts}
                              </Typography>
                            </Box>
                            {selectedUser.emailVerification.isVerified ? (
                              <VerifiedIcon color="success" />
                            ) : (
                              <UnverifiedIcon color="error" />
                            )}
                          </Box>
                        </Paper>
                        
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <GoogleIcon sx={{ mr: 1 }} />
                            <Typography variant="subtitle2">Google Authentication</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Box>
                              <Typography variant="body2">
                                Status: {selectedUser.googleAuth.isConnected ? 'Connected' : 'Not Connected'}
                              </Typography>
                              {selectedUser.googleAuth.email && (
                                <Typography variant="caption" color="text.secondary">
                                  Google Email: {selectedUser.googleAuth.email}
                                </Typography>
                              )}
                            </Box>
                            {selectedUser.googleAuth.isConnected ? (
                              <GoogleIcon color="primary" />
                            ) : (
                              <GoogleIcon color="disabled" />
                            )}
                          </Box>
                        </Paper>
                        
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Verification Score
                          </Typography>
                          <Typography 
                            variant="h4" 
                            color={getVerificationScoreColor(selectedUser.verificationScore)}
                          >
                            {selectedUser.verificationScore}%
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={selectedUser.verificationScore} 
                            sx={{ mt: 1 }}
                          />
                        </Paper>
                      </Stack>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Verification Timeline
                  </Typography>
                  <Stack spacing={2}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="body2" fontWeight="bold">
                        Account Created
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(selectedUser.user.createdAt).toLocaleString()}
                      </Typography>
                    </Paper>
                    
                    {selectedUser.emailVerification.verificationSentAt && (
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="body2" fontWeight="bold">
                          Email Verification Sent
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(selectedUser.emailVerification.verificationSentAt).toLocaleString()}
                        </Typography>
                      </Paper>
                    )}
                    
                    {selectedUser.emailVerification.verifiedAt && (
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="body2" fontWeight="bold">
                          Email Verified
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(selectedUser.emailVerification.verifiedAt).toLocaleString()}
                        </Typography>
                      </Paper>
                    )}
                    
                    {selectedUser.googleAuth.connectedAt && (
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="body2" fontWeight="bold">
                          Google Account Connected
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(selectedUser.googleAuth.connectedAt).toLocaleString()}
                        </Typography>
                      </Paper>
                    )}
                  </Stack>
                </Box>
              )}

              {activeTab === 2 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Security Flags & Risk Assessment
                  </Typography>
                  
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" gutterBottom>
                      Risk Level: 
                      <Chip
                        label={selectedUser.riskLevel}
                        size="small"
                        color={getRiskColor(selectedUser.riskLevel) as any}
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                  </Box>
                  
                  {selectedUser.flags.length > 0 ? (
                    <Stack spacing={2}>
                      {selectedUser.flags.map((flag, index) => (
                        <Alert key={index} severity="warning" icon={<WarningIcon />}>
                          <Typography variant="body2">
                            {flag.replace(/_/g, ' ')}
                          </Typography>
                        </Alert>
                      ))}
                    </Stack>
                  ) : (
                    <Alert severity="success">
                      No security flags detected. This user has a clean verification record.
                    </Alert>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Close
              </Button>
              {!selectedUser.emailVerification.isVerified && (
                <Button
                  variant="outlined"
                  startIcon={<SendIcon />}
                  onClick={() => handleSendVerificationEmail(selectedUser.id)}
                >
                  Resend Email
                </Button>
              )}
              {selectedUser.accountStatus === 'PENDING' && (
                <Button
                  variant="contained"
                  color="success"
                  onClick={() => handleUpdateAccountStatus(selectedUser.id, 'ACTIVE')}
                >
                  Activate Account
                </Button>
              )}
              {selectedUser.accountStatus === 'ACTIVE' && selectedUser.riskLevel === 'HIGH' && (
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<BlockIcon />}
                  onClick={() => handleUpdateAccountStatus(selectedUser.id, 'SUSPENDED')}
                >
                  Suspend Account
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default UserVerification;
