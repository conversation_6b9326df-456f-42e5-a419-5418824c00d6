import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  People as UsersIcon,
  DirectionsCar as VehiclesIcon,
  Notifications as NotificationIcon,
  Download as ExportIcon,
  CleaningServices as CleanupIcon,
  AttachMoney as PricingIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import BulkOperationsService from '../services/BulkOperationsService';

const BulkOperations: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentOperation, setCurrentOperation] = useState<string | null>(null);
  const [operationResult, setOperationResult] = useState<any>(null);

  // Form states for different operations
  const [userBulkForm, setUserBulkForm] = useState({
    userIds: '',
    action: 'update_status',
    status: 'active'
  });

  const [vehicleBulkForm, setVehicleBulkForm] = useState({
    vehicleIds: '',
    action: 'update_status',
    active: true,
    priceAdjustment: { type: 'percentage', value: 0 }
  });

  const [exportForm, setExportForm] = useState({
    type: 'users',
    filters: {}
  });

  const [notificationForm, setNotificationForm] = useState({
    title: '',
    message: '',
    targetType: 'all_users',
    type: 'info'
  });

  const [cleanupForm, setCleanupForm] = useState({
    type: 'sessions',
    daysOld: 30
  });

  const handleOperation = async (operationType: string) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setCurrentOperation(operationType);

      let result;

      switch (operationType) {
        case 'bulk_user_update':
          const userIds = userBulkForm.userIds.split(',').map(id => id.trim()).filter(id => id);
          if (userBulkForm.action === 'update_status') {
            result = await BulkOperationsService.bulkUpdateUserStatus(userIds, userBulkForm.status as any);
          } else if (userBulkForm.action === 'delete') {
            result = await BulkOperationsService.bulkDeleteUsers(userIds);
          }
          break;

        case 'bulk_vehicle_update':
          const vehicleIds = vehicleBulkForm.vehicleIds.split(',').map(id => id.trim()).filter(id => id);
          if (vehicleBulkForm.action === 'update_status') {
            result = await BulkOperationsService.bulkUpdateVehicleStatus(vehicleIds, vehicleBulkForm.active);
          } else if (vehicleBulkForm.action === 'update_pricing') {
            result = await BulkOperationsService.bulkUpdateVehiclePricing(vehicleIds, vehicleBulkForm.priceAdjustment);
          }
          break;

        case 'export_users':
          result = await BulkOperationsService.exportUsers(exportForm.filters);
          if (result.success && result.data) {
            BulkOperationsService.downloadCSV(result.data, `users-export-${new Date().toISOString().split('T')[0]}.csv`);
          }
          break;

        case 'export_vehicles':
          result = await BulkOperationsService.exportVehicles(exportForm.filters);
          if (result.success && result.data) {
            BulkOperationsService.downloadCSV(result.data, `vehicles-export-${new Date().toISOString().split('T')[0]}.csv`);
          }
          break;

        case 'bulk_notification':
          result = await BulkOperationsService.sendBulkNotification(
            notificationForm.title,
            notificationForm.message,
            notificationForm.targetType as any,
            notificationForm.type as any
          );
          break;

        case 'cleanup_sessions':
          result = await BulkOperationsService.cleanupExpiredSessions();
          break;

        case 'cleanup_notifications':
          result = await BulkOperationsService.cleanupOldNotifications(cleanupForm.daysOld);
          break;

        default:
          throw new Error('Unknown operation type');
      }

      setOperationResult(result);
      setSuccess(`Operation completed successfully! Processed: ${result.processed}, Failed: ${result.failed}`);
      
      if (result.failed > 0) {
        setError(`Some items failed to process: ${result.errors.join(', ')}`);
      }

    } catch (err: any) {
      console.error('Error in bulk operation:', err);
      setError(err.message || 'Operation failed');
    } finally {
      setLoading(false);
      setDialogOpen(false);
      setCurrentOperation(null);
    }
  };

  const openOperationDialog = (operationType: string) => {
    setCurrentOperation(operationType);
    setDialogOpen(true);
    setError(null);
    setSuccess(null);
  };

  const renderOperationDialog = () => {
    if (!currentOperation) return null;

    const getDialogContent = () => {
      switch (currentOperation) {
        case 'bulk_user_update':
          return (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="User IDs (comma-separated)"
                value={userBulkForm.userIds}
                onChange={(e) => setUserBulkForm({ ...userBulkForm, userIds: e.target.value })}
                placeholder="user-id-1, user-id-2, user-id-3"
                multiline
                rows={3}
              />
              <FormControl fullWidth>
                <InputLabel>Action</InputLabel>
                <Select
                  value={userBulkForm.action}
                  label="Action"
                  onChange={(e) => setUserBulkForm({ ...userBulkForm, action: e.target.value })}
                >
                  <MenuItem value="update_status">Update Status</MenuItem>
                  <MenuItem value="delete">Delete Users</MenuItem>
                </Select>
              </FormControl>
              {userBulkForm.action === 'update_status' && (
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={userBulkForm.status}
                    label="Status"
                    onChange={(e) => setUserBulkForm({ ...userBulkForm, status: e.target.value })}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="suspended">Suspended</MenuItem>
                  </Select>
                </FormControl>
              )}
            </Box>
          );

        case 'bulk_vehicle_update':
          return (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="Vehicle IDs (comma-separated)"
                value={vehicleBulkForm.vehicleIds}
                onChange={(e) => setVehicleBulkForm({ ...vehicleBulkForm, vehicleIds: e.target.value })}
                placeholder="vehicle-id-1, vehicle-id-2, vehicle-id-3"
                multiline
                rows={3}
              />
              <FormControl fullWidth>
                <InputLabel>Action</InputLabel>
                <Select
                  value={vehicleBulkForm.action}
                  label="Action"
                  onChange={(e) => setVehicleBulkForm({ ...vehicleBulkForm, action: e.target.value })}
                >
                  <MenuItem value="update_status">Update Status</MenuItem>
                  <MenuItem value="update_pricing">Update Pricing</MenuItem>
                </Select>
              </FormControl>
              {vehicleBulkForm.action === 'update_status' && (
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={vehicleBulkForm.active}
                    label="Status"
                    onChange={(e) => setVehicleBulkForm({ ...vehicleBulkForm, active: e.target.value === 'true' })}
                  >
                    <MenuItem value="true">Active</MenuItem>
                    <MenuItem value="false">Inactive</MenuItem>
                  </Select>
                </FormControl>
              )}
              {vehicleBulkForm.action === 'update_pricing' && (
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <FormControl sx={{ minWidth: 120 }}>
                    <InputLabel>Type</InputLabel>
                    <Select
                      value={vehicleBulkForm.priceAdjustment.type}
                      label="Type"
                      onChange={(e) => setVehicleBulkForm({
                        ...vehicleBulkForm,
                        priceAdjustment: { ...vehicleBulkForm.priceAdjustment, type: e.target.value as any }
                      })}
                    >
                      <MenuItem value="percentage">Percentage</MenuItem>
                      <MenuItem value="fixed">Fixed Amount</MenuItem>
                    </Select>
                  </FormControl>
                  <TextField
                    fullWidth
                    label={vehicleBulkForm.priceAdjustment.type === 'percentage' ? 'Percentage (%)' : 'Amount ($)'}
                    type="number"
                    value={vehicleBulkForm.priceAdjustment.value}
                    onChange={(e) => setVehicleBulkForm({
                      ...vehicleBulkForm,
                      priceAdjustment: { ...vehicleBulkForm.priceAdjustment, value: parseFloat(e.target.value) || 0 }
                    })}
                  />
                </Box>
              )}
            </Box>
          );

        case 'bulk_notification':
          return (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="Title"
                value={notificationForm.title}
                onChange={(e) => setNotificationForm({ ...notificationForm, title: e.target.value })}
              />
              <TextField
                fullWidth
                label="Message"
                multiline
                rows={3}
                value={notificationForm.message}
                onChange={(e) => setNotificationForm({ ...notificationForm, message: e.target.value })}
              />
              <FormControl fullWidth>
                <InputLabel>Target</InputLabel>
                <Select
                  value={notificationForm.targetType}
                  label="Target"
                  onChange={(e) => setNotificationForm({ ...notificationForm, targetType: e.target.value })}
                >
                  <MenuItem value="all_users">All Users</MenuItem>
                  <MenuItem value="providers">Providers Only</MenuItem>
                  <MenuItem value="customers">Customers Only</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={notificationForm.type}
                  label="Type"
                  onChange={(e) => setNotificationForm({ ...notificationForm, type: e.target.value })}
                >
                  <MenuItem value="info">Info</MenuItem>
                  <MenuItem value="success">Success</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="error">Error</MenuItem>
                </Select>
              </FormControl>
            </Box>
          );

        case 'cleanup_notifications':
          return (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="Days Old"
                type="number"
                value={cleanupForm.daysOld}
                onChange={(e) => setCleanupForm({ ...cleanupForm, daysOld: parseInt(e.target.value) || 30 })}
                helperText="Delete read notifications older than this many days"
              />
            </Box>
          );

        default:
          return <Typography>No additional configuration needed for this operation.</Typography>;
      }
    };

    return (
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentOperation?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {getDialogContent()}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleOperation(currentOperation)}
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'Execute'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Bulk Operations
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Perform bulk operations to manage users, vehicles, and system data efficiently
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* User Operations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <UsersIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">User Operations</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Bulk update user statuses, delete users, or export user data
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><SuccessIcon color="success" /></ListItemIcon>
                  <ListItemText primary="Update user statuses" secondary="Activate, deactivate, or suspend multiple users" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><ErrorIcon color="error" /></ListItemIcon>
                  <ListItemText primary="Delete users" secondary="Soft delete multiple user accounts" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><ExportIcon color="info" /></ListItemIcon>
                  <ListItemText primary="Export user data" secondary="Download user data as CSV" />
                </ListItem>
              </List>
            </CardContent>
            <CardActions>
              <Button onClick={() => openOperationDialog('bulk_user_update')}>
                Update Users
              </Button>
              <Button onClick={() => handleOperation('export_users')}>
                Export Users
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Vehicle Operations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <VehiclesIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Vehicle Operations</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Bulk update vehicle statuses, pricing, or export vehicle data
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><SuccessIcon color="success" /></ListItemIcon>
                  <ListItemText primary="Update vehicle status" secondary="Activate or deactivate multiple vehicles" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><PricingIcon color="warning" /></ListItemIcon>
                  <ListItemText primary="Update pricing" secondary="Bulk adjust vehicle pricing" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><ExportIcon color="info" /></ListItemIcon>
                  <ListItemText primary="Export vehicle data" secondary="Download vehicle data as CSV" />
                </ListItem>
              </List>
            </CardContent>
            <CardActions>
              <Button onClick={() => openOperationDialog('bulk_vehicle_update')}>
                Update Vehicles
              </Button>
              <Button onClick={() => handleOperation('export_vehicles')}>
                Export Vehicles
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Notification Operations */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotificationIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Notification Operations</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Send bulk notifications to users or clean up old notifications
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><InfoIcon color="info" /></ListItemIcon>
                  <ListItemText primary="Send bulk notifications" secondary="Send notifications to all users, providers, or customers" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><CleanupIcon color="warning" /></ListItemIcon>
                  <ListItemText primary="Cleanup old notifications" secondary="Remove old read notifications" />
                </ListItem>
              </List>
            </CardContent>
            <CardActions>
              <Button onClick={() => openOperationDialog('bulk_notification')}>
                Send Notification
              </Button>
              <Button onClick={() => openOperationDialog('cleanup_notifications')}>
                Cleanup
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* System Cleanup */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CleanupIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">System Cleanup</Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Clean up expired sessions and optimize system performance
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><CleanupIcon color="success" /></ListItemIcon>
                  <ListItemText primary="Cleanup expired sessions" secondary="Remove expired user sessions" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><CleanupIcon color="info" /></ListItemIcon>
                  <ListItemText primary="Optimize database" secondary="Clean up temporary data and optimize performance" />
                </ListItem>
              </List>
            </CardContent>
            <CardActions>
              <Button onClick={() => handleOperation('cleanup_sessions')}>
                Cleanup Sessions
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>

      {/* Operation Result */}
      {operationResult && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Operation Result
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Chip
                  icon={<SuccessIcon />}
                  label={`Processed: ${operationResult.processed}`}
                  color="success"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Chip
                  icon={<ErrorIcon />}
                  label={`Failed: ${operationResult.failed}`}
                  color="error"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Chip
                  icon={operationResult.success ? <SuccessIcon /> : <ErrorIcon />}
                  label={operationResult.success ? 'Success' : 'Partial Failure'}
                  color={operationResult.success ? 'success' : 'warning'}
                />
              </Grid>
            </Grid>
            {operationResult.errors && operationResult.errors.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" color="error">
                  Errors:
                </Typography>
                <List dense>
                  {operationResult.errors.map((error: string, index: number) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <ErrorIcon color="error" />
                      </ListItemIcon>
                      <ListItemText primary={error} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {renderOperationDialog()}
    </Box>
  );
};

export default BulkOperations;
