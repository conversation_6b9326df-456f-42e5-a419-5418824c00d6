import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Badge,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  ListItemButton,
  ListItemIcon,
  Snackbar,
  InputAdornment,
  OutlinedInput,
  FormHelperText,
  Radio,
  RadioGroup,
  FormLabel,
  FormGroup,
  Checkbox,
  FormControlLabel,
  Rating,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  AttachMoney as MoneyIcon,
  TrendingDown as TrendingDownIcon,
  CheckCircle as CheckCircleIcon,
  Pending as PendingIcon,
  Cancel as CancelIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon,
  ExpandMore as ExpandMoreIcon,
  AdminPanelSettings as AdminIcon,
  Report as ReportIcon,
  SystemUpdate as SystemUpdateIcon,
  Backup as BackupIcon,
  Warning as EmergencyIcon,
  LocalOffer as DiscountIcon,
  Star as StarIcon,
  RateReview as ReviewIcon,
  Feedback as FeedbackIcon,
  Help as HelpIcon,
  ContactSupport as ContactIcon,
  BugReport as BugIcon,
  NewReleases as NewIcon,
  Update as UpdateIcon,
  Storage as StorageIcon,
  CloudUpload as CloudIcon,
  Sync as SyncIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  RestartAlt as RestartIcon,
  Power as PowerIcon,
  PowerOff as PowerOffIcon,
  PowerSettingsNew as PowerSettingsIcon,
  BatteryChargingFull as BatteryIcon,
  SignalCellular4Bar as SignalIcon,
  Wifi as WifiIcon,
  Bluetooth as BluetoothIcon,
  LocationOn as LocationIcon,
  MyLocation as MyLocationIcon,
  Navigation as NavigationIcon,
  Directions as DirectionsIcon,
  Map as MapIcon,
  Terrain as TerrainIcon,
  Streetview as StreetviewIcon,
  Satellite as SatelliteIcon,
  Public as PublicIcon,
  Language as LanguageIcon,
  Translate as TranslateIcon,
  GTranslate as GTranslateIcon,
  Translate as TranslateIcon2,
  GTranslate as GTranslateIcon2,
  Translate as TranslateIcon3,
  GTranslate as GTranslateIcon3,
  Translate as TranslateIcon4,
  GTranslate as GTranslateIcon4,
  Translate as TranslateIcon5,
  GTranslate as GTranslateIcon5,
  Translate as TranslateIcon6,
  GTranslate as GTranslateIcon6,
  Translate as TranslateIcon7,
  GTranslate as GTranslateIcon7,
  Translate as TranslateIcon8,
  GTranslate as GTranslateIcon8,
  Translate as TranslateIcon9,
  GTranslate as GTranslateIcon9,
  Translate as TranslateIcon10,
  GTranslate as GTranslateIcon10,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';
import { supabase } from '../utils/supabaseClient';
import ErrorBoundary from '../components/ErrorBoundary';

interface CashflowData {
  date: string;
  revenue: number;
  payouts: number;
  platformFees: number;
  netCashflow: number;
}

interface Transaction {
  id: string;
  type: 'REVENUE' | 'PAYOUT' | 'REFUND' | 'FEE';
  amount: number;
  description: string;
  status: 'COMPLETED' | 'PENDING' | 'FAILED';
  date: string;
  providerId?: string;
  providerName?: string;
  bookingId?: string;
}

interface CashflowStats {
  totalRevenue: number;
  totalPayouts: number;
  totalPlatformFees: number;
  netCashflow: number;
  pendingPayouts: number;
  monthlyGrowth: number;
  averageTransactionValue: number;
  totalTransactions: number;
}

export default function CashflowDashboard() {
  const [activeTab, setActiveTab] = useState(0);
  const [cashflowData, setCashflowData] = useState<CashflowData[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [stats, setStats] = useState<CashflowStats>({
    totalRevenue: 0,
    totalPayouts: 0,
    totalPlatformFees: 0,
    netCashflow: 0,
    pendingPayouts: 0,
    monthlyGrowth: 0,
    averageTransactionValue: 0,
    totalTransactions: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  const [dateRange, setDateRange] = useState('30'); // days
  
  // Search and filter states
  const [transactionSearch, setTransactionSearch] = useState('');
  const [typeFilter, setTypeFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);

  useEffect(() => {
    fetchCashflowData();
  }, [dateRange]);

  const fetchCashflowData = async () => {
    try {
      setLoading(true);
      
      // Mock cashflow data for the last 30 days
      const mockCashflowData: CashflowData[] = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        const baseRevenue = 5000000 + Math.random() * 2000000;
        const basePayouts = baseRevenue * 0.85;
        const platformFees = baseRevenue * 0.15;
        
        return {
          date: date.toISOString().split('T')[0],
          revenue: baseRevenue,
          payouts: basePayouts,
          platformFees: platformFees,
          netCashflow: platformFees,
        };
      });

      // Mock transactions
      const mockTransactions: Transaction[] = [
        {
          id: '1',
          type: 'REVENUE',
          amount: 250000,
          description: 'Booking #BK001 - Jakarta Motor Rentals',
          status: 'COMPLETED',
          date: '2024-01-20T10:30:00Z',
          providerId: 'provider1',
          providerName: 'Jakarta Motor Rentals',
          bookingId: 'BK001',
        },
        {
          id: '2',
          type: 'PAYOUT',
          amount: 212500,
          description: 'Payout to Jakarta Motor Rentals',
          status: 'COMPLETED',
          date: '2024-01-20T14:15:00Z',
          providerId: 'provider1',
          providerName: 'Jakarta Motor Rentals',
        },
        {
          id: '3',
          type: 'REVENUE',
          amount: 180000,
          description: 'Booking #BK002 - Bali Scooter Hub',
          status: 'COMPLETED',
          date: '2024-01-19T09:20:00Z',
          providerId: 'provider2',
          providerName: 'Bali Scooter Hub',
          bookingId: 'BK002',
        },
        {
          id: '4',
          type: 'PAYOUT',
          amount: 153000,
          description: 'Payout to Bali Scooter Hub',
          status: 'PENDING',
          date: '2024-01-19T16:45:00Z',
          providerId: 'provider2',
          providerName: 'Bali Scooter Hub',
        },
        {
          id: '5',
          type: 'REFUND',
          amount: -50000,
          description: 'Refund for cancelled booking #BK003',
          status: 'COMPLETED',
          date: '2024-01-18T11:30:00Z',
          bookingId: 'BK003',
        },
        {
          id: '6',
          type: 'FEE',
          amount: 37500,
          description: 'Platform fee from Jakarta Motor Rentals',
          status: 'COMPLETED',
          date: '2024-01-20T10:30:00Z',
          providerId: 'provider1',
          providerName: 'Jakarta Motor Rentals',
        },
      ];

      setCashflowData(mockCashflowData);
      setTransactions(mockTransactions);
      
      // Calculate stats
      const totalRevenue = mockCashflowData.reduce((sum, day) => sum + day.revenue, 0);
      const totalPayouts = mockCashflowData.reduce((sum, day) => sum + day.payouts, 0);
      const totalPlatformFees = mockCashflowData.reduce((sum, day) => sum + day.platformFees, 0);
      const netCashflow = totalPlatformFees;
      const pendingPayouts = mockTransactions
        .filter(t => t.type === 'PAYOUT' && t.status === 'PENDING')
        .reduce((sum, t) => sum + t.amount, 0);
      const monthlyGrowth = 12.5; // Mock percentage
      const averageTransactionValue = totalRevenue / mockTransactions.length;
      const totalTransactions = mockTransactions.length;

      setStats({
        totalRevenue,
        totalPayouts,
        totalPlatformFees,
        netCashflow,
        pendingPayouts,
        monthlyGrowth,
        averageTransactionValue,
        totalTransactions,
      });

      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch cashflow data:', error);
      setError('Failed to load cashflow data');
      setLoading(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'REVENUE': return 'success';
      case 'PAYOUT': return 'warning';
      case 'REFUND': return 'error';
      case 'FEE': return 'info';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const chartColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <ErrorBoundary>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            💰 Cashflow Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor platform revenue, payouts, and financial performance
          </Typography>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{formatCurrency(stats.totalRevenue)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PaymentIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{formatCurrency(stats.totalPayouts)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Payouts
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <AccountBalanceIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{formatCurrency(stats.totalPlatformFees)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Platform Fees
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PendingIcon color="error" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{formatCurrency(stats.pendingPayouts)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Payouts
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Cashflow Overview (Last 30 Days)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={cashflowData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip 
                      formatter={(value: number) => [formatCurrency(value), '']}
                      labelFormatter={(label) => new Date(label).toLocaleDateString()}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stackId="1" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      name="Revenue"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="payouts" 
                      stackId="1" 
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      name="Payouts"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Revenue vs Payouts
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Platform Fees', value: stats.totalPlatformFees },
                        { name: 'Provider Payouts', value: stats.totalPayouts },
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {chartColors.map((color, index) => (
                        <Cell key={`cell-${index}`} fill={color} />
                      ))}
                    </Pie>
                    <RechartsTooltip formatter={(value: number) => [formatCurrency(value), '']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Date Range Filter */}
        <Box sx={{ mb: 3 }}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Date Range</InputLabel>
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              label="Date Range"
            >
              <MenuItem value="7">Last 7 days</MenuItem>
              <MenuItem value="30">Last 30 days</MenuItem>
              <MenuItem value="90">Last 90 days</MenuItem>
              <MenuItem value="365">Last year</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Transactions" />
            <Tab label="Revenue Analysis" />
            <Tab label="Payout Summary" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {activeTab === 0 && (
          <Box>
            {/* Search and Filters */}
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="Search transactions..."
                    value={transactionSearch}
                    onChange={(e) => setTransactionSearch(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Type</InputLabel>
                    <Select
                      multiple
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value as string[])}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      <MenuItem value="REVENUE">Revenue</MenuItem>
                      <MenuItem value="PAYOUT">Payout</MenuItem>
                      <MenuItem value="REFUND">Refund</MenuItem>
                      <MenuItem value="FEE">Fee</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      multiple
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value as string[])}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      <MenuItem value="COMPLETED">Completed</MenuItem>
                      <MenuItem value="PENDING">Pending</MenuItem>
                      <MenuItem value="FAILED">Failed</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    fullWidth
                    onClick={() => {
                      // TODO: Export transactions
                      console.log('Export transactions');
                    }}
                  >
                    Export
                  </Button>
                </Grid>
              </Grid>
            </Box>

            {/* Transactions Table */}
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Transaction</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Provider</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{transaction.description}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            #{transaction.id}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.type}
                          color={getTypeColor(transaction.type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography 
                          variant="body2" 
                          color={transaction.amount >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatCurrency(transaction.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.status}
                          color={getStatusColor(transaction.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {transaction.providerName || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(transaction.date)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedTransaction(transaction);
                              setShowTransactionDetails(true);
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {activeTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Revenue Growth
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <LineChart data={cashflowData.slice(-7)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip 
                        formatter={(value: number) => [formatCurrency(value), '']}
                        labelFormatter={(label) => new Date(label).toLocaleDateString()}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="revenue" 
                        stroke="#8884d8" 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Platform Fees Trend
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={cashflowData.slice(-7)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip 
                        formatter={(value: number) => [formatCurrency(value), '']}
                        labelFormatter={(label) => new Date(label).toLocaleDateString()}
                      />
                      <Bar dataKey="platformFees" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {activeTab === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Payout Summary
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Provider</TableCell>
                          <TableCell>Total Earnings</TableCell>
                          <TableCell>Paid Out</TableCell>
                          <TableCell>Pending</TableCell>
                          <TableCell>Next Payout</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>Jakarta Motor Rentals</TableCell>
                          <TableCell>{formatCurrency(25000000)}</TableCell>
                          <TableCell>{formatCurrency(21250000)}</TableCell>
                          <TableCell>{formatCurrency(3750000)}</TableCell>
                          <TableCell>2024-01-25</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Bali Scooter Hub</TableCell>
                          <TableCell>{formatCurrency(18000000)}</TableCell>
                          <TableCell>{formatCurrency(15300000)}</TableCell>
                          <TableCell>{formatCurrency(2700000)}</TableCell>
                          <TableCell>2024-01-22</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Payout Status
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                      <Typography variant="body2">Completed</Typography>
                      <Typography variant="body2" color="success.main">
                        {formatCurrency(stats.totalPayouts)}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                      <Typography variant="body2">Pending</Typography>
                      <Typography variant="body2" color="warning.main">
                        {formatCurrency(stats.pendingPayouts)}
                      </Typography>
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2">Total</Typography>
                      <Typography variant="subtitle2">
                        {formatCurrency(stats.totalPayouts + stats.pendingPayouts)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Transaction Details Dialog */}
        <Dialog
          open={showTransactionDetails}
          onClose={() => setShowTransactionDetails(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Transaction Details</DialogTitle>
          <DialogContent>
            {selectedTransaction && (
              <Box>
                <List>
                  <ListItem>
                    <ListItemText
                      primary="Transaction ID"
                      secondary={selectedTransaction.id}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Type"
                      secondary={
                        <Chip
                          label={selectedTransaction.type}
                          color={getTypeColor(selectedTransaction.type) as any}
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Amount"
                      secondary={
                        <Typography 
                          variant="body1" 
                          color={selectedTransaction.amount >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatCurrency(selectedTransaction.amount)}
                        </Typography>
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Status"
                      secondary={
                        <Chip
                          label={selectedTransaction.status}
                          color={getStatusColor(selectedTransaction.status) as any}
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Description"
                      secondary={selectedTransaction.description}
                    />
                  </ListItem>
                  {selectedTransaction.providerName && (
                    <ListItem>
                      <ListItemText
                        primary="Provider"
                        secondary={selectedTransaction.providerName}
                      />
                    </ListItem>
                  )}
                  {selectedTransaction.bookingId && (
                    <ListItem>
                      <ListItemText
                        primary="Booking ID"
                        secondary={selectedTransaction.bookingId}
                      />
                    </ListItem>
                  )}
                  <ListItem>
                    <ListItemText
                      primary="Date"
                      secondary={formatDate(selectedTransaction.date)}
                    />
                  </ListItem>
                </List>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowTransactionDetails(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </ErrorBoundary>
  );
} 