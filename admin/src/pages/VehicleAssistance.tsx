import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Badge,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Build as BuildIcon,
  CarCrash as AccidentIcon,
  LocalGasStation as FuelIcon,
  BatteryAlert as BatteryIcon,
  Key as KeyIcon,
  Engineering as MechanicalIcon,
  ElectricalServices as ElectricalIcon,
  Help as HelpIcon
} from '@mui/icons-material';

interface AssistanceRequest {
  id: string;
  userId: string;
  bookingId?: string;
  providerId?: string;
  vehicleId?: string;
  category: string;
  priority: string;
  status: string;
  reason: string;
  message?: string;
  location?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  user?: {
    id: string;
    name: string;
    email: string;
    phoneNumber?: string;
  };
  provider?: {
    id: string;
    name: string;
    email: string;
    phoneNumber?: string;
  };
  vehicle?: {
    id: string;
    brand: string;
    model: string;
    vehicleType: string;
  };
  booking?: {
    id: string;
    startDate: string;
    endDate: string;
  };
}

interface AssistanceStats {
  total: number;
  pending: number;
  urgent: number;
  resolved: number;
  categoryBreakdown: Array<{ category: string; _count: { category: number } }>;
  priorityBreakdown: Array<{ priority: string; _count: { priority: number } }>;
}

const VehicleAssistance: React.FC = () => {
  const [requests, setRequests] = useState<AssistanceRequest[]>([]);
  const [stats, setStats] = useState<AssistanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<AssistanceRequest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [updateOpen, setUpdateOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [updateData, setUpdateData] = useState({
    status: '',
    adminNotes: ''
  });

  const statusFilters = ['ALL', 'PENDING', 'ASSIGNED', 'IN_PROGRESS', 'RESOLVED', 'CANCELLED'];

  useEffect(() => {
    fetchData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      setError(null);
      
      // Mock data for demonstration - replace with actual API calls
      const mockRequests: AssistanceRequest[] = [
        {
          id: '1',
          userId: 'user1',
          category: 'BREAKDOWN',
          priority: 'HIGH',
          status: 'PENDING',
          reason: 'Engine stopped working suddenly',
          location: 'Jl. Sudirman No. 123, Jakarta',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          user: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>',
            phoneNumber: '+62812345678'
          }
        },
        {
          id: '2',
          userId: 'user2',
          category: 'FLAT_TIRE',
          priority: 'MEDIUM',
          status: 'IN_PROGRESS',
          reason: 'Front tire is flat',
          location: 'Bali, Denpasar',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          updatedAt: new Date().toISOString(),
          adminNotes: 'Technician dispatched',
          user: {
            id: 'user2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            phoneNumber: '+62887654321'
          }
        }
      ];

      const mockStats: AssistanceStats = {
        total: 25,
        pending: 8,
        urgent: 3,
        resolved: 12,
        categoryBreakdown: [
          { category: 'BREAKDOWN', _count: { category: 8 } },
          { category: 'FLAT_TIRE', _count: { category: 6 } },
          { category: 'ACCIDENT', _count: { category: 2 } }
        ],
        priorityBreakdown: [
          { priority: 'URGENT', _count: { priority: 3 } },
          { priority: 'HIGH', _count: { priority: 7 } },
          { priority: 'MEDIUM', _count: { priority: 10 } },
          { priority: 'LOW', _count: { priority: 5 } }
        ]
      };

      setRequests(mockRequests);
      setStats(mockStats);
    } catch (error) {
      console.error('Error fetching assistance data:', error);
      setError('Failed to fetch assistance data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async () => {
    if (!selectedRequest) return;

    try {
      // Mock update - replace with actual API call
      console.log('Updating request:', selectedRequest.id, updateData);
      
      await fetchData(); // Refresh data
      setUpdateOpen(false);
      setSelectedRequest(null);
      setUpdateData({ status: '', adminNotes: '' });
    } catch (error) {
      console.error('Error updating request:', error);
      setError('Failed to update request');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'warning';
      case 'ASSIGNED':
        return 'info';
      case 'IN_PROGRESS':
        return 'primary';
      case 'RESOLVED':
        return 'success';
      case 'CANCELLED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'error';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      case 'LOW':
        return 'default';
      default:
        return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'BREAKDOWN':
        return <BuildIcon />;
      case 'ACCIDENT':
        return <AccidentIcon />;
      case 'FLAT_TIRE':
        return <BuildIcon />;
      case 'FUEL_EMPTY':
        return <FuelIcon />;
      case 'BATTERY_DEAD':
        return <BatteryIcon />;
      case 'KEY_LOCKED':
        return <KeyIcon />;
      case 'MECHANICAL_ISSUE':
        return <MechanicalIcon />;
      case 'ELECTRICAL_ISSUE':
        return <ElectricalIcon />;
      default:
        return <HelpIcon />;
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleViewDetails = (request: AssistanceRequest) => {
    setSelectedRequest(request);
    setDetailsOpen(true);
  };

  const handleEditRequest = (request: AssistanceRequest) => {
    setSelectedRequest(request);
    setUpdateData({
      status: request.status,
      adminNotes: request.adminNotes || ''
    });
    setUpdateOpen(true);
  };

  const getFilteredRequests = () => {
    let filtered = requests;
    
    if (currentTab > 0) {
      const statusFilter = statusFilters[currentTab];
      if (statusFilter !== 'ALL') {
        filtered = filtered.filter(req => req.status === statusFilter);
      }
    }
    
    return filtered;
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight={400}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          🚗 Vehicle Assistance Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Monitor and manage vehicle assistance requests from users
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Requests
                </Typography>
                <Typography variant="h4">
                  {stats.total}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Pending
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {stats.pending}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Urgent
                </Typography>
                <Typography variant="h4" color="error.main">
                  {stats.urgent}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Resolved
                </Typography>
                <Typography variant="h4" color="success.main">
                  {stats.resolved}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Main Content */}
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">
              Assistance Requests
            </Typography>
            <Tooltip title="Refresh">
              <IconButton onClick={fetchData}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Status Filter Tabs */}
          <Tabs
            value={currentTab}
            onChange={(_, newValue) => setCurrentTab(newValue)}
            sx={{ mb: 3 }}
          >
            {statusFilters.map((status, index) => (
              <Tab
                key={status}
                label={
                  <Badge
                    badgeContent={
                      status === 'ALL' 
                        ? requests.length 
                        : requests.filter(req => req.status === status).length
                    }
                    color="primary"
                  >
                    {status.replace('_', ' ')}
                  </Badge>
                }
              />
            ))}
          </Tabs>

          {/* Requests Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Category</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getFilteredRequests().map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getCategoryIcon(request.category)}
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {request.category.replace('_', ' ')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {request.reason.substring(0, 30)}...
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={request.priority}
                        color={getPriorityColor(request.priority) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={request.status.replace('_', ' ')}
                        color={getStatusColor(request.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {request.user?.name || 'Unknown'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {request.user?.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {request.location || 'Not provided'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDateTime(request.createdAt)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewDetails(request)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Status">
                          <IconButton
                            size="small"
                            onClick={() => handleEditRequest(request)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        {request.user?.phoneNumber && (
                          <Tooltip title="Call User">
                            <IconButton
                              size="small"
                              href={`tel:${request.user.phoneNumber}`}
                            >
                              <PhoneIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        {request.user?.email && (
                          <Tooltip title="Email User">
                            <IconButton
                              size="small"
                              href={`mailto:${request.user.email}`}
                            >
                              <EmailIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Assistance Request Details
        </DialogTitle>
        <DialogContent>
          {selectedRequest && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Category
                </Typography>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  {getCategoryIcon(selectedRequest.category)}
                  <Typography variant="body1">
                    {selectedRequest.category.replace('_', ' ')}
                  </Typography>
                </Box>
                
                <Typography variant="subtitle2" gutterBottom>
                  Reason
                </Typography>
                <Typography variant="body1" paragraph>
                  {selectedRequest.reason}
                </Typography>
                
                {selectedRequest.message && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      Additional Message
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {selectedRequest.message}
                    </Typography>
                  </>
                )}
                
                {selectedRequest.location && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      Location
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <LocationIcon fontSize="small" />
                      <Typography variant="body1">
                        {selectedRequest.location}
                      </Typography>
                    </Box>
                  </>
                )}
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>
                  User Information
                </Typography>
                {selectedRequest.user && (
                  <Box mb={2}>
                    <Typography variant="body1">{selectedRequest.user.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedRequest.user.email}
                    </Typography>
                    {selectedRequest.user.phoneNumber && (
                      <Typography variant="body2" color="text.secondary">
                        {selectedRequest.user.phoneNumber}
                      </Typography>
                    )}
                  </Box>
                )}
                
                <Typography variant="subtitle2" gutterBottom>
                  Status & Priority
                </Typography>
                <Box display="flex" gap={1} mb={2}>
                  <Chip
                    label={selectedRequest.priority}
                    color={getPriorityColor(selectedRequest.priority) as any}
                    size="small"
                  />
                  <Chip
                    label={selectedRequest.status.replace('_', ' ')}
                    color={getStatusColor(selectedRequest.status) as any}
                    size="small"
                  />
                </Box>
                
                <Typography variant="subtitle2" gutterBottom>
                  Timestamps
                </Typography>
                <Typography variant="body2">
                  <strong>Created:</strong> {formatDateTime(selectedRequest.createdAt)}
                </Typography>
                <Typography variant="body2">
                  <strong>Updated:</strong> {formatDateTime(selectedRequest.updatedAt)}
                </Typography>
                {selectedRequest.resolvedAt && (
                  <Typography variant="body2">
                    <strong>Resolved:</strong> {formatDateTime(selectedRequest.resolvedAt)}
                  </Typography>
                )}
                
                {selectedRequest.adminNotes && (
                  <>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                      Admin Notes
                    </Typography>
                    <Alert severity="info">
                      <Typography variant="body2">
                        {selectedRequest.adminNotes}
                      </Typography>
                    </Alert>
                  </>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>
            Close
          </Button>
          {selectedRequest && (
            <Button
              variant="contained"
              onClick={() => handleEditRequest(selectedRequest)}
            >
              Edit Status
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog
        open={updateOpen}
        onClose={() => setUpdateOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Update Assistance Request
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={updateData.status}
                  label="Status"
                  onChange={(e) => setUpdateData(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="PENDING">Pending</MenuItem>
                  <MenuItem value="ASSIGNED">Assigned</MenuItem>
                  <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                  <MenuItem value="RESOLVED">Resolved</MenuItem>
                  <MenuItem value="CANCELLED">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Admin Notes"
                value={updateData.adminNotes}
                onChange={(e) => setUpdateData(prev => ({ ...prev, adminNotes: e.target.value }))}
                placeholder="Add notes for the user..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpdateOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleUpdateStatus}
            disabled={!updateData.status}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default VehicleAssistance;
