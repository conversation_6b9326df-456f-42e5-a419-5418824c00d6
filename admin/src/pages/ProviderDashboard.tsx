import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  LinearProgress,
  Badge,
  Tooltip,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  DirectionsCar as CarIcon,
  BookOnline as BookingIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as AnalyticsIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Warning as WarningIcon,
  Notifications as NotificationsIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  LocalShipping as FleetIcon,
  MonetizationOn as RevenueIcon,
  CalendarToday as CalendarIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface FleetVehicle {
  id: string;
  vehicleType: string;
  brand: string;
  model: string;
  year: number;
  licensePlate: string;
  dailyRate: number;
  status: 'AVAILABLE' | 'UNAVAILABLE' | 'MAINTENANCE' | 'RESERVED' | 'IN_USE';
  location: string;
  mileage: number;
  fuelType: string;
  transmission: string;
  seats: number;
  lastMaintenance: string;
  nextMaintenance: string;
  totalEarnings: number;
  totalBookings: number;
  averageRating: number;
  imageUrl?: string;
}

interface FleetBooking {
  id: string;
  vehicleId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  startDate: string;
  endDate: string;
  totalPrice: number;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  paymentStatus: 'PENDING' | 'PAID' | 'REFUNDED';
  pickupLocation: string;
  dropoffLocation: string;
  createdAt: string;
  vehicle: {
    vehicleType: string;
    brand: string;
    model: string;
    licensePlate: string;
  };
}

interface FleetStats {
  totalVehicles: number;
  availableVehicles: number;
  inUseVehicles: number;
  maintenanceVehicles: number;
  totalBookings: number;
  activeBookings: number;
  completedBookings: number;
  totalRevenue: number;
  monthlyRevenue: number;
  averageRating: number;
  totalCustomers: number;
  repeatCustomers: number;
}

interface RevenueData {
  date: string;
  revenue: number;
  bookings: number;
}

interface VehiclePerformance {
  vehicleId: string;
  vehicleType: string;
  totalEarnings: number;
  totalBookings: number;
  averageRating: number;
  utilizationRate: number;
}

export default function ProviderDashboard() {
  const [activeTab, setActiveTab] = useState(0);
  const [vehicles, setVehicles] = useState<FleetVehicle[]>([]);
  const [bookings, setBookings] = useState<FleetBooking[]>([]);
  const [stats, setStats] = useState<FleetStats>({
    totalVehicles: 0,
    availableVehicles: 0,
    inUseVehicles: 0,
    maintenanceVehicles: 0,
    totalBookings: 0,
    activeBookings: 0,
    completedBookings: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    averageRating: 0,
    totalCustomers: 0,
    repeatCustomers: 0
  });
  const [loading, setLoading] = useState(true);
  const [showAddVehicle, setShowAddVehicle] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<FleetVehicle | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<FleetBooking | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [vehiclePerformance, setVehiclePerformance] = useState<VehiclePerformance[]>([]);

  // Mock data for demonstration
  const mockVehicles: FleetVehicle[] = [
    {
      id: '1',
      vehicleType: 'Sedan',
      brand: 'Toyota',
      model: 'Camry',
      year: 2022,
      licensePlate: 'ABC-123',
      dailyRate: 75,
      status: 'AVAILABLE',
      location: 'Jakarta Central',
      mileage: 15000,
      fuelType: 'Gasoline',
      transmission: 'Automatic',
      seats: 5,
      lastMaintenance: '2024-01-15',
      nextMaintenance: '2024-04-15',
      totalEarnings: 4500,
      totalBookings: 12,
      averageRating: 4.5
    },
    {
      id: '2',
      vehicleType: 'SUV',
      brand: 'Honda',
      model: 'CR-V',
      year: 2023,
      licensePlate: 'XYZ-789',
      dailyRate: 95,
      status: 'IN_USE',
      location: 'Jakarta South',
      mileage: 8000,
      fuelType: 'Gasoline',
      transmission: 'Automatic',
      seats: 7,
      lastMaintenance: '2024-02-01',
      nextMaintenance: '2024-05-01',
      totalEarnings: 3800,
      totalBookings: 8,
      averageRating: 4.8
    },
    {
      id: '3',
      vehicleType: 'Motorcycle',
      brand: 'Yamaha',
      model: 'NMAX',
      year: 2023,
      licensePlate: 'MTR-456',
      dailyRate: 35,
      status: 'MAINTENANCE',
      location: 'Jakarta West',
      mileage: 25000,
      fuelType: 'Gasoline',
      transmission: 'Automatic',
      seats: 2,
      lastMaintenance: '2024-03-01',
      nextMaintenance: '2024-06-01',
      totalEarnings: 2100,
      totalBookings: 15,
      averageRating: 4.2
    }
  ];

  const mockBookings: FleetBooking[] = [
    {
      id: '1',
      vehicleId: '1',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+62-812-3456-7890',
      startDate: '2024-03-15',
      endDate: '2024-03-17',
      totalPrice: 225,
      status: 'CONFIRMED',
      paymentStatus: 'PAID',
      pickupLocation: 'Jakarta Central',
      dropoffLocation: 'Jakarta Central',
      createdAt: '2024-03-10',
      vehicle: {
        vehicleType: 'Sedan',
        brand: 'Toyota',
        model: 'Camry',
        licensePlate: 'ABC-123'
      }
    },
    {
      id: '2',
      vehicleId: '2',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      customerPhone: '+62-813-4567-8901',
      startDate: '2024-03-20',
      endDate: '2024-03-22',
      totalPrice: 285,
      status: 'IN_PROGRESS',
      paymentStatus: 'PAID',
      pickupLocation: 'Jakarta South',
      dropoffLocation: 'Jakarta South',
      createdAt: '2024-03-12',
      vehicle: {
        vehicleType: 'SUV',
        brand: 'Honda',
        model: 'CR-V',
        licensePlate: 'XYZ-789'
      }
    }
  ];

  useEffect(() => {
    fetchProviderData();
  }, []);

  const fetchProviderData = async () => {
    try {
      setLoading(true);

      // Import the service dynamically to avoid circular imports
      const { SupabaseDashboardService } = await import('../services/SupabaseDashboardService');

      // For demo purposes, use a mock provider ID
      // In a real app, this would come from authentication context
      const providerId = 'demo-provider-id';

      // Fetch live data from Supabase
      const providerData = await SupabaseDashboardService.getProviderDashboardData(providerId);

      setVehicles(providerData.vehicles);
      setBookings(providerData.bookings);
      setStats(providerData.stats);


      // Generate revenue data from bookings
      const monthlyRevenue = Array.from({ length: 6 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - (5 - i));
        const monthName = date.toLocaleDateString('en', { month: 'short' });

        const monthBookings = providerData.bookings.filter((b: any) => {
          const bookingDate = new Date(b.created_at);
          return bookingDate.getMonth() === date.getMonth() &&
                 bookingDate.getFullYear() === date.getFullYear() &&
                 b.booking_status === 'completed';
        });

        return {
          date: monthName,
          revenue: monthBookings.reduce((sum: number, b: any) => sum + (b.total || 0), 0),
          bookings: monthBookings.length
        };
      });

      setRevenueData(monthlyRevenue);

      // Generate vehicle performance data
      const vehiclePerf = providerData.vehicles.map((v: any) => {
        const vehicleBookings = providerData.bookings.filter((b: any) => b.vehicle_id === v.id);
        const completedBookings = vehicleBookings.filter((b: any) => b.booking_status === 'completed');

        return {
          vehicleId: v.id,
          vehicleType: v.category || 'Vehicle',
          totalEarnings: completedBookings.reduce((sum: number, b: any) => sum + (b.total || 0), 0),
          totalBookings: vehicleBookings.length,
          averageRating: 4.5, // Default rating
          utilizationRate: vehicleBookings.length > 0 ? (completedBookings.length / vehicleBookings.length) * 100 : 0
        };
      });

      setVehiclePerformance(vehiclePerf);

      if (providerData.vehicles.length === 0) {
        setError('No vehicles found. Add some vehicles to see your provider dashboard data.');
      } else {
        setError(null);
      }

    } catch (error) {
      console.error('Error fetching provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVehicleAction = async (vehicleId: string, action: 'edit' | 'delete' | 'maintenance') => {
    // Handle vehicle actions
    console.log(`Vehicle action: ${action} for vehicle ${vehicleId}`);
  };

  const handleBookingAction = async (bookingId: string, action: 'confirm' | 'cancel' | 'complete') => {
    // Handle booking actions
    console.log(`Booking action: ${action} for booking ${bookingId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'success';
      case 'IN_USE': return 'primary';
      case 'MAINTENANCE': return 'warning';
      case 'RESERVED': return 'info';
      default: return 'default';
    }
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED': return 'success';
      case 'IN_PROGRESS': return 'primary';
      case 'COMPLETED': return 'info';
      case 'CANCELLED': return 'error';
      case 'PENDING': return 'warning';
      default: return 'default';
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Fleet Management Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your fleet, track revenue, and monitor bookings
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Fleet
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalVehicles}
                  </Typography>
                </Box>
                <FleetIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={2}>
                <Typography variant="body2" color="text.secondary">
                  {stats.availableVehicles} available • {stats.inUseVehicles} in use
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Revenue
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    ${stats.totalRevenue.toLocaleString()}
                  </Typography>
                </Box>
                <RevenueIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={2}>
                <Typography variant="body2" color="text.secondary">
                  ${stats.monthlyRevenue.toLocaleString()} this month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Bookings
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.activeBookings}
                  </Typography>
                </Box>
                <BookingIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={2}>
                <Typography variant="body2" color="text.secondary">
                  {stats.totalBookings} total bookings
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Average Rating
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.averageRating.toFixed(1)}
                  </Typography>
                </Box>
                <StarIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={2}>
                <Typography variant="body2" color="text.secondary">
                  {stats.totalCustomers} total customers
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box mb={3}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Fleet Overview" />
          <Tab label="Bookings" />
          <Tab label="Revenue Analytics" />
          <Tab label="Maintenance" />
          <Tab label="Settings" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">Fleet Overview</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setShowAddVehicle(true)}
            >
              Add Vehicle
            </Button>
          </Box>
          
          <Grid container spacing={3}>
            {vehicles.map((vehicle) => (
              <Grid item xs={12} md={6} lg={4} key={vehicle.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                      <Box>
                        <Typography variant="h6" fontWeight="bold">
                          {vehicle.brand} {vehicle.model}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {vehicle.year} • {vehicle.licensePlate}
                        </Typography>
                      </Box>
                      <Chip
                        label={vehicle.status}
                        color={getStatusColor(vehicle.status) as any}
                        size="small"
                      />
                    </Box>

                    <Box display="flex" justifyContent="space-between" mb={2}>
                      <Typography variant="body2">
                        <strong>Daily Rate:</strong> ${vehicle.dailyRate}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Location:</strong> {vehicle.location}
                      </Typography>
                    </Box>

                    <Box display="flex" justifyContent="space-between" mb={2}>
                      <Typography variant="body2">
                        <strong>Total Earnings:</strong> ${vehicle.totalEarnings}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Bookings:</strong> {vehicle.totalBookings}
                      </Typography>
                    </Box>

                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <StarIcon sx={{ fontSize: 16, color: 'gold' }} />
                      <Typography variant="body2">
                        {vehicle.averageRating} ({vehicle.totalBookings} reviews)
                      </Typography>
                    </Box>

                    <Box display="flex" gap={1}>
                      <IconButton size="small" color="primary">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="warning">
                        <WarningIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {activeTab === 1 && (
        <Box>
          <Typography variant="h6" mb={3}>Booking Management</Typography>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Booking ID</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Vehicle</TableCell>
                  <TableCell>Dates</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Payment</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bookings.map((booking) => (
                  <TableRow key={booking.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {booking.id.slice(0, 8)}...
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {booking.customerName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.customerEmail}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {booking.vehicle.brand} {booking.vehicle.model}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.vehicle.licensePlate}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        ${booking.totalPrice}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={booking.status}
                        color={getBookingStatusColor(booking.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={booking.paymentStatus}
                        color={booking.paymentStatus === 'PAID' ? 'success' : 'warning'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton size="small" color="primary">
                          <ViewIcon />
                        </IconButton>
                        <IconButton size="small" color="success">
                          <ApproveIcon />
                        </IconButton>
                        <IconButton size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {activeTab === 2 && (
        <Box>
          <Typography variant="h6" mb={3}>Revenue Analytics</Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" mb={2}>Revenue Trend</Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <RechartsTooltip />
                      <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" mb={2}>Vehicle Performance</Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={vehiclePerformance}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ vehicleType, totalEarnings }) => `${vehicleType}: $${totalEarnings}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="totalEarnings"
                      >
                        {vehiclePerformance.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {activeTab === 3 && (
        <Box>
          <Typography variant="h6" mb={3}>Maintenance Schedule</Typography>
          
          <Grid container spacing={3}>
            {vehicles.filter(v => v.status === 'MAINTENANCE' || new Date(v.nextMaintenance) <= new Date()).map((vehicle) => (
              <Grid item xs={12} md={6} key={vehicle.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="h6">
                        {vehicle.brand} {vehicle.model}
                      </Typography>
                      <Chip
                        label={vehicle.status === 'MAINTENANCE' ? 'In Maintenance' : 'Due Soon'}
                        color={vehicle.status === 'MAINTENANCE' ? 'warning' : 'error'}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" mb={1}>
                      <strong>Last Maintenance:</strong> {new Date(vehicle.lastMaintenance).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2" mb={1}>
                      <strong>Next Maintenance:</strong> {new Date(vehicle.nextMaintenance).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2" mb={2}>
                      <strong>Mileage:</strong> {vehicle.mileage.toLocaleString()} km
                    </Typography>

                    <Box display="flex" gap={1}>
                      <Button size="small" variant="outlined">
                        Schedule Service
                      </Button>
                      <Button size="small" variant="outlined">
                        View History
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {activeTab === 4 && (
        <Box>
          <Typography variant="h6" mb={3}>Provider Settings</Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" mb={2}>Business Information</Typography>
                  <TextField
                    fullWidth
                    label="Business Name"
                    defaultValue="My Fleet Services"
                    margin="normal"
                  />
                  <TextField
                    fullWidth
                    label="Contact Email"
                    defaultValue="<EMAIL>"
                    margin="normal"
                  />
                  <TextField
                    fullWidth
                    label="Phone Number"
                    defaultValue="+62-812-3456-7890"
                    margin="normal"
                  />
                  <TextField
                    fullWidth
                    label="Business Address"
                    defaultValue="Jakarta, Indonesia"
                    margin="normal"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" mb={2}>Notification Settings</Typography>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Email notifications for new bookings"
                  />
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="SMS notifications for urgent matters"
                  />
                  <FormControlLabel
                    control={<Switch />}
                    label="Maintenance reminders"
                  />
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Revenue reports"
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setShowAddVehicle(true)}
      >
        <AddIcon />
      </Fab>
    </Container>
  );
} 