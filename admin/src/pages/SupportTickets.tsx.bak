import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Tabs,
  Tab,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Badge,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  ListItemButton,
  ListItemIcon,
  Snackbar,
  InputAdornment,
  OutlinedInput,
  FormHelperText,
  Radio,
  RadioGroup,
  FormLabel,
  FormGroup,
  Checkbox,
  FormControlLabel,
  Rating,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/lab';
import {
  Support as SupportIcon,
  Assignment as AssignmentIcon,
  PriorityHigh as PriorityHighIcon,
  LowPriority as LowPriorityIcon,
  CheckCircle as CheckCircleIcon,
  Pending as PendingIcon,
  Cancel as CancelIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon,
  ExpandMore as ExpandMoreIcon,
  AdminPanelSettings as AdminIcon,
  Report as ReportIcon,
  SystemUpdate as SystemUpdateIcon,
  Backup as BackupIcon,
  Warning as EmergencyIcon,
  LocalOffer as DiscountIcon,
  Star as StarIcon,
  RateReview as ReviewIcon,
  Feedback as FeedbackIcon,
  Help as HelpIcon,
  ContactSupport as ContactIcon,
  BugReport as BugIcon,
  NewReleases as NewIcon,
  Update as UpdateIcon,
  Storage as StorageIcon,
  CloudUpload as CloudIcon,
  Sync as SyncIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  RestartAlt as RestartIcon,
  Power as PowerIcon,
  PowerOff as PowerOffIcon,
  PowerSettingsNew as PowerSettingsIcon,
  BatteryChargingFull as BatteryIcon,
  SignalCellular4Bar as SignalIcon,
  Wifi as WifiIcon,
  Bluetooth as BluetoothIcon,
  LocationOn as LocationIcon,
  MyLocation as MyLocationIcon,
  Navigation as NavigationIcon,
  Directions as DirectionsIcon,
  Map as MapIcon,
  Terrain as TerrainIcon,
  Streetview as StreetviewIcon,
  Satellite as SatelliteIcon,
  Public as PublicIcon,
  Language as LanguageIcon,
  Translate as TranslateIcon,
  GTranslate as GTranslateIcon,
  Translate as TranslateIcon2,
  GTranslate as GTranslateIcon2,
  Translate as TranslateIcon3,
  GTranslate as GTranslateIcon3,
  Translate as TranslateIcon4,
  GTranslate as GTranslateIcon4,
  Translate as TranslateIcon5,
  GTranslate as GTranslateIcon5,
  Translate as TranslateIcon6,
  GTranslate as GTranslateIcon6,
  Translate as TranslateIcon7,
  GTranslate as GTranslateIcon7,
  Translate as TranslateIcon8,
  GTranslate as GTranslateIcon8,
  Translate as TranslateIcon9,
  GTranslate as GTranslateIcon9,
  Translate as TranslateIcon10,
  GTranslate as GTranslateIcon10,
} from '@mui/icons-material';
import { supabase } from '../utils/supabaseClient';
import ErrorBoundary from '../components/ErrorBoundary';

interface SupportTicket {
  id: string;
  title: string;
  description: string;
  category: 'TECHNICAL' | 'BILLING' | 'BOOKING' | 'ACCOUNT' | 'GENERAL';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
  assignedTo?: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
    role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN';
  };
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  attachments?: string[];
  comments: TicketComment[];
  satisfaction?: number;
}

interface TicketComment {
  id: string;
  author: {
    id: string;
    name: string;
    role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN' | 'SUPPORT';
  };
  content: string;
  timestamp: string;
  isInternal: boolean;
}

interface TicketStats {
  totalTickets: number;
  openTickets: number;
  inProgressTickets: number;
  resolvedTickets: number;
  urgentTickets: number;
  averageResolutionTime: number;
  customerSatisfaction: number;
  ticketsByCategory: Record<string, number>;
}

export default function SupportTickets() {
  const [activeTab, setActiveTab] = useState(0);
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<SupportTicket[]>([]);
  const [stats, setStats] = useState<TicketStats>({
    totalTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    resolvedTickets: 0,
    urgentTickets: 0,
    averageResolutionTime: 0,
    customerSatisfaction: 0,
    ticketsByCategory: {},
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  
  // Search and filter states
  const [ticketSearch, setTicketSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [priorityFilter, setPriorityFilter] = useState<string[]>([]);
  const [categoryFilter, setCategoryFilter] = useState<string[]>([]);

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    filterTickets();
  }, [tickets, ticketSearch, statusFilter, priorityFilter, categoryFilter]);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockTickets: SupportTicket[] = [
        {
          id: '1',
          title: 'Cannot complete booking payment',
          description: 'I\'m trying to book a motorcycle but the payment keeps failing. I\'ve tried multiple cards.',
          category: 'BOOKING',
          priority: 'HIGH',
          status: 'IN_PROGRESS',
          assignedTo: '<EMAIL>',
          createdBy: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'CUSTOMER',
          },
          createdAt: '2024-01-20T10:30:00Z',
          updatedAt: '2024-01-20T14:15:00Z',
          comments: [
            {
              id: '1',
              author: {
                id: 'user1',
                name: 'John Doe',
                role: 'CUSTOMER',
              },
              content: 'I\'m trying to book a motorcycle but the payment keeps failing. I\'ve tried multiple cards.',
              timestamp: '2024-01-20T10:30:00Z',
              isInternal: false,
            },
            {
              id: '2',
              author: {
                id: 'support1',
                name: 'Support Team',
                role: 'SUPPORT',
              },
              content: 'Thank you for reporting this issue. We\'re investigating the payment gateway. Can you try using a different browser?',
              timestamp: '2024-01-20T11:00:00Z',
              isInternal: false,
            },
            {
              id: '3',
              author: {
                id: 'admin1',
                name: 'Admin User',
                role: 'ADMIN',
              },
              content: 'Payment gateway issue confirmed. Working with payment provider to resolve.',
              timestamp: '2024-01-20T14:15:00Z',
              isInternal: true,
            },
          ],
        },
        {
          id: '2',
          title: 'Vehicle not available on scheduled date',
          description: 'I booked a motorcycle for tomorrow but the provider says it\'s not available.',
          category: 'BOOKING',
          priority: 'URGENT',
          status: 'OPEN',
          createdBy: {
            id: 'user2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: 'CUSTOMER',
          },
          createdAt: '2024-01-20T15:45:00Z',
          updatedAt: '2024-01-20T15:45:00Z',
          comments: [
            {
              id: '1',
              author: {
                id: 'user2',
                name: 'Jane Smith',
                role: 'CUSTOMER',
              },
              content: 'I booked a motorcycle for tomorrow but the provider says it\'s not available.',
              timestamp: '2024-01-20T15:45:00Z',
              isInternal: false,
            },
          ],
        },
        {
          id: '3',
          title: 'Account verification pending',
          description: 'I submitted my documents for verification but it\'s been 3 days with no response.',
          category: 'ACCOUNT',
          priority: 'MEDIUM',
          status: 'OPEN',
          createdBy: {
            id: 'provider1',
            name: 'Jakarta Motor Rentals',
            email: '<EMAIL>',
            role: 'PROVIDER',
          },
          createdAt: '2024-01-18T09:20:00Z',
          updatedAt: '2024-01-18T09:20:00Z',
          comments: [
            {
              id: '1',
              author: {
                id: 'provider1',
                name: 'Jakarta Motor Rentals',
                role: 'PROVIDER',
              },
              content: 'I submitted my documents for verification but it\'s been 3 days with no response.',
              timestamp: '2024-01-18T09:20:00Z',
              isInternal: false,
            },
          ],
        },
        {
          id: '4',
          title: 'Payout not received',
          description: 'I was supposed to receive my payout yesterday but it hasn\'t arrived in my bank account.',
          category: 'BILLING',
          priority: 'HIGH',
          status: 'RESOLVED',
          assignedTo: '<EMAIL>',
          createdBy: {
            id: 'provider2',
            name: 'Bali Scooter Hub',
            email: '<EMAIL>',
            role: 'PROVIDER',
          },
          createdAt: '2024-01-15T11:30:00Z',
          updatedAt: '2024-01-16T16:45:00Z',
          resolvedAt: '2024-01-16T16:45:00Z',
          satisfaction: 4,
          comments: [
            {
              id: '1',
              author: {
                id: 'provider2',
                name: 'Bali Scooter Hub',
                role: 'PROVIDER',
              },
              content: 'I was supposed to receive my payout yesterday but it hasn\'t arrived in my bank account.',
              timestamp: '2024-01-15T11:30:00Z',
              isInternal: false,
            },
            {
              id: '2',
              author: {
                id: 'finance1',
                name: 'Finance Team',
                role: 'SUPPORT',
              },
              content: 'We\'ve processed your payout. It should arrive within 1-2 business days.',
              timestamp: '2024-01-16T16:45:00Z',
              isInternal: false,
            },
          ],
        },
      ];

      setTickets(mockTickets);
      
      // Calculate stats
      const totalTickets = mockTickets.length;
      const openTickets = mockTickets.filter(t => t.status === 'OPEN').length;
      const inProgressTickets = mockTickets.filter(t => t.status === 'IN_PROGRESS').length;
      const resolvedTickets = mockTickets.filter(t => t.status === 'RESOLVED').length;
      const urgentTickets = mockTickets.filter(t => t.priority === 'URGENT').length;
      const averageResolutionTime = 2.5; // Mock average in days
      const customerSatisfaction = 4.2; // Mock average rating
      const ticketsByCategory = mockTickets.reduce((acc, ticket) => {
        acc[ticket.category] = (acc[ticket.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      setStats({
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        urgentTickets,
        averageResolutionTime,
        customerSatisfaction,
        ticketsByCategory,
      });

      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch tickets:', error);
      setError('Failed to load support tickets');
      setLoading(false);
    }
  };

  const filterTickets = () => {
    let filtered = tickets;

    // Search filter
    if (ticketSearch) {
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(ticketSearch.toLowerCase()) ||
        ticket.description.toLowerCase().includes(ticketSearch.toLowerCase()) ||
        ticket.createdBy.name.toLowerCase().includes(ticketSearch.toLowerCase()) ||
        ticket.createdBy.email.toLowerCase().includes(ticketSearch.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter.length > 0) {
      filtered = filtered.filter(ticket => statusFilter.includes(ticket.status));
    }

    // Priority filter
    if (priorityFilter.length > 0) {
      filtered = filtered.filter(ticket => priorityFilter.includes(ticket.priority));
    }

    // Category filter
    if (categoryFilter.length > 0) {
      filtered = filtered.filter(ticket => categoryFilter.includes(ticket.category));
    }

    setFilteredTickets(filtered);
  };

  const handleTicketAction = async (ticketId: string, action: 'assign' | 'resolve' | 'close' | 'reopen') => {
    try {
      console.log(`Performing ${action} on ticket ${ticketId}`);
      
      // Update local state
      setTickets(prev => prev.map(ticket => {
        if (ticket.id === ticketId) {
          switch (action) {
            case 'resolve':
              return { ...ticket, status: 'RESOLVED', resolvedAt: new Date().toISOString() };
            case 'close':
              return { ...ticket, status: 'CLOSED' };
            case 'reopen':
              return { ...ticket, status: 'OPEN' };
            default:
              return ticket;
          }
        }
        return ticket;
      }));

      // Refresh data
      await fetchTickets();
    } catch (error) {
      console.error(`Failed to ${action} ticket:`, error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      case 'LOW': return 'default';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'error';
      case 'IN_PROGRESS': return 'warning';
      case 'RESOLVED': return 'success';
      case 'CLOSED': return 'default';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'TECHNICAL': return <BugIcon />;
      case 'BILLING': return <PaymentIcon />;
      case 'BOOKING': return <BookOnline />;
      case 'ACCOUNT': return <AccountCircle />;
      case 'GENERAL': return <HelpIcon />;
      default: return <HelpIcon />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <ErrorBoundary>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            🎫 Support Tickets
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage customer support tickets and track resolution progress
          </Typography>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SupportIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{stats.totalTickets}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Tickets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <PendingIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{stats.openTickets}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Open Tickets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <EmergencyIcon color="error" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{stats.urgentTickets}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Urgent Tickets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <StarIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">{stats.customerSatisfaction.toFixed(1)}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg. Satisfaction
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Search and Filters */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search tickets..."
                value={ticketSearch}
                onChange={(e) => setTicketSearch(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  multiple
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="OPEN">Open</MenuItem>
                  <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                  <MenuItem value="RESOLVED">Resolved</MenuItem>
                  <MenuItem value="CLOSED">Closed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  multiple
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="URGENT">Urgent</MenuItem>
                  <MenuItem value="HIGH">High</MenuItem>
                  <MenuItem value="MEDIUM">Medium</MenuItem>
                  <MenuItem value="LOW">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  multiple
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value as string[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="TECHNICAL">Technical</MenuItem>
                  <MenuItem value="BILLING">Billing</MenuItem>
                  <MenuItem value="BOOKING">Booking</MenuItem>
                  <MenuItem value="ACCOUNT">Account</MenuItem>
                  <MenuItem value="GENERAL">General</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                fullWidth
                onClick={() => {
                  // TODO: Implement new ticket creation
                  console.log('Create new ticket');
                }}
              >
                New Ticket
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="All Tickets" />
            <Tab label="Open" />
            <Tab label="In Progress" />
            <Tab label="Resolved" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {activeTab === 0 && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Ticket</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell>Assigned To</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTickets.map((ticket) => (
                  <TableRow key={ticket.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" sx={{ cursor: 'pointer' }} onClick={() => {
                          setSelectedTicket(ticket);
                          setShowTicketDetails(true);
                        }}>
                          {ticket.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ 
                          maxWidth: 200, 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {ticket.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getCategoryIcon(ticket.category)}
                        label={ticket.category}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={ticket.priority}
                        color={getPriorityColor(ticket.priority) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={ticket.status.replace('_', ' ')}
                        color={getStatusColor(ticket.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{ticket.createdBy.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {ticket.createdBy.role}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {ticket.assignedTo || 'Unassigned'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(ticket.createdAt)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedTicket(ticket);
                              setShowTicketDetails(true);
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        {ticket.status === 'OPEN' && (
                          <Tooltip title="Assign">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => {
                                setSelectedTicket(ticket);
                                setShowAssignDialog(true);
                              }}
                            >
                              <AssignmentIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        {ticket.status === 'OPEN' && (
                          <Tooltip title="Start Progress">
                            <IconButton
                              size="small"
                              color="warning"
                              onClick={() => handleTicketAction(ticket.id, 'assign')}
                            >
                              <PlayArrow />
                            </IconButton>
                          </Tooltip>
                        )}
                        {ticket.status === 'IN_PROGRESS' && (
                          <Tooltip title="Resolve">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleTicketAction(ticket.id, 'resolve')}
                            >
                              <CheckCircleIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        {ticket.status === 'RESOLVED' && (
                          <Tooltip title="Close">
                            <IconButton
                              size="small"
                              color="default"
                              onClick={() => handleTicketAction(ticket.id, 'close')}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {activeTab === 1 && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Ticket</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTickets
                  .filter(t => t.status === 'OPEN')
                  .map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{ticket.title}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {ticket.description.substring(0, 100)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getCategoryIcon(ticket.category)}
                          label={ticket.category}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={ticket.priority}
                          color={getPriorityColor(ticket.priority) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{ticket.createdBy.name}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(ticket.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={() => {
                              setSelectedTicket(ticket);
                              setShowAssignDialog(true);
                            }}
                          >
                            Assign
                          </Button>
                          <Button
                            variant="outlined"
                            color="warning"
                            size="small"
                            onClick={() => handleTicketAction(ticket.id, 'assign')}
                          >
                            Start Progress
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {activeTab === 2 && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Ticket</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Assigned To</TableCell>
                  <TableCell>Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTickets
                  .filter(t => t.status === 'IN_PROGRESS')
                  .map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{ticket.title}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {ticket.description.substring(0, 100)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getCategoryIcon(ticket.category)}
                          label={ticket.category}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={ticket.priority}
                          color={getPriorityColor(ticket.priority) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{ticket.assignedTo}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(ticket.updatedAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Button
                            variant="contained"
                            color="success"
                            size="small"
                            onClick={() => handleTicketAction(ticket.id, 'resolve')}
                          >
                            Resolve
                          </Button>
                          <Button
                            variant="outlined"
                            color="error"
                            size="small"
                            onClick={() => handleTicketAction(ticket.id, 'reopen')}
                          >
                            Reopen
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {activeTab === 3 && (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Ticket</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Resolved By</TableCell>
                  <TableCell>Resolved</TableCell>
                  <TableCell>Satisfaction</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTickets
                  .filter(t => t.status === 'RESOLVED')
                  .map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">{ticket.title}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {ticket.description.substring(0, 100)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getCategoryIcon(ticket.category)}
                          label={ticket.category}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{ticket.assignedTo}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {ticket.resolvedAt ? formatDate(ticket.resolvedAt) : 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {ticket.satisfaction ? (
                          <Rating value={ticket.satisfaction} readOnly size="small" />
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No rating
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => {
                            setSelectedTicket(ticket);
                            setShowTicketDetails(true);
                          }}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Ticket Details Dialog */}
        <Dialog
          open={showTicketDetails}
          onClose={() => setShowTicketDetails(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Ticket Details
            {selectedTicket && (
              <Typography variant="body2" color="text.secondary">
                #{selectedTicket.id}
              </Typography>
            )}
          </DialogTitle>
          <DialogContent>
            {selectedTicket && (
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <Typography variant="h6" gutterBottom>
                      {selectedTicket.title}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {selectedTicket.description}
                    </Typography>
                    
                    <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                      Comments
                    </Typography>
                    <Timeline>
                      {selectedTicket.comments.map((comment) => (
                        <TimelineItem key={comment.id}>
                          <TimelineOppositeContent>
                            <Typography variant="caption" color="text.secondary">
                              {formatDate(comment.timestamp)}
                            </Typography>
                          </TimelineOppositeContent>
                          <TimelineSeparator>
                            <TimelineDot color={comment.isInternal ? 'secondary' : 'primary'} />
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent>
                            <Box>
                              <Typography variant="subtitle2">
                                {comment.author.name} ({comment.author.role})
                                {comment.isInternal && (
                                  <Chip label="Internal" size="small" sx={{ ml: 1 }} />
                                )}
                              </Typography>
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {comment.content}
                              </Typography>
                            </Box>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Ticket Information
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemText
                              primary="Status"
                              secondary={
                                <Chip
                                  label={selectedTicket.status.replace('_', ' ')}
                                  color={getStatusColor(selectedTicket.status) as any}
                                  size="small"
                                />
                              }
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemText
                              primary="Priority"
                              secondary={
                                <Chip
                                  label={selectedTicket.priority}
                                  color={getPriorityColor(selectedTicket.priority) as any}
                                  size="small"
                                />
                              }
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemText
                              primary="Category"
                              secondary={
                                <Chip
                                  icon={getCategoryIcon(selectedTicket.category)}
                                  label={selectedTicket.category}
                                  size="small"
                                  variant="outlined"
                                />
                              }
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemText
                              primary="Created By"
                              secondary={selectedTicket.createdBy.name}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemText
                              primary="Assigned To"
                              secondary={selectedTicket.assignedTo || 'Unassigned'}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemText
                              primary="Created"
                              secondary={formatDate(selectedTicket.createdAt)}
                            />
                          </ListItem>
                          {selectedTicket.satisfaction && (
                            <ListItem>
                              <ListItemText
                                primary="Satisfaction"
                                secondary={
                                  <Rating value={selectedTicket.satisfaction} readOnly size="small" />
                                }
                              />
                            </ListItem>
                          )}
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowTicketDetails(false)}>Close</Button>
            <Button
              variant="contained"
              onClick={() => {
                setShowTicketDetails(false);
                setShowCommentDialog(true);
              }}
            >
              Add Comment
            </Button>
          </DialogActions>
        </Dialog>

        {/* Assign Dialog */}
        <Dialog
          open={showAssignDialog}
          onClose={() => setShowAssignDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Assign Ticket</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Assign To</InputLabel>
              <Select
                value=""
                onChange={(e) => {
                  // Handle assignment
                  console.log('Assigning to:', e.target.value);
                }}
              >
                <MenuItem value="<EMAIL>">Support Team</MenuItem>
                <MenuItem value="<EMAIL>">Finance Team</MenuItem>
                <MenuItem value="<EMAIL>">Admin Team</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowAssignDialog(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => setShowAssignDialog(false)}>
              Assign
            </Button>
          </DialogActions>
        </Dialog>

        {/* Comment Dialog */}
        <Dialog
          open={showCommentDialog}
          onClose={() => setShowCommentDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Add Comment</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={4}
              placeholder="Enter your comment..."
              sx={{ mt: 2 }}
            />
            <FormControlLabel
              control={<Checkbox />}
              label="Internal comment (not visible to customer)"
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowCommentDialog(false)}>Cancel</Button>
            <Button variant="contained" onClick={() => setShowCommentDialog(false)}>
              Add Comment
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </ErrorBoundary>
  );
} 