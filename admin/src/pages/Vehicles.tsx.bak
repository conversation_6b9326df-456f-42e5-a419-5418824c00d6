import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Chip,
  IconButton,
  Alert,
} from '@mui/material'
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridToolbar,
} from '@mui/x-data-grid'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

interface Vehicle {
  id: string
  make: string
  model: string
  year: number
  category: 'small_scooter' | 'large_scooter' | 'luxury_bike'
  daily_rate: number
  engine_size: number
  transmission: 'automatic' | 'manual'
  fuel_type: 'petrol' | 'electric'
  active: boolean
  quantity: number
  available_quantity: number
  images: string[]
  created_at: string
}

const Vehicles: React.FC = () => {
  const navigate = useNavigate()
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [loading, setLoading] = useState(true)
  const [error] = useState('')

  // Mock data for now - replace with actual API call
  useEffect(() => {
    const mockVehicles: Vehicle[] = [
      {
        id: '1',
        make: 'Honda',
        model: 'PCX 160',
        year: 2023,
        category: 'large_scooter',
        daily_rate: 120000,
        engine_size: 160,
        transmission: 'automatic',
        fuel_type: 'petrol',
        active: true,
        quantity: 5,
        available_quantity: 3,
        images: [],
        created_at: '2024-01-15T10:00:00Z',
      },
      {
        id: '2',
        make: 'Yamaha',
        model: 'NMAX 155',
        year: 2023,
        category: 'large_scooter',
        daily_rate: 110000,
        engine_size: 155,
        transmission: 'automatic',
        fuel_type: 'petrol',
        active: true,
        quantity: 3,
        available_quantity: 1,
        images: [],
        created_at: '2024-01-14T09:30:00Z',
      },
      {
        id: '3',
        make: 'Honda',
        model: 'Scoopy',
        year: 2022,
        category: 'small_scooter',
        daily_rate: 80000,
        engine_size: 110,
        transmission: 'automatic',
        fuel_type: 'petrol',
        active: true,
        quantity: 8,
        available_quantity: 5,
        images: [],
        created_at: '2024-01-13T14:20:00Z',
      },
    ]

    setTimeout(() => {
      setVehicles(mockVehicles)
      setLoading(false)
    }, 1000)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getCategoryLabel = (category: string) => {
    const labels = {
      small_scooter: 'Small Scooter',
      large_scooter: 'Large Scooter',
      luxury_bike: 'Luxury Bike',
    }
    return labels[category as keyof typeof labels] || category
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      small_scooter: 'info',
      large_scooter: 'success',
      luxury_bike: 'warning',
    }
    return colors[category as keyof typeof colors] || 'default'
  }

  const handleEdit = (id: string) => {
    navigate(`/vehicles/${id}/update`)
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this vehicle?')) {
      try {
        // Replace with actual API call
        setVehicles(prev => prev.filter(v => v.id !== id))
        toast.success('Vehicle deleted successfully')
      } catch (error) {
        toast.error('Failed to delete vehicle')
      }
    }
  }

  const columns: GridColDef[] = [
    {
      field: 'images',
      headerName: 'Image',
      width: 80,
      renderCell: () => (
        <Box
          sx={{
            width: 50,
            height: 50,
            borderRadius: 1,
            backgroundColor: 'grey.200',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            my: 1,
          }}
        >
          🏍️
        </Box>
      ),
      sortable: false,
      filterable: false,
    },
    {
      field: 'vehicle',
      headerName: 'Vehicle',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <Typography variant="body2" fontWeight="bold">
            {params.row.make} {params.row.model}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.year} • {params.row.engine_size}cc
          </Typography>
        </Box>
      ),
    },
    {
      field: 'category',
      headerName: 'Category',
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={getCategoryLabel(params.value)}
          color={getCategoryColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'daily_rate',
      headerName: 'Daily Rate',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2" fontWeight="bold">
          {formatCurrency(params.value)}
        </Typography>
      ),
    },
    {
      field: 'transmission',
      headerName: 'Trans.',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value === 'automatic' ? 'Auto' : 'Manual'}
          variant="outlined"
          size="small"
        />
      ),
    },
    {
      field: 'fuel_type',
      headerName: 'Fuel',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value === 'petrol' ? '⛽ Gas' : '🔋 Electric'}
          variant="outlined"
          size="small"
        />
      ),
    },
    {
      field: 'availability',
      headerName: 'Availability',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {params.row.available_quantity}/{params.row.quantity}
        </Typography>
      ),
    },
    {
      field: 'active',
      headerName: 'Status',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'Active' : 'Inactive'}
          color={params.value ? 'success' : 'error'}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEdit(params.row.id)}
            color="primary"
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(params.row.id)}
            color="error"
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
      sortable: false,
      filterable: false,
    },
  ]

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Vehicle Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your scooter and motorbike fleet
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/vehicles/create')}
        >
          Add Vehicle
        </Button>
      </Box>

      {/* Data Grid */}
      <Paper sx={{ width: '100%', mb: 2 }}>
        <DataGrid
          rows={vehicles}
          columns={columns}
          loading={loading}
          autoHeight
          checkboxSelection
          disableRowSelectionOnClick
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
            },
          }}
          initialState={{
            pagination: {
              paginationModel: { page: 0, pageSize: 10 },
            },
          }}
          pageSizeOptions={[5, 10, 25]}
          sx={{
            '& .MuiDataGrid-row:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        />
      </Paper>
    </Box>
  )
}

export default Vehicles