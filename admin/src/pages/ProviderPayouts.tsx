import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Avatar,
  Stack,
  Divider,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Visibility as ViewIcon,
  AccountBalance as BankIcon,
  CheckCircle as CompletedIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  Send as ProcessIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { AdminApiService } from '../services/AdminApiService';
import { adminSupabase as supabase } from '../services/adminSupabaseClient';

interface ProviderPayout {
  id: string;
  payoutId: string;
  provider: {
    id: string;
    name: string;
    email: string;
    businessName?: string;
  };
  amount: number;
  currency: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  payoutMethod: {
    type: 'BANK_TRANSFER' | 'PAYPAL' | 'STRIPE';
    accountDetails: {
      bankName?: string;
      accountNumber?: string;
      accountHolderName?: string;
    };
  };
  bookings: Array<{
    id: string;
    vehicleInfo: string;
    dates: string;
    earnings: number;
    platformFee: number;
  }>;
  fees: {
    platformFee: number;
    processingFee: number;
    taxes: number;
  };
  period: {
    startDate: string;
    endDate: string;
  };
  createdAt: string;
  processedAt?: string;
  failureReason?: string;
}

interface PayoutStats {
  totalPayouts: number;
  pendingPayouts: number;
  completedPayouts: number;
  failedPayouts: number;
  totalAmount: number;
  avgPayoutAmount: number;
  processingTime: number;
}

const ProviderPayouts: React.FC = () => {
  const [payouts, setPayouts] = useState<ProviderPayout[]>([]);
  const [stats, setStats] = useState<PayoutStats>({
    totalPayouts: 0,
    pendingPayouts: 0,
    completedPayouts: 0,
    failedPayouts: 0,
    totalAmount: 0,
    avgPayoutAmount: 0,
    processingTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedPayout, setSelectedPayout] = useState<ProviderPayout | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoPayoutEnabled, setAutoPayoutEnabled] = useState(true);

  useEffect(() => {
    fetchPayouts();
    fetchStats();
  }, [filterStatus, searchTerm]);

  const fetchPayouts = async () => {
    try {
      setLoading(true);

      // Since we don't have a provider_payouts table yet, show empty data
      // In a real implementation, you would query your provider_payouts table
      const livePayouts: ProviderPayout[] = [];

      setPayouts(livePayouts);
    } catch (error) {
      console.error('Error fetching payouts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Get live payout statistics from Supabase Payment table
      const { data: payments, error } = await supabase
        .from('Payment')
        .select('amount, status, providerEarnings');

      if (error) {
        console.error('Error fetching payout stats:', error);
        setStats({
          totalPayouts: 0,
          pendingPayouts: 0,
          completedPayouts: 0,
          failedPayouts: 0,
          totalAmount: 0,
          avgPayoutAmount: 0,
          processingTime: 0
        });
        return;
      }

      const totalPayouts = payments?.length || 0;
      const completedPayouts = payments?.filter(p => p.status === 'COMPLETED').length || 0;
      const pendingPayouts = payments?.filter(p => p.status === 'PENDING').length || 0;
      const failedPayouts = payments?.filter(p => p.status === 'FAILED').length || 0;
      const totalAmount = payments?.reduce((sum, p) => sum + (p.providerEarnings || 0), 0) || 0;
      const avgPayoutAmount = totalPayouts > 0 ? totalAmount / totalPayouts : 0;

      setStats({
        totalPayouts,
        pendingPayouts,
        completedPayouts,
        failedPayouts,
        totalAmount,
        avgPayoutAmount,
        processingTime: 0 // Would need timestamp analysis for real calculation
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'PROCESSING': return 'info';
      case 'FAILED': return 'error';
      case 'CANCELLED': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CompletedIcon />;
      case 'PENDING': return <PendingIcon />;
      case 'PROCESSING': return <ProcessIcon />;
      case 'FAILED': return <ErrorIcon />;
      default: return <PendingIcon />;
    }
  };

  const handleViewPayout = (payout: ProviderPayout) => {
    setSelectedPayout(payout);
    setDialogOpen(true);
  };

  const handleProcessPayout = async (payoutId: string) => {
    try {
      // TODO: Connect to Supabase
      // const { error } = await supabase
      //   .from('provider_payouts')
      //   .update({ status: 'PROCESSING', processed_at: new Date().toISOString() })
      //   .eq('id', payoutId);
      
      setPayouts(prev => prev.map(payout => 
        payout.id === payoutId 
          ? { ...payout, status: 'PROCESSING' as any }
          : payout
      ));
      
      alert('Payout processing initiated!');
    } catch (error) {
      console.error('Error processing payout:', error);
      alert('Failed to process payout');
    }
  };

  const filteredPayouts = payouts.filter(payout => {
    const matchesStatus = filterStatus === 'ALL' || payout.status === filterStatus;
    const matchesSearch = searchTerm === '' || 
      payout.provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payout.provider.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payout.payoutId.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          💸 Provider Payouts
        </Typography>
        <Stack direction="row" spacing={2}>
          <FormControlLabel
            control={
              <Switch
                checked={autoPayoutEnabled}
                onChange={(e) => setAutoPayoutEnabled(e.target.checked)}
              />
            }
            label="Auto Payouts"
          />
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchPayouts(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<ProcessIcon />}
            color="primary"
          >
            Process Batch
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Payouts
              </Typography>
              <Typography variant="h4">
                {stats.totalPayouts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pendingPayouts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.completedPayouts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Amount
              </Typography>
              <Typography variant="h5" color="primary.main">
                ${stats.totalAmount.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Payout
              </Typography>
              <Typography variant="h5">
                ${stats.avgPayoutAmount.toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Processing Time
              </Typography>
              <Typography variant="h5">
                {stats.processingTime}d
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search payouts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
                <MenuItem value="PROCESSING">Processing</MenuItem>
                <MenuItem value="COMPLETED">Completed</MenuItem>
                <MenuItem value="FAILED">Failed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Payouts Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Payout ID</TableCell>
                  <TableCell>Provider</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Method</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Period</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredPayouts.map((payout) => (
                  <TableRow key={payout.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {payout.payoutId}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(payout.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                          {payout.provider.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {payout.provider.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {payout.provider.businessName || payout.provider.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        ${payout.amount.toFixed(2)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {payout.currency}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BankIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Box>
                          <Typography variant="body2">
                            {payout.payoutMethod.type.replace('_', ' ')}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {payout.payoutMethod.accountDetails.bankName}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(payout.status)}
                        label={payout.status}
                        size="small"
                        color={getStatusColor(payout.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(payout.period.startDate).toLocaleDateString()} - 
                        {new Date(payout.period.endDate).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewPayout(payout)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        {payout.status === 'PENDING' && (
                          <Tooltip title="Process Payout">
                            <IconButton
                              size="small"
                              onClick={() => handleProcessPayout(payout.id)}
                            >
                              <ProcessIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Payout Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedPayout && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  Payout Details - {selectedPayout.payoutId}
                </Typography>
                <Chip
                  label={selectedPayout.status}
                  color={getStatusColor(selectedPayout.status) as any}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Provider Information
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ mr: 2 }}>
                      {selectedPayout.provider.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1">
                        {selectedPayout.provider.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedPayout.provider.email}
                      </Typography>
                      {selectedPayout.provider.businessName && (
                        <Typography variant="caption" color="text.secondary">
                          {selectedPayout.provider.businessName}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Payout Information
                  </Typography>
                  <Stack spacing={1}>
                    <Box>
                      <Typography variant="caption">Amount:</Typography>
                      <Typography variant="h6" color="primary.main">
                        ${selectedPayout.amount.toFixed(2)} {selectedPayout.currency}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Period:</Typography>
                      <Typography variant="body2">
                        {new Date(selectedPayout.period.startDate).toLocaleDateString()} - 
                        {new Date(selectedPayout.period.endDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Payment Method
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BankIcon sx={{ mr: 1 }} />
                      <Typography variant="body2" fontWeight="bold">
                        {selectedPayout.payoutMethod.type.replace('_', ' ')}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      <strong>Bank:</strong> {selectedPayout.payoutMethod.accountDetails.bankName}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Account:</strong> {selectedPayout.payoutMethod.accountDetails.accountNumber}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Account Holder:</strong> {selectedPayout.payoutMethod.accountDetails.accountHolderName}
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Bookings Included ({selectedPayout.bookings.length})
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Vehicle</TableCell>
                          <TableCell>Dates</TableCell>
                          <TableCell>Earnings</TableCell>
                          <TableCell>Platform Fee</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedPayout.bookings.map((booking) => (
                          <TableRow key={booking.id}>
                            <TableCell>{booking.vehicleInfo}</TableCell>
                            <TableCell>{booking.dates}</TableCell>
                            <TableCell>${booking.earnings.toFixed(2)}</TableCell>
                            <TableCell>${booking.platformFee.toFixed(2)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Fee Breakdown
                  </Typography>
                  <Stack spacing={1}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Platform Fee:</Typography>
                      <Typography variant="body2">${selectedPayout.fees.platformFee.toFixed(2)}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Processing Fee:</Typography>
                      <Typography variant="body2">${selectedPayout.fees.processingFee.toFixed(2)}</Typography>
                    </Box>
                    <Divider />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" fontWeight="bold">Net Payout:</Typography>
                      <Typography variant="body2" fontWeight="bold" color="primary.main">
                        ${selectedPayout.amount.toFixed(2)}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>

                {selectedPayout.processedAt && (
                  <Grid item xs={12}>
                    <Alert severity="success">
                      <Typography variant="body2">
                        <strong>Processed:</strong> {new Date(selectedPayout.processedAt).toLocaleString()}
                      </Typography>
                    </Alert>
                  </Grid>
                )}

                {selectedPayout.failureReason && (
                  <Grid item xs={12}>
                    <Alert severity="error">
                      <Typography variant="body2">
                        <strong>Failure Reason:</strong> {selectedPayout.failureReason}
                      </Typography>
                    </Alert>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Close
              </Button>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
              >
                Download Statement
              </Button>
              {selectedPayout.status === 'PENDING' && (
                <Button
                  variant="contained"
                  startIcon={<ProcessIcon />}
                  onClick={() => handleProcessPayout(selectedPayout.id)}
                >
                  Process Payout
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default ProviderPayouts;
