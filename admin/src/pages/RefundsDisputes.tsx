import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  Badge,
  LinearProgress,
  Divider,
  Avatar,
  Stack,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Visibility as ViewIcon,
  AccountBalance as RefundIcon,
  Gavel as DisputeIcon,
  AttachMoney as MoneyIcon,
  Schedule as PendingIcon,
  CheckCircle as ApprovedIcon,
  Cancel as RejectedIcon,
  Warning as WarningIcon,
  Receipt as ReceiptIcon,
  Send as ProcessIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface RefundDispute {
  id: string;
  type: 'REFUND' | 'DISPUTE';
  caseNumber: string;
  title: string;
  description: string;
  amount: number;
  status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'PROCESSED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  category: 'DAMAGE' | 'BILLING' | 'CANCELLATION' | 'SERVICE' | 'OTHER';
  user: {
    id: string;
    name: string;
    email: string;
  };
  booking: {
    id: string;
    vehicleInfo: string;
    dates: string;
    totalAmount: number;
  };
  evidence: string[];
  timeline: TimelineEvent[];
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolutionNotes?: string;
  refundAmount?: number;
  processingFee?: number;
}

interface TimelineEvent {
  id: string;
  type: 'CREATED' | 'REVIEWED' | 'APPROVED' | 'REJECTED' | 'PROCESSED' | 'COMMENT';
  description: string;
  timestamp: string;
  actor: string;
}

interface RefundDisputeStats {
  totalCases: number;
  pendingReview: number;
  approved: number;
  rejected: number;
  totalRefunded: number;
  avgProcessingTime: number;
  satisfactionRate: number;
}

const RefundsDisputes: React.FC = () => {
  const [cases, setCases] = useState<RefundDispute[]>([]);
  const [stats, setStats] = useState<RefundDisputeStats>({
    totalCases: 0,
    pendingReview: 0,
    approved: 0,
    rejected: 0,
    totalRefunded: 0,
    avgProcessingTime: 0,
    satisfactionRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedCase, setSelectedCase] = useState<RefundDispute | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [filterType, setFilterType] = useState<string>('ALL');
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [refundAmount, setRefundAmount] = useState<number>(0);
  const [processingFee, setProcessingFee] = useState<number>(0);
  const [resolutionNotes, setResolutionNotes] = useState('');

  useEffect(() => {
    fetchCases();
    fetchStats();
  }, [filterType, filterStatus, searchTerm]);

  const fetchCases = async () => {
    try {
      setLoading(true);

      // Import admin Supabase client
      const { adminSupabase } = await import('../services/adminSupabaseClient');

      // Get refund data with related booking and user information
      const { data: refunds, error: refundsError } = await adminSupabase
        .from('Refund')
        .select(`
          *,
          booking:Booking!inner(
            id,
            totalPrice,
            startDate,
            endDate,
            user:User!inner(
              id,
              name,
              email
            ),
            vehicle:Vehicle!inner(
              make,
              model
            )
          )
        `)
        .order('createdAt', { ascending: false });

      if (refundsError) {
        console.error('Error fetching refund cases:', refundsError);
        setCases([]);
        return;
      }

      // Transform Supabase data to RefundDispute format
      const liveCases: RefundDispute[] = (refunds || []).map((refund, index) => ({
        id: refund.id,
        type: 'REFUND' as const,
        caseNumber: `REF-${String(index + 1).padStart(4, '0')}`,
        title: `Refund Request - ${refund.booking?.vehicle?.make} ${refund.booking?.vehicle?.model}`,
        description: refund.reason || 'Refund request submitted',
        amount: refund.amount || 0,
        status: refund.status?.toUpperCase() as any || 'PENDING',
        priority: 'MEDIUM' as const,
        category: 'CANCELLATION' as const,
        user: {
          id: refund.booking?.user?.id || '',
          name: refund.booking?.user?.name || 'Unknown User',
          email: refund.booking?.user?.email || ''
        },
        booking: {
          id: refund.booking?.id || '',
          vehicleInfo: `${refund.booking?.vehicle?.make} ${refund.booking?.vehicle?.model}`,
          dates: refund.booking?.startDate && refund.booking?.endDate
            ? `${new Date(refund.booking.startDate).toLocaleDateString()} - ${new Date(refund.booking.endDate).toLocaleDateString()}`
            : 'N/A',
          totalAmount: refund.booking?.totalPrice || 0
        },
        evidence: [],
        timeline: [
          {
            id: '1',
            type: 'CREATED' as const,
            description: 'Refund request created',
            timestamp: refund.createdAt || new Date().toISOString(),
            actor: 'System'
          }
        ],
        createdAt: refund.createdAt || new Date().toISOString(),
        updatedAt: refund.updatedAt || new Date().toISOString(),
        resolvedAt: refund.status === 'COMPLETED' ? refund.updatedAt : undefined,
        resolutionNotes: refund.reason || '',
        refundAmount: refund.amount || 0,
        processingFee: 0
      }));

      setCases(liveCases);
      console.log('✅ Loaded', liveCases.length, 'refund cases from Supabase');

    } catch (error) {
      console.error('Error fetching cases:', error);
      setCases([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Import admin Supabase client
      const { adminSupabase } = await import('../services/adminSupabaseClient');

      // Get refund data from Supabase
      const { data: refunds, error: refundsError } = await adminSupabase
        .from('Refund')
        .select('*');

      if (refundsError) {
        console.error('Error fetching refunds:', refundsError);
        // Set empty stats if no data
        setStats({
          totalCases: 0,
          pendingReview: 0,
          approved: 0,
          rejected: 0,
          totalRefunded: 0,
          avgProcessingTime: 0,
          satisfactionRate: 0
        });
        return;
      }

      // Calculate live stats from Supabase data
      const totalCases = refunds?.length || 0;
      const pendingReview = refunds?.filter(r => r.status === 'PENDING').length || 0;
      const approved = refunds?.filter(r => r.status === 'APPROVED' || r.status === 'COMPLETED').length || 0;
      const rejected = refunds?.filter(r => r.status === 'REJECTED').length || 0;
      const totalRefunded = refunds?.reduce((sum, r) => sum + (r.amount || 0), 0) || 0;

      // Calculate average processing time (mock for now since we don't have processing time data)
      const avgProcessingTime = totalCases > 0 ? 2.1 : 0;

      // Mock satisfaction rate (would come from user feedback in real implementation)
      const satisfactionRate = totalCases > 0 ? 4.2 : 0;

      setStats({
        totalCases,
        pendingReview,
        approved,
        rejected,
        totalRefunded,
        avgProcessingTime,
        satisfactionRate
      });

      console.log('✅ Refunds & Disputes stats loaded from Supabase:', {
        totalCases,
        pendingReview,
        approved,
        rejected,
        totalRefunded
      });

    } catch (error) {
      console.error('Error fetching stats:', error);
      // Set empty stats on error
      setStats({
        totalCases: 0,
        pendingReview: 0,
        approved: 0,
        rejected: 0,
        totalRefunded: 0,
        avgProcessingTime: 0,
        satisfactionRate: 0
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'warning';
      case 'UNDER_REVIEW': return 'info';
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      case 'PROCESSED': return 'success';
      case 'CLOSED': return 'default';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'REFUND' ? <RefundIcon /> : <DisputeIcon />;
  };

  const handleViewCase = (caseItem: RefundDispute) => {
    setSelectedCase(caseItem);
    setRefundAmount(caseItem.refundAmount || caseItem.amount);
    setProcessingFee(caseItem.processingFee || 0);
    setResolutionNotes(caseItem.resolutionNotes || '');
    setDialogOpen(true);
  };

  const handleProcessCase = async (action: 'APPROVE' | 'REJECT' | 'PROCESS') => {
    if (!selectedCase) return;
    
    try {
      // API call to process case
      // await AdminApiService.processRefundDispute(selectedCase.id, action, refundAmount, resolutionNotes);
      
      const newStatus = action === 'APPROVE' ? 'APPROVED' : 
                       action === 'REJECT' ? 'REJECTED' : 'PROCESSED';
      
      // Update local state
      setCases(prev => prev.map(c => 
        c.id === selectedCase.id 
          ? { 
              ...c, 
              status: newStatus as any, 
              refundAmount: action === 'APPROVE' ? refundAmount : undefined,
              processingFee: action === 'APPROVE' ? processingFee : undefined,
              resolutionNotes 
            }
          : c
      ));
      
      setDialogOpen(false);
      fetchStats();
    } catch (error) {
      console.error('Error processing case:', error);
    }
  };

  const filteredCases = cases.filter(caseItem => {
    const matchesType = filterType === 'ALL' || caseItem.type === filterType;
    const matchesStatus = filterStatus === 'ALL' || caseItem.status === filterStatus;
    const matchesSearch = searchTerm === '' || 
      caseItem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.caseNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesType && matchesStatus && matchesSearch;
  });

  const getStepperSteps = (type: string) => {
    if (type === 'REFUND') {
      return ['Submitted', 'Under Review', 'Approved/Rejected', 'Processed'];
    } else {
      return ['Submitted', 'Investigation', 'Resolution', 'Closed'];
    }
  };

  const getActiveStep = (status: string, type: string) => {
    if (type === 'REFUND') {
      switch (status) {
        case 'PENDING': return 0;
        case 'UNDER_REVIEW': return 1;
        case 'APPROVED':
        case 'REJECTED': return 2;
        case 'PROCESSED': return 3;
        default: return 0;
      }
    } else {
      switch (status) {
        case 'PENDING': return 0;
        case 'UNDER_REVIEW': return 1;
        case 'APPROVED':
        case 'REJECTED': return 2;
        case 'CLOSED': return 3;
        default: return 0;
      }
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          💰 Refunds & Disputes
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchCases(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
          >
            Export Report
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Cases
              </Typography>
              <Typography variant="h4">
                {stats.totalCases}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending Review
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pendingReview}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approved
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.approved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Refunded
              </Typography>
              <Typography variant="h4" color="primary.main">
                ${stats.totalRefunded}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Processing
              </Typography>
              <Typography variant="h4">
                {stats.avgProcessingTime}d
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Satisfaction
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.satisfactionRate}/5
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search cases..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Type"
              >
                <MenuItem value="ALL">All Types</MenuItem>
                <MenuItem value="REFUND">Refunds</MenuItem>
                <MenuItem value="DISPUTE">Disputes</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
                <MenuItem value="UNDER_REVIEW">Under Review</MenuItem>
                <MenuItem value="APPROVED">Approved</MenuItem>
                <MenuItem value="REJECTED">Rejected</MenuItem>
                <MenuItem value="PROCESSED">Processed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Cases Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Case #</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCases.map((caseItem) => (
                  <TableRow key={caseItem.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {caseItem.caseNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getTypeIcon(caseItem.type)}
                        label={caseItem.type}
                        size="small"
                        color={caseItem.type === 'REFUND' ? 'primary' : 'secondary'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {caseItem.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {caseItem.booking.vehicleInfo} • {caseItem.booking.dates}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                          {caseItem.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {caseItem.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {caseItem.user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        ${caseItem.amount}
                      </Typography>
                      {caseItem.refundAmount && (
                        <Typography variant="caption" color="success.main">
                          Refund: ${caseItem.refundAmount}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={caseItem.status.replace('_', ' ')}
                        size="small"
                        color={getStatusColor(caseItem.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(caseItem.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewCase(caseItem)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Case Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        {selectedCase && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedCase.caseNumber} - {selectedCase.title}
                </Typography>
                <Chip
                  icon={getTypeIcon(selectedCase.type)}
                  label={selectedCase.type}
                  color={selectedCase.type === 'REFUND' ? 'primary' : 'secondary'}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
                <Tab label="Overview" />
                <Tab label="Timeline" />
                <Tab label="Evidence" />
                <Tab label="Resolution" />
              </Tabs>

              {activeTab === 0 && (
                <Box sx={{ mt: 2 }}>
                  {/* Progress Stepper */}
                  <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Case Progress
                    </Typography>
                    <Stepper activeStep={getActiveStep(selectedCase.status, selectedCase.type)}>
                      {getStepperSteps(selectedCase.type).map((label) => (
                        <Step key={label}>
                          <StepLabel>{label}</StepLabel>
                        </Step>
                      ))}
                    </Stepper>
                  </Paper>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Case Details
                      </Typography>
                      <Stack spacing={2}>
                        <Box>
                          <Typography variant="caption">Category:</Typography>
                          <Chip label={selectedCase.category} size="small" sx={{ ml: 1 }} />
                        </Box>
                        <Box>
                          <Typography variant="caption">Priority:</Typography>
                          <Chip label={selectedCase.priority} size="small" sx={{ ml: 1 }} />
                        </Box>
                        <Box>
                          <Typography variant="caption">Amount:</Typography>
                          <Typography variant="body2" component="span" sx={{ ml: 1, fontWeight: 'bold' }}>
                            ${selectedCase.amount}
                          </Typography>
                        </Box>
                        {selectedCase.refundAmount && (
                          <Box>
                            <Typography variant="caption">Refund Amount:</Typography>
                            <Typography variant="body2" component="span" sx={{ ml: 1, fontWeight: 'bold', color: 'success.main' }}>
                              ${selectedCase.refundAmount}
                            </Typography>
                          </Box>
                        )}
                      </Stack>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Booking Information
                      </Typography>
                      <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                        <Typography variant="body2">
                          <strong>Vehicle:</strong> {selectedCase.booking.vehicleInfo}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Dates:</strong> {selectedCase.booking.dates}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Total Amount:</strong> ${selectedCase.booking.totalAmount}
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>
                        Description
                      </Typography>
                      <Typography variant="body2">
                        {selectedCase.description}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Case Timeline
                  </Typography>
                  <Stack spacing={2}>
                    {selectedCase.timeline.map((event, index) => (
                      <Paper key={event.id} sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box>
                            <Typography variant="subtitle2">
                              {event.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              by {event.actor}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(event.timestamp).toLocaleString()}
                          </Typography>
                        </Box>
                      </Paper>
                    ))}
                  </Stack>
                </Box>
              )}

              {activeTab === 2 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Evidence & Attachments
                  </Typography>
                  {selectedCase.evidence.length > 0 ? (
                    <Grid container spacing={2}>
                      {selectedCase.evidence.map((file, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index}>
                          <Paper sx={{ p: 2, textAlign: 'center' }}>
                            <ReceiptIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                            <Typography variant="body2">
                              {file}
                            </Typography>
                            <Button size="small" startIcon={<DownloadIcon />} sx={{ mt: 1 }}>
                              Download
                            </Button>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Alert severity="info">
                      No evidence files attached to this case.
                    </Alert>
                  )}
                </Box>
              )}

              {activeTab === 3 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={3}>
                    {selectedCase.type === 'REFUND' && (
                      <>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Refund Amount"
                            value={refundAmount}
                            onChange={(e) => setRefundAmount(Number(e.target.value))}
                            InputProps={{
                              startAdornment: '$'
                            }}
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Processing Fee"
                            value={processingFee}
                            onChange={(e) => setProcessingFee(Number(e.target.value))}
                            InputProps={{
                              startAdornment: '$'
                            }}
                          />
                        </Grid>
                      </>
                    )}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Resolution Notes"
                        placeholder="Enter resolution details and reasoning..."
                        value={resolutionNotes}
                        onChange={(e) => setResolutionNotes(e.target.value)}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Alert severity="info">
                        <Typography variant="body2">
                          <strong>Net Amount:</strong> ${refundAmount - processingFee} will be refunded to the customer.
                        </Typography>
                      </Alert>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Cancel
              </Button>
              {selectedCase.status === 'PENDING' || selectedCase.status === 'UNDER_REVIEW' ? (
                <>
                  <Button
                    color="error"
                    onClick={() => handleProcessCase('REJECT')}
                  >
                    Reject
                  </Button>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() => handleProcessCase('APPROVE')}
                  >
                    Approve
                  </Button>
                </>
              ) : selectedCase.status === 'APPROVED' ? (
                <Button
                  variant="contained"
                  startIcon={<ProcessIcon />}
                  onClick={() => handleProcessCase('PROCESS')}
                >
                  Process Payment
                </Button>
              ) : null}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default RefundsDisputes;
