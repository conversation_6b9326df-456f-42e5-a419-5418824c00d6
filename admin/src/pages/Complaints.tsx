import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab,
  Badge,
  LinearProgress,
  Divider,
  Avatar,
  Stack
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  Warning as WarningIcon,
  CheckCircle as ResolvedIcon,
  Schedule as PendingIcon,
  Assignment as AssignIcon,
  Message as MessageIcon,
  AttachFile as AttachIcon,
  Send as SendIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { AdminApiService } from '../services/AdminApiService';

interface Complaint {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  category: 'PAYMENT' | 'VEHICLE' | 'DAMAGE' | 'BOOKING' | 'SERVICE' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED' | 'ESCALATED';
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  booking?: {
    id: string;
    vehicleInfo: string;
    dates: string;
  };
  assignedTo?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolutionNotes?: string;
  attachments: string[];
  comments: ComplaintComment[];
  satisfaction?: number;
}

interface ComplaintComment {
  id: string;
  author: {
    id: string;
    name: string;
    role: 'USER' | 'ADMIN' | 'SUPPORT';
  };
  content: string;
  timestamp: string;
  isInternal: boolean;
}

interface ComplaintStats {
  total: number;
  open: number;
  inProgress: number;
  resolved: number;
  escalated: number;
  avgResolutionTime: number;
  satisfactionScore: number;
}

const Complaints: React.FC = () => {
  const [complaints, setComplaints] = useState<Complaint[]>([]);
  const [stats, setStats] = useState<ComplaintStats>({
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    escalated: 0,
    avgResolutionTime: 0,
    satisfactionScore: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedComplaint, setSelectedComplaint] = useState<Complaint | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [filterCategory, setFilterCategory] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [newComment, setNewComment] = useState('');
  const [statusUpdate, setStatusUpdate] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');

  useEffect(() => {
    fetchComplaints();
    fetchStats();
  }, [filterStatus, filterCategory, searchTerm]);

  const fetchComplaints = async () => {
    try {
      setLoading(true);
      // Get live complaints data from Supabase
      // Since we don't have a complaints table yet, show empty for now
      // In a real implementation, you would query your complaints table
      const liveComplaints: Complaint[] = [];
      setComplaints(liveComplaints);
    } catch (error) {
      console.error('Error fetching complaints:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Since we don't have a complaints table yet, show empty stats
      // In a real implementation, you would query your complaints table
      setStats({
        total: 0,
        open: 0,
        inProgress: 0,
        resolved: 0,
        escalated: 0,
        avgResolutionTime: 0,
        satisfactionScore: 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'error';
      case 'IN_PROGRESS': return 'warning';
      case 'RESOLVED': return 'success';
      case 'CLOSED': return 'default';
      case 'ESCALATED': return 'secondary';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      case 'LOW': return 'success';
      default: return 'default';
    }
  };

  const handleViewComplaint = (complaint: Complaint) => {
    setSelectedComplaint(complaint);
    setStatusUpdate(complaint.status);
    setResolutionNotes(complaint.resolutionNotes || '');
    setDialogOpen(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedComplaint) return;
    
    try {
      // API call to update status
      // await AdminApiService.updateComplaintStatus(selectedComplaint.id, statusUpdate, resolutionNotes);
      
      // Update local state
      setComplaints(prev => prev.map(complaint => 
        complaint.id === selectedComplaint.id 
          ? { ...complaint, status: statusUpdate as any, resolutionNotes }
          : complaint
      ));
      
      setDialogOpen(false);
      fetchStats(); // Refresh stats
    } catch (error) {
      console.error('Error updating complaint status:', error);
    }
  };

  const handleAddComment = async () => {
    if (!selectedComplaint || !newComment.trim()) return;
    
    try {
      const comment: ComplaintComment = {
        id: Date.now().toString(),
        author: { id: 'admin', name: 'Admin', role: 'ADMIN' },
        content: newComment,
        timestamp: new Date().toISOString(),
        isInternal: false
      };
      
      // API call to add comment
      // await AdminApiService.addComplaintComment(selectedComplaint.id, newComment);
      
      // Update local state
      setSelectedComplaint(prev => prev ? {
        ...prev,
        comments: [...prev.comments, comment]
      } : null);
      
      setNewComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const filteredComplaints = complaints.filter(complaint => {
    const matchesStatus = filterStatus === 'ALL' || complaint.status === filterStatus;
    const matchesCategory = filterCategory === 'ALL' || complaint.category === filterCategory;
    const matchesSearch = searchTerm === '' || 
      complaint.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      complaint.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesCategory && matchesSearch;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🚨 Complaints Management
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchComplaints(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
          >
            Export
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Complaints
              </Typography>
              <Typography variant="h4">
                {stats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Open
              </Typography>
              <Typography variant="h4" color="error">
                {stats.open}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.inProgress}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Resolved
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.resolved}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Resolution
              </Typography>
              <Typography variant="h4">
                {stats.avgResolutionTime}d
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Satisfaction
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.satisfactionScore}/5
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search complaints..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="OPEN">Open</MenuItem>
                <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                <MenuItem value="RESOLVED">Resolved</MenuItem>
                <MenuItem value="CLOSED">Closed</MenuItem>
                <MenuItem value="ESCALATED">Escalated</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                label="Category"
              >
                <MenuItem value="ALL">All Categories</MenuItem>
                <MenuItem value="PAYMENT">Payment</MenuItem>
                <MenuItem value="VEHICLE">Vehicle</MenuItem>
                <MenuItem value="DAMAGE">Damage</MenuItem>
                <MenuItem value="BOOKING">Booking</MenuItem>
                <MenuItem value="SERVICE">Service</MenuItem>
                <MenuItem value="OTHER">Other</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              Filter
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Complaints Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Ticket #</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredComplaints.map((complaint) => (
                  <TableRow key={complaint.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {complaint.ticketNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {complaint.title}
                      </Typography>
                      {complaint.booking && (
                        <Typography variant="caption" color="text.secondary">
                          {complaint.booking.vehicleInfo} • {complaint.booking.dates}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                          {complaint.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {complaint.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {complaint.user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={complaint.category}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={complaint.priority}
                        size="small"
                        color={getPriorityColor(complaint.priority) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={complaint.status.replace('_', ' ')}
                        size="small"
                        color={getStatusColor(complaint.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(complaint.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewComplaint(complaint)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Complaint Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedComplaint && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedComplaint.ticketNumber} - {selectedComplaint.title}
                </Typography>
                <IconButton onClick={() => setDialogOpen(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
                <Tab label="Details" />
                <Tab label={`Comments (${selectedComplaint.comments.length})`} />
                <Tab label="Resolution" />
              </Tabs>

              {activeTab === 0 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        User Information
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Avatar sx={{ mr: 2 }}>
                          {selectedComplaint.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body1">
                            {selectedComplaint.user.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedComplaint.user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Complaint Details
                      </Typography>
                      <Stack spacing={1}>
                        <Box>
                          <Typography variant="caption">Category:</Typography>
                          <Chip label={selectedComplaint.category} size="small" sx={{ ml: 1 }} />
                        </Box>
                        <Box>
                          <Typography variant="caption">Priority:</Typography>
                          <Chip 
                            label={selectedComplaint.priority} 
                            size="small" 
                            color={getPriorityColor(selectedComplaint.priority) as any}
                            sx={{ ml: 1 }} 
                          />
                        </Box>
                        <Box>
                          <Typography variant="caption">Status:</Typography>
                          <Chip 
                            label={selectedComplaint.status.replace('_', ' ')} 
                            size="small" 
                            color={getStatusColor(selectedComplaint.status) as any}
                            sx={{ ml: 1 }} 
                          />
                        </Box>
                      </Stack>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>
                        Description
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 2 }}>
                        {selectedComplaint.description}
                      </Typography>
                    </Grid>
                    {selectedComplaint.booking && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          Related Booking
                        </Typography>
                        <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <Typography variant="body2">
                            <strong>Vehicle:</strong> {selectedComplaint.booking.vehicleInfo}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Dates:</strong> {selectedComplaint.booking.dates}
                          </Typography>
                        </Paper>
                      </Grid>
                    )}
                    {selectedComplaint.attachments.length > 0 && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          Attachments
                        </Typography>
                        <Stack direction="row" spacing={1}>
                          {selectedComplaint.attachments.map((attachment, index) => (
                            <Chip
                              key={index}
                              label={attachment}
                              icon={<AttachIcon />}
                              variant="outlined"
                              clickable
                            />
                          ))}
                        </Stack>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              )}

              {activeTab === 1 && (
                <Box sx={{ mt: 2 }}>
                  <Stack spacing={2}>
                    {selectedComplaint.comments.map((comment) => (
                      <Paper key={comment.id} sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                              {comment.author.name.charAt(0)}
                            </Avatar>
                            <Typography variant="subtitle2">
                              {comment.author.name}
                            </Typography>
                            <Chip
                              label={comment.author.role}
                              size="small"
                              variant="outlined"
                              sx={{ ml: 1 }}
                            />
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(comment.timestamp).toLocaleString()}
                          </Typography>
                        </Box>
                        <Typography variant="body2">
                          {comment.content}
                        </Typography>
                      </Paper>
                    ))}
                    
                    <Divider />
                    
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Add Comment
                      </Typography>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        placeholder="Type your response..."
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        sx={{ mb: 1 }}
                      />
                      <Button
                        variant="contained"
                        startIcon={<SendIcon />}
                        onClick={handleAddComment}
                        disabled={!newComment.trim()}
                      >
                        Send Comment
                      </Button>
                    </Box>
                  </Stack>
                </Box>
              )}

              {activeTab === 2 && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Update Status</InputLabel>
                        <Select
                          value={statusUpdate}
                          onChange={(e) => setStatusUpdate(e.target.value)}
                          label="Update Status"
                        >
                          <MenuItem value="OPEN">Open</MenuItem>
                          <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                          <MenuItem value="RESOLVED">Resolved</MenuItem>
                          <MenuItem value="CLOSED">Closed</MenuItem>
                          <MenuItem value="ESCALATED">Escalated</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Resolution Notes"
                        placeholder="Enter resolution details..."
                        value={resolutionNotes}
                        onChange={(e) => setResolutionNotes(e.target.value)}
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleUpdateStatus}
              >
                Update Complaint
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default Complaints;
