import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Divider,
  Alert,
  Alert<PERSON>itle,
  Snackbar,
  Tab,
  Tabs,
  CircularProgress
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security,
  Notifications,
  System,
  Payment as PaymentIcon,
  Business as BusinessIcon,
  Save as SaveIcon
} from '@mui/icons-material';

interface PlatformSettings {
  platformName: string;
  supportEmail: string;
  commissionRate: number;
  currency: string;
  twoFactorAuth: boolean;
  emailVerificationRequired: boolean;
  phoneVerificationRequired: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  paymentGateway: string;
  stripePublicKey: string;
  stripeSecretKey: string;
}

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<PlatformSettings>({
    platformName: 'RentaHub',
    supportEmail: '<EMAIL>',
    commissionRate: 15,
    currency: 'USD',
    twoFactorAuth: true,
    emailVerificationRequired: true,
    phoneVerificationRequired: false,
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: false,
    paymentGateway: 'stripe',
    stripePublicKey: '',
    stripeSecretKey: ''
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleSettingChange = (key: keyof PlatformSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Here you would save to your backend/Supabase
      // For now, we'll simulate a save operation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSaveSuccess(true);
      console.log('Settings saved:', settings);
    } catch (error) {
      setError('Failed to save settings. Please try again.');
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResetSettings = () => {
    setSettings({
      platformName: 'RentaHub',
      supportEmail: '<EMAIL>',
      commissionRate: 15,
      currency: 'USD',
      twoFactorAuth: true,
      emailVerificationRequired: true,
      phoneVerificationRequired: false,
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: false,
      paymentGateway: 'stripe',
      stripePublicKey: '',
      stripeSecretKey: ''
    });
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Platform Settings
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Configure your RentaHub platform settings, security options, and integrations.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="General" icon={<BusinessIcon />} />
          <Tab label="Security" icon={<Security />} />
          <Tab label="Notifications" icon={<Notifications />} />
          <Tab label="Payments" icon={<PaymentIcon />} />
        </Tabs>
      </Paper>

      <Grid container spacing={3}>
        {/* General Settings Tab */}
        {activeTab === 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  General Platform Settings
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Platform Name"
                      value={settings.platformName}
                      onChange={(e) => handleSettingChange('platformName', e.target.value)}
                      variant="outlined"
                      helperText="The name of your rental platform"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Support Email"
                      value={settings.supportEmail}
                      onChange={(e) => handleSettingChange('supportEmail', e.target.value)}
                      variant="outlined"
                      type="email"
                      helperText="Email address for customer support"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Commission Rate (%)"
                      value={settings.commissionRate}
                      onChange={(e) => handleSettingChange('commissionRate', parseFloat(e.target.value) || 0)}
                      type="number"
                      variant="outlined"
                      helperText="Platform commission percentage"
                      inputProps={{ min: 0, max: 100, step: 0.1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Currency"
                      value={settings.currency}
                      onChange={(e) => handleSettingChange('currency', e.target.value)}
                      variant="outlined"
                      helperText="Default platform currency"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Security Settings Tab */}
        {activeTab === 1 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Security & Authentication
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.twoFactorAuth}
                          onChange={(e) => handleSettingChange('twoFactorAuth', e.target.checked)}
                        />
                      }
                      label="Two-Factor Authentication"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Require 2FA for admin accounts
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.emailVerificationRequired}
                          onChange={(e) => handleSettingChange('emailVerificationRequired', e.target.checked)}
                        />
                      }
                      label="Email Verification Required"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Require email verification for new users
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.phoneVerificationRequired}
                          onChange={(e) => handleSettingChange('phoneVerificationRequired', e.target.checked)}
                        />
                      }
                      label="Phone Verification Required"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Require phone verification for providers
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Notifications Tab */}
        {activeTab === 2 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Notification Settings
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.emailNotifications}
                          onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                        />
                      }
                      label="Email Notifications"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Send booking confirmations and updates via email
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.smsNotifications}
                          onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
                        />
                      }
                      label="SMS Notifications"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Send important updates via SMS
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.pushNotifications}
                          onChange={(e) => handleSettingChange('pushNotifications', e.target.checked)}
                        />
                      }
                      label="Push Notifications"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
                      Send push notifications to mobile apps
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Payments Tab */}
        {activeTab === 3 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Payment Gateway Settings
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Alert severity="info" sx={{ mb: 3 }}>
                      <AlertTitle>Payment Integration</AlertTitle>
                      Configure your payment gateway settings. These keys will be encrypted and stored securely.
                    </Alert>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Stripe Publishable Key"
                      value={settings.stripePublicKey}
                      onChange={(e) => handleSettingChange('stripePublicKey', e.target.value)}
                      variant="outlined"
                      helperText="Your Stripe publishable key (pk_...)"
                      placeholder="pk_test_..."
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Stripe Secret Key"
                      value={settings.stripeSecretKey}
                      onChange={(e) => handleSettingChange('stripeSecretKey', e.target.value)}
                      variant="outlined"
                      type="password"
                      helperText="Your Stripe secret key (sk_...)"
                      placeholder="sk_test_..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Save/Reset Buttons */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={handleResetSettings}
                disabled={loading}
              >
                Reset to Defaults
              </Button>
              <Button
                variant="contained"
                onClick={handleSaveSettings}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Success Snackbar */}
      <Snackbar
        open={saveSuccess}
        autoHideDuration={6000}
        onClose={() => setSaveSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSaveSuccess(false)} severity="success" sx={{ width: '100%' }}>
          Settings saved successfully!
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Settings;
