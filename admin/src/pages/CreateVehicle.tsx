import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  Alert,
  Divider
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as BackIcon,
  ArrowForward as ForwardIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import FileUpload from '../components/FileUpload';

interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  category: string;
  pricePerHour: number;
  pricePerDay: number;
  location: string;
  description: string;
  features: string[];
  fuelType: string;
  transmission: string;
  seats: number;
  color: string;
  licensePlate: string;
  providerId: string;
}

const steps = ['Basic Information', 'Pricing & Location', 'Features & Details', 'Images & Documents'];

const CreateVehicle: React.FC = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<Array<{ url: string; id: string; name: string }>>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<Array<{ url: string; id: string; name: string }>>([]);

  const [formData, setFormData] = useState<VehicleFormData>({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    category: '',
    pricePerHour: 0,
    pricePerDay: 0,
    location: '',
    description: '',
    features: [],
    fuelType: '',
    transmission: '',
    seats: 4,
    color: '',
    licensePlate: '',
    providerId: ''
  });

  const [errors, setErrors] = useState<Partial<VehicleFormData>>({});

  const handleInputChange = (field: keyof VehicleFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<VehicleFormData> = {};

    switch (step) {
      case 0: // Basic Information
        if (!formData.make) newErrors.make = 'Make is required';
        if (!formData.model) newErrors.model = 'Model is required';
        if (!formData.year || formData.year < 1900) newErrors.year = 'Valid year is required';
        if (!formData.category) newErrors.category = 'Category is required';
        break;
      
      case 1: // Pricing & Location
        if (!formData.pricePerHour || formData.pricePerHour <= 0) newErrors.pricePerHour = 'Valid hourly price is required';
        if (!formData.pricePerDay || formData.pricePerDay <= 0) newErrors.pricePerDay = 'Valid daily price is required';
        if (!formData.location) newErrors.location = 'Location is required';
        break;
      
      case 2: // Features & Details
        if (!formData.fuelType) newErrors.fuelType = 'Fuel type is required';
        if (!formData.transmission) newErrors.transmission = 'Transmission is required';
        if (!formData.seats || formData.seats < 1) newErrors.seats = 'Valid seat count is required';
        break;
      
      case 3: // Images & Documents
        if (uploadedImages.length === 0) {
          toast.error('At least one vehicle image is required');
          return false;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep)) return;

    setLoading(true);
    try {
      // Mock API call - replace with actual implementation
      const vehicleData = {
        ...formData,
        images: uploadedImages,
        documents: uploadedDocuments
      };

      console.log('Creating vehicle:', vehicleData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Vehicle created successfully!');
      navigate('/vehicles');
    } catch (error) {
      console.error('Failed to create vehicle:', error);
      toast.error('Failed to create vehicle. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Make"
                value={formData.make}
                onChange={(e) => handleInputChange('make', e.target.value)}
                error={!!errors.make}
                helperText={errors.make}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Model"
                value={formData.model}
                onChange={(e) => handleInputChange('model', e.target.value)}
                error={!!errors.model}
                helperText={errors.model}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Year"
                type="number"
                value={formData.year}
                onChange={(e) => handleInputChange('year', parseInt(e.target.value))}
                error={!!errors.year}
                helperText={errors.year}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.category} required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <MenuItem value="sedan">Sedan</MenuItem>
                  <MenuItem value="suv">SUV</MenuItem>
                  <MenuItem value="hatchback">Hatchback</MenuItem>
                  <MenuItem value="coupe">Coupe</MenuItem>
                  <MenuItem value="convertible">Convertible</MenuItem>
                  <MenuItem value="truck">Truck</MenuItem>
                  <MenuItem value="van">Van</MenuItem>
                  <MenuItem value="luxury">Luxury</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe the vehicle's condition, special features, etc."
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Price per Hour ($)"
                type="number"
                value={formData.pricePerHour}
                onChange={(e) => handleInputChange('pricePerHour', parseFloat(e.target.value))}
                error={!!errors.pricePerHour}
                helperText={errors.pricePerHour}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Price per Day ($)"
                type="number"
                value={formData.pricePerDay}
                onChange={(e) => handleInputChange('pricePerDay', parseFloat(e.target.value))}
                error={!!errors.pricePerDay}
                helperText={errors.pricePerDay}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                error={!!errors.location}
                helperText={errors.location}
                placeholder="City, State or specific address"
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="License Plate"
                value={formData.licensePlate}
                onChange={(e) => handleInputChange('licensePlate', e.target.value)}
                placeholder="ABC-1234"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Provider ID"
                value={formData.providerId}
                onChange={(e) => handleInputChange('providerId', e.target.value)}
                placeholder="Leave empty for admin-managed vehicle"
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.fuelType} required>
                <InputLabel>Fuel Type</InputLabel>
                <Select
                  value={formData.fuelType}
                  label="Fuel Type"
                  onChange={(e) => handleInputChange('fuelType', e.target.value)}
                >
                  <MenuItem value="gasoline">Gasoline</MenuItem>
                  <MenuItem value="diesel">Diesel</MenuItem>
                  <MenuItem value="electric">Electric</MenuItem>
                  <MenuItem value="hybrid">Hybrid</MenuItem>
                  <MenuItem value="plugin-hybrid">Plug-in Hybrid</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.transmission} required>
                <InputLabel>Transmission</InputLabel>
                <Select
                  value={formData.transmission}
                  label="Transmission"
                  onChange={(e) => handleInputChange('transmission', e.target.value)}
                >
                  <MenuItem value="automatic">Automatic</MenuItem>
                  <MenuItem value="manual">Manual</MenuItem>
                  <MenuItem value="cvt">CVT</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Number of Seats"
                type="number"
                value={formData.seats}
                onChange={(e) => handleInputChange('seats', parseInt(e.target.value))}
                error={!!errors.seats}
                helperText={errors.seats}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                placeholder="e.g., Black, White, Red"
              />
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Vehicle Images
              </Typography>
              <FileUpload
                onFilesUploaded={setUploadedImages}
                acceptedTypes={['image/*']}
                maxFiles={10}
                maxSizeInMB={5}
                uploadType="vehicle"
              />
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Documents (Optional)
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Upload registration, insurance, or other relevant documents
              </Typography>
              <FileUpload
                onFilesUploaded={setUploadedDocuments}
                acceptedTypes={['.pdf', '.doc', '.docx', 'image/*']}
                maxFiles={5}
                maxSizeInMB={10}
                uploadType="document"
              />
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<BackIcon />}
          onClick={() => navigate('/vehicles')}
          sx={{ mr: 2 }}
        >
          Back to Vehicles
        </Button>
        <Typography variant="h4" component="h1">
          Create New Vehicle
        </Typography>
      </Box>

      <Paper sx={{ p: 3 }}>
        {/* Stepper */}
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {steps[activeStep]}
            </Typography>
            {renderStepContent(activeStep)}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
            startIcon={<BackIcon />}
          >
            Back
          </Button>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={loading}
                startIcon={<SaveIcon />}
              >
                {loading ? 'Creating...' : 'Create Vehicle'}
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                endIcon={<ForwardIcon />}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>

        {/* Progress Info */}
        {activeStep === steps.length - 1 && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              Review all information before creating the vehicle. You can edit these details later if needed.
            </Typography>
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default CreateVehicle;
