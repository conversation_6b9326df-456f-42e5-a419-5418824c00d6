import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import {
  Reply as ReplyIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Support as SupportIcon,
  PriorityHigh as HighPriorityIcon,
  Schedule as PendingIcon,
  CheckCircle as ResolvedIcon
} from '@mui/icons-material';
import { AdminApiService } from '../services/AdminApiService';
import { toast } from 'react-toastify';

interface SupportTicket {
  id: string;
  userId: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  user?: {
    name: string;
    email: string;
  };
  responses?: Array<{
    id: string;
    message: string;
    isAdmin: boolean;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

const SupportTickets: React.FC = () => {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [responseText, setResponseText] = useState('');

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const response = await AdminApiService.getSupportTickets(page + 1, rowsPerPage, { 
        search, 
        status: statusFilter,
        priority: priorityFilter
      });
      setTickets(response.tickets);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to fetch support tickets:', error);
      toast.error('Failed to fetch support tickets');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, [page, rowsPerPage, search, statusFilter, priorityFilter]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleStatusFilter = (event: any) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handlePriorityFilter = (event: any) => {
    setPriorityFilter(event.target.value);
    setPage(0);
  };

  const handleViewTicket = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
    setViewDialogOpen(true);
    setResponseText('');
  };

  const handleUpdateTicketStatus = async (status: string) => {
    if (!selectedTicket) return;

    try {
      await AdminApiService.updateTicketStatus(selectedTicket.id, status);
      toast.success('Ticket status updated successfully');
      fetchTickets();
      // Update the selected ticket status for immediate UI feedback
      setSelectedTicket({ ...selectedTicket, status });
    } catch (error) {
      console.error('Failed to update ticket status:', error);
      toast.error('Failed to update ticket status');
    }
  };

  const handleAddResponse = async () => {
    if (!selectedTicket || !responseText.trim()) return;

    try {
      await AdminApiService.addTicketResponse(selectedTicket.id, responseText);
      toast.success('Response added successfully');
      setResponseText('');
      fetchTickets();
      // Close dialog and reopen to refresh data
      setViewDialogOpen(false);
      setTimeout(() => {
        handleViewTicket(selectedTicket);
      }, 100);
    } catch (error) {
      console.error('Failed to add response:', error);
      toast.error('Failed to add response');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'error';
      case 'IN_PROGRESS':
        return 'warning';
      case 'RESOLVED':
        return 'success';
      case 'CLOSED':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'error';
      case 'MEDIUM':
        return 'warning';
      case 'LOW':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Support Tickets
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Tickets
                  </Typography>
                  <Typography variant="h4">
                    {total}
                  </Typography>
                </Box>
                <SupportIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Open Tickets
                  </Typography>
                  <Typography variant="h4">
                    {tickets.filter(t => t.status === 'OPEN').length}
                  </Typography>
                </Box>
                <PendingIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    High Priority
                  </Typography>
                  <Typography variant="h4">
                    {tickets.filter(t => t.priority === 'HIGH').length}
                  </Typography>
                </Box>
                <HighPriorityIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Resolved Today
                  </Typography>
                  <Typography variant="h4">
                    {tickets.filter(t => t.status === 'RESOLVED').length}
                  </Typography>
                </Box>
                <ResolvedIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ p: 3, mt: 2 }}>
        {/* Search and Filters */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, gap: 2, flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search tickets..."
            value={search}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ width: 300 }}
          />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={handleStatusFilter}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="OPEN">Open</MenuItem>
                <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                <MenuItem value="RESOLVED">Resolved</MenuItem>
                <MenuItem value="CLOSED">Closed</MenuItem>
              </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Priority</InputLabel>
              <Select
                value={priorityFilter}
                label="Priority"
                onChange={handlePriorityFilter}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="HIGH">High</MenuItem>
                <MenuItem value="MEDIUM">Medium</MenuItem>
                <MenuItem value="LOW">Low</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Tickets Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ticket ID</TableCell>
                    <TableCell>Subject</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tickets.map((ticket) => (
                    <TableRow key={ticket.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          #{ticket.id.slice(-8)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200 }}>
                          {ticket.subject}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {ticket.user?.name || 'N/A'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {ticket.user?.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={ticket.category}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={ticket.priority}
                          color={getPriorityColor(ticket.priority) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={ticket.status}
                          color={getStatusColor(ticket.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {formatDate(ticket.createdAt)}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => handleViewTicket(ticket)}
                          size="small"
                        >
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={total}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(event) => {
                setRowsPerPage(parseInt(event.target.value, 10));
                setPage(0);
              }}
            />
          </>
        )}
      </Paper>

      {/* View Ticket Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Ticket #{selectedTicket?.id.slice(-8)} - {selectedTicket?.subject}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                label={selectedTicket?.priority}
                color={getPriorityColor(selectedTicket?.priority || '') as any}
                size="small"
              />
              <Chip
                label={selectedTicket?.status}
                color={getStatusColor(selectedTicket?.status || '') as any}
                size="small"
              />
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedTicket && (
            <Box>
              {/* Ticket Info */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Customer</Typography>
                  <Typography>{selectedTicket.user?.name}</Typography>
                  <Typography variant="body2" color="text.secondary">{selectedTicket.user?.email}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Category</Typography>
                  <Typography>{selectedTicket.category}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                  <Typography>{selectedTicket.description}</Typography>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              {/* Status Update */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>Update Status</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant={selectedTicket.status === 'OPEN' ? 'contained' : 'outlined'}
                    onClick={() => handleUpdateTicketStatus('OPEN')}
                  >
                    Open
                  </Button>
                  <Button
                    size="small"
                    variant={selectedTicket.status === 'IN_PROGRESS' ? 'contained' : 'outlined'}
                    onClick={() => handleUpdateTicketStatus('IN_PROGRESS')}
                  >
                    In Progress
                  </Button>
                  <Button
                    size="small"
                    variant={selectedTicket.status === 'RESOLVED' ? 'contained' : 'outlined'}
                    onClick={() => handleUpdateTicketStatus('RESOLVED')}
                  >
                    Resolved
                  </Button>
                  <Button
                    size="small"
                    variant={selectedTicket.status === 'CLOSED' ? 'contained' : 'outlined'}
                    onClick={() => handleUpdateTicketStatus('CLOSED')}
                  >
                    Closed
                  </Button>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Responses */}
              <Typography variant="subtitle2" gutterBottom>Responses</Typography>
              {selectedTicket.responses && selectedTicket.responses.length > 0 ? (
                <List>
                  {selectedTicket.responses.map((response) => (
                    <ListItem key={response.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2">
                              {response.isAdmin ? 'Admin' : 'Customer'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatDate(response.createdAt)}
                            </Typography>
                          </Box>
                        }
                        secondary={response.message}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary">No responses yet</Typography>
              )}

              {/* Add Response */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" gutterBottom>Add Response</Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Type your response..."
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  sx={{ mb: 2 }}
                />
                <Button
                  variant="contained"
                  startIcon={<ReplyIcon />}
                  onClick={handleAddResponse}
                  disabled={!responseText.trim()}
                >
                  Send Response
                </Button>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SupportTickets;
