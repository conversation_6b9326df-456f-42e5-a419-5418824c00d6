import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Chip,
  Button,
  Alert,
  AlertTitle
} from '@mui/material';
import {
  People as PeopleIcon,
  DirectionsCar as CarIcon,
  BookOnline as BookingIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Add as AddIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { SupabaseDashboardService, DashboardStats } from '../services/SupabaseDashboardService';
import EmptyState from '../components/EmptyState';
import GetStartedCTA from '../components/GetStartedCTA';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const liveStats = await SupabaseDashboardService.getDashboardStats();
        setStats(liveStats);
      } catch (error) {
        console.error('Failed to fetch dashboard stats from Supabase:', error);
        setError('Failed to load dashboard data. Please check your database connection.');
        // Fallback to empty stats with CTA
        setStats({
          users: {
            total: 0,
            recent: [],
            growth: { total: 0, period: 'month' }
          },
          vehicles: {
            total: 0,
            available: 0,
            recent: []
          },
          bookings: {
            total: 0,
            active: 0,
            recent: []
          },
          financials: {
            totalRevenue: 0,
            monthlyRevenue: 0,
            pendingPayments: 0
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  const hasNoData = stats && (
    stats.users.total === 0 &&
    stats.vehicles.total === 0 &&
    stats.bookings.total === 0 &&
    stats.financials.totalRevenue === 0
  );

  const handleNavigateToUsers = () => {
    navigate('/users');
  };

  const handleNavigateToVehicles = () => {
    navigate('/vehicles');
  };

  const handleNavigateToSettings = () => {
    navigate('/settings');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  const StatCard = ({ title, value, icon, subtitle, color = 'primary' }: any) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box color={color}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard Overview
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Database Connection Error</AlertTitle>
          {error}
        </Alert>
      )}

      {hasNoData && !error && (
        <Box sx={{ mb: 3 }}>
          <GetStartedCTA
            onNavigateToUsers={handleNavigateToUsers}
            onNavigateToVehicles={handleNavigateToVehicles}
            onNavigateToSettings={handleNavigateToSettings}
          />
        </Box>
      )}

      {!hasNoData && (
        <Grid container spacing={3}>
        {/* Users Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={stats?.users.total || 0}
            icon={<PeopleIcon fontSize="large" />}
            subtitle={`${stats?.users.activeUsers || 0} active • +${stats?.users.newUsersThisMonth || 0} this month`}
            color="primary"
          />
        </Grid>

        {/* Vehicles Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Vehicles"
            value={stats?.vehicles.total || 0}
            icon={<CarIcon fontSize="large" />}
            subtitle={`${stats?.vehicles.available || 0} available • ${stats?.vehicles.rented || 0} rented`}
            color="success"
          />
        </Grid>

        {/* Bookings Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Bookings"
            value={stats?.bookings.total || 0}
            icon={<BookingIcon fontSize="large" />}
            subtitle={`${stats?.bookings.active || 0} active • ${stats?.bookings.completed || 0} completed`}
            color="info"
          />
        </Grid>

        {/* Financial Statistics */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${(stats?.financials.totalRevenue || 0).toLocaleString()}`}
            icon={<MoneyIcon fontSize="large" />}
            subtitle={`$${(stats?.financials.monthlyRevenue || 0).toLocaleString()} this month`}
            color="success"
          />
        </Grid>

        {/* Additional Stats Row */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Providers"
            value={stats?.providers.total || 0}
            icon={<PeopleIcon fontSize="large" />}
            subtitle={`${stats?.providers.active || 0} active • ${stats?.providers.pending || 0} pending`}
            color="warning"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg Booking Value"
            value={`$${(stats?.financials.averageBookingValue || 0).toFixed(0)}`}
            icon={<MoneyIcon fontSize="large" />}
            subtitle={`${stats?.financials.completedPayments || 0} completed payments`}
            color="info"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Payments"
            value={stats?.financials.pendingPayments || 0}
            icon={<WarningIcon fontSize="large" />}
            subtitle="Requires attention"
            color="warning"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Maintenance"
            value={stats?.vehicles.maintenance || 0}
            icon={<CarIcon fontSize="large" />}
            subtitle="Vehicles in maintenance"
            color="error"
          />
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          {stats?.users.recent.length === 0 ? (
            <EmptyState
              title="No Users Yet"
              description="Start by inviting users to join your platform"
              actionText="Manage Users"
              actionIcon={<PeopleIcon />}
              onAction={handleNavigateToUsers}
            />
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Users
                </Typography>
                <List>
                  {stats?.users.recent.map((user, index) => (
                    <React.Fragment key={user.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar>{user.name?.charAt(0) || user.email.charAt(0)}</Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={user.name || user.email}
                          secondary={user.email}
                        />
                        <Chip
                          label={user.role || "New"}
                          size="small"
                          color={user.role === 'PROVIDER' ? 'secondary' : 'primary'}
                        />
                      </ListItem>
                      {index < stats.users.recent.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Recent Bookings */}
        <Grid item xs={12} md={6}>
          {stats?.bookings.recent.length === 0 ? (
            <EmptyState
              title="No Bookings Yet"
              description="Bookings will appear here once users start renting vehicles"
              actionText="Manage Vehicles"
              actionIcon={<CarIcon />}
              onAction={handleNavigateToVehicles}
            />
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Bookings
                </Typography>
                <List>
                  {stats?.bookings.recent.map((booking, index) => (
                    <React.Fragment key={booking.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: booking.status === 'confirmed' || booking.status === 'active' ? 'success.main' : 'grey.500' }}>
                            <BookingIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${booking.user} - ${booking.vehicle}`}
                          secondary={`${booking.status} • ${booking.startDate ? new Date(booking.startDate).toLocaleDateString() : ''}`}
                        />
                        <Chip
                          label={booking.status}
                          size="small"
                          color={booking.status === 'confirmed' || booking.status === 'active' ? 'success' : 'default'}
                        />
                      </ListItem>
                      {index < stats.bookings.recent.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
      )}
    </Container>
  );
};

export default Dashboard;