import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Avatar,
  Stack,
  Divider,
  DatePicker
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalance as BankIcon,
  CreditCard as CardIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { adminSupabase as supabase } from '../services/adminSupabaseClient';

interface Transaction {
  id: string;
  transactionId: string;
  type: 'PAYMENT' | 'REFUND' | 'PAYOUT' | 'FEE' | 'DEPOSIT';
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  amount: number;
  currency: string;
  description: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: 'CUSTOMER' | 'PROVIDER' | 'ADMIN';
  };
  booking?: {
    id: string;
    vehicleInfo: string;
    dates: string;
  };
  paymentMethod: {
    type: 'CREDIT_CARD' | 'BANK_TRANSFER' | 'PAYPAL' | 'STRIPE' | 'CASH';
    last4?: string;
    brand?: string;
  };
  fees: {
    platformFee: number;
    processingFee: number;
    taxes: number;
  };
  createdAt: string;
  completedAt?: string;
  failureReason?: string;
  metadata: Record<string, any>;
}

interface TransactionStats {
  totalVolume: number;
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  totalFees: number;
  avgTransactionValue: number;
  successRate: number;
}

const TransactionHistory: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [stats, setStats] = useState<TransactionStats>({
    totalVolume: 0,
    totalTransactions: 0,
    successfulTransactions: 0,
    failedTransactions: 0,
    totalFees: 0,
    avgTransactionValue: 0,
    successRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filterType, setFilterType] = useState<string>('ALL');
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{ start: Date | null; end: Date | null }>({
    start: null,
    end: null
  });

  useEffect(() => {
    fetchTransactions();
    fetchStats();
  }, [filterType, filterStatus, searchTerm, dateRange]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      // Get live transaction data from Supabase Payment table
      const { data: payments, error } = await supabase
        .from('Payment')
        .select('*')
        .order('transactionDate', { ascending: false });

      if (error) {
        console.error('Error fetching payments:', error);
        setTransactions([]);
        return;
      }

      // Convert Payment records to Transaction format
      const liveTransactions: Transaction[] = (payments || []).map(payment => ({
        id: payment.id,
        transactionId: payment.id,
        type: 'PAYMENT' as const,
        status: payment.status === 'COMPLETED' ? 'COMPLETED' as const : 'PENDING' as const,
        amount: payment.amount,
        currency: 'MYR',
        description: `Payment for booking ${payment.bookingId}`,
        createdAt: payment.transactionDate,
        updatedAt: payment.transactionDate,
        booking: {
          id: payment.bookingId,
          vehicleDetails: 'Vehicle details not available',
          customerName: 'Customer name not available',
          startDate: payment.transactionDate,
          endDate: payment.transactionDate
        },
        provider: {
          id: payment.providerId,
          name: `Provider ${payment.providerId}`,
          email: '<EMAIL>'
        },
        paymentMethod: {
          type: payment.paymentMethod,
          last4: '****',
          brand: 'Unknown'
        },
        fees: {
          platformFee: payment.amount * 0.05, // 5% platform fee
          processingFee: payment.amount * 0.029, // 2.9% processing fee
          providerEarnings: payment.providerEarnings
        }
      }));

      setTransactions(liveTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Get live transaction statistics from Supabase Payment table
      const { data: payments, error } = await supabase
        .from('Payment')
        .select('amount, status, providerEarnings');

      if (error) {
        console.error('Error fetching payment stats:', error);
        setStats({
          totalVolume: 0,
          totalTransactions: 0,
          successfulTransactions: 0,
          failedTransactions: 0,
          totalFees: 0,
          avgTransactionValue: 0,
          successRate: 0
        });
        return;
      }

      const totalTransactions = payments?.length || 0;
      const successfulTransactions = payments?.filter(p => p.status === 'COMPLETED').length || 0;
      const failedTransactions = totalTransactions - successfulTransactions;
      const totalVolume = payments?.reduce((sum, p) => sum + (p.amount || 0), 0) || 0;
      const totalFees = payments?.reduce((sum, p) => sum + ((p.amount || 0) * 0.079), 0) || 0; // 7.9% total fees
      const avgTransactionValue = totalTransactions > 0 ? totalVolume / totalTransactions : 0;
      const successRate = totalTransactions > 0 ? (successfulTransactions / totalTransactions) * 100 : 0;

      setStats({
        totalVolume,
        totalTransactions,
        successfulTransactions,
        failedTransactions,
        totalFees,
        avgTransactionValue,
        successRate
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Set empty stats on error
      setStats({
        totalVolume: 0,
        totalTransactions: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        totalFees: 0,
        avgTransactionValue: 0,
        successRate: 0
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'error';
      case 'CANCELLED': return 'default';
      default: return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PAYMENT': return 'primary';
      case 'REFUND': return 'warning';
      case 'PAYOUT': return 'success';
      case 'FEE': return 'info';
      case 'DEPOSIT': return 'secondary';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'PAYMENT': return <TrendingUpIcon />;
      case 'REFUND': return <TrendingDownIcon />;
      case 'PAYOUT': return <BankIcon />;
      case 'FEE': return <MoneyIcon />;
      case 'DEPOSIT': return <AccountBalance />;
      default: return <ReceiptIcon />;
    }
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'CREDIT_CARD': return <CardIcon />;
      case 'BANK_TRANSFER': return <BankIcon />;
      default: return <ReceiptIcon />;
    }
  };

  const handleViewTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setDialogOpen(true);
  };

  const handleExportTransactions = () => {
    // Implement export functionality
    console.log('Exporting transactions...');
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesType = filterType === 'ALL' || transaction.type === filterType;
    const matchesStatus = filterStatus === 'ALL' || transaction.status === filterStatus;
    const matchesSearch = searchTerm === '' || 
      transaction.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesType && matchesStatus && matchesSearch;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          📊 Transaction History
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchTransactions(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportTransactions}
          >
            Export
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Volume
              </Typography>
              <Typography variant="h5" color="primary.main">
                ${stats.totalVolume.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Transactions
              </Typography>
              <Typography variant="h5">
                {stats.totalTransactions.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Successful
              </Typography>
              <Typography variant="h5" color="success.main">
                {stats.successfulTransactions.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Failed
              </Typography>
              <Typography variant="h5" color="error.main">
                {stats.failedTransactions}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Success Rate
              </Typography>
              <Typography variant="h5" color="success.main">
                {stats.successRate}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Value
              </Typography>
              <Typography variant="h5">
                ${stats.avgTransactionValue}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Type"
              >
                <MenuItem value="ALL">All Types</MenuItem>
                <MenuItem value="PAYMENT">Payments</MenuItem>
                <MenuItem value="REFUND">Refunds</MenuItem>
                <MenuItem value="PAYOUT">Payouts</MenuItem>
                <MenuItem value="FEE">Fees</MenuItem>
                <MenuItem value="DEPOSIT">Deposits</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="COMPLETED">Completed</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
                <MenuItem value="FAILED">Failed</MenuItem>
                <MenuItem value="CANCELLED">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Stack direction="row" spacing={1}>
              <TextField
                type="date"
                label="Start Date"
                InputLabelProps={{ shrink: true }}
                size="small"
              />
              <TextField
                type="date"
                label="End Date"
                InputLabelProps={{ shrink: true }}
                size="small"
              />
            </Stack>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
            >
              Apply Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Transactions Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Transaction ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Payment Method</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {transaction.transactionId}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {transaction.description}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getTypeIcon(transaction.type)}
                        label={transaction.type}
                        size="small"
                        color={getTypeColor(transaction.type) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                          {transaction.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {transaction.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {transaction.user.role}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        ${transaction.amount.toFixed(2)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {transaction.currency}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getPaymentMethodIcon(transaction.paymentMethod.type)}
                        <Box sx={{ ml: 1 }}>
                          <Typography variant="body2">
                            {transaction.paymentMethod.type.replace('_', ' ')}
                          </Typography>
                          {transaction.paymentMethod.last4 && (
                            <Typography variant="caption" color="text.secondary">
                              •••• {transaction.paymentMethod.last4}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={transaction.status}
                        size="small"
                        color={getStatusColor(transaction.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(transaction.createdAt).toLocaleTimeString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewTransaction(transaction)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Transaction Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedTransaction && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  Transaction Details
                </Typography>
                <Chip
                  label={selectedTransaction.status}
                  color={getStatusColor(selectedTransaction.status) as any}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Transaction Information
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="caption">Transaction ID:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {selectedTransaction.transactionId}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Type:</Typography>
                      <Chip
                        icon={getTypeIcon(selectedTransaction.type)}
                        label={selectedTransaction.type}
                        size="small"
                        color={getTypeColor(selectedTransaction.type) as any}
                        sx={{ ml: 1 }}
                      />
                    </Box>
                    <Box>
                      <Typography variant="caption">Amount:</Typography>
                      <Typography variant="h6" color="primary.main">
                        ${selectedTransaction.amount.toFixed(2)} {selectedTransaction.currency}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Description:</Typography>
                      <Typography variant="body2">
                        {selectedTransaction.description}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    User Information
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ mr: 2 }}>
                      {selectedTransaction.user.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1">
                        {selectedTransaction.user.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedTransaction.user.email}
                      </Typography>
                      <Chip
                        label={selectedTransaction.user.role}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                </Grid>

                {selectedTransaction.booking && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Related Booking
                    </Typography>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography variant="body2">
                        <strong>Vehicle:</strong> {selectedTransaction.booking.vehicleInfo}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Dates:</strong> {selectedTransaction.booking.dates}
                      </Typography>
                    </Paper>
                  </Grid>
                )}

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Payment Method
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getPaymentMethodIcon(selectedTransaction.paymentMethod.type)}
                    <Box sx={{ ml: 1 }}>
                      <Typography variant="body2">
                        {selectedTransaction.paymentMethod.type.replace('_', ' ')}
                      </Typography>
                      {selectedTransaction.paymentMethod.brand && (
                        <Typography variant="caption" color="text.secondary">
                          {selectedTransaction.paymentMethod.brand} •••• {selectedTransaction.paymentMethod.last4}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Fee Breakdown
                  </Typography>
                  <Stack spacing={1}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Platform Fee:</Typography>
                      <Typography variant="body2">${selectedTransaction.fees.platformFee.toFixed(2)}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Processing Fee:</Typography>
                      <Typography variant="body2">${selectedTransaction.fees.processingFee.toFixed(2)}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Taxes:</Typography>
                      <Typography variant="body2">${selectedTransaction.fees.taxes.toFixed(2)}</Typography>
                    </Box>
                    <Divider />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" fontWeight="bold">Total Fees:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        ${(selectedTransaction.fees.platformFee + selectedTransaction.fees.processingFee + selectedTransaction.fees.taxes).toFixed(2)}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Timeline
                  </Typography>
                  <Stack spacing={1}>
                    <Box>
                      <Typography variant="caption">Created:</Typography>
                      <Typography variant="body2">
                        {new Date(selectedTransaction.createdAt).toLocaleString()}
                      </Typography>
                    </Box>
                    {selectedTransaction.completedAt && (
                      <Box>
                        <Typography variant="caption">Completed:</Typography>
                        <Typography variant="body2">
                          {new Date(selectedTransaction.completedAt).toLocaleString()}
                        </Typography>
                      </Box>
                    )}
                    {selectedTransaction.failureReason && (
                      <Alert severity="error">
                        <Typography variant="body2">
                          <strong>Failure Reason:</strong> {selectedTransaction.failureReason}
                        </Typography>
                      </Alert>
                    )}
                  </Stack>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Close
              </Button>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
              >
                Download Receipt
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default TransactionHistory;
