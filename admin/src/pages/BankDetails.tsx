import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Avatar,
  Stack,
  Divider
} from '@mui/material';
import {
  Visibility as ViewIcon,
  AccountBalance as BankIcon,
  CheckCircle as VerifiedIcon,
  Cancel as UnverifiedIcon,
  Warning as WarningIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { AdminApiService } from '../services/AdminApiService';

interface BankAccount {
  id: string;
  provider: {
    id: string;
    name: string;
    email: string;
    businessName?: string;
  };
  accountType: 'CHECKING' | 'SAVINGS' | 'BUSINESS';
  bankName: string;
  accountNumber: string;
  routingNumber: string;
  accountHolderName: string;
  country: string;
  currency: string;
  isVerified: boolean;
  isPrimary: boolean;
  verificationStatus: 'PENDING' | 'VERIFIED' | 'FAILED' | 'REQUIRES_ACTION';
  verificationMethod: 'MICRO_DEPOSITS' | 'INSTANT' | 'MANUAL';
  verificationDate?: string;
  failureReason?: string;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    plaidAccountId?: string;
    stripeAccountId?: string;
    riskScore?: number;
  };
}

interface BankStats {
  totalAccounts: number;
  verifiedAccounts: number;
  pendingVerification: number;
  failedVerification: number;
  primaryAccounts: number;
  verificationRate: number;
}

const BankDetails: React.FC = () => {
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [stats, setStats] = useState<BankStats>({
    totalAccounts: 0,
    verifiedAccounts: 0,
    pendingVerification: 0,
    failedVerification: 0,
    primaryAccounts: 0,
    verificationRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState<BankAccount | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchBankAccounts();
    fetchStats();
  }, [filterStatus, searchTerm]);

  const fetchBankAccounts = async () => {
    try {
      setLoading(true);
      // TODO: Connect to Supabase
      // const { data, error } = await supabase
      //   .from('bank_accounts')
      //   .select(`
      //     *,
      //     provider:providers(*)
      //   `)
      //   .order('created_at', { ascending: false });
      
      // Get live bank account data from Supabase
      // Since we don't have a dedicated bank_accounts table, we'll show empty for now
      // In a real implementation, you would query your bank_accounts table
      const liveAccounts: BankAccount[] = [];
      setBankAccounts(liveAccounts);
    } catch (error) {
      console.error('Error fetching bank accounts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Since we don't have a bank_accounts table yet, show empty stats
      // In a real implementation, you would query your bank_accounts table
      setStats({
        totalAccounts: 0,
        verifiedAccounts: 0,
        pendingVerification: 0,
        failedVerification: 0,
        primaryAccounts: 0,
        verificationRate: 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'VERIFIED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'error';
      case 'REQUIRES_ACTION': return 'info';
      default: return 'default';
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'VERIFIED': return <VerifiedIcon />;
      case 'PENDING': return <WarningIcon />;
      case 'FAILED': return <UnverifiedIcon />;
      case 'REQUIRES_ACTION': return <WarningIcon />;
      default: return <WarningIcon />;
    }
  };

  const getRiskColor = (score: number) => {
    if (score <= 20) return 'success.main';
    if (score <= 40) return 'warning.main';
    return 'error.main';
  };

  const handleViewAccount = (account: BankAccount) => {
    setSelectedAccount(account);
    setDialogOpen(true);
  };

  const handleVerifyAccount = async (accountId: string) => {
    try {
      // TODO: Connect to Supabase
      // const { error } = await supabase
      //   .from('bank_accounts')
      //   .update({ 
      //     verification_status: 'VERIFIED',
      //     is_verified: true,
      //     verification_date: new Date().toISOString()
      //   })
      //   .eq('id', accountId);
      
      setBankAccounts(prev => prev.map(account => 
        account.id === accountId 
          ? { 
              ...account, 
              verificationStatus: 'VERIFIED' as any,
              isVerified: true,
              verificationDate: new Date().toISOString()
            }
          : account
      ));
      
      alert('Bank account verified successfully!');
      fetchStats();
    } catch (error) {
      console.error('Error verifying account:', error);
      alert('Failed to verify account');
    }
  };

  const handleDeleteAccount = async (accountId: string) => {
    if (!confirm('Are you sure you want to delete this bank account?')) return;
    
    try {
      // TODO: Connect to Supabase
      // const { error } = await supabase
      //   .from('bank_accounts')
      //   .delete()
      //   .eq('id', accountId);
      
      setBankAccounts(prev => prev.filter(account => account.id !== accountId));
      alert('Bank account deleted successfully!');
      fetchStats();
    } catch (error) {
      console.error('Error deleting account:', error);
      alert('Failed to delete account');
    }
  };

  const filteredAccounts = bankAccounts.filter(account => {
    const matchesStatus = filterStatus === 'ALL' || account.verificationStatus === filterStatus;
    const matchesSearch = searchTerm === '' || 
      account.provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.provider.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.bankName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.accountHolderName.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          🏦 Bank Details Management
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => { fetchBankAccounts(); fetchStats(); }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            color="primary"
          >
            Add Bank Account
          </Button>
        </Stack>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Accounts
              </Typography>
              <Typography variant="h4">
                {stats.totalAccounts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Verified
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.verifiedAccounts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pendingVerification}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Failed
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.failedVerification}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Primary Accounts
              </Typography>
              <Typography variant="h4">
                {stats.primaryAccounts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Verification Rate
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.verificationRate}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search bank accounts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Verification Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Verification Status"
              >
                <MenuItem value="ALL">All Statuses</MenuItem>
                <MenuItem value="VERIFIED">Verified</MenuItem>
                <MenuItem value="PENDING">Pending</MenuItem>
                <MenuItem value="FAILED">Failed</MenuItem>
                <MenuItem value="REQUIRES_ACTION">Requires Action</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Bank Accounts Table */}
      <Paper>
        {loading ? (
          <LinearProgress />
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Provider</TableCell>
                  <TableCell>Bank Details</TableCell>
                  <TableCell>Account Type</TableCell>
                  <TableCell>Verification</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Last Used</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAccounts.map((account) => (
                  <TableRow key={account.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
                          {account.provider.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {account.provider.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {account.provider.businessName || account.provider.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BankIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {account.bankName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {account.accountNumber} • {account.accountHolderName}
                          </Typography>
                          {account.isPrimary && (
                            <Chip label="Primary" size="small" color="primary" sx={{ ml: 1 }} />
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={account.accountType}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Chip
                          icon={getVerificationIcon(account.verificationStatus)}
                          label={account.verificationStatus}
                          size="small"
                          color={getVerificationStatusColor(account.verificationStatus) as any}
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {account.verificationMethod}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        fontWeight="bold"
                        color={getRiskColor(account.metadata.riskScore || 0)}
                      >
                        {account.metadata.riskScore || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Risk Score
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {account.lastUsed ? 
                          new Date(account.lastUsed).toLocaleDateString() : 
                          'Never'
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewAccount(account)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        {account.verificationStatus === 'PENDING' && (
                          <Tooltip title="Verify Account">
                            <IconButton
                              size="small"
                              onClick={() => handleVerifyAccount(account.id)}
                            >
                              <VerifiedIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Delete Account">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteAccount(account.id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Account Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedAccount && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  Bank Account Details
                </Typography>
                <Chip
                  label={selectedAccount.verificationStatus}
                  color={getVerificationStatusColor(selectedAccount.verificationStatus) as any}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Provider Information
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ mr: 2 }}>
                      {selectedAccount.provider.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body1">
                        {selectedAccount.provider.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedAccount.provider.email}
                      </Typography>
                      {selectedAccount.provider.businessName && (
                        <Typography variant="caption" color="text.secondary">
                          {selectedAccount.provider.businessName}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Account Information
                  </Typography>
                  <Stack spacing={1}>
                    <Box>
                      <Typography variant="caption">Bank Name:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {selectedAccount.bankName}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Account Type:</Typography>
                      <Typography variant="body2">
                        {selectedAccount.accountType}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Currency:</Typography>
                      <Typography variant="body2">
                        {selectedAccount.currency}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Bank Account Details
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="caption">Account Number:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {selectedAccount.accountNumber}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="caption">Routing Number:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {selectedAccount.routingNumber}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="caption">Account Holder Name:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {selectedAccount.accountHolderName}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Verification Details
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="caption">Status:</Typography>
                      <Chip
                        icon={getVerificationIcon(selectedAccount.verificationStatus)}
                        label={selectedAccount.verificationStatus}
                        size="small"
                        color={getVerificationStatusColor(selectedAccount.verificationStatus) as any}
                        sx={{ ml: 1 }}
                      />
                    </Box>
                    <Box>
                      <Typography variant="caption">Method:</Typography>
                      <Typography variant="body2">
                        {selectedAccount.verificationMethod}
                      </Typography>
                    </Box>
                    {selectedAccount.verificationDate && (
                      <Box>
                        <Typography variant="caption">Verified Date:</Typography>
                        <Typography variant="body2">
                          {new Date(selectedAccount.verificationDate).toLocaleString()}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Security & Risk
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="caption">Risk Score:</Typography>
                      <Typography 
                        variant="h6" 
                        color={getRiskColor(selectedAccount.metadata.riskScore || 0)}
                      >
                        {selectedAccount.metadata.riskScore || 0}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Last Used:</Typography>
                      <Typography variant="body2">
                        {selectedAccount.lastUsed ? 
                          new Date(selectedAccount.lastUsed).toLocaleString() : 
                          'Never'
                        }
                      </Typography>
                    </Box>
                    {selectedAccount.isPrimary && (
                      <Chip
                        label="Primary Account"
                        color="primary"
                        icon={<SecurityIcon />}
                      />
                    )}
                  </Stack>
                </Grid>

                {selectedAccount.failureReason && (
                  <Grid item xs={12}>
                    <Alert severity="error">
                      <Typography variant="body2">
                        <strong>Verification Failed:</strong> {selectedAccount.failureReason}
                      </Typography>
                    </Alert>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Account Timeline
                  </Typography>
                  <Stack spacing={1}>
                    <Box>
                      <Typography variant="caption">Created:</Typography>
                      <Typography variant="body2">
                        {new Date(selectedAccount.createdAt).toLocaleString()}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption">Last Updated:</Typography>
                      <Typography variant="body2">
                        {new Date(selectedAccount.updatedAt).toLocaleString()}
                      </Typography>
                    </Box>
                  </Stack>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>
                Close
              </Button>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
              >
                Edit Account
              </Button>
              {selectedAccount.verificationStatus === 'PENDING' && (
                <Button
                  variant="contained"
                  startIcon={<VerifiedIcon />}
                  onClick={() => handleVerifyAccount(selectedAccount.id)}
                >
                  Verify Account
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default BankDetails;
