import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  CheckCircle as ActiveIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import SessionService, { SessionState } from '../services/SessionService';
import { useAuth } from '../contexts/AuthContext';

const SessionDemo: React.FC = () => {
  const { extendSession } = useAuth();
  const [sessionState, setSessionState] = useState<SessionState>({
    isActive: false,
    lastActivity: 0,
    sessionStart: 0,
    showWarning: false,
    timeRemaining: 0
  });

  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    const sessionService = SessionService.getInstance();

    // Listen for session state changes
    const handleSessionChange = (state: SessionState) => {
      setSessionState(state);
    };

    sessionService.addListener(handleSessionChange);

    // Initial state
    setSessionState(sessionService.getState());

    // Update current time every second
    const timer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => {
      sessionService.removeListener(handleSessionChange);
      clearInterval(timer);
    };
  }, []);

  const handleExtendSession = () => {
    extendSession();
  };

  const formatTime = (milliseconds: number): string => {
    return SessionService.formatTimeRemaining(milliseconds);
  };

  const getSessionDuration = (): string => {
    if (!sessionState.isActive) return '0m';
    return SessionService.formatSessionDuration(currentTime - sessionState.sessionStart);
  };

  const getTimeUntilLogout = (): string => {
    if (!sessionState.isActive) return '0:00';
    const remaining = SessionService.getInstance().getTimeRemaining();
    return formatTime(remaining);
  };

  const getProgressValue = (): number => {
    if (!sessionState.isActive) return 0;
    const remaining = SessionService.getInstance().getTimeRemaining();
    const total = 5 * 60 * 1000; // 5 minutes
    return ((total - remaining) / total) * 100;
  };

  const getStatusColor = (): 'success' | 'warning' | 'error' => {
    if (!sessionState.isActive) return 'error';
    
    const remaining = SessionService.getInstance().getTimeRemaining();
    if (remaining < 60000) return 'error'; // Less than 1 minute
    if (remaining < 120000) return 'warning'; // Less than 2 minutes
    return 'success';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Session Management Demo
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        This page demonstrates the session management system with 30-minute minimum sessions and 5-minute inactivity timeout.
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Session Rules:</strong>
          <br />
          • Sessions last a minimum of 30 minutes
          <br />
          • After 30 minutes, sessions will auto-logout after 5 minutes of inactivity
          <br />
          • Warning appears 1 minute before logout
          <br />
          • Any activity (mouse, keyboard, clicks) resets the inactivity timer
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Session Status Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ActiveIcon color={sessionState.isActive ? 'success' : 'error'} sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Session Status
                </Typography>
              </Box>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Session Active"
                    secondary={sessionState.isActive ? 'Yes' : 'No'}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <TimeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Session Duration"
                    secondary={getSessionDuration()}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <WarningIcon color={getStatusColor()} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Time Until Auto-Logout"
                    secondary={getTimeUntilLogout()}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <InfoIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Can Terminate Session"
                    secondary={SessionService.getInstance().canTerminateSession() ? 'Yes (30+ min passed)' : 'No (< 30 min)'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Session Controls Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Session Controls
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Inactivity Progress
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={getProgressValue()}
                  color={getStatusColor()}
                  sx={{ height: 8, borderRadius: 4, mb: 1 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {getTimeUntilLogout()} remaining
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={handleExtendSession}
                  disabled={!sessionState.isActive}
                  fullWidth
                >
                  Extend Session
                </Button>
                
                <Chip
                  icon={<TimeIcon />}
                  label={`Warning: ${sessionState.showWarning ? 'Active' : 'Inactive'}`}
                  color={sessionState.showWarning ? 'warning' : 'default'}
                  variant={sessionState.showWarning ? 'filled' : 'outlined'}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Instructions Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                How to Test Session Management
              </Typography>
              
              <Typography variant="body2" paragraph>
                1. <strong>Login:</strong> Use any email (e.g., <EMAIL>) with any password
              </Typography>
              
              <Typography variant="body2" paragraph>
                2. <strong>Session Timer:</strong> Look at the top-right corner for the session status chip showing time remaining
              </Typography>
              
              <Typography variant="body2" paragraph>
                3. <strong>Activity Detection:</strong> Move your mouse, click, or type to reset the inactivity timer
              </Typography>
              
              <Typography variant="body2" paragraph>
                4. <strong>Warning Dialog:</strong> After 30 minutes, if you're inactive for 4 minutes, a warning dialog will appear
              </Typography>
              
              <Typography variant="body2" paragraph>
                5. <strong>Auto-Logout:</strong> If you don't extend the session, you'll be logged out after 5 minutes of inactivity
              </Typography>
              
              <Typography variant="body2" paragraph>
                6. <strong>Extend Session:</strong> Click "Stay Logged In" in the warning dialog or use the "Extend Session" button above
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="text.secondary">
                <strong>Note:</strong> For testing purposes, you can modify the session timeouts in SessionService.ts:
                <br />
                • minSessionDuration: Currently 30 minutes
                <br />
                • inactivityTimeout: Currently 5 minutes
                <br />
                • warningTime: Currently 1 minute before logout
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SessionDemo;
