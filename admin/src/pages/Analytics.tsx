import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  DirectionsCar as CarIcon,
  AttachMoney as MoneyIcon,
  Assessment as ReportIcon,
  Download as DownloadIcon,
  DateRange as DateIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { format, subDays, subMonths } from 'date-fns';
import DirectSupabaseService from '../services/DirectSupabaseService';
import StripeService from '../services/StripeService';
import { SupabaseDashboardService } from '../services/SupabaseDashboardService';
const Analytics: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('30d');

  // Analytics data
  const [userGrowth, setUserGrowth] = useState<any[]>([]);
  const [revenueData, setRevenueData] = useState<any[]>([]);
  const [bookingTrends, setBookingTrends] = useState<any[]>([]);
  const [vehicleStats, setVehicleStats] = useState<any[]>([]);
  const [providerPerformance, setProviderPerformance] = useState<any[]>([]);
  const [geographicData, setGeographicData] = useState<any[]>([]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get live analytics data from Supabase
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;

      // User Growth Data - Get real user stats
      try {
        const userStats = await DirectSupabaseService.getUserStats();
        const userGrowthData = Array.from({ length: days }, (_, i) => {
          const date = format(subDays(new Date(), days - i - 1), 'MMM dd');
          return {
            date,
            newUsers: Math.floor((userStats.newUsersThisMonth || 0) / days),
            totalUsers: userStats.totalUsers || 0,
            activeUsers: userStats.activeUsers || 0
          };
        });
        setUserGrowth(userGrowthData);
      } catch (error) {
        console.error('Error fetching user growth data:', error);
        setUserGrowth([]);
      }

      // Revenue Data
      const revenueAnalytics = await StripeService.getRevenueAnalytics(
        timeRange === '7d' ? 'week' : timeRange === '30d' ? 'month' : 'year'
      );
      setRevenueData(revenueAnalytics);

      // Booking Trends - Get real booking data
      try {
        const dashboardStats = await SupabaseDashboardService.getDashboardStats();
        const bookingData = Array.from({ length: days }, (_, i) => {
          const date = format(subDays(new Date(), days - i - 1), 'MMM dd');
          return {
            date,
            bookings: Math.floor((dashboardStats.bookings?.total || 0) / days),
            completedBookings: Math.floor((dashboardStats.bookings?.completed || 0) / days),
            cancelledBookings: Math.floor((dashboardStats.bookings?.cancelled || 0) / days)
          };
        });
        setBookingTrends(bookingData);
      } catch (error) {
        console.error('Error fetching booking trends:', error);
        setBookingTrends([]);
      }

      // Vehicle Category Stats - Get real vehicle data
      try {
        const vehicleResponse = await DirectSupabaseService.getVehicles(1, 100);
        const vehicles = vehicleResponse.data || [];

        // Group vehicles by category
        const categoryCount: { [key: string]: number } = {};
        vehicles.forEach(vehicle => {
          const category = vehicle.category || 'Other';
          categoryCount[category] = (categoryCount[category] || 0) + 1;
        });

        const colors = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
        const vehicleCategories = Object.entries(categoryCount).map(([name, value], index) => ({
          name,
          value,
          color: colors[index % colors.length]
        }));

        setVehicleStats(vehicleCategories);
      } catch (error) {
        console.error('Error fetching vehicle stats:', error);
        setVehicleStats([]);
      }

      // Provider Performance - Get real provider data
      try {
        const providers = await DirectSupabaseService.getProviders();
        const providerData = providers.map(provider => ({
          name: provider.name || 'Unknown Provider',
          revenue: provider.totalRevenue || 0,
          bookings: provider.totalBookings || 0,
          rating: provider.rating || 0
        }));
        setProviderPerformance(providerData);
      } catch (error) {
        console.error('Error fetching provider performance:', error);
        setProviderPerformance([]);
      }

      // Geographic Data - Get real location data from bookings
      try {
        // Since we don't have location data in our current schema, show empty for now
        // In a real implementation, you would query booking locations
        setGeographicData([]);
      } catch (error) {
        console.error('Error fetching geographic data:', error);
        setGeographicData([]);
      }

    } catch (err: any) {
      console.error('Error fetching analytics data:', err);
      setError(err.message || 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const exportReport = () => {
    const reportData = {
      userGrowth,
      revenueData,
      bookingTrends,
      vehicleStats,
      providerPerformance,
      geographicData,
      generatedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    const exportFileDefaultName = `analytics-report-${format(new Date(), 'yyyy-MM-dd')}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        System Analytics
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Users
                  </Typography>
                  <Typography variant="h4">
                    {data.totalUsers.toLocaleString()}
                  </Typography>
                  {data.userGrowth > 0 && (
                    <Typography variant="body2" color="success.main">
                      +{data.userGrowth} this month
                    </Typography>
                  )}
                </Box>
                <People color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Vehicles
                  </Typography>
                  <Typography variant="h4">
                    {data.totalVehicles.toLocaleString()}
                  </Typography>
                  {data.vehicleGrowth > 0 && (
                    <Typography variant="body2" color="success.main">
                      +{data.vehicleGrowth} this month
                    </Typography>
                  )}
                </Box>
                <DirectionsCar color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Bookings
                  </Typography>
                  <Typography variant="h4">
                    {data.totalBookings.toLocaleString()}
                  </Typography>
                  {data.bookingGrowth > 0 && (
                    <Typography variant="body2" color="success.main">
                      +{data.bookingGrowth} this month
                    </Typography>
                  )}
                </Box>
                <BookOnline color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Revenue
                  </Typography>
                  <Typography variant="h4">
                    ${data.totalRevenue.toLocaleString()}
                  </Typography>
                  {data.revenueGrowth > 0 && (
                    <Typography variant="body2" color="success.main">
                      +{data.revenueGrowth}% this month
                    </Typography>
                  )}
                </Box>
                <AttachMoney color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              System Analytics Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              This page will contain comprehensive system analytics including user behavior, 
              platform performance metrics, revenue analytics, and business intelligence reports.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Analytics;
