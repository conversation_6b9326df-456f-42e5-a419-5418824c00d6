import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  IconButton,
  Chip,
  InputAdornment,
} from '@mui/material'
import {
  Delete as DeleteIcon,
  PhotoCamera,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import { toast } from 'react-toastify'
import { 
  VehicleFormSchema, 
  VehicleFormData, 
  getDefaultVehicleFormValues,
  vehicleCategories,
  validateVehicleForm,
} from '../models/VehicleForm'
import { vehicleService } from '../services/VehicleService'
import { useUser } from '../context/UserContext'

const CreateVehicle: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useUser()
  const [loading, setLoading] = useState(false)
  const [imageFiles, setImageFiles] = useState<File[]>([])
  const [imagePreviews, setImagePreviews] = useState<string[]>([])

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<VehicleFormData>({
    resolver: zodResolver(VehicleFormSchema),
    defaultValues: getDefaultVehicleFormValues(),
  })



  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length + imageFiles.length > 10) {
      toast.error('Maximum 10 images allowed')
      return
    }

    const newFiles = [...imageFiles, ...files]
    setImageFiles(newFiles)

    // Create previews
    const newPreviews = [...imagePreviews]
    files.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        newPreviews.push(e.target?.result as string)
        setImagePreviews([...newPreviews])
      }
      reader.readAsDataURL(file)
    })

    // Update form
    const imageUrls = newFiles.map((_, index) => `preview_${index}`)
    setValue('images', imageUrls)
  }

  const removeImage = (index: number) => {
    const newFiles = imageFiles.filter((_, i) => i !== index)
    const newPreviews = imagePreviews.filter((_, i) => i !== index)
    setImageFiles(newFiles)
    setImagePreviews(newPreviews)
    
    const imageUrls = newFiles.map((_, i) => `preview_${i}`)
    setValue('images', imageUrls)
  }


  const onSubmit = async (data: VehicleFormData) => {
    setLoading(true)
    try {
      // Validate business rules
      const validationErrors = validateVehicleForm(data)
      if (validationErrors.length > 0) {
        validationErrors.forEach(error => toast.error(error))
        return
      }

      // For now, use the current user as provider
      // In production, this would be determined by authentication/authorization
      const providerId = user?.id || 'temp-provider-id'

      // Upload images first
      let imageUrls: string[] = []
      if (imageFiles.length > 0) {
        // In a real app, you'd create the vehicle first to get an ID, then upload images
        // For now, we'll use a temporary ID
        const tempVehicleId = `temp_${Date.now()}`
        imageUrls = await vehicleService.uploadVehicleImages(tempVehicleId, imageFiles)
      }

      // Create vehicle
      const vehicleData = {
        ...data,
        images: imageUrls,
      }

      await vehicleService.createVehicle(vehicleData, providerId)

      toast.success('Vehicle created successfully!')
      navigate('/vehicles')
    } catch (error: any) {
      console.error('Error creating vehicle:', error)
      toast.error(error.message || 'Failed to create vehicle')
    } finally {
      setLoading(false)
    }
  }


  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Add New Vehicle
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Add a new scooter or motorbike to your fleet
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={() => navigate('/vehicles')}
          >
            Cancel
          </Button>
          <LoadingButton
            variant="contained"
            startIcon={<SaveIcon />}
            loading={loading}
            onClick={handleSubmit(onSubmit)}
          >
            Save Vehicle
          </LoadingButton>
        </Box>
      </Box>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="make"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Make"
                          fullWidth
                          error={!!errors.make}
                          helperText={errors.make?.message}
                          placeholder="e.g., Honda, Yamaha, Suzuki"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="model"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Model"
                          fullWidth
                          error={!!errors.model}
                          helperText={errors.model?.message}
                          placeholder="e.g., PCX 160, NMAX 155"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Controller
                      name="year"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Year"
                          type="number"
                          fullWidth
                          error={!!errors.year}
                          helperText={errors.year?.message}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Controller
                      name="category"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth error={!!errors.category}>
                          <InputLabel>Category</InputLabel>
                          <Select {...field} label="Category">
                            {vehicleCategories.map((category) => (
                              <MenuItem key={category.value} value={category.value}>
                                {category.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Controller
                      name="engine_size"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Engine Size"
                          type="number"
                          fullWidth
                          error={!!errors.engine_size}
                          helperText={errors.engine_size?.message}
                          InputProps={{
                            endAdornment: <InputAdornment position="end">cc</InputAdornment>,
                          }}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="transmission"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Transmission</InputLabel>
                          <Select {...field} label="Transmission">
                            <MenuItem value="automatic">Automatic</MenuItem>
                            <MenuItem value="manual">Manual</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="fuel_type"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Fuel Type</InputLabel>
                          <Select {...field} label="Fuel Type">
                            <MenuItem value="petrol">⛽ Petrol</MenuItem>
                            <MenuItem value="electric">🔋 Electric</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Description"
                          multiline
                          rows={4}
                          fullWidth
                          error={!!errors.description}
                          helperText={errors.description?.message}
                          placeholder="Describe the vehicle's condition, special features, and what makes it great for riders..."
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Images */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Vehicle Images
                </Typography>
                <Box mb={2}>
                  <input
                    accept="image/*"
                    style={{ display: 'none' }}
                    id="image-upload"
                    multiple
                    type="file"
                    onChange={handleImageChange}
                  />
                  <label htmlFor="image-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<PhotoCamera />}
                    >
                      Upload Images
                    </Button>
                  </label>
                  <Typography variant="caption" display="block" mt={1}>
                    Upload up to 10 images. First image will be the main photo.
                  </Typography>
                </Box>
                
                {imagePreviews.length > 0 && (
                  <Grid container spacing={2}>
                    {imagePreviews.map((preview, index) => (
                      <Grid item xs={6} sm={4} md={3} key={index}>
                        <Paper sx={{ position: 'relative', p: 1 }}>
                          <img
                            src={preview}
                            alt={`Preview ${index + 1}`}
                            style={{
                              width: '100%',
                              height: 120,
                              objectFit: 'cover',
                              borderRadius: 4,
                            }}
                          />
                          <IconButton
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: 4,
                              right: 4,
                              backgroundColor: 'error.main',
                              color: 'white',
                              '&:hover': { backgroundColor: 'error.dark' },
                            }}
                            onClick={() => removeImage(index)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                          {index === 0 && (
                            <Chip
                              label="Main"
                              size="small"
                              color="primary"
                              sx={{ position: 'absolute', bottom: 8, left: 8 }}
                            />
                          )}
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                )}
                
                {errors.images && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {errors.images.message}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Continue with pricing, features, etc. - truncated for response length */}
          
        </Grid>
      </form>
    </Box>
  )
}

export default CreateVehicle