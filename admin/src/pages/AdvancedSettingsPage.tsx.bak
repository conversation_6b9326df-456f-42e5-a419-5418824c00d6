import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Switch, 
  FormControlLabel, 
  TextField, 
  Button, 
  Grid, 
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { 
  DiscountRule, 
  DiscountService 
} from '../services/DiscountService';
import { 
  PredictivePricingService, 
  PredictivePricingConfig 
} from '../services/PredictivePricingService';
import { supabase } from '../utils/supabaseClient';

const AdvancedSettingsPage: React.FC = () => {
  // Discount Rules State
  const [discountRules, setDiscountRules] = useState<DiscountRule[]>([]);
  const [newRule, setNewRule] = useState<Partial<DiscountRule>>({
    type: 'percentage',
    value: 0,
    priority: 1
  });

  // Predictive Pricing State
  const [pricingConfig, setPricingConfig] = useState<PredictivePricingConfig>({
    enabled: false
  });

  // Dispute Management State
  const [disputeSettings, setDisputeSettings] = useState({
    maxDisputeDays: 30,
    autoResolutionEnabled: false
  });

  // Load initial configurations
  useEffect(() => {
    loadDiscountRules();
    loadPricingConfig();
    loadDisputeSettings();
  }, []);

  // Discount Rules Management
  const loadDiscountRules = async () => {
    try {
      const { data, error } = await supabase
        .from('discount_rules')
        .select('*');
      
      if (error) throw error;
      setDiscountRules(data || []);
    } catch (error) {
      console.error('Error loading discount rules', error);
    }
  };

  const addDiscountRule = async () => {
    try {
      const { data, error } = await supabase
        .from('discount_rules')
        .insert(newRule)
        .select();
      
      if (error) throw error;
      
      setDiscountRules([...discountRules, data[0]]);
      setNewRule({
        type: 'percentage',
        value: 0,
        priority: 1
      });
    } catch (error) {
      console.error('Error adding discount rule', error);
    }
  };

  const removeDiscountRule = async (ruleId: string) => {
    try {
      const { error } = await supabase
        .from('discount_rules')
        .delete()
        .eq('id', ruleId);
      
      if (error) throw error;
      
      setDiscountRules(
        discountRules.filter(rule => rule.id !== ruleId)
      );
    } catch (error) {
      console.error('Error removing discount rule', error);
    }
  };

  // Predictive Pricing Configuration
  const loadPricingConfig = async () => {
    try {
      const { data, error } = await supabase
        .from('system_configs')
        .select('*')
        .eq('key', 'predictive_pricing')
        .single();
      
      if (error) throw error;
      
      setPricingConfig(JSON.parse(data.value));
    } catch (error) {
      console.error('Error loading pricing config', error);
    }
  };

  const updatePricingConfig = async () => {
    try {
      const { error } = await supabase
        .from('system_configs')
        .upsert({
          key: 'predictive_pricing',
          value: JSON.stringify(pricingConfig)
        });
      
      if (error) throw error;
      
      // Trigger model training if enabled
      if (pricingConfig.enabled) {
        await trainPricingModel();
      }
    } catch (error) {
      console.error('Error updating pricing config', error);
    }
  };

  const trainPricingModel = async () => {
    try {
      // Fetch historical booking data for training
      const { data: historicalData, error } = await supabase
        .from('bookings')
        .select('*')
        .gte('created_at', new Date(
          new Date().setFullYear(new Date().getFullYear() - 1)
        ).toISOString());
      
      if (error) throw error;
      
      const trainingResult = PredictivePricingService.trainModel(
        historicalData || []
      );
      
      // Update config with new model version
      setPricingConfig(prev => ({
        ...prev,
        model_version: trainingResult.model_version,
        last_trained: new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error training pricing model', error);
    }
  };

  // Dispute Settings Management
  const loadDisputeSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('system_configs')
        .select('*')
        .eq('key', 'dispute_settings')
        .single();
      
      if (error) throw error;
      
      setDisputeSettings(JSON.parse(data.value));
    } catch (error) {
      console.error('Error loading dispute settings', error);
    }
  };

  const updateDisputeSettings = async () => {
    try {
      const { error } = await supabase
        .from('system_configs')
        .upsert({
          key: 'dispute_settings',
          value: JSON.stringify(disputeSettings)
        });
      
      if (error) throw error;
    } catch (error) {
      console.error('Error updating dispute settings', error);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Advanced Platform Settings
      </Typography>

      {/* Discount Rules Section */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6">Discount Rules</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {discountRules.map(rule => (
                <TableRow key={rule.id}>
                  <TableCell>{rule.name}</TableCell>
                  <TableCell>{rule.type}</TableCell>
                  <TableCell>{rule.value}</TableCell>
                  <TableCell>{rule.priority}</TableCell>
                  <TableCell>
                    <Button 
                      color="error" 
                      onClick={() => removeDiscountRule(rule.id)}
                    >
                      Remove
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Add New Discount Rule Form */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={3}>
            <TextField
              label="Rule Name"
              value={newRule.name || ''}
              onChange={(e) => setNewRule(prev => ({
                ...prev, 
                name: e.target.value
              }))}
              fullWidth
            />
          </Grid>
          <Grid item xs={3}>
            <TextField
              select
              label="Type"
              value={newRule.type}
              onChange={(e) => setNewRule(prev => ({
                ...prev, 
                type: e.target.value as 'percentage' | 'fixed'
              }))}
              fullWidth
              SelectProps={{ native: true }}
            >
              <option value="percentage">Percentage</option>
              <option value="fixed">Fixed Amount</option>
            </TextField>
          </Grid>
          <Grid item xs={2}>
            <TextField
              label="Value"
              type="number"
              value={newRule.value}
              onChange={(e) => setNewRule(prev => ({
                ...prev, 
                value: Number(e.target.value)
              }))}
              fullWidth
            />
          </Grid>
          <Grid item xs={2}>
            <TextField
              label="Priority"
              type="number"
              value={newRule.priority}
              onChange={(e) => setNewRule(prev => ({
                ...prev, 
                priority: Number(e.target.value)
              }))}
              fullWidth
            />
          </Grid>
          <Grid item xs={2}>
            <Button 
              variant="contained" 
              onClick={addDiscountRule}
            >
              Add Rule
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Predictive Pricing Section */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6">Predictive Pricing</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={pricingConfig.enabled}
                  onChange={(e) => setPricingConfig(prev => ({
                    ...prev,
                    enabled: e.target.checked
                  }))}
                />
              }
              label="Enable Predictive Pricing"
            />
          </Grid>
          {pricingConfig.enabled && (
            <>
              <Grid item xs={6}>
                <TextField
                  label="Model Version"
                  value={pricingConfig.model_version || 'Not trained'}
                  disabled
                  fullWidth
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  label="Last Trained"
                  value={pricingConfig.last_trained || 'Never'}
                  disabled
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <Button 
                  variant="contained" 
                  onClick={updatePricingConfig}
                >
                  Save Pricing Configuration
                </Button>
                <Button 
                  variant="outlined" 
                  sx={{ ml: 2 }}
                  onClick={trainPricingModel}
                >
                  Train Model Now
                </Button>
              </Grid>
            </>
          )}
        </Grid>
      </Paper>

      {/* Dispute Management Section */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6">Dispute Management</Typography>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <TextField
              label="Max Days to File Dispute"
              type="number"
              value={disputeSettings.maxDisputeDays}
              onChange={(e) => setDisputeSettings(prev => ({
                ...prev,
                maxDisputeDays: Number(e.target.value)
              }))}
              fullWidth
            />
          </Grid>
          <Grid item xs={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={disputeSettings.autoResolutionEnabled}
                  onChange={(e) => setDisputeSettings(prev => ({
                    ...prev,
                    autoResolutionEnabled: e.target.checked
                  }))}
                />
              }
              label="Enable Auto-Resolution"
            />
          </Grid>
          <Grid item xs={12}>
            <Button 
              variant="contained" 
              onClick={updateDisputeSettings}
            >
              Save Dispute Settings
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AdvancedSettingsPage; 