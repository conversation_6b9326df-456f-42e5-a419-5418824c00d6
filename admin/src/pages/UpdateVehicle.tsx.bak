import React from 'react'
import { Box, Typography, Paper } from '@mui/material'
import { useParams } from 'react-router-dom'

const UpdateVehicle: React.FC = () => {
  const { id } = useParams<{ id: string }>()

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Edit Vehicle
      </Typography>
      <Paper sx={{ p: 3, mt: 2 }}>
        <Typography>
          🚧 Vehicle editing form coming soon...
        </Typography>
        <Typography variant="body2" color="text.secondary" mt={1}>
          Editing vehicle ID: {id}
        </Typography>
      </Paper>
    </Box>
  )
}

export default UpdateVehicle