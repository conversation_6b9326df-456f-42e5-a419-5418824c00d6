import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Avatar
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Add as AddIcon,
  DirectionsCar as CarIcon
} from '@mui/icons-material';
import DirectSupabaseService from '../services/DirectSupabaseService';
import { useNavigate } from 'react-router-dom';

interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  category: string;
  pricePerHour: number;
  status: string;
  location: string;
  providerId: string;
  provider?: {
    name: string;
    email: string;
  };
  images: Array<{
    id: string;
    url: string;
    isPrimary: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
}

const Vehicles: React.FC = () => {
  const navigate = useNavigate();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const fetchVehicles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await DirectSupabaseService.getVehicles(page + 1, rowsPerPage, {
        search
      });

      // Map the response to match the expected interface
      const mappedVehicles = response.data.map((vehicle: any) => ({
        id: vehicle.id,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year,
        category: vehicle.category,
        pricePerHour: vehicle.daily_rate / 24, // Convert daily rate to hourly
        status: vehicle.status || 'available',
        location: `${vehicle.location_city || 'Unknown'}, ${vehicle.location_country || 'Unknown'}`,
        providerId: vehicle.provider_id,
        provider: {
          name: vehicle.provider_name || 'Unknown Provider',
          email: '<EMAIL>'
        },
        images: vehicle.images || [],
        createdAt: vehicle.created_at,
        updatedAt: vehicle.updated_at
      }));

      setVehicles(mappedVehicles);
      setTotal(response.total);
    } catch (error: any) {
      console.error('Failed to fetch vehicles:', error);
      setError(error.message || 'Failed to fetch vehicles');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVehicles();
  }, [page, rowsPerPage, search]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  const handleEditVehicle = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setEditDialogOpen(true);
  };

  const handleDeleteVehicle = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setDeleteDialogOpen(true);
  };

  const handleUpdateVehicleStatus = async (status: string) => {
    if (!selectedVehicle) return;

    try {
      await DirectSupabaseService.updateVehicle(selectedVehicle.id, {
        status: status as any,
        active: status === 'available'
      });
      console.log('Vehicle status updated successfully');
      setEditDialogOpen(false);
      fetchVehicles();
    } catch (error: any) {
      console.error('Failed to update vehicle status:', error);
      setError(error.message || 'Failed to update vehicle status');
    }
  };

  const handleConfirmDelete = async () => {
    if (!selectedVehicle) return;

    try {
      await DirectSupabaseService.deleteVehicle(selectedVehicle.id);
      console.log('Vehicle deleted successfully');
      setDeleteDialogOpen(false);
      fetchVehicles();
    } catch (error: any) {
      console.error('Failed to delete vehicle:', error);
      setError(error.message || 'Failed to delete vehicle');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'success';
      case 'BOOKED':
        return 'warning';
      case 'MAINTENANCE':
        return 'error';
      case 'INACTIVE':
        return 'default';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'luxury':
        return 'error';
      case 'suv':
        return 'primary';
      case 'sedan':
        return 'info';
      case 'hatchback':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Vehicle Management
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mt: 2 }}>
        {/* Search and Actions */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <TextField
            placeholder="Search vehicles..."
            value={search}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ width: 300 }}
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/create-vehicle')}
          >
            Add Vehicle
          </Button>
        </Box>

        {/* Vehicles Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Vehicle</TableCell>
                    <TableCell>Details</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Price/Hour</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Provider</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {vehicles.map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          {vehicle.images.length > 0 ? (
                            <Avatar
                              src={vehicle.images.find(img => img.isPrimary)?.url || vehicle.images[0].url}
                              alt={`${vehicle.make} ${vehicle.model}`}
                              sx={{ width: 50, height: 50 }}
                            />
                          ) : (
                            <Avatar sx={{ width: 50, height: 50 }}>
                              <CarIcon />
                            </Avatar>
                          )}
                          <Box>
                            <Typography variant="subtitle2">
                              {vehicle.make} {vehicle.model}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {vehicle.year}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {vehicle.make} {vehicle.model} ({vehicle.year})
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={vehicle.category}
                          color={getCategoryColor(vehicle.category) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>${vehicle.pricePerHour}</TableCell>
                      <TableCell>
                        <Chip
                          label={vehicle.status}
                          color={getStatusColor(vehicle.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {vehicle.provider?.name || 'N/A'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {vehicle.provider?.email}
                        </Typography>
                      </TableCell>
                      <TableCell>{vehicle.location}</TableCell>
                      <TableCell>
                        <IconButton
                          onClick={() => handleEditVehicle(vehicle)}
                          size="small"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          onClick={() => handleDeleteVehicle(vehicle)}
                          size="small"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={total}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(event) => {
                setRowsPerPage(parseInt(event.target.value, 10));
                setPage(0);
              }}
            />
          </>
        )}
      </Paper>

      {/* Edit Vehicle Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)}>
        <DialogTitle>Edit Vehicle</DialogTitle>
        <DialogContent>
          {selectedVehicle && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Vehicle:</strong> {selectedVehicle.make} {selectedVehicle.model} ({selectedVehicle.year})
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Category:</strong> {selectedVehicle.category}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Price per Hour:</strong> ${selectedVehicle.pricePerHour}
              </Typography>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={selectedVehicle.status}
                  label="Status"
                  onChange={(e) => handleUpdateVehicleStatus(e.target.value)}
                >
                  <MenuItem value="AVAILABLE">Available</MenuItem>
                  <MenuItem value="BOOKED">Booked</MenuItem>
                  <MenuItem value="MAINTENANCE">Maintenance</MenuItem>
                  <MenuItem value="INACTIVE">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Vehicle Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Vehicle</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone. Are you sure you want to delete this vehicle?
          </Alert>
          {selectedVehicle && (
            <Typography>
              <strong>Vehicle:</strong> {selectedVehicle.make} {selectedVehicle.model} ({selectedVehicle.year})
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Vehicles;
