import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AuthService, { User, LoginCredentials } from '../services/AuthService';
import SessionService from '../services/SessionService';

// Auth State Interface
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' };

// Auth Context Interface
interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
  extendSession: () => void;
}

// Initial State
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Auth Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Create Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider Component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize authentication on mount
  useEffect(() => {
    const initializeAuth = async () => {
      dispatch({ type: 'AUTH_START' });

      try {
        const user = await AuthService.initialize();

        if (user && user.role === 'ADMIN') {
          dispatch({ type: 'AUTH_SUCCESS', payload: user });
        } else {
          // If user is not admin, clear auth and show error
          await AuthService.logout();
          dispatch({ 
            type: 'AUTH_ERROR', 
            payload: user ? 'Admin access required' : 'Authentication required' 
          });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);

        // For development/demo purposes, allow access without authentication
        // TODO: Remove this in production
        if (import.meta.env.DEV) {
          console.warn('🚧 Running in demo mode - authentication bypassed');
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: {
              id: 'demo-admin',
              email: '<EMAIL>',
              name: 'Demo Admin',
              role: 'ADMIN'
            }
          });
        } else {
          dispatch({
            type: 'AUTH_ERROR',
            payload: error instanceof Error ? error.message : 'Authentication failed'
          });
        }
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials): Promise<void> => {
    console.log('🔐 AuthContext: Starting login process');
    dispatch({ type: 'AUTH_START' });

    try {
      console.log('🔐 AuthContext: Calling AuthService.login');
      const response = await AuthService.login(credentials);

      console.log('🔐 AuthContext: Login successful for user:', response.user);

      console.log('🔐 AuthContext: Dispatching AUTH_SUCCESS');
      dispatch({ type: 'AUTH_SUCCESS', payload: response.user });

      console.log('🔐 AuthContext: Starting session management');
      SessionService.getInstance().startSession();

      console.log('🔐 AuthContext: Login process completed successfully');
    } catch (error) {
      console.error('🔐 AuthContext: Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // End session management
      SessionService.getInstance().endSession();
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  // Clear error function
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Refresh user function
  const refreshUser = async (): Promise<void> => {
    try {
      const user = await AuthService.getCurrentUser();

      if (user && user.role === 'ADMIN') {
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } else {
        await logout();
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      await logout();
    }
  };

  // Extend session function
  const extendSession = (): void => {
    SessionService.getInstance().extendSession();
  };

  // Setup session expiry listener
  useEffect(() => {
    const handleSessionExpired = () => {
      console.log('Session expired, logging out...');
      logout();
    };

    window.addEventListener('session-expired', handleSessionExpired);

    return () => {
      window.removeEventListener('session-expired', handleSessionExpired);
    };
  }, []);

  // Start session if user is already authenticated
  useEffect(() => {
    if (state.isAuthenticated && state.user) {
      SessionService.getInstance().startSession();
    }
  }, [state.isAuthenticated, state.user]);

  // Context value
  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    clearError,
    refreshUser,
    extendSession,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

// Protected Route Component
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback = null 
}) => {
  const { state } = useAuth();

  if (state.isLoading) {
    return <>{fallback}</>;
  }

  if (!state.isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default AuthContext;
