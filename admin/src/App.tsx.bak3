import { FC } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Box } from '@mui/material'
import { useUser } from './context/UserContext'
import Layout from './components/Layout'
import SignIn from './pages/SignIn'
import Dashboard from './pages/Dashboard'
import AdminDashboard from './pages/AdminDashboard'
import ProviderDashboard from './pages/ProviderDashboard'
import Vehicles from './pages/Vehicles'
import CreateVehicle from './pages/CreateVehicle'
import UpdateVehicle from './pages/UpdateVehicle'
import Bookings from './pages/Bookings'
import Providers from './pages/Providers'
import Users from './pages/Users'
import LoadingScreen from './components/LoadingScreen'

// New admin pages
import ProviderPayouts from './pages/ProviderPayouts'
import BankDetails from './pages/BankDetails'
import UserVerification from './pages/UserVerification'
import SupportTickets from './pages/SupportTickets'
import Complaints from './pages/Complaints'
import RefundsDisputes from './pages/RefundsDisputes'
import CashflowDashboard from './pages/CashflowDashboard'
import TransactionHistory from './pages/TransactionHistory'
import FinancialReports from './pages/FinancialReports'
import SystemAnalytics from './pages/SystemAnalytics'
import PlatformHealth from './pages/PlatformHealth'
import SystemSettings from './pages/SystemSettings'

// Protected Route component
const ProtectedRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useUser()
  
  if (loading) {
    return <LoadingScreen />
  }
  
  if (!user) {
    return <Navigate to="/signin" replace />
  }
  
  return <>{children}</>
}

// Admin Route component (only for admin users)
const AdminRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAdmin, isProvider } = useUser()
  
  // Allow both admin and providers to access admin panel
  if (!isAdmin && !isProvider) {
    return <Navigate to="/dashboard" replace />
  }
  
  return <>{children}</>
}

// Provider Route component (only for provider users)
const ProviderRoute: FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProvider } = useUser()
  
  if (!isProvider) {
    return <Navigate to="/dashboard" replace />
  }
  
  return <>{children}</>
}

const App: FC = () => {
  const { user, loading } = useUser()

  if (loading) {
    return <LoadingScreen />
  }

  return (
    <Router>
      <Box sx={{ minHeight: '100vh', backgroundColor: 'background.default' }}>
        <Routes>
          {/* Public routes */}
          <Route path="/signin" element={
            user ? <Navigate to="/dashboard" replace /> : <SignIn />
          } />
          
          {/* Protected routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            
            {/* Admin Dashboard */}
            <Route path="admin-dashboard" element={
              <AdminRoute>
                <AdminDashboard />
              </AdminRoute>
            } />
            
            {/* Provider Dashboard */}
            <Route path="provider-dashboard" element={
              <ProviderRoute>
                <ProviderDashboard />
              </ProviderRoute>
            } />
            
            {/* Vehicle management */}
            <Route path="vehicles" element={<Vehicles />} />
            <Route path="vehicles/create" element={<CreateVehicle />} />
            <Route path="vehicles/:id/update" element={<UpdateVehicle />} />
            
            {/* Booking management */}
            <Route path="bookings" element={<Bookings />} />
            
            {/* Admin only routes - Priority 1: Provider Management */}
            <Route path="providers" element={
              <AdminRoute>
                <Providers />
              </AdminRoute>
            } />
            <Route path="provider-payouts" element={
              <AdminRoute>
                <ProviderPayouts />
              </AdminRoute>
            } />
            <Route path="bank-details" element={
              <AdminRoute>
                <BankDetails />
              </AdminRoute>
            } />
            
            {/* Priority 2: User Management */}
            <Route path="users" element={
              <AdminRoute>
                <Users />
              </AdminRoute>
            } />
            <Route path="user-verification" element={
              <AdminRoute>
                <UserVerification />
              </AdminRoute>
            } />
            
            {/* Priority 3: Support & Complaints */}
            <Route path="support-tickets" element={
              <AdminRoute>
                <SupportTickets />
              </AdminRoute>
            } />
            <Route path="complaints" element={
              <AdminRoute>
                <Complaints />
              </AdminRoute>
            } />
            <Route path="refunds-disputes" element={
              <AdminRoute>
                <RefundsDisputes />
              </AdminRoute>
            } />
            
            {/* Priority 4: Financial Oversight */}
            <Route path="cashflow" element={
              <AdminRoute>
                <CashflowDashboard />
              </AdminRoute>
            } />
            <Route path="transactions" element={
              <AdminRoute>
                <TransactionHistory />
              </AdminRoute>
            } />
            <Route path="financial-reports" element={
              <AdminRoute>
                <FinancialReports />
              </AdminRoute>
            } />
            
            {/* Priority 5: System Monitoring */}
            <Route path="system-analytics" element={
              <AdminRoute>
                <SystemAnalytics />
              </AdminRoute>
            } />
            <Route path="platform-health" element={
              <AdminRoute>
                <PlatformHealth />
              </AdminRoute>
            } />
            <Route path="system-settings" element={
              <AdminRoute>
                <SystemSettings />
              </AdminRoute>
            } />
          </Route>
          
          {/* Catch all */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Box>
    </Router>
  )
}

export default App