// API config file
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Domain configuration
export const FRONTEND_URL = import.meta.env.VITE_FRONTEND_URL || 'http://localhost:5173';
export const ADMIN_URL = import.meta.env.VITE_ADMIN_URL || 'http://localhost:3002';
export const APP_URL = import.meta.env.VITE_APP_URL || 'http://localhost:5173';

// Production domains
export const PRODUCTION_DOMAINS = {
  frontend: 'https://rentahub.info',
  admin: 'https://admin.rentahub.info',
  api: 'https://api.rentahub.info'
};

// Environment detection
export const isProduction = import.meta.env.NODE_ENV === 'production';
export const isDevelopment = import.meta.env.NODE_ENV === 'development';
