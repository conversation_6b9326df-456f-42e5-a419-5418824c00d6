export interface SessionConfig {
  minSessionDuration: number; // 30 minutes in milliseconds
  inactivityTimeout: number;  // 5 minutes in milliseconds
  warningTime: number;        // 1 minute before logout warning
}

export interface SessionState {
  isActive: boolean;
  lastActivity: number;
  sessionStart: number;
  showWarning: boolean;
  timeRemaining: number;
}

export class SessionService {
  private static instance: SessionService;
  private config: SessionConfig;
  private state: SessionState;
  private activityTimer: NodeJS.Timeout | null = null;
  private warningTimer: NodeJS.Timeout | null = null;
  private logoutTimer: NodeJS.Timeout | null = null;
  private listeners: Set<(state: SessionState) => void> = new Set();

  private constructor() {
    this.config = {
      minSessionDuration: 30 * 60 * 1000, // 30 minutes
      inactivityTimeout: 5 * 60 * 1000,   // 5 minutes
      warningTime: 1 * 60 * 1000,         // 1 minute warning
    };

    this.state = {
      isActive: false,
      lastActivity: 0,
      sessionStart: 0,
      showWarning: false,
      timeRemaining: 0,
    };

    this.setupActivityListeners();
  }

  static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService();
    }
    return SessionService.instance;
  }

  // Start a new session
  startSession(): void {
    const now = Date.now();
    this.state = {
      isActive: true,
      lastActivity: now,
      sessionStart: now,
      showWarning: false,
      timeRemaining: this.config.inactivityTimeout,
    };

    this.resetTimers();
    this.notifyListeners();
    console.log('Session started');
  }

  // End the current session
  endSession(): void {
    this.state = {
      isActive: false,
      lastActivity: 0,
      sessionStart: 0,
      showWarning: false,
      timeRemaining: 0,
    };

    this.clearTimers();
    this.notifyListeners();
    console.log('Session ended');
  }

  // Update last activity time
  updateActivity(): void {
    if (!this.state.isActive) return;

    const now = Date.now();
    this.state.lastActivity = now;
    this.state.showWarning = false;
    this.state.timeRemaining = this.config.inactivityTimeout;

    this.resetTimers();
    this.notifyListeners();
  }

  // Check if session can be terminated (after minimum duration)
  canTerminateSession(): boolean {
    if (!this.state.isActive) return false;
    
    const sessionDuration = Date.now() - this.state.sessionStart;
    return sessionDuration >= this.config.minSessionDuration;
  }

  // Get time remaining until forced logout
  getTimeRemaining(): number {
    if (!this.state.isActive) return 0;
    
    const timeSinceActivity = Date.now() - this.state.lastActivity;
    const remaining = this.config.inactivityTimeout - timeSinceActivity;
    return Math.max(0, remaining);
  }

  // Get session duration
  getSessionDuration(): number {
    if (!this.state.isActive) return 0;
    return Date.now() - this.state.sessionStart;
  }

  // Add listener for session state changes
  addListener(callback: (state: SessionState) => void): void {
    this.listeners.add(callback);
  }

  // Remove listener
  removeListener(callback: (state: SessionState) => void): void {
    this.listeners.delete(callback);
  }

  // Get current session state
  getState(): SessionState {
    return { ...this.state };
  }

  // Extend session (reset inactivity timer)
  extendSession(): void {
    if (!this.state.isActive) return;
    
    this.updateActivity();
    console.log('Session extended');
  }

  // Setup activity listeners for mouse, keyboard, touch events
  private setupActivityListeners(): void {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    const throttledUpdate = this.throttle(() => {
      this.updateActivity();
    }, 1000); // Throttle to once per second

    events.forEach(event => {
      document.addEventListener(event, throttledUpdate, true);
    });
  }

  // Reset all timers
  private resetTimers(): void {
    this.clearTimers();

    if (!this.state.isActive) return;

    // Set warning timer (4 minutes after activity)
    this.warningTimer = setTimeout(() => {
      if (this.state.isActive && this.canTerminateSession()) {
        this.state.showWarning = true;
        this.notifyListeners();
        console.log('Session warning: 1 minute until logout');
      }
    }, this.config.inactivityTimeout - this.config.warningTime);

    // Set logout timer (5 minutes after activity)
    this.logoutTimer = setTimeout(() => {
      if (this.state.isActive && this.canTerminateSession()) {
        console.log('Session expired due to inactivity');
        this.handleSessionExpiry();
      }
    }, this.config.inactivityTimeout);

    // Update time remaining every second
    this.activityTimer = setInterval(() => {
      if (this.state.isActive) {
        this.state.timeRemaining = this.getTimeRemaining();
        this.notifyListeners();
      }
    }, 1000);
  }

  // Clear all timers
  private clearTimers(): void {
    if (this.activityTimer) {
      clearInterval(this.activityTimer);
      this.activityTimer = null;
    }
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
      this.warningTimer = null;
    }
    if (this.logoutTimer) {
      clearTimeout(this.logoutTimer);
      this.logoutTimer = null;
    }
  }

  // Handle session expiry
  private handleSessionExpiry(): void {
    this.endSession();
    
    // Trigger logout
    window.dispatchEvent(new CustomEvent('session-expired', {
      detail: { reason: 'inactivity' }
    }));
  }

  // Notify all listeners of state changes
  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.getState());
      } catch (error) {
        console.error('Error in session listener:', error);
      }
    });
  }

  // Throttle function to limit how often activity updates occur
  private throttle(func: Function, limit: number): Function {
    let inThrottle: boolean;
    return function(this: any, ...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Format time remaining for display
  static formatTimeRemaining(milliseconds: number): string {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // Format session duration for display
  static formatSessionDuration(milliseconds: number): string {
    const hours = Math.floor(milliseconds / 3600000);
    const minutes = Math.floor((milliseconds % 3600000) / 60000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }
}

export default SessionService;
