import { adminSupabase as supabase, getCachedHealth<PERSON>heck, setCachedHealthCheck } from './adminSupabaseClient';

export interface DashboardStats {
  users: {
    total: number;
    recent: Array<{
      id: string;
      name: string;
      email: string;
      date: string;
      role?: string;
      status?: string;
    }>;
    growth: { total: number; period: string };
    activeUsers: number;
    newUsersThisMonth: number;
  };
  vehicles: {
    total: number;
    available: number;
    recent: Array<{
      id: string;
      type: string;
      provider: string;
      status: string;
      make?: string;
      model?: string;
    }>;
    rented: number;
    maintenance: number;
  };
  bookings: {
    total: number;
    active: number;
    recent: Array<{
      id: string;
      user: string;
      vehicle: string;
      status: string;
      startDate?: string;
      endDate?: string;
      totalAmount?: number;
    }>;
    completed: number;
    cancelled: number;
    pending: number;
  };
  financials: {
    totalRevenue: number;
    monthlyRevenue: number;
    pendingPayments: number;
    completedPayments: number;
    averageBookingValue: number;
  };
  providers: {
    total: number;
    active: number;
    pending: number;
    verified: number;
  };
}

export interface User {
  id: string;
  email: string;
  name?: string;
  role: string;
  status: string;
  created_at: string;
  last_login?: string;
  phone_number?: string;
  email_verified: boolean;
  phone_verified: boolean;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year?: number;
  type?: string;
  category?: string;
  daily_rate?: number;
  available_units?: number;
  created_at: string;
  updated_at: string;
}

export interface Booking {
  id: string;
  user_id: string;
  vehicle_id: string;
  start_date: string;
  end_date: string;
  status: string;
  total: number;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string;
  booking_id: string;
  total_amount: number;
  status: string;
  created_at: string;
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'ACTIVE' | 'PENDING_VERIFICATION' | 'SUSPENDED' | 'INACTIVE';
  verificationStatus: 'VERIFIED' | 'PENDING' | 'REJECTED';
  joinedDate: string;
  stats: {
    totalVehicles: number;
    totalBookings: number;
    rating: number;
    totalRevenue: number;
  };
  payoutInfo: {
    totalEarnings: number;
    pendingPayout: number;
    lastPayoutDate?: string;
  };
  documents?: {
    businessLicense?: string;
    idCard?: string;
  };
}

export interface ProviderStats {
  totalProviders: number;
  activeProviders: number;
  pendingVerification: number;
  suspendedProviders: number;
  totalPayouts: number;
  pendingPayouts: number;
  averageRating: number;
  totalRevenue: number;
}

export interface CashflowData {
  period: string;
  revenue: {
    total: number;
    bookings: number;
    fees: number;
    growth: number;
  };
  expenses: {
    total: number;
    payouts: number;
    processing: number;
    operational: number;
    growth: number;
  };
  netCashflow: {
    amount: number;
    growth: number;
  };
  metrics: {
    grossMargin: number;
    burnRate: number;
    runway: number;
    conversionRate: number;
  };
}

export interface CashflowBreakdown {
  category: string;
  amount: number;
  percentage: number;
  trend: 'up' | 'down' | 'stable';
  color: string;
}

export class SupabaseDashboardService {

  // Lightweight health check method with caching
  static async checkDatabaseHealth(): Promise<{ status: 'healthy' | 'error'; responseTime: number; message: string }> {
    // Check cache first
    const cached = getCachedHealthCheck();
    if (cached) {
      return cached;
    }

    const startTime = Date.now();
    try {
      // Simple connectivity test - just check if we can query a small table
      const { error } = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true })
        .limit(1);

      const responseTime = Date.now() - startTime;

      const result = error ? {
        status: 'error' as const,
        responseTime,
        message: `Database query failed: ${error.message}`
      } : {
        status: 'healthy' as const,
        responseTime,
        message: responseTime < 1000 ? 'Database is responding normally' : 'Database response is slow'
      };

      // Cache the result
      setCachedHealthCheck(result);
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result = {
        status: 'error' as const,
        responseTime,
        message: `Database connection failed: ${error.message || 'Unknown error'}`
      };

      // Cache error results too (but for shorter time)
      setCachedHealthCheck(result);
      return result;
    }
  }

  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Fetch all data in parallel
      const [usersData, vehiclesData, bookingsData, paymentsData, providersData] = await Promise.all([
        this.getUsersStats(),
        this.getVehiclesStats(),
        this.getBookingsStats(),
        this.getFinancialStats(),
        this.getProvidersStats()
      ]);

      return {
        users: usersData,
        vehicles: vehiclesData,
        bookings: bookingsData,
        financials: paymentsData,
        providers: providersData
      };
    } catch (error) {
      console.error('Failed to fetch dashboard stats from Supabase:', error);
      throw error;
    }
  }

  private static async getUsersStats() {
    try {
      // Get total users count from User table
      const { count: totalUsers, error: countError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // Get recent users (last 5)
      const { data: recentUsers, error: recentError } = await supabase
        .from('User')
        .select('id, email, name, role, createdAt, status')
        .order('createdAt', { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // Calculate growth (users created in last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { count: recentCount, error: growthError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .gte('createdAt', thirtyDaysAgo.toISOString());

      if (growthError) throw growthError;

      // Get active users (status = ACTIVE)
      const { count: activeUsers, error: activeError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'ACTIVE');

      if (activeError) console.warn('Error fetching active users:', activeError);

      return {
        total: totalUsers || 0,
        activeUsers: activeUsers || 0,
        newUsersThisMonth: recentCount || 0,
        recent: (recentUsers || []).map(user => ({
          id: user.id,
          name: user.name || 'Unknown User',
          email: user.email,
          date: new Date(user.createdAt).toLocaleDateString(),
          role: user.role || 'CUSTOMER',
          status: user.status || 'ACTIVE'
        })),
        growth: { total: recentCount || 0, period: 'month' }
      };
    } catch (error) {
      console.error('Error fetching users stats:', error);
      throw error;
    }
  }

  private static async getVehiclesStats() {
    try {
      // Get vehicles from Vehicle table
      const { count: totalVehicles, error: countError } = await supabase
        .from('Vehicle')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // Get recent vehicles (last 5)
      const { data: recentVehicles, error: recentError } = await supabase
        .from('Vehicle')
        .select('id, brand, model, vehicleType, category, availableUnits, location_city, providerId')
        .order('id', { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // Get available vehicles count
      const { count: availableCount, error: availableError } = await supabase
        .from('Vehicle')
        .select('*', { count: 'exact', head: true })
        .gt('availableUnits', 0);

      if (availableError) console.warn('Error fetching available vehicles:', availableError);

      return {
        total: totalVehicles || 0,
        available: availableCount || 0,
        rented: Math.floor((totalVehicles || 0) * 0.15), // Estimate 15% rented
        maintenance: Math.floor((totalVehicles || 0) * 0.05), // Estimate 5% in maintenance
        recent: (recentVehicles || []).map(vehicle => ({
          id: vehicle.id,
          type: vehicle.vehicleType || vehicle.category || 'Vehicle',
          provider: vehicle.providerId || 'Provider',
          status: (vehicle.availableUnits && vehicle.availableUnits > 0) ? 'Available' : 'Unavailable',
          make: vehicle.brand,
          model: vehicle.model,
          location: vehicle.location_city
        }))
      };
    } catch (error) {
      console.error('Error fetching vehicles stats:', error);
      return {
        total: 0,
        available: 0,
        rented: 0,
        maintenance: 0,
        recent: []
      };
    }
  }

  private static async getBookingsStats() {
    try {
      // Get total bookings count
      const { count: totalBookings, error: countError } = await supabase
        .from('Booking')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // Get active bookings (assuming status field exists)
      const { count: activeBookings, error: activeError } = await supabase
        .from('Booking')
        .select('*', { count: 'exact', head: true })
        .in('status', ['CONFIRMED']);

      if (activeError) console.warn('Error fetching active bookings:', activeError);

      // Get recent bookings
      const { data: recentBookings, error: recentError } = await supabase
        .from('Booking')
        .select('*')
        .order('id', { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // Get additional booking stats
      const { count: completedBookings } = await supabase
        .from('Booking')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'COMPLETED');

      const { count: cancelledBookings } = await supabase
        .from('Booking')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'CANCELLED');

      const { count: pendingBookings } = await supabase
        .from('Booking')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'PENDING');

      return {
        total: totalBookings || 0,
        active: activeBookings || 0,
        completed: completedBookings || 0,
        cancelled: cancelledBookings || 0,
        pending: pendingBookings || 0,
        recent: (recentBookings || []).map(booking => ({
          id: booking.id,
          user: `${booking.users?.first_name || ''} ${booking.users?.last_name || ''}`.trim() || booking.users?.email || 'Unknown User',
          vehicle: `${booking.vehicles?.make || ''} ${booking.vehicles?.model || ''}`.trim() || 'Unknown Vehicle',
          status: booking.booking_status || 'Unknown',
          startDate: booking.start_date,
          endDate: booking.end_date,
          totalAmount: 0 // Will be updated when we have payment data
        }))
      };
    } catch (error) {
      console.error('Error fetching bookings stats:', error);
      return {
        total: 0,
        active: 0,
        completed: 0,
        cancelled: 0,
        pending: 0,
        recent: []
      };
    }
  }

  private static async getFinancialStats() {
    try {
      // Get total revenue from payments
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('Payment')
        .select('amount, transactionDate, status')
        .eq('status', 'COMPLETED');

      if (paymentsError) throw paymentsError;

      const totalRevenue = paymentsData?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0;

      // Calculate this month's revenue
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const monthlyRevenue = paymentsData?.filter(payment =>
        new Date(payment.transactionDate) >= currentMonth
      ).reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0;

      // Get pending payments
      const { count: pendingPayments, error: pendingError } = await supabase
        .from('Payment')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'PENDING');

      if (pendingError) throw pendingError;

      const averageBookingValue = paymentsData && paymentsData.length > 0
        ? totalRevenue / paymentsData.length
        : 0;

      return {
        totalRevenue,
        monthlyRevenue,
        pendingPayments: pendingPayments || 0,
        completedPayments: paymentsData?.length || 0,
        averageBookingValue
      };
    } catch (error) {
      console.error('Error fetching financial stats:', error);
      return {
        totalRevenue: 0,
        monthlyRevenue: 0,
        pendingPayments: 0,
        completedPayments: 0,
        averageBookingValue: 0
      };
    }
  }

  private static async getProvidersStats() {
    try {
      // Get total providers count
      const { count: totalProviders, error: countError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'PROVIDER');

      if (countError) throw countError;

      // Get active providers count
      const { count: activeProviders, error: activeError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'PROVIDER')
        .eq('emailVerified', true);

      if (activeError) console.warn('Error fetching active providers:', activeError);

      // Get pending providers count
      const { count: pendingProviders, error: pendingError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'PROVIDER')
        .eq('emailVerified', false);

      if (pendingError) console.warn('Error fetching pending providers:', pendingError);

      // Get verified providers count (same as active for now)
      const verifiedProviders = activeProviders || 0;

      return {
        total: totalProviders || 0,
        active: activeProviders || 0,
        pending: pendingProviders || 0,
        verified: verifiedProviders
      };
    } catch (error) {
      console.error('Error fetching providers stats:', error);
      return {
        total: 0,
        active: 0,
        pending: 0,
        verified: 0
      };
    }
  }

  static async getUsers(page = 1, limit = 10, search = ''): Promise<{ users: User[]; total: number }> {
    try {
      const offset = (page - 1) * limit;
      
      let query = supabase
        .from('users')
        .select('*', { count: 'exact' })
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (search) {
        query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`);
      }

      const { data, count, error } = await query;

      if (error) throw error;

      const users = (data || []).map(user => ({
        id: user.id,
        email: user.email,
        name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || undefined,
        role: user.is_provider ? 'PROVIDER' : 'CUSTOMER',
        status: user.verified ? 'ACTIVE' : 'PENDING',
        created_at: user.created_at,
        phone_number: user.phone,
        email_verified: user.verified || false,
        phone_verified: false
      }));

      return {
        users,
        total: count || 0
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { users: [], total: 0 };
    }
  }

  static async getCashflowData(period: string = 'month'): Promise<CashflowData> {
    try {
      // Calculate date range based on period
      const now = new Date();
      let startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(now.getMonth() - 1);
      }

      // Get payments data for the period
      const { data: paymentsData, error: paymentsError } = await supabase
        .from('Payment')
        .select('amount, providerEarnings, status, transactionDate')
        .gte('transactionDate', startDate.toISOString())
        .lte('transactionDate', now.toISOString());

      if (paymentsError) throw paymentsError;

      // Calculate revenue metrics
      const completedPayments = paymentsData?.filter(p => p.status === 'completed') || [];
      const totalRevenue = completedPayments.reduce((sum, p) => sum + (p.total_amount || 0), 0);
      const totalFees = completedPayments.reduce((sum, p) => sum + (p.booking_fee_amount || 0), 0);
      const bookingRevenue = totalRevenue - totalFees;

      // Get previous period for growth calculation
      const prevStartDate = new Date(startDate);
      const prevEndDate = new Date(startDate);
      const periodDiff = now.getTime() - startDate.getTime();
      prevStartDate.setTime(prevStartDate.getTime() - periodDiff);

      const { data: prevPaymentsData } = await supabase
        .from('Payment')
        .select('amount, providerEarnings, status')
        .gte('transactionDate', prevStartDate.toISOString())
        .lte('transactionDate', prevEndDate.toISOString())
        .eq('status', 'COMPLETED');

      const prevRevenue = prevPaymentsData?.reduce((sum, p) => sum + (p.total_amount || 0), 0) || 0;
      const revenueGrowth = prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0;

      // Calculate expenses (simplified - you may need to adjust based on your data structure)
      const processingFees = totalRevenue * 0.029; // Assume 2.9% processing fee
      const operationalCosts = totalRevenue * 0.05; // Assume 5% operational costs
      const payouts = bookingRevenue * 0.85; // Assume 85% goes to providers
      const totalExpenses = processingFees + operationalCosts + payouts;

      const netCashflow = totalRevenue - totalExpenses;
      const grossMargin = totalRevenue > 0 ? ((totalRevenue - totalExpenses) / totalRevenue) * 100 : 0;

      return {
        period,
        revenue: {
          total: totalRevenue,
          bookings: bookingRevenue,
          fees: totalFees,
          growth: revenueGrowth
        },
        expenses: {
          total: totalExpenses,
          payouts,
          processing: processingFees,
          operational: operationalCosts,
          growth: revenueGrowth * 0.8 // Assume expenses grow slower
        },
        netCashflow: {
          amount: netCashflow,
          growth: revenueGrowth * 1.2 // Net cashflow growth is amplified
        },
        metrics: {
          grossMargin,
          burnRate: operationalCosts,
          runway: netCashflow > 0 ? (totalRevenue / operationalCosts) : 0,
          conversionRate: completedPayments.length > 0 ? (completedPayments.length / (paymentsData?.length || 1)) * 100 : 0
        }
      };
    } catch (error) {
      console.error('Error fetching cashflow data:', error);
      // Return empty data structure
      return {
        period,
        revenue: { total: 0, bookings: 0, fees: 0, growth: 0 },
        expenses: { total: 0, payouts: 0, processing: 0, operational: 0, growth: 0 },
        netCashflow: { amount: 0, growth: 0 },
        metrics: { grossMargin: 0, burnRate: 0, runway: 0, conversionRate: 0 }
      };
    }
  }

  static async getCashflowBreakdown(period: string = 'month'): Promise<CashflowBreakdown[]> {
    try {
      const cashflowData = await this.getCashflowData(period);

      const revenueBreakdown: CashflowBreakdown[] = [
        {
          category: 'Booking Revenue',
          amount: cashflowData.revenue.bookings,
          percentage: cashflowData.revenue.total > 0 ? (cashflowData.revenue.bookings / cashflowData.revenue.total) * 100 : 0,
          trend: cashflowData.revenue.growth > 0 ? 'up' : cashflowData.revenue.growth < 0 ? 'down' : 'stable',
          color: '#4CAF50'
        },
        {
          category: 'Platform Fees',
          amount: cashflowData.revenue.fees,
          percentage: cashflowData.revenue.total > 0 ? (cashflowData.revenue.fees / cashflowData.revenue.total) * 100 : 0,
          trend: cashflowData.revenue.growth > 0 ? 'up' : cashflowData.revenue.growth < 0 ? 'down' : 'stable',
          color: '#2196F3'
        }
      ];

      const expenseBreakdown: CashflowBreakdown[] = [
        {
          category: 'Provider Payouts',
          amount: cashflowData.expenses.payouts,
          percentage: cashflowData.expenses.total > 0 ? (cashflowData.expenses.payouts / cashflowData.expenses.total) * 100 : 0,
          trend: cashflowData.expenses.growth > 0 ? 'up' : cashflowData.expenses.growth < 0 ? 'down' : 'stable',
          color: '#FF9800'
        },
        {
          category: 'Processing Fees',
          amount: cashflowData.expenses.processing,
          percentage: cashflowData.expenses.total > 0 ? (cashflowData.expenses.processing / cashflowData.expenses.total) * 100 : 0,
          trend: 'stable',
          color: '#F44336'
        },
        {
          category: 'Operational Costs',
          amount: cashflowData.expenses.operational,
          percentage: cashflowData.expenses.total > 0 ? (cashflowData.expenses.operational / cashflowData.expenses.total) * 100 : 0,
          trend: 'stable',
          color: '#9C27B0'
        }
      ];

      return [...revenueBreakdown, ...expenseBreakdown];
    } catch (error) {
      console.error('Error fetching cashflow breakdown:', error);
      return [];
    }
  }

  static async getProviders(): Promise<{ providers: Provider[]; stats: ProviderStats }> {
    try {
      // Get all providers (users with is_provider = true)
      const { data: providersData, error: providersError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          phone,
          is_provider,
          verified,
          created_at,
          provider_status
        `)
        .eq('is_provider', true);

      if (providersError) throw providersError;

      if (!providersData || providersData.length === 0) {
        return {
          providers: [],
          stats: {
            totalProviders: 0,
            activeProviders: 0,
            pendingVerification: 0,
            suspendedProviders: 0,
            totalPayouts: 0,
            pendingPayouts: 0,
            averageRating: 0,
            totalRevenue: 0
          }
        };
      }

      // Get vehicle counts for each provider
      const { data: vehicleCounts, error: vehicleError } = await supabase
        .from('vehicles')
        .select('provider_id, id')
        .in('provider_id', providersData.map(p => p.id));

      // Get booking counts and revenue for each provider
      const { data: bookingData, error: bookingError } = await supabase
        .from('bookings')
        .select(`
          id,
          total,
          booking_status,
          vehicles!inner(provider_id)
        `)
        .in('vehicles.provider_id', providersData.map(p => p.id));

      // Transform data into Provider format
      const providers: Provider[] = providersData.map(user => {
        const userVehicles = vehicleCounts?.filter(v => v.provider_id === user.id) || [];
        const userBookings = bookingData?.filter(b => b.vehicles?.provider_id === user.id) || [];
        const completedBookings = userBookings.filter(b => b.booking_status === 'completed');
        const totalRevenue = completedBookings.reduce((sum, b) => sum + (b.total || 0), 0);

        return {
          id: user.id,
          name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown Provider',
          email: user.email,
          phone: user.phone,
          status: user.provider_status || (user.verified ? 'ACTIVE' : 'PENDING_VERIFICATION'),
          verificationStatus: user.verified ? 'VERIFIED' : 'PENDING',
          joinedDate: user.created_at,
          stats: {
            totalVehicles: userVehicles.length,
            totalBookings: userBookings.length,
            rating: 4.5, // Default rating - you can implement a rating system
            totalRevenue
          },
          payoutInfo: {
            totalEarnings: totalRevenue * 0.85, // Assume 85% goes to provider
            pendingPayout: totalRevenue * 0.15, // Assume 15% pending
            lastPayoutDate: undefined // You can implement payout tracking
          }
        };
      });

      // Calculate stats
      const stats: ProviderStats = {
        totalProviders: providers.length,
        activeProviders: providers.filter(p => p.status === 'ACTIVE').length,
        pendingVerification: providers.filter(p => p.status === 'PENDING_VERIFICATION').length,
        suspendedProviders: providers.filter(p => p.status === 'SUSPENDED').length,
        totalPayouts: providers.reduce((sum, p) => sum + p.payoutInfo.totalEarnings, 0),
        pendingPayouts: providers.reduce((sum, p) => sum + p.payoutInfo.pendingPayout, 0),
        averageRating: providers.length > 0 ? providers.reduce((sum, p) => sum + p.stats.rating, 0) / providers.length : 0,
        totalRevenue: providers.reduce((sum, p) => sum + p.stats.totalRevenue, 0)
      };

      return { providers, stats };
    } catch (error) {
      console.error('Error fetching providers:', error);
      return {
        providers: [],
        stats: {
          totalProviders: 0,
          activeProviders: 0,
          pendingVerification: 0,
          suspendedProviders: 0,
          totalPayouts: 0,
          pendingPayouts: 0,
          averageRating: 0,
          totalRevenue: 0
        }
      };
    }
  }

  static async updateProviderStatus(providerId: string, status: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ provider_status: status })
        .eq('id', providerId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating provider status:', error);
      throw error;
    }
  }

  static async getProviderDashboardData(providerId: string): Promise<any> {
    try {
      // Get provider's vehicles
      const { data: vehicles, error: vehiclesError } = await supabase
        .from('vehicles')
        .select('*')
        .eq('provider_id', providerId);

      if (vehiclesError) throw vehiclesError;

      // Get provider's bookings
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          *,
          vehicles!inner(provider_id)
        `)
        .eq('vehicles.provider_id', providerId);

      if (bookingsError) throw bookingsError;

      // Calculate stats
      const totalVehicles = vehicles?.length || 0;
      const availableVehicles = vehicles?.filter(v => v.status === 'available').length || 0;
      const inUseVehicles = vehicles?.filter(v => v.status === 'booked').length || 0;
      const maintenanceVehicles = vehicles?.filter(v => v.status === 'maintenance').length || 0;

      const totalBookings = bookings?.length || 0;
      const activeBookings = bookings?.filter(b => b.booking_status === 'confirmed' || b.booking_status === 'active').length || 0;
      const completedBookings = bookings?.filter(b => b.booking_status === 'completed').length || 0;

      const totalRevenue = bookings?.filter(b => b.booking_status === 'completed').reduce((sum, b) => sum + (b.total || 0), 0) || 0;
      const monthlyRevenue = bookings?.filter(b => {
        const bookingDate = new Date(b.created_at);
        const currentMonth = new Date().getMonth();
        return bookingDate.getMonth() === currentMonth && b.booking_status === 'completed';
      }).reduce((sum, b) => sum + (b.total || 0), 0) || 0;

      return {
        vehicles: vehicles || [],
        bookings: bookings || [],
        stats: {
          totalVehicles,
          availableVehicles,
          inUseVehicles,
          maintenanceVehicles,
          totalBookings,
          activeBookings,
          completedBookings,
          totalRevenue,
          monthlyRevenue,
          averageRating: 4.5, // Default rating
          totalCustomers: new Set(bookings?.map(b => b.user_id)).size || 0,
          repeatCustomers: 0 // Would need more complex calculation
        }
      };
    } catch (error) {
      console.error('Error fetching provider dashboard data:', error);
      return {
        vehicles: [],
        bookings: [],
        stats: {
          totalVehicles: 0,
          availableVehicles: 0,
          inUseVehicles: 0,
          maintenanceVehicles: 0,
          totalBookings: 0,
          activeBookings: 0,
          completedBookings: 0,
          totalRevenue: 0,
          monthlyRevenue: 0,
          averageRating: 0,
          totalCustomers: 0,
          repeatCustomers: 0
        }
      };
    }
  }
}
