import { adminSupabase as supabase } from './adminSupabaseClient';

export interface Notification {
  id: string;
  userId: string;
  type: 'BOOKING_CONFIRMED' | 'PAYMENT_RECEIVED' | 'VEHICLE_RETURNED' | 'SYSTEM_ALERT';
  title: string;
  message: string;
  status: 'PENDING' | 'SENT' | 'FAILED';
  readAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  urgent: number;
  todayCount: number;
}

export class NotificationService {
  private static listeners: Set<(notifications: Notification[]) => void> = new Set();
  private static realtimeSubscription: any = null;

  // Real-time subscription management
  static startRealtimeSubscription() {
    if (this.realtimeSubscription) return;

    this.realtimeSubscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Notification'
        },
        (payload) => {
          console.log('Notification change:', payload);
          this.notifyListeners();
        }
      )
      .subscribe();
  }

  static stopRealtimeSubscription() {
    if (this.realtimeSubscription) {
      supabase.removeChannel(this.realtimeSubscription);
      this.realtimeSubscription = null;
    }
  }

  static addListener(callback: (notifications: Notification[]) => void) {
    this.listeners.add(callback);
    this.startRealtimeSubscription();
  }

  static removeListener(callback: (notifications: Notification[]) => void) {
    this.listeners.delete(callback);
    if (this.listeners.size === 0) {
      this.stopRealtimeSubscription();
    }
  }

  private static async notifyListeners() {
    try {
      const notifications = await this.getNotifications();
      this.listeners.forEach(callback => callback(notifications.data));
    } catch (error) {
      console.error('Error notifying listeners:', error);
    }
  }

  // CRUD Operations
  static async getNotifications(
    page = 1,
    size = 20,
    options: {
      unreadOnly?: boolean;
      type?: string;
      priority?: string;
    } = {}
  ) {
    try {
      const from = (page - 1) * size;
      const to = from + size - 1;

      let query = supabase
        .from('Notification')
        .select('*', { count: 'exact' })
        .range(from, to)
        .order('createdAt', { ascending: false });

      // Apply filters
      if (options.unreadOnly) {
        query = query.is('readAt', null);
      }

      if (options.type) {
        query = query.eq('type', options.type);
      }

      // Note: Notification table doesn't have priority or expires_at fields

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  static async getNotificationStats(): Promise<NotificationStats> {
    try {
      // Get total notifications
      const { count: total, error: totalError } = await supabase
        .from('Notification')
        .select('*', { count: 'exact', head: true });

      if (totalError) throw totalError;

      // Get unread notifications
      const { count: unread, error: unreadError } = await supabase
        .from('Notification')
        .select('*', { count: 'exact', head: true })
        .is('readAt', null);

      if (unreadError) console.warn('Error fetching unread notifications:', unreadError);

      // Note: Notification table doesn't have priority field, so we skip urgent count
      const urgent = 0;

      // Get today's notifications
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { count: todayCount, error: todayError } = await supabase
        .from('Notification')
        .select('*', { count: 'exact', head: true })
        .gte('createdAt', today.toISOString());

      if (todayError) console.warn('Error fetching today notifications:', todayError);

      return {
        total: total || 0,
        unread: unread || 0,
        urgent: urgent || 0,
        todayCount: todayCount || 0
      };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      return {
        total: 0,
        unread: 0,
        urgent: 0,
        todayCount: 0
      };
    }
  }

  static async createNotification(notification: Omit<Notification, 'id' | 'created_at'>): Promise<Notification> {
    try {
      const { data, error } = await supabase
        .from('Notification')
        .insert({
          ...notification,
          createdAt: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  static async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('Notification')
        .update({ readAt: new Date().toISOString() })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  static async markAllAsRead(): Promise<void> {
    try {
      const { error } = await supabase
        .from('Notification')
        .update({ readAt: new Date().toISOString() })
        .is('readAt', null);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  static async deleteNotification(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('Notification')
        .delete()
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // System notification generators
  static async createSystemNotification(
    type: 'user_registered' | 'booking_created' | 'payment_received' | 'vehicle_added' | 'provider_verified',
    data: any
  ): Promise<void> {
    try {
      let notification: Omit<Notification, 'id' | 'createdAt' | 'updatedAt'>;

      switch (type) {
        case 'user_registered':
          notification = {
            userId: 'system',
            title: 'New User Registration',
            message: `${data.name} (${data.email}) has registered as a ${data.is_provider ? 'provider' : 'customer'}`,
            type: 'SYSTEM_ALERT',
            status: 'PENDING'
          };
          break;

        case 'booking_created':
          notification = {
            userId: 'system',
            title: 'New Booking Created',
            message: `Booking #${data.id} created for ${data.vehicle_make} ${data.vehicle_model}`,
            type: 'BOOKING_CONFIRMED',
            status: 'PENDING'
          };
          break;

        case 'payment_received':
          notification = {
            userId: 'system',
            title: 'Payment Received',
            message: `Payment of $${data.amount} received for booking #${data.booking_id}`,
            type: 'PAYMENT_RECEIVED',
            status: 'PENDING'
          };
          break;

        case 'vehicle_added':
          notification = {
            userId: 'system',
            title: 'New Vehicle Added',
            message: `${data.make} ${data.model} (${data.year}) added by ${data.provider_name}`,
            type: 'SYSTEM_ALERT',
            status: 'PENDING'
          };
          break;

        case 'provider_verified':
          notification = {
            userId: 'system',
            title: 'Provider Verified',
            message: `${data.name} has been verified as a provider`,
            type: 'SYSTEM_ALERT',
            status: 'PENDING'
          };
          break;

        default:
          throw new Error(`Unknown notification type: ${type}`);
      }

      await this.createNotification(notification);
    } catch (error) {
      console.error('Error creating system notification:', error);
      // Don't throw here to avoid breaking the main flow
    }
  }

  // Bulk operations
  static async createBulkNotification(
    title: string,
    message: string,
    type: Notification['type'] = 'SYSTEM_ALERT'
  ): Promise<void> {
    try {
      await this.createNotification({
        userId: 'system',
        title,
        message,
        type,
        status: 'PENDING'
      });
    } catch (error) {
      console.error('Error creating bulk notification:', error);
      throw error;
    }
  }

  static async cleanupExpiredNotifications(): Promise<void> {
    try {
      // Note: Notification table doesn't have expires_at field, so we skip cleanup
      console.log('Notification cleanup skipped - no expires_at field');
      return;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      throw error;
    }
  }
}

export default NotificationService;
