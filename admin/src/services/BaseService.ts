import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { adminSupabase as supabase } from './adminSupabaseClient';
import * as helper from '../utils/helper';

// Base configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('[API] Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/sign-in';
    }
    
    // Log error for debugging
    console.error('[API] Response Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url,
    });
    
    return Promise.reject(error);
  }
);

// Base service class with common patterns
export abstract class BaseService {
  protected static apiClient = apiClient;
  protected static supabase = supabase;

  // Generic API call with fallback to Supabase
  protected static async apiCall<T>(
    apiCall: () => Promise<AxiosResponse<T>>,
    supabaseCall?: () => Promise<T>,
    fallbackData?: T
  ): Promise<T> {
    try {
      // Try API first
      const response = await apiCall();
      return response.data;
    } catch (apiError) {
      console.warn('[BaseService] API call failed, trying Supabase fallback:', apiError);
      
      if (supabaseCall) {
        try {
          return await supabaseCall();
        } catch (supabaseError) {
          console.error('[BaseService] Supabase fallback also failed:', supabaseError);
          if (fallbackData !== undefined) {
            return fallbackData;
          }
          throw supabaseError;
        }
      }
      
      if (fallbackData !== undefined) {
        return fallbackData;
      }
      
      throw apiError;
    }
  }

  // Generic Supabase query with error handling
  protected static async supabaseQuery<T>(
    query: () => Promise<{ data: T | null; error: any }>
  ): Promise<T> {
    try {
      const { data, error } = await query();
      
      if (error) {
        console.error('[BaseService] Supabase query error:', error);
        throw new Error(error.message || 'Database query failed');
      }
      
      if (data === null) {
        throw new Error('No data returned from query');
      }
      
      return data;
    } catch (error) {
      console.error('[BaseService] Supabase query failed:', error);
      throw error;
    }
  }

  // Pagination helper
  protected static getPaginationParams(page: number, size: number) {
    const from = (page - 1) * size;
    const to = from + size - 1;
    return { from, to };
  }

  // Error handling helper
  protected static handleError(error: any, context: string): never {
    console.error(`[${context}] Error:`, error);
    
    if (error.response?.data?.message) {
      throw new Error(error.response.data.message);
    }
    
    if (error.message) {
      throw new Error(error.message);
    }
    
    throw new Error(`${context} operation failed`);
  }

  // Success response helper
  protected static createResponse<T>(data: T, message?: string) {
    return {
      success: true,
      data,
      message: message || 'Operation completed successfully',
    };
  }

  // Validation helper
  protected static validateRequired(data: any, fields: string[]): void {
    const missing = fields.filter(field => !data[field]);
    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }
  }

  // Date formatting helper
  protected static formatDate(date: Date | string): string {
    return new Date(date).toISOString();
  }

  // Search helper for Supabase
  protected static buildSearchQuery(searchTerm: string, fields: string[]) {
    if (!searchTerm.trim()) return '';
    
    const conditions = fields.map(field => `${field}.ilike.%${searchTerm}%`);
    return conditions.join(',');
  }
}

// Export the configured axios instance for direct use if needed
export { apiClient };

// Common response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

export interface FilterOptions {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// Common error types
export class ServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public context?: string
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

export class ValidationError extends ServiceError {
  constructor(message: string, public field?: string) {
    super(message, 400, 'Validation');
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends ServiceError {
  constructor(resource: string, id?: string) {
    super(`${resource}${id ? ` with id ${id}` : ''} not found`, 404, 'NotFound');
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends ServiceError {
  constructor(message = 'Unauthorized access') {
    super(message, 401, 'Unauthorized');
    this.name = 'UnauthorizedError';
  }
}
