import { BaseService, ApiResponse, PaginatedResponse, FilterOptions, ValidationError, NotFoundError } from './BaseService';
import * as RentaHubTypes from '../types/rentahub-types';

export interface Provider {
  id: string;
  user_id: string;
  business_name: string;
  business_type: string;
  business_license: string;
  tax_id: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  website?: string;
  description?: string;
  verified: boolean;
  verification_status: 'pending' | 'verified' | 'rejected';
  verification_documents?: string[];
  bank_account_name?: string;
  bank_account_number?: string;
  bank_name?: string;
  bank_swift_code?: string;
  created_at: string;
  updated_at: string;
  // Computed fields
  total_vehicles?: number;
  total_bookings?: number;
  total_revenue?: number;
  rating?: number;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface ProviderStats {
  totalProviders: number;
  activeProviders: number;
  pendingVerification: number;
  verifiedProviders: number;
  suspendedProviders: number;
  totalRevenue: number;
  averageRating: number;
}

export class ProviderService extends BaseService {
  
  static async getProviders(
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<Provider>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get('/admin/providers', {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('providers')
          .select(`
            *,
            users!inner(email, first_name, last_name),
            vehicles(count),
            bookings(count, total_amount)
          `, { count: 'exact' })
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`business_name.ilike.%${options.search}%,email.ilike.%${options.search}%`);
        }

        // Apply filters
        if (options.filters?.status) {
          if (options.filters.status === 'active') {
            query = query.eq('verified', true);
          } else if (options.filters.status === 'pending') {
            query = query.eq('verification_status', 'pending');
          } else if (options.filters.status === 'suspended') {
            query = query.eq('verified', false).neq('verification_status', 'pending');
          }
        }

        if (options.filters?.verification_status) {
          query = query.eq('verification_status', options.filters.verification_status);
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';
        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseProvider),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }

  static async getProvider(id: string): Promise<Provider> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.get(`/admin/providers/${id}`),
      async () => {
        const { data, error } = await this.supabase
          .from('providers')
          .select(`
            *,
            users!inner(email, first_name, last_name),
            vehicles(count),
            bookings(count, total_amount)
          `)
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Provider', id);
          }
          throw error;
        }

        return this.mapSupabaseProvider(data);
      }
    );
  }

  static async getProviderStats(): Promise<ProviderStats> {
    return this.apiCall(
      () => this.apiClient.get('/admin/providers/stats'),
      async () => {
        // Get total providers
        const { count: totalProviders, error: totalError } = await this.supabase
          .from('providers')
          .select('*', { count: 'exact', head: true });

        if (totalError) throw totalError;

        // Get active providers (verified)
        const { count: activeProviders, error: activeError } = await this.supabase
          .from('providers')
          .select('*', { count: 'exact', head: true })
          .eq('verified', true);

        if (activeError) console.warn('Error fetching active providers:', activeError);

        // Get pending verification
        const { count: pendingVerification, error: pendingError } = await this.supabase
          .from('providers')
          .select('*', { count: 'exact', head: true })
          .eq('verification_status', 'pending');

        if (pendingError) console.warn('Error fetching pending providers:', pendingError);

        // Get verified providers
        const { count: verifiedProviders, error: verifiedError } = await this.supabase
          .from('providers')
          .select('*', { count: 'exact', head: true })
          .eq('verification_status', 'verified');

        if (verifiedError) console.warn('Error fetching verified providers:', verifiedError);

        // Get suspended providers
        const { count: suspendedProviders, error: suspendedError } = await this.supabase
          .from('providers')
          .select('*', { count: 'exact', head: true })
          .eq('verified', false)
          .neq('verification_status', 'pending');

        if (suspendedError) console.warn('Error fetching suspended providers:', suspendedError);

        // Get total revenue from provider bookings
        const { data: revenueData, error: revenueError } = await this.supabase
          .from('bookings')
          .select('total_amount')
          .eq('booking_status', 'completed');

        const totalRevenue = (revenueData || []).reduce((sum, booking) => sum + (booking.total_amount || 0), 0);

        return {
          totalProviders: totalProviders || 0,
          activeProviders: activeProviders || 0,
          pendingVerification: pendingVerification || 0,
          verifiedProviders: verifiedProviders || 0,
          suspendedProviders: suspendedProviders || 0,
          totalRevenue,
          averageRating: 4.2 // TODO: Calculate from reviews
        };
      }
    );
  }

  static async updateProviderStatus(
    providerId: string, 
    status: 'verified' | 'rejected' | 'suspended'
  ): Promise<Provider> {
    this.validateRequired({ providerId, status }, ['providerId', 'status']);

    return this.apiCall(
      () => this.apiClient.patch(`/admin/providers/${providerId}/status`, { status }),
      async () => {
        const updateData: any = {
          verification_status: status,
          updated_at: new Date().toISOString()
        };

        if (status === 'verified') {
          updateData.verified = true;
        } else if (status === 'rejected' || status === 'suspended') {
          updateData.verified = false;
        }

        const { data, error } = await this.supabase
          .from('providers')
          .update(updateData)
          .eq('id', providerId)
          .select(`
            *,
            users!inner(email, first_name, last_name),
            vehicles(count),
            bookings(count, total_amount)
          `)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Provider', providerId);
          }
          throw error;
        }

        return this.mapSupabaseProvider(data);
      }
    );
  }

  static async deleteProvider(providerId: string): Promise<void> {
    this.validateRequired({ providerId }, ['providerId']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/providers/${providerId}`),
      async () => {
        // Soft delete by setting verified to false and status to suspended
        const { error } = await this.supabase
          .from('providers')
          .update({ 
            verified: false, 
            verification_status: 'rejected',
            updated_at: new Date().toISOString() 
          })
          .eq('id', providerId);

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Provider', providerId);
          }
          throw error;
        }
      }
    );
  }

  private static mapSupabaseProvider(data: any): Provider {
    return {
      id: data.id,
      user_id: data.user_id,
      business_name: data.business_name,
      business_type: data.business_type,
      business_license: data.business_license,
      tax_id: data.tax_id,
      address: data.address,
      city: data.city,
      country: data.country,
      phone: data.phone,
      email: data.users?.email || data.email,
      website: data.website,
      description: data.description,
      verified: data.verified,
      verification_status: data.verification_status,
      verification_documents: data.verification_documents,
      bank_account_name: data.bank_account_name,
      bank_account_number: data.bank_account_number,
      bank_name: data.bank_name,
      bank_swift_code: data.bank_swift_code,
      created_at: data.created_at,
      updated_at: data.updated_at,
      total_vehicles: data.vehicles?.length || 0,
      total_bookings: data.bookings?.length || 0,
      total_revenue: (data.bookings || []).reduce((sum: number, booking: any) => sum + (booking.total_amount || 0), 0),
      rating: 4.2, // TODO: Calculate from reviews
      status: data.verified ? 'active' : (data.verification_status === 'pending' ? 'inactive' : 'suspended')
    };
  }
}

export default ProviderService;
