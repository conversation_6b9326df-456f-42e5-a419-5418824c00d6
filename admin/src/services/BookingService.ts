import { BaseService, PaginatedResponse, FilterOptions, ValidationError, NotFoundError } from './BaseService';
import * as RentaHubTypes from '../types/rentahub-types';

export class BookingService extends BaseService {
  
  // CRUD Operations
  static async createBooking(data: RentaHubTypes.CreateBookingPayload): Promise<RentaHubTypes.Booking> {
    this.validateRequired(data, ['userId', 'vehicleId', 'startDate', 'endDate']);

    return this.apiCall(
      () => this.apiClient.post('/admin/bookings', data),
      async () => {
        // Calculate booking details
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

        // Get vehicle details for pricing
        const { data: vehicle, error: vehicleError } = await this.supabase
          .from('vehicles')
          .select('daily_rate, deposit, provider_id')
          .eq('id', data.vehicleId)
          .single();

        if (vehicleError) throw vehicleError;

        const subtotal = vehicle.daily_rate * totalDays;
        const taxes = subtotal * 0.1; // 10% tax
        const fees = 25; // Platform fee
        const total = subtotal + taxes + fees;

        const bookingData = {
          user_id: data.userId,
          vehicle_id: data.vehicleId,
          provider_id: vehicle.provider_id,
          start_date: data.startDate,
          end_date: data.endDate,
          pickup_location_id: data.pickupLocationId,
          return_location_id: data.returnLocationId,
          status: 'pending',
          total_days: totalDays,
          daily_rate: vehicle.daily_rate,
          subtotal,
          taxes,
          fees,
          total,
          deposit: vehicle.deposit,
          payment_status: 'pending',
          additional_services: data.additionalServices || [],
          special_requests: data.specialRequests,
          created_at: new Date().toISOString(),
        };

        const { data: created, error } = await this.supabase
          .from('bookings')
          .insert(bookingData)
          .select(`
            *,
            user:users!bookings_user_id_fkey(*),
            vehicle:vehicles!bookings_vehicle_id_fkey(*),
            provider:users!bookings_provider_id_fkey(*),
            pickup_location:locations!bookings_pickup_location_id_fkey(*),
            return_location:locations!bookings_return_location_id_fkey(*)
          `)
          .single();

        if (error) throw error;
        return this.mapSupabaseBooking(created);
      }
    );
  }

  static async updateBooking(id: string, data: RentaHubTypes.UpdateBookingPayload): Promise<RentaHubTypes.Booking> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.put(`/admin/bookings/${id}`, data),
      async () => {
        const updateData: any = {
          updated_at: new Date().toISOString(),
        };

        // Map fields
        if (data.status) {
          updateData.status = data.status;
          if (data.status === 'confirmed') {
            updateData.confirmed_at = new Date().toISOString();
          } else if (data.status === 'completed') {
            updateData.completed_at = new Date().toISOString();
          } else if (data.status === 'cancelled') {
            updateData.cancelled_at = new Date().toISOString();
          }
        }
        if (data.startDate) updateData.start_date = data.startDate;
        if (data.endDate) updateData.end_date = data.endDate;
        if (data.specialRequests !== undefined) updateData.special_requests = data.specialRequests;

        // Recalculate totals if dates changed
        if (data.startDate || data.endDate) {
          const { data: booking, error: bookingError } = await this.supabase
            .from('bookings')
            .select('start_date, end_date, daily_rate')
            .eq('id', id)
            .single();

          if (bookingError) throw bookingError;

          const startDate = new Date(data.startDate || booking.start_date);
          const endDate = new Date(data.endDate || booking.end_date);
          const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          
          const subtotal = booking.daily_rate * totalDays;
          const taxes = subtotal * 0.1;
          const fees = 25;
          const total = subtotal + taxes + fees;

          updateData.total_days = totalDays;
          updateData.subtotal = subtotal;
          updateData.taxes = taxes;
          updateData.total = total;
        }

        const { data: updated, error } = await this.supabase
          .from('bookings')
          .update(updateData)
          .eq('id', id)
          .select(`
            *,
            user:users!bookings_user_id_fkey(*),
            vehicle:vehicles!bookings_vehicle_id_fkey(*),
            provider:users!bookings_provider_id_fkey(*),
            pickup_location:locations!bookings_pickup_location_id_fkey(*),
            return_location:locations!bookings_return_location_id_fkey(*)
          `)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Booking', id);
          }
          throw error;
        }

        return this.mapSupabaseBooking(updated);
      }
    );
  }

  static async deleteBooking(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/bookings/${id}`),
      async () => {
        // Soft delete - update status instead of actual deletion
        const { error } = await this.supabase
          .from('bookings')
          .update({ 
            status: 'cancelled',
            cancelled_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Booking', id);
          }
          throw error;
        }
      }
    );
  }

  static async getBooking(id: string): Promise<RentaHubTypes.Booking> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.get(`/admin/bookings/${id}`),
      async () => {
        const { data, error } = await this.supabase
          .from('bookings')
          .select(`
            *,
            user:users!bookings_user_id_fkey(*),
            vehicle:vehicles!bookings_vehicle_id_fkey(*),
            provider:users!bookings_provider_id_fkey(*),
            pickup_location:locations!bookings_pickup_location_id_fkey(*),
            return_location:locations!bookings_return_location_id_fkey(*)
          `)
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Booking', id);
          }
          throw error;
        }

        return this.mapSupabaseBooking(data);
      }
    );
  }

  static async getBookings(
    page = 1,
    size = 10,
    options: FilterOptions & { filters?: RentaHubTypes.BookingFilters } = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Booking>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get('/admin/bookings', {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('bookings')
          .select(`
            *,
            user:users!bookings_user_id_fkey(*),
            vehicle:vehicles!bookings_vehicle_id_fkey(*),
            provider:users!bookings_provider_id_fkey(*),
            pickup_location:locations!bookings_pickup_location_id_fkey(*),
            return_location:locations!bookings_return_location_id_fkey(*)
          `, { count: 'exact' })
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`special_requests.ilike.%${options.search}%`);
        }

        // Apply filters
        const filters = options.filters;
        if (filters) {
          if (filters.status && filters.status.length > 0) {
            query = query.in('status', filters.status);
          }
          if (filters.providerId) {
            query = query.eq('provider_id', filters.providerId);
          }
          if (filters.userId) {
            query = query.eq('user_id', filters.userId);
          }
          if (filters.dateRange) {
            query = query
              .gte('start_date', filters.dateRange.start)
              .lte('end_date', filters.dateRange.end);
          }
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';
        
        // Map sort fields
        const sortField = sortBy === 'createdAt' ? 'created_at' : 
                         sortBy === 'startDate' ? 'start_date' : 
                         sortBy === 'total' ? 'total' : sortBy;
        
        query = query.order(sortField, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseBooking),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }

  // Provider-specific methods
  static async getProviderBookings(
    providerId: string,
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Booking>> {
    const providerOptions = {
      ...options,
      filters: { ...options.filters, providerId }
    };
    return this.getBookings(page, size, providerOptions);
  }

  // User-specific methods
  static async getUserBookings(
    userId: string,
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Booking>> {
    const userOptions = {
      ...options,
      filters: { ...options.filters, userId }
    };
    return this.getBookings(page, size, userOptions);
  }

  // Status management
  static async updateBookingStatus(id: string, status: RentaHubTypes.BookingStatus): Promise<RentaHubTypes.Booking> {
    return this.updateBooking(id, { status });
  }

  // Helper methods
  private static mapSupabaseBooking(data: any): RentaHubTypes.Booking {
    return {
      id: data.id,
      userId: data.user_id,
      user: data.user ? {
        id: data.user.id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role,
        status: data.user.status,
        emailVerified: data.user.email_verified || false,
        phoneVerified: data.user.phone_verified || false,
        createdAt: data.user.created_at,
      } : undefined,
      vehicleId: data.vehicle_id,
      vehicle: data.vehicle ? {
        id: data.vehicle.id,
        providerId: data.vehicle.provider_id,
        make: data.vehicle.make,
        model: data.vehicle.model,
        year: data.vehicle.year,
        type: data.vehicle.type,
        status: data.vehicle.status,
        licensePlate: data.vehicle.license_plate,
        fuelType: data.vehicle.fuel_type,
        gearbox: data.vehicle.gearbox,
        seats: data.vehicle.seats,
        doors: data.vehicle.doors,
        airConditioning: data.vehicle.air_conditioning || false,
        dailyRate: data.vehicle.daily_rate,
        deposit: data.vehicle.deposit,
        features: data.vehicle.features || [],
        images: data.vehicle.images || [],
        minimumRentalDays: data.vehicle.minimum_rental_days || 1,
        totalBookings: data.vehicle.total_bookings || 0,
        rating: data.vehicle.rating,
        reviewCount: data.vehicle.review_count || 0,
        createdAt: data.vehicle.created_at,
      } : undefined,
      providerId: data.provider_id,
      provider: data.provider ? {
        id: data.provider.id,
        email: data.provider.email,
        name: data.provider.name,
        role: data.provider.role,
        status: data.provider.status,
        emailVerified: data.provider.email_verified || false,
        phoneVerified: data.provider.phone_verified || false,
        createdAt: data.provider.created_at,
      } : undefined,
      startDate: data.start_date,
      endDate: data.end_date,
      pickupTime: data.pickup_time,
      returnTime: data.return_time,
      pickupLocationId: data.pickup_location_id,
      pickupLocation: data.pickup_location,
      returnLocationId: data.return_location_id,
      returnLocation: data.return_location,
      status: data.status,
      totalDays: data.total_days,
      dailyRate: data.daily_rate,
      subtotal: data.subtotal,
      taxes: data.taxes,
      fees: data.fees,
      total: data.total,
      deposit: data.deposit,
      paymentStatus: data.payment_status,
      paymentMethod: data.payment_method,
      paymentIntentId: data.payment_intent_id,
      additionalServices: data.additional_services || [],
      specialRequests: data.special_requests,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      confirmedAt: data.confirmed_at,
      completedAt: data.completed_at,
      cancelledAt: data.cancelled_at,
    };
  }
}

export default BookingService;
