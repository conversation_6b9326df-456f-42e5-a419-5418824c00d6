import { loadStripe, Stripe } from '@stripe/stripe-js';
import { adminSupabase as supabase } from './adminSupabaseClient';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_...');

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: string;
  booking_id?: string;
  customer_id: string;
  provider_id: string;
  created_at: string;
  metadata?: Record<string, any>;
}

export interface Payout {
  id: string;
  provider_id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  stripe_payout_id?: string;
  created_at: string;
  processed_at?: string;
}

export interface FinancialStats {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayouts: number;
  completedPayouts: number;
  averageTransactionValue: number;
  totalTransactions: number;
  stripeBalance: number;
  platformFees: number;
}

export class StripeService {
  private static stripe: Stripe | null = null;

  static async getStripe(): Promise<Stripe | null> {
    if (!this.stripe) {
      this.stripe = await stripePromise;
    }
    return this.stripe;
  }

  // Payment Management
  static async getPayments(page = 1, size = 10, options: {
    search?: string;
    status?: string;
    dateRange?: { start: string; end: string };
  } = {}) {
    try {
      const from = (page - 1) * size;
      const to = from + size - 1;

      let query = supabase
        .from('Payment')
        .select('*', { count: 'exact' })
        .range(from, to)
        .order('transactionDate', { ascending: false });

      // Apply filters
      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.dateRange) {
        query = query
          .gte('transactionDate', options.dateRange.start)
          .lte('transactionDate', options.dateRange.end);
      }

      if (options.search) {
        query = query.or(`stripe_payment_intent_id.ilike.%${options.search}%`);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: (data || []).map(payment => ({
          id: payment.id,
          amount: payment.total_amount,
          currency: payment.currency || 'USD',
          status: payment.status,
          booking_id: payment.booking_id,
          customer_id: payment.bookings?.users?.id,
          customer_name: `${payment.bookings?.users?.first_name || ''} ${payment.bookings?.users?.last_name || ''}`.trim(),
          customer_email: payment.bookings?.users?.email,
          provider_id: payment.provider_id,
          provider_name: payment.providers?.business_name || 
            `${payment.providers?.users?.first_name || ''} ${payment.providers?.users?.last_name || ''}`.trim(),
          stripe_payment_intent_id: payment.stripe_payment_intent_id,
          created_at: payment.created_at,
          metadata: payment.metadata
        })),
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  }

  static async getFinancialStats(): Promise<FinancialStats> {
    try {
      // Get total revenue from completed payments
      const { data: completedPayments, error: paymentsError } = await supabase
        .from('Payment')
        .select('amount, transactionDate')
        .eq('status', 'COMPLETED');

      if (paymentsError) throw paymentsError;

      const totalRevenue = (completedPayments || []).reduce((sum, payment) => sum + (payment.amount || 0), 0);
      const totalTransactions = completedPayments?.length || 0;
      const averageTransactionValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

      // Calculate monthly revenue (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const monthlyRevenue = (completedPayments || [])
        .filter(payment => new Date(payment.transactionDate) >= thirtyDaysAgo)
        .reduce((sum, payment) => sum + (payment.amount || 0), 0);

      // Get pending payouts
      const { data: pendingPayouts, error: payoutsError } = await supabase
        .from('payouts')
        .select('amount')
        .eq('status', 'pending');

      const pendingPayoutsAmount = (pendingPayouts || []).reduce((sum, payout) => sum + (payout.amount || 0), 0);

      // Get completed payouts
      const { data: completedPayoutsData, error: completedPayoutsError } = await supabase
        .from('payouts')
        .select('amount')
        .eq('status', 'completed');

      const completedPayoutsAmount = (completedPayoutsData || []).reduce((sum, payout) => sum + (payout.amount || 0), 0);

      // Calculate platform fees (assuming 5% platform fee)
      const platformFees = totalRevenue * 0.05;

      return {
        totalRevenue,
        monthlyRevenue,
        pendingPayouts: pendingPayoutsAmount,
        completedPayouts: completedPayoutsAmount,
        averageTransactionValue,
        totalTransactions,
        stripeBalance: totalRevenue - completedPayoutsAmount - platformFees, // Simplified calculation
        platformFees
      };
    } catch (error) {
      console.error('Error fetching financial stats:', error);
      return {
        totalRevenue: 0,
        monthlyRevenue: 0,
        pendingPayouts: 0,
        completedPayouts: 0,
        averageTransactionValue: 0,
        totalTransactions: 0,
        stripeBalance: 0,
        platformFees: 0
      };
    }
  }

  // Payout Management
  static async getPayouts(page = 1, size = 10, options: {
    status?: string;
    providerId?: string;
  } = {}) {
    try {
      const from = (page - 1) * size;
      const to = from + size - 1;

      let query = supabase
        .from('payout_requests')
        .select(`
          *,
          provider:User!inner(firstName, lastName, email)
        `, { count: 'exact' })
        .range(from, to)
        .order('createdAt', { ascending: false });

      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.providerId) {
        query = query.eq('providerId', options.providerId);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: (data || []).map(payout => ({
          id: payout.id,
          provider_id: payout.provider_id,
          provider_name: payout.providers?.business_name || 
            `${payout.providers?.users?.first_name || ''} ${payout.providers?.users?.last_name || ''}`.trim(),
          provider_email: payout.providers?.users?.email,
          amount: payout.amount,
          currency: payout.currency || 'USD',
          status: payout.status,
          stripe_payout_id: payout.stripe_payout_id,
          created_at: payout.created_at,
          processed_at: payout.processed_at
        })),
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error fetching payouts:', error);
      throw error;
    }
  }

  static async processPayout(providerId: string, amount: number): Promise<void> {
    try {
      // Create payout record in database
      const { data: payout, error: payoutError } = await supabase
        .from('payout_requests')
        .insert({
          providerId: providerId,
          amount,
          method: 'stripe',
          status: 'processing',
          createdAt: new Date().toISOString()
        })
        .select()
        .single();

      if (payoutError) throw payoutError;

      // In a real implementation, you would:
      // 1. Create a Stripe payout using their API
      // 2. Update the payout record with the Stripe payout ID
      // 3. Handle webhooks for payout status updates

      // For now, simulate processing
      setTimeout(async () => {
        await supabase
          .from('payout_requests')
          .update({
            status: 'completed'
          })
          .eq('id', payout.id);
      }, 2000);

    } catch (error) {
      console.error('Error processing payout:', error);
      throw error;
    }
  }

  static async refundPayment(paymentId: string, amount?: number): Promise<void> {
    try {
      // Get payment details
      const { data: payment, error: paymentError } = await supabase
        .from('Payment')
        .select('*')
        .eq('id', paymentId)
        .single();

      if (paymentError) throw paymentError;

      // In a real implementation, you would:
      // 1. Create a Stripe refund using their API
      // 2. Update the payment record with refund information

      // For now, simulate refund
      await supabase
        .from('Payment')
        .update({
          status: 'REFUNDED',
          amount: amount || payment.amount,
          transactionDate: new Date().toISOString()
        })
        .eq('id', paymentId);

    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  // Analytics
  static async getRevenueAnalytics(period: 'week' | 'month' | 'year' = 'month') {
    try {
      const now = new Date();
      let startDate = new Date();

      switch (period) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      const { data: payments, error } = await supabase
        .from('Payment')
        .select('amount, transactionDate')
        .eq('status', 'COMPLETED')
        .gte('transactionDate', startDate.toISOString())
        .order('transactionDate', { ascending: true });

      if (error) throw error;

      // Group payments by date
      const revenueByDate = (payments || []).reduce((acc, payment) => {
        const date = new Date(payment.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + payment.total_amount;
        return acc;
      }, {} as Record<string, number>);

      return Object.entries(revenueByDate).map(([date, revenue]) => ({
        date,
        revenue
      }));
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      return [];
    }
  }
}

export default StripeService;
