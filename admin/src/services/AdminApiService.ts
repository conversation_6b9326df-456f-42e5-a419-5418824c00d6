import axios, { AxiosResponse } from 'axios';
import { SupabaseDashboardService } from './SupabaseDashboardService';
import { adminSupabase as supabase } from './adminSupabaseClient';

const API_BASE_URL = 'http://localhost:3001/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

export interface DashboardStats {
  users: {
    total: number;
    recent: Array<{
      id: string;
      name: string;
      email: string;
      date: string;
      role?: string;
      status?: string;
    }>;
    growth: { total: number; period: string };
  };
  vehicles: {
    total: number;
    available: number;
    recent: Array<{
      id: string;
      type: string;
      provider: string;
      status: string;
      make?: string;
      model?: string;
    }>;
  };
  bookings: {
    total: number;
    active: number;
    recent: Array<{
      id: string;
      user: string;
      vehicle: string;
      status: string;
      startDate?: string;
      endDate?: string;
    }>;
  };
  financials: {
    totalRevenue: number;
    monthlyRevenue: number;
    pendingPayments: number;
  };
}

export interface User {
  id: string;
  email: string;
  name?: string;
  role: string;
  status: string;
  createdAt: string;
  lastLogin?: string;
  phoneNumber?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  status: string;
  location: string;
  pricePerDay: number;
  providerId: string;
  providerName: string;
  createdAt: string;
}

export interface Booking {
  id: string;
  vehicleId: string;
  userId: string;
  startDate: string;
  endDate: string;
  status: string;
  totalAmount: number;
  createdAt: string;
}

export class AdminApiService {
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Try to get data from API first
      const response: AxiosResponse<DashboardStats> = await apiClient.get('/admin/dashboard/stats');
      return response.data;
    } catch (error) {
      console.error('API failed, falling back to Supabase:', error);

      try {
        // Fallback to Supabase
        return await SupabaseDashboardService.getDashboardStats();
      } catch (supabaseError) {
        console.error('Supabase also failed:', supabaseError);

        // Final fallback to empty data
        return {
          users: {
            total: 0,
            recent: [],
            growth: { total: 0, period: 'month' }
          },
          vehicles: {
            total: 0,
            available: 0,
            recent: []
          },
          bookings: {
            total: 0,
            active: 0,
            recent: []
          },
          financials: {
            totalRevenue: 0,
            monthlyRevenue: 0,
            pendingPayments: 0
          }
        };
      }
    }
  }

  static async getUsers(page = 1, limit = 10, search = ''): Promise<{ users: User[]; total: number }> {
    try {
      const response = await apiClient.get('/admin/users', {
        params: { page, limit, search }
      });
      return response.data;
    } catch (error) {
      console.error('API failed, falling back to Supabase:', error);

      try {
        // Fallback to Supabase
        return await SupabaseDashboardService.getUsers(page, limit, search);
      } catch (supabaseError) {
        console.error('Supabase also failed:', supabaseError);
        return { users: [], total: 0 };
      }
    }
  }

  static async updateUserStatus(userId: string, status: string): Promise<User> {
    const response = await apiClient.patch(`/admin/users/${userId}/status`, { status });
    return response.data;
  }

  static async deleteUser(userId: string): Promise<void> {
    await apiClient.delete(`/admin/users/${userId}`);
  }

  static async getVehicles(page = 1, limit = 10, filters = {}): Promise<{ vehicles: Vehicle[]; total: number }> {
    try {
      const response = await apiClient.get('/admin/vehicles', {
        params: { page, limit, ...filters }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch vehicles:', error);
      return {
        vehicles: [
          {
            id: '1',
            make: 'Honda',
            model: 'Civic',
            year: 2023,
            licensePlate: 'ABC123',
            status: 'AVAILABLE',
            location: 'New York',
            pricePerDay: 50,
            providerId: 'provider1',
            providerName: 'John Doe',
            createdAt: '2024-01-15T10:00:00Z'
          }
        ],
        total: 1
      };
    }
  }

  static async updateVehicleStatus(vehicleId: string, status: string): Promise<Vehicle> {
    const response = await apiClient.patch(`/admin/vehicles/${vehicleId}/status`, { status });
    return response.data;
  }

  static async deleteVehicle(vehicleId: string): Promise<void> {
    await apiClient.delete(`/admin/vehicles/${vehicleId}`);
  }

  static async getBookings(page = 1, limit = 10, filters = {}): Promise<{ bookings: Booking[]; total: number }> {
    try {
      // Get live booking data from Supabase
      const { data: bookings, error, count } = await supabase
        .from('Booking')
        .select('*', { count: 'exact' })
        .range((page - 1) * limit, page * limit - 1)
        .order('id', { ascending: false });

      if (error) {
        console.error('Error fetching bookings:', error);
        return { bookings: [], total: 0 };
      }

      return {
        bookings: bookings || [],
        total: count || 0
      };
    } catch (error) {
      console.error('Failed to fetch bookings:', error);
      return { bookings: [], total: 0 };
    }
  }

  static async updateBookingStatus(bookingId: string, status: string): Promise<Booking> {
    const response = await apiClient.patch(`/admin/bookings/${bookingId}/status`, { status });
    return response.data;
  }

  static async cancelBooking(bookingId: string): Promise<void> {
    await apiClient.patch(`/admin/bookings/${bookingId}/cancel`);
  }

  static async getSupportTickets(page = 1, limit = 10, filters = {}): Promise<{ tickets: any[]; total: number }> {
    try {
      // Use Supabase directly instead of backend API
      const { adminSupabase } = await import('./adminSupabaseClient');

      const from = (page - 1) * limit;
      const to = from + limit - 1;

      let query = adminSupabase
        .from('support_tickets')
        .select('*', { count: 'exact' })
        .range(from, to)
        .order('createdAt', { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority);
      }

      const { data: tickets, error, count } = await query;

      if (error) {
        console.error('Supabase support tickets error:', error);
        return { tickets: [], total: 0 };
      }

      return {
        tickets: tickets || [],
        total: count || 0
      };

    } catch (error) {
      console.error('Failed to fetch support tickets:', error);
      return { tickets: [], total: 0 };
    }
  }

  static async updateTicketStatus(ticketId: string, status: string): Promise<any> {
    const response = await apiClient.patch(`/admin/support-tickets/${ticketId}/status`, { status });
    return response.data;
  }

  static async getFinancialReports(period: string): Promise<any> {
    try {
      const response = await apiClient.get('/admin/financial-reports', {
        params: { period }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch financial reports:', error);
      return {};
    }
  }

  static async getAnalytics(period: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<any> {
    try {
      const response = await apiClient.get('/admin/analytics', {
        params: { period }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      return {
        revenue: { total: 125000, growth: 22.1 },
        bookings: { total: 2840, growth: 15.2 },
        users: { total: 1250, growth: 12.5 },
        vehicles: { total: 450, growth: 8.3 }
      };
    }
  }
}
