import { BaseService, PaginatedResponse, FilterOptions, ValidationError, NotFoundError } from './BaseService';
import * as RentaHubTypes from '../types/rentahub-types';

export class VehicleService extends BaseService {

  // CRUD Operations
  static async createVehicle(data: RentaHubTypes.CreateVehiclePayload): Promise<RentaHubTypes.Vehicle> {
    this.validateRequired(data, ['providerId', 'make', 'model', 'year', 'type', 'licensePlate', 'dailyRate']);

    return this.apiCall(
      () => this.apiClient.post('/admin/vehicles', data),
      async () => {
        const vehicleData = {
          provider_id: data.providerId,
          make: data.make,
          model: data.model,
          year: data.year,
          type: data.type,
          license_plate: data.licensePlate,
          fuel_type: data.fuelType,
          gearbox: data.gearbox,
          seats: data.seats,
          doors: data.doors,
          air_conditioning: data.airConditioning,
          daily_rate: data.dailyRate,
          deposit: data.deposit,
          features: data.features,
          description: data.description,
          location_id: data.locationId,
          minimum_rental_days: data.minimumRentalDays,
          status: 'available',
          total_bookings: 0,
          rating: 0,
          review_count: 0,
          created_at: new Date().toISOString(),
        };

        const { data: created, error } = await this.supabase
          .from('vehicles')
          .insert(vehicleData)
          .select(`
            *,
            provider:users!vehicles_provider_id_fkey(*),
            location:locations(*)
          `)
          .single();

        if (error) throw error;
        return this.mapSupabaseVehicle(created);
      }
    );
  }

  static async updateVehicle(id: string, data: RentaHubTypes.UpdateVehiclePayload): Promise<RentaHubTypes.Vehicle> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.put(`/admin/vehicles/${id}`, data),
      async () => {
        const updateData: any = {
          updated_at: new Date().toISOString(),
        };

        // Map fields
        if (data.make) updateData.make = data.make;
        if (data.model) updateData.model = data.model;
        if (data.year) updateData.year = data.year;
        if (data.type) updateData.type = data.type;
        if (data.status) updateData.status = data.status;
        if (data.dailyRate) updateData.daily_rate = data.dailyRate;
        if (data.deposit) updateData.deposit = data.deposit;
        if (data.features) updateData.features = data.features;
        if (data.description !== undefined) updateData.description = data.description;
        if (data.airConditioning !== undefined) updateData.air_conditioning = data.airConditioning;
        if (data.minimumRentalDays) updateData.minimum_rental_days = data.minimumRentalDays;

        const { data: updated, error } = await this.supabase
          .from('vehicles')
          .update(updateData)
          .eq('id', id)
          .select(`
            *,
            provider:users!vehicles_provider_id_fkey(*),
            location:locations(*)
          `)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Vehicle', id);
          }
          throw error;
        }

        return this.mapSupabaseVehicle(updated);
      }
    );
  }

  static async deleteVehicle(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/vehicles/${id}`),
      async () => {
        // Soft delete - update status instead of actual deletion
        const { error } = await this.supabase
          .from('vehicles')
          .update({
            status: 'inactive',
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Vehicle', id);
          }
          throw error;
        }
      }
    );
  }

  static async getVehicle(id: string): Promise<RentaHubTypes.Vehicle> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.get(`/admin/vehicles/${id}`),
      async () => {
        const { data, error } = await this.supabase
          .from('vehicles')
          .select(`
            *,
            provider:users!vehicles_provider_id_fkey(*),
            location:locations(*)
          `)
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Vehicle', id);
          }
          throw error;
        }

        return this.mapSupabaseVehicle(data);
      }
    );
  }

  static async getVehicles(
    page = 1,
    size = 10,
    options: FilterOptions & { filters?: RentaHubTypes.VehicleFilters } = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Vehicle>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get('/admin/vehicles', {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('vehicles')
          .select(`
            *,
            provider:users!vehicles_provider_id_fkey(*),
            location:locations(*)
          `, { count: 'exact' })
          .neq('status', 'inactive')
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`make.ilike.%${options.search}%,model.ilike.%${options.search}%,license_plate.ilike.%${options.search}%`);
        }

        // Apply filters
        const filters = options.filters;
        if (filters) {
          if (filters.type && filters.type.length > 0) {
            query = query.in('type', filters.type);
          }
          if (filters.fuelType && filters.fuelType.length > 0) {
            query = query.in('fuel_type', filters.fuelType);
          }
          if (filters.gearbox && filters.gearbox.length > 0) {
            query = query.in('gearbox', filters.gearbox);
          }
          if (filters.minPrice) {
            query = query.gte('daily_rate', filters.minPrice);
          }
          if (filters.maxPrice) {
            query = query.lte('daily_rate', filters.maxPrice);
          }
          if (filters.seats) {
            query = query.eq('seats', filters.seats);
          }
          if (filters.location) {
            query = query.eq('location_id', filters.location);
          }
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';

        // Map sort fields
        const sortField = sortBy === 'createdAt' ? 'created_at' :
                         sortBy === 'dailyRate' ? 'daily_rate' : sortBy;

        query = query.order(sortField, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseVehicle),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }
  // Provider-specific methods
  static async getProviderVehicles(
    providerId: string,
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Vehicle>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get(`/providers/${providerId}/vehicles`, {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('vehicles')
          .select(`
            *,
            provider:users!vehicles_provider_id_fkey(*),
            location:locations(*)
          `, { count: 'exact' })
          .eq('provider_id', providerId)
          .neq('status', 'inactive')
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`make.ilike.%${options.search}%,model.ilike.%${options.search}%,license_plate.ilike.%${options.search}%`);
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';
        const sortField = sortBy === 'createdAt' ? 'created_at' :
                         sortBy === 'dailyRate' ? 'daily_rate' : sortBy;

        query = query.order(sortField, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseVehicle),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }

  // Availability methods
  static async checkAvailability(
    vehicleId: string,
    startDate: string,
    endDate: string
  ): Promise<boolean> {
    this.validateRequired({ vehicleId, startDate, endDate }, ['vehicleId', 'startDate', 'endDate']);

    return this.apiCall(
      () => this.apiClient.get(`/vehicles/${vehicleId}/availability`, {
        params: { startDate, endDate }
      }),
      async () => {
        // Check for conflicting bookings
        const { data, error } = await this.supabase
          .from('bookings')
          .select('id')
          .eq('vehicle_id', vehicleId)
          .in('status', ['confirmed', 'active'])
          .or(`and(start_date.lte.${endDate},end_date.gte.${startDate})`);

        if (error) throw error;

        return (data || []).length === 0;
      },
      false
    );
  }

  // Helper methods
  private static mapSupabaseVehicle(data: any): RentaHubTypes.Vehicle {
    return {
      id: data.id,
      providerId: data.provider_id,
      provider: data.provider ? {
        id: data.provider.id,
        email: data.provider.email,
        name: data.provider.name,
        role: data.provider.role,
        status: data.provider.status,
        emailVerified: data.provider.email_verified || false,
        phoneVerified: data.provider.phone_verified || false,
        createdAt: data.provider.created_at,
      } : undefined,
      make: data.make,
      model: data.model,
      year: data.year,
      type: data.type,
      category: data.category,
      status: data.status,
      licensePlate: data.license_plate,
      vin: data.vin,
      color: data.color,
      fuelType: data.fuel_type,
      gearbox: data.gearbox,
      seats: data.seats,
      doors: data.doors,
      airConditioning: data.air_conditioning || false,
      dailyRate: data.daily_rate,
      weeklyRate: data.weekly_rate,
      monthlyRate: data.monthly_rate,
      deposit: data.deposit,
      features: data.features || [],
      images: data.images || [],
      description: data.description,
      locationId: data.location_id,
      location: data.location ? {
        id: data.location.id,
        name: data.location.name,
        address: data.location.address,
        city: data.location.city,
        state: data.location.state,
        country: data.location.country,
        postalCode: data.location.postal_code,
        latitude: data.location.latitude,
        longitude: data.location.longitude,
        isActive: data.location.is_active,
        createdAt: data.location.created_at,
      } : undefined,
      availableFrom: data.available_from,
      availableTo: data.available_to,
      minimumRentalDays: data.minimum_rental_days || 1,
      maximumRentalDays: data.maximum_rental_days,
      totalBookings: data.total_bookings || 0,
      rating: data.rating,
      reviewCount: data.review_count || 0,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  }
}

export default VehicleService;