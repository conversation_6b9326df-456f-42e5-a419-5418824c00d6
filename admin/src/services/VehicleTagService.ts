import axios from 'axios';
import { PREDEFINED_SMART_TAGS } from '../models/VehicleForm';

export class VehicleTagService {
  // Get predefined tags
  static async getPredefinedTags(): Promise<string[]> {
    try {
      const response = await axios.get('/api/vehicles/tags');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch predefined tags', error);
      return PREDEFINED_SMART_TAGS; // Fallback to local tags
    }
  }

  // Add tags to a vehicle
  static async addTagsToVehicle(vehicleId: string, tags: string[]): Promise<void> {
    try {
      await axios.post(`/api/vehicles/${vehicleId}/tags`, { tags });
    } catch (error) {
      console.error('Failed to add tags to vehicle', error);
      throw error;
    }
  }

  // Filter vehicles by tags
  static async filterVehiclesByTags(tags: string[]): Promise<any[]> {
    try {
      const response = await axios.get('/api/vehicles/filter', {
        params: { tags }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to filter vehicles by tags', error);
      throw error;
    }
  }

  // Remove tags from a vehicle
  static async removeTagsFromVehicle(vehicleId: string): Promise<void> {
    try {
      await axios.delete(`/api/vehicles/${vehicleId}/tags`);
    } catch (error) {
      console.error('Failed to remove tags from vehicle', error);
      throw error;
    }
  }
}

export default VehicleTagService;
