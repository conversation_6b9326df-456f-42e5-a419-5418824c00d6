import { BaseService, PaginatedResponse, FilterOptions, ValidationError, NotFoundError } from './BaseService';
import * as RentaHubTypes from '../types/rentahub-types';

export class LocationService extends BaseService {
  
  // CRUD Operations
  static async createLocation(data: {
    name: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    latitude?: number;
    longitude?: number;
  }): Promise<RentaHubTypes.Location> {
    this.validateRequired(data, ['name', 'address', 'city', 'state', 'country', 'postalCode']);

    return this.apiCall(
      () => this.apiClient.post('/admin/locations', data),
      async () => {
        const locationData = {
          name: data.name,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country,
          postal_code: data.postalCode,
          latitude: data.latitude,
          longitude: data.longitude,
          is_active: true,
          created_at: new Date().toISOString(),
        };

        const { data: created, error } = await this.supabase
          .from('locations')
          .insert(locationData)
          .select()
          .single();

        if (error) throw error;
        return this.mapSupabaseLocation(created);
      }
    );
  }

  static async updateLocation(id: string, data: {
    name?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
    latitude?: number;
    longitude?: number;
    isActive?: boolean;
  }): Promise<RentaHubTypes.Location> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.put(`/admin/locations/${id}`, data),
      async () => {
        const updateData: any = {
          updated_at: new Date().toISOString(),
        };

        // Map fields
        if (data.name) updateData.name = data.name;
        if (data.address) updateData.address = data.address;
        if (data.city) updateData.city = data.city;
        if (data.state) updateData.state = data.state;
        if (data.country) updateData.country = data.country;
        if (data.postalCode) updateData.postal_code = data.postalCode;
        if (data.latitude !== undefined) updateData.latitude = data.latitude;
        if (data.longitude !== undefined) updateData.longitude = data.longitude;
        if (data.isActive !== undefined) updateData.is_active = data.isActive;

        const { data: updated, error } = await this.supabase
          .from('locations')
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Location', id);
          }
          throw error;
        }

        return this.mapSupabaseLocation(updated);
      }
    );
  }

  static async deleteLocation(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/locations/${id}`),
      async () => {
        // Soft delete - update status instead of actual deletion
        const { error } = await this.supabase
          .from('locations')
          .update({ 
            is_active: false,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Location', id);
          }
          throw error;
        }
      }
    );
  }

  static async getLocation(id: string): Promise<RentaHubTypes.Location> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.get(`/admin/locations/${id}`),
      async () => {
        const { data, error } = await this.supabase
          .from('locations')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('Location', id);
          }
          throw error;
        }

        return this.mapSupabaseLocation(data);
      }
    );
  }

  static async getLocations(
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.Location>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get('/admin/locations', {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('locations')
          .select('*', { count: 'exact' })
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`name.ilike.%${options.search}%,address.ilike.%${options.search}%,city.ilike.%${options.search}%`);
        }

        // Apply filters
        if (options.filters?.isActive !== undefined) {
          query = query.eq('is_active', options.filters.isActive);
        }
        if (options.filters?.city) {
          query = query.eq('city', options.filters.city);
        }
        if (options.filters?.state) {
          query = query.eq('state', options.filters.state);
        }
        if (options.filters?.country) {
          query = query.eq('country', options.filters.country);
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';
        
        // Map sort fields
        const sortField = sortBy === 'createdAt' ? 'created_at' : 
                         sortBy === 'isActive' ? 'is_active' : sortBy;
        
        query = query.order(sortField, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseLocation),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }

  // Get active locations for dropdowns
  static async getActiveLocations(): Promise<RentaHubTypes.Location[]> {
    return this.apiCall(
      () => this.apiClient.get('/locations/active'),
      async () => {
        const { data, error } = await this.supabase
          .from('locations')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (error) throw error;

        return (data || []).map(this.mapSupabaseLocation);
      },
      []
    );
  }

  // Search locations by query
  static async searchLocations(query: string, limit = 10): Promise<RentaHubTypes.Location[]> {
    if (!query.trim()) return [];

    return this.apiCall(
      () => this.apiClient.get('/locations/search', {
        params: { q: query, limit }
      }),
      async () => {
        const { data, error } = await this.supabase
          .from('locations')
          .select('*')
          .eq('is_active', true)
          .or(`name.ilike.%${query}%,address.ilike.%${query}%,city.ilike.%${query}%`)
          .limit(limit)
          .order('name');

        if (error) throw error;

        return (data || []).map(this.mapSupabaseLocation);
      },
      []
    );
  }

  // Get locations by city
  static async getLocationsByCity(city: string): Promise<RentaHubTypes.Location[]> {
    this.validateRequired({ city }, ['city']);

    return this.apiCall(
      () => this.apiClient.get(`/locations/city/${city}`),
      async () => {
        const { data, error } = await this.supabase
          .from('locations')
          .select('*')
          .eq('city', city)
          .eq('is_active', true)
          .order('name');

        if (error) throw error;

        return (data || []).map(this.mapSupabaseLocation);
      },
      []
    );
  }

  // Get locations within radius (if coordinates are provided)
  static async getLocationsNearby(
    latitude: number,
    longitude: number,
    radiusKm = 50
  ): Promise<RentaHubTypes.Location[]> {
    this.validateRequired({ latitude, longitude }, ['latitude', 'longitude']);

    return this.apiCall(
      () => this.apiClient.get('/locations/nearby', {
        params: { lat: latitude, lng: longitude, radius: radiusKm }
      }),
      async () => {
        // For Supabase, we'll use a simple bounding box approach
        // In production, you might want to use PostGIS for more accurate distance calculations
        const latDelta = radiusKm / 111; // Rough conversion: 1 degree ≈ 111 km
        const lngDelta = radiusKm / (111 * Math.cos(latitude * Math.PI / 180));

        const { data, error } = await this.supabase
          .from('locations')
          .select('*')
          .eq('is_active', true)
          .gte('latitude', latitude - latDelta)
          .lte('latitude', latitude + latDelta)
          .gte('longitude', longitude - lngDelta)
          .lte('longitude', longitude + lngDelta)
          .not('latitude', 'is', null)
          .not('longitude', 'is', null)
          .order('name');

        if (error) throw error;

        return (data || []).map(this.mapSupabaseLocation);
      },
      []
    );
  }

  // Validate location name uniqueness
  static async validateLocationName(name: string, excludeId?: string): Promise<boolean> {
    this.validateRequired({ name }, ['name']);

    return this.apiCall(
      () => this.apiClient.post('/locations/validate-name', { name, excludeId }),
      async () => {
        let query = this.supabase
          .from('locations')
          .select('id')
          .eq('name', name);

        if (excludeId) {
          query = query.neq('id', excludeId);
        }

        const { data, error } = await query;

        if (error) throw error;

        return (data || []).length === 0; // true if name is available
      },
      true
    );
  }

  // Helper methods
  private static mapSupabaseLocation(data: any): RentaHubTypes.Location {
    return {
      id: data.id,
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      country: data.country,
      postalCode: data.postal_code,
      latitude: data.latitude,
      longitude: data.longitude,
      isActive: data.is_active,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  }
}

export default LocationService;
