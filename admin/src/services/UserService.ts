import { BaseService, ApiResponse, PaginatedResponse, FilterOptions, ValidationError, NotFoundError } from './BaseService';
import * as RentaHubTypes from '../types/rentahub-types';

export class UserService extends BaseService {
  private static readonly STORAGE_KEY = 'rentahub_user';
  private static readonly TOKEN_KEY = 'rentahub_token';

  // Authentication methods
  static async signIn(credentials: RentaHubTypes.SignInPayload): Promise<RentaHubTypes.AuthResponse> {
    this.validateRequired(credentials, ['email', 'password']);

    return this.apiCall(
      () => this.apiClient.post('/auth/signin', credentials),
      async () => {
        // Supabase auth fallback
        const { data, error } = await this.supabase.auth.signInWithPassword({
          email: credentials.email,
          password: credentials.password,
        });

        if (error) throw error;
        if (!data.user) throw new Error('Authentication failed');

        // Get user profile
        const { data: profile, error: profileError } = await this.supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError) throw profileError;

        const authResponse: RentaHubTypes.AuthResponse = {
          user: {
            id: data.user.id,
            email: data.user.email!,
            name: profile.name,
            role: profile.role,
            status: profile.status,
            emailVerified: data.user.email_confirmed_at !== null,
            phoneVerified: profile.phone_verified || false,
            createdAt: data.user.created_at,
            lastLogin: new Date().toISOString(),
          },
          token: data.session?.access_token || '',
          refreshToken: data.session?.refresh_token || '',
        };

        // Store auth data
        this.setCurrentUser(authResponse.user);
        this.setAccessToken(authResponse.token);

        return authResponse;
      }
    );
  }

  static async signUp(data: RentaHubTypes.SignUpPayload): Promise<number> {
    this.validateRequired(data, ['email', 'password', 'name']);

    return this.apiCall(
      () => this.apiClient.post('/auth/signup', data),
      async () => {
        // Supabase auth fallback
        const { data: authData, error } = await this.supabase.auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            data: {
              name: data.name,
              role: data.role || 'customer',
            },
          },
        });

        if (error) throw error;
        return 201; // Created status
      },
      201
    );
  }

  static async signOut(redirect = true): Promise<void> {
    try {
      // Clear local storage
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.TOKEN_KEY);

      // Sign out from Supabase
      await this.supabase.auth.signOut();

      // API signout (if available)
      try {
        await this.apiClient.post('/auth/signout');
      } catch (error) {
        console.warn('API signout failed:', error);
      }

      if (redirect) {
        window.location.href = '/sign-in';
      }
    } catch (error) {
      console.error('Signout error:', error);
      if (redirect) {
        window.location.href = '/sign-in';
      }
    }
  }

  // User management methods
  static async createUser(data: RentaHubTypes.CreateUserPayload): Promise<RentaHubTypes.User> {
    this.validateRequired(data, ['email', 'name', 'role']);

    return this.apiCall(
      () => this.apiClient.post('/admin/users', data),
      async () => {
        // Create auth user first
        const { data: authData, error: authError } = await this.supabase.auth.admin.createUser({
          email: data.email,
          password: data.password || this.generateTempPassword(),
          email_confirm: true,
          user_metadata: {
            name: data.name,
            role: data.role,
          },
        });

        if (authError) throw authError;

        // Create user profile
        const { data: profile, error: profileError } = await this.supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: data.email,
            name: data.name,
            role: data.role,
            status: 'active',
            phone_number: data.phoneNumber || '',
            email_verified: true,
            phone_verified: false,
            created_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (profileError) throw profileError;

        return this.mapSupabaseUser(profile);
      }
    );
  }

  static async updateUser(id: string, data: RentaHubTypes.UpdateUserPayload): Promise<RentaHubTypes.User> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.put(`/admin/users/${id}`, data),
      async () => {
        const { data: updated, error } = await this.supabase
          .from('users')
          .update({
            name: data.name,
            phone_number: data.phoneNumber,
            status: data.status,
            role: data.role,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id)
          .select()
          .single();

        if (error) throw error;
        return this.mapSupabaseUser(updated);
      }
    );
  }

  static async deleteUser(id: string): Promise<void> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/users/${id}`),
      async () => {
        // Soft delete - update status instead of actual deletion
        const { error } = await this.supabase
          .from('users')
          .update({ 
            status: 'deleted',
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (error) throw error;
      }
    );
  }

  static async getUser(id: string): Promise<RentaHubTypes.User> {
    this.validateRequired({ id }, ['id']);

    return this.apiCall(
      () => this.apiClient.get(`/admin/users/${id}`),
      async () => {
        const { data, error } = await this.supabase
          .from('User')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('User', id);
          }
          throw error;
        }

        return this.mapSupabaseUser(data);
      }
    );
  }

  static async getUsers(
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.User>> {
    const { from, to } = this.getPaginationParams(page, size);

    return this.apiCall(
      () => this.apiClient.get('/admin/users', {
        params: { page, size, ...options }
      }),
      async () => {
        let query = this.supabase
          .from('users')
          .select('*', { count: 'exact' })
          .neq('status', 'deleted')
          .range(from, to);

        // Apply search
        if (options.search) {
          query = query.or(`name.ilike.%${options.search}%,email.ilike.%${options.search}%`);
        }

        // Apply filters
        if (options.filters?.role) {
          query = query.eq('role', options.filters.role);
        }
        if (options.filters?.status) {
          query = query.eq('status', options.filters.status);
        }

        // Apply sorting
        const sortBy = options.sortBy || 'created_at';
        const sortOrder = options.sortOrder || 'desc';
        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          data: (data || []).map(this.mapSupabaseUser),
          total: count || 0,
          page,
          size,
          totalPages: Math.ceil((count || 0) / size),
        };
      }
    );
  }

  // Token and session management
  static getCurrentUser(): RentaHubTypes.User | null {
    try {
      const userData = localStorage.getItem(this.STORAGE_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  }

  static setCurrentUser(user: RentaHubTypes.User): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user));
  }

  static getAccessToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setAccessToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static async validateAccessToken(): Promise<number> {
    const token = this.getAccessToken();
    if (!token) return 401;

    try {
      const response = await this.apiClient.get('/auth/validate');
      return response.status;
    } catch (error: any) {
      return error.response?.status || 401;
    }
  }

  // Helper methods
  private static generateTempPassword(): string {
    return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
  }

  private static mapSupabaseUser(data: any): RentaHubTypes.User {
    return {
      id: data.id,
      email: data.email,
      name: data.name,
      role: data.role,
      status: data.status,
      phoneNumber: data.phone_number,
      emailVerified: data.email_verified || false,
      phoneVerified: data.phone_verified || false,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      lastLogin: data.last_login,
    };
  }

  // Provider-specific methods
  static async getProviders(
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.User>> {
    const providerOptions = {
      ...options,
      filters: { ...options.filters, role: 'provider' }
    };
    return this.getUsers(page, size, providerOptions);
  }

  static async updateProviderStatus(id: string, status: string): Promise<RentaHubTypes.User> {
    return this.updateUser(id, { status });
  }

  // Customer-specific methods
  static async getCustomers(
    page = 1,
    size = 10,
    options: FilterOptions = {}
  ): Promise<PaginatedResponse<RentaHubTypes.User>> {
    const customerOptions = {
      ...options,
      filters: { ...options.filters, role: 'customer' }
    };
    return this.getUsers(page, size, customerOptions);
  }

  // Password reset methods
  static async forgotPassword(email: string): Promise<void> {
    this.validateRequired({ email }, ['email']);

    return this.apiCall(
      () => this.apiClient.post('/auth/forgot-password', { email }),
      async () => {
        const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/reset-password`,
        });
        if (error) throw error;
      }
    );
  }

  static async resetPassword(token: string, password: string): Promise<void> {
    this.validateRequired({ token, password }, ['token', 'password']);

    return this.apiCall(
      () => this.apiClient.post('/auth/reset-password', { token, password }),
      async () => {
        const { error } = await this.supabase.auth.updateUser({
          password: password,
        });
        if (error) throw error;
      }
    );
  }

  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    this.validateRequired({ currentPassword, newPassword }, ['currentPassword', 'newPassword']);

    return this.apiCall(
      () => this.apiClient.post('/auth/change-password', { currentPassword, newPassword }),
      async () => {
        const { error } = await this.supabase.auth.updateUser({
          password: newPassword,
        });
        if (error) throw error;
      }
    );
  }

  // Email verification
  static async resendVerificationEmail(email: string): Promise<void> {
    this.validateRequired({ email }, ['email']);

    return this.apiCall(
      () => this.apiClient.post('/auth/resend-verification', { email }),
      async () => {
        const { error } = await this.supabase.auth.resend({
          type: 'signup',
          email: email,
        });
        if (error) throw error;
      }
    );
  }

  static async verifyEmail(token: string): Promise<void> {
    this.validateRequired({ token }, ['token']);

    return this.apiCall(
      () => this.apiClient.post('/auth/verify-email', { token }),
      async () => {
        const { error } = await this.supabase.auth.verifyOtp({
          token_hash: token,
          type: 'email',
        });
        if (error) throw error;
      }
    );
  }

  // Admin methods for user management
  static async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    newUsersThisMonth: number;
    verifiedUsers: number;
    providers: number;
    customers: number;
    suspendedUsers: number;
  }> {
    return this.apiCall(
      () => this.apiClient.get('/admin/users/stats'),
      async () => {
        // Get total users
        const { count: totalUsers, error: totalError } = await this.supabase
          .from('User')
          .select('*', { count: 'exact', head: true })
          .neq('status', 'DELETED');

        if (totalError) throw totalError;

        // Get active users
        const { count: activeUsers, error: activeError } = await this.supabase
          .from('User')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'ACTIVE');

        if (activeError) console.warn('Error fetching active users:', activeError);

        // Get verified users
        const { count: verifiedUsers, error: verifiedError } = await this.supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('verified', true);

        if (verifiedError) console.warn('Error fetching verified users:', verifiedError);

        // Get providers
        const { count: providers, error: providersError } = await this.supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('is_provider', true);

        if (providersError) console.warn('Error fetching providers:', providersError);

        // Get customers (non-providers)
        const { count: customers, error: customersError } = await this.supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('is_provider', false);

        if (customersError) console.warn('Error fetching customers:', customersError);

        // Get suspended users
        const { count: suspendedUsers, error: suspendedError } = await this.supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'suspended');

        if (suspendedError) console.warn('Error fetching suspended users:', suspendedError);

        // Get new users this month
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const { count: newUsersThisMonth, error: newUsersError } = await this.supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', thirtyDaysAgo.toISOString());

        if (newUsersError) console.warn('Error fetching new users:', newUsersError);

        return {
          totalUsers: totalUsers || 0,
          activeUsers: activeUsers || 0,
          newUsersThisMonth: newUsersThisMonth || 0,
          verifiedUsers: verifiedUsers || 0,
          providers: providers || 0,
          customers: customers || 0,
          suspendedUsers: suspendedUsers || 0,
        };
      }
    );
  }

  static async updateUserStatus(userId: string, status: 'active' | 'inactive' | 'suspended'): Promise<RentaHubTypes.User> {
    this.validateRequired({ userId, status }, ['userId', 'status']);

    return this.apiCall(
      () => this.apiClient.patch(`/admin/users/${userId}/status`, { status }),
      async () => {
        const { data, error } = await this.supabase
          .from('users')
          .update({ status, updated_at: new Date().toISOString() })
          .eq('id', userId)
          .select()
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('User', userId);
          }
          throw error;
        }

        return this.mapSupabaseUser(data);
      }
    );
  }

  static async deleteUser(userId: string): Promise<void> {
    this.validateRequired({ userId }, ['userId']);

    return this.apiCall(
      () => this.apiClient.delete(`/admin/users/${userId}`),
      async () => {
        const { error } = await this.supabase
          .from('users')
          .update({ status: 'deleted', updated_at: new Date().toISOString() })
          .eq('id', userId);

        if (error) {
          if (error.code === 'PGRST116') {
            throw new NotFoundError('User', userId);
          }
          throw error;
        }
      }
    );
  }
}

export default UserService;
