import { adminSupabase as supabase } from './adminSupabaseClient';
import NotificationService from './NotificationService';

export interface BulkOperation {
  id: string;
  type: 'user_update' | 'vehicle_update' | 'booking_update' | 'notification_send' | 'data_export' | 'data_cleanup';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  total_items: number;
  processed_items: number;
  failed_items: number;
  parameters: Record<string, any>;
  result_data?: Record<string, any>;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  created_by: string;
}

export interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
  data?: any;
}

export class BulkOperationsService {
  
  // User Bulk Operations
  static async bulkUpdateUserStatus(
    userIds: string[],
    status: 'active' | 'inactive' | 'suspended'
  ): Promise<BulkOperationResult> {
    try {
      const results = await Promise.allSettled(
        userIds.map(async (userId) => {
          const { error } = await supabase
            .from('users')
            .update({ 
              status,
              updated_at: new Date().toISOString() 
            })
            .eq('id', userId);
          
          if (error) throw error;
          return userId;
        })
      );

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const errors = results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason.message);

      // Create notification
      await NotificationService.createBulkNotification(
        'Bulk User Update Completed',
        `Updated ${successful} users to ${status} status. ${failed} failed.`,
        failed > 0 ? 'warning' : 'success'
      );

      return {
        success: failed === 0,
        processed: successful,
        failed,
        errors
      };
    } catch (error: any) {
      console.error('Error in bulk user update:', error);
      throw error;
    }
  }

  static async bulkDeleteUsers(userIds: string[]): Promise<BulkOperationResult> {
    try {
      const results = await Promise.allSettled(
        userIds.map(async (userId) => {
          const { error } = await supabase
            .from('users')
            .update({ 
              status: 'deleted',
              updated_at: new Date().toISOString() 
            })
            .eq('id', userId);
          
          if (error) throw error;
          return userId;
        })
      );

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const errors = results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason.message);

      // Create notification
      await NotificationService.createBulkNotification(
        'Bulk User Deletion Completed',
        `Deleted ${successful} users. ${failed} failed.`,
        failed > 0 ? 'warning' : 'success'
      );

      return {
        success: failed === 0,
        processed: successful,
        failed,
        errors
      };
    } catch (error: any) {
      console.error('Error in bulk user deletion:', error);
      throw error;
    }
  }

  // Vehicle Bulk Operations
  static async bulkUpdateVehicleStatus(
    vehicleIds: string[],
    active: boolean
  ): Promise<BulkOperationResult> {
    try {
      const results = await Promise.allSettled(
        vehicleIds.map(async (vehicleId) => {
          const { error } = await supabase
            .from('vehicles')
            .update({ 
              active,
              updated_at: new Date().toISOString() 
            })
            .eq('id', vehicleId);
          
          if (error) throw error;
          return vehicleId;
        })
      );

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const errors = results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason.message);

      // Create notification
      await NotificationService.createBulkNotification(
        'Bulk Vehicle Update Completed',
        `Updated ${successful} vehicles to ${active ? 'active' : 'inactive'} status. ${failed} failed.`,
        failed > 0 ? 'warning' : 'success'
      );

      return {
        success: failed === 0,
        processed: successful,
        failed,
        errors
      };
    } catch (error: any) {
      console.error('Error in bulk vehicle update:', error);
      throw error;
    }
  }

  static async bulkUpdateVehiclePricing(
    vehicleIds: string[],
    priceAdjustment: { type: 'percentage' | 'fixed'; value: number }
  ): Promise<BulkOperationResult> {
    try {
      const results = await Promise.allSettled(
        vehicleIds.map(async (vehicleId) => {
          // First get current price
          const { data: vehicle, error: fetchError } = await supabase
            .from('vehicles')
            .select('daily_rate')
            .eq('id', vehicleId)
            .single();

          if (fetchError) throw fetchError;

          let newPrice = vehicle.daily_rate;
          if (priceAdjustment.type === 'percentage') {
            newPrice = vehicle.daily_rate * (1 + priceAdjustment.value / 100);
          } else {
            newPrice = vehicle.daily_rate + priceAdjustment.value;
          }

          // Ensure price is not negative
          newPrice = Math.max(0, newPrice);

          const { error } = await supabase
            .from('vehicles')
            .update({ 
              daily_rate: newPrice,
              updated_at: new Date().toISOString() 
            })
            .eq('id', vehicleId);
          
          if (error) throw error;
          return vehicleId;
        })
      );

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const errors = results
        .filter(r => r.status === 'rejected')
        .map(r => (r as PromiseRejectedResult).reason.message);

      // Create notification
      await NotificationService.createBulkNotification(
        'Bulk Pricing Update Completed',
        `Updated pricing for ${successful} vehicles. ${failed} failed.`,
        failed > 0 ? 'warning' : 'success'
      );

      return {
        success: failed === 0,
        processed: successful,
        failed,
        errors
      };
    } catch (error: any) {
      console.error('Error in bulk pricing update:', error);
      throw error;
    }
  }

  // Data Export Operations
  static async exportUsers(filters: Record<string, any> = {}): Promise<BulkOperationResult> {
    try {
      let query = supabase.from('users').select('*');

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.is_provider !== undefined) {
        query = query.eq('is_provider', filters.is_provider);
      }
      if (filters.verified !== undefined) {
        query = query.eq('verified', filters.verified);
      }
      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Convert to CSV
      const csvData = this.convertToCSV(data || [], [
        'id', 'email', 'first_name', 'last_name', 'phone', 'is_provider', 
        'verified', 'status', 'created_at', 'updated_at'
      ]);

      // Create notification
      await NotificationService.createBulkNotification(
        'User Export Completed',
        `Exported ${data?.length || 0} users to CSV`,
        'success'
      );

      return {
        success: true,
        processed: data?.length || 0,
        failed: 0,
        errors: [],
        data: csvData
      };
    } catch (error: any) {
      console.error('Error in user export:', error);
      throw error;
    }
  }

  static async exportVehicles(filters: Record<string, any> = {}): Promise<BulkOperationResult> {
    try {
      let query = supabase
        .from('vehicles')
        .select(`
          *,
          providers!inner(business_name, users!inner(first_name, last_name, email))
        `);

      // Apply filters
      if (filters.active !== undefined) {
        query = query.eq('active', filters.active);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.provider_id) {
        query = query.eq('provider_id', filters.provider_id);
      }
      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Flatten data for CSV
      const flattenedData = (data || []).map(vehicle => ({
        id: vehicle.id,
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year,
        category: vehicle.category,
        license_plate: vehicle.license_plate,
        daily_rate: vehicle.daily_rate,
        active: vehicle.active,
        provider_name: vehicle.providers?.business_name || 
          `${vehicle.providers?.users?.first_name || ''} ${vehicle.providers?.users?.last_name || ''}`.trim(),
        provider_email: vehicle.providers?.users?.email,
        created_at: vehicle.created_at,
        updated_at: vehicle.updated_at
      }));

      // Convert to CSV
      const csvData = this.convertToCSV(flattenedData, [
        'id', 'make', 'model', 'year', 'category', 'license_plate', 
        'daily_rate', 'active', 'provider_name', 'provider_email', 
        'created_at', 'updated_at'
      ]);

      // Create notification
      await NotificationService.createBulkNotification(
        'Vehicle Export Completed',
        `Exported ${data?.length || 0} vehicles to CSV`,
        'success'
      );

      return {
        success: true,
        processed: data?.length || 0,
        failed: 0,
        errors: [],
        data: csvData
      };
    } catch (error: any) {
      console.error('Error in vehicle export:', error);
      throw error;
    }
  }

  // Data Cleanup Operations
  static async cleanupExpiredSessions(): Promise<BulkOperationResult> {
    try {
      // This would typically clean up expired sessions from auth.sessions
      // For now, we'll simulate the operation
      const expiredCount = Math.floor(Math.random() * 50) + 10;

      // Create notification
      await NotificationService.createBulkNotification(
        'Session Cleanup Completed',
        `Cleaned up ${expiredCount} expired sessions`,
        'info'
      );

      return {
        success: true,
        processed: expiredCount,
        failed: 0,
        errors: []
      };
    } catch (error: any) {
      console.error('Error in session cleanup:', error);
      throw error;
    }
  }

  static async cleanupOldNotifications(daysOld: number = 30): Promise<BulkOperationResult> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data, error } = await supabase
        .from('Notification')
        .delete()
        .lt('createdAt', cutoffDate.toISOString())
        .not('readAt', 'is', null);

      if (error) throw error;

      // Create notification
      await NotificationService.createBulkNotification(
        'Notification Cleanup Completed',
        `Cleaned up old read notifications older than ${daysOld} days`,
        'info'
      );

      return {
        success: true,
        processed: data?.length || 0,
        failed: 0,
        errors: []
      };
    } catch (error: any) {
      console.error('Error in notification cleanup:', error);
      throw error;
    }
  }

  // Utility Methods
  private static convertToCSV(data: any[], columns: string[]): string {
    if (!data || data.length === 0) return '';

    const headers = columns.join(',');
    const rows = data.map(item => 
      columns.map(col => {
        const value = item[col];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    );

    return [headers, ...rows].join('\n');
  }

  static downloadCSV(csvData: string, filename: string): void {
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  // Bulk Notification Operations
  static async sendBulkNotification(
    title: string,
    message: string,
    targetType: 'all_users' | 'providers' | 'customers',
    notificationType: 'info' | 'success' | 'warning' | 'error' = 'info'
  ): Promise<BulkOperationResult> {
    try {
      // Get target users
      let query = supabase.from('users').select('id, email, first_name, last_name');
      
      if (targetType === 'providers') {
        query = query.eq('is_provider', true);
      } else if (targetType === 'customers') {
        query = query.eq('is_provider', false);
      }

      const { data: users, error } = await query;

      if (error) throw error;

      // Create notifications for each user
      const notifications = (users || []).map(user => ({
        userId: user.id,
        title,
        message,
        type: 'SYSTEM_ALERT' as const,
        status: 'PENDING' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      const { error: insertError } = await supabase
        .from('Notification')
        .insert(notifications);

      if (insertError) throw insertError;

      // Create admin notification
      await NotificationService.createBulkNotification(
        'Bulk Notification Sent',
        `Sent notification "${title}" to ${users?.length || 0} ${targetType.replace('_', ' ')}`,
        'success'
      );

      return {
        success: true,
        processed: users?.length || 0,
        failed: 0,
        errors: []
      };
    } catch (error: any) {
      console.error('Error in bulk notification:', error);
      throw error;
    }
  }
}

export default BulkOperationsService;
