import { adminSupabase as supabase } from './adminSupabaseClient';

export class DirectSupabaseService {
  
  // Users
  static async getUsers(page = 1, size = 10, options: { search?: string } = {}) {
    try {
      const from = (page - 1) * size;
      const to = from + size - 1;

      let query = supabase
        .from('User')
        .select('*', { count: 'exact' })
        .range(from, to)
        .order('createdAt', { ascending: false });

      // Apply search
      if (options.search) {
        query = query.or(`name.ilike.%${options.search}%,email.ilike.%${options.search}%`);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: (data || []).map(user => ({
          id: user.id,
          email: user.email,
          name: user.name || 'No name',
          first_name: user.name?.split(' ')[0] || '',
          last_name: user.name?.split(' ').slice(1).join(' ') || '',
          phone: user.phoneNumber,
          is_provider: user.role === 'PROVIDER',
          verified: user.emailVerified || false,
          created_at: user.createdAt,
          updated_at: user.updatedAt,
          status: user.status || 'ACTIVE'
        })),
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  static async getUserStats() {
    try {
      // Get total users
      const { count: totalUsers, error: totalError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true });

      if (totalError) throw totalError;

      // Get active users (verified)
      const { count: activeUsers, error: activeError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('emailVerified', true);

      if (activeError) console.warn('Error fetching active users:', activeError);

      // Get providers
      const { count: providers, error: providersError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'PROVIDER');

      if (providersError) console.warn('Error fetching providers:', providersError);

      // Get new users this month
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { count: newUsersThisMonth, error: newUsersError } = await supabase
        .from('User')
        .select('*', { count: 'exact', head: true })
        .gte('createdAt', thirtyDaysAgo.toISOString());

      if (newUsersError) console.warn('Error fetching new users:', newUsersError);

      return {
        totalUsers: totalUsers || 0,
        activeUsers: activeUsers || 0,
        providers: providers || 0,
        customers: (totalUsers || 0) - (providers || 0),
        newUsersThisMonth: newUsersThisMonth || 0,
        verifiedUsers: activeUsers || 0,
        suspendedUsers: 0 // TODO: Calculate when we have status field
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  static async updateUserStatus(userId: string, status: 'active' | 'inactive' | 'suspended') {
    try {
      const { data, error } = await supabase
        .from('User')
        .update({
          status: status.toUpperCase(),
          updatedAt: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        email: data.email,
        name: `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'No name',
        first_name: data.first_name,
        last_name: data.last_name,
        phone: data.phone,
        is_provider: data.is_provider || false,
        verified: data.verified || false,
        created_at: data.created_at,
        updated_at: data.updated_at,
        status: data.status || 'active'
      };
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  }

  static async deleteUser(userId: string) {
    try {
      const { error } = await supabase
        .from('User')
        .update({
          status: 'DELETED',
          updatedAt: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Vehicles
  static async getVehicles(page = 1, size = 10, options: { search?: string } = {}) {
    try {
      const from = (page - 1) * size;
      const to = from + size - 1;

      let query = supabase
        .from('Vehicle')
        .select('*', { count: 'exact' })
        .range(from, to)
        .order('id', { ascending: false });

      // Apply search
      if (options.search) {
        query = query.or(`make.ilike.%${options.search}%,model.ilike.%${options.search}%,license_plate.ilike.%${options.search}%`);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: (data || []).map(vehicle => {
          // We'll fetch provider info separately if needed
          const providerName = 'Provider ' + (vehicle.providerId || 'Unknown');

          return {
            id: vehicle.id,
            provider_id: vehicle.providerId,
            make: vehicle.brand,
            model: vehicle.model,
            year: vehicle.year,
            category: vehicle.category,
            license_plate: vehicle.id, // Using ID as placeholder
            color: 'N/A', // Not in schema
            fuel_type: vehicle.engine,
            transmission: 'N/A', // Not in schema
            seats: 'N/A', // Not in schema
            doors: 'N/A', // Not in schema
            air_conditioning: 'N/A', // Not in schema
            daily_rate: vehicle.dailyRate,
            security_deposit: vehicle.depositAmount,
            available_quantity: vehicle.availableUnits,
            total_quantity: vehicle.availableUnits,
            active: vehicle.availableUnits > 0,
            description: `${vehicle.brand} ${vehicle.model} ${vehicle.year}`,
            features: vehicle.smartTags || [],
            images: [], // Would need to join with vehicle_images table
            location_city: vehicle.location_city,
            location_country: 'Malaysia', // Default
            created_at: vehicle.id, // Using ID as placeholder
            updated_at: vehicle.id, // Using ID as placeholder
            provider_name: providerName,
            status: vehicle.availableUnits > 0 ? 'available' : 'rented'
          };
        }),
        total: count || 0,
        page,
        size
      };
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      throw error;
    }
  }

  static async updateVehicle(id: string, updates: any) {
    try {
      const { data, error } = await supabase
        .from('Vehicle')
        .update(updates)
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;

      const providerName = 'Provider ' + (data.providerId || 'Unknown');

      return {
        id: data.id,
        provider_id: data.providerId,
        make: data.brand,
        model: data.model,
        year: data.year,
        category: data.category,
        license_plate: data.id,
        daily_rate: data.dailyRate,
        active: data.availableUnits > 0,
        provider_name: providerName,
        status: data.availableUnits > 0 ? 'available' : 'inactive',
        created_at: data.id,
        updated_at: data.id
      };
    } catch (error) {
      console.error('Error updating vehicle:', error);
      throw error;
    }
  }

  static async deleteVehicle(id: string) {
    try {
      const { error } = await supabase
        .from('Vehicle')
        .update({
          availableUnits: 0
        })
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      throw error;
    }
  }

  // Providers
  static async getProviders() {
    try {
      // Get providers from User table where role = 'PROVIDER'
      const { data: providersData, error: providersError } = await supabase
        .from('User')
        .select('*')
        .eq('role', 'PROVIDER')
        .order('createdAt', { ascending: false });

      if (providersError) {
        console.warn('Providers table error, using users table:', providersError);
        
        // This shouldn't happen now, but keeping as fallback
        console.error('Unexpected error accessing User table for providers');
        return [];

        if (usersError) throw usersError;

        return (usersData || []).map(user => ({
          id: user.id,
          name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown Provider',
          email: user.email,
          status: user.verified ? 'ACTIVE' : 'PENDING',
          verificationStatus: user.verified ? 'VERIFIED' : 'PENDING',
          totalVehicles: 0,
          totalBookings: 0,
          totalRevenue: 0,
          rating: 4.0,
          joinedDate: user.created_at,
        }));
      } else {
        return (providersData || []).map(provider => ({
          id: provider.id,
          name: provider.companyName || provider.name || 'Unknown Provider',
          email: provider.email,
          status: provider.status || 'ACTIVE',
          verificationStatus: provider.emailVerified ? 'VERIFIED' : 'PENDING',
          totalVehicles: 0,
          totalBookings: 0,
          totalRevenue: 0,
          rating: 4.0,
          joinedDate: provider.createdAt,
        }));
      }
    } catch (error) {
      console.error('Error fetching providers:', error);
      throw error;
    }
  }
}

export default DirectSupabaseService;
