import { createClient } from '@supabase/supabase-js';

// Hardcoded Supabase credentials for admin access
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
// Service role key for full database access (bypasses RLS)
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Create admin client with hardcoded service role key for full database access
const adminSupabase = createClient(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    global: {
      headers: {
        'X-Client-Info': 'admin-panel',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      }
    },
    db: {
      schema: 'public'
    },
    // Add connection optimization
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  }
);

// Simple cache for health checks to avoid repeated queries
let healthCheckCache: { result: any; timestamp: number } | null = null;
const HEALTH_CHECK_CACHE_TTL = 30000; // 30 seconds

export const getCachedHealthCheck = () => {
  if (healthCheckCache && Date.now() - healthCheckCache.timestamp < HEALTH_CHECK_CACHE_TTL) {
    return healthCheckCache.result;
  }
  return null;
};

export const setCachedHealthCheck = (result: any) => {
  healthCheckCache = {
    result,
    timestamp: Date.now()
  };
};

console.log('Admin Supabase Client: Initialized with service role key');

export { adminSupabase };
export default adminSupabase;
