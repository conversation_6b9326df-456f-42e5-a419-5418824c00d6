import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

// Create axios instance for auth
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'PROVIDER' | 'CUSTOMER';
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  emailVerified: boolean;
  phoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  success: boolean;
  user: User;
  token: string;
  message?: string;
}

export interface AuthError {
  success: false;
  message: string;
  errors?: string[];
}

class AuthService {
  private static readonly TOKEN_KEY = 'token';
  private static readonly USER_KEY = 'user';

  /**
   * Login with email and password
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await authApi.post('/api/auth/login', credentials);

      // Handle the actual backend response format
      if (response.data.message === 'Login successful' && response.data.token) {
        // Store token and user data
        this.setToken(response.data.token);
        this.setUser(response.data.user);

        // Convert to expected format
        return {
          success: true,
          user: response.data.user,
          token: response.data.token,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || response.data.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      
      if (axios.isAxiosError(error) && error.response) {
        const errorMessage = error.response.data.error || 
                           error.response.data.message || 
                           'Login failed';
        throw new Error(errorMessage);
      }
      
      throw new Error('Network error. Please try again.');
    }
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      const token = this.getToken();
      
      if (token) {
        await authApi.post('/api/auth/logout', {}, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuth();
    }
  }

  /**
   * Get current user from API
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const token = this.getToken();
      
      if (!token) {
        return null;
      }

      const response = await authApi.get('/api/auth/me', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success && response.data.user) {
        this.setUser(response.data.user);
        return response.data.user;
      }

      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      this.clearAuth();
      return null;
    }
  }

  /**
   * Initialize auth service
   */
  static async initialize(): Promise<User | null> {
    try {
      const token = await this.ensureValidToken();
      
      if (!token) {
        this.clearAuth();
        return null;
      }

      return await this.getCurrentUser();
    } catch (error) {
      console.error('Auth initialization error:', error);
      this.clearAuth();
      return null;
    }
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<string | null> {
    try {
      const token = this.getToken();
      
      if (!token) {
        return null;
      }

      const response = await authApi.post('/api/auth/refresh', {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success && response.data.token) {
        this.setToken(response.data.token);
        return response.data.token;
      }

      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.clearAuth();
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUser();
    
    return !!(token && user);
  }

  /**
   * Get stored token
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Set token in storage
   */
  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * Get stored user
   */
  static getUser(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    
    if (!userStr) {
      return null;
    }

    try {
      return JSON.parse(userStr);
    } catch (error) {
      console.error('Error parsing stored user:', error);
      return null;
    }
  }

  /**
   * Set user in storage
   */
  static setUser(user: User): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }

  /**
   * Clear authentication data
   */
  static clearAuth(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  /**
   * Auto-refresh token if needed
   */
  static async ensureValidToken(): Promise<string | null> {
    const token = this.getToken();
    
    if (!token) {
      return null;
    }

    if (this.isTokenExpired(token)) {
      return await this.refreshToken();
    }

    return token;
  }
}

export default AuthService;
