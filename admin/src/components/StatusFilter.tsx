import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography
} from '@mui/material';

interface StatusFilterProps {
  statuses: string[];
  selectedStatuses: string[];
  onStatusChange: (statuses: string[]) => void;
  label?: string;
  multiple?: boolean;
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  statuses,
  selectedStatuses,
  onStatusChange,
  label = "Status",
  multiple = true
}) => {
  const handleChange = (event: any) => {
    const value = event.target.value;
    if (multiple) {
      onStatusChange(typeof value === 'string' ? value.split(',') : value);
    } else {
      onStatusChange([value]);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
      case 'completed':
        return 'success';
      case 'pending':
      case 'inactive':
        return 'warning';
      case 'suspended':
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        {label}
      </Typography>
      <FormControl fullWidth size="small">
        <InputLabel>{label}</InputLabel>
        <Select
          multiple={multiple}
          value={multiple ? selectedStatuses : selectedStatuses[0] || ''}
          onChange={handleChange}
          label={label}
          renderValue={(selected) => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {Array.isArray(selected) ? selected.map((value) => (
                <Chip
                  key={value}
                  label={value}
                  color={getStatusColor(value) as any}
                  size="small"
                />
              )) : (
                <Chip
                  label={selected}
                  color={getStatusColor(selected) as any}
                  size="small"
                />
              )}
            </Box>
          )}
        >
          {statuses.map((status) => (
            <MenuItem key={status} value={status}>
              {status}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default StatusFilter; 