import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  Box, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField, 
  Typography 
} from '@mui/material';

// Interfaces for location entities
interface Country {
  id: string;
  name: string;
  code: string;
}

interface State {
  id: string;
  name: string;
  countryId: string;
}

interface City {
  id: string;
  name: string;
  stateId: string;
}

interface Area {
  id: string;
  name: string;
  cityId: string;
  latitude?: number;
  longitude?: number;
  vehicleCount?: number;
}

interface LocationSelectorProps {
  onLocationSelect: (location: {
    countryId?: string;
    stateId?: string;
    cityId?: string;
    areaId?: string;
    locationNotes?: string;
    latitude?: number;
    longitude?: number;
  }) => void;
  initialLocation?: {
    countryId?: string;
    stateId?: string;
    cityId?: string;
    areaId?: string;
  };
}

const LocationSelector: React.FC<LocationSelectorProps> = ({ 
  onLocationSelect, 
  initialLocation 
}) => {
  // State for location hierarchy
  const [countries, setCountries] = useState<Country[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);

  // Selected location values
  const [selectedCountry, setSelectedCountry] = useState<string>(
    initialLocation?.countryId || ''
  );
  const [selectedState, setSelectedState] = useState<string>(
    initialLocation?.stateId || ''
  );
  const [selectedCity, setSelectedCity] = useState<string>(
    initialLocation?.cityId || ''
  );
  const [selectedArea, setSelectedArea] = useState<string>(
    initialLocation?.areaId || ''
  );
  const [locationNotes, setLocationNotes] = useState<string>('');

  // Fetch countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get('/api/locations/countries');
        setCountries(response.data.countries);
      } catch (error) {
        console.error('Failed to fetch countries', error);
      }
    };

    fetchCountries();
  }, []);

  // Fetch states when country is selected
  useEffect(() => {
    const fetchStates = async () => {
      if (!selectedCountry) {
        setStates([]);
        return;
      }

      try {
        const response = await axios.get(`/api/locations/states/${selectedCountry}`);
        setStates(response.data.states);
        
        // Reset dependent fields
        setSelectedState('');
        setSelectedCity('');
        setSelectedArea('');
      } catch (error) {
        console.error('Failed to fetch states', error);
      }
    };

    fetchStates();
  }, [selectedCountry]);

  // Fetch cities when state is selected
  useEffect(() => {
    const fetchCities = async () => {
      if (!selectedState) {
        setCities([]);
        return;
      }

      try {
        const response = await axios.get(`/api/locations/cities/${selectedState}`);
        setCities(response.data.cities);
        
        // Reset dependent fields
        setSelectedCity('');
        setSelectedArea('');
      } catch (error) {
        console.error('Failed to fetch cities', error);
      }
    };

    fetchCities();
  }, [selectedState]);

  // Fetch areas when city is selected
  useEffect(() => {
    const fetchAreas = async () => {
      if (!selectedCity) {
        setAreas([]);
        return;
      }

      try {
        const response = await axios.get(`/api/locations/areas/${selectedCity}`);
        setAreas(response.data.areas);
        
        // Reset area selection
        setSelectedArea('');
      } catch (error) {
        console.error('Failed to fetch areas', error);
      }
    };

    fetchAreas();
  }, [selectedCity]);

  // Trigger location select callback when area is selected
  useEffect(() => {
    if (selectedArea) {
      const selectedAreaObj = areas.find(area => area.id === selectedArea);
      
      onLocationSelect({
        countryId: selectedCountry,
        stateId: selectedState,
        cityId: selectedCity,
        areaId: selectedArea,
        locationNotes,
        latitude: selectedAreaObj?.latitude,
        longitude: selectedAreaObj?.longitude
      });
    }
  }, [
    selectedArea, 
    selectedCity, 
    selectedState, 
    selectedCountry, 
    locationNotes,
    onLocationSelect
  ]);

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      gap: 2 
    }}>
      {/* Country Selector */}
      <FormControl fullWidth>
        <InputLabel>Country</InputLabel>
        <Select
          value={selectedCountry}
          label="Country"
          onChange={(e) => setSelectedCountry(e.target.value as string)}
        >
          {countries.map(country => (
            <MenuItem key={country.id} value={country.id}>
              {country.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* State Selector */}
      <FormControl fullWidth disabled={!selectedCountry}>
        <InputLabel>State/Province</InputLabel>
        <Select
          value={selectedState}
          label="State/Province"
          onChange={(e) => setSelectedState(e.target.value as string)}
        >
          {states.map(state => (
            <MenuItem key={state.id} value={state.id}>
              {state.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* City Selector */}
      <FormControl fullWidth disabled={!selectedState}>
        <InputLabel>City</InputLabel>
        <Select
          value={selectedCity}
          label="City"
          onChange={(e) => setSelectedCity(e.target.value as string)}
        >
          {cities.map(city => (
            <MenuItem key={city.id} value={city.id}>
              {city.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Area Selector */}
      <FormControl fullWidth disabled={!selectedCity}>
        <InputLabel>Area/Neighborhood</InputLabel>
        <Select
          value={selectedArea}
          label="Area/Neighborhood"
          onChange={(e) => setSelectedArea(e.target.value as string)}
        >
          {areas.map(area => (
            <MenuItem key={area.id} value={area.id}>
              {area.name} 
              {area.vehicleCount !== undefined && (
                <Typography 
                  variant="caption" 
                  color="text.secondary" 
                  sx={{ ml: 1 }}
                >
                  ({area.vehicleCount} vehicles)
                </Typography>
              )}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Location Notes */}
      <TextField
        fullWidth
        label="Location Notes (Optional)"
        placeholder="e.g., Behind XYZ Cafe, Near Main Street"
        value={locationNotes}
        onChange={(e) => setLocationNotes(e.target.value)}
        multiline
        rows={2}
      />
    </Box>
  );
};

export default LocationSelector;