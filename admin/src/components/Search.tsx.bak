import React, { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { IconButton, TextField, InputAdornment } from '@mui/material';
import { Search as SearchIcon, Clear as ClearIcon } from '@mui/icons-material';

interface SearchProps {
  className?: string;
  placeholder?: string;
  onSubmit?: (value: string) => void;
  onClear?: () => void;
}

interface SearchFormData {
  keyword: string;
}

const Search: React.FC<SearchProps> = ({
  className,
  placeholder = "Search...",
  onSubmit,
  onClear
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const { register, handleSubmit, setValue, watch } = useForm<SearchFormData>({
    mode: 'onSubmit',
  });

  const keyword = watch('keyword');

  const handleFormSubmit = (data: SearchFormData) => {
    if (onSubmit) {
      onSubmit(data.keyword || '');
    }
  };

  const handleClear = () => {
    setValue('keyword', '');
    if (onClear) {
      onClear();
    }
    inputRef.current?.focus();
  };

  return (
    <div className={className}>
      <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
        <input autoComplete="false" name="hidden" type="text" style={{ display: 'none' }} />
        <TextField
          inputRef={inputRef}
          variant="outlined"
          size="small"
          {...register('keyword')}
          placeholder={placeholder}
          InputProps={{
            endAdornment: keyword ? (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={handleClear}
                >
                  <ClearIcon style={{ width: 20, height: 20 }} />
                </IconButton>
              </InputAdornment>
            ) : null
          }}
          sx={{ minWidth: 200 }}
        />
        <IconButton type="submit" color="primary">
          <SearchIcon />
        </IconButton>
      </form>
    </div>
  );
};

export default Search; 