import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import axios from 'axios';

// Predefined smart tags
const PREDEFINED_SMART_TAGS = [
  'Beginner-Friendly',
  'Great for Couples',
  'Adventure-Ready',
  'Lightweight',
  'Long-Term Friendly',
  'Luxury Experience',
  'Fuel Efficient',
  'Easy City Navigation',
  'Good for Bali Roads'
];

interface Vehicle {
  id: string;
  name: string;
  dailyRate: number;
  smartTags: string[];
}

interface PerformanceMetrics {
  totalBookings: number;
  totalRevenue: number;
  averageBookingDuration: number;
  utilizationRate: number;
}

interface EarningsProjection {
  averageMonthlyEarnings: number;
  projectedEarnings: Array<{
    month: string;
    projectedEarnings: number;
    rentahubFees: number;
    ownerEarnings: number;
  }>;
}

export const FleetManagementDashboard: React.FC = () => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [earningsProjection, setEarningsProjection] = useState<EarningsProjection | null>(null);
  const [maintenanceDialogOpen, setMaintenanceDialogOpen] = useState(false);
  const [pricingDialogOpen, setPricingDialogOpen] = useState(false);
  const [maintenanceData, setMaintenanceData] = useState({
    startDate: null,
    endDate: null,
    reason: ''
  });
  const [pricingData, setPricingData] = useState({
    strategy: 'FIXED',
    minRate: 0,
    maxRate: 0
  });

  // Fetch vehicles
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const response = await axios.get('/api/vehicles');
        setVehicles(response.data);
      } catch (error) {
        console.error('Failed to fetch vehicles', error);
      }
    };
    fetchVehicles();
  }, []);

  // Fetch performance metrics when vehicle is selected
  useEffect(() => {
    const fetchPerformanceMetrics = async () => {
      if (!selectedVehicle) return;

      try {
        const metricsResponse = await axios.get(`/api/fleet/${selectedVehicle.id}/performance`);
        setPerformanceMetrics(metricsResponse.data);

        const earningsResponse = await axios.get(`/api/fleet/${selectedVehicle.id}/earnings-projection`);
        setEarningsProjection(earningsResponse.data);
      } catch (error) {
        console.error('Failed to fetch performance metrics', error);
      }
    };
    fetchPerformanceMetrics();
  }, [selectedVehicle]);

  // Handle maintenance scheduling
  const handleScheduleMaintenance = async () => {
    if (!selectedVehicle) return;

    try {
      await axios.post(`/api/fleet/${selectedVehicle.id}/maintenance`, {
        startDate: maintenanceData.startDate,
        endDate: maintenanceData.endDate,
        reason: maintenanceData.reason
      });
      setMaintenanceDialogOpen(false);
    } catch (error) {
      console.error('Failed to schedule maintenance', error);
    }
  };

  // Handle pricing strategy update
  const handleUpdatePricingStrategy = async () => {
    if (!selectedVehicle) return;

    try {
      await axios.put(`/api/fleet/${selectedVehicle.id}/pricing`, {
        strategy: pricingData.strategy,
        minRate: pricingData.minRate,
        maxRate: pricingData.maxRate
      });
      setPricingDialogOpen(false);
    } catch (error) {
      console.error('Failed to update pricing strategy', error);
    }
  };

  // Manage vehicle tags
  const handleAddTag = async (tag: string) => {
    if (!selectedVehicle) return;

    try {
      await axios.post(`/api/fleet/${selectedVehicle.id}/tags`, { tags: [tag] });
      // Refresh vehicle data
      const updatedVehicle = await axios.get(`/api/vehicles/${selectedVehicle.id}`);
      setSelectedVehicle(updatedVehicle.data);
    } catch (error) {
      console.error('Failed to add tag', error);
    }
  };

  const handleRemoveTag = async (tag: string) => {
    if (!selectedVehicle) return;

    try {
      await axios.delete(`/api/fleet/${selectedVehicle.id}/tags`, { 
        data: { tags: [tag] } 
      });
      // Refresh vehicle data
      const updatedVehicle = await axios.get(`/api/vehicles/${selectedVehicle.id}`);
      setSelectedVehicle(updatedVehicle.data);
    } catch (error) {
      console.error('Failed to remove tag', error);
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Fleet Management Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Vehicle Selection */}
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Select Vehicle</InputLabel>
            <Select
              value={selectedVehicle?.id || ''}
              label="Select Vehicle"
              onChange={(e) => {
                const vehicle = vehicles.find(v => v.id === e.target.value);
                setSelectedVehicle(vehicle || null);
              }}
            >
              {vehicles.map(vehicle => (
                <MenuItem key={vehicle.id} value={vehicle.id}>
                  {vehicle.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {selectedVehicle && (
          <>
            {/* Vehicle Details */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6">Vehicle Details</Typography>
                  <Typography>Daily Rate: ${selectedVehicle.dailyRate}</Typography>
                  
                  {/* Smart Tags Management */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1">Smart Tags</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {selectedVehicle.smartTags.map(tag => (
                        <Chip 
                          key={tag} 
                          label={tag} 
                          onDelete={() => handleRemoveTag(tag)} 
                        />
                      ))}
                    </Box>
                    <Select
                      fullWidth
                      displayEmpty
                      value=""
                      onChange={(e) => handleAddTag(e.target.value as string)}
                    >
                      <MenuItem value="" disabled>Add Tag</MenuItem>
                      {PREDEFINED_SMART_TAGS
                        .filter(tag => !selectedVehicle.smartTags.includes(tag))
                        .map(tag => (
                          <MenuItem key={tag} value={tag}>
                            {tag}
                          </MenuItem>
                        ))
                      }
                    </Select>
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                    <Button 
                      variant="outlined" 
                      onClick={() => setMaintenanceDialogOpen(true)}
                    >
                      Schedule Maintenance
                    </Button>
                    <Button 
                      variant="outlined" 
                      onClick={() => setPricingDialogOpen(true)}
                    >
                      Update Pricing
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance Metrics */}
            <Grid item xs={12} md={8}>
              {performanceMetrics && (
                <Card>
                  <CardContent>
                    <Typography variant="h6">Performance Metrics</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6} md={3}>
                        <Typography>Total Bookings</Typography>
                        <Typography variant="h5">
                          {performanceMetrics.totalBookings}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <Typography>Total Revenue</Typography>
                        <Typography variant="h5">
                          ${performanceMetrics.totalRevenue.toFixed(2)}
                        </Typography>
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <Typography>Avg. Booking Duration</Typography>
                        <Typography variant="h5">
                          {performanceMetrics.averageBookingDuration.toFixed(1)} days
                        </Typography>
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <Typography>Utilization Rate</Typography>
                        <Typography variant="h5">
                          {performanceMetrics.utilizationRate.toFixed(1)}%
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              )}

              {/* Earnings Projection */}
              {earningsProjection && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Typography variant="h6">Earnings Projection</Typography>
                    <Typography>
                      Avg. Monthly Earnings: ${earningsProjection.averageMonthlyEarnings.toFixed(2)}
                    </Typography>
                    <LineChart width={600} height={300} data={earningsProjection.projectedEarnings}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="projectedEarnings" stroke="#8884d8" name="Projected Earnings" />
                      <Line type="monotone" dataKey="rentahubFees" stroke="#82ca9d" name="RentaHub Fees" />
                      <Line type="monotone" dataKey="ownerEarnings" stroke="#ffc658" name="Owner Earnings" />
                    </LineChart>
                  </CardContent>
                </Card>
              )}
            </Grid>
          </>
        )}
      </Grid>

      {/* Maintenance Scheduling Dialog */}
      <Dialog 
        open={maintenanceDialogOpen} 
        onClose={() => setMaintenanceDialogOpen(false)}
      >
        <DialogTitle>Schedule Vehicle Maintenance</DialogTitle>
        <DialogContent>
          <DatePicker
            label="Start Date"
            value={maintenanceData.startDate}
            onChange={(newValue) => setMaintenanceData(prev => ({ ...prev, startDate: newValue }))}
          />
          <DatePicker
            label="End Date"
            value={maintenanceData.endDate}
            onChange={(newValue) => setMaintenanceData(prev => ({ ...prev, endDate: newValue }))}
          />
          <TextField
            fullWidth
            label="Maintenance Reason"
            value={maintenanceData.reason}
            onChange={(e) => setMaintenanceData(prev => ({ ...prev, reason: e.target.value }))}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMaintenanceDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleScheduleMaintenance}>Schedule</Button>
        </DialogActions>
      </Dialog>

      {/* Pricing Strategy Dialog */}
      <Dialog 
        open={pricingDialogOpen} 
        onClose={() => setPricingDialogOpen(false)}
      >
        <DialogTitle>Update Pricing Strategy</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Pricing Strategy</InputLabel>
            <Select
              value={pricingData.strategy}
              label="Pricing Strategy"
              onChange={(e) => setPricingData(prev => ({ ...prev, strategy: e.target.value }))}
            >
              <MenuItem value="FIXED">Fixed</MenuItem>
              <MenuItem value="DYNAMIC">Dynamic</MenuItem>
              <MenuItem value="SEASONAL">Seasonal</MenuItem>
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Minimum Daily Rate"
            type="number"
            value={pricingData.minRate}
            onChange={(e) => setPricingData(prev => ({ ...prev, minRate: Number(e.target.value) }))}
            sx={{ mt: 2 }}
          />
          <TextField
            fullWidth
            label="Maximum Daily Rate"
            type="number"
            value={pricingData.maxRate}
            onChange={(e) => setPricingData(prev => ({ ...prev, maxRate: Number(e.target.value) }))}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPricingDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdatePricingStrategy}>Update</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FleetManagementDashboard;
