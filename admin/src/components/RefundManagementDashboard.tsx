import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import axios from 'axios';

// Types for refund-related data
interface RefundRule {
  id: string;
  cancellationWindow: number;
  refundPercentage: number;
  bookingFeeRefund: boolean;
}

interface RefundTransaction {
  id: string;
  bookingId: string;
  originalAmount: number;
  refundAmount: number;
  refundFee: number;
  status: string;
  reason: string;
  createdAt: string;
  booking: {
    vehicle: {
      name: string;
    };
    user: {
      name: string;
    };
  };
}

export const RefundManagementDashboard: React.FC = () => {
  // State management
  const [refundRules, setRefundRules] = useState<RefundRule[]>([]);
  const [refundTransactions, setRefundTransactions] = useState<RefundTransaction[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<RefundTransaction | null>(null);
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [newRule, setNewRule] = useState<Partial<RefundRule>>({
    cancellationWindow: 0,
    refundPercentage: 0,
    bookingFeeRefund: false
  });
  const [filterStatus, setFilterStatus] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null
  });

  // Fetch refund rules
  useEffect(() => {
    const fetchRefundRules = async () => {
      try {
        const response = await axios.get('/api/refunds/rules');
        setRefundRules(response.data);
      } catch (error) {
        console.error('Failed to fetch refund rules', error);
      }
    };
    fetchRefundRules();
  }, []);

  // Fetch refund transactions with optional filtering
  useEffect(() => {
    const fetchRefundTransactions = async () => {
      try {
        const response = await axios.get('/api/refunds/transactions', {
          params: {
            ...(filterStatus && { status: filterStatus }),
            ...(dateRange.startDate && dateRange.endDate && {
              startDate: dateRange.startDate,
              endDate: dateRange.endDate
            })
          }
        });
        setRefundTransactions(response.data);
      } catch (error) {
        console.error('Failed to fetch refund transactions', error);
      }
    };
    fetchRefundTransactions();
  }, [filterStatus, dateRange]);

  // Process refund for a transaction
  const handleProcessRefund = async (bookingId: string) => {
    try {
      await axios.post(`/api/refunds/${bookingId}/process`);
      // Refresh transactions
      const response = await axios.get('/api/refunds/transactions');
      setRefundTransactions(response.data);
    } catch (error) {
      console.error('Failed to process refund', error);
    }
  };

  // Create new refund rule
  const handleCreateRule = async () => {
    try {
      const response = await axios.post('/api/refunds/rules', newRule);
      setRefundRules([...refundRules, response.data]);
      setRuleDialogOpen(false);
      // Reset new rule form
      setNewRule({
        cancellationWindow: 0,
        refundPercentage: 0,
        bookingFeeRefund: false
      });
    } catch (error) {
      console.error('Failed to create refund rule', error);
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Refund Management Dashboard
      </Typography>

      {/* Filtering Options */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth>
            <InputLabel>Filter by Status</InputLabel>
            <Select
              value={filterStatus}
              label="Filter by Status"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="">All Statuses</MenuItem>
              <MenuItem value="PENDING">Pending</MenuItem>
              <MenuItem value="PROCESSING">Processing</MenuItem>
              <MenuItem value="COMPLETED">Completed</MenuItem>
              <MenuItem value="FAILED">Failed</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={4}>
          <DatePicker
            label="Start Date"
            value={dateRange.startDate}
            onChange={(newValue) => setDateRange(prev => ({ ...prev, startDate: newValue }))}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <DatePicker
            label="End Date"
            value={dateRange.endDate}
            onChange={(newValue) => setDateRange(prev => ({ ...prev, endDate: newValue }))}
          />
        </Grid>
      </Grid>

      {/* Refund Transactions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6">Refund Transactions</Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Booking ID</TableCell>
                  <TableCell>Vehicle</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Original Amount</TableCell>
                  <TableCell>Refund Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {refundTransactions.map(transaction => (
                  <TableRow key={transaction.id}>
                    <TableCell>{transaction.bookingId}</TableCell>
                    <TableCell>{transaction.booking.vehicle.name}</TableCell>
                    <TableCell>{transaction.booking.user.name}</TableCell>
                    <TableCell>${transaction.originalAmount.toFixed(2)}</TableCell>
                    <TableCell>${transaction.refundAmount.toFixed(2)}</TableCell>
                    <TableCell>
                      <Chip 
                        label={transaction.status} 
                        color={
                          transaction.status === 'COMPLETED' ? 'success' :
                          transaction.status === 'FAILED' ? 'error' :
                          'warning'
                        } 
                      />
                    </TableCell>
                    <TableCell>
                      {transaction.status === 'PENDING' && (
                        <Button 
                          variant="contained" 
                          color="primary"
                          onClick={() => handleProcessRefund(transaction.bookingId)}
                        >
                          Process Refund
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Refund Rules */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Refund Rules</Typography>
            <Button 
              variant="contained" 
              color="primary"
              onClick={() => setRuleDialogOpen(true)}
            >
              Create Rule
            </Button>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Cancellation Window (Hours)</TableCell>
                  <TableCell>Refund Percentage</TableCell>
                  <TableCell>Booking Fee Refund</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {refundRules.map(rule => (
                  <TableRow key={rule.id}>
                    <TableCell>{rule.cancellationWindow}</TableCell>
                    <TableCell>{rule.refundPercentage}%</TableCell>
                    <TableCell>{rule.bookingFeeRefund ? 'Yes' : 'No'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create Refund Rule Dialog */}
      <Dialog 
        open={ruleDialogOpen} 
        onClose={() => setRuleDialogOpen(false)}
      >
        <DialogTitle>Create Refund Rule</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Cancellation Window (Hours)</InputLabel>
            <Select
              value={newRule.cancellationWindow}
              label="Cancellation Window (Hours)"
              onChange={(e) => setNewRule(prev => ({ 
                ...prev, 
                cancellationWindow: Number(e.target.value) 
              }))}
            >
              <MenuItem value={48}>48 Hours</MenuItem>
              <MenuItem value={24}>24 Hours</MenuItem>
              <MenuItem value={12}>12 Hours</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Refund Percentage</InputLabel>
            <Select
              value={newRule.refundPercentage}
              label="Refund Percentage"
              onChange={(e) => setNewRule(prev => ({ 
                ...prev, 
                refundPercentage: Number(e.target.value) 
              }))}
            >
              <MenuItem value={100}>100%</MenuItem>
              <MenuItem value={50}>50%</MenuItem>
              <MenuItem value={0}>0%</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Booking Fee Refund</InputLabel>
            <Select
              value={newRule.bookingFeeRefund}
              label="Booking Fee Refund"
              onChange={(e) => setNewRule(prev => ({ 
                ...prev, 
                bookingFeeRefund: e.target.value === 'true' 
              }))}
            >
              <MenuItem value={'true'}>Yes</MenuItem>
              <MenuItem value={'false'}>No</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRuleDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateRule}>Create Rule</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RefundManagementDashboard;
