import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  FormGroup,
  TextField,
  Button,
  Chip,
  Divider,
  IconButton,
  Tooltip,
  Slider,
  Grid,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import * as helper from '../utils/helper';

export interface FilterOption {
  value: string | number;
  label: string;
  count?: number;
}

export interface FilterConfig {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'checkbox' | 'text' | 'number' | 'date' | 'daterange' | 'slider';
  options?: FilterOption[];
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: any;
  multiple?: boolean;
  searchable?: boolean;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface FilterValues {
  [key: string]: any;
}

export interface FilterPanelProps {
  filters: FilterConfig[];
  values: FilterValues;
  onChange: (values: FilterValues) => void;
  onReset?: () => void;
  onApply?: () => void;
  loading?: boolean;
  showApplyButton?: boolean;
  showResetButton?: boolean;
  showActiveFilters?: boolean;
  title?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  values,
  onChange,
  onReset,
  onApply,
  loading = false,
  showApplyButton = false,
  showResetButton = true,
  showActiveFilters = true,
  title = 'Filters',
  collapsible = false,
  defaultExpanded = true,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);
  const [searchTerms, setSearchTerms] = useState<{ [key: string]: string }>({});

  const handleFilterChange = (filterId: string, value: any) => {
    const newValues = { ...values, [filterId]: value };
    onChange(newValues);
  };

  const handleReset = () => {
    const resetValues: FilterValues = {};
    filters.forEach(filter => {
      if (filter.defaultValue !== undefined) {
        resetValues[filter.id] = filter.defaultValue;
      }
    });
    onChange(resetValues);
    onReset?.();
  };

  const getActiveFiltersCount = () => {
    return Object.keys(values).filter(key => {
      const value = values[key];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== undefined && value !== null && value !== '';
    }).length;
  };

  const getActiveFilterChips = () => {
    const chips: React.ReactNode[] = [];
    
    filters.forEach(filter => {
      const value = values[filter.id];
      if (!value || (Array.isArray(value) && value.length === 0)) return;

      let chipLabel = '';
      let chipValue = value;

      switch (filter.type) {
        case 'select':
        case 'multiselect':
          if (Array.isArray(value)) {
            const selectedOptions = filter.options?.filter(opt => value.includes(opt.value));
            chipLabel = `${filter.label}: ${selectedOptions?.map(opt => opt.label).join(', ')}`;
          } else {
            const selectedOption = filter.options?.find(opt => opt.value === value);
            chipLabel = `${filter.label}: ${selectedOption?.label || value}`;
          }
          break;
        case 'checkbox':
          if (value) {
            chipLabel = filter.label;
          }
          break;
        case 'text':
        case 'number':
          chipLabel = `${filter.label}: ${value}`;
          break;
        case 'date':
          chipLabel = `${filter.label}: ${helper.formatDate(value)}`;
          break;
        case 'daterange':
          if (value.start && value.end) {
            chipLabel = `${filter.label}: ${helper.formatDate(value.start)} - ${helper.formatDate(value.end)}`;
          }
          break;
        case 'slider':
          if (Array.isArray(value)) {
            chipLabel = `${filter.label}: ${value[0]} - ${value[1]}`;
          } else {
            chipLabel = `${filter.label}: ${value}`;
          }
          break;
      }

      if (chipLabel) {
        chips.push(
          <Chip
            key={filter.id}
            label={chipLabel}
            onDelete={() => handleFilterChange(filter.id, filter.type === 'checkbox' ? false : 
              filter.type === 'multiselect' ? [] : 
              filter.type === 'daterange' ? { start: null, end: null } :
              filter.type === 'slider' && Array.isArray(filter.defaultValue) ? filter.defaultValue :
              undefined)}
            size="small"
            variant="outlined"
            sx={{ m: 0.5 }}
          />
        );
      }
    });

    return chips;
  };

  const renderFilter = (filter: FilterConfig) => {
    const value = values[filter.id];

    switch (filter.type) {
      case 'select':
        return (
          <FormControl fullWidth size="small">
            <InputLabel>{filter.label}</InputLabel>
            <Select
              value={value || ''}
              label={filter.label}
              onChange={(e) => handleFilterChange(filter.id, e.target.value)}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              {filter.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                  {option.count !== undefined && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      ({option.count})
                    </Typography>
                  )}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'multiselect':
        return (
          <FormControl fullWidth size="small">
            <InputLabel>{filter.label}</InputLabel>
            <Select
              multiple
              value={value || []}
              label={filter.label}
              onChange={(e) => handleFilterChange(filter.id, e.target.value)}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {(selected as string[]).map((val) => {
                    const option = filter.options?.find(opt => opt.value === val);
                    return (
                      <Chip key={val} label={option?.label || val} size="small" />
                    );
                  })}
                </Box>
              )}
            >
              {filter.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Checkbox checked={(value || []).includes(option.value)} />
                  {option.label}
                  {option.count !== undefined && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      ({option.count})
                    </Typography>
                  )}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'checkbox':
        return (
          <FormGroup>
            {filter.options?.map((option) => (
              <FormControlLabel
                key={option.value}
                control={
                  <Checkbox
                    checked={(value || []).includes(option.value)}
                    onChange={(e) => {
                      const currentValues = value || [];
                      const newValues = e.target.checked
                        ? [...currentValues, option.value]
                        : currentValues.filter((v: any) => v !== option.value);
                      handleFilterChange(filter.id, newValues);
                    }}
                  />
                }
                label={
                  <Box>
                    {option.label}
                    {option.count !== undefined && (
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                        ({option.count})
                      </Typography>
                    )}
                  </Box>
                }
              />
            )) || (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={value || false}
                    onChange={(e) => handleFilterChange(filter.id, e.target.checked)}
                  />
                }
                label={filter.label}
              />
            )}
          </FormGroup>
        );

      case 'text':
        return (
          <TextField
            fullWidth
            size="small"
            label={filter.label}
            placeholder={filter.placeholder}
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.id, e.target.value)}
          />
        );

      case 'number':
        return (
          <TextField
            fullWidth
            size="small"
            type="number"
            label={filter.label}
            placeholder={filter.placeholder}
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.id, e.target.value)}
            inputProps={{
              min: filter.min,
              max: filter.max,
              step: filter.step,
            }}
          />
        );

      case 'date':
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label={filter.label}
              value={value || null}
              onChange={(newValue) => handleFilterChange(filter.id, newValue)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: 'small',
                },
              }}
            />
          </LocalizationProvider>
        );

      case 'daterange':
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <DatePicker
                  label="From"
                  value={value?.start || null}
                  onChange={(newValue) => handleFilterChange(filter.id, { ...value, start: newValue })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <DatePicker
                  label="To"
                  value={value?.end || null}
                  onChange={(newValue) => handleFilterChange(filter.id, { ...value, end: newValue })}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: 'small',
                    },
                  }}
                />
              </Grid>
            </Grid>
          </LocalizationProvider>
        );

      case 'slider':
        return (
          <Box sx={{ px: 1 }}>
            <Typography variant="body2" gutterBottom>
              {filter.label}
            </Typography>
            <Slider
              value={value || [filter.min || 0, filter.max || 100]}
              onChange={(_, newValue) => handleFilterChange(filter.id, newValue)}
              valueLabelDisplay="auto"
              min={filter.min || 0}
              max={filter.max || 100}
              step={filter.step || 1}
              marks={[
                { value: filter.min || 0, label: filter.min?.toString() || '0' },
                { value: filter.max || 100, label: filter.max?.toString() || '100' },
              ]}
            />
          </Box>
        );

      default:
        return null;
    }
  };

  const filterContent = (
    <Box>
      {/* Active Filters */}
      {showActiveFilters && getActiveFiltersCount() > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Active Filters ({getActiveFiltersCount()})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {getActiveFilterChips()}
          </Box>
          <Divider sx={{ mt: 2 }} />
        </Box>
      )}

      {/* Filter Controls */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {filters.map((filter) => (
          <Box key={filter.id}>
            {filter.collapsible ? (
              <Accordion defaultExpanded={filter.defaultExpanded !== false}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2">{filter.label}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {renderFilter(filter)}
                </AccordionDetails>
              </Accordion>
            ) : (
              renderFilter(filter)
            )}
          </Box>
        ))}
      </Box>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 1, mt: 3 }}>
        {showApplyButton && (
          <Button
            variant="contained"
            onClick={onApply}
            disabled={loading}
            startIcon={<FilterIcon />}
            fullWidth
          >
            Apply Filters
          </Button>
        )}
        {showResetButton && (
          <Button
            variant="outlined"
            onClick={handleReset}
            disabled={loading}
            startIcon={<ClearIcon />}
            fullWidth={!showApplyButton}
          >
            Reset
          </Button>
        )}
      </Box>
    </Box>
  );

  if (collapsible) {
    return (
      <Paper sx={{ mb: 2 }}>
        <Accordion expanded={expanded} onChange={(_, isExpanded) => setExpanded(isExpanded)}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FilterIcon />
              <Typography variant="h6">{title}</Typography>
              {getActiveFiltersCount() > 0 && (
                <Chip
                  label={getActiveFiltersCount()}
                  size="small"
                  color="primary"
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {filterContent}
          </AccordionDetails>
        </Accordion>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <FilterIcon />
        <Typography variant="h6">{title}</Typography>
        {getActiveFiltersCount() > 0 && (
          <Chip
            label={getActiveFiltersCount()}
            size="small"
            color="primary"
          />
        )}
      </Box>
      {filterContent}
    </Paper>
  );
};

export default FilterPanel;
