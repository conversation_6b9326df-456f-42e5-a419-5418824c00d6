import React, { useState, useCallback } from 'react'
import { GoogleMap, Marker, StandaloneSearchBox } from '@react-google-maps/api'

const libraries = ['places'] as const

interface GoogleMapsLocationPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string) => void
  initialLat?: number
  initialLng?: number
  initialAddress?: string
}

const GoogleMapsLocationPicker: React.FC<GoogleMapsLocationPickerProps> = ({
  onLocationSelect,
  initialLat = -6.2088, // Default to Jakarta
  initialLng = 106.8456,
  initialAddress = '',
}) => {
  const [searchBox, setSearchBox] = useState<google.maps.places.SearchBox | null>(null)
  const [markerPosition, setMarkerPosition] = useState({
    lat: initialLat,
    lng: initialLng,
  })
  const [address, setAddress] = useState(initialAddress)

  const onMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const lat = event.latLng.lat()
      const lng = event.latLng.lng()
      setMarkerPosition({ lat, lng })
      
      // Reverse geocode to get address
      const geocoder = new google.maps.Geocoder()
      geocoder.geocode({ location: { lat, lng } }, (results, status) => {
        if (status === 'OK' && results?.[0]) {
          setAddress(results[0].formatted_address)
          onLocationSelect(lat, lng, results[0].formatted_address)
        }
      })
    }
  }, [onLocationSelect])

  const onSearchBoxLoaded = (ref: google.maps.places.SearchBox) => {
    setSearchBox(ref)
  }

  const onPlacesChanged = () => {
    if (searchBox) {
      const places = searchBox.getPlaces()
      if (places && places.length > 0) {
        const place = places[0]
        const location = place.geometry?.location
        if (location) {
          const lat = location.lat()
          const lng = location.lng()
          setMarkerPosition({ lat, lng })
          setAddress(place.formatted_address || '')
          onLocationSelect(lat, lng, place.formatted_address || '')
        }
      }
    }
  }

  return (
    <div style={{ height: '400px', width: '100%' }}>
      <div style={{ marginBottom: '10px' }}>
        <StandaloneSearchBox
          onLoad={onSearchBoxLoaded}
          onPlacesChanged={onPlacesChanged}
        >
          <input
            type="text"
            placeholder="Search for a location"
            style={{
              boxSizing: 'border-box',
              border: '1px solid transparent',
              width: '240px',
              height: '32px',
              padding: '0 12px',
              borderRadius: '3px',
              boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
              fontSize: '14px',
              outline: 'none',
              textOverflow: 'ellipses',
            }}
          />
        </StandaloneSearchBox>
      </div>
      <GoogleMap
        mapContainerStyle={{ height: '350px', width: '100%' }}
        zoom={15}
        center={markerPosition}
        onClick={onMapClick}
      >
        <Marker position={markerPosition} />
      </GoogleMap>
      <p>Selected Address: {address}</p>
    </div>
  )
}

export default GoogleMapsLocationPicker
