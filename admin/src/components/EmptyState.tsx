import React from 'react';
import {
  Box,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  SxProps,
  Theme
} from '@mui/material';
import {
  Warning as WarningIcon,
  Add as AddIcon
} from '@mui/icons-material';

interface EmptyStateProps {
  title: string;
  description: string;
  actionText?: string;
  actionIcon?: React.ReactNode;
  icon?: React.ReactNode;
  onAction?: () => void;
  sx?: SxProps<Theme>;
  minHeight?: number | string;
}

/**
 * A reusable empty state component that displays a message and optional action button
 * when no data is available.
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  actionText,
  actionIcon = <AddIcon />,
  icon = <WarningIcon sx={{ fontSize: 48 }} />,
  onAction,
  sx = {},
  minHeight = 200
}) => {
  return (
    <Card sx={{ 
      height: '100%', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      minHeight,
      ...sx
    }}>
      <CardContent sx={{ textAlign: 'center' }}>
        <Box sx={{ mb: 2, color: 'text.secondary' }}>
          {icon}
        </Box>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
        {actionText && (
          <Button
            variant="contained"
            startIcon={actionIcon}
            size="small"
            onClick={onAction}
          >
            {actionText}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default EmptyState;
