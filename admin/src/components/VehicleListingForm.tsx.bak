import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  TextField, 
  Typography, 
  Container, 
  Grid 
} from '@mui/material';
import LocationSelector from './LocationSelector';
import axios from 'axios';

interface VehicleListingFormProps {
  onSubmit?: (vehicleData: any) => void;
}

const VehicleListingForm: React.FC<VehicleListingFormProps> = ({ onSubmit }) => {
  // Vehicle details state
  const [vehicleDetails, setVehicleDetails] = useState({
    make: '',
    model: '',
    year: '',
    type: '',
    dailyRate: '',
    description: '',
    
    // Location details
    areaId: '',
    locationNotes: '',
    latitude: undefined,
    longitude: undefined
  });

  // Handle location selection
  const handleLocationSelect = (location: {
    countryId?: string;
    stateId?: string;
    cityId?: string;
    areaId?: string;
    locationNotes?: string;
    latitude?: number;
    longitude?: number;
  }) => {
    setVehicleDetails(prev => ({
      ...prev,
      areaId: location.areaId || '',
      locationNotes: location.locationNotes || '',
      latitude: location.latitude,
      longitude: location.longitude
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate required fields
      if (!vehicleDetails.areaId) {
        alert('Please select a location for your vehicle');
        return;
      }

      // Submit vehicle listing
      const response = await axios.post('/api/vehicles/list', vehicleDetails);
      
      // Call onSubmit callback if provided
      onSubmit?.(response.data.vehicle);

      // Reset form or navigate
      alert('Vehicle listed successfully!');
    } catch (error) {
      console.error('Vehicle listing failed', error);
      alert('Failed to list vehicle. Please try again.');
    }
  };

  return (
    <Container maxWidth="md">
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
        <Typography variant="h5" gutterBottom>
          List Your Vehicle
        </Typography>

        <Grid container spacing={3}>
          {/* Vehicle Details */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Make"
              value={vehicleDetails.make}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                make: e.target.value
              }))}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Model"
              value={vehicleDetails.model}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                model: e.target.value
              }))}
              required
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Year"
              type="number"
              value={vehicleDetails.year}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                year: e.target.value
              }))}
              required
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Vehicle Type"
              select
              value={vehicleDetails.type}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                type: e.target.value
              }))}
              required
              SelectProps={{ native: true }}
            >
              <option value="">Select Type</option>
              <option value="SEDAN">Sedan</option>
              <option value="SUV">SUV</option>
              <option value="TRUCK">Truck</option>
              <option value="VAN">Van</option>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Daily Rate"
              type="number"
              value={vehicleDetails.dailyRate}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                dailyRate: e.target.value
              }))}
              required
              InputProps={{
                startAdornment: '$'
              }}
            />
          </Grid>

          {/* Vehicle Description */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Vehicle Description"
              multiline
              rows={3}
              value={vehicleDetails.description}
              onChange={(e) => setVehicleDetails(prev => ({
                ...prev, 
                description: e.target.value
              }))}
            />
          </Grid>

          {/* Location Selector */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Vehicle Location
            </Typography>
            <LocationSelector 
              onLocationSelect={handleLocationSelect}
            />
          </Grid>

          {/* Submit Button */}
          <Grid item xs={12}>
            <Button 
              type="submit" 
              variant="contained" 
              color="primary" 
              fullWidth
            >
              List Vehicle
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default VehicleListingForm;
