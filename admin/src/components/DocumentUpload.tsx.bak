import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  FormControl, 
  FormLabel, 
  Input, 
  VStack, 
  Text, 
  useToast 
} from '@chakra-ui/react';
import axios from 'axios';

interface DocumentUploadProps {
  onUploadSuccess?: (documentId: string) => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({ onUploadSuccess }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<string>('license');
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const toast = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid File Type',
          description: 'Only JPEG, PNG, and PDF files are allowed.',
          status: 'error',
          duration: 3000,
          isClosable: true
        });
        return;
      }

      if (file.size > maxSize) {
        toast({
          title: 'File Too Large',
          description: 'File must be less than 5MB.',
          status: 'error',
          duration: 3000,
          isClosable: true
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: 'No File Selected',
        description: 'Please select a document to upload.',
        status: 'warning',
        duration: 3000,
        isClosable: true
      });
      return;
    }

    setIsUploading(true);

    const formData = new FormData();
    formData.append('document', selectedFile);
    formData.append('documentType', documentType);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post('/api/auth/upload-document', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });

      toast({
        title: 'Document Uploaded',
        description: 'Your document has been uploaded successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true
      });

      // Call optional callback with document ID
      if (onUploadSuccess) {
        onUploadSuccess(response.data.documentId);
      }

      // Reset form
      setSelectedFile(null);
      (document.getElementById('file-upload') as HTMLInputElement).value = '';
    } catch (error: any) {
      toast({
        title: 'Upload Failed',
        description: error.response?.data?.error || 'Failed to upload document',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Box 
      maxWidth="500px" 
      margin="auto" 
      padding={6} 
      boxShadow="md" 
      borderRadius="md"
    >
      <VStack spacing={4}>
        <Text fontSize="xl" fontWeight="bold">
          Upload Verification Document
        </Text>

        <FormControl>
          <FormLabel>Document Type</FormLabel>
          <Input 
            value={documentType}
            onChange={(e) => setDocumentType(e.target.value)}
            placeholder="Enter document type (e.g., license)"
          />
        </FormControl>

        <FormControl>
          <FormLabel>Upload Document</FormLabel>
          <Input 
            type="file" 
            id="file-upload"
            accept=".jpg,.jpeg,.png,.pdf"
            onChange={handleFileChange}
          />
          {selectedFile && (
            <Text mt={2} color="green.500">
              Selected: {selectedFile.name}
            </Text>
          )}
        </FormControl>

        <Button 
          colorScheme="blue" 
          onClick={handleUpload}
          isLoading={isUploading}
          disabled={!selectedFile}
        >
          Upload Document
        </Button>

        <Text fontSize="sm" color="gray.500" textAlign="center">
          Accepted file types: JPEG, PNG, PDF
          <br />
          Maximum file size: 5MB
        </Text>
      </VStack>
    </Box>
  );
};

export default DocumentUpload;