import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  AlertTitle
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckIcon,
  Code as CodeIcon,
  Storage as DatabaseIcon,
  CloudUpload as UploadIcon
} from '@mui/icons-material';

interface DataSeedingGuideProps {
  onClose?: () => void;
}

/**
 * A guide component that helps administrators understand how to add initial data
 * to their RentaHub platform when starting from scratch.
 */
const DataSeedingGuide: React.FC<DataSeedingGuideProps> = ({ onClose }) => {
  const [expandedPanel, setExpandedPanel] = useState<string | false>('users');

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  const seedingSteps = [
    {
      id: 'users',
      title: 'Add Users & Providers',
      description: 'Create user accounts and vehicle providers',
      items: [
        'Create admin accounts for platform management',
        'Invite vehicle providers to join your platform',
        'Set up customer accounts for testing',
        'Configure user roles and permissions'
      ],
      sqlExample: `-- Example: Insert a test provider
INSERT INTO users (email, first_name, last_name, is_provider, verified)
VALUES ('<EMAIL>', 'John', 'Doe', true, true);`
    },
    {
      id: 'vehicles',
      title: 'Populate Vehicle Catalog',
      description: 'Add vehicles to your rental marketplace',
      items: [
        'Import vehicle data from CSV or API',
        'Add vehicle photos and descriptions',
        'Set pricing and availability',
        'Configure vehicle categories and features'
      ],
      sqlExample: `-- Example: Insert a test vehicle
INSERT INTO vehicle_catalog (make, model, year, type, daily_rate, available_units)
VALUES ('Honda', 'Civic', '2023', 'Car', 50.00, 5);`
    },
    {
      id: 'bookings',
      title: 'Create Test Bookings',
      description: 'Generate sample booking data for testing',
      items: [
        'Create test bookings with different statuses',
        'Set up payment records for financial tracking',
        'Test the booking workflow end-to-end',
        'Verify email notifications and confirmations'
      ],
      sqlExample: `-- Example: Insert a test booking
INSERT INTO bookings (user_id, vehicle_id, start_date, end_date, total, booking_status)
VALUES ('user-id', 'vehicle-id', '2024-01-20', '2024-01-22', 100.00, 'confirmed');`
    }
  ];

  return (
    <Card sx={{ maxWidth: 800, mx: 'auto' }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <DatabaseIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Data Seeding Guide
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Follow these steps to populate your RentaHub platform with initial data
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Getting Started</AlertTitle>
          Your dashboard is empty because there's no data in your database yet. 
          You can add data through the admin interface or by importing existing data.
        </Alert>

        {seedingSteps.map((step) => (
          <Accordion
            key={step.id}
            expanded={expandedPanel === step.id}
            onChange={handleAccordionChange(step.id)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                  {step.title}
                </Typography>
                <Chip 
                  label={`${step.items.length} steps`} 
                  size="small" 
                  color="primary" 
                  variant="outlined"
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {step.description}
              </Typography>
              
              <List dense>
                {step.items.map((item, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckIcon color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>

              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CodeIcon fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2">Example SQL</Typography>
                </Box>
                <Typography 
                  variant="body2" 
                  component="pre" 
                  sx={{ 
                    fontFamily: 'monospace', 
                    fontSize: '0.75rem',
                    whiteSpace: 'pre-wrap',
                    color: 'text.secondary'
                  }}
                >
                  {step.sqlExample}
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Need help with data import? We can assist with bulk data migration.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              size="small"
            >
              Import Data
            </Button>
            <Button
              variant="text"
              size="small"
              onClick={onClose}
            >
              Close Guide
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default DataSeedingGuide;
