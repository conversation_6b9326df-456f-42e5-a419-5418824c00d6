import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Stack,
  Divider
} from '@mui/material';
import {
  People as PeopleIcon,
  DirectionsCar as CarIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

interface GetStartedCTAProps {
  onNavigateToUsers?: () => void;
  onNavigateToVehicles?: () => void;
  onNavigateToSettings?: () => void;
}

/**
 * A comprehensive call-to-action component that guides new admins
 * through the initial setup process when no data is available.
 */
const GetStartedCTA: React.FC<GetStartedCTAProps> = ({
  onNavigateToUsers,
  onNavigateToVehicles,
  onNavigateToSettings
}) => {
  const steps = [
    {
      title: 'Set Up Your Platform',
      description: 'Configure basic settings and payment methods',
      icon: <SettingsIcon sx={{ fontSize: 40 }} />,
      action: 'Configure Settings',
      onClick: onNavigateToSettings,
      color: 'primary'
    },
    {
      title: 'Add Vehicle Providers',
      description: 'Invite vehicle owners to list their vehicles',
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      action: 'Manage Users',
      onClick: onNavigateToUsers,
      color: 'secondary'
    },
    {
      title: 'Populate Vehicle Catalog',
      description: 'Add vehicles to your rental marketplace',
      icon: <CarIcon sx={{ fontSize: 40 }} />,
      action: 'Add Vehicles',
      onClick: onNavigateToVehicles,
      color: 'success'
    }
  ];

  return (
    <Card sx={{ p: 3, textAlign: 'center', bgcolor: 'background.paper' }}>
      <CardContent>
        <Box sx={{ mb: 3 }}>
          <TrendingUpIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Welcome to RentaHub Admin!
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
            Your dashboard is empty because you're just getting started. Follow these steps to set up your vehicle rental platform and start seeing data flow in.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={3} sx={{ mt: 2 }}>
          {steps.map((step, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4
                  }
                }}
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <Box sx={{ mb: 2, color: `${step.color}.main` }}>
                    {step.icon}
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {step.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {step.description}
                  </Typography>
                  <Button
                    variant="contained"
                    color={step.color as any}
                    onClick={step.onClick}
                    fullWidth
                  >
                    {step.action}
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 4 }}>
          <Typography variant="body2" color="text.secondary">
            Need help getting started? Check out our{' '}
            <Button 
              variant="text" 
              size="small" 
              sx={{ textTransform: 'none', p: 0, minWidth: 'auto' }}
            >
              documentation
            </Button>
            {' '}or{' '}
            <Button 
              variant="text" 
              size="small" 
              sx={{ textTransform: 'none', p: 0, minWidth: 'auto' }}
            >
              contact support
            </Button>
            .
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default GetStartedCTA;
