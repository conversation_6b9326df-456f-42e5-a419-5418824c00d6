import React, { useState } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard,
  DirectionsBike,
  BookOnline,
  People,
  Business,
  AccountCircle,
  Logout,
  Add as AddIcon,
  LocalShipping as FleetIcon,
  MonetizationOn as RevenueIcon,
  Assessment as AnalyticsIcon,
  Build as MaintenanceIcon,
  // New icons for enhanced admin features
  Support as SupportIcon,
  Report as ReportIcon,
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Assignment as AssignmentIcon,
  Receipt as ReceiptIcon,
  Timeline as TimelineIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  MoreVert as MoreIcon,
  ExpandMore as ExpandMoreIcon,
  AdminPanelSettings as AdminIcon,
  VerifiedUser as VerifiedIcon,
  SystemUpdate as SystemUpdateIcon,
  Backup as BackupIcon,
  Warning as EmergencyIcon,
  LocalOffer as DiscountIcon,
  Star as StarIcon,
  RateReview as ReviewIcon,
  Feedback as FeedbackIcon,
  Help as HelpIcon,
  ContactSupport as ContactIcon,
  BugReport as BugIcon,
  NewReleases as NewIcon,
  Update as UpdateIcon,
  Storage as StorageIcon,
  CloudUpload as CloudIcon,
  Sync as SyncIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  RestartAlt as RestartIcon,
  Power as PowerIcon,
  PowerOff as PowerOffIcon,
  PowerSettingsNew as PowerSettingsIcon,
  BatteryChargingFull as BatteryIcon,
  SignalCellular4Bar as SignalIcon,
  Wifi as WifiIcon,
  Bluetooth as BluetoothIcon,
  LocationOn as LocationIcon,
  MyLocation as MyLocationIcon,
  Navigation as NavigationIcon,
  Directions as DirectionsIcon,
  Map as MapIcon,
  Terrain as TerrainIcon,
  Streetview as StreetviewIcon,
  Satellite as SatelliteIcon,
  Public as PublicIcon,
  Language as LanguageIcon,
  Translate as TranslateIcon,
  GTranslate as GTranslateIcon,
  Translate as TranslateIcon2,
  GTranslate as GTranslateIcon2,
  Translate as TranslateIcon3,
  GTranslate as GTranslateIcon3,
  Translate as TranslateIcon4,
  GTranslate as GTranslateIcon4,
  Translate as TranslateIcon5,
  GTranslate as GTranslateIcon5,
  Translate as TranslateIcon6,
  GTranslate as GTranslateIcon6,
  Translate as TranslateIcon7,
  GTranslate as GTranslateIcon7,
  Translate as TranslateIcon8,
  GTranslate as GTranslateIcon8,
  Translate as TranslateIcon9,
  GTranslate as GTranslateIcon9,
  Translate as TranslateIcon10,
  GTranslate as GTranslateIcon10,
} from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import SessionWarning from './SessionWarning'
import SessionStatus from './SessionStatus'
import { toast } from 'react-toastify'

const drawerWidth = 240

const Layout: React.FC = () => {
  const navigate = useNavigate()
  const { state, logout, extendSession } = useAuth(); const user = state.user; const isAdmin = state.user?.role === "ADMIN"; const isProvider = state.user?.role === "PROVIDER"
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleSignOut = async () => {
    try {
      await logout()
      toast.success('Signed out successfully')
      navigate('/signin')
    } catch (error) {
      toast.error('Error signing out')
    }
    handleProfileMenuClose()
  }

  const menuItems = [
    { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
    ...(isAdmin ? [
      { text: 'Admin Dashboard', icon: <AnalyticsIcon />, path: '/admin' },
      // Enhanced Admin Features - Priority 1: Provider Management
      { text: 'Provider Management', icon: <Business />, path: '/providers' },
      { text: 'Provider Payouts', icon: <PaymentIcon />, path: '/payouts' },
      { text: 'Bank Details', icon: <BankIcon />, path: '/bank-details' },
      // Priority 2: User Management
      { text: 'User Management', icon: <People />, path: '/users' },
      { text: 'User Verification', icon: <VerifiedIcon />, path: '/user-verification' },
      // Priority 3: Financial Management
      { text: 'Financial Management', icon: <PaymentIcon />, path: '/financial' },
      { text: 'Cashflow Dashboard', icon: <TrendingUpIcon />, path: '/cashflow' },
      { text: 'Transaction History', icon: <TimelineIcon />, path: '/transactions' },
      { text: 'Financial Reports', icon: <AnalyticsIcon />, path: '/financial-reports' },
      // Priority 4: Analytics & Reporting
      { text: 'Advanced Analytics', icon: <AnalyticsIcon />, path: '/analytics' },
      { text: 'System Analytics', icon: <TrendingUpIcon />, path: '/system-analytics' },
      // Priority 5: Notifications & Communication
      { text: 'Notifications', icon: <NotificationsIcon />, path: '/notifications' },
      { text: 'Bulk Operations', icon: <FilterIcon />, path: '/bulk-operations' },
      // Priority 6: Support & Complaints
      { text: 'Vehicle Assistance', icon: <MaintenanceIcon />, path: '/vehicle-assistance' },
      { text: 'Support Tickets', icon: <SupportIcon />, path: '/support-tickets' },
      { text: 'Complaints', icon: <ReportIcon />, path: '/complaints' },
      { text: 'Refunds & Disputes', icon: <ReceiptIcon />, path: '/refunds-disputes' },
      // Priority 7: System Monitoring
      { text: 'Platform Health', icon: <SecurityIcon />, path: '/platform-health' },
      { text: 'System Settings', icon: <SettingsIcon />, path: '/settings' },
    ] : []),
    ...(isProvider ? [
      { text: 'Fleet Dashboard', icon: <FleetIcon />, path: '/provider-dashboard' },
      { text: 'Revenue Analytics', icon: <RevenueIcon />, path: '/provider-dashboard?tab=2' },
      { text: 'Maintenance', icon: <MaintenanceIcon />, path: '/provider-dashboard?tab=3' },
    ] : []),
    { text: 'Vehicles', icon: <DirectionsBike />, path: '/vehicles' },
    { text: 'Add Vehicle', icon: <AddIcon />, path: '/create-vehicle' },
    { text: 'Bookings', icon: <BookOnline />, path: '/bookings' },
  ]

  const drawer = (
    <div>
      <Toolbar>
        <Typography variant="h6" noWrap component="div" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
          {isProvider ? '🏍️ Fleet Manager' : '🏍️ RentaHub Admin'}
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              onClick={() => {
                navigate(item.path)
                setMobileOpen(false)
              }}
              sx={{
                '&:hover': {
                  backgroundColor: 'primary.light',
                  '& .MuiListItemIcon-root': {
                    color: 'white',
                  },
                  '& .MuiListItemText-root': {
                    color: 'white',
                  },
                },
              }}
            >
              <ListItemIcon sx={{ color: 'primary.main' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  )

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          backgroundColor: 'white',
          color: 'text.primary',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {isProvider ? 'Fleet Management' : 'Admin Panel'}
          </Typography>

          {/* Session Status */}
          <Box sx={{ mr: 2 }}>
            <SessionStatus
              onExtendSession={extendSession}
              onLogout={handleSignOut}
            />
          </Box>

          <IconButton
            size="large"
            edge="end"
            aria-label="account of current user"
            aria-controls="profile-menu"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
              {user?.email?.charAt(0).toUpperCase()}
            </Avatar>
          </IconButton>
          <Menu
            id="profile-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleProfileMenuClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem onClick={handleProfileMenuClose}>
              <ListItemIcon>
                <AccountCircle fontSize="small" />
              </ListItemIcon>
              Profile
            </MenuItem>
            <MenuItem onClick={handleSignOut}>
              <ListItemIcon>
                <Logout fontSize="small" />
              </ListItemIcon>
              Sign Out
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
        }}
      >
        <Outlet />
      </Box>

      {/* Session Warning Dialog */}
      <SessionWarning
        onExtendSession={extendSession}
        onLogout={handleSignOut}
      />
    </Box>
  )
}

export default Layout