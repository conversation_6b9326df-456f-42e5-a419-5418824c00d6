import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  LinearProgress, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent 
} from '@mui/material';
import { 
  Analytics as AnalyticsIcon, 
  Speed as SpeedIcon, 
  Notifications as NotificationsIcon,
  Storage as StorageIcon
} from '@mui/icons-material';

import { 
  fetchAnalytics, 
  fetchPerformanceMetrics, 
  fetchMonitoringData 
} from '../services/AdvancedFeaturesService';

interface AnalyticsData {
  totalBookings: number;
  totalRevenue: number;
  mostPopularVehicles: Array<{
    vehicleType: string;
    bookingCount: number;
  }>;
}

interface PerformanceMetrics {
  apiResponseTime: number;
  databaseQueryTime: number;
  cacheHitRate: number;
}

interface MonitoringData {
  systemHealth: {
    cpuUsage: number;
    memoryUsage: number;
  };
  alerts: Array<{
    type: 'warning' | 'error';
    message: string;
  }>;
}

const AdvancedFeaturesPanel: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [analytics, performance, monitoring] = await Promise.all([
          fetchAnalytics(),
          fetchPerformanceMetrics(),
          fetchMonitoringData()
        ]);

        setAnalyticsData(analytics);
        setPerformanceMetrics(performance);
        setMonitoringData(monitoring);
      } catch (error) {
        console.error('Failed to fetch advanced features data', error);
      }
    };

    fetchData();
    const intervalId = setInterval(fetchData, 60000); // Refresh every minute

    return () => clearInterval(intervalId);
  }, []);

  const renderMetricDetails = () => {
    switch (selectedMetric) {
      case 'analytics':
        return (
          <Box>
            <Typography variant="h6">Booking Analytics</Typography>
            <Typography>Total Bookings: {analyticsData?.totalBookings}</Typography>
            <Typography>Total Revenue: ${analyticsData?.totalRevenue.toFixed(2)}</Typography>
            <Typography>Most Popular Vehicles:</Typography>
            {analyticsData?.mostPopularVehicles.map(vehicle => (
              <Typography key={vehicle.vehicleType}>
                {vehicle.vehicleType}: {vehicle.bookingCount} bookings
              </Typography>
            ))}
          </Box>
        );
      case 'performance':
        return (
          <Box>
            <Typography variant="h6">Performance Metrics</Typography>
            <Typography>API Response Time: {performanceMetrics?.apiResponseTime}ms</Typography>
            <Typography>Database Query Time: {performanceMetrics?.databaseQueryTime}ms</Typography>
            <Typography>Cache Hit Rate: {(performanceMetrics?.cacheHitRate || 0) * 100}%</Typography>
          </Box>
        );
      case 'monitoring':
        return (
          <Box>
            <Typography variant="h6">System Monitoring</Typography>
            <Typography>CPU Usage: {monitoringData?.systemHealth.cpuUsage.toFixed(2)}%</Typography>
            <Typography>Memory Usage: {monitoringData?.systemHealth.memoryUsage.toFixed(2)}%</Typography>
            <Typography variant="subtitle1">Alerts:</Typography>
            {monitoringData?.alerts.map((alert, index) => (
              <Typography 
                key={index} 
                color={alert.type === 'error' ? 'error' : 'warning'}
              >
                {alert.message}
              </Typography>
            ))}
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Advanced Features Dashboard
      </Typography>

      <Grid container spacing={3}>
        {[
          { 
            icon: <AnalyticsIcon />, 
            title: 'Analytics', 
            key: 'analytics' 
          },
          { 
            icon: <SpeedIcon />, 
            title: 'Performance', 
            key: 'performance' 
          },
          { 
            icon: <NotificationsIcon />, 
            title: 'Monitoring', 
            key: 'monitoring' 
          },
          { 
            icon: <StorageIcon />, 
            title: 'Caching', 
            key: 'caching' 
          }
        ].map(({ icon, title, key }) => (
          <Grid item xs={12} md={3} key={key}>
            <Card 
              onClick={() => setSelectedMetric(key)}
              sx={{ 
                cursor: 'pointer', 
                transition: 'transform 0.2s',
                '&:hover': { 
                  transform: 'scale(1.05)' 
                }
              }}
            >
              <CardContent>
                <Box 
                  display="flex" 
                  alignItems="center" 
                  justifyContent="space-between"
                >
                  {icon}
                  <Typography variant="h6">{title}</Typography>
                </Box>
                {key === 'performance' && performanceMetrics && (
                  <LinearProgress 
                    variant="determinate" 
                    value={(performanceMetrics.cacheHitRate || 0) * 100} 
                  />
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog 
        open={!!selectedMetric} 
        onClose={() => setSelectedMetric(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedMetric && selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} Details
        </DialogTitle>
        <DialogContent>
          {renderMetricDetails()}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default AdvancedFeaturesPanel; 