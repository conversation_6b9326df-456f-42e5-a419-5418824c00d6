import React, { useState, useEffect } from 'react';
import { 
    Box, 
    Typography, 
    Select, 
    MenuItem, 
    TextField, 
    Button, 
    FormControl, 
    InputLabel,
    Chip,
    Stack
} from '@mui/material';
import { 
    InsuranceType, 
    DepositType 
} from '../../../../backend/src/services/InsuranceDepositService';
import { apiService } from '../services/apiService';

interface InsuranceDepositConfigProps {
    vehicleId: string;
}

export const VehicleInsuranceDepositToggle: React.FC<InsuranceDepositConfigProps> = ({ vehicleId }) => {
    const [insuranceType, setInsuranceType] = useState<InsuranceType>(InsuranceType.BASIC);
    const [depositType, setDepositType] = useState<DepositType>(DepositType.REFUNDABLE);
    const [coverageAmount, setCoverageAmount] = useState<number>(0);
    const [depositAmount, setDepositAmount] = useState<number>(0);
    const [additionalCoverage, setAdditionalCoverage] = useState<string[]>([]);
    const [newCoverageItem, setNewCoverageItem] = useState<string>('');
    const [refundConditions, setRefundConditions] = useState<string[]>([]);
    const [newRefundCondition, setNewRefundCondition] = useState<string>('');

    useEffect(() => {
        // Fetch existing configurations when component mounts
        const fetchConfigurations = async () => {
            try {
                const [insuranceConfig, depositConfig] = await Promise.all([
                    apiService.get(`/insurance-deposit/insurance/${vehicleId}`),
                    apiService.get(`/insurance-deposit/deposit/${vehicleId}`)
                ]);

                if (insuranceConfig) {
                    setInsuranceType(insuranceConfig.type);
                    setCoverageAmount(insuranceConfig.coverageAmount);
                    setAdditionalCoverage(insuranceConfig.additionalCoverage);
                }

                if (depositConfig) {
                    setDepositType(depositConfig.type);
                    setDepositAmount(depositConfig.amount);
                    setRefundConditions(depositConfig.refundConditions);
                }
            } catch (error) {
                console.error('Error fetching configurations:', error);
            }
        };

        fetchConfigurations();
    }, [vehicleId]);

    const handleSaveConfiguration = async () => {
        try {
            // Save insurance configuration
            await apiService.post('/insurance-deposit/insurance', {
                vehicleId,
                insuranceConfig: {
                    type: insuranceType,
                    coverageAmount,
                    additionalCoverage
                }
            });

            // Save deposit configuration
            await apiService.post('/insurance-deposit/deposit', {
                vehicleId,
                depositConfig: {
                    type: depositType,
                    amount: depositAmount,
                    refundConditions
                }
            });

            alert('Configuration saved successfully!');
        } catch (error) {
            console.error('Error saving configuration:', error);
            alert('Failed to save configuration');
        }
    };

    const addAdditionalCoverage = () => {
        if (newCoverageItem && !additionalCoverage.includes(newCoverageItem)) {
            setAdditionalCoverage([...additionalCoverage, newCoverageItem]);
            setNewCoverageItem('');
        }
    };

    const removeAdditionalCoverage = (item: string) => {
        setAdditionalCoverage(additionalCoverage.filter(coverage => coverage !== item));
    };

    const addRefundCondition = () => {
        if (newRefundCondition && !refundConditions.includes(newRefundCondition)) {
            setRefundConditions([...refundConditions, newRefundCondition]);
            setNewRefundCondition('');
        }
    };

    const removeRefundCondition = (condition: string) => {
        setRefundConditions(refundConditions.filter(c => c !== condition));
    };

    return (
        <Box sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
                Insurance and Deposit Configuration
            </Typography>

            {/* Insurance Configuration */}
            <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Insurance Type</InputLabel>
                <Select
                    value={insuranceType}
                    label="Insurance Type"
                    onChange={(e) => setInsuranceType(e.target.value as InsuranceType)}
                >
                    {Object.values(InsuranceType).map(type => (
                        <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                </Select>
            </FormControl>

            <TextField
                fullWidth
                type="number"
                label="Coverage Amount"
                value={coverageAmount}
                onChange={(e) => setCoverageAmount(Number(e.target.value))}
                sx={{ mb: 2 }}
            />

            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <TextField
                    fullWidth
                    label="Additional Coverage"
                    value={newCoverageItem}
                    onChange={(e) => setNewCoverageItem(e.target.value)}
                />
                <Button variant="contained" onClick={addAdditionalCoverage}>
                    Add Coverage
                </Button>
            </Stack>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {additionalCoverage.map(item => (
                    <Chip 
                        key={item} 
                        label={item} 
                        onDelete={() => removeAdditionalCoverage(item)} 
                    />
                ))}
            </Box>

            {/* Deposit Configuration */}
            <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Deposit Type</InputLabel>
                <Select
                    value={depositType}
                    label="Deposit Type"
                    onChange={(e) => setDepositType(e.target.value as DepositType)}
                >
                    {Object.values(DepositType).map(type => (
                        <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                </Select>
            </FormControl>

            <TextField
                fullWidth
                type="number"
                label="Deposit Amount"
                value={depositAmount}
                onChange={(e) => setDepositAmount(Number(e.target.value))}
                sx={{ mb: 2 }}
            />

            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <TextField
                    fullWidth
                    label="Refund Condition"
                    value={newRefundCondition}
                    onChange={(e) => setNewRefundCondition(e.target.value)}
                />
                <Button variant="contained" onClick={addRefundCondition}>
                    Add Condition
                </Button>
            </Stack>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {refundConditions.map(condition => (
                    <Chip 
                        key={condition} 
                        label={condition} 
                        onDelete={() => removeRefundCondition(condition)} 
                    />
                ))}
            </Box>

            <Button 
                fullWidth 
                variant="contained" 
                color="primary" 
                onClick={handleSaveConfiguration}
            >
                Save Configuration
            </Button>
        </Box>
    );
}; 