import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Block as BlockIcon,
  CheckCircle as ActivateIcon,
} from '@mui/icons-material';
import DataTable, { Column, Action } from './DataTable';
import FilterPanel, { FilterConfig, FilterValues } from './FilterPanel';
import UserService from '../services/UserService';
import * as RentaHubTypes from '../types/rentahub-types';
import * as helper from '../utils/helper';

interface UserListProps {
  userType?: 'all' | 'customers' | 'providers' | 'admins';
  title?: string;
  showFilters?: boolean;
  showActions?: boolean;
  selectable?: boolean;
  onUserSelect?: (user: RentaHubTypes.User) => void;
}

const UserList: React.FC<UserListProps> = ({
  userType = 'all',
  title = 'Users',
  showFilters = true,
  showActions = true,
  selectable = false,
  onUserSelect,
}) => {
  const [users, setUsers] = useState<RentaHubTypes.User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedUsers, setSelectedUsers] = useState<RentaHubTypes.User[]>([]);
  
  // Filter state
  const [filterValues, setFilterValues] = useState<FilterValues>({});
  
  // Dialog state
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<RentaHubTypes.User | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<RentaHubTypes.CreateUserPayload>>({
    email: '',
    name: '',
    role: RentaHubTypes.UserRole.CUSTOMER,
    phoneNumber: '',
  });

  // Define table columns
  const columns: Column<RentaHubTypes.User>[] = [
    {
      id: 'name',
      label: 'User',
      minWidth: 200,
      sortable: true,
      render: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar src={row.avatar} sx={{ width: 40, height: 40 }}>
            {row.name.charAt(0).toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle2">{row.name}</Typography>
            <Typography variant="caption" color="text.secondary">
              {row.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      id: 'role',
      label: 'Role',
      minWidth: 120,
      sortable: true,
      render: (value) => (
        <Chip
          label={helper.getStatusLabel(value)}
          color={value === 'admin' ? 'error' : value === 'provider' ? 'warning' : 'default'}
          size="small"
        />
      ),
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      sortable: true,
      render: (value) => (
        <Chip
          label={helper.getStatusLabel(value)}
          color={helper.getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      id: 'phoneNumber',
      label: 'Phone',
      minWidth: 150,
      render: (value) => value || 'Not provided',
    },
    {
      id: 'emailVerified',
      label: 'Email Verified',
      minWidth: 120,
      align: 'center',
      render: (value) => (
        <Chip
          label={value ? 'Verified' : 'Unverified'}
          color={value ? 'success' : 'warning'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      id: 'createdAt',
      label: 'Created',
      minWidth: 120,
      sortable: true,
      render: (value) => helper.formatDate(value),
    },
    {
      id: 'lastLogin',
      label: 'Last Login',
      minWidth: 120,
      render: (value) => value ? helper.formatDateTime(value) : 'Never',
    },
  ];

  // Define filter configuration
  const filterConfig: FilterConfig[] = [
    {
      id: 'search',
      label: 'Search',
      type: 'text',
      placeholder: 'Search by name or email...',
    },
    {
      id: 'role',
      label: 'Role',
      type: 'multiselect',
      options: [
        { value: 'admin', label: 'Admin' },
        { value: 'provider', label: 'Provider' },
        { value: 'customer', label: 'Customer' },
      ],
    },
    {
      id: 'status',
      label: 'Status',
      type: 'multiselect',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'suspended', label: 'Suspended' },
        { value: 'pending', label: 'Pending' },
      ],
    },
    {
      id: 'emailVerified',
      label: 'Email Verification',
      type: 'select',
      options: [
        { value: 'true', label: 'Verified' },
        { value: 'false', label: 'Unverified' },
      ],
    },
    {
      id: 'createdAt',
      label: 'Registration Date',
      type: 'daterange',
    },
  ];

  // Define actions
  const actions: Action<RentaHubTypes.User>[] = [
    {
      label: 'View Details',
      icon: <ViewIcon />,
      onClick: (user) => {
        setSelectedUser(user);
        onUserSelect?.(user);
      },
    },
    {
      label: 'Edit User',
      icon: <EditIcon />,
      onClick: (user) => {
        setSelectedUser(user);
        setFormData({
          name: user.name,
          phoneNumber: user.phoneNumber,
          role: user.role as RentaHubTypes.UserRole,
          status: user.status as RentaHubTypes.UserStatus,
        });
        setEditDialogOpen(true);
      },
    },
    {
      label: user => user.status === 'active' ? 'Suspend User' : 'Activate User',
      icon: user => user.status === 'active' ? <BlockIcon /> : <ActivateIcon />,
      color: user => user.status === 'active' ? 'warning' : 'success',
      onClick: async (user) => {
        try {
          const newStatus = user.status === 'active' ? 'suspended' : 'active';
          await UserService.updateUser(user.id, { status: newStatus });
          helper.success(`User ${newStatus === 'active' ? 'activated' : 'suspended'} successfully`);
          fetchUsers();
        } catch (error) {
          helper.error(error, 'Failed to update user status');
        }
      },
    },
    {
      label: 'Delete User',
      icon: <DeleteIcon />,
      color: 'error',
      onClick: (user) => {
        setSelectedUser(user);
        setDeleteDialogOpen(true);
      },
      disabled: (user) => user.role === 'admin', // Prevent deleting admin users
    },
  ];

  // Fetch users
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const options = {
        search: filterValues.search,
        sortBy,
        sortOrder,
        filters: {
          role: filterValues.role,
          status: filterValues.status,
          emailVerified: filterValues.emailVerified === 'true' ? true : 
                         filterValues.emailVerified === 'false' ? false : undefined,
          createdAt: filterValues.createdAt,
        },
      };

      let response;
      switch (userType) {
        case 'customers':
          response = await UserService.getCustomers(page + 1, rowsPerPage, options);
          break;
        case 'providers':
          response = await UserService.getProviders(page + 1, rowsPerPage, options);
          break;
        default:
          response = await UserService.getUsers(page + 1, rowsPerPage, options);
      }

      setUsers(response.data);
      setTotalCount(response.total);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Failed to load users. Please try again.');
      helper.error(error, 'Failed to load users');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, sortBy, sortOrder, filterValues, userType]);

  // Effects
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setPage(0);
  };

  const handleFilterChange = (newFilterValues: FilterValues) => {
    setFilterValues(newFilterValues);
    setPage(0);
  };

  const handleCreateUser = async () => {
    try {
      await UserService.createUser(formData as RentaHubTypes.CreateUserPayload);
      helper.success('User created successfully');
      setCreateDialogOpen(false);
      setFormData({
        email: '',
        name: '',
        role: RentaHubTypes.UserRole.CUSTOMER,
        phoneNumber: '',
      });
      fetchUsers();
    } catch (error) {
      helper.error(error, 'Failed to create user');
    }
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;

    try {
      await UserService.updateUser(selectedUser.id, formData as RentaHubTypes.UpdateUserPayload);
      helper.success('User updated successfully');
      setEditDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (error) {
      helper.error(error, 'Failed to update user');
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      await UserService.deleteUser(selectedUser.id);
      helper.success('User deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (error) {
      helper.error(error, 'Failed to delete user');
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {title}
        </Typography>
        {showActions && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Add User
          </Button>
        )}
      </Box>

      {/* Filters */}
      {showFilters && (
        <FilterPanel
          filters={filterConfig}
          values={filterValues}
          onChange={handleFilterChange}
          onReset={() => setFilterValues({})}
          collapsible
          defaultExpanded={false}
        />
      )}

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={users}
        loading={loading}
        error={error}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSortChange={handleSortChange}
        sortBy={sortBy}
        sortOrder={sortOrder}
        selectable={selectable}
        selectedRows={selectedUsers}
        onSelectionChange={setSelectedUsers}
        actions={showActions ? actions : []}
        emptyMessage="No users found"
        onRowClick={onUserSelect}
      />

      {/* Create User Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New User</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Phone Number"
                value={formData.phoneNumber}
                onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as RentaHubTypes.UserRole })}
                >
                  <MenuItem value={RentaHubTypes.UserRole.CUSTOMER}>Customer</MenuItem>
                  <MenuItem value={RentaHubTypes.UserRole.PROVIDER}>Provider</MenuItem>
                  <MenuItem value={RentaHubTypes.UserRole.ADMIN}>Admin</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateUser} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Phone Number"
                value={formData.phoneNumber}
                onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as RentaHubTypes.UserRole })}
                >
                  <MenuItem value={RentaHubTypes.UserRole.CUSTOMER}>Customer</MenuItem>
                  <MenuItem value={RentaHubTypes.UserRole.PROVIDER}>Provider</MenuItem>
                  <MenuItem value={RentaHubTypes.UserRole.ADMIN}>Admin</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as RentaHubTypes.UserStatus })}
                >
                  <MenuItem value={RentaHubTypes.UserStatus.ACTIVE}>Active</MenuItem>
                  <MenuItem value={RentaHubTypes.UserStatus.INACTIVE}>Inactive</MenuItem>
                  <MenuItem value={RentaHubTypes.UserStatus.SUSPENDED}>Suspended</MenuItem>
                  <MenuItem value={RentaHubTypes.UserStatus.PENDING}>Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateUser} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete User</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{selectedUser?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserList;
