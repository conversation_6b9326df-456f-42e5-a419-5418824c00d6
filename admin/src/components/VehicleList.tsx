import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  DirectionsCar as CarIcon,
  LocalGasStation as FuelIcon,
  Settings as GearIcon,
  EventSeat as SeatIcon,
  AcUnit as AcIcon,
} from '@mui/icons-material';
import DataTable, { Column, Action } from './DataTable';
import FilterPanel, { FilterConfig, FilterValues } from './FilterPanel';
import VehicleService from '../services/VehicleService';
import * as RentaHubTypes from '../types/rentahub-types';
import * as helper from '../utils/helper';

interface VehicleListProps {
  providerId?: string;
  title?: string;
  showFilters?: boolean;
  showActions?: boolean;
  selectable?: boolean;
  onVehicleSelect?: (vehicle: RentaHubTypes.Vehicle) => void;
}

const VehicleList: React.FC<VehicleListProps> = ({
  providerId,
  title = 'Vehicles',
  showFilters = true,
  showActions = true,
  selectable = false,
  onVehicleSelect,
}) => {
  const [vehicles, setVehicles] = useState<RentaHubTypes.Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedVehicles, setSelectedVehicles] = useState<RentaHubTypes.Vehicle[]>([]);
  
  // Filter state
  const [filterValues, setFilterValues] = useState<FilterValues>({});
  
  // Dialog state
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<RentaHubTypes.Vehicle | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Form state
  const [formData, setFormData] = useState<Partial<RentaHubTypes.CreateVehiclePayload>>({
    providerId: providerId || '',
    make: '',
    model: '',
    year: new Date().getFullYear(),
    type: RentaHubTypes.VehicleType.CAR,
    licensePlate: '',
    fuelType: RentaHubTypes.FuelType.GASOLINE,
    gearbox: RentaHubTypes.GearboxType.MANUAL,
    seats: 4,
    doors: 4,
    airConditioning: true,
    dailyRate: 0,
    deposit: 0,
    features: [],
    minimumRentalDays: 1,
  });

  // Define table columns
  const columns: Column<RentaHubTypes.Vehicle>[] = [
    {
      id: 'make',
      label: 'Vehicle',
      minWidth: 250,
      sortable: true,
      render: (value, row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {row.images && row.images.length > 0 ? (
            <Avatar
              src={row.images[0]}
              variant="rounded"
              sx={{ width: 60, height: 40 }}
            />
          ) : (
            <Avatar variant="rounded" sx={{ width: 60, height: 40 }}>
              <CarIcon />
            </Avatar>
          )}
          <Box>
            <Typography variant="subtitle2">
              {row.make} {row.model} ({row.year})
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {row.licensePlate} • {helper.getStatusLabel(row.type)}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      id: 'provider',
      label: 'Provider',
      minWidth: 150,
      render: (value, row) => (
        <Box>
          <Typography variant="body2">{row.provider?.name || 'Unknown'}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.provider?.email}
          </Typography>
        </Box>
      ),
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      sortable: true,
      render: (value) => (
        <Chip
          label={helper.getStatusLabel(value)}
          color={helper.getStatusColor(value)}
          size="small"
        />
      ),
    },
    {
      id: 'dailyRate',
      label: 'Daily Rate',
      minWidth: 120,
      sortable: true,
      align: 'right',
      render: (value) => helper.formatCurrency(value),
    },
    {
      id: 'fuelType',
      label: 'Fuel & Transmission',
      minWidth: 150,
      render: (value, row) => (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
            <FuelIcon fontSize="small" />
            <Typography variant="caption">
              {helper.getStatusLabel(row.fuelType)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <GearIcon fontSize="small" />
            <Typography variant="caption">
              {helper.getStatusLabel(row.gearbox)}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      id: 'seats',
      label: 'Specs',
      minWidth: 120,
      render: (value, row) => (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
            <SeatIcon fontSize="small" />
            <Typography variant="caption">{row.seats} seats</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <AcIcon fontSize="small" />
            <Typography variant="caption">
              {row.airConditioning ? 'A/C' : 'No A/C'}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      id: 'totalBookings',
      label: 'Bookings',
      minWidth: 100,
      align: 'center',
      sortable: true,
      render: (value, row) => (
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h6">{value || 0}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.rating ? `${row.rating.toFixed(1)}★` : 'No rating'}
          </Typography>
        </Box>
      ),
    },
    {
      id: 'createdAt',
      label: 'Added',
      minWidth: 120,
      sortable: true,
      render: (value) => helper.formatDate(value),
    },
  ];

  // Define filter configuration
  const filterConfig: FilterConfig[] = [
    {
      id: 'search',
      label: 'Search',
      type: 'text',
      placeholder: 'Search by make, model, or license plate...',
    },
    {
      id: 'type',
      label: 'Vehicle Type',
      type: 'multiselect',
      options: Object.values(RentaHubTypes.VehicleType).map(type => ({
        value: type,
        label: helper.getStatusLabel(type),
      })),
    },
    {
      id: 'status',
      label: 'Status',
      type: 'multiselect',
      options: Object.values(RentaHubTypes.VehicleStatus).map(status => ({
        value: status,
        label: helper.getStatusLabel(status),
      })),
    },
    {
      id: 'fuelType',
      label: 'Fuel Type',
      type: 'multiselect',
      options: Object.values(RentaHubTypes.FuelType).map(fuel => ({
        value: fuel,
        label: helper.getStatusLabel(fuel),
      })),
    },
    {
      id: 'gearbox',
      label: 'Transmission',
      type: 'multiselect',
      options: Object.values(RentaHubTypes.GearboxType).map(gearbox => ({
        value: gearbox,
        label: helper.getStatusLabel(gearbox),
      })),
    },
    {
      id: 'priceRange',
      label: 'Daily Rate Range',
      type: 'slider',
      min: 0,
      max: 500,
      step: 10,
      defaultValue: [0, 500],
    },
    {
      id: 'seats',
      label: 'Seats',
      type: 'select',
      options: [
        { value: 2, label: '2 seats' },
        { value: 4, label: '4 seats' },
        { value: 5, label: '5 seats' },
        { value: 7, label: '7+ seats' },
      ],
    },
  ];

  // Define actions
  const actions: Action<RentaHubTypes.Vehicle>[] = [
    {
      label: 'View Details',
      icon: <ViewIcon />,
      onClick: (vehicle) => {
        setSelectedVehicle(vehicle);
        onVehicleSelect?.(vehicle);
      },
    },
    {
      label: 'Edit Vehicle',
      icon: <EditIcon />,
      onClick: (vehicle) => {
        setSelectedVehicle(vehicle);
        setFormData({
          make: vehicle.make,
          model: vehicle.model,
          year: vehicle.year,
          type: vehicle.type as RentaHubTypes.VehicleType,
          dailyRate: vehicle.dailyRate,
          deposit: vehicle.deposit,
          features: vehicle.features,
          description: vehicle.description,
          airConditioning: vehicle.airConditioning,
          minimumRentalDays: vehicle.minimumRentalDays,
        });
        setEditDialogOpen(true);
      },
    },
    {
      label: vehicle => vehicle.status === 'available' ? 'Mark Unavailable' : 'Mark Available',
      icon: vehicle => vehicle.status === 'available' ? <DeleteIcon /> : <ViewIcon />,
      color: vehicle => vehicle.status === 'available' ? 'warning' : 'success',
      onClick: async (vehicle) => {
        try {
          const newStatus = vehicle.status === 'available' ? 'maintenance' : 'available';
          await VehicleService.updateVehicle(vehicle.id, { status: newStatus });
          helper.success(`Vehicle marked as ${newStatus}`);
          fetchVehicles();
        } catch (error) {
          helper.error(error, 'Failed to update vehicle status');
        }
      },
    },
    {
      label: 'Delete Vehicle',
      icon: <DeleteIcon />,
      color: 'error',
      onClick: (vehicle) => {
        setSelectedVehicle(vehicle);
        setDeleteDialogOpen(true);
      },
    },
  ];

  // Fetch vehicles
  const fetchVehicles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const options = {
        search: filterValues.search,
        sortBy,
        sortOrder,
        filters: {
          type: filterValues.type,
          status: filterValues.status,
          fuelType: filterValues.fuelType,
          gearbox: filterValues.gearbox,
          minPrice: filterValues.priceRange?.[0],
          maxPrice: filterValues.priceRange?.[1],
          seats: filterValues.seats,
        },
      };

      let response;
      if (providerId) {
        response = await VehicleService.getProviderVehicles(providerId, page + 1, rowsPerPage, options);
      } else {
        response = await VehicleService.getVehicles(page + 1, rowsPerPage, options);
      }

      setVehicles(response.data);
      setTotalCount(response.total);
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      setError('Failed to load vehicles. Please try again.');
      helper.error(error, 'Failed to load vehicles');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, sortBy, sortOrder, filterValues, providerId]);

  // Effects
  useEffect(() => {
    fetchVehicles();
  }, [fetchVehicles]);

  // Handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setPage(0);
  };

  const handleFilterChange = (newFilterValues: FilterValues) => {
    setFilterValues(newFilterValues);
    setPage(0);
  };

  const handleCreateVehicle = async () => {
    try {
      await VehicleService.createVehicle(formData as RentaHubTypes.CreateVehiclePayload);
      helper.success('Vehicle created successfully');
      setCreateDialogOpen(false);
      setFormData({
        providerId: providerId || '',
        make: '',
        model: '',
        year: new Date().getFullYear(),
        type: RentaHubTypes.VehicleType.CAR,
        licensePlate: '',
        fuelType: RentaHubTypes.FuelType.GASOLINE,
        gearbox: RentaHubTypes.GearboxType.MANUAL,
        seats: 4,
        doors: 4,
        airConditioning: true,
        dailyRate: 0,
        deposit: 0,
        features: [],
        minimumRentalDays: 1,
      });
      fetchVehicles();
    } catch (error) {
      helper.error(error, 'Failed to create vehicle');
    }
  };

  const handleUpdateVehicle = async () => {
    if (!selectedVehicle) return;

    try {
      await VehicleService.updateVehicle(selectedVehicle.id, formData as RentaHubTypes.UpdateVehiclePayload);
      helper.success('Vehicle updated successfully');
      setEditDialogOpen(false);
      setSelectedVehicle(null);
      fetchVehicles();
    } catch (error) {
      helper.error(error, 'Failed to update vehicle');
    }
  };

  const handleDeleteVehicle = async () => {
    if (!selectedVehicle) return;

    try {
      await VehicleService.deleteVehicle(selectedVehicle.id);
      helper.success('Vehicle deleted successfully');
      setDeleteDialogOpen(false);
      setSelectedVehicle(null);
      fetchVehicles();
    } catch (error) {
      helper.error(error, 'Failed to delete vehicle');
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {title}
        </Typography>
        {showActions && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Add Vehicle
          </Button>
        )}
      </Box>

      {/* Filters */}
      {showFilters && (
        <FilterPanel
          filters={filterConfig}
          values={filterValues}
          onChange={handleFilterChange}
          onReset={() => setFilterValues({})}
          collapsible
          defaultExpanded={false}
        />
      )}

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={vehicles}
        loading={loading}
        error={error}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSortChange={handleSortChange}
        sortBy={sortBy}
        sortOrder={sortOrder}
        selectable={selectable}
        selectedRows={selectedVehicles}
        onSelectionChange={setSelectedVehicles}
        actions={showActions ? actions : []}
        emptyMessage="No vehicles found"
        onRowClick={onVehicleSelect}
      />

      {/* Create Vehicle Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Vehicle</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Make"
                value={formData.make}
                onChange={(e) => setFormData({ ...formData, make: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Model"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Year"
                type="number"
                value={formData.year}
                onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="License Plate"
                value={formData.licensePlate}
                onChange={(e) => setFormData({ ...formData, licensePlate: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Vehicle Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Vehicle Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as RentaHubTypes.VehicleType })}
                >
                  {Object.values(RentaHubTypes.VehicleType).map(type => (
                    <MenuItem key={type} value={type}>
                      {helper.getStatusLabel(type)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Fuel Type</InputLabel>
                <Select
                  value={formData.fuelType}
                  label="Fuel Type"
                  onChange={(e) => setFormData({ ...formData, fuelType: e.target.value as RentaHubTypes.FuelType })}
                >
                  {Object.values(RentaHubTypes.FuelType).map(fuel => (
                    <MenuItem key={fuel} value={fuel}>
                      {helper.getStatusLabel(fuel)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Daily Rate"
                type="number"
                value={formData.dailyRate}
                onChange={(e) => setFormData({ ...formData, dailyRate: parseFloat(e.target.value) })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Security Deposit"
                type="number"
                value={formData.deposit}
                onChange={(e) => setFormData({ ...formData, deposit: parseFloat(e.target.value) })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.airConditioning}
                    onChange={(e) => setFormData({ ...formData, airConditioning: e.target.checked })}
                  />
                }
                label="Air Conditioning"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateVehicle} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Vehicle Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Vehicle</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Make"
                value={formData.make}
                onChange={(e) => setFormData({ ...formData, make: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Model"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Daily Rate"
                type="number"
                value={formData.dailyRate}
                onChange={(e) => setFormData({ ...formData, dailyRate: parseFloat(e.target.value) })}
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Security Deposit"
                type="number"
                value={formData.deposit}
                onChange={(e) => setFormData({ ...formData, deposit: parseFloat(e.target.value) })}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.airConditioning}
                    onChange={(e) => setFormData({ ...formData, airConditioning: e.target.checked })}
                  />
                }
                label="Air Conditioning"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateVehicle} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Vehicle</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedVehicle?.make} {selectedVehicle?.model}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteVehicle} color="error" variant="contained">Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VehicleList;
