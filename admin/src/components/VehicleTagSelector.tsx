import React from 'react';
import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import { PREDEFINED_SMART_TAGS } from '../models/VehicleForm';

interface VehicleTagSelectorProps {
  selectedTags: string[];
  onChange: (tags: string[]) => void;
}

export const VehicleTagSelector: React.FC<VehicleTagSelectorProps> = ({ 
  selectedTags, 
  onChange 
}) => {
  const handleTagChange = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    // Limit to 7 tags
    onChange(newTags.slice(0, 7));
  };

  return (
    <FormGroup>
      {PREDEFINED_SMART_TAGS.map(tag => (
        <FormControlLabel
          key={tag}
          control={
            <Checkbox
              checked={selectedTags.includes(tag)}
              onChange={() => handleTagChange(tag)}
              disabled={
                !selectedTags.includes(tag) && selectedTags.length >= 7
              }
            />
          }
          label={tag}
        />
      ))}
    </FormGroup>
  );
};

export default VehicleTagSelector;
