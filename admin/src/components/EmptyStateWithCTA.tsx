import React from 'react';
import {
  Box,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Stack
} from '@mui/material';
import {
  Add as AddIcon,
  CloudUpload as UploadIcon,
  GetApp as ImportIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface EmptyStateWithCTAProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  helpText?: string;
}

const EmptyStateWithCTA: React.FC<EmptyStateWithCTAProps> = ({
  title,
  description,
  icon,
  primaryAction,
  secondaryAction,
  helpText
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '400px',
        p: 4,
        textAlign: 'center'
      }}
    >
      <Card sx={{ maxWidth: 600, width: '100%' }}>
        <CardContent sx={{ p: 4 }}>
          {icon && (
            <Box sx={{ mb: 3, color: 'text.secondary' }}>
              {icon}
            </Box>
          )}
          
          <Typography variant="h5" component="h2" gutterBottom>
            {title}
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            {description}
          </Typography>

          <Stack direction="row" spacing={2} justifyContent="center" sx={{ mb: 3 }}>
            {primaryAction && (
              <Button
                variant="contained"
                size="large"
                startIcon={primaryAction.icon || <AddIcon />}
                onClick={primaryAction.onClick}
              >
                {primaryAction.label}
              </Button>
            )}
            
            {secondaryAction && (
              <Button
                variant="outlined"
                size="large"
                startIcon={secondaryAction.icon || <ImportIcon />}
                onClick={secondaryAction.onClick}
              >
                {secondaryAction.label}
              </Button>
            )}
          </Stack>

          {helpText && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 1,
                p: 2,
                backgroundColor: 'info.light',
                borderRadius: 1,
                color: 'info.contrastText'
              }}
            >
              <InfoIcon fontSize="small" />
              <Typography variant="body2">
                {helpText}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default EmptyStateWithCTA;
