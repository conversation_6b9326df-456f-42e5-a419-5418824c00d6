// <PERSON>ript to create auto-confirmed superuser
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createConfirmedSuperuser() {
  try {
    console.log('Creating auto-confirmed superuser...');
    
    // Create superuser with a valid email
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'Password1234',
      options: {
        data: {
          full_name: 'Super Admin',
          first_name: 'Super',
          last_name: 'Ad<PERSON>',
          role: 'ADMIN'
        }
      }
    });

    if (error) {
      console.error('Error creating superuser:', error);
      
      // If user already exists, that's fine
      if (error.message.includes('already registered')) {
        console.log('User already exists, trying to sign in...');
        
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'Password1234'
        });
        
        if (signInError) {
          console.error('Sign in error:', signInError);
        } else {
          console.log('✅ User can sign in successfully!');
        }
      }
      return;
    }

    console.log('✅ Superuser created!');
    console.log('Email: <EMAIL>');
    console.log('Password: Password1234');
    console.log('\nNow I will confirm the email programmatically...');

    // The user will still need email confirmation, so let's provide instructions
    console.log('\n⚠️  You still need to confirm the email in Supabase Dashboard:');
    console.log('1. Go to: https://supabase.com/dashboard/project/rocxjzukyqelvuyltrfq');
    console.log('2. Navigate to: Authentication → Users');
    console.log('3. Find user: <EMAIL>');
    console.log('4. Click the three dots (...) and select "Confirm email"');

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

createConfirmedSuperuser();
