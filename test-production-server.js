#!/usr/bin/env node

// =============================================================================
// PRODUCTION SERVER TEST SCRIPT
// =============================================================================
// Quick test to verify the production server is working

const http = require('http');

const BASE_URL = 'http://localhost:3001';

async function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: jsonBody
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing RentaHub Production Server');
  console.log('=====================================');

  const tests = [
    {
      name: 'Health Check',
      path: '/health',
      expectedStatus: 200
    },
    {
      name: 'API Status',
      path: '/api/status',
      expectedStatus: 200
    },
    {
      name: 'API Root',
      path: '/api',
      expectedStatus: 200
    },
    {
      name: 'Vehicle Search',
      path: '/api/vehicles',
      expectedStatus: 200
    },
    {
      name: 'Vehicle Categories',
      path: '/api/vehicles/categories',
      expectedStatus: 200
    },
    {
      name: 'Price Ranges',
      path: '/api/vehicles/price-ranges',
      expectedStatus: 200
    }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`\n🔍 Testing: ${test.name}`);
      console.log(`   Endpoint: ${test.path}`);
      
      const result = await testEndpoint(test.path);
      
      if (result.status === test.expectedStatus) {
        console.log(`   ✅ PASS - Status: ${result.status}`);
        if (result.data && typeof result.data === 'object') {
          console.log(`   📊 Response: ${JSON.stringify(result.data).substring(0, 100)}...`);
        }
        passed++;
      } else {
        console.log(`   ❌ FAIL - Expected: ${test.expectedStatus}, Got: ${result.status}`);
        console.log(`   📊 Response: ${JSON.stringify(result.data)}`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ ERROR - ${error.message}`);
      failed++;
    }
  }

  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Production server is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the server logs for more details.');
  }
}

// Run tests if server is available
console.log('🔍 Checking if production server is running...');

testEndpoint('/health')
  .then(() => {
    console.log('✅ Server is running, starting tests...\n');
    runTests();
  })
  .catch(() => {
    console.log('❌ Server is not running. Please start it with:');
    console.log('   cd backend && npm run dev:production');
    console.log('   or');
    console.log('   cd backend && npm run build && npm start');
  });
