# 🚀 Railway-Supabase Database Connection Fix

## 🔍 Problem Analysis

The RentaHub backend is failing to connect to Supabase from Railway due to network connectivity issues. This is a common problem when deploying to Railway with external databases.

## 🛠️ Solution Steps

### Step 1: Update Railway Environment Variables

Add these environment variables to your Railway service:

```bash
# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require&ssl=true&connection_limit=5&pool_timeout=20
DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
SHADOW_DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require

# Server Configuration
NODE_ENV=production
PORT=8080

# Authentication
JWT_SECRET=rentahub_jwt_secret_2025_production

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51O8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
STRIPE_PUBLISHABLE_KEY=pk_test_51O8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

# Supabase Configuration
SUPABASE_URL=https://rocxjzukyqelvuyltrfq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://rentahub-front-end-production.up.railway.app,https://rentahub-admin-production.up.railway.app
FRONTEND_URL=https://rentahub-front-end-production.up.railway.app
ADMIN_URL=https://rentahub-admin-production.up.railway.app
```

### Step 2: Configure Supabase IP Whitelist

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `rocxjzukyqelvuyltrfq`
3. Go to **Settings** → **Database**
4. Scroll down to **Connection Pooling**
5. Add Railway IP ranges to the whitelist:
   ```
   0.0.0.0/0
   ```
   (This allows all IPs - for production, you should use specific Railway IP ranges)

### Step 3: Enable Supabase Connection Pooling

1. In Supabase Dashboard → **Settings** → **Database**
2. Enable **Connection Pooling**
3. Note the connection pooler URL (usually port 6543)
4. Update DATABASE_URL to use the pooler:

```bash
# Alternative: Use connection pooler
DATABASE_URL=postgresql://postgres:<EMAIL>:6543/postgres?sslmode=require&pgbouncer=true
```

### Step 4: Test the Connection

Run the connection test script:

```bash
cd backend
npm run test-railway
```

### Step 5: Deploy the Fix

```bash
# Deploy to Railway
railway up

# Check logs
railway logs --tail
```

## 🔧 Alternative Solutions

### Option A: Use Direct Connection
If connection pooling doesn't work, try direct connection:

```bash
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require
```

### Option B: Use Prisma with Connection Pooling
Update your Prisma schema to handle connection pooling:

```prisma
datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  directUrl         = env("DIRECT_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}
```

### Option C: Railway-Specific Database
Consider using Railway's PostgreSQL service instead of Supabase:

1. Create a new PostgreSQL service in Railway
2. Update DATABASE_URL to use Railway's database
3. Run migrations: `npx prisma migrate deploy`

## 🚨 Troubleshooting

### Common Issues:

1. **Connection Timeout**
   - Increase timeout in DATABASE_URL: `&pool_timeout=30`
   - Check if Supabase database is paused

2. **SSL Errors**
   - Ensure `sslmode=require` is in DATABASE_URL
   - Add `ssl=true` parameter

3. **Authentication Errors**
   - Verify password in DATABASE_URL
   - Check if Supabase credentials are correct

4. **Network Issues**
   - Railway may have outbound network restrictions
   - Contact Railway support if issues persist

### Debug Commands:

```bash
# Test connection locally
npm run test-railway

# Fix connection issues
npm run fix-railway-db

# Check Railway logs
railway logs --tail

# Check Railway variables
railway variables
```

## 📊 Monitoring

After deployment, monitor these endpoints:

- Health Check: `https://rentahub-backend-fixed-production.up.railway.app/health`
- Database Health: `https://rentahub-backend-fixed-production.up.railway.app/health/db`
- API Test: `https://rentahub-backend-fixed-production.up.railway.app/test`

## ✅ Success Criteria

The deployment is successful when:

1. ✅ Health check returns 200 OK
2. ✅ Database health check returns 200 OK
3. ✅ API endpoints respond correctly
4. ✅ No connection errors in Railway logs
5. ✅ Application functions normally

## 🔄 Rollback Plan

If the fix doesn't work:

1. Revert DATABASE_URL to original format
2. Disable connection pooling in Supabase
3. Use Railway's PostgreSQL service as fallback
4. Contact Railway support for network issues

---

**Last Updated**: July 8, 2025
**Status**: 🔧 In Progress
**Next Action**: Deploy updated environment variables to Railway 