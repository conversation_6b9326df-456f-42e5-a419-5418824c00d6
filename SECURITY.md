# Security Configuration Guide

## ⚠️ IMPORTANT: Credential Rotation Required

If you have previously committed .env files to this repository, you MUST rotate all sensitive credentials immediately:

### 1. Database Credentials (Supabase)
- [ ] Generate new Supabase project or rotate keys
- [ ] Update `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`

### 2. Stripe Credentials
- [ ] Rotate Stripe secret keys in Stripe Dashboard
- [ ] Generate new webhook endpoints and secrets
- [ ] Update `STRIPE_SECRET_KEY`, `STRIPE_PUBLISHABLE_KEY`, `STRIPE_WEBHOOK_SECRET`

### 3. Authentication Secrets
- [ ] Generate new JWT secret (use: `openssl rand -base64 64`)
- [ ] Update `JWT_SECRET`

### 4. Email Credentials
- [ ] Rotate SMTP passwords/app passwords
- [ ] Update `SMTP_USER`, `SMTP_PASS`

### 5. API Keys and External Services
- [ ] Rotate any third-party API keys
- [ ] Update monitoring and analytics tokens

## Environment Variables Setup

### Backend (.env)
Copy `backend/.env.example` to `backend/.env` and fill in your values:

```bash
cp backend/.env.example backend/.env
```

### Frontend (.env)
Copy `frontend/.env.example` to `frontend/.env` and fill in your values:

```bash
cp frontend/.env.example frontend/.env
```

## Security Best Practices

1. **Never commit .env files** - They are now properly gitignored
2. **Use different credentials for each environment** (dev, staging, prod)
3. **Rotate credentials regularly** (every 90 days minimum)
4. **Use environment-specific secrets management** in production
5. **Monitor for credential leaks** using tools like GitGuardian

## Production Deployment

For production environments, use your platform's secret management:

### Railway
Set environment variables in Railway dashboard under your service settings.

### Vercel
Use Vercel environment variables in project settings.

### AWS/GCP/Azure
Use their respective secret management services (AWS Secrets Manager, Google Secret Manager, Azure Key Vault).

## Emergency Response

If credentials are compromised:

1. **Immediately rotate all affected credentials**
2. **Review access logs for unauthorized usage**
3. **Update all deployment environments**
4. **Monitor for any suspicious activity**
5. **Consider enabling additional security measures** (2FA, IP restrictions)

## Contact

For security concerns, please contact the development team immediately.