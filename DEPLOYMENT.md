# 🚀 RentaHub Deployment Guide

## Quick Start for Testing

### 🎯 **What You Need To Do:**

1. **Create GitHub Repository**
   - Go to [GitHub.com](https://github.com)
   - Click "New Repository"
   - Name: `rentahub-platform`
   - Make it **Public** (free deployments require public repos)
   - Don't initialize with README (we have files already)

2. **Push Code to GitHub**
   ```bash
   cd /Users/<USER>/Desktop/RENTAHUB
   git init
   git add .
   git commit -m "Initial RentaHub platform setup"
   git branch -M main
   git remote add origin https://github.com/YOUR_USERNAME/rentahub-platform.git
   git push -u origin main
   ```

### 🚂 **Railway Deployment**

1. **Install Railway CLI and Login**
   ```bash
   npm i -g @railway/cli
   railway login
   ```

2. **Setup Railway Project**
   - Go to [railway.app](https://railway.app)
   - Create a new project or use an existing one
   - Link your GitHub repository

3. **Deploy Backend**
   ```bash
   cd backend
   railway link  # Link to your Railway project
   railway up    # Deploy the service
   ```

4. **Configure Environment Variables in Railway Dashboard**
   Add these in Railway dashboard → Variables:
   ```
   NODE_ENV=production
   DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
   PORT=8080
   JWT_SECRET=your_jwt_secret
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   EMAIL_SERVICE_API_KEY=your_email_service_api_key
   FRONTEND_URL=https://your-frontend-domain.railway.app
   ```

5. **Deploy Frontend**
   ```bash
   cd frontend
   railway link  # Link to your Railway project
   railway up    # Deploy the service
   ```

6. **Configure Frontend Environment Variables**
   ```
   VITE_API_URL=https://your-backend-domain.railway.app
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

7. **Deploy Admin Dashboard**
   ```bash
   cd admin
   railway link  # Link to your Railway project
   railway up    # Deploy the service
   ```

## 🔗 **After Deployment**

You'll get URLs like:
- **Backend**: `https://rentahub-backend-production.up.railway.app`
- **Frontend**: `https://rentahub-frontend-production.up.railway.app`
- **Admin**: `https://rentahub-admin-production.up.railway.app`

## 🧪 **Testing Your Deployed App**

1. **Check Backend**: Visit `https://rentahub-backend-production.up.railway.app/health`
2. **Check Frontend**: Visit your frontend URL
3. **Check Admin Dashboard**: Visit your admin URL
4. **Test Features**:
   - User registration/login
   - Browse vehicles (if any exist)
   - Admin panel functionality
   - Responsive design on mobile

## 🆘 **Troubleshooting**

### Common Issues:
1. **Build Fails**: Check Node version is set to 20
2. **API Not Working**: Verify environment variables are set correctly
3. **CORS Errors**: Make sure backend CORS includes your frontend and admin URLs
4. **Health Check Fails**: Ensure your backend is properly configured with a /health endpoint
5. **Database Connection Issues**: Verify DATABASE_URL is correct and accessible

### Quick Fixes:
```bash
# Update backend CORS to include production URLs
# In backend/src/index.ts or server.ts, add your frontend and admin URLs to cors origin array
```

### Checking Logs
```bash
railway logs
```

## 🎉 **You're Live!**

Once deployed, you'll have:
- ✅ Professional URLs for frontend, backend, and admin
- ✅ Automatic deploys on every Git push
- ✅ Scalable hosting with Railway
- ✅ SSL certificates included
- ✅ Ready to share with users and investors

---

# 🌐 PRODUCTION DEPLOYMENT WITH RENTAHUB.INFO

## 📋 Prerequisites for Production

### 1. Domain Setup
- ✅ Domain registered: `rentahub.info`
- ⏳ DNS configuration (see DNS Setup section below)

### 2. Required Accounts & Tools
- [Railway Account](https://railway.app) - For hosting
- [Railway CLI](https://docs.railway.app/develop/cli) - For deployment
- Domain registrar access - For DNS configuration

## 🌐 DNS Configuration

Configure these DNS records at your domain registrar:

```dns
Type    Name    Value                           TTL
CNAME   @       your-frontend-app.railway.app   300
CNAME   api     your-backend-app.railway.app    300
CNAME   admin   your-admin-app.railway.app      300
CNAME   www     your-frontend-app.railway.app   300
```

**Note:** Replace `your-*-app.railway.app` with actual Railway app URLs after deployment.

## 🏗️ Railway Project Structure

RentaHub uses a **3-service architecture**:

1. **Frontend Service** (`rentahub.info`)
   - React application
   - Serves main website

2. **Backend Service** (`api.rentahub.info`)
   - Node.js API server
   - Handles all API requests

3. **Admin Service** (`admin.rentahub.info`)
   - Admin dashboard
   - Management interface

## 🚀 Production Deployment Methods

### Method 1: Automated Script (Recommended)

```bash
# Deploy all services
./deploy.sh all

# Deploy individual services
./deploy.sh backend
./deploy.sh frontend
./deploy.sh admin

# Check deployment status
./deploy.sh status
```

### Method 2: Manual Production Deployment

#### Step 1: Deploy Backend API
```bash
cd backend
railway login
railway init
railway up
railway domain api.rentahub.info
```

#### Step 2: Deploy Frontend
```bash
cd frontend
railway init
railway up
railway domain rentahub.info
railway domain www.rentahub.info  # Optional redirect
```

#### Step 3: Deploy Admin Panel
```bash
cd admin
railway init
railway up
railway domain admin.rentahub.info
```

## ⚙️ Production Environment Variables

All environment variables are already configured in the `.env` files. In Railway dashboard, set:

### Backend Environment Variables
```bash
# All values from backend/.env
NODE_ENV=production
FRONTEND_URL=https://rentahub.info
BACKEND_URL=https://api.rentahub.info
ADMIN_URL=https://admin.rentahub.info
CORS_ORIGIN=https://rentahub.info,https://admin.rentahub.info
```

### Frontend Environment Variables
```bash
# All values from frontend/.env
VITE_API_URL=https://api.rentahub.info
VITE_APP_URL=https://rentahub.info
VITE_ADMIN_URL=https://admin.rentahub.info
```

### Admin Environment Variables
```bash
# All values from admin/.env
VITE_API_URL=https://api.rentahub.info
VITE_APP_URL=https://rentahub.info
VITE_ADMIN_URL=https://admin.rentahub.info
VITE_FRONTEND_URL=https://rentahub.info
```

## 🔧 Post-Production Configuration

### 1. SSL Certificates
Railway automatically provides SSL certificates. Verify HTTPS works:
- ✅ https://rentahub.info
- ✅ https://api.rentahub.info
- ✅ https://admin.rentahub.info

### 2. Google OAuth Configuration
Update Google OAuth redirect URIs:
- `https://rentahub.info/auth/google/callback`
- `https://admin.rentahub.info/auth/google/callback`

### 3. Stripe Webhook Configuration
Update Stripe webhook endpoint:
- `https://api.rentahub.info/webhook/stripe`

## 🎉 Production Success!

Once deployed, your RentaHub platform will be available at:
- **Main Site**: https://rentahub.info
- **API**: https://api.rentahub.info
- **Admin**: https://admin.rentahub.info

**Need help?** The deployment configurations are already created in your project files (`backend/railway.toml`).