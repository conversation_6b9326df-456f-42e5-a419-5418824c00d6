const { createClient } = require('@supabase/supabase-js');

// Supabase credentials from frontend/.env
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function grantPermissions() {
  console.log('🔐 Granting SELECT permissions to anon role...');
  
  try {
    // Execute the GRANT command using Supabase's RPC
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: 'grant select on table public.vehicles to anon;'
    });

    if (error) {
      console.error('❌ Error granting permissions:', error);
      
      // Alternative: Try direct SQL execution
      console.log('🔄 Trying alternative method...');
      const { data: testData, error: testError } = await supabase
        .from('vehicles')
        .select('count')
        .limit(1);
        
      if (testError) {
        console.log('❌ Still no access. You need to run this SQL in Supabase dashboard:');
        console.log('grant select on table public.vehicles to anon;');
      } else {
        console.log('✅ Permissions already granted or working!');
      }
    } else {
      console.log('✅ Permissions granted successfully!');
    }
    
  } catch (error) {
    console.error('❌ Failed to execute SQL:', error);
    console.log('\n📋 Please run this SQL in your Supabase dashboard:');
    console.log('grant select on table public.vehicles to anon;');
  }
}

// Run the script
grantPermissions(); 