# 🛠️ RentaHub Developer Guide

## Quick Start
```bash
# Start both servers
cd /Users/<USER>/Desktop/RENTAHUB

# Backend (Terminal 1)
cd backend && npm run dev

# Frontend (Terminal 2)  
cd frontend && npm run dev

# Open app: http://localhost:5173
```

## 🏗️ Architecture Overview

### Frontend (`/frontend`)
- **Framework**: React + TypeScript + Vite
- **UI Library**: Material-UI (MUI) 
- **Routing**: React Router
- **State**: React hooks + Context
- **Maps**: Google Maps API
- **Payments**: Stripe
- **Database**: Supabase

### Backend (`/backend`)
- **Framework**: Node.js + Express + TypeScript
- **Database**: Prisma + PostgreSQL
- **Auth**: JWT + bcrypt
- **Payments**: Stripe webhooks
- **Email**: Nodemailer
- **Deployment**: Ready for Railway/Render

## 📁 Key Files & Components

### Frontend Core Files:
- `src/services/apiService.ts` - Complete API integration
- `src/components/BookingFlow.tsx` - Full booking process
- `src/components/MapVehicleSearch.tsx` - Vehicle search with maps
- `src/components/HealthCheck.tsx` - System health monitoring
- `src/pages/HomePage.tsx` - Main landing page
- `src/pages/SearchPage.tsx` - Vehicle search interface
- `src/pages/VehicleDetailPage.tsx` - Vehicle details + booking

### Backend Core Files:
- `src/services/UserService.ts` - User management (FIXED)
- `src/services/NotificationService.ts` - Email/SMS notifications
- `src/routes/` - API endpoints for all features
- `src/config/env.ts` - Environment configuration

## 🔧 Development Commands

### Frontend:
```bash
cd frontend
npm run dev          # Start dev server
npm run build        # Production build
npm run test         # Run tests
npm run lint         # Check code quality
npx tsc --noEmit     # Check TypeScript
```

### Backend:
```bash
cd backend  
npm run dev          # Start with nodemon
npm run test         # Run all tests
npm run build        # TypeScript compilation
```

## 🧪 Testing

### Run All Tests:
```bash
# Frontend tests
cd frontend && npm test

# Backend tests  
cd backend && npm run test

# Integration test
./test-integration.sh
```

### Health Checks:
- Backend: `curl http://localhost:3001/api/health`
- Frontend: Open http://localhost:5173
- Database: Check HealthCheck component in app

## 🔌 API Integration

### Using apiService:
```typescript
import apiService from '../services/apiService'

// Search vehicles
const vehicles = await apiService.searchVehicles({
  location: 'New York',
  startDate: '2025-07-15',
  endDate: '2025-07-17'
})

// Create booking
const booking = await apiService.createBooking({
  vehicleId: 'abc123',
  startDate: '2025-07-15T10:00:00Z',
  endDate: '2025-07-17T10:00:00Z',
  userId: 'user123'
})

// Check health
const health = await apiService.healthCheck()
```

## 🗺️ Maps Integration

### Google Maps Setup:
1. Set `VITE_GOOGLE_MAPS_API_KEY` in frontend `.env`
2. Enable Maps JavaScript API + Places API
3. Use `MapVehicleSearch` component for vehicle location

## 💳 Payment Integration

### Stripe Setup:
1. Set `VITE_STRIPE_PUBLIC_KEY` in frontend `.env`
2. Set `STRIPE_SECRET_KEY` in backend `.env`
3. Use `PaymentService` for processing payments

## 📧 Email/Notifications

### Email Setup:
1. Configure SMTP settings in backend `.env`:
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 🔍 Debugging

### Common Issues:
1. **Import errors**: Check file paths and exports
2. **Type errors**: Run `npx tsc --noEmit`
3. **API errors**: Check backend logs and health endpoint
4. **Build errors**: Clear `node_modules` and reinstall

### Logs:
- Backend: Check console output or `backend.log`
- Frontend: Check browser console and network tab
- Database: Check Supabase dashboard

## 🚀 Deployment Ready

### Environment Variables:
- Copy `.env.example` files and configure
- Update `VITE_API_URL` for production backend
- Set up production database and Stripe keys

### Build Commands:
```bash
# Frontend production build
cd frontend && npm run build

# Backend production ready
cd backend && npm run build
```

## 🆘 Getting Help

### If servers won't start:
1. Check `.env` files are configured
2. Run `npm install` in both directories
3. Check port availability (3001, 5173)
4. Check logs for specific errors

### If API calls fail:
1. Verify backend health: `curl http://localhost:3001/api/health`
2. Check network tab in browser dev tools
3. Verify auth tokens in localStorage
4. Check CORS settings in backend

The system is now fully functional and ready for feature development! 🎉
