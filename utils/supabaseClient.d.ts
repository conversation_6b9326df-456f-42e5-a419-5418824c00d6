export declare const supabase: import("@supabase/supabase-js").SupabaseClient<any, "public", any>;
export type Database = {
    public: {
        Tables: {
            users: {
                Row: {
                    id: string;
                    email: string;
                    phone: string | null;
                    first_name: string;
                    last_name: string;
                    avatar: string | null;
                    verified: boolean;
                    is_provider: boolean;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    email: string;
                    phone?: string | null;
                    first_name: string;
                    last_name: string;
                    avatar?: string | null;
                    verified?: boolean;
                    is_provider?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    email?: string;
                    phone?: string | null;
                    first_name?: string;
                    last_name?: string;
                    avatar?: string | null;
                    verified?: boolean;
                    is_provider?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            providers: {
                Row: {
                    id: string;
                    user_id: string;
                    business_name: string;
                    business_type: 'individual' | 'shop';
                    business_registration: string | null;
                    address: string;
                    city: string;
                    phone: string;
                    whatsapp: string | null;
                    description: string | null;
                    stripe_account_id: string | null;
                    bank_details: any | null;
                    verified: boolean;
                    rating: number;
                    total_reviews: number;
                    total_vehicles: number;
                    response_rate: number;
                    response_time: number;
                    offers_cash_payments: boolean;
                    offers_insurance: boolean;
                    offers_driving_lessons: boolean;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    user_id: string;
                    business_name: string;
                    business_type?: 'individual' | 'shop';
                    business_registration?: string | null;
                    address: string;
                    city: string;
                    phone: string;
                    whatsapp?: string | null;
                    description?: string | null;
                    stripe_account_id?: string | null;
                    bank_details?: any | null;
                    verified?: boolean;
                    rating?: number;
                    total_reviews?: number;
                    total_vehicles?: number;
                    response_rate?: number;
                    response_time?: number;
                    offers_cash_payments?: boolean;
                    offers_insurance?: boolean;
                    offers_driving_lessons?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string;
                    business_name?: string;
                    business_type?: 'individual' | 'shop';
                    business_registration?: string | null;
                    address?: string;
                    city?: string;
                    phone?: string;
                    whatsapp?: string | null;
                    description?: string | null;
                    stripe_account_id?: string | null;
                    bank_details?: any | null;
                    verified?: boolean;
                    rating?: number;
                    total_reviews?: number;
                    total_vehicles?: number;
                    response_rate?: number;
                    response_time?: number;
                    offers_cash_payments?: boolean;
                    offers_insurance?: boolean;
                    offers_driving_lessons?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            vehicles: {
                Row: {
                    id: string;
                    provider_id: string;
                    category: 'small_scooter' | 'large_scooter' | 'luxury_bike';
                    make: string;
                    model: string;
                    year: number;
                    engine_size: number;
                    transmission: 'automatic' | 'manual';
                    fuel_type: 'petrol' | 'electric';
                    description: string;
                    images: string[];
                    daily_rate: number;
                    weekly_rate: number;
                    monthly_rate: number;
                    yearly_rate: number | null;
                    security_deposit: number;
                    minimum_rental_days: number;
                    maximum_rental_days: number;
                    delivery_available: boolean;
                    delivery_fee: number;
                    delivery_radius: number;
                    quantity: number;
                    available_quantity: number;
                    features: any[];
                    add_ons: any[];
                    location: any;
                    pickup_instructions: string | null;
                    rental_agreement: string | null;
                    active: boolean;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    provider_id: string;
                    category: 'small_scooter' | 'large_scooter' | 'luxury_bike';
                    make: string;
                    model: string;
                    year: number;
                    engine_size: number;
                    transmission?: 'automatic' | 'manual';
                    fuel_type?: 'petrol' | 'electric';
                    description: string;
                    images: string[];
                    daily_rate: number;
                    weekly_rate: number;
                    monthly_rate: number;
                    yearly_rate?: number | null;
                    security_deposit: number;
                    minimum_rental_days?: number;
                    maximum_rental_days?: number;
                    delivery_available?: boolean;
                    delivery_fee?: number;
                    delivery_radius?: number;
                    quantity: number;
                    available_quantity?: number;
                    features?: any[];
                    add_ons?: any[];
                    location: any;
                    pickup_instructions?: string | null;
                    rental_agreement?: string | null;
                    active?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    provider_id?: string;
                    category?: 'small_scooter' | 'large_scooter' | 'luxury_bike';
                    make?: string;
                    model?: string;
                    year?: number;
                    engine_size?: number;
                    transmission?: 'automatic' | 'manual';
                    fuel_type?: 'petrol' | 'electric';
                    description?: string;
                    images?: string[];
                    daily_rate?: number;
                    weekly_rate?: number;
                    monthly_rate?: number;
                    yearly_rate?: number | null;
                    security_deposit?: number;
                    minimum_rental_days?: number;
                    maximum_rental_days?: number;
                    delivery_available?: boolean;
                    delivery_fee?: number;
                    delivery_radius?: number;
                    quantity?: number;
                    available_quantity?: number;
                    features?: any[];
                    add_ons?: any[];
                    location?: any;
                    pickup_instructions?: string | null;
                    rental_agreement?: string | null;
                    active?: boolean;
                    created_at?: string;
                    updated_at?: string;
                };
            };
            bookings: {
                Row: {
                    id: string;
                    user_id: string;
                    provider_id: string;
                    vehicle_id: string;
                    start_date: string;
                    end_date: string;
                    total_days: number;
                    vehicle_rate: number;
                    add_ons_total: number;
                    delivery_fee: number;
                    subtotal: number;
                    commission_rate: number;
                    commission_amount: number;
                    total: number;
                    payment_method: 'card' | 'cash';
                    payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
                    booking_status: 'pending_payment' | 'pending_pickup' | 'active' | 'completed' | 'cancelled' | 'dispute';
                    pickup_confirmed: boolean;
                    pickup_confirmed_at: string | null;
                    return_confirmed: boolean;
                    return_confirmed_at: string | null;
                    selected_add_ons: any[];
                    delivery_requested: boolean;
                    delivery_address: string | null;
                    pickup_instructions: string | null;
                    stripe_payment_intent_id: string | null;
                    cash_confirmation_code: string | null;
                    created_at: string;
                    updated_at: string;
                };
                Insert: {
                    id?: string;
                    user_id: string;
                    provider_id: string;
                    vehicle_id: string;
                    start_date: string;
                    end_date: string;
                    total_days: number;
                    vehicle_rate: number;
                    add_ons_total?: number;
                    delivery_fee?: number;
                    subtotal: number;
                    commission_rate: number;
                    commission_amount: number;
                    total: number;
                    payment_method: 'card' | 'cash';
                    payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
                    booking_status?: 'pending_payment' | 'pending_pickup' | 'active' | 'completed' | 'cancelled' | 'dispute';
                    pickup_confirmed?: boolean;
                    pickup_confirmed_at?: string | null;
                    return_confirmed?: boolean;
                    return_confirmed_at?: string | null;
                    selected_add_ons?: any[];
                    delivery_requested?: boolean;
                    delivery_address?: string | null;
                    pickup_instructions?: string | null;
                    stripe_payment_intent_id?: string | null;
                    cash_confirmation_code?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
                Update: {
                    id?: string;
                    user_id?: string;
                    provider_id?: string;
                    vehicle_id?: string;
                    start_date?: string;
                    end_date?: string;
                    total_days?: number;
                    vehicle_rate?: number;
                    add_ons_total?: number;
                    delivery_fee?: number;
                    subtotal?: number;
                    commission_rate?: number;
                    commission_amount?: number;
                    total?: number;
                    payment_method?: 'card' | 'cash';
                    payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
                    booking_status?: 'pending_payment' | 'pending_pickup' | 'active' | 'completed' | 'cancelled' | 'dispute';
                    pickup_confirmed?: boolean;
                    pickup_confirmed_at?: string | null;
                    return_confirmed?: boolean;
                    return_confirmed_at?: string | null;
                    selected_add_ons?: any[];
                    delivery_requested?: boolean;
                    delivery_address?: string | null;
                    pickup_instructions?: string | null;
                    stripe_payment_intent_id?: string | null;
                    cash_confirmation_code?: string | null;
                    created_at?: string;
                    updated_at?: string;
                };
            };
        };
        Views: {
            [_ in never]: never;
        };
        Functions: {
            [_ in never]: never;
        };
        Enums: {
            [_ in never]: never;
        };
    };
};
//# sourceMappingURL=supabaseClient.d.ts.map