import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    const { method, url } = req
    const urlPath = new URL(url).pathname
    
    // Parse request body
    let body = null
    if (method !== 'GET') {
      body = await req.json()
    }

    // Route different database operations
    switch (urlPath) {
      case '/database-proxy/tables':
        return await handleTablesCheck(supabase)
      
      case '/database-proxy/users':
        return await handleUsersOperation(supabase, method, body)
      
      case '/database-proxy/vehicles':
        return await handleVehiclesOperation(supabase, method, body)
      
      case '/database-proxy/bookings':
        return await handleBookingsOperation(supabase, method, body)
      
      case '/database-proxy/health':
        return await handleHealthCheck(supabase)
      
      default:
        return new Response(
          JSON.stringify({ error: 'Endpoint not found' }),
          { 
            status: 404, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
    }

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function handleHealthCheck(supabase: any) {
  try {
    // Simple health check query
    const { data, error } = await supabase
      .from('User')
      .select('count')
      .limit(1)
    
    if (error) throw error
    
    return new Response(
      JSON.stringify({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        database: 'connected'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ 
        status: 'unhealthy', 
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleTablesCheck(supabase: any) {
  try {
    const tables = ['User', 'ListedVehicle', 'Booking', 'Payment']
    const results = {}
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        results[table] = error ? { error: error.message } : { count }
      } catch (e) {
        results[table] = { error: e.message }
      }
    }
    
    return new Response(
      JSON.stringify({ tables: results }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleUsersOperation(supabase: any, method: string, body: any) {
  try {
    switch (method) {
      case 'GET':
        const { data: users, error: getUsersError } = await supabase
          .from('User')
          .select('id, email, name, role, status, createdAt')
          .limit(10)
        
        if (getUsersError) throw getUsersError
        
        return new Response(
          JSON.stringify({ users }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      
      case 'POST':
        const { data: newUser, error: createUserError } = await supabase
          .from('User')
          .insert(body)
          .select()
        
        if (createUserError) throw createUserError
        
        return new Response(
          JSON.stringify({ user: newUser[0] }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          { 
            status: 405,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
    }
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleVehiclesOperation(supabase: any, method: string, body: any) {
  // Similar implementation for vehicles
  return new Response(
    JSON.stringify({ message: 'Vehicles endpoint - implementation needed' }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleBookingsOperation(supabase: any, method: string, body: any) {
  // Similar implementation for bookings
  return new Response(
    JSON.stringify({ message: 'Bookings endpoint - implementation needed' }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}
