-- Enable RLS on vehicles table
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow anon users to read active vehicles
CREATE POLICY "Allow anon users to read active vehicles" ON vehicles
FOR SELECT USING (active = true);

-- Create policy to allow authenticated users to read all vehicles
CREATE POLICY "Allow authenticated users to read all vehicles" ON vehicles
FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy to allow service role to manage all vehicles
CREATE POLICY "Allow service role to manage vehicles" ON vehicles
FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT SELECT ON vehicles TO anon;
GRANT SELECT ON vehicles TO authenticated;
GRANT ALL ON vehicles TO service_role; 