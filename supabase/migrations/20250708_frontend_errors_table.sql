-- Basic Frontend Errors Table

-- Enable necessary extensions if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the frontend_errors table
CREATE TABLE public.frontend_errors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message TEXT NOT NULL,
    stack TEXT,
    context JSONB DEFAULT '{}',
    url TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_frontend_errors_created_at ON
public.frontend_errors(created_at DESC);
CREATE INDEX idx_frontend_errors_timestamp ON
public.frontend_errors(timestamp DESC);
CREATE INDEX idx_frontend_errors_url ON
public.frontend_errors(url);

-- Enable Row Level Security (RLS)
ALTER TABLE public.frontend_errors ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Allow authenticated users to insert errors
CREATE POLICY "Allow authenticated users to insert errors" ON
 public.frontend_errors
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- RLS Policy: Allow service role full access
CREATE POLICY "Service role full access" ON
public.frontend_errors
    FOR ALL
    TO service_role
    USING (true);

-- RLS Policy: Allow authenticated users to view errors (for 
-- admin dashboard)
CREATE POLICY "Allow authenticated users to view errors" ON
public.frontend_errors
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant necessary permissions
GRANT SELECT, INSERT ON public.frontend_errors TO
authenticated;
GRANT ALL ON public.frontend_errors TO service_role;
