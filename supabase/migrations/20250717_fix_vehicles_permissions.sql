-- Fix vehicles table permissions for anon access
-- This migration ensures anon users can read from the vehicles table

-- First, grant basic permissions to anon role
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;

-- Enable RLS on vehicles table if not already enabled
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to start fresh
DROP POLICY IF EXISTS "Allow anon users to read active vehicles" ON vehicles;
DROP POLICY IF EXISTS "Allow authenticated users to read all vehicles" ON vehicles;
DROP POLICY IF EXISTS "Allow service role to manage vehicles" ON vehicles;

-- Create policy to allow anon users to read active vehicles
CREATE POLICY "Allow anon users to read active vehicles" ON vehicles
FOR SELECT USING (active = true);

-- Create policy to allow authenticated users to read all vehicles
CREATE POLICY "Allow authenticated users to read all vehicles" ON vehicles
FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy to allow service role to manage vehicles
CREATE POLICY "Allow service role to manage vehicles" ON vehicles
FOR ALL USING (auth.role() = 'service_role');

-- Grant specific permissions
GRANT SELECT ON vehicles TO anon;
GRANT SELECT ON vehicles TO authenticated;
GRANT ALL ON vehicles TO service_role;

-- Ensure future tables will have proper permissions
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO anon;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO authenticated; 