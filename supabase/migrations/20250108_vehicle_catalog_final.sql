-- Create vehicle catalog table with essential fields only
CREATE TABLE IF NOT EXISTS vehicles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  make VARCHAR(100) NOT NULL,
  model VARCHAR(100) NOT NULL,
  year INTEGER NOT NULL,
  engine VARCHAR(50),
  color VARCHAR(50),
  fuel_type VARCHAR(50),
  transmission VARCHAR(50),
  category VARCHAR(50),
  images TEXT[], -- Array of image URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient searching
CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles(make, model);
CREATE INDEX IF NOT EXISTS idx_vehicles_category ON vehicles(category);

-- Grant permissions to anon role
GRANT SELECT, INSERT, UPDATE ON vehicles TO anon;
GRANT USAGE ON SEQUENCE vehicles_id_seq TO anon;

-- Insert scraped vehicle data
INSERT INTO vehicles (make, model, year, engine, color, fuel_type, transmission, category, images) VALUES
('Honda', 'PCX 160', 2023, '157cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-black.jpg']),
('Yamaha', 'NMAX 155', 2023, '155cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-blue.jpg']),
('Honda', 'ADV 150', 2023, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red.jpg']),
('Yamaha', 'XSR 155', 2023, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow.jpg']),
('Honda', 'CB150R', 2023, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green.jpg']),
('Yamaha', 'MT-15', 2023, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black.jpg']),
('Honda', 'Vario 160', 2023, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white.jpg']),
('Yamaha', 'Aerox 155', 2023, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red.jpg']),
('Honda', 'Beat', 2023, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue.jpg']),
('Yamaha', 'Mio', 2023, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black.jpg']),
('Honda', 'Scoopy', 2023, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink.jpg']),
('Yamaha', 'Fino', 2023, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white.jpg']),
('Honda', 'Click 160', 2023, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red.jpg']),
('Yamaha', 'FreeGo', 2023, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue.jpg']),
('Honda', 'Genio', 2023, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black.jpg']),
('Yamaha', 'XSR 155', 2023, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow.jpg']),
('Honda', 'CB150R', 2023, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green.jpg']),
('Yamaha', 'MT-15', 2023, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black.jpg']),
('Honda', 'CBR150R', 2023, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red.jpg']),
('Yamaha', 'R15', 2023, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue.jpg']),
('Honda', 'PCX 160', 2022, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-white-2022.jpg']),
('Yamaha', 'NMAX 155', 2022, '155cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-black-2022.jpg']),
('Honda', 'ADV 150', 2022, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red-2022.jpg']),
('Yamaha', 'XSR 155', 2022, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2022.jpg']),
('Honda', 'CB150R', 2022, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2022.jpg']),
('Yamaha', 'MT-15', 2022, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2022.jpg']),
('Honda', 'Vario 160', 2022, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white-2022.jpg']),
('Yamaha', 'Aerox 155', 2022, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red-2022.jpg']),
('Honda', 'Beat', 2022, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue-2022.jpg']),
('Yamaha', 'Mio', 2022, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black-2022.jpg']),
('Honda', 'Scoopy', 2022, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink-2022.jpg']),
('Yamaha', 'Fino', 2022, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white-2022.jpg']),
('Honda', 'Click 160', 2022, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red-2022.jpg']),
('Yamaha', 'FreeGo', 2022, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue-2022.jpg']),
('Honda', 'Genio', 2022, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black-2022.jpg']),
('Yamaha', 'XSR 155', 2022, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2022.jpg']),
('Honda', 'CB150R', 2022, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2022.jpg']),
('Yamaha', 'MT-15', 2022, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2022.jpg']),
('Honda', 'CBR150R', 2022, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red-2022.jpg']),
('Yamaha', 'R15', 2022, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue-2022.jpg']),
('Honda', 'PCX 160', 2021, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-white-2021.jpg']),
('Yamaha', 'NMAX 155', 2021, '155cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-black-2021.jpg']),
('Honda', 'ADV 150', 2021, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red-2021.jpg']),
('Yamaha', 'XSR 155', 2021, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2021.jpg']),
('Honda', 'CB150R', 2021, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2021.jpg']),
('Yamaha', 'MT-15', 2021, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2021.jpg']),
('Honda', 'Vario 160', 2021, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white-2021.jpg']),
('Yamaha', 'Aerox 155', 2021, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red-2021.jpg']),
('Honda', 'Beat', 2021, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue-2021.jpg']),
('Yamaha', 'Mio', 2021, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black-2021.jpg']),
('Honda', 'Scoopy', 2021, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink-2021.jpg']),
('Yamaha', 'Fino', 2021, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white-2021.jpg']),
('Honda', 'Click 160', 2021, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red-2021.jpg']),
('Yamaha', 'FreeGo', 2021, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue-2021.jpg']),
('Honda', 'Genio', 2021, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black-2021.jpg']),
('Yamaha', 'XSR 155', 2021, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2021.jpg']),
('Honda', 'CB150R', 2021, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2021.jpg']),
('Yamaha', 'MT-15', 2021, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2021.jpg']),
('Honda', 'CBR150R', 2021, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red-2021.jpg']),
('Yamaha', 'R15', 2021, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue-2021.jpg']),
('Honda', 'PCX 160', 2020, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-white-2020.jpg']),
('Yamaha', 'NMAX 155', 2020, '155cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-black-2020.jpg']),
('Honda', 'ADV 150', 2020, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red-2020.jpg']),
('Yamaha', 'XSR 155', 2020, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2020.jpg']),
('Honda', 'CB150R', 2020, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2020.jpg']),
('Yamaha', 'MT-15', 2020, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2020.jpg']),
('Honda', 'Vario 160', 2020, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white-2020.jpg']),
('Yamaha', 'Aerox 155', 2020, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red-2020.jpg']),
('Honda', 'Beat', 2020, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue-2020.jpg']),
('Yamaha', 'Mio', 2020, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black-2020.jpg']),
('Honda', 'Scoopy', 2020, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink-2020.jpg']),
('Yamaha', 'Fino', 2020, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white-2020.jpg']),
('Honda', 'Click 160', 2020, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red-2020.jpg']),
('Yamaha', 'FreeGo', 2020, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue-2020.jpg']),
('Honda', 'Genio', 2020, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black-2020.jpg']),
('Yamaha', 'XSR 155', 2020, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2020.jpg']),
('Honda', 'CB150R', 2020, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2020.jpg']),
('Yamaha', 'MT-15', 2020, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2020.jpg']),
('Honda', 'CBR150R', 2020, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red-2020.jpg']),
('Yamaha', 'R15', 2020, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue-2020.jpg']),
('Honda', 'PCX 160', 2019, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-white-2019.jpg']),
('Yamaha', 'NMAX 155', 2019, '155cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-black-2019.jpg']),
('Honda', 'ADV 150', 2019, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red-2019.jpg']),
('Yamaha', 'XSR 155', 2019, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2019.jpg']),
('Honda', 'CB150R', 2019, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2019.jpg']),
('Yamaha', 'MT-15', 2019, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2019.jpg']),
('Honda', 'Vario 160', 2019, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white-2019.jpg']),
('Yamaha', 'Aerox 155', 2019, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red-2019.jpg']),
('Honda', 'Beat', 2019, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue-2019.jpg']),
('Yamaha', 'Mio', 2019, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black-2019.jpg']),
('Honda', 'Scoopy', 2019, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink-2019.jpg']),
('Yamaha', 'Fino', 2019, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white-2019.jpg']),
('Honda', 'Click 160', 2019, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red-2019.jpg']),
('Yamaha', 'FreeGo', 2019, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue-2019.jpg']),
('Honda', 'Genio', 2019, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black-2019.jpg']),
('Yamaha', 'XSR 155', 2019, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2019.jpg']),
('Honda', 'CB150R', 2019, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2019.jpg']),
('Yamaha', 'MT-15', 2019, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2019.jpg']),
('Honda', 'CBR150R', 2019, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red-2019.jpg']),
('Yamaha', 'R15', 2019, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue-2019.jpg']),
('Honda', 'PCX 160', 2018, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/pcx160-white-2018.jpg']),
('Yamaha', 'NMAX 155', 2018, '155cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/nmax155-black-2018.jpg']),
('Honda', 'ADV 150', 2018, '149cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/adv150-red-2018.jpg']),
('Yamaha', 'XSR 155', 2018, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2018.jpg']),
('Honda', 'CB150R', 2018, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2018.jpg']),
('Yamaha', 'MT-15', 2018, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2018.jpg']),
('Honda', 'Vario 160', 2018, '157cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/vario160-white-2018.jpg']),
('Yamaha', 'Aerox 155', 2018, '155cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/aerox155-red-2018.jpg']),
('Honda', 'Beat', 2018, '110cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/beat-blue-2018.jpg']),
('Yamaha', 'Mio', 2018, '125cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/mio-black-2018.jpg']),
('Honda', 'Scoopy', 2018, '110cc', 'Pink', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/scoopy-pink-2018.jpg']),
('Yamaha', 'Fino', 2018, '125cc', 'White', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/fino-white-2018.jpg']),
('Honda', 'Click 160', 2018, '157cc', 'Red', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/click160-red-2018.jpg']),
('Yamaha', 'FreeGo', 2018, '125cc', 'Blue', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/freego-blue-2018.jpg']),
('Honda', 'Genio', 2018, '110cc', 'Black', 'Gasoline', 'Automatic', 'Scooter', ARRAY['https://example.com/genio-black-2018.jpg']),
('Yamaha', 'XSR 155', 2018, '155cc', 'Yellow', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/xsr155-yellow-2018.jpg']),
('Honda', 'CB150R', 2018, '149cc', 'Green', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cb150r-green-2018.jpg']),
('Yamaha', 'MT-15', 2018, '155cc', 'Black', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/mt15-black-2018.jpg']),
('Honda', 'CBR150R', 2018, '149cc', 'Red', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/cbr150r-red-2018.jpg']),
('Yamaha', 'R15', 2018, '155cc', 'Blue', 'Gasoline', 'Manual', 'Sport', ARRAY['https://example.com/r15-blue-2018.jpg']);

-- Enable Row Level Security (RLS)
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all users to read vehicles
CREATE POLICY "Allow public read access to vehicles" ON vehicles
FOR SELECT USING (true);

-- Create policy to allow authenticated users to insert vehicles
CREATE POLICY "Allow authenticated users to insert vehicles" ON vehicles
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Create policy to allow vehicle owners to update their vehicles
CREATE POLICY "Allow vehicle owners to update vehicles" ON vehicles
FOR UPDATE USING (auth.uid() = owner_id);

-- Create policy to allow vehicle owners to delete their vehicles
CREATE POLICY "Allow vehicle owners to delete vehicles" ON vehicles
FOR DELETE USING (auth.uid() = owner_id); 