-- RentaHub Database Schema
-- Execute this in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types
CREATE TYPE vehicle_category AS ENUM ('small_scooter', 'large_scooter', 'luxury_bike');
CREATE TYPE transmission_type AS ENUM ('automatic', 'manual');
CREATE TYPE fuel_type AS ENUM ('petrol', 'electric');
CREATE TYPE business_type AS ENUM ('individual', 'shop');
CREATE TYPE payment_method AS ENUM ('card', 'cash');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE booking_status AS ENUM ('pending_payment', 'pending_pickup', 'active', 'completed', 'cancelled', 'dispute');
CREATE TYPE lesson_experience AS ENUM ('beginner', 'intermediate', 'advanced');
CREATE TYPE lesson_status AS ENUM ('pending', 'assigned', 'confirmed', 'completed', 'cancelled');
CREATE TYPE message_type AS ENUM ('text', 'image', 'system');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    avatar TEXT,
    verified BOOLEAN DEFAULT false,
    is_provider BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Providers table
CREATE TABLE public.providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    business_name VARCHAR(200) NOT NULL,
    business_type business_type DEFAULT 'individual',
    business_registration VARCHAR(100),
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    whatsapp VARCHAR(20),
    description TEXT,
    stripe_account_id VARCHAR(100),
    bank_details JSONB,
    verified BOOLEAN DEFAULT false,
    rating DECIMAL(2,1) DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    total_vehicles INTEGER DEFAULT 0,
    response_rate DECIMAL(5,2) DEFAULT 0.0,
    response_time INTEGER DEFAULT 0, -- in minutes
    offers_cash_payments BOOLEAN DEFAULT true,
    offers_insurance BOOLEAN DEFAULT false,
    offers_driving_lessons BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vehicles table
CREATE TABLE public.vehicles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_id UUID REFERENCES public.providers(id) ON DELETE CASCADE,
    category vehicle_category NOT NULL,
    make VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    year INTEGER NOT NULL,
    engine_size INTEGER NOT NULL, -- in cc
    transmission transmission_type DEFAULT 'automatic',
    fuel_type fuel_type DEFAULT 'petrol',
    description TEXT NOT NULL,
    images TEXT[] DEFAULT '{}',
    daily_rate DECIMAL(10,2) NOT NULL,
    weekly_rate DECIMAL(10,2) NOT NULL,
    monthly_rate DECIMAL(10,2) NOT NULL,
    yearly_rate DECIMAL(10,2),
    security_deposit DECIMAL(10,2) NOT NULL,
    minimum_rental_days INTEGER DEFAULT 1,
    maximum_rental_days INTEGER DEFAULT 365,
    delivery_available BOOLEAN DEFAULT false,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    delivery_radius INTEGER DEFAULT 0, -- in km
    quantity INTEGER NOT NULL DEFAULT 1, -- Total inventory
    available_quantity INTEGER NOT NULL DEFAULT 1, -- Currently available
    features JSONB DEFAULT '[]',
    add_ons JSONB DEFAULT '[]',
    location JSONB NOT NULL, -- {address, city, lat, lng, etc}
    pickup_instructions TEXT,
    rental_agreement TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivery_options JSONB DEFAULT '{
      "is_free_delivery": false,
      "base_delivery_fee": 0,
      "max_free_delivery_radius": 0,
      "additional_km_rate": 0,
      "delivery_locations": []
    }',
    
    CONSTRAINT positive_quantity CHECK (quantity > 0),
    CONSTRAINT available_quantity_valid CHECK (available_quantity >= 0 AND available_quantity <= quantity),
    insurance_provided BOOLEAN DEFAULT FALSE,
    deposit_required BOOLEAN DEFAULT FALSE,
    deposit_amount DECIMAL(10,2) DEFAULT 0.00,
    insurance_notes TEXT,
    deposit_notes TEXT
);

-- Bookings table
CREATE TABLE public.bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES public.providers(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_days INTEGER NOT NULL,
    vehicle_rate DECIMAL(10,2) NOT NULL,
    add_ons_total DECIMAL(10,2) DEFAULT 0,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    subtotal DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL DEFAULT 15.00, -- 15%
    commission_amount DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    payment_method payment_method NOT NULL,
    payment_status payment_status DEFAULT 'pending',
    booking_status booking_status DEFAULT 'pending_payment',
    pickup_confirmed BOOLEAN DEFAULT false,
    pickup_confirmed_at TIMESTAMP WITH TIME ZONE,
    return_confirmed BOOLEAN DEFAULT false,
    return_confirmed_at TIMESTAMP WITH TIME ZONE,
    selected_add_ons JSONB DEFAULT '[]',
    delivery_requested BOOLEAN DEFAULT false,
    delivery_address TEXT,
    pickup_instructions TEXT,
    stripe_payment_intent_id VARCHAR(100),
    cash_confirmation_code VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_date_range CHECK (end_date >= start_date),
    CONSTRAINT positive_total CHECK (total > 0)
);

-- Reviews table
CREATE TABLE public.reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES public.providers(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    response TEXT, -- Provider response
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(booking_id) -- One review per booking
);

-- Driving lesson requests table
CREATE TABLE public.driving_lesson_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    provider_id UUID REFERENCES public.providers(id) ON DELETE SET NULL,
    vehicle_category vehicle_category NOT NULL,
    preferred_date DATE NOT NULL,
    preferred_time TIME NOT NULL,
    experience lesson_experience NOT NULL,
    message TEXT,
    status lesson_status DEFAULT 'pending',
    assigned_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL, -- Generated from user_id + provider_id hash
    sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    type message_type DEFAULT 'text',
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vehicle availability tracking (for complex scheduling)
CREATE TABLE public.vehicle_unavailable_dates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    quantity_blocked INTEGER NOT NULL DEFAULT 1,
    reason VARCHAR(100) DEFAULT 'booking',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_unavailable_date_range CHECK (end_date >= start_date)
);

-- Discount Codes Table
CREATE TABLE IF NOT EXISTS discount_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(10,2) NOT NULL,
    min_purchase_amount DECIMAL(10,2) DEFAULT 0,
    max_discount_amount DECIMAL(10,2),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expiration_date TIMESTAMP WITH TIME ZONE,
    usage_limit INTEGER DEFAULT NULL,
    total_used_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    applies_to VARCHAR(50)[] DEFAULT ARRAY['all']::VARCHAR[], -- vehicle types, user types, etc.
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_discount_type CHECK (
        (discount_type = 'percentage' AND discount_value BETWEEN 0 AND 100) OR
        (discount_type = 'fixed' AND discount_value > 0)
    ),
    CONSTRAINT positive_min_purchase CHECK (min_purchase_amount >= 0),
    CONSTRAINT positive_max_discount CHECK (max_discount_amount IS NULL OR max_discount_amount > 0)
);

-- Comprehensive insurance and deposit tracking table
CREATE TABLE IF NOT EXISTS vehicle_insurance_deposit (
    id SERIAL PRIMARY KEY,
    vehicle_id INTEGER NOT NULL REFERENCES vehicles(id),
    insurance_type VARCHAR(100),
    insurance_coverage_amount DECIMAL(12,2),
    deposit_type ENUM('cash', 'card_hold', 'bank_transfer') DEFAULT 'card_hold',
    special_instructions TEXT,
    updated_by INTEGER NOT NULL REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Indexes for performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_phone ON public.users(phone);
CREATE INDEX idx_providers_user_id ON public.providers(user_id);
CREATE INDEX idx_providers_city ON public.providers(city);
CREATE INDEX idx_vehicles_provider_id ON public.vehicles(provider_id);
CREATE INDEX idx_vehicles_category ON public.vehicles(category);
CREATE INDEX idx_vehicles_active ON public.vehicles(active);
CREATE INDEX idx_bookings_user_id ON public.bookings(user_id);
CREATE INDEX idx_bookings_provider_id ON public.bookings(provider_id);
CREATE INDEX idx_bookings_vehicle_id ON public.bookings(vehicle_id);
CREATE INDEX idx_bookings_dates ON public.bookings(start_date, end_date);
CREATE INDEX idx_bookings_status ON public.bookings(booking_status);
CREATE INDEX idx_reviews_provider_id ON public.reviews(provider_id);
CREATE INDEX idx_reviews_vehicle_id ON public.reviews(vehicle_id);
CREATE INDEX idx_messages_conversation_id ON public.messages(conversation_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_vehicle_unavailable_dates_vehicle_id ON public.vehicle_unavailable_dates(vehicle_id);
CREATE INDEX idx_vehicle_unavailable_dates_dates ON public.vehicle_unavailable_dates(start_date, end_date);

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.driving_lesson_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicle_unavailable_dates ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Providers policies
CREATE POLICY "Anyone can view verified providers" ON public.providers
    FOR SELECT USING (verified = true);

CREATE POLICY "Providers can manage own data" ON public.providers
    FOR ALL USING (auth.uid() = user_id);

-- Vehicles policies
CREATE POLICY "Anyone can view active vehicles" ON public.vehicles
    FOR SELECT USING (active = true);

CREATE POLICY "Providers can manage own vehicles" ON public.vehicles
    FOR ALL USING (auth.uid() = (SELECT user_id FROM public.providers WHERE id = provider_id));

-- Bookings policies
CREATE POLICY "Users can view own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Providers can view their bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = (SELECT user_id FROM public.providers WHERE id = provider_id));

CREATE POLICY "Users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own bookings" ON public.bookings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Providers can update their bookings" ON public.bookings
    FOR UPDATE USING (auth.uid() = (SELECT user_id FROM public.providers WHERE id = provider_id));

-- Reviews policies
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT TO authenticated;

CREATE POLICY "Users can create reviews for their bookings" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.bookings 
            WHERE id = booking_id AND user_id = auth.uid() AND booking_status = 'completed'
        )
    );

CREATE POLICY "Providers can respond to reviews" ON public.reviews
    FOR UPDATE USING (auth.uid() = (SELECT user_id FROM public.providers WHERE id = provider_id));

-- Messages policies
CREATE POLICY "Users can view their messages" ON public.messages
    FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

CREATE POLICY "Users can send messages" ON public.messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- Driving lesson requests policies
CREATE POLICY "Users can manage own lesson requests" ON public.driving_lesson_requests
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Providers can view assigned lesson requests" ON public.driving_lesson_requests
    FOR SELECT USING (auth.uid() = (SELECT user_id FROM public.providers WHERE id = provider_id));

-- Functions and triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_providers_updated_at BEFORE UPDATE ON public.providers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON public.vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON public.bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON public.reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_driving_lesson_requests_updated_at BEFORE UPDATE ON public.driving_lesson_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update vehicle availability when bookings change
CREATE OR REPLACE FUNCTION update_vehicle_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle INSERT and UPDATE
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Block dates for confirmed bookings
        IF NEW.booking_status IN ('pending_pickup', 'active') THEN
            INSERT INTO public.vehicle_unavailable_dates (vehicle_id, booking_id, start_date, end_date, quantity_blocked)
            VALUES (NEW.vehicle_id, NEW.id, NEW.start_date, NEW.end_date, 1)
            ON CONFLICT DO NOTHING;
            
            -- Update available quantity
            UPDATE public.vehicles 
            SET available_quantity = quantity - (
                SELECT COALESCE(SUM(quantity_blocked), 0) 
                FROM public.vehicle_unavailable_dates 
                WHERE vehicle_id = NEW.vehicle_id 
                AND start_date <= CURRENT_DATE 
                AND end_date >= CURRENT_DATE
            )
            WHERE id = NEW.vehicle_id;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- Handle DELETE
    IF TG_OP = 'DELETE' THEN
        DELETE FROM public.vehicle_unavailable_dates WHERE booking_id = OLD.id;
        
        -- Update available quantity
        UPDATE public.vehicles 
        SET available_quantity = quantity - (
            SELECT COALESCE(SUM(quantity_blocked), 0) 
            FROM public.vehicle_unavailable_dates 
            WHERE vehicle_id = OLD.vehicle_id 
            AND start_date <= CURRENT_DATE 
            AND end_date >= CURRENT_DATE
        )
        WHERE id = OLD.vehicle_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER booking_availability_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_vehicle_availability();

-- Function to update provider stats when reviews change
CREATE OR REPLACE FUNCTION update_provider_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE public.providers SET
            total_reviews = (SELECT COUNT(*) FROM public.reviews WHERE provider_id = NEW.provider_id),
            rating = (SELECT ROUND(AVG(rating)::numeric, 1) FROM public.reviews WHERE provider_id = NEW.provider_id)
        WHERE id = NEW.provider_id;
        
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE public.providers SET
            total_reviews = (SELECT COUNT(*) FROM public.reviews WHERE provider_id = OLD.provider_id),
            rating = (SELECT COALESCE(ROUND(AVG(rating)::numeric, 1), 0) FROM public.reviews WHERE provider_id = OLD.provider_id)
        WHERE id = OLD.provider_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER review_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_provider_stats();

-- Insert some sample data for testing
INSERT INTO public.users (id, email, first_name, last_name, is_provider) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'John', 'Doe', false),
    ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Sarah', 'Wilson', true),
    ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Mike', 'Chen', true);

INSERT INTO public.providers (user_id, business_name, business_type, address, city, phone, offers_cash_payments, offers_insurance, offers_driving_lessons, verified) VALUES
    ('550e8400-e29b-41d4-a716-446655440002', 'Bali Wheels', 'shop', 'Jl. Monkey Forest Road 123, Ubud', 'Ubud', '+***********', true, true, true, true),
    ('550e8400-e29b-41d4-a716-446655440003', 'Scooter King Rental', 'shop', 'Jl. Pantai Berawa 456, Canggu', 'Canggu', '+***********', true, false, true, true);