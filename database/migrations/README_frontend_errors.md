# Frontend Errors Table Setup

This directory contains SQL scripts to create the `frontend_errors` table in Supabase for logging frontend application errors in production.

## Files

1. **`create_frontend_errors_basic.sql`** - Minimal table structure that matches MonitoringService expectations
2. **`create_frontend_errors_table.sql`** - Full-featured table with advanced analytics and monitoring capabilities

## Quick Start (Recommended)

For immediate deployment, use the basic version:

```sql
-- Execute in Supabase SQL Editor
\i create_frontend_errors_basic.sql
```

## Table Structure

The `frontend_errors` table stores the following data:

| Column | Type | Description |
|--------|------|-------------|
| `id` | UUID | Primary key (auto-generated) |
| `message` | TEXT | Error message |
| `stack` | TEXT | Error stack trace |
| `context` | JSONB | Additional context data |
| `url` | TEXT | Page URL where error occurred |
| `timestamp` | TIMESTAMP WITH TIME ZONE | When error occurred |
| `user_agent` | TEXT | Browser user agent |
| `created_at` | TIMESTAMP WITH TIME ZONE | When logged to database |

## Row Level Security (RLS)

The table includes RLS policies that:
- Allow authenticated users to insert errors
- Allow authenticated users to view errors (for admin dashboard)
- Allow service role full access
- Prevent unauthorized access to sensitive error data

## Integration with Frontend Code

The table structure is designed to work seamlessly with:

### MonitoringService
```typescript
// In src/services/MonitoringService.ts
await supabase.from('frontend_errors').insert({
  message: error.message,
  stack: error.stack,
  context: context || {},
  url: window.location.href,
  timestamp: new Date().toISOString(),
  user_agent: navigator.userAgent,
})
```

### ErrorLogger
The table also supports the extended ErrorLogger interface with additional fields in the full version.

## Performance Considerations

### Indexes Created
- `idx_frontend_errors_created_at` - For time-based queries
- `idx_frontend_errors_timestamp` - For error occurrence time
- `idx_frontend_errors_url` - For URL-based filtering

### Query Performance Tips
```sql
-- Efficient queries using indexes
SELECT * FROM frontend_errors 
WHERE created_at > NOW() - INTERVAL '7 days'
ORDER BY created_at DESC;

-- Filter by URL
SELECT * FROM frontend_errors 
WHERE url LIKE '%/dashboard%'
ORDER BY created_at DESC;
```

## Monitoring and Analytics

### Basic Error Count
```sql
SELECT COUNT(*) as total_errors
FROM frontend_errors
WHERE created_at > NOW() - INTERVAL '24 hours';
```

### Errors by URL
```sql
SELECT 
    url,
    COUNT(*) as error_count
FROM frontend_errors
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY url
ORDER BY error_count DESC
LIMIT 10;
```

### Common Error Messages
```sql
SELECT 
    message,
    COUNT(*) as occurrence_count
FROM frontend_errors
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY message
ORDER BY occurrence_count DESC
LIMIT 10;
```

## Data Retention

Consider implementing data retention policies:

```sql
-- Clean up errors older than 90 days
DELETE FROM frontend_errors 
WHERE created_at < NOW() - INTERVAL '90 days';
```

## Security Considerations

1. **Sensitive Data**: The `context` field can contain sensitive information. Review what data is being logged.

2. **User Privacy**: Stack traces may contain user information. Consider data anonymization.

3. **Rate Limiting**: Implement client-side rate limiting to prevent error spam.

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure RLS policies are correctly set up
2. **Missing Extension**: Make sure `uuid-ossp` extension is enabled
3. **Insert Failures**: Check that all required fields are provided

### Testing the Setup

```sql
-- Test insert (should work for authenticated users)
INSERT INTO frontend_errors (message, url, timestamp)
VALUES ('Test error', 'https://example.com', NOW());

-- Test query (should work for authenticated users)
SELECT * FROM frontend_errors LIMIT 1;
```

## Advanced Features (Full Version Only)

The full version (`create_frontend_errors_table.sql`) includes:

- Error severity levels (low, medium, high, critical)
- Error source tracking (api, component, network, etc.)
- User ID association
- Session tracking
- Build version tracking
- Analytics functions
- Automated cleanup procedures
- Error statistics views

## Migration Steps

1. **Backup**: Always backup your database before running migrations
2. **Test**: Run the script in a staging environment first
3. **Deploy**: Execute the chosen SQL script in Supabase SQL Editor
4. **Verify**: Test that the frontend can successfully log errors
5. **Monitor**: Set up monitoring for the new error logging system

## Environment Variables

Ensure these environment variables are set in your frontend:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Next Steps

After creating the table:

1. Deploy the SQL script to your Supabase instance
2. Test error logging in production
3. Set up monitoring dashboards
4. Configure alerting for critical errors
5. Implement data retention policies

## Support

For issues or questions about the frontend error logging system, check:
- MonitoringService implementation in `src/services/MonitoringService.ts`
- ErrorLogger implementation in `src/utils/errorLogger.ts`
- Supabase documentation for RLS and table management