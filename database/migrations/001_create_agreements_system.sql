-- Agreement Templates Table
CREATE TABLE public.agreement_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type agreement_type NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.providers(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agreement Template Sections Table
CREATE TABLE public.agreement_template_sections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID REFERENCES public.agreement_templates(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_required BOOLEAN DEFAULT true,
    section_order INTEGER NOT NULL,
    variables JSONB DEFAULT '[]', -- Array of variable names used in this section
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generated Agreements Table
CREATE TABLE public.agreements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
    template_id UUID REFERENCES public.agreement_templates(id) ON DELETE SET NULL,
    provider_id UUID REFERENCES public.providers(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Agreement Content
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    generated_content TEXT NOT NULL, -- Final content with variables replaced
    
    -- Status and Tracking
    status agreement_status DEFAULT 'draft',
    version INTEGER DEFAULT 1,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    viewed_at TIMESTAMP WITH TIME ZONE,
    signed_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE,
    expired_at TIMESTAMP WITH TIME ZONE,
    
    -- Settings
    settings JSONB DEFAULT '{}', -- Agreement-specific settings
    
    -- Metadata for quick access
    metadata JSONB DEFAULT '{}' -- Customer name, vehicle name, etc.
);

-- Agreement Signatures Table
CREATE TABLE public.agreement_signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agreement_id UUID REFERENCES public.agreements(id) ON DELETE CASCADE,
    signer_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    signer_type signature_type NOT NULL,
    
    -- Signature Data
    signature_data TEXT, -- Base64 encoded signature image or digital signature token
    signature_method signature_method NOT NULL,
    ip_address INET,
    user_agent TEXT,
    
    -- Verification
    is_verified BOOLEAN DEFAULT false,
    verification_token VARCHAR(255),
    verification_method VARCHAR(100),
    
    -- Timestamps
    signed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    verified_at TIMESTAMP WITH TIME ZONE
);

-- Agreement History/Audit Table
CREATE TABLE public.agreement_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agreement_id UUID REFERENCES public.agreements(id) ON DELETE CASCADE,
    action agreement_action NOT NULL,
    actor_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    actor_type actor_type NOT NULL,
    
    -- Change Details
    old_status agreement_status,
    new_status agreement_status,
    changes JSONB DEFAULT '{}',
    notes TEXT,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ENUMs
CREATE TYPE agreement_type AS ENUM (
    'rental',
    'insurance',
    'damage_waiver',
    'extension',
    'custom'
);

CREATE TYPE agreement_status AS ENUM (
    'draft',
    'sent',
    'viewed',
    'agreed',
    'declined',
    'expired',
    'cancelled',
    'rejected'
);

CREATE TYPE signature_type AS ENUM (
    'customer',
    'provider',
    'witness',
    'admin'
);

CREATE TYPE signature_method AS ENUM (
    'digital_draw',
    'digital_type',
    'esignature_service',
    'wet_signature',
    'click_to_sign'
);

CREATE TYPE agreement_action AS ENUM (
    'created',
    'sent',
    'viewed',
    'agreed',
    'declined',
    'expired',
    'cancelled',
    'rejected',
    'resent',
    'modified'
);

CREATE TYPE actor_type AS ENUM (
    'customer',
    'provider',
    'admin',
    'system'
);

-- Create Indexes
CREATE INDEX idx_agreements_booking_id ON public.agreements(booking_id);
CREATE INDEX idx_agreements_provider_id ON public.agreements(provider_id);
CREATE INDEX idx_agreements_customer_id ON public.agreements(customer_id);
CREATE INDEX idx_agreements_status ON public.agreements(status);
CREATE INDEX idx_agreements_created_at ON public.agreements(created_at);
CREATE INDEX idx_agreements_sent_at ON public.agreements(sent_at);

CREATE INDEX idx_agreement_templates_type ON public.agreement_templates(type);
CREATE INDEX idx_agreement_templates_active ON public.agreement_templates(is_active);

CREATE INDEX idx_agreement_signatures_agreement_id ON public.agreement_signatures(agreement_id);
CREATE INDEX idx_agreement_signatures_signer_id ON public.agreement_signatures(signer_id);

CREATE INDEX idx_agreement_history_agreement_id ON public.agreement_history(agreement_id);
CREATE INDEX idx_agreement_history_created_at ON public.agreement_history(created_at);

-- Insert Default Templates
INSERT INTO public.agreement_templates (name, type, description, is_default, is_active) VALUES
('Standard Rental Agreement', 'rental', 'Comprehensive rental agreement for vehicle rentals', true, true),
('Insurance Waiver', 'insurance', 'Insurance coverage waiver agreement', true, true),
('Damage Waiver Agreement', 'damage_waiver', 'Damage protection waiver terms', true, true);

-- Insert Default Template Sections (Standard Rental Agreement)
WITH rental_template AS (
    SELECT id FROM public.agreement_templates WHERE type = 'rental' AND is_default = true LIMIT 1
)
INSERT INTO public.agreement_template_sections (template_id, title, content, section_order, variables) VALUES
(
    (SELECT id FROM rental_template),
    'Parties to the Agreement',
    'This Vehicle Rental Agreement ("Agreement") is entered into on {{AGREEMENT_DATE}} between:

**PROVIDER:** {{PROVIDER_NAME}}
Company: {{PROVIDER_COMPANY}}
Phone: {{PROVIDER_PHONE}}

**CUSTOMER:** {{CUSTOMER_NAME}}
Email: {{CUSTOMER_EMAIL}}
Phone: {{CUSTOMER_PHONE}}',
    1,
    '["AGREEMENT_DATE", "PROVIDER_NAME", "PROVIDER_COMPANY", "PROVIDER_PHONE", "CUSTOMER_NAME", "CUSTOMER_EMAIL", "CUSTOMER_PHONE"]'::jsonb
),
(
    (SELECT id FROM rental_template),
    'Vehicle Information',
    'The following vehicle is being rented under this agreement:

**Vehicle:** {{VEHICLE_NAME}}
**Year:** {{VEHICLE_YEAR}}
**License Plate:** {{VEHICLE_LICENSE}}
**Daily Rate:** {{DAILY_RATE}}
**Security Deposit:** {{DEPOSIT_AMOUNT}}',
    2,
    '["VEHICLE_NAME", "VEHICLE_YEAR", "VEHICLE_LICENSE", "DAILY_RATE", "DEPOSIT_AMOUNT"]'::jsonb
),
(
    (SELECT id FROM rental_template),
    'Rental Period and Payment',
    '**Rental Period:** {{RENTAL_START}} to {{RENTAL_END}} ({{RENTAL_DAYS}} days)
**Total Amount:** {{TOTAL_AMOUNT}}
**Payment Method:** {{PAYMENT_METHOD}}
**Booking ID:** {{BOOKING_ID}}

The customer agrees to pay the total rental amount as specified above. Payment must be completed before vehicle pickup.',
    3,
    '["RENTAL_START", "RENTAL_END", "RENTAL_DAYS", "TOTAL_AMOUNT", "PAYMENT_METHOD", "BOOKING_ID"]'::jsonb
);

-- Create Functions for Agreement Management
CREATE OR REPLACE FUNCTION update_agreement_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create Triggers
CREATE TRIGGER update_agreement_templates_updated_at
    BEFORE UPDATE ON public.agreement_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_agreement_updated_at();

CREATE TRIGGER update_agreement_template_sections_updated_at
    BEFORE UPDATE ON public.agreement_template_sections
    FOR EACH ROW
    EXECUTE FUNCTION update_agreement_updated_at();
