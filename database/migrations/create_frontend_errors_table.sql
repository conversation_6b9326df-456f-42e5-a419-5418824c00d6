-- Frontend Errors Table Migration
-- Execute this in Supabase SQL Editor to create the frontend_errors table
-- for logging frontend errors in production

-- Enable necessary extensions if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum for error severity levels
CREATE TYPE error_severity AS ENUM ('low', 'medium', 'high', 'critical');

-- Create enum for error sources
CREATE TYPE error_source AS ENUM ('user-action', 'api', 'component', 'network', 'unknown');

-- Create the frontend_errors table
CREATE TABLE public.frontend_errors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message TEXT NOT NULL,
    stack TEXT,
    context JSONB DEFAULT '{}',
    url TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional fields from ErrorLogger interface
    severity error_severity DEFAULT 'medium',
    source error_source DEFAULT 'unknown',
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Metadata for tracking and debugging
    session_id UUID,
    build_version TEXT,
    environment TEXT DEFAULT 'production',
    
    -- Indexes for performance
    CONSTRAINT frontend_errors_timestamp_check CHECK (timestamp IS NOT NULL),
    CONSTRAINT frontend_errors_message_check CHECK (length(message) > 0),
    CONSTRAINT frontend_errors_url_check CHECK (length(url) > 0)
);

-- Create indexes for optimal query performance
CREATE INDEX idx_frontend_errors_created_at ON public.frontend_errors(created_at DESC);
CREATE INDEX idx_frontend_errors_timestamp ON public.frontend_errors(timestamp DESC);
CREATE INDEX idx_frontend_errors_severity ON public.frontend_errors(severity);
CREATE INDEX idx_frontend_errors_source ON public.frontend_errors(source);
CREATE INDEX idx_frontend_errors_user_id ON public.frontend_errors(user_id);
CREATE INDEX idx_frontend_errors_url ON public.frontend_errors(url);
CREATE INDEX idx_frontend_errors_message_text ON public.frontend_errors USING gin(to_tsvector('english', message));

-- Create composite indexes for common query patterns
CREATE INDEX idx_frontend_errors_severity_created_at ON public.frontend_errors(severity, created_at DESC);
CREATE INDEX idx_frontend_errors_source_created_at ON public.frontend_errors(source, created_at DESC);
CREATE INDEX idx_frontend_errors_user_severity ON public.frontend_errors(user_id, severity) WHERE user_id IS NOT NULL;

-- Enable Row Level Security (RLS)
ALTER TABLE public.frontend_errors ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Allow authenticated users to insert their own errors
CREATE POLICY "Users can insert their own errors" ON public.frontend_errors
    FOR INSERT 
    TO authenticated 
    WITH CHECK (
        auth.uid() = user_id OR 
        user_id IS NULL  -- Allow anonymous error logging
    );

-- RLS Policy: Allow service role to insert any error (for batch operations)
CREATE POLICY "Service role can insert any error" ON public.frontend_errors
    FOR INSERT 
    TO service_role 
    WITH CHECK (true);

-- RLS Policy: Allow authenticated users to view their own errors
CREATE POLICY "Users can view their own errors" ON public.frontend_errors
    FOR SELECT 
    TO authenticated 
    USING (auth.uid() = user_id);

-- RLS Policy: Allow admin users to view all errors
CREATE POLICY "Admin users can view all errors" ON public.frontend_errors
    FOR SELECT 
    TO authenticated 
    USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND (
                email LIKE '%@admin.%' OR 
                email IN (
                    SELECT email FROM public.users 
                    WHERE id = auth.uid() 
                    AND (email LIKE '%admin%' OR email LIKE '%support%')
                )
            )
        )
    );

-- RLS Policy: Allow service role to view all errors
CREATE POLICY "Service role can view all errors" ON public.frontend_errors
    FOR SELECT 
    TO service_role 
    USING (true);

-- RLS Policy: Prevent updates and deletes for data integrity
CREATE POLICY "Prevent updates" ON public.frontend_errors
    FOR UPDATE 
    TO authenticated 
    USING (false);

CREATE POLICY "Prevent deletes except for cleanup" ON public.frontend_errors
    FOR DELETE 
    TO service_role 
    USING (created_at < NOW() - INTERVAL '90 days'); -- Allow cleanup of old errors

-- Create a function to automatically set user_id from auth context
CREATE OR REPLACE FUNCTION public.set_frontend_error_user_id()
RETURNS TRIGGER AS $$
BEGIN
    -- If user_id is not explicitly set, try to get it from auth context
    IF NEW.user_id IS NULL THEN
        NEW.user_id := auth.uid();
    END IF;
    
    -- Ensure timestamp is set if not provided
    IF NEW.timestamp IS NULL THEN
        NEW.timestamp := NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically set user_id and timestamp
CREATE TRIGGER set_frontend_error_user_id_trigger
    BEFORE INSERT ON public.frontend_errors
    FOR EACH ROW
    EXECUTE FUNCTION public.set_frontend_error_user_id();

-- Create a function for error aggregation and reporting
CREATE OR REPLACE FUNCTION public.get_frontend_error_stats(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '7 days',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE(
    total_errors BIGINT,
    errors_by_severity JSONB,
    errors_by_source JSONB,
    top_error_messages JSONB,
    top_error_urls JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT as total_errors,
        jsonb_object_agg(severity, severity_count) as errors_by_severity,
        jsonb_object_agg(source, source_count) as errors_by_source,
        jsonb_agg(
            jsonb_build_object(
                'message', message,
                'count', message_count
            ) ORDER BY message_count DESC
        ) FILTER (WHERE message_rank <= 10) as top_error_messages,
        jsonb_agg(
            jsonb_build_object(
                'url', url,
                'count', url_count
            ) ORDER BY url_count DESC
        ) FILTER (WHERE url_rank <= 10) as top_error_urls
    FROM (
        SELECT 
            severity,
            source,
            message,
            url,
            COUNT(*) OVER (PARTITION BY severity) as severity_count,
            COUNT(*) OVER (PARTITION BY source) as source_count,
            COUNT(*) OVER (PARTITION BY message) as message_count,
            COUNT(*) OVER (PARTITION BY url) as url_count,
            ROW_NUMBER() OVER (PARTITION BY message ORDER BY COUNT(*) DESC) as message_rank,
            ROW_NUMBER() OVER (PARTITION BY url ORDER BY COUNT(*) DESC) as url_rank
        FROM public.frontend_errors
        WHERE created_at BETWEEN start_date AND end_date
    ) stats
    GROUP BY severity, source, message, url, severity_count, source_count, message_count, url_count, message_rank, url_rank;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT ON public.frontend_errors TO authenticated;
GRANT INSERT ON public.frontend_errors TO authenticated;
GRANT ALL ON public.frontend_errors TO service_role;

-- Create a cleanup function for old errors (optional)
CREATE OR REPLACE FUNCTION public.cleanup_old_frontend_errors()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete errors older than 90 days
    DELETE FROM public.frontend_errors 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Optional: Create a scheduled job to clean up old errors
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-frontend-errors', '0 2 * * *', 'SELECT public.cleanup_old_frontend_errors();');

-- Create a view for error analytics (for admin/monitoring purposes)
CREATE OR REPLACE VIEW public.frontend_error_analytics AS
SELECT 
    DATE_TRUNC('day', created_at) as error_date,
    COUNT(*) as total_errors,
    COUNT(DISTINCT user_id) as unique_users_affected,
    COUNT(*) FILTER (WHERE severity = 'critical') as critical_errors,
    COUNT(*) FILTER (WHERE severity = 'high') as high_errors,
    COUNT(*) FILTER (WHERE severity = 'medium') as medium_errors,
    COUNT(*) FILTER (WHERE severity = 'low') as low_errors,
    COUNT(*) FILTER (WHERE source = 'api') as api_errors,
    COUNT(*) FILTER (WHERE source = 'component') as component_errors,
    COUNT(*) FILTER (WHERE source = 'network') as network_errors,
    COUNT(*) FILTER (WHERE source = 'user-action') as user_action_errors,
    COUNT(*) FILTER (WHERE source = 'unknown') as unknown_errors
FROM public.frontend_errors
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY error_date DESC;

-- Grant access to the view
GRANT SELECT ON public.frontend_error_analytics TO authenticated;
GRANT SELECT ON public.frontend_error_analytics TO service_role;

-- Add helpful comments
COMMENT ON TABLE public.frontend_errors IS 'Stores frontend application errors for monitoring and debugging';
COMMENT ON COLUMN public.frontend_errors.message IS 'Error message text';
COMMENT ON COLUMN public.frontend_errors.stack IS 'Error stack trace';
COMMENT ON COLUMN public.frontend_errors.context IS 'Additional context data in JSON format';
COMMENT ON COLUMN public.frontend_errors.url IS 'URL where the error occurred';
COMMENT ON COLUMN public.frontend_errors.timestamp IS 'When the error occurred on the client side';
COMMENT ON COLUMN public.frontend_errors.user_agent IS 'Browser user agent string';
COMMENT ON COLUMN public.frontend_errors.severity IS 'Error severity level';
COMMENT ON COLUMN public.frontend_errors.source IS 'Source of the error (component, api, network, etc.)';
COMMENT ON COLUMN public.frontend_errors.user_id IS 'ID of the user who encountered the error (if authenticated)';
COMMENT ON COLUMN public.frontend_errors.session_id IS 'Session ID for grouping related errors';
COMMENT ON COLUMN public.frontend_errors.build_version IS 'Application build version';
COMMENT ON COLUMN public.frontend_errors.environment IS 'Environment where error occurred (production, staging, etc.)';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Frontend errors table created successfully with RLS policies and indexes!';
    RAISE NOTICE 'Table: public.frontend_errors';
    RAISE NOTICE 'Indexes: 7 indexes created for optimal performance';
    RAISE NOTICE 'RLS Policies: 6 security policies enabled';
    RAISE NOTICE 'Functions: Error stats, cleanup, and auto-user-id functions created';
    RAISE NOTICE 'View: frontend_error_analytics for monitoring dashboard';
END $$;