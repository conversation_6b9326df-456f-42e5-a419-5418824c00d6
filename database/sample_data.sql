-- Add sample vehicles for testing
-- Execute this in Supabase SQL Editor after the main schema

INSERT INTO public.vehicles (
    provider_id, 
    category, 
    make, 
    model, 
    year, 
    engine_size, 
    transmission, 
    fuel_type,
    description,
    images,
    daily_rate,
    weekly_rate,
    monthly_rate,
    security_deposit,
    minimum_rental_days,
    maximum_rental_days,
    delivery_available,
    delivery_fee,
    delivery_radius,
    quantity,
    available_quantity,
    features,
    add_ons,
    location,
    pickup_instructions,
    active
) VALUES 
-- Bali Wheels vehicles
(
    (SELECT id FROM public.providers WHERE business_name = 'Bali Wheels'),
    'small_scooter',
    'Honda',
    'Beat Street',
    2023,
    110,
    'automatic',
    'petrol',
    'Perfect for exploring Ubud and surrounding areas. Fuel-efficient and easy to ride.',
    ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800'],
    65000.00,
    400000.00,
    1500000.00,
    200000.00,
    1,
    30,
    true,
    25000.00,
    15,
    3,
    3,
    '[{"name": "Keyless Start", "icon": "🔑"}, {"name": "LED Lights", "icon": "💡"}, {"name": "Digital Display", "icon": "📱"}]'::jsonb,
    '[{"name": "Helmet", "daily_rate": 15000, "weekly_rate": 75000, "monthly_rate": 250000}, {"name": "Phone Holder", "daily_rate": 10000, "weekly_rate": 50000, "monthly_rate": 150000}]'::jsonb,
    '{"address": "Jl. Monkey Forest Road 123, Ubud", "city": "Ubud", "latitude": -8.5069, "longitude": 115.2625, "postal_code": "80571"}'::jsonb,
    'Located behind our main shop. Look for the blue Honda scooters in the parking area.',
    true
),
(
    (SELECT id FROM public.providers WHERE business_name = 'Bali Wheels'),
    'large_scooter',
    'Honda',
    'PCX 160',
    2024,
    160,
    'automatic',
    'petrol',
    'Premium scooter perfect for longer rides and highway cruising. Very comfortable for two people.',
    ARRAY['https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=800', 'https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=800'],
    95000.00,
    600000.00,
    2200000.00,
    300000.00,
    1,
    30,
    true,
    30000.00,
    20,
    2,
    2,
    '[{"name": "ABS Brakes", "icon": "🛡️"}, {"name": "Smart Key", "icon": "🔑"}, {"name": "USB Charging", "icon": "🔌"}, {"name": "Storage Box", "icon": "📦"}]'::jsonb,
    '[{"name": "Helmet (2x)", "daily_rate": 25000, "weekly_rate": 125000, "monthly_rate": 400000}, {"name": "Surf Rack", "daily_rate": 20000, "weekly_rate": 100000, "monthly_rate": 300000}, {"name": "Raincoat", "daily_rate": 15000, "weekly_rate": 75000, "monthly_rate": 200000}]'::jsonb,
    '{"address": "Jl. Monkey Forest Road 123, Ubud", "city": "Ubud", "latitude": -8.5069, "longitude": 115.2625, "postal_code": "80571"}'::jsonb,
    'Premium section in our garage. White PCX scooters with "Bali Wheels" stickers.',
    true
),
-- Scooter King vehicles
(
    (SELECT id FROM public.providers WHERE business_name = 'Scooter King Rental'),
    'small_scooter',
    'Yamaha',
    'Mio S',
    2023,
    125,
    'automatic',
    'petrol',
    'Reliable and economical scooter perfect for beach rides in Canggu. Great for beginners.',
    ARRAY['https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=800', 'https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=800'],
    55000.00,
    350000.00,
    1300000.00,
    180000.00,
    1,
    30,
    true,
    20000.00,
    10,
    5,
    4,
    '[{"name": "Digital Panel", "icon": "📱"}, {"name": "LED Headlight", "icon": "💡"}, {"name": "Side Stand Switch", "icon": "⚡"}]'::jsonb,
    '[{"name": "Helmet", "daily_rate": 10000, "weekly_rate": 50000, "monthly_rate": 180000}, {"name": "Phone Holder", "daily_rate": 8000, "weekly_rate": 40000, "monthly_rate": 120000}]'::jsonb,
    '{"address": "Jl. Pantai Berawa 456, Canggu", "city": "Canggu", "latitude": -8.6481, "longitude": 115.1253, "postal_code": "80361"}'::jsonb,
    'Located in front of our shop. Yellow Yamaha Mio scooters with black seats.',
    true
),
(
    (SELECT id FROM public.providers WHERE business_name = 'Scooter King Rental'),
    'luxury_bike',
    'Kawasaki',
    'Ninja 250',
    2024,
    250,
    'manual',
    'petrol',
    'Sport bike for experienced riders. Perfect for mountain rides and highway touring.',
    ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800', 'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=800'],
    180000.00,
    1100000.00,
    4000000.00,
    500000.00,
    2,
    14,
    false,
    0.00,
    0,
    1,
    1,
    '[{"name": "ABS Brakes", "icon": "🛡️"}, {"name": "Digital Display", "icon": "📱"}, {"name": "LED Lights", "icon": "💡"}, {"name": "Sport Suspension", "icon": "🏍️"}]'::jsonb,
    '[{"name": "Racing Helmet", "daily_rate": 35000, "weekly_rate": 200000, "monthly_rate": 700000}, {"name": "Riding Jacket", "daily_rate": 30000, "weekly_rate": 180000, "monthly_rate": 600000}, {"name": "Gloves", "daily_rate": 15000, "weekly_rate": 85000, "monthly_rate": 300000}]'::jsonb,
    '{"address": "Jl. Pantai Berawa 456, Canggu", "city": "Canggu", "latitude": -8.6481, "longitude": 115.1253, "postal_code": "80361"}'::jsonb,
    'Secured garage access required. Green Kawasaki Ninja in the back section. Valid motorcycle license required.',
    true
),
(
    (SELECT id FROM public.providers WHERE business_name = 'Scooter King Rental'),
    'large_scooter',
    'Yamaha',
    'NMAX 155',
    2024,
    155,
    'automatic',
    'petrol',
    'Modern maxi-scooter with premium features. Excellent for touring Bali with comfort and style.',
    ARRAY['https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=800', 'https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=800'],
    85000.00,
    550000.00,
    2000000.00,
    280000.00,
    1,
    30,
    true,
    25000.00,
    15,
    3,
    3,
    '[{"name": "Smart Key", "icon": "🔑"}, {"name": "USB Port", "icon": "🔌"}, {"name": "Large Storage", "icon": "📦"}, {"name": "Traction Control", "icon": "🛡️"}]'::jsonb,
    '[{"name": "Premium Helmet (2x)", "daily_rate": 30000, "weekly_rate": 150000, "monthly_rate": 500000}, {"name": "Top Box", "daily_rate": 25000, "weekly_rate": 125000, "monthly_rate": 400000}]'::jsonb,
    '{"address": "Jl. Pantai Berawa 456, Canggu", "city": "Canggu", "latitude": -8.6481, "longitude": 115.1253, "postal_code": "80361"}'::jsonb,
    'Blue NMAX scooters in the covered area. Keys available at front desk.',
    true
);

-- Add some sample bookings
INSERT INTO public.bookings (
    user_id,
    provider_id,
    vehicle_id,
    start_date,
    end_date,
    total_days,
    vehicle_rate,
    add_ons_total,
    delivery_fee,
    subtotal,
    commission_rate,
    commission_amount,
    total,
    payment_method,
    payment_status,
    booking_status,
    selected_add_ons,
    delivery_requested,
    pickup_instructions
) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440001',
    (SELECT id FROM public.providers WHERE business_name = 'Bali Wheels'),
    (SELECT id FROM public.vehicles WHERE make = 'Honda' AND model = 'Beat Street' LIMIT 1),
    '2025-01-15',
    '2025-01-22',
    7,
    65000.00,
    25000.00,
    25000.00,
    515000.00,
    15.00,
    77250.00,
    515000.00,
    'card',
    'paid',
    'active',
    '[{"name": "Helmet", "quantity": 1, "daily_rate": 15000, "total_cost": 105000}]'::jsonb,
    true,
    'Pick up near the Monkey Forest entrance. White helmet provided.'
);

-- Add some sample reviews
INSERT INTO public.reviews (
    booking_id,
    user_id,
    provider_id,
    vehicle_id,
    rating,
    comment
) VALUES 
(
    (SELECT id FROM public.bookings WHERE booking_status = 'active' LIMIT 1),
    '550e8400-e29b-41d4-a716-446655440001',
    (SELECT id FROM public.providers WHERE business_name = 'Bali Wheels'),
    (SELECT id FROM public.vehicles WHERE make = 'Honda' AND model = 'Beat Street' LIMIT 1),
    5,
    'Perfect scooter for exploring Ubud! Sarah was very helpful and the bike was in excellent condition. Highly recommended for first-time riders in Bali.'
);