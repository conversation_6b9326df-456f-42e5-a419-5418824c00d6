-- =============================================================================
-- RENTAHUB PRODUCTION SAMPLE DATA
-- =============================================================================
-- Realistic sample data for testing and demonstration
-- Execute this AFTER running production_schema.sql

-- =============================================================================
-- SAMPLE USERS (Customers, Providers, Admins)
-- =============================================================================

-- Insert sample customers
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, role, email_verified, city, country, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'John', 'Doe', '+62812345678', 'CUSTOMER', true, 'Jakarta', 'Indonesia', NOW() - INTERVAL '30 days'),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Jane', 'Smith', '+62812345679', 'CUSTOMER', true, 'Bandung', 'Indonesia', NOW() - INTERVAL '25 days'),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Mike', 'Wilson', '+***********', 'CUSTOMER', true, 'Surabaya', 'Indonesia', NOW() - INTERVAL '20 days'),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Sarah', 'Johnson', '+***********', 'CUSTOMER', true, 'Jakarta', 'Indonesia', NOW() - INTERVAL '15 days');

-- Insert sample providers
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, role, email_verified, business_name, business_license, verification_status, rating, total_reviews, total_vehicles, total_earnings, city, country, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Ahmad', 'Rental', '+***********', 'PROVIDER', true, 'Jakarta Bike Rental', 'JKT-001-2023', 'VERIFIED', 4.8, 45, 8, 15750000.00, 'Jakarta', 'Indonesia', NOW() - INTERVAL '90 days'),
('550e8400-e29b-41d4-a716-446655440011', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Siti', 'Scooter', '+***********', 'PROVIDER', true, 'Bandung Scooter Hub', 'BDG-002-2023', 'VERIFIED', 4.6, 32, 6, 12400000.00, 'Bandung', 'Indonesia', NOW() - INTERVAL '75 days'),
('550e8400-e29b-41d4-a716-446655440012', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Budi', 'Motors', '+***********', 'PROVIDER', true, 'Surabaya Motor Rental', 'SBY-003-2023', 'VERIFIED', 4.7, 28, 5, 9800000.00, 'Surabaya', 'Indonesia', NOW() - INTERVAL '60 days');

-- Insert admin user
INSERT INTO users (id, email, password_hash, first_name, last_name, phone, role, email_verified, city, country, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440020', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', 'Admin', 'RentaHub', '+62812345700', 'ADMIN', true, 'Jakarta', 'Indonesia', NOW() - INTERVAL '120 days');

-- =============================================================================
-- SAMPLE VEHICLES
-- =============================================================================

-- Jakarta Provider Vehicles
INSERT INTO vehicles (id, provider_id, make, model, year, category, daily_rate, weekly_rate, monthly_rate, status, location_city, location_address, description, features, images, license_plate, engine_size, fuel_type, transmission, helmet_included, insurance_included, deposit_required, rating, total_reviews, total_bookings, created_at) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', 'Honda', 'Scoopy', 2023, 'small_scooter', 75000, 450000, 1800000, 'available', 'Jakarta', 'Jl. Sudirman No. 123, Jakarta Pusat', 'Perfect city scooter for daily commuting. Fuel efficient and easy to park.', ARRAY['Helmet included', 'Fuel efficient', 'Easy to park', 'Storage compartment'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'B 1234 ABC', '110cc', 'gasoline', 'automatic', true, false, 200000, 4.5, 12, 25, NOW() - INTERVAL '80 days'),
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440010', 'Yamaha', 'NMAX', 2023, 'large_scooter', 120000, 720000, 2880000, 'available', 'Jakarta', 'Jl. Thamrin No. 456, Jakarta Pusat', 'Comfortable scooter for longer rides with premium features.', ARRAY['Helmet included', 'Storage space', 'Comfortable seat', 'USB charging'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'B 5678 DEF', '155cc', 'gasoline', 'automatic', true, true, 300000, 4.7, 18, 32, NOW() - INTERVAL '75 days'),
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440010', 'Kawasaki', 'Ninja 250', 2022, 'luxury_bike', 250000, 1500000, 6000000, 'available', 'Jakarta', 'Jl. Gatot Subroto No. 789, Jakarta Selatan', 'Sport bike for enthusiasts. High performance and thrilling ride.', ARRAY['Full gear included', 'High performance', 'Sport riding position', 'ABS brakes'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'B 9012 GHI', '249cc', 'gasoline', 'manual', true, true, 500000, 4.8, 8, 15, NOW() - INTERVAL '70 days');

-- Bandung Provider Vehicles
INSERT INTO vehicles (id, provider_id, make, model, year, category, daily_rate, weekly_rate, monthly_rate, status, location_city, location_address, description, features, images, license_plate, engine_size, fuel_type, transmission, helmet_included, insurance_included, deposit_required, rating, total_reviews, total_bookings, created_at) VALUES
('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440011', 'Honda', 'Vario', 2023, 'small_scooter', 70000, 420000, 1680000, 'available', 'Bandung', 'Jl. Asia Afrika No. 100, Bandung', 'Reliable and economical scooter perfect for Bandung city tours.', ARRAY['Helmet included', 'Fuel efficient', 'Comfortable', 'Storage space'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'D 3456 JKL', '125cc', 'gasoline', 'automatic', true, false, 200000, 4.4, 15, 28, NOW() - INTERVAL '65 days'),
('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440011', 'Yamaha', 'Aerox', 2023, 'large_scooter', 110000, 660000, 2640000, 'available', 'Bandung', 'Jl. Braga No. 200, Bandung', 'Sporty scooter with modern design and excellent performance.', ARRAY['Helmet included', 'Sporty design', 'LED lights', 'Digital dashboard'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'D 7890 MNO', '155cc', 'gasoline', 'automatic', true, false, 300000, 4.6, 12, 22, NOW() - INTERVAL '60 days');

-- Surabaya Provider Vehicles
INSERT INTO vehicles (id, provider_id, make, model, year, category, daily_rate, weekly_rate, monthly_rate, status, location_city, location_address, description, features, images, license_plate, engine_size, fuel_type, transmission, helmet_included, insurance_included, deposit_required, rating, total_reviews, total_bookings, created_at) VALUES
('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440012', 'Honda', 'Beat', 2023, 'small_scooter', 65000, 390000, 1560000, 'available', 'Surabaya', 'Jl. Pemuda No. 300, Surabaya', 'Compact and efficient scooter ideal for city navigation.', ARRAY['Helmet included', 'Compact size', 'Fuel efficient', 'Easy handling'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'L 1357 PQR', '110cc', 'gasoline', 'automatic', true, false, 150000, 4.3, 10, 18, NOW() - INTERVAL '55 days'),
('660e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440012', 'Yamaha', 'Mio', 2022, 'small_scooter', 68000, 408000, 1632000, 'available', 'Surabaya', 'Jl. Tunjungan No. 400, Surabaya', 'Popular scooter with proven reliability and comfort.', ARRAY['Helmet included', 'Reliable', 'Comfortable seat', 'Good fuel economy'], ARRAY['https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400'], 'L 2468 STU', '115cc', 'gasoline', 'automatic', true, false, 150000, 4.2, 8, 14, NOW() - INTERVAL '50 days');

-- =============================================================================
-- SAMPLE BOOKINGS
-- =============================================================================

-- Completed bookings
INSERT INTO bookings (id, customer_id, provider_id, vehicle_id, start_date, end_date, start_time, end_time, pickup_location, dropoff_location, status, total_amount, base_amount, tax_amount, service_fee, payment_status, payment_method, stripe_payment_intent_id, created_at, updated_at) VALUES
('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440001', '2024-01-15', '2024-01-17', '09:00', '18:00', 'Hotel Indonesia, Jakarta', 'Hotel Indonesia, Jakarta', 'completed', 165000, 150000, 15000, 0, 'paid', 'stripe', 'pi_test_completed_001', NOW() - INTERVAL '20 days', NOW() - INTERVAL '18 days'),
('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440011', '660e8400-e29b-41d4-a716-446655440004', '2024-01-20', '2024-01-22', '10:00', '17:00', 'Bandung Station', 'Bandung Station', 'completed', 154000, 140000, 14000, 0, 'paid', 'stripe', 'pi_test_completed_002', NOW() - INTERVAL '15 days', NOW() - INTERVAL '13 days'),
('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440012', '660e8400-e29b-41d4-a716-446655440006', '2024-01-25', '2024-01-26', '08:00', '20:00', 'Juanda Airport, Surabaya', 'Juanda Airport, Surabaya', 'completed', 71500, 65000, 6500, 0, 'paid', 'stripe', 'pi_test_completed_003', NOW() - INTERVAL '10 days', NOW() - INTERVAL '9 days');

-- Active bookings
INSERT INTO bookings (id, customer_id, provider_id, vehicle_id, start_date, end_date, start_time, end_time, pickup_location, dropoff_location, status, total_amount, base_amount, tax_amount, service_fee, payment_status, payment_method, stripe_payment_intent_id, created_at, updated_at) VALUES
('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440002', CURRENT_DATE, CURRENT_DATE + INTERVAL '2 days', '09:00', '18:00', 'Soekarno-Hatta Airport', 'Soekarno-Hatta Airport', 'active', 264000, 240000, 24000, 0, 'paid', 'stripe', 'pi_test_active_001', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day');

-- Pending bookings
INSERT INTO bookings (id, customer_id, provider_id, vehicle_id, start_date, end_date, start_time, end_time, pickup_location, dropoff_location, status, total_amount, base_amount, tax_amount, service_fee, payment_status, payment_method, created_at, updated_at) VALUES
('770e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440011', '660e8400-e29b-41d4-a716-446655440005', CURRENT_DATE + INTERVAL '3 days', CURRENT_DATE + INTERVAL '5 days', '10:00', '17:00', 'Bandung City Center', 'Bandung City Center', 'pending', 242000, 220000, 22000, 0, 'pending', 'stripe', NOW(), NOW());

-- =============================================================================
-- SAMPLE PAYMENTS
-- =============================================================================

INSERT INTO payments (id, booking_id, customer_id, provider_id, amount, currency, payment_method, payment_status, stripe_payment_intent_id, provider_payout_amount, platform_fee, processing_fee, created_at) VALUES
('880e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', 165000, 'IDR', 'stripe', 'completed', 'pi_test_completed_001', 148500, 16500, 0, NOW() - INTERVAL '20 days'),
('880e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440011', 154000, 'IDR', 'stripe', 'completed', 'pi_test_completed_002', 138600, 15400, 0, NOW() - INTERVAL '15 days'),
('880e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440012', 71500, 'IDR', 'stripe', 'completed', 'pi_test_completed_003', 64350, 7150, 0, NOW() - INTERVAL '10 days'),
('880e8400-e29b-41d4-a716-446655440004', '770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440010', 264000, 'IDR', 'stripe', 'completed', 'pi_test_active_001', 237600, 26400, 0, NOW() - INTERVAL '1 day');

-- =============================================================================
-- SAMPLE REVIEWS
-- =============================================================================

INSERT INTO reviews (id, booking_id, customer_id, provider_id, vehicle_id, rating, comment, cleanliness_rating, communication_rating, accuracy_rating, value_rating, created_at) VALUES
('990e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440010', '660e8400-e29b-41d4-a716-446655440001', 5, 'Excellent scooter! Very clean and well-maintained. Perfect for exploring Jakarta.', 5, 5, 5, 5, NOW() - INTERVAL '18 days'),
('990e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440011', '660e8400-e29b-41d4-a716-446655440004', 4, 'Good scooter for city tours. Provider was responsive and helpful.', 4, 5, 4, 4, NOW() - INTERVAL '13 days'),
('990e8400-e29b-41d4-a716-446655440003', '770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440012', '660e8400-e29b-41d4-a716-446655440006', 4, 'Reliable transportation. Good value for money.', 4, 4, 4, 5, NOW() - INTERVAL '9 days');

-- =============================================================================
-- SAMPLE ANALYTICS DATA
-- =============================================================================

-- Platform-wide analytics for the last 30 days
INSERT INTO analytics (date, provider_id, total_bookings, total_revenue, total_vehicles, active_vehicles, occupancy_rate, average_booking_value, created_at) VALUES
(CURRENT_DATE - INTERVAL '1 day', NULL, 15, 2500000, 8, 7, 0.6250, 166667, NOW()),
(CURRENT_DATE - INTERVAL '2 days', NULL, 12, 1980000, 8, 7, 0.5000, 165000, NOW()),
(CURRENT_DATE - INTERVAL '3 days', NULL, 18, 3200000, 8, 8, 0.7500, 177778, NOW());

-- Provider-specific analytics
INSERT INTO analytics (date, provider_id, total_bookings, total_revenue, total_vehicles, active_vehicles, occupancy_rate, average_booking_value, created_at) VALUES
(CURRENT_DATE - INTERVAL '1 day', '550e8400-e29b-41d4-a716-446655440010', 8, 1400000, 3, 3, 0.8889, 175000, NOW()),
(CURRENT_DATE - INTERVAL '1 day', '550e8400-e29b-41d4-a716-446655440011', 4, 680000, 2, 2, 0.6667, 170000, NOW()),
(CURRENT_DATE - INTERVAL '1 day', '550e8400-e29b-41d4-a716-446655440012', 3, 420000, 2, 2, 0.5000, 140000, NOW());
