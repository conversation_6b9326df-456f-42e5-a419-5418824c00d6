name: Deploy to Railway

on:
  push:
    branches: [main]
  workflow_run:
    workflows: ["CI/CD Pipeline"]
    types: [completed]
    branches: [main]

jobs:
  deploy-backend:
    name: Deploy Backend to Railway
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event.workflow_run.conclusion == 'success')
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
          
      - name: Install Dependencies
        working-directory: ./backend
        run: npm ci
        
      - name: Generate Prisma Client
        working-directory: ./backend
        run: npx prisma generate
        
      - name: Build Backend
        working-directory: ./backend
        run: npm run build
        
      - name: Deploy Backend to Railway
        uses: railwayapp/action@v2
        with:
          railwayToken: ${{ secrets.RAILWAY_TOKEN }}
          
      - name: Wait for Backend Deployment
        run: |
          echo "⏳ Waiting for backend deployment..."
          sleep 60
          
      - name: Health Check Backend
        run: |
          BACKEND_URL="${{ secrets.RAILWAY_BACKEND_URL }}"
          if [ -n "$BACKEND_URL" ]; then
            for i in {1..10}; do
              if curl -f "$BACKEND_URL/health"; then
                echo "✅ Backend health check passed"
                break
              fi
              echo "🔄 Attempt $i failed, retrying in 10s..."
              sleep 10
            done
          else
            echo "⚠️ RAILWAY_BACKEND_URL not set"
          fi

  deploy-frontend:
    name: Deploy Frontend to Railway
    runs-on: ubuntu-latest
    needs: deploy-backend
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install Dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Build Frontend
        working-directory: ./frontend
        run: npm run build:production
        env:
          VITE_API_URL: ${{ secrets.RAILWAY_BACKEND_URL }}
          
      - name: Deploy Frontend to Railway
        uses: railwayapp/action@v2
        with:
          railwayToken: ${{ secrets.RAILWAY_FRONTEND_TOKEN }}
          
      - name: Health Check Frontend
        run: |
          FRONTEND_URL="${{ secrets.RAILWAY_FRONTEND_URL }}"
          if [ -n "$FRONTEND_URL" ]; then
            for i in {1..10}; do
              if curl -f "$FRONTEND_URL"; then
                echo "✅ Frontend health check passed"
                break
              fi
              echo "🔄 Attempt $i failed, retrying in 10s..."
              sleep 10
            done
          else
            echo "⚠️ RAILWAY_FRONTEND_URL not set"
          fi

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-backend, deploy-frontend]
    if: always()
    
    steps:
      - name: Deployment Success
        if: needs.deploy-backend.result == 'success' && needs.deploy-frontend.result == 'success'
        run: |
          echo "🚀 Deployment successful!"
          echo "Backend: ${{ secrets.RAILWAY_BACKEND_URL }}"
          echo "Frontend: ${{ secrets.RAILWAY_FRONTEND_URL }}"
          
      - name: Deployment Failure
        if: needs.deploy-backend.result == 'failure' || needs.deploy-frontend.result == 'failure'
        run: |
          echo "💥 Deployment failed!"
          echo "Backend status: ${{ needs.deploy-backend.result }}"
          echo "Frontend status: ${{ needs.deploy-frontend.result }}"
          exit 1 