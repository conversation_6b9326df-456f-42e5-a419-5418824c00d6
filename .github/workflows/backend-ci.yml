name: Backend CI

on:
  push:
    branches: [main]
    paths:
      - 'backend/**'
      - '.github/workflows/backend-ci.yml'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json
      - name: Install Dependencies
        working-directory: ./backend
        run: npm ci
      - name: Generate Prisma Client
        working-directory: ./backend
        run: npx prisma generate
      - name: Lint
        working-directory: ./backend
        run: npm run lint
      - name: Build
        working-directory: ./backend
        run: npm run build 