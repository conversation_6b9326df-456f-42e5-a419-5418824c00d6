name: PR Preview Deployment

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, develop ]
  pull_request:
    types: [closed]
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'

jobs:
  # ============================================================================
  # BUILD PR PREVIEW
  # ============================================================================
  build-preview:
    name: 🏗️ Build PR Preview
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'
    
    outputs:
      preview-url: ${{ steps.deploy.outputs.preview-url }}
      backend-url: ${{ steps.deploy.outputs.backend-url }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            frontend/package-lock.json

      - name: 📥 Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: 📥 Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🏗️ Build backend
        working-directory: ./backend
        run: npm run build

      - name: 🏗️ Build frontend
        working-directory: ./frontend
        env:
          VITE_API_URL: https://rentahub-backend-pr-${{ github.event.number }}.up.railway.app
        run: npm run build

      - name: 🚀 Deploy to Railway Preview
        id: deploy
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_PREVIEW_TOKEN }}
          PR_NUMBER: ${{ github.event.number }}
        run: |
          npm install -g @railway/cli
          railway login --token $RAILWAY_TOKEN
          
          # Create or update preview environment
          PREVIEW_ENV="pr-$PR_NUMBER"
          
          # Deploy backend
          railway up --service backend --environment $PREVIEW_ENV
          BACKEND_URL="https://rentahub-backend-pr-$PR_NUMBER.up.railway.app"
          
          # Deploy frontend
          railway up --service frontend --environment $PREVIEW_ENV
          FRONTEND_URL="https://rentahub-frontend-pr-$PR_NUMBER.up.railway.app"
          
          echo "backend-url=$BACKEND_URL" >> $GITHUB_OUTPUT
          echo "preview-url=$FRONTEND_URL" >> $GITHUB_OUTPUT

      - name: 🔍 Health check preview deployment
        run: |
          sleep 30
          curl -f ${{ steps.deploy.outputs.backend-url }}/health || echo "Backend health check failed"
          curl -f ${{ steps.deploy.outputs.preview-url }} || echo "Frontend health check failed"

  # ============================================================================
  # RUN PREVIEW TESTS
  # ============================================================================
  test-preview:
    name: 🧪 Test PR Preview
    runs-on: ubuntu-latest
    needs: build-preview
    if: github.event.action != 'closed'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📥 Install dependencies
        working-directory: ./backend
        run: npm ci

      - name: 🎭 Install Playwright browsers
        working-directory: ./backend
        run: npx playwright install --with-deps chromium

      - name: 🧪 Run smoke tests against preview
        working-directory: ./backend
        env:
          NODE_ENV: test
          FRONTEND_URL: ${{ needs.build-preview.outputs.preview-url }}
          BACKEND_URL: ${{ needs.build-preview.outputs.backend-url }}
        run: |
          # Run a subset of E2E tests against the preview environment
          npx playwright test --project=chromium --grep="smoke|critical" || true

      - name: 📤 Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: preview-test-results-${{ github.event.number }}
          path: backend/test-results/
          retention-days: 3

  # ============================================================================
  # COMMENT ON PR
  # ============================================================================
  comment-preview:
    name: 💬 Comment Preview Links
    runs-on: ubuntu-latest
    needs: [build-preview, test-preview]
    if: github.event.action != 'closed' && always()
    
    steps:
      - name: 💬 Comment PR with preview links
        uses: actions/github-script@v7
        with:
          script: |
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const botComment = comments.find(comment => 
              comment.user.type === 'Bot' && 
              comment.body.includes('🚀 PR Preview Deployed')
            );

            const previewUrl = '${{ needs.build-preview.outputs.preview-url }}';
            const backendUrl = '${{ needs.build-preview.outputs.backend-url }}';
            const testStatus = '${{ needs.test-preview.result }}';
            
            const body = `## 🚀 PR Preview Deployed

            Your pull request has been deployed to a preview environment!

            ### 🔗 Preview Links
            - **🌐 Frontend Preview:** ${previewUrl}
            - **⚡ Backend API:** ${backendUrl}
            - **❤️ Health Check:** ${backendUrl}/health

            ### 🧪 Test Results
            - **Smoke Tests:** ${testStatus === 'success' ? '✅ Passed' : testStatus === 'failure' ? '❌ Failed' : '⏳ Running'}

            ### 📋 Quick Actions
            - [View Deployment Logs](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            - [Test the Preview](${previewUrl})
            - [Check API Health](${backendUrl}/health)

            ---
            
            **🔄 This comment will be updated when you push new changes.**
            
            <sub>Preview environment will be automatically cleaned up when the PR is closed.</sub>`;

            if (botComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: body
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: body
              });
            }

  # ============================================================================
  # CLEANUP PREVIEW ENVIRONMENT
  # ============================================================================
  cleanup-preview:
    name: 🧹 Cleanup Preview Environment
    runs-on: ubuntu-latest
    if: github.event.action == 'closed'
    
    steps:
      - name: 🧹 Remove Railway preview environment
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_PREVIEW_TOKEN }}
          PR_NUMBER: ${{ github.event.number }}
        run: |
          npm install -g @railway/cli
          railway login --token $RAILWAY_TOKEN
          
          # Remove preview environment
          PREVIEW_ENV="pr-$PR_NUMBER"
          railway environment delete $PREVIEW_ENV --yes || echo "Environment may not exist"

      - name: 💬 Comment cleanup status
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `## 🧹 Preview Environment Cleaned Up

              The preview environment for this PR has been automatically removed.

              **Cleaned up:**
              - ✅ Railway preview environment \`pr-${{ github.event.number }}\`
              - ✅ Associated resources and deployments

              Thank you for your contribution! 🎉`
            });

  # ============================================================================
  # LIGHTHOUSE PERFORMANCE AUDIT
  # ============================================================================
  lighthouse-audit:
    name: 🔍 Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: build-preview
    if: github.event.action != 'closed'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Run Lighthouse audit
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            ${{ needs.build-preview.outputs.preview-url }}
            ${{ needs.build-preview.outputs.preview-url }}/vehicles
            ${{ needs.build-preview.outputs.preview-url }}/auth/login
          configPath: './.lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: 💬 Comment Lighthouse results
        uses: actions/github-script@v7
        if: always()
        with:
          script: |
            const fs = require('fs');
            
            // This would read Lighthouse results and comment on PR
            // Implementation depends on Lighthouse CI output format
            
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `## 🔍 Lighthouse Performance Audit

              Performance audit has been completed for your preview deployment.

              **Audited Pages:**
              - Homepage
              - Vehicle Search
              - Login Page

              📊 [View detailed Lighthouse report](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

              *Performance scores will be available in the action artifacts.*`
            });
