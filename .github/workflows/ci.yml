name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20.x'

jobs:
  # Security and quality checks
  security-check:
    name: Security & Quality Checks
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Check for credentials in code
        run: |
          if grep -r -E "(sk_live_|pk_live_)" . --exclude-dir=node_modules --exclude-dir=.git; then
            echo "❌ Live credentials found in code!"
            exit 1
          fi
          echo "✅ No live credentials found"

      - name: Validate environment configuration
        run: |
          if [ -f "scripts/validate-env.js" ]; then
            node scripts/validate-env.js || echo "⚠️ Environment validation warnings"
          fi

  # Backend tests and build
  backend:
    name: Backend Build & Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Lint code
        run: npm run lint

      - name: Type check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test
        env:
          NODE_ENV: test

      - name: Build application
        run: npm run build

      - name: Test health endpoint
        run: |
          npm start &
          SERVER_PID=$!
          sleep 10
          curl -f http://localhost:8080/health || exit 1
          kill $SERVER_PID

  # Frontend tests and build  
  frontend:
    name: Frontend Build & Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
        
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npx tsc --noEmit

      - name: Run tests
        run: npm test

      - name: Build for production
        run: npm run build:production

      - name: Check build output
        run: |
          ls -la dist/
          if [ ! -f "dist/index.html" ]; then
            echo "❌ Build failed - index.html not found"
            exit 1
          fi
          echo "✅ Build successful"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist/
          retention-days: 7

  # Integration tests
  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend, frontend]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: Generate Prisma client
        working-directory: ./backend
        run: npx prisma generate

      - name: Start backend
        working-directory: ./backend
        run: npm start &

      - name: Wait for backend
        run: |
          timeout 30 bash -c 'until curl -f http://localhost:8080/health; do sleep 1; done'

      - name: Download frontend build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: frontend-build/

      - name: Serve frontend
        run: |
          npm install -g serve
          serve -s frontend-build -p 3000 &

      - name: Wait for frontend
        run: |
          timeout 30 bash -c 'until curl -f http://localhost:3000; do sleep 1; done'

      - name: Run integration tests
        working-directory: ./backend
        run: npm run test:integration

  # Production readiness check
  production-ready:
    name: Production Readiness
    runs-on: ubuntu-latest
    needs: [security-check, backend, frontend]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Check production configuration
        run: |
          echo "🔍 Checking production readiness..."
          
          # Check for required files
          required_files=(
            "backend/package.json"
            "frontend/package.json" 
            "backend/.env.example"
            "frontend/.env.example"
            ".gitignore"
          )
          
          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ Missing required file: $file"
              exit 1
            fi
          done
          
          # Check that .env files are not tracked
          if git ls-files | grep -E "\.env$" | grep -v "\.env\.example$"; then
            echo "❌ .env files are being tracked in git"
            exit 1
          fi
          
          echo "✅ Production readiness checks passed"

      - name: Performance check
        run: |
          echo "📊 Performance metrics check would go here"
          # Add lighthouse CI or other performance checks
          
      - name: Security scan
        run: |
          echo "🔒 Security scan would go here"  
          # Add security scanning tools like Snyk, SAST, etc.

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration, production-ready]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Railway
        uses: railwayapp/action@v2
        with:
          railwayToken: ${{ secrets.RAILWAY_TOKEN }}
          
      - name: Health check after deployment
        run: |
          echo "🏥 Waiting for deployment to be healthy..."
          sleep 30
          
          # Replace with your actual production URL
          PROD_URL="${{ secrets.PRODUCTION_URL }}"
          if [ -n "$PROD_URL" ]; then
            curl -f "$PROD_URL/health" || exit 1
            echo "✅ Deployment health check passed"
          else
            echo "⚠️ PRODUCTION_URL not set, skipping health check"
          fi

      - name: Notify deployment success
        if: success()
        run: |
          echo "🚀 Deployment successful!"
          # Add Slack/Discord/email notification here
          
      - name: Notify deployment failure  
        if: failure()
        run: |
          echo "💥 Deployment failed!"
          # Add failure notification here