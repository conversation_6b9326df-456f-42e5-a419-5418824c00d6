name: RentaHub CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ============================================================================
  # LINTING & CODE QUALITY
  # ============================================================================
  lint:
    name: 🔍 Lint & Code Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            frontend/package-lock.json

      - name: 📥 Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: 📥 Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🔍 Lint backend code
        working-directory: ./backend
        run: npm run lint

      - name: 🔍 Lint frontend code
        working-directory: ./frontend
        run: npm run lint

      - name: 🔍 TypeScript type checking (backend)
        working-directory: ./backend
        run: npx tsc --noEmit

      - name: 🔍 TypeScript type checking (frontend)
        working-directory: ./frontend
        run: npx tsc --noEmit

  # ============================================================================
  # BUILD VALIDATION
  # ============================================================================
  build:
    name: 🏗️ Build Validation
    runs-on: ubuntu-latest
    needs: lint
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ matrix.component }}/package-lock.json

      - name: 📥 Install dependencies
        working-directory: ./${{ matrix.component }}
        run: npm ci

      - name: 🏗️ Build ${{ matrix.component }}
        working-directory: ./${{ matrix.component }}
        run: npm run build

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.component }}-build
          path: ${{ matrix.component }}/dist
          retention-days: 1

  # ============================================================================
  # BACKEND TESTING
  # ============================================================================
  test-backend:
    name: 🧪 Backend Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_rentahub
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📥 Install dependencies
        working-directory: ./backend
        run: npm ci

      - name: 🏗️ Build backend
        working-directory: ./backend
        run: npm run build

      - name: 🧪 Run unit tests
        working-directory: ./backend
        env:
          NODE_ENV: test
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          JWT_SECRET: test_jwt_secret_for_ci
        run: npm run test:unit

      - name: 🧪 Run integration tests
        working-directory: ./backend
        env:
          NODE_ENV: test
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          JWT_SECRET: test_jwt_secret_for_ci
        run: npm run test:integration

      - name: 📊 Generate coverage report
        working-directory: ./backend
        env:
          NODE_ENV: test
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          JWT_SECRET: test_jwt_secret_for_ci
        run: npm run test:coverage

      - name: 📤 Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false

  # ============================================================================
  # E2E TESTING
  # ============================================================================
  test-e2e:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            frontend/package-lock.json

      - name: 📥 Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: 📥 Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: 🎭 Install Playwright browsers
        working-directory: ./backend
        run: npx playwright install --with-deps

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: backend-build
          path: backend/dist

      - name: 📥 Download frontend build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist

      - name: 🎭 Run Playwright tests
        working-directory: ./backend
        env:
          NODE_ENV: test
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          JWT_SECRET: test_jwt_secret_for_ci
          FRONTEND_URL: http://localhost:3000
        run: npm run test:playwright

      - name: 📤 Upload Playwright report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: backend/playwright-report/
          retention-days: 7

  # ============================================================================
  # SECURITY SCANNING
  # ============================================================================
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            frontend/package-lock.json

      - name: 🔒 Run npm audit (backend)
        working-directory: ./backend
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: 🔒 Run npm audit (frontend)
        working-directory: ./frontend
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: 🔒 Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: 🔒 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # ============================================================================
  # DEPLOYMENT TO STAGING
  # ============================================================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, test-backend]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: backend-build
          path: backend/dist

      - name: 📥 Download frontend build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist

      - name: 🚀 Deploy to Railway (Staging)
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_STAGING_TOKEN }}
        run: |
          npm install -g @railway/cli
          railway login --token $RAILWAY_TOKEN
          railway up --service backend --environment staging
          railway up --service frontend --environment staging

      - name: 🔍 Health check staging deployment
        run: |
          sleep 30
          curl -f ${{ secrets.STAGING_BACKEND_URL }}/health || exit 1
          curl -f ${{ secrets.STAGING_FRONTEND_URL }} || exit 1

      - name: 💬 Notify deployment status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

  # ============================================================================
  # DEPLOYMENT TO PRODUCTION
  # ============================================================================
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, test-backend, test-e2e, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: backend-build
          path: backend/dist

      - name: 📥 Download frontend build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist

      - name: 🏷️ Create release tag
        id: tag
        run: |
          TAG="v$(date +'%Y.%m.%d')-$(git rev-parse --short HEAD)"
          echo "tag=$TAG" >> $GITHUB_OUTPUT
          git tag $TAG
          git push origin $TAG

      - name: 🚀 Deploy to Railway (Production)
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_PRODUCTION_TOKEN }}
        run: |
          npm install -g @railway/cli
          railway login --token $RAILWAY_TOKEN
          railway up --service backend --environment production
          railway up --service frontend --environment production

      - name: 🔍 Health check production deployment
        run: |
          sleep 60
          curl -f ${{ secrets.PRODUCTION_BACKEND_URL }}/health || exit 1
          curl -f ${{ secrets.PRODUCTION_FRONTEND_URL }} || exit 1

      - name: 📋 Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.tag.outputs.tag }}
          release_name: Release ${{ steps.tag.outputs.tag }}
          body: |
            ## 🚀 Production Deployment

            **Deployed at:** $(date +'%Y-%m-%d %H:%M:%S UTC')
            **Commit:** ${{ github.sha }}
            **Branch:** ${{ github.ref_name }}

            ### ✅ Validation Passed
            - ✅ Linting and code quality
            - ✅ Build validation
            - ✅ Unit and integration tests
            - ✅ End-to-end tests
            - ✅ Security scanning

            ### 🔗 Links
            - **Frontend:** ${{ secrets.PRODUCTION_FRONTEND_URL }}
            - **Backend API:** ${{ secrets.PRODUCTION_BACKEND_URL }}
            - **Health Check:** ${{ secrets.PRODUCTION_BACKEND_URL }}/health
          draft: false
          prerelease: false

      - name: 💬 Notify production deployment
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#production'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            🌟 Production deployment ${{ job.status }}!
            Tag: ${{ steps.tag.outputs.tag }}
            Frontend: ${{ secrets.PRODUCTION_FRONTEND_URL }}
            Backend: ${{ secrets.PRODUCTION_BACKEND_URL }}
