# =============================================================================
# RAILWAY IGNORE - EXCLUDE FILES FROM DEPLOYMENT
# =============================================================================

# Test files and directories
__tests__/
**/__tests__/
**/__integration__/
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
*.test.ts
*.test.js
*.spec.ts
*.spec.js

# Test configuration
jest.config.js
jest.config.ts
playwright.config.ts
vitest.config.ts
cypress.config.js

# Development files
*.log
*.tmp
.env.local
.env.development
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Documentation
README.md
CHANGELOG.md
docs/

# Git
.git/
.gitignore

# Node modules (handled by package.json)
node_modules/

# Build artifacts that shouldn't be included
coverage/
.nyc_output/

# Temporary files
temp/
tmp/
*.tmp

# Development scripts
scripts/dev-*
scripts/test-*

# Mock data
**/mock-data/
**/fixtures/
