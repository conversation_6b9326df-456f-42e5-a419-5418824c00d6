#!/bin/bash

# Pre-commit hook to prevent .env files from being committed
# and check for potential credential leaks

set -e

echo "🔍 Running pre-commit security checks..."

# Check for .env files being committed
env_files=$(git diff --cached --name-only | grep -E '\.env(\.|$)' | grep -v '\.env\.example$' || true)

if [ -n "$env_files" ]; then
    echo "❌ ERROR: Attempting to commit .env files!"
    echo "The following files should not be committed:"
    echo "$env_files"
    echo ""
    echo "To fix this:"
    echo "1. Remove these files from staging: git reset HEAD <file>"
    echo "2. Add them to .gitignore if not already there"
    echo "3. If accidentally committed before, remove from history"
    echo ""
    exit 1
fi

# Check for potential secrets in code
secrets_found=$(git diff --cached | grep -E -i "(api_key|secret|password|token|private_key)" | grep -E "(=|:)" | grep -v "example\|placeholder\|your_\|CHANGE_ME" || true)

if [ -n "$secrets_found" ]; then
    echo "⚠️  WARNING: Potential secrets detected in staged changes:"
    echo "$secrets_found"
    echo ""
    echo "Please review these changes to ensure no secrets are being committed."
    echo "If these are legitimate, you can bypass this check with: git commit --no-verify"
    echo ""
    read -p "Continue with commit? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run environment validation if the script exists
if [ -f "scripts/validate-env.js" ]; then
    echo "🔍 Validating environment configuration..."
    if ! node scripts/validate-env.js > /dev/null 2>&1; then
        echo "⚠️  Environment validation warnings detected."
        echo "Run 'npm run validate-env' to see details."
    fi
fi

echo "✅ Pre-commit checks passed!"