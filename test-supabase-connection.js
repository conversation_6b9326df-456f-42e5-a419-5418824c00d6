#!/usr/bin/env node

/**
 * RENTAHUB - Supabase Connection Test
 * Tests both frontend and backend Supabase connections
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

console.log('🔍 RENTAHUB Supabase Connection Test\n');

// Test Backend Connection
async function testBackendConnection() {
  console.log('🔧 Testing Backend Supabase Connection...');
  
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
  
  console.log('📋 Backend Configuration:');
  console.log(`   URL: ${supabaseUrl}`);
  console.log(`   Service Key: ${supabaseServiceKey ? `${supabaseServiceKey.substring(0, 20)}...` : 'NOT SET'}`);
  console.log(`   Anon Key: ${supabaseAnonKey ? `${supabaseAnonKey.substring(0, 20)}...` : 'NOT SET'}`);
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.log('❌ Backend: Missing Supabase credentials\n');
    return false;
  }
  
  try {
    // Test with service role key (backend)
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test connection by trying to fetch from a table
    console.log('🔍 Testing database connection...');
    const { data, error } = await supabaseAdmin.from('users').select('count').limit(1);
    
    if (error) {
      console.log(`❌ Backend Connection Error: ${error.message}`);
      console.log(`   Code: ${error.code}`);
      console.log(`   Details: ${error.details}\n`);
      return false;
    }
    
    console.log('✅ Backend: Supabase connection successful!');
    console.log(`   Response: ${JSON.stringify(data)}\n`);
    return true;
    
  } catch (error) {
    console.log(`❌ Backend Connection Exception: ${error.message}\n`);
    return false;
  }
}

// Test Frontend Configuration
function testFrontendConfig() {
  console.log('🎨 Testing Frontend Supabase Configuration...');
  
  // Load frontend .env
  const frontendEnvPath = path.join(__dirname, 'frontend', '.env');
  const frontendEnv = {};
  
  try {
    const fs = require('fs');
    const frontendEnvContent = fs.readFileSync(frontendEnvPath, 'utf8');
    
    frontendEnvContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value && key.startsWith('VITE_SUPABASE_')) {
        frontendEnv[key.trim()] = value.trim();
      }
    });
  } catch (error) {
    console.log('❌ Frontend: Could not read frontend .env file\n');
    return false;
  }
  
  console.log('📋 Frontend Configuration:');
  console.log(`   URL: ${frontendEnv.VITE_SUPABASE_URL}`);
  console.log(`   Anon Key: ${frontendEnv.VITE_SUPABASE_ANON_KEY ? `${frontendEnv.VITE_SUPABASE_ANON_KEY.substring(0, 20)}...` : 'NOT SET'}`);
  
  if (!frontendEnv.VITE_SUPABASE_URL || !frontendEnv.VITE_SUPABASE_ANON_KEY) {
    console.log('❌ Frontend: Missing Supabase credentials\n');
    return false;
  }
  
  // Check if frontend and backend URLs match
  if (frontendEnv.VITE_SUPABASE_URL !== process.env.SUPABASE_URL) {
    console.log('⚠️  Frontend and Backend Supabase URLs do not match!');
    console.log(`   Backend: ${process.env.SUPABASE_URL}`);
    console.log(`   Frontend: ${frontendEnv.VITE_SUPABASE_URL}\n`);
    return false;
  }
  
  console.log('✅ Frontend: Configuration looks good!\n');
  return true;
}

// Test Database Schema
async function testDatabaseSchema() {
  console.log('🗄️  Testing Database Schema...');
  
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.log('❌ Schema: Missing credentials\n');
    return false;
  }
  
  try {
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test key tables
    const tables = ['users', 'vehicles', 'bookings', 'providers'];
    const results = {};
    
    for (const table of tables) {
      try {
        const { data, error } = await supabaseAdmin.from(table).select('*').limit(1);
        if (error) {
          results[table] = `❌ ${error.message}`;
        } else {
          results[table] = `✅ Available (${data ? data.length : 0} sample records)`;
        }
      } catch (err) {
        results[table] = `❌ ${err.message}`;
      }
    }
    
    console.log('📊 Table Status:');
    Object.entries(results).forEach(([table, status]) => {
      console.log(`   ${table}: ${status}`);
    });
    console.log('');
    
    return true;
    
  } catch (error) {
    console.log(`❌ Schema Test Error: ${error.message}\n`);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Supabase Connection Tests...\n');
  
  const frontendConfigOk = testFrontendConfig();
  const backendConnectionOk = await testBackendConnection();
  const schemaOk = await testDatabaseSchema();
  
  console.log('📋 Test Summary:');
  console.log(`   Frontend Config: ${frontendConfigOk ? '✅' : '❌'}`);
  console.log(`   Backend Connection: ${backendConnectionOk ? '✅' : '❌'}`);
  console.log(`   Database Schema: ${schemaOk ? '✅' : '❌'}`);
  
  if (frontendConfigOk && backendConnectionOk && schemaOk) {
    console.log('\n🎉 All tests passed! Supabase is ready for deployment.');
  } else {
    console.log('\n⚠️  Some tests failed. Please fix the issues before deploying.');
  }
}

// Run the tests
runTests().catch(console.error);
