const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase credentials - using service role key for INSERT permissions
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function insertVehicleData() {
  try {
    console.log('🚀 Starting vehicle data insertion...');

    // Load scraped data from the JSON file
    const jsonPath = path.join(__dirname, 'indonesia_scooters_essential.json');
    
    if (!fs.existsSync(jsonPath)) {
      console.error('❌ Vehicle data file not found:', jsonPath);
      return;
    }

    const jsonContent = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    const vehicleData = jsonContent.data || [];
    console.log(`📊 Found ${vehicleData.length} vehicles to process`);

    if (vehicleData.length === 0) {
      console.log('❌ No vehicles found to process');
      return;
    }

    let insertedCount = 0;
    let skippedCount = 0;

    // Process vehicles in batches
    const batchSize = 10;
    for (let i = 0; i < vehicleData.length; i += batchSize) {
      const batch = vehicleData.slice(i, i + batchSize);
      
      const vehiclesToInsert = batch.map(vehicle => {
        // Determine category based on engine size
        let category = 'small_scooter';
        const engineSize = parseInt(vehicle.engineSize?.replace('cc', '')) || 125;
        if (engineSize >= 200) category = 'luxury_bike';
        else if (engineSize >= 150) category = 'large_scooter';
        else if (engineSize >= 125) category = 'medium_scooter';
        else category = 'small_scooter';

        return {
          // Essential vehicle information only
          make: vehicle.make || vehicle.brand,
          model: vehicle.model,
          year: parseInt(vehicle.year) || 2024,
          engine_size: engineSize,
          transmission: vehicle.transmission || 'automatic',
          fuel_type: 'petrol',
          category: category,
          images: vehicle.images || [],
          // Set minimal required fields with defaults
          description: `${vehicle.make || vehicle.brand} ${vehicle.model} - ${vehicle.engineSize || '125cc'} ${vehicle.transmission || 'automatic'}`,
          daily_rate: 50000, // Default placeholder
          weekly_rate: 315000, // Default placeholder
          monthly_rate: 1200000, // Default placeholder
          security_deposit: 500000, // Default placeholder
          minimum_rental_days: 1,
          maximum_rental_days: 365,
          delivery_available: false,
          delivery_fee: 0,
          delivery_radius: 0,
          quantity: 1,
          available_quantity: 1,
          features: vehicle.specs ? Object.keys(vehicle.specs) : [],
          add_ons: [],
          location: {
            address: '',
            city: '',
            latitude: 0,
            longitude: 0,
            postal_code: ''
          },
          pickup_instructions: '',
          active: true
        };
      });

      try {
        const { data, error } = await supabase
          .from('vehicles')
          .insert(vehiclesToInsert)
          .select();

        if (error) {
          console.error(`❌ Error inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
          skippedCount += batch.length;
        } else {
          console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}: ${data?.length || 0} vehicles`);
          insertedCount += data?.length || 0;
        }
      } catch (error) {
        console.error(`❌ Error processing batch ${Math.floor(i/batchSize) + 1}:`, error);
        skippedCount += batch.length;
      }
    }

    console.log('\n📈 Insertion Summary:');
    console.log(`✅ Successfully inserted: ${insertedCount} vehicles`);
    console.log(`⏭️  Skipped (errors): ${skippedCount} vehicles`);
    console.log(`📊 Total processed: ${vehicleData.length} vehicles`);

    // Verify the count
    const { data: countData, error: countError } = await supabase
      .from('vehicles')
      .select('id', { count: 'exact' });

    if (countError) {
      console.error('❌ Error counting vehicles:', countError);
    } else {
      console.log(`🎯 Total vehicles in database: ${countData?.length || 0}`);
    }

  } catch (error) {
    console.error('❌ Insertion failed:', error);
  }
}

// Run the insertion
insertVehicleData().then(() => {
  console.log('🎉 Vehicle data insertion completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Insertion failed:', error);
  process.exit(1);
}); 