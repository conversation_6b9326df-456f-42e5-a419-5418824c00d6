# Submodules (exclude completely from all deployments)
bookcars/
rentnride/

# Test files and directories
**/__tests__/
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
**/test/
**/tests/
**/*.test.*
**/*.spec.*
coverage/
.nyc_output/
jest.config.*
vitest.config.*
test-results/
playwright-report/
test-env.js
test-*.js
test-*.ts
test-*.tsx

# Dependencies
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Development files
.env.local
.env.development
.env.test
*.log
.DS_Store
.vscode/
.idea/

# Build artifacts
.cache/
.temp/
.tmp/

# Git
.git/
.gitignore
.gitmodules

# Documentation
README.md
*.md
docs/

# Scripts and configs not needed in production
scripts/
*.config.js
*.config.ts
eslint.config.js
tailwind.config.js
tsconfig.json
tsconfig.node.json

# Other
.husky/
.github/
.railway/
