# =============================================================================
# RENTAHUB - UNIFIED ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# This single file configures ALL services (backend, frontend, admin)

# =============================================================================
# 🚀 DEPLOYMENT CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3001

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================
# Primary PostgreSQL Database (Prisma)
DATABASE_URL=postgresql://username:password@localhost:5432/rentahub

# =============================================================================
# 🔐 SUPABASE CONFIGURATION
# =============================================================================
# Your actual Supabase credentials
SUPABASE_URL=https://rocxjzukyqelvuyltrfq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU

# Frontend Environment Variables (Vite requires VITE_ prefix)
VITE_SUPABASE_URL=https://rocxjzukyqelvuyltrfq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8

# =============================================================================
# 🔒 AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=your_super_secure_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRATION=24h

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback

# =============================================================================
# 💳 STRIPE PAYMENT CONFIGURATION
# =============================================================================
# Get these from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_signing_secret

# Frontend Stripe (Vite requires VITE_ prefix)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# =============================================================================
# 📧 EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# =============================================================================
# 📁 FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# =============================================================================
# 🌐 CORS & FRONTEND URLS
# =============================================================================
FRONTEND_URL=http://localhost:3000
ADMIN_URL=http://localhost:3002
BACKEND_URL=http://localhost:3001

# =============================================================================
# 🔧 DEVELOPMENT & DEBUGGING
# =============================================================================
LOG_LEVEL=info
DEBUG=false

# =============================================================================
# 🚀 RAILWAY DEPLOYMENT OVERRIDES
# =============================================================================
# Railway will automatically set these:
# - PORT (Railway assigns this)
# - DATABASE_URL (if using Railway PostgreSQL)
# - NODE_ENV=production

# =============================================================================
# 📊 MONITORING & ANALYTICS (Optional)
# =============================================================================
SENTRY_DSN=your_sentry_dsn_if_using_error_tracking
ANALYTICS_API_KEY=your_analytics_key_if_using

# =============================================================================
# 🔑 API KEYS (Optional)
# =============================================================================
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
TWILIO_ACCOUNT_SID=your_twilio_sid_for_sms
TWILIO_AUTH_TOKEN=your_twilio_token

# =============================================================================
# ⚡ PERFORMANCE & CACHING
# =============================================================================
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# =============================================================================
# 🛡️ SECURITY HEADERS
# =============================================================================
CORS_ORIGIN=http://localhost:3000,http://localhost:3002
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
