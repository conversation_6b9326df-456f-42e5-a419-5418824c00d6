version: '3.8'

services:
  # =============================================================================
  # BACKEND SERVICE
  # =============================================================================
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        VITE_API_URL: http://localhost:3001
    container_name: rentahub-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    volumes:
      - ./logs:/app/logs
    networks:
      - rentahub-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres

  # =============================================================================
  # FRONTEND SERVICE (NGINX)
  # =============================================================================
  frontend:
    image: nginx:alpine
    container_name: rentahub-frontend
    ports:
      - "3000:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - rentahub-network
    restart: unless-stopped
    depends_on:
      - backend

  # =============================================================================
  # REDIS CACHE
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: rentahub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - rentahub-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}

  # =============================================================================
  # POSTGRESQL DATABASE (for local development)
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: rentahub-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=rentahub
      - POSTGRES_USER=rentahub_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-defaultpassword}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - rentahub-network
    restart: unless-stopped

  # =============================================================================
  # MONITORING - PROMETHEUS
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: rentahub-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - rentahub-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # =============================================================================
  # MONITORING - GRAFANA
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: rentahub-grafana
    ports:
      - "3003:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - rentahub-network
    restart: unless-stopped
    depends_on:
      - prometheus

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  rentahub-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
