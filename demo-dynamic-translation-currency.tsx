/**
 * DEMO: Dynamic Translation & Currency System
 * 
 * This demonstrates how to replace hardcoded text and currency values
 * with dynamic, Google Translate-like functionality
 */

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  MenuItem,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  LocationOn as LocationIcon,
  TwoWheeler as BikeIcon,
  Star as StarIcon
} from '@mui/icons-material';

// Import our new dynamic services
import AutoTranslate, { useAutoTranslate, AutoTranslateBatch } from './src/components/AutoTranslate';
import PriceDisplay from './src/components/PriceDisplay';
import DynamicCurrencyService from './src/services/DynamicCurrencyService';

/**
 * BEFORE vs AFTER Comparison Demo
 */
const DynamicTranslationCurrencyDemo: React.FC = () => {
  const { translate } = useAutoTranslate();
  const [selectedExample, setSelectedExample] = useState<'before' | 'after'>('before');

  // Sample vehicle data
  const sampleVehicles = [
    {
      id: 1,
      name: 'Honda PCX 150',
      type: 'Scooter',
      price: 25,
      currency: 'USD',
      location: 'Jakarta',
      rating: 4.5,
      description: 'Comfortable and fuel-efficient scooter perfect for city rides'
    },
    {
      id: 2,
      name: 'Yamaha NMAX 155',
      type: 'Scooter', 
      price: 350000,
      currency: 'IDR',
      location: 'Bali',
      rating: 4.7,
      description: 'Sporty and reliable scooter with great performance'
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Demo Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" gutterBottom>
          <AutoTranslate>Dynamic Translation & Currency Demo</AutoTranslate>
        </Typography>
        <Typography variant="h6" color="text.secondary" mb={3}>
          <AutoTranslate>See how hardcoded text and prices become dynamic</AutoTranslate>
        </Typography>
        
        <Box display="flex" gap={2} justifyContent="center">
          <Button
            variant={selectedExample === 'before' ? 'contained' : 'outlined'}
            onClick={() => setSelectedExample('before')}
            color="error"
          >
            <AutoTranslate>❌ Before (Hardcoded)</AutoTranslate>
          </Button>
          <Button
            variant={selectedExample === 'after' ? 'contained' : 'outlined'}
            onClick={() => setSelectedExample('after')}
            color="success"
          >
            <AutoTranslate>✅ After (Dynamic)</AutoTranslate>
          </Button>
        </Box>
      </Box>

      <Divider sx={{ mb: 4 }} />

      {/* BEFORE Example - Hardcoded */}
      {selectedExample === 'before' && (
        <Paper elevation={3} sx={{ p: 3, mb: 4, border: '2px solid #f44336' }}>
          <Typography variant="h5" color="error" gutterBottom>
            ❌ BEFORE: Hardcoded Text & Currency
          </Typography>
          
          {/* Hardcoded Search Form */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>Search Vehicles</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField fullWidth label="Location" placeholder="Enter city or area" />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField fullWidth label="Start Date" type="date" InputLabelProps={{ shrink: true }} />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField fullWidth label="End Date" type="date" InputLabelProps={{ shrink: true }} />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField fullWidth select label="Vehicle Type" defaultValue="">
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="scooter">Scooter</MenuItem>
                  <MenuItem value="motorcycle">Motorcycle</MenuItem>
                </TextField>
              </Grid>
            </Grid>
            <Button variant="contained" startIcon={<SearchIcon />} sx={{ mt: 2 }}>
              Search Vehicles
            </Button>
          </Box>

          {/* Hardcoded Vehicle Cards */}
          <Grid container spacing={3}>
            {sampleVehicles.map((vehicle) => (
              <Grid item xs={12} md={6} key={vehicle.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>{vehicle.name}</Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {vehicle.description}
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <LocationIcon fontSize="small" color="primary" />
                      <Typography variant="body2">{vehicle.location}</Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <StarIcon fontSize="small" color="warning" />
                      <Typography variant="body2">{vehicle.rating} User Rating</Typography>
                    </Box>
                    {/* Hardcoded price formatting */}
                    <Typography variant="h6" color="primary" gutterBottom>
                      {vehicle.currency === 'USD' 
                        ? `$${vehicle.price}/day`
                        : `Rp ${vehicle.price.toLocaleString()}/day`
                      }
                    </Typography>
                    <Button variant="contained" fullWidth>
                      Book Now
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Problems with hardcoded approach */}
          <Box sx={{ mt: 4, p: 2, bgcolor: '#ffebee', borderRadius: 2 }}>
            <Typography variant="h6" color="error" gutterBottom>
              Problems with Hardcoded Approach:
            </Typography>
            <ul>
              <li>All text is in English only</li>
              <li>Prices show in original currency (USD/IDR)</li>
              <li>No automatic conversion based on user location</li>
              <li>Adding new languages requires manual translation of every string</li>
              <li>Currency formatting is inconsistent</li>
              <li>Poor user experience for non-English speakers</li>
            </ul>
          </Box>
        </Paper>
      )}

      {/* AFTER Example - Dynamic */}
      {selectedExample === 'after' && (
        <Paper elevation={3} sx={{ p: 3, mb: 4, border: '2px solid #4caf50' }}>
          <Typography variant="h5" color="success.main" gutterBottom>
            ✅ AFTER: Dynamic Translation & Currency
          </Typography>
          
          {/* Dynamic Search Form */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              <AutoTranslate>Search Vehicles</AutoTranslate>
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField 
                  fullWidth 
                  label={<AutoTranslate>Location</AutoTranslate>} 
                  placeholder={translate("Enter city or area")}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField 
                  fullWidth 
                  label={<AutoTranslate>Start Date</AutoTranslate>} 
                  type="date" 
                  InputLabelProps={{ shrink: true }} 
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField 
                  fullWidth 
                  label={<AutoTranslate>End Date</AutoTranslate>} 
                  type="date" 
                  InputLabelProps={{ shrink: true }} 
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField fullWidth select label={<AutoTranslate>Vehicle Type</AutoTranslate>} defaultValue="">
                  <MenuItem value=""><AutoTranslate>All Types</AutoTranslate></MenuItem>
                  <MenuItem value="scooter"><AutoTranslate>Scooter</AutoTranslate></MenuItem>
                  <MenuItem value="motorcycle"><AutoTranslate>Motorcycle</AutoTranslate></MenuItem>
                </TextField>
              </Grid>
            </Grid>
            <Button variant="contained" startIcon={<SearchIcon />} sx={{ mt: 2 }}>
              <AutoTranslate>Search Vehicles</AutoTranslate>
            </Button>
          </Box>

          {/* Dynamic Vehicle Cards */}
          <Grid container spacing={3}>
            {sampleVehicles.map((vehicle) => (
              <Grid item xs={12} md={6} key={vehicle.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <AutoTranslate>{vehicle.name}</AutoTranslate>
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      <AutoTranslate>{vehicle.description}</AutoTranslate>
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <LocationIcon fontSize="small" color="primary" />
                      <Typography variant="body2">
                        <AutoTranslate>{vehicle.location}</AutoTranslate>
                      </Typography>
                    </Box>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <StarIcon fontSize="small" color="warning" />
                      <Typography variant="body2">
                        {vehicle.rating} <AutoTranslate>User Rating</AutoTranslate>
                      </Typography>
                    </Box>
                    {/* Dynamic price formatting */}
                    <PriceDisplay
                      amount={vehicle.price}
                      currency={vehicle.currency}
                      period="day"
                      variant="h6"
                      color="primary"
                      showComparison
                    />
                    <Button variant="contained" fullWidth sx={{ mt: 1 }}>
                      <AutoTranslate>Book Now</AutoTranslate>
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Benefits of dynamic approach */}
          <Box sx={{ mt: 4, p: 2, bgcolor: '#e8f5e8', borderRadius: 2 }}>
            <Typography variant="h6" color="success.main" gutterBottom>
              Benefits of Dynamic Approach:
            </Typography>
            <ul>
              <li>✅ Automatic translation to user's language</li>
              <li>✅ Real-time currency conversion based on user location</li>
              <li>✅ Consistent price formatting with proper localization</li>
              <li>✅ No need to manually translate every string</li>
              <li>✅ Works like Google Translate - just wrap any text</li>
              <li>✅ Better user experience for international users</li>
              <li>✅ Easy to maintain and scale</li>
            </ul>
          </Box>
        </Paper>
      )}

      {/* Implementation Guide */}
      <Paper elevation={2} sx={{ p: 3, bgcolor: '#f5f5f5' }}>
        <Typography variant="h5" gutterBottom>
          🚀 How to Implement
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom color="primary">
              <AutoTranslate>For Translation:</AutoTranslate>
            </Typography>
            <Box component="pre" sx={{ bgcolor: 'white', p: 2, borderRadius: 1, fontSize: '0.8rem', overflow: 'auto' }}>
{`// Instead of:
<Typography>Search Vehicles</Typography>

// Use:
<AutoTranslate>Search Vehicles</AutoTranslate>

// Or for dynamic content:
const { translate } = useAutoTranslate();
const text = await translate("Any text here");`}
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom color="primary">
              <AutoTranslate>For Currency:</AutoTranslate>
            </Typography>
            <Box component="pre" sx={{ bgcolor: 'white', p: 2, borderRadius: 1, fontSize: '0.8rem', overflow: 'auto' }}>
{`// Instead of:
<Typography>$25/day</Typography>

// Use:
<PriceDisplay 
  amount={25} 
  currency="USD" 
  period="day" 
  showComparison 
/>`}
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3 }}>
          <Typography variant="body1" gutterBottom>
            <AutoTranslate>
              The system automatically detects user language and location, 
              then translates text and converts currencies in real-time.
            </AutoTranslate>
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default DynamicTranslationCurrencyDemo;
