# RentaHub Deployment Guide

## Overview

This guide covers the complete deployment process for RentaHub, including CI/CD pipelines, environment management, and deployment strategies.

## Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ Local Testing   │───▶│ Pre-prod Tests  │───▶│  Live Service   │
│ Feature Branches│    │ Integration     │    │ Real Users      │
│ Pull Requests   │    │ User Acceptance │    │ Monitoring      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Preview Deploys │    │ Staging Railway │    │ Prod Railway    │
│ PR Environments │    │ Test Database   │    │ Prod Database   │
│ Feature Testing │    │ Stripe Test     │    │ Stripe Live     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Deployment Environments

### 1. Development Environment
- **Purpose**: Local development and testing
- **Location**: Developer machines
- **Database**: Local Supabase or development instance
- **Payment**: Stripe test mode
- **Access**: Developers only

### 2. Preview Environments
- **Purpose**: Feature testing and PR reviews
- **Location**: Railway preview deployments
- **Database**: Shared staging database
- **Payment**: Stripe test mode
- **Access**: Development team and stakeholders
- **Lifecycle**: Created for PRs, cleaned up when closed

### 3. Staging Environment
- **Purpose**: Pre-production testing and validation
- **Location**: Railway staging environment
- **Database**: Staging Supabase instance
- **Payment**: Stripe test mode
- **Access**: Development team, QA, and stakeholders
- **Deployment**: Automatic from `develop` branch

### 4. Production Environment
- **Purpose**: Live application serving real users
- **Location**: Railway production environment
- **Database**: Production Supabase instance
- **Payment**: Stripe live mode
- **Access**: End users and administrators
- **Deployment**: Manual approval from `main` branch

## CI/CD Pipeline

### GitHub Actions Workflows

#### 1. Main CI/CD Pipeline (`.github/workflows/ci-cd.yml`)
Triggered on push to `main` and `develop` branches, and on pull requests.

**Stages:**
1. **Linting & Code Quality**
   - ESLint for TypeScript
   - Prettier formatting check
   - Type checking

2. **Build Validation**
   - Backend and frontend builds
   - Artifact generation
   - Build size analysis

3. **Testing**
   - Unit tests
   - Integration tests
   - Coverage reporting

4. **Security Scanning**
   - npm audit
   - CodeQL analysis
   - Dependency vulnerability check

5. **Deployment**
   - Staging: Auto-deploy from `develop`
   - Production: Auto-deploy from `main` (with approval)

#### 2. PR Preview Pipeline (`.github/workflows/pr-preview.yml`)
Triggered on pull request events.

**Features:**
- Creates preview environments for each PR
- Runs smoke tests against preview
- Comments deployment links on PR
- Performs Lighthouse audits
- Cleans up when PR is closed

### Deployment Scripts

#### 1. Build Script (`scripts/build-all.sh`)
```bash
# Development build
./scripts/build-all.sh

# Production build
./scripts/build-all.sh --environment production

# Clean parallel build
./scripts/build-all.sh --clean --parallel
```

#### 2. Deployment Script (`scripts/deploy.sh`)
```bash
# Deploy to staging
./scripts/deploy.sh --environment staging

# Deploy to production (with confirmation)
./scripts/deploy.sh --environment production

# Rollback production
./scripts/deploy.sh --rollback --environment production
```

#### 3. Preview Deployment (`scripts/preview-deploy.sh`)
```bash
# Deploy PR preview
./scripts/preview-deploy.sh --pr 123

# Deploy branch preview
./scripts/preview-deploy.sh --branch feature/new-feature

# Cleanup preview
./scripts/preview-deploy.sh --cleanup --pr 123
```

#### 4. Build Validation (`scripts/validate-build.sh`)
```bash
# Full validation
./scripts/validate-build.sh

# Skip tests
./scripts/validate-build.sh --skip-tests

# Backend only
./scripts/validate-build.sh --skip-frontend
```

## Deployment Platforms

### Railway Configuration

#### Project Structure
```
rentahub-project/
├── backend-service/
│   ├── production environment
│   ├── staging environment
│   └── preview environments
└── frontend-service/
    ├── production environment
    ├── staging environment
    └── preview environments
```

#### Environment Variables
Each environment requires specific variables:

**Production:**
- `NODE_ENV=production`
- `SUPABASE_URL` (production instance)
- `STRIPE_SECRET_KEY` (live key)
- All production secrets

**Staging:**
- `NODE_ENV=staging`
- `SUPABASE_URL` (staging instance)
- `STRIPE_SECRET_KEY` (test key)
- Staging-specific secrets

**Preview:**
- `NODE_ENV=staging`
- `RAILWAY_ENVIRONMENT=preview`
- `PREVIEW_BRANCH` (source branch)
- `PR_NUMBER` (if applicable)

### Alternative Platforms

#### Vercel (Frontend)
- Automatic deployments from Git
- Preview deployments for PRs
- Edge functions for API routes
- Global CDN distribution

#### Docker Deployment
- Containerized application
- Kubernetes orchestration
- Docker Compose for local development
- Multi-stage builds for optimization

## Deployment Process

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-booking-flow

# Develop and test locally
npm run dev

# Create pull request
# → Triggers preview deployment
# → Runs CI/CD pipeline
# → Creates preview environment
```

### 2. Staging Deployment
```bash
# Merge to develop branch
git checkout develop
git merge feature/new-booking-flow

# Push to trigger staging deployment
git push origin develop
# → Automatic deployment to staging
# → Integration tests run
# → Stakeholder review
```

### 3. Production Deployment
```bash
# Merge to main branch
git checkout main
git merge develop

# Push to trigger production deployment
git push origin main
# → Manual approval required
# → Production deployment
# → Health checks
# → Monitoring alerts
```

## Rollback Procedures

### Automatic Rollback
- Health check failures trigger automatic rollback
- Database migration failures prevent deployment
- Critical error thresholds trigger rollback

### Manual Rollback
```bash
# Rollback production deployment
./scripts/deploy.sh --rollback --environment production

# Rollback to specific version
railway rollback --service backend --environment production

# Database rollback (if needed)
# Follow database migration rollback procedures
```

## Monitoring and Health Checks

### Health Check Endpoints
- **Backend**: `/health`
- **Database**: `/health/db`
- **External Services**: `/health/external`

### Monitoring Stack
- **Application Metrics**: Railway built-in monitoring
- **Error Tracking**: Sentry integration
- **Uptime Monitoring**: External service monitoring
- **Performance**: Lighthouse CI for frontend

### Alerts
- **Critical**: Production errors, downtime
- **Warning**: Performance degradation, high error rates
- **Info**: Deployment notifications, health check status

## Security Considerations

### Secrets Management
- Environment variables stored in Railway
- No secrets in code repository
- Separate secrets for each environment
- Regular secret rotation

### Access Control
- GitHub branch protection rules
- Required PR reviews for main branch
- Deployment approval workflows
- Limited production access

### Security Scanning
- Dependency vulnerability scanning
- Code security analysis (CodeQL)
- Container security scanning
- Regular security audits

## Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Check build logs
railway logs --service backend --environment production

# Validate build locally
./scripts/validate-build.sh

# Check dependencies
npm audit
```

#### 2. Deployment Failures
```bash
# Check deployment status
railway status --service backend --environment production

# View deployment logs
railway logs --service backend --environment production --tail

# Rollback if needed
./scripts/deploy.sh --rollback --environment production
```

#### 3. Health Check Failures
```bash
# Check application health
curl https://your-backend-url.railway.app/health

# Check database connectivity
curl https://your-backend-url.railway.app/health/db

# Review application logs
railway logs --service backend --environment production
```

### Debug Commands
```bash
# Check environment variables
railway variables --service backend --environment production

# Connect to service shell
railway shell --service backend --environment production

# View service metrics
railway metrics --service backend --environment production
```

## Best Practices

### 1. Deployment Strategy
- Use feature flags for gradual rollouts
- Deploy during low-traffic periods
- Monitor metrics after deployment
- Have rollback plan ready

### 2. Testing Strategy
- Comprehensive test coverage
- Integration tests in staging
- Load testing before production
- User acceptance testing

### 3. Database Migrations
- Test migrations in staging first
- Use reversible migrations
- Backup before production migrations
- Monitor performance impact

### 4. Monitoring
- Set up alerts before deployment
- Monitor key metrics continuously
- Use structured logging
- Implement distributed tracing

## Emergency Procedures

### Production Incident Response
1. **Immediate Response**
   - Assess impact and severity
   - Notify stakeholders
   - Begin incident documentation

2. **Mitigation**
   - Rollback if deployment-related
   - Apply hotfix if critical bug
   - Scale resources if capacity issue

3. **Resolution**
   - Identify root cause
   - Implement permanent fix
   - Update monitoring/alerts

4. **Post-Incident**
   - Conduct post-mortem
   - Update procedures
   - Implement preventive measures

### Contact Information
- **On-Call Engineer**: [Contact details]
- **DevOps Team**: [Contact details]
- **Platform Support**: Railway support, Supabase support

## Maintenance

### Regular Tasks
- **Weekly**: Review deployment metrics
- **Monthly**: Update dependencies
- **Quarterly**: Security audit
- **Annually**: Disaster recovery test

### Scheduled Maintenance
- Plan maintenance windows
- Notify users in advance
- Use maintenance mode
- Monitor during maintenance

This deployment guide ensures reliable, secure, and efficient deployment of RentaHub across all environments.
