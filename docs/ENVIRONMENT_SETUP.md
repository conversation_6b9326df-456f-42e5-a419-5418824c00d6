# RentaHub Environment Configuration Guide

## Overview

This guide explains how to set up and manage environment configurations for RentaHub across different deployment stages (development, staging, production).

## Environment Structure

```
backend/
├── .env                    # Local development (not in git)
├── .env.example           # Template with all variables
├── .env.staging          # Staging-specific config
├── .env.production       # Production-specific config
└── .env.test             # Test environment config
```

## Environment Types

### 1. Development Environment
- **Purpose**: Local development and testing
- **File**: `.env` (created from `.env.example`)
- **Database**: Local or development Supabase instance
- **Payment**: Stripe test mode
- **Email**: Local SMTP or test service
- **Logging**: Debug level with verbose output

### 2. Staging Environment
- **Purpose**: Pre-production testing and validation
- **File**: `.env.staging`
- **Database**: Staging Supabase instance
- **Payment**: Stripe test mode
- **Email**: Test email service (Mailtrap)
- **Logging**: Debug level for troubleshooting

### 3. Production Environment
- **Purpose**: Live application serving real users
- **File**: `.env.production`
- **Database**: Production Supabase instance
- **Payment**: Stripe live mode
- **Email**: Production SMTP service
- **Logging**: Info level with error tracking

## Required Environment Variables

### Core Application Settings
```bash
NODE_ENV=development|staging|production
PORT=3001
APP_NAME=RentaHub
APP_VERSION=1.0.0
```

### Database Configuration
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_ANON_KEY=your_anon_key
```

### Authentication & Security
```bash
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters
JWT_EXPIRATION=24h
JWT_REFRESH_EXPIRATION=7d
BCRYPT_ROUNDS=12
```

### Payment Processing
```bash
STRIPE_SECRET_KEY=sk_test_or_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_or_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### Email Configuration
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=RentaHub
```

## Setting Up Environments

### 1. Local Development Setup

1. **Copy the example file:**
   ```bash
   cd backend
   cp .env.example .env
   ```

2. **Fill in your values:**
   ```bash
   # Edit .env with your actual values
   nano .env
   ```

3. **Required for local development:**
   - Supabase project URL and keys
   - JWT secret (generate a secure random string)
   - Stripe test keys (from Stripe Dashboard)
   - SMTP credentials (Gmail App Password recommended)

### 2. Staging Environment Setup

1. **Configure staging secrets in your deployment platform:**
   ```bash
   # Railway CLI example
   railway variables set SUPABASE_URL=https://staging-project.supabase.co
   railway variables set JWT_SECRET=staging_jwt_secret_32_chars_min
   # ... other variables
   ```

2. **Use staging-specific values:**
   - Separate Supabase project for staging
   - Stripe test mode keys
   - Test email service (Mailtrap)
   - Debug logging enabled

### 3. Production Environment Setup

1. **Configure production secrets securely:**
   ```bash
   # Railway CLI example
   railway variables set SUPABASE_URL=https://production-project.supabase.co
   railway variables set JWT_SECRET=production_jwt_secret_32_chars_min
   railway variables set STRIPE_SECRET_KEY=sk_live_your_live_key
   # ... other variables
   ```

2. **Production-specific considerations:**
   - Use live Stripe keys
   - Production email service
   - Error monitoring (Sentry)
   - Minimal logging
   - SSL certificates

## GitHub Actions Secrets

Configure these secrets in your GitHub repository settings:

### Required Secrets
```bash
# Database
SUPABASE_URL
SUPABASE_SERVICE_ROLE_KEY

# Deployment
RAILWAY_STAGING_TOKEN
RAILWAY_PRODUCTION_TOKEN
RAILWAY_PREVIEW_TOKEN

# URLs for health checks
STAGING_BACKEND_URL
STAGING_FRONTEND_URL
PRODUCTION_BACKEND_URL
PRODUCTION_FRONTEND_URL

# Notifications (optional)
SLACK_WEBHOOK
```

### Setting GitHub Secrets
1. Go to your repository on GitHub
2. Navigate to Settings → Secrets and variables → Actions
3. Click "New repository secret"
4. Add each required secret

## Environment Variable Validation

The application validates required environment variables on startup:

```typescript
// Example validation in src/config/env.ts
const requiredVars = [
  'SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET'
];

requiredVars.forEach(varName => {
  if (!process.env[varName]) {
    throw new Error(`Required environment variable ${varName} is not set`);
  }
});
```

## Security Best Practices

### 1. Secret Management
- **Never commit** `.env` files to version control
- Use **strong, unique secrets** for each environment
- **Rotate secrets** regularly
- Use **deployment platform secret management**

### 2. Environment Separation
- **Separate databases** for each environment
- **Different API keys** for external services
- **Isolated storage buckets**
- **Environment-specific domains**

### 3. Access Control
- **Limit access** to production secrets
- **Use service accounts** for CI/CD
- **Audit secret access** regularly
- **Implement least privilege** principle

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```bash
   Error: Required environment variable JWT_SECRET is not set
   ```
   **Solution**: Check that all required variables are set in your environment

2. **Database Connection Failed**
   ```bash
   Error: Database connection failed
   ```
   **Solution**: Verify Supabase URL and service role key are correct

3. **Stripe Webhook Verification Failed**
   ```bash
   Error: Stripe webhook signature verification failed
   ```
   **Solution**: Ensure webhook secret matches your Stripe dashboard

### Debugging Environment Issues

1. **Check environment loading:**
   ```bash
   # Add to your startup script
   console.log('Environment:', process.env.NODE_ENV);
   console.log('Database URL:', process.env.SUPABASE_URL ? 'Set' : 'Not set');
   ```

2. **Validate configuration:**
   ```bash
   npm run validate-env
   ```

3. **Test database connection:**
   ```bash
   npm run test:db-connection
   ```

## Environment-Specific Features

### Development
- Hot reloading enabled
- Detailed error messages
- Debug endpoints available
- Test data seeding
- Relaxed rate limiting

### Staging
- Production-like configuration
- Test payment processing
- Email testing
- Performance monitoring
- User acceptance testing

### Production
- Optimized performance
- Error tracking
- Security hardening
- Monitoring and alerts
- Backup and recovery

## Deployment Platform Configuration

### Railway
```bash
# Set environment variables
railway variables set NODE_ENV=production
railway variables set PORT=3001

# Deploy with specific environment
railway up --environment production
```

### Vercel
```bash
# Set environment variables
vercel env add NODE_ENV production
vercel env add PORT 3001

# Deploy with environment
vercel --prod
```

### Docker
```dockerfile
# Use environment-specific env file
COPY .env.production .env
```

## Monitoring and Maintenance

### Health Checks
Each environment should have health check endpoints:
- `/health` - Basic application health
- `/health/db` - Database connectivity
- `/health/external` - External service connectivity

### Environment Monitoring
- **Application metrics** (response times, error rates)
- **Database performance** (query times, connection pool)
- **External service status** (Stripe, email service)
- **Resource usage** (CPU, memory, disk)

### Regular Maintenance
- **Update dependencies** regularly
- **Rotate secrets** quarterly
- **Review and update** environment configurations
- **Test disaster recovery** procedures

## Support and Documentation

- **Environment Issues**: Check this guide first
- **Database Problems**: See `docs/DATABASE.md`
- **Deployment Issues**: See `docs/DEPLOYMENT.md`
- **Security Concerns**: See `docs/SECURITY.md`

For additional help, create an issue in the repository with:
- Environment type (development/staging/production)
- Error messages
- Steps to reproduce
- Configuration (without sensitive values)
