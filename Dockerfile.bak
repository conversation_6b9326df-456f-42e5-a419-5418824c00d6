# =============================================================================
# RENTAHUB PRODUCTION DOCKERFILE
# =============================================================================
# Multi-stage build for optimized production deployment

# =============================================================================
# STAGE 1: Build Backend
# =============================================================================
FROM node:20-alpine AS backend-builder

WORKDIR /app/backend

# Copy backend package files
COPY backend/package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy backend source
COPY backend/ ./

# Build backend
RUN npm run build

# =============================================================================
# STAGE 2: Build Frontend
# =============================================================================
FROM node:20-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci && npm cache clean --force

# Copy frontend source
COPY frontend/ ./

# Build frontend
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL
RUN npm run build

# =============================================================================
# STAGE 3: Production Runtime
# =============================================================================
FROM node:20-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S rentahub -u 1001

# Set working directory
WORKDIR /app

# Copy backend build and dependencies
COPY --from=backend-builder --chown=rentahub:nodejs /app/backend/dist ./backend/dist
COPY --from=backend-builder --chown=rentahub:nodejs /app/backend/node_modules ./backend/node_modules
COPY --from=backend-builder --chown=rentahub:nodejs /app/backend/package.json ./backend/package.json

# Copy frontend build
COPY --from=frontend-builder --chown=rentahub:nodejs /app/frontend/dist ./frontend/dist

# Create logs directory
RUN mkdir -p /app/logs && chown rentahub:nodejs /app/logs

# Switch to non-root user
USER rentahub

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Set environment
ENV NODE_ENV=production
ENV PORT=3001

# Start application with dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "backend/dist/index.js"]

# =============================================================================
# LABELS
# =============================================================================
LABEL maintainer="RentaHub Team"
LABEL version="1.0.0"
LABEL description="RentaHub Production Container"
LABEL org.opencontainers.image.source="https://github.com/your-org/rentahub"
