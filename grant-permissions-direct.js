const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function grantPermissions() {
  console.log('🔐 Granting SELECT permissions to anon role...');
  
  try {
    // Try to execute the GRANT command using RPC
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: 'grant select on table public.vehicles to anon;'
    });

    if (error) {
      console.log('❌ RPC method failed, trying alternative...');
      
      // Try to create a function to execute SQL
      const { error: createFuncError } = await supabase.rpc('create_exec_sql_function', {
        sql: `
          CREATE OR REPLACE FUNCTION exec_sql(sql text)
          RETURNS void
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          BEGIN
            EXECUTE sql;
          END;
          $$;
        `
      });

      if (createFuncError) {
        console.log('❌ Cannot create function. You need to run this SQL in Supabase dashboard:');
        console.log('grant select on table public.vehicles to anon;');
        return;
      }

      // Now try to execute the grant
      const { error: grantError } = await supabase.rpc('exec_sql', {
        sql: 'grant select on table public.vehicles to anon;'
      });

      if (grantError) {
        console.log('❌ Still cannot execute. Please run this SQL in Supabase dashboard:');
        console.log('grant select on table public.vehicles to anon;');
      } else {
        console.log('✅ Permissions granted successfully!');
      }
    } else {
      console.log('✅ Permissions granted successfully!');
    }
    
  } catch (error) {
    console.error('❌ Failed to execute SQL:', error);
    console.log('\n📋 Please run this SQL in your Supabase dashboard:');
    console.log('grant select on table public.vehicles to anon;');
  }
}

// Run the script
grantPermissions(); 