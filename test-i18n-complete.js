#!/usr/bin/env node

/**
 * Comprehensive i18n test to validate all language translations
 */

const fs = require('fs');
const path = require('path');

console.log('🌐 Comprehensive i18n Translation Test\n');

// All supported languages
const LANGUAGES = ['en', 'id', 'zh', 'ar', 'es', 'fr', 'de', 'ja', 'ko', 'pt', 'ru'];
const NAMESPACES = ['common', 'assistance'];

// Required translation keys for homepage functionality
const REQUIRED_KEYS = [
  'homepage.hero.title',
  'homepage.hero.subtitle', 
  'homepage.hero.description',
  'homepage.search.location',
  'homepage.search.startDate',
  'homepage.search.endDate',
  'homepage.search.vehicleType',
  'homepage.search.searchButton',
  'homepage.search.mapView',
  'homepage.features.title',
  'homepage.features.comingSoon',
  'homepage.features.description',
  'search.nearby',
  'search.latitude',
  'search.longitude',
  'search.radius',
  'search.searchButton',
  'search.errors.location',
  'search.errors.invalidCoords',
  'search.errors.searchFailed',
  'language.select',
  'language.help',
  'nav.home',
  'nav.vehicles',
  'nav.bookings',
  'nav.profile',
  'buttons.submit',
  'buttons.cancel',
  'buttons.search',
  'buttons.filter'
];

function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current && current[key], obj);
}

function testLanguageFile(lang, namespace) {
  const filePath = `frontend/public/locales/${lang}/${namespace}.json`;
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File missing: ${filePath}`);
      return { exists: false, keys: 0, missing: REQUIRED_KEYS.length };
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(content);
    
    let foundKeys = 0;
    let missingKeys = [];
    
    // Only test required keys for common namespace
    if (namespace === 'common') {
      for (const key of REQUIRED_KEYS) {
        const value = getNestedValue(translations, key);
        if (value && typeof value === 'string' && value.trim() !== '') {
          foundKeys++;
        } else {
          missingKeys.push(key);
        }
      }
    } else {
      // For other namespaces, just check if file exists and has content
      foundKeys = Object.keys(translations).length;
    }
    
    const status = missingKeys.length === 0 ? '✅' : '⚠️';
    console.log(`${status} ${lang}/${namespace}.json - ${foundKeys} keys found${missingKeys.length > 0 ? `, ${missingKeys.length} missing` : ''}`);
    
    if (missingKeys.length > 0 && missingKeys.length <= 5) {
      console.log(`   Missing: ${missingKeys.join(', ')}`);
    } else if (missingKeys.length > 5) {
      console.log(`   Missing: ${missingKeys.slice(0, 5).join(', ')} and ${missingKeys.length - 5} more...`);
    }
    
    return { 
      exists: true, 
      keys: foundKeys, 
      missing: missingKeys.length,
      missingKeys: missingKeys
    };
    
  } catch (error) {
    console.log(`❌ Error reading ${filePath}: ${error.message}`);
    return { exists: false, keys: 0, missing: REQUIRED_KEYS.length, error: error.message };
  }
}

// Test all languages and namespaces
let totalFiles = 0;
let successfulFiles = 0;
let totalMissingKeys = 0;

console.log('📋 Testing Translation Files:');
console.log('================================\n');

for (const lang of LANGUAGES) {
  console.log(`🏳️ Language: ${lang.toUpperCase()}`);
  
  for (const namespace of NAMESPACES) {
    totalFiles++;
    const result = testLanguageFile(lang, namespace);
    
    if (result.exists) {
      successfulFiles++;
      totalMissingKeys += result.missing;
    }
  }
  console.log('');
}

// Test i18n configuration
console.log('⚙️ Testing i18n Configuration:');
console.log('===============================');

try {
  const i18nConfigPath = 'frontend/src/i18n/index.ts';
  if (fs.existsSync(i18nConfigPath)) {
    const configContent = fs.readFileSync(i18nConfigPath, 'utf8');
    
    // Check critical configuration
    const checks = [
      { key: 'fallbackLng', pattern: /fallbackLng:\s*['"](en|id)['"]/, name: 'Fallback language set' },
      { key: 'backend', pattern: /loadPath:\s*['"]\/locales\/\{\{lng\}\}\/\{\{ns\}\}\.json['"]/, name: 'Backend load path configured' },
      { key: 'supportedLngs', pattern: /supportedLngs:\s*\[.*\]/, name: 'Supported languages defined' },
      { key: 'saveMissing', pattern: /saveMissing:\s*false/, name: 'Missing key warnings disabled' },
      { key: 'useSuspense', pattern: /useSuspense:\s*false/, name: 'Suspense disabled for better UX' }
    ];
    
    for (const check of checks) {
      if (check.pattern.test(configContent)) {
        console.log(`✅ ${check.name}`);
      } else {
        console.log(`⚠️ ${check.name} - Not found or incorrect`);
      }
    }
  } else {
    console.log('❌ i18n configuration file not found');
  }
} catch (error) {
  console.log(`❌ Error reading i18n config: ${error.message}`);
}

// Summary
console.log('\n📊 Test Summary:');
console.log('================');
console.log(`📁 Files tested: ${totalFiles}`);
console.log(`✅ Files found: ${successfulFiles}`);
console.log(`❌ Files missing: ${totalFiles - successfulFiles}`);
console.log(`🔑 Total missing keys: ${totalMissingKeys}`);

if (successfulFiles === totalFiles && totalMissingKeys === 0) {
  console.log('\n🎉 All i18n tests passed!');
  console.log('✅ All languages have complete translations');
  console.log('✅ All required keys are present');
  console.log('✅ Configuration is properly set up');
  console.log('\n🚀 Language switching should work perfectly!');
} else {
  console.log('\n⚠️ Some issues found:');
  if (successfulFiles < totalFiles) {
    console.log(`   - ${totalFiles - successfulFiles} translation files are missing`);
  }
  if (totalMissingKeys > 0) {
    console.log(`   - ${totalMissingKeys} translation keys are missing`);
  }
  console.log('\n🔧 Please address these issues for full i18n functionality');
}
