#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixPermissions() {
  console.log('🔧 Fixing Supabase permissions...');
  
  try {
    // Test basic connection
    console.log('📡 Testing connection...');
    const { data: testData, error: testError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (testError) {
      console.log('❌ Connection test failed:', testError.message);
    } else {
      console.log('✅ Connection successful');
    }

    // Execute basic permissions
    console.log('🔐 Applying basic permissions...');
    
    const permissionQueries = [
      'GRANT USAGE ON SCHEMA public TO service_role',
      'GRANT CREATE ON SCHEMA public TO service_role',
      'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role',
      'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO service_role',
      'ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO service_role',
      'GRANT USAGE ON SCHEMA public TO anon',
      'GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon',
      'GRANT USAGE ON SCHEMA public TO authenticated',
      'GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated'
    ];

    for (const query of permissionQueries) {
      try {
        console.log(`Executing: ${query}`);
        const { error } = await supabase.rpc('exec_sql', { sql: query });
        if (error) {
          console.log(`⚠️  Query failed: ${error.message}`);
        } else {
          console.log('✅ Query executed successfully');
        }
      } catch (err) {
        console.log(`⚠️  Query error: ${err.message}`);
      }
    }

    // Test table access
    console.log('🔍 Testing table access...');
    const { data: tables, error: tablesError } = await supabase
      .rpc('exec_sql', { 
        sql: `SELECT tablename FROM pg_tables WHERE schemaname = 'public' LIMIT 5` 
      });
    
    if (tablesError) {
      console.log('❌ Table access test failed:', tablesError.message);
    } else {
      console.log('✅ Table access working');
    }

    console.log('🎉 Permission fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing permissions:', error.message);
  }
}

fixPermissions();
