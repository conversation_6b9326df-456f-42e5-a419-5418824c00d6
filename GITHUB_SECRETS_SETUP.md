# 🔐 GitHub Secrets Configuration Guide

## Required GitHub Repository Secrets

To enable the CI/CD pipeline, configure these secrets in your GitHub repository:

**Settings → Secrets and variables → Actions → New repository secret**

### 🗄️ **Database Secrets**
```
SUPABASE_URL=https://rocxjzukyqelvuyltfrq.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU
```

### 🚀 **Deployment Secrets**
```
RAILWAY_STAGING_TOKEN=your_railway_staging_token_here
RAILWAY_PRODUCTION_TOKEN=your_railway_production_token_here
```

### 🌐 **Environment URLs**
```
STAGING_BACKEND_URL=https://your-staging-backend.railway.app
STAGING_FRONTEND_URL=https://your-staging-frontend.railway.app
PRODUCTION_BACKEND_URL=https://your-production-backend.railway.app
PRODUCTION_FRONTEND_URL=https://your-production-frontend.railway.app
```

### 📢 **Notification Secrets**
```
SLACK_WEBHOOK=https://hooks.slack.com/services/your/slack/webhook
```

## 🔧 **How to Configure**

1. **Go to GitHub Repository**
   - Navigate to your RentaHub repository
   - Click **Settings** tab
   - Click **Secrets and variables** → **Actions**

2. **Add Each Secret**
   - Click **New repository secret**
   - Enter the **Name** (e.g., `SUPABASE_URL`)
   - Enter the **Value** (e.g., the actual URL)
   - Click **Add secret**

3. **Verify Configuration**
   - All secrets should appear in the list
   - Names must match exactly as shown above

## ✅ **Current Status**

### 🟢 **Already Configured**
- ✅ Supabase database credentials (provided above)
- ✅ CI/CD workflow file structure
- ✅ Environment variable validation

### 🟡 **Needs Configuration**
- ⚠️ Railway deployment tokens (get from Railway dashboard)
- ⚠️ Staging/Production URLs (will be available after first deployment)
- ⚠️ Slack webhook (optional for notifications)

## 🚀 **Railway Setup**

1. **Create Railway Account**
   - Go to https://railway.app
   - Sign up with GitHub

2. **Get API Token**
   - Go to Railway dashboard
   - Click **Account Settings**
   - Generate **API Token**
   - Copy token for `RAILWAY_STAGING_TOKEN` and `RAILWAY_PRODUCTION_TOKEN`

3. **Create Projects**
   - Create staging project
   - Create production project
   - Note the URLs for environment variables

## 📝 **Notes**

- **Supabase credentials** are already provided and working
- **Railway tokens** are required for deployment
- **Slack webhook** is optional for notifications
- **Environment URLs** will be generated after first deployment

## 🔍 **Troubleshooting**

### VS Code Warnings
The warnings you see in VS Code about "Context access might be invalid" are normal and don't affect functionality. They appear because VS Code can't validate GitHub Actions secrets locally.

### Missing Secrets
If CI/CD fails, check that all required secrets are configured with the exact names shown above.

### Railway Deployment
If Railway deployment fails, ensure:
- Railway CLI is properly authenticated
- Project exists in Railway dashboard
- API token has correct permissions

## ✅ **Verification**

Once configured, the CI/CD pipeline will:
1. ✅ Run tests on every push
2. ✅ Deploy to staging on main branch
3. ✅ Deploy to production on release tags
4. ✅ Send notifications to Slack (if configured)

**The system is ready for deployment once Railway tokens are configured! 🚀**
