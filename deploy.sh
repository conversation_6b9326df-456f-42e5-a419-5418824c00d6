#!/bin/bash

# =============================================================================
# RENTAHUB DEPLOYMENT SCRIPT
# =============================================================================
# Deploy RentaHub to Railway with rentahub.info domain
# Usage: ./deploy.sh [frontend|backend|admin|all]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="rentahub.info"
BACKEND_DOMAIN="api.${DOMAIN}"
ADMIN_DOMAIN="admin.${DOMAIN}"

echo -e "${BLUE}🚀 RentaHub Deployment Script${NC}"
echo -e "${BLUE}================================${NC}"

# Function to check if Railway CLI is installed
check_railway_cli() {
    if ! command -v railway &> /dev/null; then
        echo -e "${RED}❌ Railway CLI is not installed${NC}"
        echo -e "${YELLOW}Install it with: npm install -g @railway/cli${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Railway CLI found${NC}"
}

# Function to check if user is logged in to Railway
check_railway_auth() {
    if ! railway whoami &> /dev/null; then
        echo -e "${RED}❌ Not logged in to Railway${NC}"
        echo -e "${YELLOW}Login with: railway login${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Railway authentication verified${NC}"
}

# Function to deploy backend
deploy_backend() {
    echo -e "${BLUE}🔧 Deploying Backend API...${NC}"
    cd backend
    
    # Build the project
    echo -e "${YELLOW}📦 Building backend...${NC}"
    npm install
    npm run build
    
    # Deploy to Railway
    echo -e "${YELLOW}🚀 Deploying to Railway...${NC}"
    railway up --service rentahub-backend
    
    # Set custom domain
    echo -e "${YELLOW}🌐 Setting custom domain: ${BACKEND_DOMAIN}${NC}"
    railway domain --service rentahub-backend ${BACKEND_DOMAIN}
    
    cd ..
    echo -e "${GREEN}✅ Backend deployed successfully${NC}"
}

# Function to deploy frontend
deploy_frontend() {
    echo -e "${BLUE}🔧 Deploying Frontend...${NC}"
    cd frontend
    
    # Build the project
    echo -e "${YELLOW}📦 Building frontend...${NC}"
    npm install
    npm run build
    
    # Deploy to Railway
    echo -e "${YELLOW}🚀 Deploying to Railway...${NC}"
    railway up --service rentahub-frontend
    
    # Set custom domain
    echo -e "${YELLOW}🌐 Setting custom domain: ${DOMAIN}${NC}"
    railway domain --service rentahub-frontend ${DOMAIN}
    
    cd ..
    echo -e "${GREEN}✅ Frontend deployed successfully${NC}"
}

# Function to deploy admin
deploy_admin() {
    echo -e "${BLUE}🔧 Deploying Admin Panel...${NC}"
    cd admin
    
    # Build the project
    echo -e "${YELLOW}📦 Building admin panel...${NC}"
    npm install
    npm run build
    
    # Deploy to Railway
    echo -e "${YELLOW}🚀 Deploying to Railway...${NC}"
    railway up --service rentahub-admin
    
    # Set custom domain
    echo -e "${YELLOW}🌐 Setting custom domain: ${ADMIN_DOMAIN}${NC}"
    railway domain --service rentahub-admin ${ADMIN_DOMAIN}
    
    cd ..
    echo -e "${GREEN}✅ Admin panel deployed successfully${NC}"
}

# Function to deploy all services
deploy_all() {
    echo -e "${BLUE}🔧 Deploying All Services...${NC}"
    deploy_backend
    deploy_frontend
    deploy_admin
    echo -e "${GREEN}🎉 All services deployed successfully!${NC}"
}

# Function to show deployment status
show_status() {
    echo -e "${BLUE}📊 Deployment Status${NC}"
    echo -e "${BLUE}==================${NC}"
    echo -e "${GREEN}Frontend:${NC} https://${DOMAIN}"
    echo -e "${GREEN}Backend API:${NC} https://${BACKEND_DOMAIN}"
    echo -e "${GREEN}Admin Panel:${NC} https://${ADMIN_DOMAIN}"
    echo ""
    echo -e "${YELLOW}🔍 Check Railway dashboard for detailed status${NC}"
}

# Main script logic
main() {
    check_railway_cli
    check_railway_auth
    
    case "${1:-all}" in
        "backend")
            deploy_backend
            ;;
        "frontend")
            deploy_frontend
            ;;
        "admin")
            deploy_admin
            ;;
        "all")
            deploy_all
            ;;
        "status")
            show_status
            ;;
        *)
            echo -e "${RED}❌ Invalid option: $1${NC}"
            echo -e "${YELLOW}Usage: ./deploy.sh [frontend|backend|admin|all|status]${NC}"
            exit 1
            ;;
    esac
    
    show_status
}

# Run main function with all arguments
main "$@"
