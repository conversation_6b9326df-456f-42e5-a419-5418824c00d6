#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Checking deployment status...${NC}"

# Check if frontend is deployed
FRONTEND_URL="https://rentahub-frontend-production.up.railway.app"
echo -e "\n${YELLOW}Checking frontend at:${NC} $FRONTEND_URL"

FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $FRONTEND_URL || echo "Failed")
if [ "$FRONTEND_STATUS" == "200" ]; then
  echo -e "${GREEN}✅ Frontend is deployed and accessible (HTTP 200)${NC}"
elif [ "$FRONTEND_STATUS" == "Failed" ]; then
  echo -e "${RED}❌ Could not connect to frontend${NC}"
else
  echo -e "${YELLOW}⚠️ Frontend returned HTTP status:${NC} $FRONTEND_STATUS"
fi

# Check if backend is deployed
BACKEND_URL="https://rentahub-production.up.railway.app"
echo -e "\n${YELLOW}Checking backend at:${NC} $BACKEND_URL"

BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BACKEND_URL || echo "Failed")
if [ "$BACKEND_STATUS" == "200" ]; then
  echo -e "${GREEN}✅ Backend is deployed and accessible (HTTP 200)${NC}"
elif [ "$BACKEND_STATUS" == "Failed" ]; then
  echo -e "${RED}❌ Could not connect to backend${NC}"
else
  echo -e "${YELLOW}⚠️ Backend returned HTTP status:${NC} $BACKEND_STATUS"
fi

# Check backend health endpoint
HEALTH_URL="$BACKEND_URL/health"
echo -e "\n${YELLOW}Checking health endpoint at:${NC} $HEALTH_URL"

HEALTH_RESPONSE=$(curl -s $HEALTH_URL || echo "Failed")
if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
  echo -e "${GREEN}✅ Health check passed${NC}"
  echo -e "${YELLOW}Health response:${NC} $HEALTH_RESPONSE"
elif [ "$HEALTH_RESPONSE" == "Failed" ]; then
  echo -e "${RED}❌ Could not connect to health endpoint${NC}"
else
  echo -e "${YELLOW}⚠️ Health check response:${NC} $HEALTH_RESPONSE"
fi

echo -e "\n${YELLOW}Deployment check complete${NC}" 