#!/bin/bash

# =============================================================================
# RENTAHUB COMPREHENSIVE BUILD SCRIPT
# =============================================================================
# Builds both backend and frontend with full validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="development"
SKIP_VALIDATION=false
SKIP_TESTS=false
CLEAN=false
PARALLEL=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-validation)
            SKIP_VALIDATION=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV  Target environment (development|staging|production)"
            echo "  --skip-validation      Skip build validation"
            echo "  --skip-tests          Skip running tests"
            echo "  --clean               Clean build directories first"
            echo "  --parallel            Build backend and frontend in parallel"
            echo "  --help                Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Development build"
            echo "  $0 --environment production          # Production build"
            echo "  $0 --clean --parallel               # Clean parallel build"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_status "🏗️ RentaHub Comprehensive Build"
print_status "Environment: $ENVIRONMENT"
print_status "Skip Validation: $SKIP_VALIDATION"
print_status "Skip Tests: $SKIP_TESTS"
print_status "Clean: $CLEAN"
print_status "Parallel: $PARALLEL"
echo ""

# Start timing
START_TIME=$(date +%s)

# Clean build directories if requested
if [[ "$CLEAN" == "true" ]]; then
    print_status "🧹 Cleaning build directories..."
    rm -rf backend/dist
    rm -rf frontend/dist
    rm -rf backend/node_modules/.cache
    rm -rf frontend/node_modules/.cache
    print_success "Build directories cleaned"
fi

# Function to build backend
build_backend() {
    print_status "🔧 Building Backend..."
    
    cd backend
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    npm ci
    
    # Run linting
    print_status "Linting backend code..."
    npm run lint
    
    # Type checking
    print_status "Type checking backend..."
    npx tsc --noEmit
    
    # Run tests (if not skipped)
    if [[ "$SKIP_TESTS" != "true" ]]; then
        print_status "Running backend tests..."
        npm run test:unit
    fi
    
    # Build
    print_status "Compiling backend..."
    npm run build
    
    # Validate build output
    if [[ ! -f "dist/index.js" ]]; then
        print_error "Backend build failed - main entry point not found"
        exit 1
    fi
    
    print_success "Backend build completed"
    cd ..
}

# Function to build frontend
build_frontend() {
    print_status "🎨 Building Frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm ci
    
    # Run linting
    print_status "Linting frontend code..."
    npm run lint
    
    # Type checking
    print_status "Type checking frontend..."
    npx tsc --noEmit
    
    # Set environment variables for build
    case $ENVIRONMENT in
        production)
            export VITE_API_URL="https://api.rentahub.info/api"
            export VITE_APP_ENV="production"
            ;;
        staging)
            export VITE_API_URL="https://api.rentahub.info/api"
            export VITE_APP_ENV="staging"
            ;;
        *)
            export VITE_API_URL="http://localhost:3001"
            export VITE_APP_ENV="development"
            ;;
    esac
    
    # Build
    print_status "Building frontend for $ENVIRONMENT..."
    npm run build
    
    # Validate build output
    if [[ ! -f "dist/index.html" ]]; then
        print_error "Frontend build failed - index.html not found"
        exit 1
    fi
    
    print_success "Frontend build completed"
    cd ..
}

# Build based on parallel flag
if [[ "$PARALLEL" == "true" ]]; then
    print_status "🚀 Building in parallel..."
    
    # Run builds in background
    build_backend &
    BACKEND_PID=$!
    
    build_frontend &
    FRONTEND_PID=$!
    
    # Wait for both to complete
    wait $BACKEND_PID
    BACKEND_EXIT=$?
    
    wait $FRONTEND_PID
    FRONTEND_EXIT=$?
    
    # Check if both succeeded
    if [[ $BACKEND_EXIT -ne 0 ]]; then
        print_error "Backend build failed"
        exit 1
    fi
    
    if [[ $FRONTEND_EXIT -ne 0 ]]; then
        print_error "Frontend build failed"
        exit 1
    fi
    
    print_success "Parallel builds completed"
else
    # Sequential builds
    build_backend
    build_frontend
fi

# Run build validation (if not skipped)
if [[ "$SKIP_VALIDATION" != "true" ]]; then
    print_status "🔍 Running build validation..."
    ./scripts/validate-build.sh --skip-tests
fi

# Calculate build time
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))

# Build summary
print_status "📊 Build Summary"
echo "=" * 40

# Backend build info
if [[ -d "backend/dist" ]]; then
    BACKEND_SIZE=$(du -sh backend/dist | cut -f1)
    BACKEND_FILES=$(find backend/dist -type f | wc -l)
    print_success "Backend: $BACKEND_FILES files, $BACKEND_SIZE"
fi

# Frontend build info
if [[ -d "frontend/dist" ]]; then
    FRONTEND_SIZE=$(du -sh frontend/dist | cut -f1)
    FRONTEND_JS_FILES=$(find frontend/dist -name "*.js" | wc -l)
    FRONTEND_CSS_FILES=$(find frontend/dist -name "*.css" | wc -l)
    print_success "Frontend: $FRONTEND_JS_FILES JS, $FRONTEND_CSS_FILES CSS files, $FRONTEND_SIZE"
fi

print_status "Environment: $ENVIRONMENT"
print_status "Build time: ${BUILD_TIME}s"
print_status "Parallel: $PARALLEL"

# Environment-specific instructions
case $ENVIRONMENT in
    production)
        echo ""
        print_warning "🌟 PRODUCTION BUILD READY"
        echo "Next steps:"
        echo "1. Run final tests: npm run test"
        echo "2. Deploy: ./scripts/deploy.sh --environment production"
        echo "3. Monitor: Check health endpoints after deployment"
        ;;
    staging)
        echo ""
        print_status "🧪 STAGING BUILD READY"
        echo "Next steps:"
        echo "1. Deploy: ./scripts/deploy.sh --environment staging"
        echo "2. Test: Run E2E tests against staging"
        echo "3. Validate: Check all functionality works"
        ;;
    *)
        echo ""
        print_status "🛠️ DEVELOPMENT BUILD READY"
        echo "Next steps:"
        echo "1. Start backend: cd backend && npm start"
        echo "2. Start frontend: cd frontend && npm run dev"
        echo "3. Test: Open http://localhost:3000"
        ;;
esac

print_success "🎉 Build completed successfully in ${BUILD_TIME}s!"

# Create build manifest
BUILD_MANIFEST="build-manifest.json"
cat > "$BUILD_MANIFEST" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "environment": "$ENVIRONMENT",
  "git_commit": "$(git rev-parse HEAD)",
  "git_branch": "$(git branch --show-current)",
  "build_time_seconds": $BUILD_TIME,
  "parallel_build": $PARALLEL,
  "backend": {
    "size": "$(du -sh backend/dist 2>/dev/null | cut -f1 || echo 'N/A')",
    "files": $(find backend/dist -type f 2>/dev/null | wc -l || echo 0)
  },
  "frontend": {
    "size": "$(du -sh frontend/dist 2>/dev/null | cut -f1 || echo 'N/A')",
    "js_files": $(find frontend/dist -name "*.js" 2>/dev/null | wc -l || echo 0),
    "css_files": $(find frontend/dist -name "*.css" 2>/dev/null | wc -l || echo 0)
  }
}
EOF

print_status "Build manifest created: $BUILD_MANIFEST"
