#!/bin/bash

# =============================================================================
# RENTAHUB DEPLOYMENT SCRIPT
# =============================================================================
# Automated deployment script for RentaHub with validation and rollback

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="staging"
SKIP_TESTS=false
SKIP_BUILD=false
DRY_RUN=false
ROLLBACK=false
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Target environment (staging|production)"
            echo "  --skip-tests            Skip running tests"
            echo "  --skip-build            Skip build validation"
            echo "  --dry-run               Show what would be deployed without deploying"
            echo "  --rollback              Rollback to previous deployment"
            echo "  --force                 Force deployment without confirmations"
            echo "  --help                  Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --environment staging"
            echo "  $0 --environment production --force"
            echo "  $0 --rollback --environment production"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

print_status "🚀 RentaHub Deployment Script"
print_status "Environment: $ENVIRONMENT"
print_status "Skip Tests: $SKIP_TESTS"
print_status "Skip Build: $SKIP_BUILD"
print_status "Dry Run: $DRY_RUN"
print_status "Rollback: $ROLLBACK"
echo ""

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    print_error "Railway CLI is not installed. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if logged in to Railway
if ! railway whoami &> /dev/null; then
    print_error "Not logged in to Railway. Please login first:"
    echo "railway login"
    exit 1
fi

# Function to get current deployment info
get_current_deployment() {
    local service=$1
    railway status --service $service --environment $ENVIRONMENT --json 2>/dev/null || echo "{}"
}

# Function to perform rollback
perform_rollback() {
    print_status "🔄 Rolling back deployment..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Would rollback to previous deployment"
        return 0
    fi
    
    # Get previous deployment
    local backend_deployment=$(get_current_deployment "backend")
    local frontend_deployment=$(get_current_deployment "frontend")
    
    print_status "Rolling back backend service..."
    railway rollback --service backend --environment $ENVIRONMENT
    
    print_status "Rolling back frontend service..."
    railway rollback --service frontend --environment $ENVIRONMENT
    
    print_success "Rollback completed successfully"
    return 0
}

# Handle rollback request
if [[ "$ROLLBACK" == "true" ]]; then
    if [[ "$ENVIRONMENT" == "production" && "$FORCE" != "true" ]]; then
        echo ""
        print_warning "⚠️  You are about to rollback PRODUCTION deployment!"
        read -p "Are you sure you want to continue? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            print_status "Rollback cancelled"
            exit 0
        fi
    fi
    
    perform_rollback
    exit 0
fi

# Pre-deployment validation
print_status "🔍 Pre-deployment validation..."

# Check if we're on the correct branch
current_branch=$(git branch --show-current)
if [[ "$ENVIRONMENT" == "production" && "$current_branch" != "main" ]]; then
    print_error "Production deployments must be from 'main' branch. Current branch: $current_branch"
    exit 1
elif [[ "$ENVIRONMENT" == "staging" && "$current_branch" != "develop" && "$current_branch" != "main" ]]; then
    print_warning "Staging deployments are typically from 'develop' branch. Current branch: $current_branch"
    if [[ "$FORCE" != "true" ]]; then
        read -p "Continue anyway? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
fi

# Check for uncommitted changes
if [[ -n $(git status --porcelain) ]]; then
    print_error "You have uncommitted changes. Please commit or stash them first."
    git status --short
    exit 1
fi

# Validate environment configuration
print_status "Validating environment configuration..."
cd backend
if ! npm run validate-env; then
    print_error "Environment validation failed"
    exit 1
fi
cd ..

# Run tests unless skipped
if [[ "$SKIP_TESTS" != "true" ]]; then
    print_status "🧪 Running tests..."
    
    cd backend
    if ! npm test; then
        print_error "Tests failed. Deployment aborted."
        exit 1
    fi
    cd ..
    
    print_success "All tests passed"
fi

# Build validation unless skipped
if [[ "$SKIP_BUILD" != "true" ]]; then
    print_status "🏗️ Validating build..."
    
    # Build backend
    cd backend
    if ! npm run build; then
        print_error "Backend build failed"
        exit 1
    fi
    cd ..
    
    # Build frontend
    cd frontend
    if ! npm run build; then
        print_error "Frontend build failed"
        exit 1
    fi
    cd ..
    
    print_success "Build validation passed"
fi

# Get current deployment info for comparison
print_status "📊 Getting current deployment info..."
current_backend=$(get_current_deployment "backend")
current_frontend=$(get_current_deployment "frontend")

# Show deployment summary
print_status "📋 Deployment Summary"
echo "Environment: $ENVIRONMENT"
echo "Git Branch: $current_branch"
echo "Git Commit: $(git rev-parse --short HEAD)"
echo "Timestamp: $(date)"

if [[ "$DRY_RUN" == "true" ]]; then
    print_status "DRY RUN: Would deploy to $ENVIRONMENT environment"
    print_status "Backend service would be updated"
    print_status "Frontend service would be updated"
    exit 0
fi

# Production deployment confirmation
if [[ "$ENVIRONMENT" == "production" && "$FORCE" != "true" ]]; then
    echo ""
    print_warning "⚠️  You are about to deploy to PRODUCTION!"
    print_warning "This will affect live users and real data."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
fi

# Deploy to Railway
print_status "🚀 Deploying to Railway ($ENVIRONMENT)..."

# Set Railway environment
export RAILWAY_ENVIRONMENT=$ENVIRONMENT

# Deploy backend
print_status "Deploying backend service..."
if ! railway up --service backend --environment $ENVIRONMENT; then
    print_error "Backend deployment failed"
    exit 1
fi

# Deploy frontend
print_status "Deploying frontend service..."
if ! railway up --service frontend --environment $ENVIRONMENT; then
    print_error "Frontend deployment failed"
    print_warning "Backend was deployed successfully, but frontend failed"
    exit 1
fi

# Wait for deployment to be ready
print_status "⏳ Waiting for deployment to be ready..."
sleep 30

# Health check
print_status "🔍 Performing health checks..."

# Get service URLs
if [[ "$ENVIRONMENT" == "production" ]]; then
    BACKEND_URL="https://rentahub-backend-production.up.railway.app"
    FRONTEND_URL="https://rentahub-frontend-production.up.railway.app"
else
    BACKEND_URL="https://rentahub-backend-staging.up.railway.app"
    FRONTEND_URL="https://rentahub-frontend-staging.up.railway.app"
fi

# Check backend health
print_status "Checking backend health..."
if curl -f "$BACKEND_URL/health" > /dev/null 2>&1; then
    print_success "Backend health check passed"
else
    print_error "Backend health check failed"
    print_warning "Deployment may have issues. Check logs: railway logs --service backend --environment $ENVIRONMENT"
fi

# Check frontend
print_status "Checking frontend..."
if curl -f "$FRONTEND_URL" > /dev/null 2>&1; then
    print_success "Frontend health check passed"
else
    print_error "Frontend health check failed"
    print_warning "Deployment may have issues. Check logs: railway logs --service frontend --environment $ENVIRONMENT"
fi

# Create deployment tag
if [[ "$ENVIRONMENT" == "production" ]]; then
    TAG="v$(date +'%Y.%m.%d')-$(git rev-parse --short HEAD)"
    print_status "Creating deployment tag: $TAG"
    git tag $TAG
    git push origin $TAG
fi

# Success message
print_success "🎉 Deployment completed successfully!"
echo ""
echo "🔗 Deployment URLs:"
echo "Frontend: $FRONTEND_URL"
echo "Backend:  $BACKEND_URL"
echo "Health:   $BACKEND_URL/health"
echo ""
echo "📊 Monitoring:"
echo "Backend Logs:  railway logs --service backend --environment $ENVIRONMENT"
echo "Frontend Logs: railway logs --service frontend --environment $ENVIRONMENT"
echo ""

if [[ "$ENVIRONMENT" == "production" ]]; then
    echo "🏷️ Deployment Tag: $TAG"
    echo ""
fi

print_success "Deployment to $ENVIRONMENT completed successfully! 🚀"
