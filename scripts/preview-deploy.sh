#!/bin/bash

# =============================================================================
# RENTAHUB PREVIEW DEPLOYMENT SCRIPT
# =============================================================================
# Creates preview deployments for pull requests and feature branches

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
BRANCH_NAME=""
PR_NUMBER=""
ACTION="deploy"
CLEANUP=false
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--branch)
            BRANCH_NAME="$2"
            shift 2
            ;;
        -p|--pr)
            PR_NUMBER="$2"
            shift 2
            ;;
        --cleanup)
            CLEANUP=true
            ACTION="cleanup"
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -b, --branch BRANCH    Branch name for preview deployment"
            echo "  -p, --pr PR_NUMBER     Pull request number"
            echo "  --cleanup              Cleanup preview deployment"
            echo "  --dry-run              Show what would be done without executing"
            echo "  --help                 Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --branch feature/new-booking-flow"
            echo "  $0 --pr 123"
            echo "  $0 --cleanup --pr 123"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Auto-detect branch and PR if not provided
if [[ -z "$BRANCH_NAME" ]]; then
    BRANCH_NAME=$(git branch --show-current)
    print_status "Auto-detected branch: $BRANCH_NAME"
fi

# Generate preview environment name
if [[ -n "$PR_NUMBER" ]]; then
    PREVIEW_ENV="pr-$PR_NUMBER"
    PREVIEW_SUFFIX="pr$PR_NUMBER"
else
    # Sanitize branch name for environment
    PREVIEW_SUFFIX=$(echo "$BRANCH_NAME" | sed 's/[^a-zA-Z0-9]/-/g' | tr '[:upper:]' '[:lower:]' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
    PREVIEW_ENV="branch-$PREVIEW_SUFFIX"
fi

# Limit environment name length
if [[ ${#PREVIEW_ENV} -gt 30 ]]; then
    PREVIEW_ENV="${PREVIEW_ENV:0:27}..."
fi

print_status "🚀 RentaHub Preview Deployment"
print_status "Branch: $BRANCH_NAME"
print_status "PR Number: ${PR_NUMBER:-'N/A'}"
print_status "Preview Environment: $PREVIEW_ENV"
print_status "Action: $ACTION"
print_status "Dry Run: $DRY_RUN"
echo ""

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    print_error "Railway CLI is not installed. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if logged in to Railway
if ! railway whoami &> /dev/null; then
    print_error "Not logged in to Railway. Please login first:"
    echo "railway login"
    exit 1
fi

# Function to cleanup preview environment
cleanup_preview() {
    print_status "🧹 Cleaning up preview environment: $PREVIEW_ENV"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Would cleanup environment $PREVIEW_ENV"
        return 0
    fi
    
    # Remove Railway environment
    if railway environment list | grep -q "$PREVIEW_ENV"; then
        print_status "Removing Railway environment..."
        railway environment delete "$PREVIEW_ENV" --yes || print_warning "Failed to delete environment (may not exist)"
    else
        print_warning "Environment $PREVIEW_ENV not found in Railway"
    fi
    
    print_success "Preview environment cleanup completed"
    return 0
}

# Function to deploy preview
deploy_preview() {
    print_status "🚀 Deploying preview environment: $PREVIEW_ENV"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Would deploy to environment $PREVIEW_ENV"
        print_status "DRY RUN: Backend URL would be: https://rentahub-backend-$PREVIEW_SUFFIX.up.railway.app"
        print_status "DRY RUN: Frontend URL would be: https://rentahub-frontend-$PREVIEW_SUFFIX.up.railway.app"
        return 0
    fi
    
    # Ensure we're on the correct branch
    current_branch=$(git branch --show-current)
    if [[ "$current_branch" != "$BRANCH_NAME" ]]; then
        print_status "Switching to branch: $BRANCH_NAME"
        git checkout "$BRANCH_NAME"
    fi
    
    # Pull latest changes
    print_status "Pulling latest changes..."
    git pull origin "$BRANCH_NAME" || print_warning "Failed to pull latest changes"
    
    # Build the application
    print_status "Building application for preview..."
    ./scripts/build-all.sh --environment staging
    
    # Create or update Railway environment
    print_status "Setting up Railway environment..."
    
    # Check if environment exists
    if ! railway environment list | grep -q "$PREVIEW_ENV"; then
        print_status "Creating new Railway environment: $PREVIEW_ENV"
        railway environment create "$PREVIEW_ENV"
    else
        print_status "Using existing Railway environment: $PREVIEW_ENV"
    fi
    
    # Set environment variables for preview
    print_status "Configuring environment variables..."
    railway environment use "$PREVIEW_ENV"
    
    # Set preview-specific variables
    railway variables set NODE_ENV=staging
    railway variables set RAILWAY_ENVIRONMENT=preview
    railway variables set PREVIEW_BRANCH="$BRANCH_NAME"
    
    if [[ -n "$PR_NUMBER" ]]; then
        railway variables set PR_NUMBER="$PR_NUMBER"
    fi
    
    # Deploy backend
    print_status "Deploying backend service..."
    railway up --service backend --environment "$PREVIEW_ENV"
    
    # Get backend URL
    BACKEND_URL="https://rentahub-backend-$PREVIEW_SUFFIX.up.railway.app"
    
    # Deploy frontend with backend URL
    print_status "Deploying frontend service..."
    VITE_API_URL="$BACKEND_URL" railway up --service frontend --environment "$PREVIEW_ENV"
    
    # Get frontend URL
    FRONTEND_URL="https://rentahub-frontend-$PREVIEW_SUFFIX.up.railway.app"
    
    # Wait for deployments to be ready
    print_status "⏳ Waiting for deployments to be ready..."
    sleep 45
    
    # Health checks
    print_status "🔍 Performing health checks..."
    
    # Check backend
    if curl -f "$BACKEND_URL/health" > /dev/null 2>&1; then
        print_success "Backend health check passed"
    else
        print_warning "Backend health check failed - deployment may still be starting"
    fi
    
    # Check frontend
    if curl -f "$FRONTEND_URL" > /dev/null 2>&1; then
        print_warning "Frontend health check failed - deployment may still be starting"
    else
        print_success "Frontend health check passed"
    fi
    
    # Create preview summary
    PREVIEW_SUMMARY="preview-summary.json"
    cat > "$PREVIEW_SUMMARY" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "branch": "$BRANCH_NAME",
  "pr_number": "${PR_NUMBER:-null}",
  "environment": "$PREVIEW_ENV",
  "urls": {
    "frontend": "$FRONTEND_URL",
    "backend": "$BACKEND_URL",
    "health": "$BACKEND_URL/health"
  },
  "git": {
    "commit": "$(git rev-parse HEAD)",
    "commit_short": "$(git rev-parse --short HEAD)",
    "author": "$(git log -1 --pretty=format:'%an')",
    "message": "$(git log -1 --pretty=format:'%s')"
  }
}
EOF
    
    # Success message
    print_success "🎉 Preview deployment completed!"
    echo ""
    echo "🔗 Preview URLs:"
    echo "Frontend: $FRONTEND_URL"
    echo "Backend:  $BACKEND_URL"
    echo "Health:   $BACKEND_URL/health"
    echo ""
    echo "📊 Environment: $PREVIEW_ENV"
    echo "🌿 Branch: $BRANCH_NAME"
    echo "📝 Commit: $(git rev-parse --short HEAD)"
    echo ""
    echo "📋 Preview Summary: $PREVIEW_SUMMARY"
    
    # Generate GitHub comment if PR number is provided
    if [[ -n "$PR_NUMBER" ]]; then
        GITHUB_COMMENT="github-comment.md"
        cat > "$GITHUB_COMMENT" << EOF
## 🚀 Preview Deployment Ready

Your pull request has been deployed to a preview environment!

### 🔗 Preview Links
- **🌐 Frontend**: $FRONTEND_URL
- **⚡ Backend API**: $BACKEND_URL
- **❤️ Health Check**: $BACKEND_URL/health

### 📋 Deployment Info
- **Environment**: \`$PREVIEW_ENV\`
- **Branch**: \`$BRANCH_NAME\`
- **Commit**: \`$(git rev-parse --short HEAD)\`
- **Deployed**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")

### 🧪 Testing
- Test the preview deployment thoroughly
- Check all functionality works as expected
- Verify mobile responsiveness
- Test payment flows (Stripe test mode)

---

**🔄 This preview will be automatically updated when you push new changes.**
**🧹 Preview environment will be cleaned up when the PR is closed.**
EOF
        
        print_status "GitHub comment template created: $GITHUB_COMMENT"
    fi
    
    return 0
}

# Execute action
case $ACTION in
    deploy)
        deploy_preview
        ;;
    cleanup)
        cleanup_preview
        ;;
    *)
        print_error "Unknown action: $ACTION"
        exit 1
        ;;
esac

print_success "Preview deployment script completed successfully! 🚀"
