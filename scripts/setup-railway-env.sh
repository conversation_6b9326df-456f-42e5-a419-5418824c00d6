#!/bin/bash

# =============================================================================
# RAILWAY ENVIRONMENT SETUP SCRIPT
# =============================================================================
# This script helps you set up environment variables in Railway
# Run this after cleaning up your local environment files

echo "🚂 Railway Environment Setup"
echo "================================"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "   npm install -g @railway/cli"
    echo "   or visit: https://docs.railway.app/develop/cli"
    exit 1
fi

# Check if user is logged in to Railway
if ! railway whoami &> /dev/null; then
    echo "🔐 Please log in to Railway first:"
    echo "   railway login"
    exit 1
fi

echo "✅ Railway CLI is ready"
echo ""

# =============================================================================
# LOAD ENVIRONMENT VARIABLES FROM .env
# =============================================================================
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please run cleanup-env-files.sh first"
    exit 1
fi

echo "📝 Loading environment variables from .env..."

# Function to set Railway environment variable
set_railway_var() {
    local key=$1
    local value=$2
    
    if [ -n "$value" ] && [ "$value" != "your_" ] && [[ "$value" != *"your_"* ]]; then
        echo "Setting $key..."
        railway variables set "$key=$value"
    else
        echo "⚠️  Skipping $key (placeholder value detected)"
    fi
}

# Read .env file and set variables in Railway
while IFS='=' read -r key value; do
    # Skip comments and empty lines
    [[ $key =~ ^#.*$ ]] && continue
    [[ -z $key ]] && continue
    
    # Remove quotes from value
    value=$(echo "$value" | sed 's/^"//;s/"$//')
    
    # Set important variables in Railway
    case $key in
        NODE_ENV|PORT|DATABASE_URL|SUPABASE_URL|SUPABASE_ANON_KEY|SUPABASE_SERVICE_ROLE_KEY|VITE_SUPABASE_URL|VITE_SUPABASE_ANON_KEY|JWT_SECRET|STRIPE_SECRET_KEY|STRIPE_PUBLISHABLE_KEY|STRIPE_WEBHOOK_SECRET|VITE_STRIPE_PUBLISHABLE_KEY|FRONTEND_URL|ADMIN_URL|BACKEND_URL)
            set_railway_var "$key" "$value"
            ;;
    esac
done < .env

echo ""
echo "🎉 Railway environment variables setup complete!"
echo ""

# =============================================================================
# VERIFY RAILWAY DEPLOYMENT
# =============================================================================
echo "🔍 Verifying Railway deployment..."

# Check if services are deployed
echo "📋 Current Railway services:"
railway status

echo ""
echo "🚀 Next steps:"
echo "  1. Verify your environment variables: railway variables"
echo "  2. Deploy your services: railway up"
echo "  3. Check logs: railway logs"
echo ""
echo "💡 Useful Railway commands:"
echo "  railway variables          - List all environment variables"
echo "  railway variables set KEY=VALUE  - Set a variable"
echo "  railway logs               - View deployment logs"
echo "  railway status             - Check service status"
echo "  railway open               - Open deployed app"
