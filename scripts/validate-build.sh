#!/bin/bash

# =============================================================================
# RENTAHUB BUILD VALIDATION SCRIPT
# =============================================================================
# Comprehensive build validation for both backend and frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
SKIP_BACKEND=false
SKIP_FRONTEND=false
SKIP_LINT=false
SKIP_TESTS=false
VERBOSE=false
CLEAN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-backend)
            SKIP_BACKEND=true
            shift
            ;;
        --skip-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        --skip-lint)
            SKIP_LINT=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --skip-backend     Skip backend validation"
            echo "  --skip-frontend    Skip frontend validation"
            echo "  --skip-lint        Skip linting checks"
            echo "  --skip-tests       Skip test execution"
            echo "  --verbose          Verbose output"
            echo "  --clean            Clean build directories first"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_status "🔍 RentaHub Build Validation"
echo "Skip Backend: $SKIP_BACKEND"
echo "Skip Frontend: $SKIP_FRONTEND"
echo "Skip Lint: $SKIP_LINT"
echo "Skip Tests: $SKIP_TESTS"
echo "Verbose: $VERBOSE"
echo "Clean: $CLEAN"
echo ""

# Track validation results
VALIDATION_ERRORS=0

# Function to increment error count
increment_errors() {
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
}

# Function to run command with error handling
run_command() {
    local description=$1
    local command=$2
    local directory=${3:-"."}
    
    print_status "$description"
    
    if [[ "$VERBOSE" == "true" ]]; then
        echo "Running: $command in $directory"
    fi
    
    cd "$directory"
    if eval "$command"; then
        print_success "$description completed"
        cd - > /dev/null
        return 0
    else
        print_error "$description failed"
        increment_errors
        cd - > /dev/null
        return 1
    fi
}

# Clean build directories if requested
if [[ "$CLEAN" == "true" ]]; then
    print_status "🧹 Cleaning build directories..."
    
    if [[ "$SKIP_BACKEND" != "true" ]]; then
        rm -rf backend/dist
        rm -rf backend/node_modules/.cache
        print_success "Backend build directory cleaned"
    fi
    
    if [[ "$SKIP_FRONTEND" != "true" ]]; then
        rm -rf frontend/dist
        rm -rf frontend/node_modules/.cache
        print_success "Frontend build directory cleaned"
    fi
fi

# Validate Node.js and npm versions
print_status "🔍 Validating environment..."

NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)

print_status "Node.js version: $NODE_VERSION"
print_status "npm version: $NPM_VERSION"

# Check minimum Node.js version (v18+)
NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
if [[ $NODE_MAJOR -lt 18 ]]; then
    print_error "Node.js version 18 or higher is required. Current: $NODE_VERSION"
    increment_errors
fi

# Backend validation
if [[ "$SKIP_BACKEND" != "true" ]]; then
    print_status "🏗️ Backend Build Validation"
    
    # Check if backend directory exists
    if [[ ! -d "backend" ]]; then
        print_error "Backend directory not found"
        increment_errors
    else
        # Install dependencies
        run_command "Installing backend dependencies" "npm ci" "backend"
        
        # TypeScript compilation check
        run_command "TypeScript compilation check" "npx tsc --noEmit" "backend"
        
        # Linting (if not skipped)
        if [[ "$SKIP_LINT" != "true" ]]; then
            run_command "Backend linting" "npm run lint" "backend"
        fi
        
        # Build backend
        run_command "Building backend" "npm run build" "backend"
        
        # Validate build output
        if [[ ! -d "backend/dist" ]]; then
            print_error "Backend build output directory not found"
            increment_errors
        else
            # Check for main entry point
            if [[ ! -f "backend/dist/index.js" ]]; then
                print_error "Backend main entry point not found in build output"
                increment_errors
            else
                print_success "Backend build output validated"
            fi
            
            # Check build size
            BUILD_SIZE=$(du -sh backend/dist | cut -f1)
            print_status "Backend build size: $BUILD_SIZE"
        fi
        
        # Run tests (if not skipped)
        if [[ "$SKIP_TESTS" != "true" ]]; then
            run_command "Backend unit tests" "npm run test:unit" "backend"
        fi
        
        # Validate environment configuration
        run_command "Environment validation" "npm run validate-env" "backend"
        
        # Check for security vulnerabilities
        print_status "Checking for security vulnerabilities..."
        cd backend
        if npm audit --audit-level=high; then
            print_success "No high-severity vulnerabilities found"
        else
            print_warning "Security vulnerabilities detected - review npm audit output"
        fi
        cd ..
    fi
fi

# Frontend validation
if [[ "$SKIP_FRONTEND" != "true" ]]; then
    print_status "🎨 Frontend Build Validation"
    
    # Check if frontend directory exists
    if [[ ! -d "frontend" ]]; then
        print_error "Frontend directory not found"
        increment_errors
    else
        # Install dependencies
        run_command "Installing frontend dependencies" "npm ci" "frontend"
        
        # TypeScript compilation check
        run_command "TypeScript compilation check" "npx tsc --noEmit" "frontend"
        
        # Linting (if not skipped)
        if [[ "$SKIP_LINT" != "true" ]]; then
            run_command "Frontend linting" "npm run lint" "frontend"
        fi
        
        # Build frontend
        run_command "Building frontend" "npm run build" "frontend"
        
        # Validate build output
        if [[ ! -d "frontend/dist" ]]; then
            print_error "Frontend build output directory not found"
            increment_errors
        else
            # Check for main HTML file
            if [[ ! -f "frontend/dist/index.html" ]]; then
                print_error "Frontend main HTML file not found in build output"
                increment_errors
            else
                print_success "Frontend build output validated"
            fi
            
            # Check build size
            BUILD_SIZE=$(du -sh frontend/dist | cut -f1)
            print_status "Frontend build size: $BUILD_SIZE"
            
            # Check for large assets
            print_status "Checking for large assets..."
            find frontend/dist -type f -size +1M -exec ls -lh {} \; | while read line; do
                print_warning "Large asset found: $line"
            done
        fi
        
        # Check for security vulnerabilities
        print_status "Checking for security vulnerabilities..."
        cd frontend
        if npm audit --audit-level=high; then
            print_success "No high-severity vulnerabilities found"
        else
            print_warning "Security vulnerabilities detected - review npm audit output"
        fi
        cd ..
    fi
fi

# Bundle analysis (if tools are available)
print_status "📊 Bundle Analysis"

if [[ "$SKIP_BACKEND" != "true" && -d "backend/dist" ]]; then
    # Analyze backend bundle
    BACKEND_FILES=$(find backend/dist -name "*.js" | wc -l)
    BACKEND_SIZE=$(du -sh backend/dist | cut -f1)
    print_status "Backend bundle: $BACKEND_FILES files, $BACKEND_SIZE total"
fi

if [[ "$SKIP_FRONTEND" != "true" && -d "frontend/dist" ]]; then
    # Analyze frontend bundle
    FRONTEND_JS_FILES=$(find frontend/dist -name "*.js" | wc -l)
    FRONTEND_CSS_FILES=$(find frontend/dist -name "*.css" | wc -l)
    FRONTEND_SIZE=$(du -sh frontend/dist | cut -f1)
    print_status "Frontend bundle: $FRONTEND_JS_FILES JS files, $FRONTEND_CSS_FILES CSS files, $FRONTEND_SIZE total"
    
    # Check for source maps in production build
    if find frontend/dist -name "*.map" | grep -q .; then
        print_warning "Source maps found in production build - consider removing for security"
    fi
fi

# Docker build validation (if Dockerfile exists)
if [[ -f "Dockerfile" ]]; then
    print_status "🐳 Docker Build Validation"
    
    # Validate Dockerfile syntax
    if command -v docker &> /dev/null; then
        if docker build --dry-run . > /dev/null 2>&1; then
            print_success "Dockerfile syntax is valid"
        else
            print_error "Dockerfile syntax validation failed"
            increment_errors
        fi
    else
        print_warning "Docker not available - skipping Docker validation"
    fi
fi

# Configuration validation
print_status "⚙️ Configuration Validation"

# Check for required configuration files
REQUIRED_FILES=(
    "package.json"
    "backend/package.json"
    "frontend/package.json"
    "backend/tsconfig.json"
    "frontend/tsconfig.json"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        print_error "Required file not found: $file"
        increment_errors
    fi
done

# Validate package.json files
print_status "Validating package.json files..."

for pkg_file in "backend/package.json" "frontend/package.json"; do
    if [[ -f "$pkg_file" ]]; then
        if ! python3 -m json.tool "$pkg_file" > /dev/null 2>&1; then
            print_error "Invalid JSON in $pkg_file"
            increment_errors
        else
            print_success "$pkg_file is valid JSON"
        fi
    fi
done

# Check for environment files
ENV_FILES=(
    "backend/.env.example"
    "backend/.env.staging"
    "backend/.env.production"
)

for env_file in "${ENV_FILES[@]}"; do
    if [[ ! -f "$env_file" ]]; then
        print_warning "Environment file not found: $env_file"
    fi
done

# Final validation summary
print_status "📋 Build Validation Summary"
echo "=" * 50

if [[ $VALIDATION_ERRORS -eq 0 ]]; then
    print_success "🎉 All build validations passed!"
    echo ""
    echo "✅ Build artifacts are ready for deployment"
    echo "✅ No critical issues found"
    echo "✅ Security checks completed"
    echo ""
    
    if [[ "$SKIP_BACKEND" != "true" && -d "backend/dist" ]]; then
        echo "📦 Backend build: backend/dist/"
    fi
    
    if [[ "$SKIP_FRONTEND" != "true" && -d "frontend/dist" ]]; then
        echo "📦 Frontend build: frontend/dist/"
    fi
    
    echo ""
    print_success "Build validation completed successfully! 🚀"
    exit 0
else
    print_error "❌ Build validation failed with $VALIDATION_ERRORS error(s)"
    echo ""
    echo "Please fix the above issues before proceeding with deployment."
    echo ""
    exit 1
fi
