#!/bin/bash

# Credential Rotation Script for RentaHub
# This script helps generate new credentials and provides guidance on rotation

set -e

echo "🔐 RentaHub Credential Rotation Script"
echo "======================================"

# Function to generate secure random string
generate_secret() {
    openssl rand -base64 $1 | tr -d "=+/" | cut -c1-$1
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -base64 64
}

echo ""
echo "📋 Generating new credentials..."
echo ""

# Generate new JWT secret
NEW_JWT_SECRET=$(generate_jwt_secret)
echo "✅ New JWT Secret: $NEW_JWT_SECRET"

# Generate webhook secret
NEW_WEBHOOK_SECRET=$(generate_secret 32)
echo "✅ New Webhook Secret: whsec_$NEW_WEBHOOK_SECRET"

# Generate API key
NEW_API_KEY=$(generate_secret 32)
echo "✅ New API Key: $NEW_API_KEY"

echo ""
echo "📝 Manual Rotation Required:"
echo ""
echo "1. 🏦 Database (Supabase):"
echo "   - Go to Supabase dashboard"
echo "   - Project Settings > API"
echo "   - Regenerate anon/service_role keys"
echo ""
echo "2. 💳 Stripe:"
echo "   - Go to Stripe dashboard"
echo "   - Developers > API keys"
echo "   - Create new secret key"
echo "   - Update webhook endpoints"
echo ""
echo "3. 📧 Email (SMTP):"
echo "   - Rotate app passwords in your email provider"
echo "   - Update SMTP credentials"
echo ""
echo "4. 🗺️  Google Maps (if used):"
echo "   - Go to Google Cloud Console"
echo "   - APIs & Services > Credentials"
echo "   - Create new API key"
echo ""
echo "⚠️  IMPORTANT STEPS AFTER ROTATION:"
echo ""
echo "1. Update all .env files with new credentials"
echo "2. Update production environment variables"
echo "3. Test all integrations"
echo "4. Monitor logs for any authentication errors"
echo "5. Revoke old credentials after confirming new ones work"
echo ""
echo "🔒 Security Checklist:"
echo "□ All .env files updated"
echo "□ Production environment updated"
echo "□ Stripe webhooks updated"
echo "□ Database connections tested"
echo "□ Email functionality tested"
echo "□ Old credentials revoked"
echo "□ Team notified of changes"
echo ""
echo "📋 Use the generated secrets above in your .env files"
echo "📧 Notify your team about the credential rotation"