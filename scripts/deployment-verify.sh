#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BACKEND_URL="${1:-https://rentahub-backend-production.up.railway.app}"
FRONTEND_URL="${2:-https://rentahub-frontend-production.up.railway.app}"

# Deployment Verification Function
verify_deployment() {
    echo -e "${YELLOW}🚀 Starting Deployment Verification${NC}"
    
    # 1. Backend Health Check
    echo -e "\n${YELLOW}Checking Backend Health:${NC}"
    BACKEND_HEALTH=$(curl -s -w "%{http_code}" "${BACKEND_URL}/health")
    if [ "${BACKEND_HEALTH}" -eq 200 ]; then
        echo -e "${GREEN}✅ Backend is healthy${NC}"
    else
        echo -e "${RED}❌ Backend health check failed (Status: ${BACKEND_HEALTH})${NC}"
        return 1
    fi
    
    # 2. Frontend Availability
    echo -e "\n${YELLOW}Checking Frontend Availability:${NC}"
    FRONTEND_STATUS=$(curl -s -w "%{http_code}" "${FRONTEND_URL}")
    if [ "${FRONTEND_STATUS}" -eq 200 ]; then
        echo -e "${GREEN}✅ Frontend is accessible${NC}"
    else
        echo -e "${RED}❌ Frontend not accessible (Status: ${FRONTEND_STATUS})${NC}"
        return 1
    fi
    
    # 3. API Endpoint Test
    echo -e "\n${YELLOW}Testing API Endpoint:${NC}"
    API_TEST=$(curl -s -w "%{http_code}" "${BACKEND_URL}/api")
    if [ "${API_TEST}" -eq 200 ]; then
        echo -e "${GREEN}✅ API endpoint is responding${NC}"
    else
        echo -e "${RED}❌ API endpoint test failed (Status: ${API_TEST})${NC}"
        return 1
    fi
    
    # Final Success
    echo -e "\n${GREEN}🎉 Deployment Verification Successful!${NC}"
    return 0
}

# Run Verification
verify_deployment

# Exit with appropriate status
exit $? 