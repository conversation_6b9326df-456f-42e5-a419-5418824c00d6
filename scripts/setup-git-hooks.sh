#!/bin/bash

# Setup script for Git hooks

echo "🔧 Setting up Git hooks for security..."

# Set the hooks directory
git config core.hooksPath .githooks

# Make sure hooks are executable
chmod +x .githooks/*

echo "✅ Git hooks installed successfully!"
echo ""
echo "The following security hooks are now active:"
echo "  - pre-commit: Prevents .env files from being committed"
echo "  - pre-commit: Scans for potential credential leaks"
echo "  - pre-commit: Validates environment configuration"
echo ""
echo "To bypass hooks (not recommended): git commit --no-verify"