#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BACKEND_SERVICE="${1:-backend}"
FRONTEND_SERVICE="${2:-frontend}"
RAILWAY_PROJECT="${3:-rentahub}"

echo -e "${YELLOW}🚀 Starting RentaHub Railway Deployment${NC}"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}❌ Railway CLI not found. Please install it:${NC}"
    echo "npm i -g @railway/cli"
    exit 1
fi

# Check login status
echo -e "\n${YELLOW}Checking Railway login status...${NC}"
RAILWAY_STATUS=$(railway status 2>&1)
if [[ $RAILWAY_STATUS == *"not logged in"* ]]; then
    echo -e "${RED}❌ Not logged in to Railway. Please login first:${NC}"
    echo "railway login"
    exit 1
fi

# Link to project if not already linked
echo -e "\n${YELLOW}Linking to Railway project...${NC}"
railway link

# Deploy backend
echo -e "\n${YELLOW}Deploying backend service...${NC}"
railway up --service $BACKEND_SERVICE --detach

# Deploy frontend
echo -e "\n${YELLOW}Deploying frontend service...${NC}"
railway up --service $FRONTEND_SERVICE --detach

echo -e "\n${GREEN}✅ Deployment initiated!${NC}"
echo -e "${YELLOW}Monitor deployment status in Railway dashboard:${NC}"
echo "https://railway.app/dashboard"

# Wait for deployments to complete
echo -e "\n${YELLOW}Waiting for deployments to complete...${NC}"
echo -e "${YELLOW}This may take a few minutes...${NC}"
sleep 30

# Get service URLs
echo -e "\n${YELLOW}Getting service URLs...${NC}"
BACKEND_URL=$(railway service url $BACKEND_SERVICE 2>/dev/null)
FRONTEND_URL=$(railway service url $FRONTEND_SERVICE 2>/dev/null)

if [[ -n "$BACKEND_URL" && -n "$FRONTEND_URL" ]]; then
    echo -e "${GREEN}✅ Deployment complete!${NC}"
    echo -e "${YELLOW}Backend URL:${NC} $BACKEND_URL"
    echo -e "${YELLOW}Frontend URL:${NC} $FRONTEND_URL"
    
    # Run verification script if available
    if [[ -f "./scripts/deployment-verify.sh" ]]; then
        echo -e "\n${YELLOW}Running deployment verification...${NC}"
        bash ./scripts/deployment-verify.sh "$BACKEND_URL" "$FRONTEND_URL"
    fi
else
    echo -e "${YELLOW}Deployment in progress...${NC}"
    echo -e "${YELLOW}Check Railway dashboard for status and URLs.${NC}"
fi 