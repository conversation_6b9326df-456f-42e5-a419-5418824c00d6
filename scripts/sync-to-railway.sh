#!/bin/bash

# =============================================================================
# RAILWAY ENVIRONMENT SYNC SCRIPT
# =============================================================================
# This script syncs your local .env to Railway environment variables

echo "🚂 Syncing Environment Variables to Railway"
echo "==========================================="

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    npm install -g @railway/cli
fi

# Check if user is logged in to Railway
if ! railway whoami &> /dev/null; then
    echo "🔐 Please log in to Railway first:"
    echo "   railway login"
    exit 1
fi

echo "✅ Railway CLI is ready"
echo ""

# =============================================================================
# SET CRITICAL ENVIRONMENT VARIABLES
# =============================================================================
echo "🔧 Setting critical environment variables in Railway..."

# Database & Supabase
railway variables set NODE_ENV=production
railway variables set PORT=3001
railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# Supabase Configuration
railway variables set SUPABASE_URL="https://rocxjzukyqelvuyltrfq.supabase.co"
railway variables set SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8"
railway variables set SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTA3ODQ3MSwiZXhwIjoyMDY2NjU0NDcxfQ.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU"

# Frontend Variables (Vite)
railway variables set VITE_SUPABASE_URL="https://rocxjzukyqelvuyltrfq.supabase.co"
railway variables set VITE_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJvY3hqenVreXFlbHZ1eWx0cmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNzg0NzEsImV4cCI6MjA2NjY1NDQ3MX0.4F9ityhdzvknK1OzlmXMNLDNJ2yzqx5WSuK-Zh6oNB8"

# Authentication
railway variables set JWT_SECRET="rentahub_jwt_secret_2025_production_secure_key_32_chars_minimum"
railway variables set JWT_EXPIRATION="24h"

# Stripe Configuration
railway variables set STRIPE_SECRET_KEY="sk_test_51RhDTEFSJ2b2gFS10sW4gFLJ"
railway variables set STRIPE_WEBHOOK_SECRET="whsec_13bb090c39c0e6d3a3d4d609de2e1ce3092100fae2f40393f0a5bc6b72ee438a"

# Email Configuration
railway variables set SMTP_HOST="smtp.elasticemail.com"
railway variables set SMTP_PORT="2525"
railway variables set SMTP_USER="<EMAIL>"
railway variables set SMTP_PASS="38187DC40E1EBD017958C403379C0065E5DE"
railway variables set SMTP_FROM="<EMAIL>"

# Application URLs
railway variables set FRONTEND_URL="https://your-frontend-domain.railway.app"
railway variables set ADMIN_URL="https://your-admin-domain.railway.app"
railway variables set BACKEND_URL="https://your-backend-domain.railway.app"

echo ""
echo "🎉 Environment variables synced to Railway!"
echo ""

# =============================================================================
# VERIFY DEPLOYMENT
# =============================================================================
echo "🔍 Verifying Railway deployment..."

echo "📋 Current Railway variables:"
railway variables

echo ""
echo "🚀 Next steps:"
echo "  1. Deploy your services: railway up"
echo "  2. Check deployment status: railway status"
echo "  3. View logs: railway logs"
echo "  4. Open deployed app: railway open"
echo ""
echo "💡 Useful Railway commands:"
echo "  railway variables          - List all environment variables"
echo "  railway logs               - View deployment logs"
echo "  railway status             - Check service status"
echo "  railway open               - Open deployed app"
