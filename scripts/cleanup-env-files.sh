#!/bin/bash

# =============================================================================
# ENVIRONMENT FILES CLEANUP SCRIPT
# =============================================================================
# This script removes duplicate and conflicting environment files
# and creates a unified environment configuration

echo "🧹 Starting Environment Files Cleanup..."

# =============================================================================
# 1. BACKUP EXISTING FILES (just in case)
# =============================================================================
echo "📦 Creating backup of existing environment files..."
mkdir -p .env-backup/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR=".env-backup/$(date +%Y%m%d_%H%M%S)"

# Backup any existing .env files
find . -name "*.env" -not -path "./node_modules/*" -not -path "./.env-backup/*" -exec cp {} $BACKUP_DIR/ \; 2>/dev/null || true
find . -name ".env.*" -not -path "./node_modules/*" -not -path "./.env-backup/*" -exec cp {} $BACKUP_DIR/ \; 2>/dev/null || true

echo "✅ Backup created in: $BACKUP_DIR"

# =============================================================================
# 2. REMOVE DUPLICATE ENVIRONMENT FILES
# =============================================================================
echo "🗑️  Removing duplicate environment files..."

# Remove root-level duplicate env files
rm -f backend.env
rm -f frontend.env  
rm -f admin.env

# Remove service-specific env examples (we'll use the unified one)
rm -f backend/.env.example
rm -f frontend/.env.example
rm -f admin/.env.example
rm -f __config/env.example

# Remove bookcars reference files (temporary clones)
rm -f bookcars/backend/.env.example
rm -f bookcars/mobile/.env.example

echo "✅ Duplicate environment files removed"

# =============================================================================
# 3. CREATE UNIFIED ENVIRONMENT STRUCTURE
# =============================================================================
echo "🔧 Creating unified environment structure..."

# Create .env from .env.example if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env from .env.example"
    echo "⚠️  IMPORTANT: Edit .env with your actual credentials!"
fi

# Create symlinks for services to use the root .env
echo "🔗 Creating environment symlinks for services..."

# Backend symlink
if [ ! -f backend/.env ]; then
    ln -sf ../.env backend/.env
    echo "✅ Created backend/.env symlink"
fi

# Frontend symlink  
if [ ! -f frontend/.env ]; then
    ln -sf ../.env frontend/.env
    echo "✅ Created frontend/.env symlink"
fi

# Admin symlink
if [ ! -f admin/.env ]; then
    ln -sf ../.env admin/.env
    echo "✅ Created admin/.env symlink"
fi

# =============================================================================
# 4. UPDATE PACKAGE.JSON SCRIPTS
# =============================================================================
echo "📦 Updating package.json scripts for unified environment..."

# Update root package.json to load from unified .env
if [ -f package.json ]; then
    # This would require jq to modify JSON, for now just inform user
    echo "📝 Please update your package.json scripts to use the unified .env"
fi

# =============================================================================
# 5. CLEAN UP RAILWAY CONFIGS
# =============================================================================
echo "🚂 Cleaning up Railway configurations..."

# Keep only the main railway.toml, remove duplicates
find . -name "railway.toml" -not -path "./railway.toml" -not -path "./node_modules/*" -delete 2>/dev/null || true
find . -name "railway.json" -not -path "./node_modules/*" -delete 2>/dev/null || true

echo "✅ Railway configs cleaned up"

# =============================================================================
# 6. UPDATE GITIGNORE
# =============================================================================
echo "📝 Updating .gitignore..."

# Add environment files to gitignore if not already there
if [ -f .gitignore ]; then
    grep -q "^\.env$" .gitignore || echo ".env" >> .gitignore
    grep -q "^\.env\.local$" .gitignore || echo ".env.local" >> .gitignore
    grep -q "^\.env\.production$" .gitignore || echo ".env.production" >> .gitignore
    echo "✅ Updated .gitignore"
fi

# =============================================================================
# 7. SUMMARY
# =============================================================================
echo ""
echo "🎉 Environment Cleanup Complete!"
echo ""
echo "📋 Summary:"
echo "  ✅ Removed duplicate environment files"
echo "  ✅ Created unified .env.example"
echo "  ✅ Created .env (if it didn't exist)"
echo "  ✅ Created symlinks for all services"
echo "  ✅ Cleaned up Railway configurations"
echo "  ✅ Updated .gitignore"
echo ""
echo "🔧 Next Steps:"
echo "  1. Edit .env with your actual credentials"
echo "  2. Set the same environment variables in Railway dashboard"
echo "  3. Test all services with: npm run dev"
echo "  4. Deploy with: npm run deploy"
echo ""
echo "📦 Backup location: $BACKUP_DIR"
echo ""
echo "⚠️  IMPORTANT: Don't forget to update Railway environment variables!"
