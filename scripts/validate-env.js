#!/usr/bin/env node

/**
 * Environment Variable Validation Script
 * Checks for common security issues in .env files
 */

const fs = require('fs');
const path = require('path');

const DANGEROUS_PATTERNS = [
  { pattern: /sk_live_/, message: 'Live Stripe secret key detected!' },
  { pattern: /pk_live_/, message: 'Live Stripe publishable key detected!' },
  { pattern: /password.*=.*password/i, message: 'Default password detected!' },
  { pattern: /secret.*=.*secret/i, message: 'Default secret detected!' },
  { pattern: /key.*=.*your_.*key/i, message: 'Example key placeholder detected!' },
  { pattern: /localhost.*production/i, message: 'Localhost in production config!' },
  { pattern: /127\.0\.0\.1.*production/i, message: 'Local IP in production config!' },
];

const REQUIRED_VARS = {
  backend: [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'JWT_SECRET',
    'STRIPE_SECRET_KEY',
    'PORT'
  ],
  frontend: [
    'VITE_API_URL',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_STRIPE_PUBLISHABLE_KEY'
  ]
};

function validateEnvFile(filePath, requiredVars = []) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  ${filePath} not found`);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const errors = [];
  const warnings = [];
  const foundVars = new Set();

  lines.forEach((line, index) => {
    const trimmed = line.trim();
    
    // Skip comments and empty lines
    if (!trimmed || trimmed.startsWith('#')) return;
    
    // Check for dangerous patterns
    DANGEROUS_PATTERNS.forEach(({ pattern, message }) => {
      if (pattern.test(trimmed)) {
        errors.push(`Line ${index + 1}: ${message}`);
      }
    });
    
    // Check for placeholder values
    if (trimmed.includes('your_') || trimmed.includes('CHANGE_ME') || trimmed.includes('REPLACE_ME')) {
      warnings.push(`Line ${index + 1}: Placeholder value detected - ${trimmed.split('=')[0]}`);
    }
    
    // Track found variables
    const [key] = trimmed.split('=');
    if (key) foundVars.add(key);
  });
  
  // Check for missing required variables
  requiredVars.forEach(varName => {
    if (!foundVars.has(varName)) {
      warnings.push(`Missing required variable: ${varName}`);
    }
  });
  
  // Report results
  console.log(`\n📁 Validating ${filePath}:`);
  
  if (errors.length > 0) {
    console.log('❌ ERRORS:');
    errors.forEach(error => console.log(`   ${error}`));
  }
  
  if (warnings.length > 0) {
    console.log('⚠️  WARNINGS:');
    warnings.forEach(warning => console.log(`   ${warning}`));
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    console.log('✅ No issues found');
  }
  
  return errors.length === 0;
}

function main() {
  console.log('🔍 Environment Variable Security Validation');
  console.log('==========================================');
  
  const projectRoot = path.join(__dirname, '..');
  const backendEnv = path.join(projectRoot, 'backend', '.env');
  const frontendEnv = path.join(projectRoot, 'frontend', '.env');
  
  let allValid = true;
  
  allValid &= validateEnvFile(backendEnv, REQUIRED_VARS.backend);
  allValid &= validateEnvFile(frontendEnv, REQUIRED_VARS.frontend);
  
  console.log('\n' + '='.repeat(50));
  
  if (allValid) {
    console.log('✅ All environment files passed validation');
  } else {
    console.log('❌ Some environment files have issues that need attention');
    process.exit(1);
  }
  
  console.log('\n💡 Tips:');
  console.log('   - Never commit .env files to git');
  console.log('   - Use different credentials for each environment');
  console.log('   - Rotate credentials regularly');
  console.log('   - Use secret management in production');
}

if (require.main === module) {
  main();
}

module.exports = { validateEnvFile, DANGEROUS_PATTERNS, REQUIRED_VARS };