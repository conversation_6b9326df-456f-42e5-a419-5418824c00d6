#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Deploying All RentaHub Services to Railway${NC}"

# Get script directory
SCRIPT_DIR="$(dirname "$0")"

# Deploy backend
echo -e "\n${YELLOW}Starting backend deployment...${NC}"
bash "$SCRIPT_DIR/deploy-backend.sh"

# Deploy frontend
echo -e "\n${YELLOW}Starting frontend deployment...${NC}"
bash "$SCRIPT_DIR/deploy-frontend.sh"

# Deploy admin
echo -e "\n${YELLOW}Starting admin deployment...${NC}"
bash "$SCRIPT_DIR/deploy-admin.sh"

echo -e "\n${GREEN}✅ All deployments initiated!${NC}"
echo -e "${YELLOW}Monitor deployment status in Railway dashboard${NC}" 