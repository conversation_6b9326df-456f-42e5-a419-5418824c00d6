#!/bin/bash

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

SERVICE_NAME=${SERVICE_NAME:-"RENTAHUB ADMIN"}

echo -e "${YELLOW}🚀 Deploying Admin Service to Railway${NC}"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}❌ Railway CLI not found. Please install it:${NC}"
    echo "npm i -g @railway/cli"
    exit 1
fi

# Navigate to admin directory
cd "$(dirname "$0")/../admin"

# Check login status
echo -e "\n${YELLOW}Checking Railway login status...${NC}"
RAILWAY_STATUS=$(railway status 2>&1)
if [[ $RAILWAY_STATUS == *"not logged in"* ]]; then
    echo -e "${RED}❌ Not logged in to Railway. Please login first:${NC}"
    echo "railway login"
    exit 1
fi

# Link to project if not already linked
echo -e "\n${YELLOW}Linking to Railway project...${NC}"
railway link

# Deploy admin
echo -e "\n${YELLOW}Deploying admin service...${NC}"
railway up --service "${SERVICE_NAME}" --detach

echo -e "\n${GREEN}✅ Admin deployment initiated!${NC}"
echo -e "${YELLOW}Monitor deployment status in Railway dashboard${NC}" 