import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'

// Pages
import HomePage from './pages/HomePage'
import SearchPage from './pages/SearchPage'
import VehicleDetailPage from './pages/VehicleDetailPage'
import BookingPage from './pages/BookingPage'
import BookingConfirmation from './pages/BookingConfirmation'

// Components
import Header from './components/Header'
import Footer from './components/Footer'

// Create RentaHub theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#2BB673', // Jungle Green
    },
    secondary: {
      main: '#FF5F57', // Electric Coral
    },
    background: {
      default: '#FDFBF6', // Cream White
      paper: '#FFFFFF',
    },
    text: {
      primary: '#2C2C2C', // Deep Charcoal
      secondary: '#666666',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 600,
    },
    h3: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 16,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          textTransform: 'none',
          fontWeight: 600,
          padding: '12px 24px',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="App">
          <Header />
          <main style={{ minHeight: 'calc(100vh - 140px)' }}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/search" element={<SearchPage />} />
              <Route path="/vehicle/:id" element={<VehicleDetailPage />} />
              <Route path="/booking/:vehicleId" element={<BookingPage />} />
              <Route path="/booking-confirmation/:bookingId" element={<BookingConfirmation />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </ThemeProvider>
  )
}

export default App 