import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  Tooltip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Divider,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  ExitToApp as LogoutIcon,
  CheckCircle as ActiveIcon
} from '@mui/icons-material';
import SessionService, { SessionState } from '../services/SessionService';

interface SessionStatusProps {
  onExtendSession: () => void;
  onLogout: () => void;
}

const SessionStatus: React.FC<SessionStatusProps> = ({ onExtendSession, onLogout }) => {
  const [sessionState, setSessionState] = useState<SessionState>({
    isActive: false,
    lastActivity: 0,
    sessionStart: 0,
    showWarning: false,
    timeRemaining: 0
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    const sessionService = SessionService.getInstance();

    // Listen for session state changes
    const handleSessionChange = (state: SessionState) => {
      setSessionState(state);
    };

    sessionService.addListener(handleSessionChange);

    // Initial state
    setSessionState(sessionService.getState());

    // Update current time every second
    const timeTimer = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => {
      sessionService.removeListener(handleSessionChange);
      clearInterval(timeTimer);
    };
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleExtendSession = () => {
    onExtendSession();
    SessionService.getInstance().extendSession();
    handleMenuClose();
  };

  const handleLogout = () => {
    SessionService.getInstance().endSession();
    onLogout();
    handleMenuClose();
  };

  const formatTime = (milliseconds: number): string => {
    return SessionService.formatTimeRemaining(milliseconds);
  };

  const getSessionDuration = (): string => {
    if (!sessionState.isActive) return '0m';
    return SessionService.formatSessionDuration(currentTime - sessionState.sessionStart);
  };

  const getTimeUntilLogout = (): string => {
    if (!sessionState.isActive) return '0:00';
    const remaining = SessionService.getInstance().getTimeRemaining();
    return formatTime(remaining);
  };

  const getChipColor = (): 'success' | 'warning' | 'error' | 'default' => {
    if (!sessionState.isActive) return 'default';
    
    const remaining = SessionService.getInstance().getTimeRemaining();
    if (remaining < 60000) return 'error'; // Less than 1 minute
    if (remaining < 120000) return 'warning'; // Less than 2 minutes
    return 'success';
  };

  const getChipIcon = () => {
    if (!sessionState.isActive) return <TimeIcon />;
    
    const remaining = SessionService.getInstance().getTimeRemaining();
    if (remaining < 60000) return <TimeIcon />; // Urgent
    return <ActiveIcon />;
  };

  if (!sessionState.isActive) {
    return null;
  }

  return (
    <>
      <Tooltip title="Session Information">
        <Chip
          icon={getChipIcon()}
          label={getTimeUntilLogout()}
          color={getChipColor()}
          size="small"
          onClick={handleMenuOpen}
          sx={{ 
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: 'action.hover'
            }
          }}
        />
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 280 }
        }}
      >
        <Box sx={{ p: 2, pb: 1 }}>
          <Typography variant="subtitle2" color="text.secondary">
            Session Information
          </Typography>
        </Box>

        <MenuItem disabled>
          <ListItemIcon>
            <ActiveIcon color="success" />
          </ListItemIcon>
          <ListItemText
            primary="Session Active"
            secondary={`Duration: ${getSessionDuration()}`}
          />
        </MenuItem>

        <MenuItem disabled>
          <ListItemIcon>
            <TimeIcon color={getChipColor()} />
          </ListItemIcon>
          <ListItemText
            primary="Auto-logout in"
            secondary={getTimeUntilLogout()}
          />
        </MenuItem>

        <Divider />

        <MenuItem onClick={handleExtendSession}>
          <ListItemIcon>
            <RefreshIcon />
          </ListItemIcon>
          <ListItemText
            primary="Extend Session"
            secondary="Reset inactivity timer"
          />
        </MenuItem>

        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon />
          </ListItemIcon>
          <ListItemText
            primary="Logout Now"
            secondary="End session immediately"
          />
        </MenuItem>

        <Divider />

        <Box sx={{ p: 2, pt: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Sessions automatically expire after 5 minutes of inactivity.
            Minimum session duration is 30 minutes.
          </Typography>
        </Box>
      </Menu>
    </>
  );
};

export default SessionStatus;
