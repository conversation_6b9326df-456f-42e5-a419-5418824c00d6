import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Warning as WarningIcon,
  AccessTime as TimeIcon,
  Refresh as ExtendIcon
} from '@mui/icons-material';
import SessionService, { SessionState } from '../services/SessionService';

interface SessionWarningProps {
  onExtendSession: () => void;
  onLogout: () => void;
}

const SessionWarning: React.FC<SessionWarningProps> = ({ onExtendSession, onLogout }) => {
  const [sessionState, setSessionState] = useState<SessionState>({
    isActive: false,
    lastActivity: 0,
    sessionStart: 0,
    showWarning: false,
    timeRemaining: 0
  });

  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    const sessionService = SessionService.getInstance();

    // Listen for session state changes
    const handleSessionChange = (state: SessionState) => {
      setSessionState(state);
      setCountdown(state.timeRemaining);
    };

    sessionService.addListener(handleSessionChange);

    // Initial state
    setSessionState(sessionService.getState());

    return () => {
      sessionService.removeListener(handleSessionChange);
    };
  }, []);

  useEffect(() => {
    // Update countdown every second when warning is shown
    if (sessionState.showWarning && sessionState.timeRemaining > 0) {
      const timer = setInterval(() => {
        const remaining = SessionService.getInstance().getTimeRemaining();
        setCountdown(remaining);
        
        if (remaining <= 0) {
          clearInterval(timer);
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [sessionState.showWarning, sessionState.timeRemaining]);

  const handleExtendSession = () => {
    onExtendSession();
    SessionService.getInstance().extendSession();
  };

  const handleLogout = () => {
    SessionService.getInstance().endSession();
    onLogout();
  };

  const formatTime = (milliseconds: number): string => {
    return SessionService.formatTimeRemaining(milliseconds);
  };

  const getProgressValue = (): number => {
    const warningDuration = 60000; // 1 minute warning
    return ((warningDuration - countdown) / warningDuration) * 100;
  };

  if (!sessionState.showWarning || !sessionState.isActive) {
    return null;
  }

  return (
    <Dialog
      open={true}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderTop: '4px solid',
          borderTopColor: 'warning.main'
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon color="warning" />
          <Typography variant="h6">Session Expiring Soon</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Your session will expire due to inactivity. You will be automatically logged out to protect your account.
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Time remaining:
            </Typography>
            <Chip
              icon={<TimeIcon />}
              label={formatTime(countdown)}
              color="warning"
              variant="outlined"
              size="small"
            />
          </Box>
          
          <LinearProgress
            variant="determinate"
            value={getProgressValue()}
            color="warning"
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Session started: {SessionService.formatSessionDuration(sessionState.lastActivity - sessionState.sessionStart)} ago
          </Typography>
        </Box>

        <Typography variant="body2" color="text.secondary">
          Click "Stay Logged In" to extend your session, or "Logout" to end your session now.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleLogout}
          color="inherit"
          variant="outlined"
        >
          Logout Now
        </Button>
        <Button
          onClick={handleExtendSession}
          color="primary"
          variant="contained"
          startIcon={<ExtendIcon />}
          autoFocus
        >
          Stay Logged In
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionWarning;
