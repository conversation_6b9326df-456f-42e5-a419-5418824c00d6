// ROOT ENTRY POINT - REDIRECTS TO MAIN BACKEND
// This file serves as the main entry point and redirects to the backend service

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    message: 'RentaHub Root Service - Redirects to Backend',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    backendPort: 3001
  });
});

// Root endpoint with service information
app.get('/', (req, res) => {
  res.json({
    message: 'RentaHub Platform',
    version: '1.0.0',
    services: {
      backend: 'http://localhost:3001',
      frontend: 'http://localhost:5173',
      admin: 'http://localhost:4173'
    },
    note: 'This is the root service. Main API is at /backend or port 3001'
  });
});

// Redirect API calls to backend
app.use('/api', (req, res) => {
  res.status(302).json({
    message: 'API endpoints are available on the backend service',
    redirect: `http://localhost:3001${req.originalUrl}`,
    note: 'Please use port 3001 for API calls'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 RentaHub Root Service running on port ${PORT}`);
  console.log(`📊 Health check available at http://localhost:${PORT}/health`);
  console.log(`🔗 Main API service should be running on port 3001`);
});

export default app;