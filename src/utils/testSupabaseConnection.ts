import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testSupabaseConnection() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase credentials');
    process.exit(1);
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    console.log('🔗 Attempting to connect to Supabase...');
    
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Connection Test Failed:', error);
      process.exit(1);
    }

    console.log('✅ Supabase Connection Successful!');
    console.log('📊 Sample Data:', data);
  } catch (err) {
    console.error('❌ Unexpected Error:', err);
    process.exit(1);
  }
}

testSupabaseConnection(); 