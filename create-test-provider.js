#!/usr/bin/env node

/**
 * Create a test provider user in the database
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://rocxjzukyqelvuyltrfq.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.KyoqB0CuCUH6ZMNbFiJm6a_Q7m_wRro7XKQUKJns1cU';

// Test user ID from the frontend logs
const TEST_USER_ID = 'dcd4a493-ba65-4406-a65f-df364b141c27';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTestProvider() {
  console.log('🧪 Creating Test Provider User');
  console.log('==============================');
  console.log(`User ID: ${TEST_USER_ID}`);
  console.log('');

  try {
    // Step 1: Check if user already exists
    console.log('🔍 Checking if user already exists...');
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('id', TEST_USER_ID)
      .single();

    if (existingUser) {
      console.log('✅ User already exists:', existingUser.email);
      console.log('📊 User data:', JSON.stringify(existingUser, null, 2));
      return existingUser;
    }

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking user:', checkError);
      return null;
    }

    // Step 2: Create the user
    console.log('👤 Creating new user...');
    const userData = {
      id: TEST_USER_ID,
      email: '<EMAIL>',
      full_name: 'Test Provider',
      phone: '+**********',
      role: 'provider',
      is_verified: true,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert(userData)
      .select()
      .single();

    if (userError) {
      console.error('❌ Error creating user:', userError);
      return null;
    }

    console.log('✅ User created successfully:', newUser.email);

    // Step 3: Create provider profile
    console.log('🏢 Creating provider profile...');
    const providerData = {
      id: TEST_USER_ID,
      user_id: TEST_USER_ID,
      business_name: 'Test Vehicle Rentals',
      business_type: 'individual',
      description: 'Test provider for development and testing purposes',
      address: '123 Test Street, Test City, TC 12345',
      city: 'Test City',
      state: 'Test State',
      country: 'Test Country',
      postal_code: '12345',
      phone: '+**********',
      email: '<EMAIL>',
      website: 'https://test-provider.com',
      is_verified: true,
      is_active: true,
      verification_status: 'verified',
      rating: 4.5,
      total_reviews: 10,
      total_bookings: 25,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: newProvider, error: providerError } = await supabase
      .from('providers')
      .insert(providerData)
      .select()
      .single();

    if (providerError) {
      console.error('❌ Error creating provider:', providerError);
      // Try to clean up the user if provider creation failed
      await supabase.from('users').delete().eq('id', TEST_USER_ID);
      return null;
    }

    console.log('✅ Provider profile created successfully');

    // Step 4: Create some test vehicles
    console.log('🚗 Creating test vehicles...');
    const vehicleData = [
      {
        provider_id: TEST_USER_ID,
        name: 'Honda PCX 150',
        description: 'Comfortable and fuel-efficient scooter perfect for city rides',
        type: 'scooter',
        brand: 'Honda',
        model: 'PCX 150',
        year: 2023,
        price_per_day: 25.00,
        price_per_week: 150.00,
        price_per_month: 500.00,
        currency: 'USD',
        location: 'Test City, TC',
        latitude: 40.7128,
        longitude: -74.0060,
        is_available: true,
        is_active: true,
        features: ['helmet_included', 'insurance_included'],
        images: ['https://example.com/honda-pcx.jpg'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        provider_id: TEST_USER_ID,
        name: 'Yamaha NMAX 155',
        description: 'Sporty and reliable scooter with great performance',
        type: 'scooter',
        brand: 'Yamaha',
        model: 'NMAX 155',
        year: 2023,
        price_per_day: 30.00,
        price_per_week: 180.00,
        price_per_month: 600.00,
        currency: 'USD',
        location: 'Test City, TC',
        latitude: 40.7128,
        longitude: -74.0060,
        is_available: true,
        is_active: true,
        features: ['helmet_included', 'gps_included'],
        images: ['https://example.com/yamaha-nmax.jpg'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { data: vehicles, error: vehicleError } = await supabase
      .from('vehicles')
      .insert(vehicleData)
      .select();

    if (vehicleError) {
      console.warn('⚠️ Error creating vehicles:', vehicleError);
    } else {
      console.log(`✅ Created ${vehicles.length} test vehicles`);
    }

    console.log('');
    console.log('🎉 Test Provider Setup Complete!');
    console.log('================================');
    console.log(`👤 User ID: ${TEST_USER_ID}`);
    console.log(`📧 Email: ${newUser.email}`);
    console.log(`🏢 Business: ${providerData.business_name}`);
    console.log(`🚗 Vehicles: ${vehicles ? vehicles.length : 0}`);
    console.log('');
    console.log('🔗 You can now test the provider dashboard at:');
    console.log('   http://localhost:5173/provider-dashboard');

    return { user: newUser, provider: newProvider, vehicles };

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return null;
  }
}

// Run the script
createTestProvider().then(result => {
  if (result) {
    console.log('✅ Test provider created successfully');
    process.exit(0);
  } else {
    console.log('❌ Failed to create test provider');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
