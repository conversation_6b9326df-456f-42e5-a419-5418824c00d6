-- Fix Supabase permissions for vehicles table
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow anon users to read active vehicles" ON vehicles;
DROP POLICY IF EXISTS "Allow authenticated users to read all vehicles" ON vehicles;
DROP POLICY IF EXISTS "Allow service role to manage vehicles" ON vehicles;

-- Create new policies
CREATE POLICY "Allow anon users to read active vehicles" ON vehicles
FOR SELECT USING (active = true);

CREATE POLICY "Allow authenticated users to read all vehicles" ON vehicles
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow service role to manage vehicles" ON vehicles
FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT SELECT ON vehicles TO anon;
GRANT SELECT ON vehicles TO authenticated;
GRANT ALL ON vehicles TO service_role; 